module.exports=[17673,t=>{"use strict";t.s(["SwingTradingAnalyzer",()=>l],17673);var e=t.i(58445);let a=(t,e,r,s)=>{t.forEach(t=>{if("string"==typeof t){(""===t?e:i(e,t)).classGroupId=r;return}if("function"==typeof t)return n(t)?void a(t(s),e,r,s):void e.validators.push({validator:t,classGroupId:r});Object.entries(t).forEach(([t,n])=>{a(n,i(e,t),r,s)})})},i=(t,e)=>{let a=t;return e.split("-").forEach(t=>{a.nextPart.has(t)||a.nextPart.set(t,{nextPart:new Map,validators:[]}),a=a.nextPart.get(t)}),a},n=t=>t.isThemeGetter,r=t=>{let e;if("string"==typeof t)return t;let a="";for(let i=0;i<t.length;i++)t[i]&&(e=r(t[i]))&&(a&&(a+=" "),a+=e);return a};function s(t,e,a){let i=Math.abs(t-e);return Math.abs(a-t)/i}Symbol.toStringTag;class l{static analyzeSwingTrade(t,a,i="1D"){if(a.length<50)throw Error("Insufficient data for swing trading analysis");let n=a.map(t=>t.close);a.map(t=>t.high),a.map(t=>t.low),n[n.length-1];let r=e.TechnicalIndicators.analyzeSwingSetup(a),{support:l,resistance:o}=e.TechnicalIndicators.findSupportResistance(a),c=this.determineTrend(a),h=this.calculateTradingLevels(a,c),d=this.calculateConfidence(r,c,h),g=this.generateRecommendation(r,d,c),f=this.generateAnalysisText(t,r,c,h,d);return{symbol:t,timeframe:i,trend:c.direction,confidence:d,entryPrice:h.entry,stopLoss:h.stopLoss,takeProfit:h.takeProfit,riskRewardRatio:s(h.entry,h.stopLoss,h.takeProfit),indicators:r,supportLevels:l.slice(-3),resistanceLevels:o.slice(-3),analysis:f,recommendation:g}}static determineTrend(t){let a=t.map(t=>t.close),i=e.TechnicalIndicators.sma(a,20),n=e.TechnicalIndicators.sma(a,50),r=a[a.length-1],s=i[i.length-1],l=n[n.length-1],o=0,c="SIDEWAYS";return r>s&&s>l?(c="BULLISH",o=Math.min((r/l-1)*100,100)):r<s&&s<l?(c="BEARISH",o=Math.min((l/r-1)*100,100)):o=Math.max(0,50-10*this.calculateVolatility(a.slice(-20))),{direction:c,strength:Math.abs(o)}}static calculateTradingLevels(t,e){let a,i,n=t.map(t=>t.close),r=t.map(t=>t.high),s=t.map(t=>t.low),l=n[n.length-1],o=this.calculateATR(t,14),c=o[o.length-1],h=l;if("BULLISH"===e.direction)a=(h=Math.max(1.005*Math.min(...s.slice(-10)),.995*l))-2*c,i=h+3*c;else if("BEARISH"===e.direction)a=(h=Math.min(.995*Math.max(...r.slice(-10)),1.005*l))+2*c,i=h-3*c;else{let t=Math.max(...r.slice(-20)),e=Math.min(...s.slice(-20));l<(t+e)/2?(h=l,a=.995*e,i=.995*t):(h=l,a=1.005*t,i=1.005*e)}return{entry:h,stopLoss:a,takeProfit:i}}static calculateATR(t,a){let i=[];for(let e=1;e<t.length;e++){let a=t[e].high,n=t[e].low,r=t[e-1].close,s=Math.max(a-n,Math.abs(a-r),Math.abs(n-r));i.push(s)}return e.TechnicalIndicators.sma(i,a)}static calculateVolatility(t){let e=[];for(let a=1;a<t.length;a++)e.push((t[a]-t[a-1])/t[a-1]);let a=e.reduce((t,e)=>t+e,0)/e.length;return Math.sqrt(e.reduce((t,e)=>t+Math.pow(e-a,2),0)/e.length)*Math.sqrt(252)}static calculateConfidence(t,e,a){let i=50,n=t.filter(t=>"BUY"===t.signal).length,r=t.filter(t=>"SELL"===t.signal).length,l=t.length;n>r?i+=n/l*30:r>n&&(i+=r/l*30),i+=e.strength/100*20;let o=s(a.entry,a.stopLoss,a.takeProfit);return o>=2?i+=10:o>=1.5&&(i+=5),Math.min(Math.max(i,0),100)}static generateRecommendation(t,e,a){if(e<40)return"NO_TRADE";let i=t.filter(t=>"BUY"===t.signal).length,n=t.filter(t=>"SELL"===t.signal).length;return i>n&&e>=70?"STRONG_BUY":i>n&&e>=50?"BUY":n>i&&e>=70?"STRONG_SELL":n>i&&e>=50?"SELL":"HOLD"}static generateAnalysisText(t,e,a,i,n){let r=s(i.entry,i.stopLoss,i.takeProfit),l=`${t} Swing Trading Analysis:

`;return l+=`Trend: ${a.direction} (Strength: ${a.strength.toFixed(1)}%)
Confidence: ${n.toFixed(1)}%

Entry: $${i.entry.toFixed(2)}
Stop Loss: $${i.stopLoss.toFixed(2)}
Take Profit: $${i.takeProfit.toFixed(2)}
Risk/Reward: ${r.toFixed(2)}:1

Technical Indicators:
`,e.forEach(t=>{l+=`• ${t.description}
`}),l}}}];

//# sourceMappingURL=swing-trader-ai_src_lib_swingAnalysis_ts_448e373e._.js.map