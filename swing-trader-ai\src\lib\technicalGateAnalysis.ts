import { TechnicalGateAnalysis, CandlestickData } from '@/types/trading'
import { TechnicalIndicators } from './indicators'
import { PolygonAPI } from './polygon'

export class TechnicalGateAnalysis {
  private polygonAPI: PolygonAPI

  constructor(polygonApiKey?: string) {
    this.polygonAPI = new PolygonAPI(polygonApiKey)
  }

  /**
   * Perform comprehensive technical gate analysis
   */
  async analyzeTechnicalGate(symbol: string): Promise<TechnicalGateAnalysis | null> {
    try {
      // Get historical data (need at least 200 days for SMA200)
      const historicalData = await this.getHistoricalData(symbol, 250)
      
      if (!historicalData || historicalData.length < 200) {
        console.error(`Insufficient data for ${symbol} - need at least 200 days`)
        return null
      }

      const currentPrice = historicalData[historicalData.length - 1].close
      
      // Calculate technical indicators
      const sma200 = TechnicalIndicators.calculateSMA(historicalData, 200)
      const ema8 = TechnicalIndicators.calculateEMA(historicalData, 8)
      const vwap = this.calculateVWAP(historicalData.slice(-20)) // 20-day VWAP
      
      // Analyze trend confirmation
      const dailyTrendConfirmed = this.analyzeDailyTrend(historicalData)
      
      // Check moving average conditions
      const aboveSMA200 = currentPrice > sma200[sma200.length - 1]
      const aboveEMA8 = currentPrice > ema8[ema8.length - 1]
      const respectsEMA8 = this.checkEMA8Respect(historicalData, ema8)
      
      // Check for all-time high
      const isAtAllTimeHigh = this.checkAllTimeHigh(historicalData)
      
      // Check for clean breakout
      const hasCleanBreakout = this.checkCleanBreakout(historicalData)
      
      // Check volume expansion
      const volumeExpansion = this.checkVolumeExpansion(historicalData)
      
      // Calculate resistance and support levels
      const resistanceLevels = this.calculateResistanceLevels(historicalData)
      const supportLevels = this.calculateSupportLevels(historicalData)
      
      // Calculate overall grade and score
      const gateScore = this.calculateGateScore({
        dailyTrendConfirmed,
        aboveSMA200,
        aboveEMA8,
        respectsEMA8,
        isAtAllTimeHigh,
        hasCleanBreakout,
        volumeExpansion
      })
      
      const overallGrade = this.calculateOverallGrade(gateScore)
      
      const analysis: TechnicalGateAnalysis = {
        symbol,
        dailyTrendConfirmed,
        aboveSMA200,
        aboveEMA8,
        respectsEMA8,
        isAtAllTimeHigh,
        hasCleanBreakout,
        volumeExpansion,
        overallGrade,
        gateScore,
        resistanceLevels,
        supportLevels,
        keyTechnicalLevels: {
          sma200: sma200[sma200.length - 1],
          ema8: ema8[ema8.length - 1],
          vwap,
          previousHigh: Math.max(...historicalData.slice(-20).map(d => d.high)),
          previousLow: Math.min(...historicalData.slice(-20).map(d => d.low))
        }
      }
      
      return analysis
    } catch (error) {
      console.error(`Error analyzing technical gate for ${symbol}:`, error)
      return null
    }
  }

  /**
   * Get historical candlestick data
   */
  private async getHistoricalData(symbol: string, days: number): Promise<CandlestickData[]> {
    try {
      const endDate = new Date()
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - days)
      
      return await this.polygonAPI.getHistoricalData(
        symbol,
        startDate.toISOString().split('T')[0],
        endDate.toISOString().split('T')[0],
        '1',
        'day'
      )
    } catch (error) {
      console.error(`Error fetching historical data for ${symbol}:`, error)
      return []
    }
  }

  /**
   * Analyze daily trend confirmation (higher highs, higher lows over 20+ days)
   */
  private analyzeDailyTrend(data: CandlestickData[]): boolean {
    if (data.length < 20) return false
    
    const recent20Days = data.slice(-20)
    const first10Days = recent20Days.slice(0, 10)
    const last10Days = recent20Days.slice(10)
    
    const firstPeriodHigh = Math.max(...first10Days.map(d => d.high))
    const firstPeriodLow = Math.min(...first10Days.map(d => d.low))
    const lastPeriodHigh = Math.max(...last10Days.map(d => d.high))
    const lastPeriodLow = Math.min(...last10Days.map(d => d.low))
    
    // Check for higher highs and higher lows
    return lastPeriodHigh > firstPeriodHigh && lastPeriodLow > firstPeriodLow
  }

  /**
   * Check if stock consistently respects/reclaims 8-EMA
   */
  private checkEMA8Respect(data: CandlestickData[], ema8: number[]): boolean {
    if (data.length < 20 || ema8.length < 20) return false
    
    const recent20Days = data.slice(-20)
    const recent20EMA = ema8.slice(-20)
    
    let respectCount = 0
    
    for (let i = 0; i < recent20Days.length; i++) {
      const candle = recent20Days[i]
      const emaValue = recent20EMA[i]
      
      // Check if low didn't break significantly below EMA8 (allow 2% cushion)
      if (candle.low >= emaValue * 0.98) {
        respectCount++
      }
    }
    
    // Stock respects EMA8 if it holds above it 70% of the time
    return respectCount / recent20Days.length >= 0.7
  }

  /**
   * Check if stock is at or near all-time high
   */
  private checkAllTimeHigh(data: CandlestickData[]): boolean {
    const currentPrice = data[data.length - 1].close
    const allTimeHigh = Math.max(...data.map(d => d.high))
    
    // Consider "at ATH" if within 5% of all-time high
    return currentPrice >= allTimeHigh * 0.95
  }

  /**
   * Check for clean breakout from consolidation patterns
   */
  private checkCleanBreakout(data: CandlestickData[]): boolean {
    if (data.length < 30) return false
    
    const recent30Days = data.slice(-30)
    const last5Days = recent30Days.slice(-5)
    const consolidationPeriod = recent30Days.slice(-30, -5)
    
    // Calculate consolidation range
    const consolidationHigh = Math.max(...consolidationPeriod.map(d => d.high))
    const consolidationLow = Math.min(...consolidationPeriod.map(d => d.low))
    const consolidationRange = (consolidationHigh - consolidationLow) / consolidationLow
    
    // Check if recent price broke above consolidation with volume
    const recentHigh = Math.max(...last5Days.map(d => d.high))
    const recentVolume = last5Days.reduce((sum, d) => sum + d.volume, 0) / last5Days.length
    const avgVolume = consolidationPeriod.reduce((sum, d) => sum + d.volume, 0) / consolidationPeriod.length
    
    // Clean breakout criteria:
    // 1. Consolidation range < 20% (tight consolidation)
    // 2. Recent high > consolidation high
    // 3. Volume expansion on breakout
    return consolidationRange < 0.20 && 
           recentHigh > consolidationHigh && 
           recentVolume > avgVolume * 1.5
  }

  /**
   * Check for volume expansion on breakout days
   */
  private checkVolumeExpansion(data: CandlestickData[]): boolean {
    if (data.length < 20) return false
    
    const recent5Days = data.slice(-5)
    const previous20Days = data.slice(-25, -5)
    
    const recentAvgVolume = recent5Days.reduce((sum, d) => sum + d.volume, 0) / recent5Days.length
    const historicalAvgVolume = previous20Days.reduce((sum, d) => sum + d.volume, 0) / previous20Days.length
    
    // Volume expansion if recent volume is 150%+ of historical average
    return recentAvgVolume > historicalAvgVolume * 1.5
  }

  /**
   * Calculate VWAP (Volume Weighted Average Price)
   */
  private calculateVWAP(data: CandlestickData[]): number {
    let totalVolume = 0
    let totalVolumePrice = 0
    
    for (const candle of data) {
      const typicalPrice = (candle.high + candle.low + candle.close) / 3
      totalVolumePrice += typicalPrice * candle.volume
      totalVolume += candle.volume
    }
    
    return totalVolume > 0 ? totalVolumePrice / totalVolume : 0
  }

  /**
   * Calculate resistance levels using pivot highs
   */
  private calculateResistanceLevels(data: CandlestickData[]): number[] {
    const resistanceLevels: number[] = []
    const lookback = 5 // Look for pivots with 5 days on each side
    
    for (let i = lookback; i < data.length - lookback; i++) {
      const current = data[i]
      let isPivotHigh = true
      
      // Check if current high is higher than surrounding highs
      for (let j = i - lookback; j <= i + lookback; j++) {
        if (j !== i && data[j].high >= current.high) {
          isPivotHigh = false
          break
        }
      }
      
      if (isPivotHigh) {
        resistanceLevels.push(current.high)
      }
    }
    
    // Return top 5 most recent resistance levels
    return resistanceLevels.slice(-5).sort((a, b) => b - a)
  }

  /**
   * Calculate support levels using pivot lows
   */
  private calculateSupportLevels(data: CandlestickData[]): number[] {
    const supportLevels: number[] = []
    const lookback = 5
    
    for (let i = lookback; i < data.length - lookback; i++) {
      const current = data[i]
      let isPivotLow = true
      
      // Check if current low is lower than surrounding lows
      for (let j = i - lookback; j <= i + lookback; j++) {
        if (j !== i && data[j].low <= current.low) {
          isPivotLow = false
          break
        }
      }
      
      if (isPivotLow) {
        supportLevels.push(current.low)
      }
    }
    
    // Return top 5 most recent support levels
    return supportLevels.slice(-5).sort((a, b) => b - a)
  }

  /**
   * Calculate overall gate score (0-100)
   */
  private calculateGateScore(conditions: {
    dailyTrendConfirmed: boolean
    aboveSMA200: boolean
    aboveEMA8: boolean
    respectsEMA8: boolean
    isAtAllTimeHigh: boolean
    hasCleanBreakout: boolean
    volumeExpansion: boolean
  }): number {
    let score = 0
    
    // Required conditions (higher weight)
    if (conditions.dailyTrendConfirmed) score += 20
    if (conditions.aboveSMA200) score += 20
    if (conditions.aboveEMA8) score += 15
    
    // Premium conditions (bonus points)
    if (conditions.respectsEMA8) score += 15
    if (conditions.isAtAllTimeHigh) score += 15
    if (conditions.hasCleanBreakout) score += 10
    if (conditions.volumeExpansion) score += 5
    
    return Math.min(100, score)
  }

  /**
   * Calculate overall grade based on score
   */
  private calculateOverallGrade(score: number): 'A' | 'B' | 'C' | 'D' | 'F' {
    if (score >= 90) return 'A'
    if (score >= 80) return 'B'
    if (score >= 70) return 'C'
    if (score >= 60) return 'D'
    return 'F'
  }

  /**
   * Batch analyze multiple symbols
   */
  async batchAnalyzeTechnicalGates(symbols: string[]): Promise<TechnicalGateAnalysis[]> {
    const results: TechnicalGateAnalysis[] = []
    
    // Process in chunks to avoid API rate limits
    const chunkSize = 5
    for (let i = 0; i < symbols.length; i += chunkSize) {
      const chunk = symbols.slice(i, i + chunkSize)
      const chunkPromises = chunk.map(symbol => this.analyzeTechnicalGate(symbol))
      const chunkResults = await Promise.all(chunkPromises)
      
      results.push(...chunkResults.filter((result): result is TechnicalGateAnalysis => result !== null))
      
      // Small delay between chunks
      if (i + chunkSize < symbols.length) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }
    
    return results.sort((a, b) => b.gateScore - a.gateScore)
  }
}
