{"version": 3, "sources": ["turbopack:///[project]/swing-trader-ai/src/lib/polygon.ts", "turbopack:///[project]/swing-trader-ai/src/lib/fmp.ts", "turbopack:///[project]/swing-trader-ai/src/lib/indicators.ts", "turbopack:///[project]/swing-trader-ai/src/lib/swingStrategies.ts", "turbopack:///[project]/swing-trader-ai/src/app/api/analysis/strategy/[symbol]/route.ts", "turbopack:///[project]/swing-trader-ai/node_modules/next/dist/esm/build/templates/app-route.js"], "sourcesContent": ["import axios from 'axios'\nimport { CandlestickData, StockData } from '@/types/trading'\n\nconst POLYGON_BASE_URL = 'https://api.polygon.io'\nconst API_KEY = process.env.POLYGON_API_KEY\n\nexport class PolygonAPI {\n  private apiKey: string\n\n  constructor(apiKey?: string) {\n    this.apiKey = apiKey || API_KEY || ''\n    if (!this.apiKey) {\n      throw new Error('Polygon API key is required')\n    }\n  }\n\n  // Get current stock quote using Polygon's snapshot endpoint (better for paid plans)\n  async getStockQuote(symbol: string): Promise<StockData> {\n    try {\n      // Use snapshot endpoint for real-time data (available on paid plans)\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v2/snapshot/locale/us/markets/stocks/tickers/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      const data = response.data.results[0]\n      if (!data) {\n        throw new Error(`No data found for ${symbol}`)\n      }\n\n      const ticker = data.value || data\n      const dayData = ticker.day || {}\n      const prevDayData = ticker.prevDay || {}\n      const lastQuote = ticker.lastQuote || {}\n      const lastTrade = ticker.lastTrade || {}\n\n      // Use the most recent price available\n      const currentPrice = lastTrade.p || dayData.c || prevDayData.c\n      const prevClose = prevDayData.c || dayData.o\n      const change = currentPrice - prevClose\n      const changePercent = (change / prevClose) * 100\n\n      return {\n        symbol: symbol.toUpperCase(),\n        name: ticker.name || symbol.toUpperCase(),\n        price: currentPrice,\n        change,\n        changePercent,\n        volume: dayData.v || 0,\n        marketCap: ticker.market_cap,\n        pe: undefined,\n        dividend: undefined\n      }\n    } catch (error) {\n      console.error('Error fetching stock quote from Polygon:', error)\n\n      // Fallback to previous day data if snapshot fails\n      try {\n        const fallbackResponse = await axios.get(\n          `${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/prev`,\n          {\n            params: {\n              adjusted: 'true',\n              apikey: this.apiKey\n            }\n          }\n        )\n\n        const data = fallbackResponse.data.results[0]\n        return {\n          symbol: symbol.toUpperCase(),\n          name: symbol.toUpperCase(),\n          price: data.c,\n          change: data.c - data.o,\n          changePercent: ((data.c - data.o) / data.o) * 100,\n          volume: data.v,\n          marketCap: undefined,\n          pe: undefined,\n          dividend: undefined\n        }\n      } catch (fallbackError) {\n        console.error('Polygon fallback also failed:', fallbackError)\n        throw new Error(`Failed to fetch quote for ${symbol}`)\n      }\n    }\n  }\n\n  // Get historical candlestick data (optimized for paid plans)\n  async getHistoricalData(\n    symbol: string,\n    timespan: 'minute' | 'hour' | 'day' | 'week' | 'month' = 'day',\n    multiplier: number = 1,\n    from: string,\n    to: string\n  ): Promise<CandlestickData[]> {\n    try {\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/range/${multiplier}/${timespan}/${from}/${to}`,\n        {\n          params: {\n            adjusted: 'true',\n            sort: 'asc',\n            limit: 50000, // Higher limit for paid plans\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      if (!response.data.results || response.data.results.length === 0) {\n        console.warn(`No historical data found for ${symbol}`)\n        return []\n      }\n\n      return response.data.results.map((candle: any) => ({\n        timestamp: candle.t,\n        open: candle.o,\n        high: candle.h,\n        low: candle.l,\n        close: candle.c,\n        volume: candle.v\n      }))\n    } catch (error) {\n      console.error(`Error fetching historical data for ${symbol}:`, error)\n\n      // Log the specific error for debugging\n      if (error.response) {\n        console.error(`Polygon API Error: ${error.response.status} - ${error.response.statusText}`)\n        console.error('Response data:', error.response.data)\n      }\n\n      throw new Error(`Failed to fetch historical data for ${symbol}: ${error.message}`)\n    }\n  }\n\n  // Get company details\n  async getCompanyDetails(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v3/reference/tickers/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data.results\n    } catch (error) {\n      console.error('Error fetching company details:', error)\n      return null\n    }\n  }\n\n  // Get market status\n  async getMarketStatus() {\n    try {\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v1/marketstatus/now`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error fetching market status:', error)\n      return null\n    }\n  }\n\n  // Search for stocks\n  async searchStocks(query: string, limit: number = 10) {\n    try {\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v3/reference/tickers`,\n        {\n          params: {\n            search: query,\n            market: 'stocks',\n            active: 'true',\n            limit,\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data.results || []\n    } catch (error) {\n      console.error('Error searching stocks:', error)\n      return []\n    }\n  }\n}\n\n// Create a singleton instance\nexport const polygonAPI = new PolygonAPI()\n", "import axios from 'axios'\nimport { StockData } from '@/types/trading'\n\nconst FMP_BASE_URL = 'https://financialmodelingprep.com/api'\nconst API_KEY = process.env.FMP_API_KEY\n\nexport class FMPAPI {\n  private apiKey: string\n\n  constructor(apiKey?: string) {\n    this.apiKey = apiKey || API_KEY || ''\n    if (!this.apiKey) {\n      throw new Error('FMP API key is required')\n    }\n  }\n\n  // Get real-time stock quote\n  async getStockQuote(symbol: string): Promise<StockData> {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/quote/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      const data = response.data[0]\n      if (!data) {\n        throw new Error(`No data found for symbol ${symbol}`)\n      }\n\n      return {\n        symbol: data.symbol,\n        name: data.name || data.symbol,\n        price: data.price,\n        change: data.change,\n        changePercent: data.changesPercentage,\n        volume: data.volume,\n        marketCap: data.marketCap,\n        pe: data.pe,\n        dividend: undefined // Will be fetched separately if needed\n      }\n    } catch (error) {\n      console.error('Error fetching FMP stock quote:', error)\n      throw new Error(`Failed to fetch quote for ${symbol}`)\n    }\n  }\n\n  // Get company profile\n  async getCompanyProfile(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/profile/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data[0]\n    } catch (error) {\n      console.error('Error fetching company profile:', error)\n      return null\n    }\n  }\n\n  // Get financial ratios\n  async getFinancialRatios(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/ratios/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data[0] // Most recent ratios\n    } catch (error) {\n      console.error('Error fetching financial ratios:', error)\n      return null\n    }\n  }\n\n  // Get key metrics\n  async getKeyMetrics(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/key-metrics/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data[0] // Most recent metrics\n    } catch (error) {\n      console.error('Error fetching key metrics:', error)\n      return null\n    }\n  }\n\n  // Get analyst recommendations\n  async getAnalystRecommendations(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/analyst-stock-recommendations/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error fetching analyst recommendations:', error)\n      return []\n    }\n  }\n\n  // Get earnings calendar\n  async getEarningsCalendar(from?: string, to?: string) {\n    try {\n      const params: any = {\n        apikey: this.apiKey\n      }\n\n      if (from) params.from = from\n      if (to) params.to = to\n\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/earning_calendar`,\n        { params }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error fetching earnings calendar:', error)\n      return []\n    }\n  }\n\n  // Get economic calendar\n  async getEconomicCalendar(from?: string, to?: string) {\n    try {\n      const params: any = {\n        apikey: this.apiKey\n      }\n\n      if (from) params.from = from\n      if (to) params.to = to\n\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/economic_calendar`,\n        { params }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error fetching economic calendar:', error)\n      return []\n    }\n  }\n\n  // Search for stocks\n  async searchStocks(query: string, limit: number = 10) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/search`,\n        {\n          params: {\n            query,\n            limit,\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error searching stocks:', error)\n      return []\n    }\n  }\n\n  // Get sector performance\n  async getSectorPerformance() {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/sector-performance`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error fetching sector performance:', error)\n      return []\n    }\n  }\n\n  // Get market gainers/losers\n  async getMarketMovers(type: 'gainers' | 'losers' | 'actives') {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/stock_market/${type}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error(`Error fetching market ${type}:`, error)\n      return []\n    }\n  }\n\n  // ===== CATALYST DETECTION ENDPOINTS =====\n\n  // Get earnings calendar for catalyst detection\n  async getEarningsCalendar(symbol?: string, days: number = 30) {\n    try {\n      const fromDate = new Date()\n      fromDate.setDate(fromDate.getDate() - days)\n      const toDate = new Date()\n\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/earning_calendar`,\n        {\n          params: {\n            apikey: this.apiKey,\n            from: fromDate.toISOString().split('T')[0],\n            to: toDate.toISOString().split('T')[0],\n            ...(symbol && { symbol: symbol.toUpperCase() })\n          }\n        }\n      )\n\n      return response.data || []\n    } catch (error) {\n      console.error('Error fetching earnings calendar:', error)\n      return []\n    }\n  }\n\n  // Get stock news for catalyst detection\n  async getStockNews(symbol: string, limit: number = 50) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/stock_news`,\n        {\n          params: {\n            apikey: this.apiKey,\n            tickers: symbol.toUpperCase(),\n            limit\n          }\n        }\n      )\n\n      return response.data || []\n    } catch (error) {\n      console.error('Error fetching stock news:', error)\n      return []\n    }\n  }\n\n  // Get analyst recommendations\n  async getAnalystRecommendations(symbol: string, days: number = 30) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/analyst-stock-recommendations/${symbol.toUpperCase()}`,\n        {\n          params: {\n            apikey: this.apiKey,\n            limit: days\n          }\n        }\n      )\n\n      return response.data || []\n    } catch (error) {\n      console.error('Error fetching analyst recommendations:', error)\n      return []\n    }\n  }\n\n  // Get insider trading data\n  async getInsiderTrading(symbol: string, days: number = 30) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v4/insider-trading`,\n        {\n          params: {\n            apikey: this.apiKey,\n            symbol: symbol.toUpperCase(),\n            limit: days * 5 // Approximate multiple to get enough data\n          }\n        }\n      )\n\n      // Filter to last N days\n      const cutoffDate = new Date()\n      cutoffDate.setDate(cutoffDate.getDate() - days)\n\n      return (response.data || []).filter((trade: any) =>\n        new Date(trade.filingDate) >= cutoffDate\n      )\n    } catch (error) {\n      console.error('Error fetching insider trading:', error)\n      return []\n    }\n  }\n\n  // Get SEC filings\n  async getSECFilings(symbol: string, days: number = 30) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/sec_filings/${symbol.toUpperCase()}`,\n        {\n          params: {\n            apikey: this.apiKey,\n            limit: days * 2 // Get more filings to filter by date\n          }\n        }\n      )\n\n      // Filter to last N days\n      const cutoffDate = new Date()\n      cutoffDate.setDate(cutoffDate.getDate() - days)\n\n      return (response.data || []).filter((filing: any) =>\n        new Date(filing.filedDate) >= cutoffDate\n      )\n    } catch (error) {\n      console.error('Error fetching SEC filings:', error)\n      return []\n    }\n  }\n\n  // Get pre-market quotes for gap scanning\n  async getPreMarketQuote(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/quote/${symbol.toUpperCase()}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      const data = response.data[0]\n      if (!data) return null\n\n      return {\n        symbol: data.symbol,\n        price: data.price,\n        previousClose: data.previousClose,\n        change: data.change,\n        changePercent: data.changesPercentage,\n        volume: data.volume,\n        marketCap: data.marketCap,\n        avgVolume: data.avgVolume,\n        // Pre-market specific data (if available)\n        preMarketPrice: data.preMarketPrice || data.price,\n        preMarketChange: data.preMarketChange || data.change,\n        preMarketChangePercent: data.preMarketChangePercent || data.changesPercentage\n      }\n    } catch (error) {\n      console.error('Error fetching pre-market quote:', error)\n      return null\n    }\n  }\n\n  // Get multiple pre-market quotes efficiently\n  async getMultiplePreMarketQuotes(symbols: string[]) {\n    try {\n      const symbolsString = symbols.map(s => s.toUpperCase()).join(',')\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/quote/${symbolsString}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return (response.data || []).map((data: any) => ({\n        symbol: data.symbol,\n        price: data.price,\n        previousClose: data.previousClose,\n        change: data.change,\n        changePercent: data.changesPercentage,\n        volume: data.volume,\n        marketCap: data.marketCap,\n        avgVolume: data.avgVolume,\n        preMarketPrice: data.preMarketPrice || data.price,\n        preMarketChange: data.preMarketChange || data.change,\n        preMarketChangePercent: data.preMarketChangePercent || data.changesPercentage\n      }))\n    } catch (error) {\n      console.error('Error fetching multiple pre-market quotes:', error)\n      return []\n    }\n  }\n\n  // Get company profile for additional context\n  async getCompanyProfile(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/profile/${symbol.toUpperCase()}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data[0] || null\n    } catch (error) {\n      console.error('Error fetching company profile:', error)\n      return null\n    }\n  }\n}\n\n// Create a singleton instance\nexport const fmpAPI = new FMPAPI()\n", "import { CandlestickData, TechnicalIndicator } from '@/types/trading'\n\nexport class TechnicalIndicators {\n  // Simple Moving Average\n  static sma(data: number[], period: number): number[] {\n    const result: number[] = []\n    for (let i = period - 1; i < data.length; i++) {\n      const sum = data.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0)\n      result.push(sum / period)\n    }\n    return result\n  }\n\n  // Exponential Moving Average\n  static ema(data: number[], period: number): number[] {\n    const result: number[] = []\n    const multiplier = 2 / (period + 1)\n    \n    // Start with SMA for first value\n    let ema = data.slice(0, period).reduce((a, b) => a + b, 0) / period\n    result.push(ema)\n    \n    for (let i = period; i < data.length; i++) {\n      ema = (data[i] * multiplier) + (ema * (1 - multiplier))\n      result.push(ema)\n    }\n    \n    return result\n  }\n\n  // Relative Strength Index\n  static rsi(data: number[], period: number = 14): number[] {\n    const gains: number[] = []\n    const losses: number[] = []\n    \n    for (let i = 1; i < data.length; i++) {\n      const change = data[i] - data[i - 1]\n      gains.push(change > 0 ? change : 0)\n      losses.push(change < 0 ? Math.abs(change) : 0)\n    }\n    \n    const avgGains = this.sma(gains, period)\n    const avgLosses = this.sma(losses, period)\n    \n    return avgGains.map((gain, i) => {\n      const rs = gain / avgLosses[i]\n      return 100 - (100 / (1 + rs))\n    })\n  }\n\n  // MACD (Moving Average Convergence Divergence)\n  static macd(data: number[], fastPeriod: number = 12, slowPeriod: number = 26, signalPeriod: number = 9) {\n    const fastEMA = this.ema(data, fastPeriod)\n    const slowEMA = this.ema(data, slowPeriod)\n    \n    // Align arrays (slowEMA starts later)\n    const startIndex = slowPeriod - fastPeriod\n    const macdLine = fastEMA.slice(startIndex).map((fast, i) => fast - slowEMA[i])\n    \n    const signalLine = this.ema(macdLine, signalPeriod)\n    const histogram = macdLine.slice(signalPeriod - 1).map((macd, i) => macd - signalLine[i])\n    \n    return {\n      macd: macdLine,\n      signal: signalLine,\n      histogram\n    }\n  }\n\n  // Bollinger Bands\n  static bollingerBands(data: number[], period: number = 20, stdDev: number = 2) {\n    const sma = this.sma(data, period)\n    const bands = sma.map((avg, i) => {\n      const slice = data.slice(i, i + period)\n      const variance = slice.reduce((sum, val) => sum + Math.pow(val - avg, 2), 0) / period\n      const standardDeviation = Math.sqrt(variance)\n      \n      return {\n        upper: avg + (standardDeviation * stdDev),\n        middle: avg,\n        lower: avg - (standardDeviation * stdDev)\n      }\n    })\n    \n    return bands\n  }\n\n  // Support and Resistance Levels\n  static findSupportResistance(candles: CandlestickData[], lookback: number = 20): { support: number[], resistance: number[] } {\n    const highs = candles.map(c => c.high)\n    const lows = candles.map(c => c.low)\n    \n    const resistance: number[] = []\n    const support: number[] = []\n    \n    for (let i = lookback; i < candles.length - lookback; i++) {\n      const currentHigh = highs[i]\n      const currentLow = lows[i]\n      \n      // Check if current high is a local maximum\n      const isResistance = highs.slice(i - lookback, i).every(h => h <= currentHigh) &&\n                          highs.slice(i + 1, i + lookback + 1).every(h => h <= currentHigh)\n      \n      // Check if current low is a local minimum\n      const isSupport = lows.slice(i - lookback, i).every(l => l >= currentLow) &&\n                       lows.slice(i + 1, i + lookback + 1).every(l => l >= currentLow)\n      \n      if (isResistance) resistance.push(currentHigh)\n      if (isSupport) support.push(currentLow)\n    }\n    \n    return { support, resistance }\n  }\n\n  // Volume analysis\n  static volumeAnalysis(candles: CandlestickData[], period: number = 20) {\n    const volumes = candles.map(c => c.volume)\n    const avgVolume = this.sma(volumes, period)\n    const currentVolume = volumes[volumes.length - 1]\n    const currentAvgVolume = avgVolume[avgVolume.length - 1]\n\n    return {\n      currentVolume,\n      averageVolume: currentAvgVolume,\n      volumeRatio: currentVolume / currentAvgVolume,\n      isHighVolume: currentVolume > currentAvgVolume * 1.5,\n      isLowVolume: currentVolume < currentAvgVolume * 0.5\n    }\n  }\n\n  // Swing Trading Analysis\n  static analyzeSwingSetup(candles: CandlestickData[]): TechnicalIndicator[] {\n    const closes = candles.map(c => c.close)\n    const indicators: TechnicalIndicator[] = []\n\n    // RSI Analysis\n    const rsi = this.rsi(closes)\n    const currentRSI = rsi[rsi.length - 1]\n\n    let rsiSignal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'\n    let rsiDescription = `RSI: ${currentRSI.toFixed(2)}`\n\n    if (currentRSI < 30) {\n      rsiSignal = 'BUY'\n      rsiDescription += ' - Oversold condition, potential bounce'\n    } else if (currentRSI > 70) {\n      rsiSignal = 'SELL'\n      rsiDescription += ' - Overbought condition, potential pullback'\n    } else {\n      rsiDescription += ' - Neutral zone'\n    }\n\n    indicators.push({\n      name: 'RSI',\n      value: currentRSI,\n      signal: rsiSignal,\n      description: rsiDescription\n    })\n\n    // Moving Average Analysis\n    const sma20 = this.sma(closes, 20)\n    const sma50 = this.sma(closes, 50)\n    const currentPrice = closes[closes.length - 1]\n    const currentSMA20 = sma20[sma20.length - 1]\n    const currentSMA50 = sma50[sma50.length - 1]\n\n    let maSignal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'\n    let maDescription = `Price vs SMA20: ${((currentPrice / currentSMA20 - 1) * 100).toFixed(2)}%`\n\n    if (currentPrice > currentSMA20 && currentSMA20 > currentSMA50) {\n      maSignal = 'BUY'\n      maDescription += ' - Bullish trend'\n    } else if (currentPrice < currentSMA20 && currentSMA20 < currentSMA50) {\n      maSignal = 'SELL'\n      maDescription += ' - Bearish trend'\n    } else {\n      maDescription += ' - Mixed signals'\n    }\n\n    indicators.push({\n      name: 'Moving Averages',\n      value: (currentPrice / currentSMA20 - 1) * 100,\n      signal: maSignal,\n      description: maDescription\n    })\n\n    // MACD Analysis\n    const macdData = this.macd(closes)\n    const currentMACD = macdData.macd[macdData.macd.length - 1]\n    const currentSignal = macdData.signal[macdData.signal.length - 1]\n    const currentHistogram = macdData.histogram[macdData.histogram.length - 1]\n\n    let macdSignal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'\n    let macdDescription = `MACD: ${currentMACD.toFixed(4)}, Signal: ${currentSignal.toFixed(4)}`\n\n    if (currentMACD > currentSignal && currentHistogram > 0) {\n      macdSignal = 'BUY'\n      macdDescription += ' - Bullish momentum'\n    } else if (currentMACD < currentSignal && currentHistogram < 0) {\n      macdSignal = 'SELL'\n      macdDescription += ' - Bearish momentum'\n    } else {\n      macdDescription += ' - Momentum shifting'\n    }\n\n    indicators.push({\n      name: 'MACD',\n      value: currentHistogram,\n      signal: macdSignal,\n      description: macdDescription\n    })\n\n    // Volume Analysis\n    const volumeData = this.volumeAnalysis(candles)\n    let volumeSignal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'\n    let volumeDescription = `Volume: ${(volumeData.volumeRatio * 100).toFixed(0)}% of average`\n\n    if (volumeData.isHighVolume) {\n      volumeSignal = 'BUY'\n      volumeDescription += ' - High volume confirms move'\n    } else if (volumeData.isLowVolume) {\n      volumeSignal = 'SELL'\n      volumeDescription += ' - Low volume, weak conviction'\n    } else {\n      volumeDescription += ' - Normal volume'\n    }\n\n    indicators.push({\n      name: 'Volume',\n      value: volumeData.volumeRatio,\n      signal: volumeSignal,\n      description: volumeDescription\n    })\n\n    return indicators\n  }\n}\n", "import { CandlestickData, StockData, SwingTradingAnalysis } from '@/types/trading'\nimport { TechnicalIndicators } from './indicators'\n\nexport interface StrategySetup {\n  strategy: 'overnight_momentum' | 'technical_breakout'\n  confidence: number\n  entryPrice: number\n  stopLoss: number\n  targets: number[]\n  positionSize: number\n  riskAmount: number\n  holdingPeriod: 'overnight' | 'days_to_weeks'\n  keyLevel: number\n  invalidation: string\n  notes: string[]\n  // Precise trading execution details\n  preciseEntry: {\n    price: number\n    orderType: 'market' | 'limit'\n    timing: string\n    conditions: string[]\n    urgency: 'immediate' | 'wait_for_pullback' | 'breakout_confirmation'\n  }\n  preciseExit: {\n    stopLoss: {\n      price: number\n      orderType: 'stop' | 'stop_limit'\n      reason: string\n      triggerConditions: string[]\n    }\n    takeProfits: Array<{\n      price: number\n      percentage: number // % of position to sell\n      target: string // \"R1\", \"R2\", \"Extension\", etc.\n      reasoning: string\n      orderType: 'limit' | 'market'\n    }>\n  }\n  riskManagement: {\n    maxRiskDollars: number\n    accountRiskPercent: number\n    sharesForRisk: number\n    invalidationPrice: number\n    timeStopHours: number\n    maxDrawdownPercent: number\n  }\n  executionPlan: {\n    entryInstructions: string[]\n    exitInstructions: string[]\n    monitoringPoints: string[]\n    contingencyPlans: string[]\n  }\n}\n\nexport interface SwingSetupCriteria {\n  // Basic filters\n  minPrice: number\n  minVolume: number\n  minMarketCap: number\n  minATRPercent: number\n  \n  // Technical requirements\n  above200SMA: boolean\n  maxDistanceFrom8EMA: number // in ATR units\n  minRoomToResistance: number // in ATR units\n  \n  // Timing\n  scanTimeStart: string // \"12:00\"\n  scanTimeEnd: string   // \"16:00\"\n  \n  // Risk management\n  maxRiskPerTrade: number // percentage of account\n  maxConcurrentPositions: number\n}\n\nexport class SwingTradingStrategies {\n  private static readonly DEFAULT_CRITERIA: SwingSetupCriteria = {\n    minPrice: 5.0,\n    minVolume: 500000,\n    minMarketCap: *********, // $800M\n    minATRPercent: 2.0,\n    above200SMA: true,\n    maxDistanceFrom8EMA: 2.0, // 2x ATR\n    minRoomToResistance: 1.0, // 1 ATR minimum\n    scanTimeStart: \"12:00\",\n    scanTimeEnd: \"16:00\",\n    maxRiskPerTrade: 1.0, // 1% max risk\n    maxConcurrentPositions: 3\n  }\n\n  // Strategy #1: Overnight Momentum Continuation\n  static analyzeOvernightMomentum(\n    symbol: string,\n    candles: CandlestickData[],\n    quote: StockData,\n    accountSize: number = 100000\n  ): StrategySetup | null {\n    if (candles.length < 50) return null\n\n    const closes = candles.map(c => c.close)\n    const highs = candles.map(c => c.high)\n    const lows = candles.map(c => c.low)\n    const volumes = candles.map(c => c.volume)\n    \n    const currentPrice = quote.price\n    const currentVolume = quote.volume\n    const changePercent = quote.changePercent\n\n    // Calculate technical indicators (adjusted for shorter history)\n    const sma50 = TechnicalIndicators.sma(closes, Math.min(50, closes.length - 1)) // Use 50-day instead of 200-day\n    const ema8 = TechnicalIndicators.ema(closes, Math.min(8, closes.length - 1))\n    const atr = this.calculateATR(candles, Math.min(14, candles.length - 1))\n\n    const current50SMA = sma50[sma50.length - 1]\n    const current8EMA = ema8[ema8.length - 1]\n    const currentATR = atr[atr.length - 1]\n\n    // Basic qualification filters (using 50-day SMA instead of 200-day)\n    if (!this.passesBasicFilters(quote, currentVolume, current50SMA, currentPrice)) {\n      return null\n    }\n\n    // Check if it's a top intraday gainer (top decile movers)\n    if (changePercent < 2.0) return null // Minimum 2% gain for momentum\n\n    // Check distance from 8-EMA (not wildly extended)\n    const distanceFrom8EMA = Math.abs(currentPrice - current8EMA) / currentATR\n    if (distanceFrom8EMA > this.DEFAULT_CRITERIA.maxDistanceFrom8EMA) return null\n\n    // Look for defended intraday level (simplified - using VWAP proxy)\n    const vwap = this.calculateVWAP(candles.slice(-1)[0])\n    const keyLevel = Math.max(vwap, currentPrice * 0.98) // Approximate defended level\n\n    // Check if holding gains (>50% of day's range)\n    const todayHigh = highs[highs.length - 1]\n    const todayLow = lows[lows.length - 1]\n    const dayRange = todayHigh - todayLow\n    const currentFromLow = currentPrice - todayLow\n    const holdingGainsPercent = currentFromLow / dayRange\n    \n    if (holdingGainsPercent < 0.5) return null // Must hold >50% of range\n\n    // Calculate room to next resistance\n    const roomToResistance = this.calculateRoomToResistance(candles, currentPrice, currentATR)\n    if (roomToResistance < this.DEFAULT_CRITERIA.minRoomToResistance) return null\n\n    // Position sizing (risk 0.5-1% of account)\n    const riskPercent = 0.75 // 0.75% risk for overnight holds\n    const stopDistance = currentPrice - keyLevel\n    const riskAmount = accountSize * (riskPercent / 100)\n    const positionSize = Math.floor(riskAmount / stopDistance)\n\n    // Targets: Pre-market scale at 3-5%, opening hour at 5-8%\n    const targets = [\n      currentPrice * 1.03, // 3% pre-market target\n      currentPrice * 1.05, // 5% opening hour target\n      currentPrice * 1.08  // 8% extended target\n    ]\n\n    const confidence = this.calculateOvernightConfidence(\n      changePercent, holdingGainsPercent, currentVolume, roomToResistance\n    )\n\n    return {\n      strategy: 'overnight_momentum',\n      confidence,\n      entryPrice: currentPrice,\n      stopLoss: keyLevel,\n      targets,\n      positionSize,\n      riskAmount,\n      holdingPeriod: 'overnight',\n      keyLevel,\n      invalidation: `Daily close below ${keyLevel.toFixed(2)} or gap down below level`,\n      notes: [\n        'Enter final 30-60 min before close',\n        'Exit pre-market on strength or first 45min',\n        'Hard stop if gaps below defended level',\n        'Scale out aggressively if gaps >1 ATR up'\n      ],\n      // Precise entry execution\n      preciseEntry: {\n        price: currentPrice * 0.999, // Slightly below current for better fill\n        orderType: 'limit',\n        timing: 'Final 30-60 minutes before market close',\n        conditions: [\n          `Stock holding above ${keyLevel.toFixed(2)} (defended level)`,\n          `Volume above ${(currentVolume * 0.8).toLocaleString()} shares`,\n          `Price above ${current8EMA.toFixed(2)} (8-EMA)`,\n          'No late-day selling pressure'\n        ],\n        urgency: 'wait_for_pullback'\n      },\n      // Precise exit execution\n      preciseExit: {\n        stopLoss: {\n          price: keyLevel * 0.995, // Slightly below key level\n          orderType: 'stop',\n          reason: 'Defended level broken - invalidates setup',\n          triggerConditions: [\n            'Any close below defended level',\n            'Gap down below key level',\n            'Heavy selling into close'\n          ]\n        },\n        takeProfits: [\n          {\n            price: targets[0],\n            percentage: 33,\n            target: 'T1 - Pre-market (3%)',\n            reasoning: 'Take profits on pre-market strength',\n            orderType: 'limit'\n          },\n          {\n            price: targets[1],\n            percentage: 33,\n            target: 'T2 - Opening hour (5%)',\n            reasoning: 'Scale out on opening momentum',\n            orderType: 'limit'\n          },\n          {\n            price: targets[2],\n            percentage: 34,\n            target: 'T3 - Extended (8%)',\n            reasoning: 'Final exit on extended move',\n            orderType: 'limit'\n          }\n        ]\n      },\n      // Risk management details\n      riskManagement: {\n        maxRiskDollars: riskAmount,\n        accountRiskPercent: riskPercent,\n        sharesForRisk: positionSize,\n        invalidationPrice: keyLevel,\n        timeStopHours: 18, // Exit by next day close if no movement\n        maxDrawdownPercent: 2.0\n      },\n      // Execution plan\n      executionPlan: {\n        entryInstructions: [\n          '1. Wait for final 30-60 minutes before close',\n          '2. Confirm stock is holding defended level',\n          '3. Place limit order slightly below current price',\n          '4. Cancel if not filled by close'\n        ],\n        exitInstructions: [\n          '1. Set stop-loss immediately after fill',\n          '2. Monitor pre-market for gap up',\n          '3. Scale out 1/3 at each target level',\n          '4. Exit all by 10:15 AM if no momentum'\n        ],\n        monitoringPoints: [\n          'Pre-market price action and volume',\n          'Opening gap and first 15-minute candle',\n          'Key level defense throughout session',\n          'Overall market sentiment'\n        ],\n        contingencyPlans: [\n          'If gaps down: Exit immediately at market open',\n          'If gaps up >2%: Scale out more aggressively',\n          'If sideways: Exit by 10:15 AM',\n          'If market weakness: Tighten stops'\n        ]\n      }\n    }\n  }\n\n  // Strategy #2: Technical Breakout Trend-Follow (8-EMA + 200-SMA)\n  static analyzeTechnicalBreakout(\n    symbol: string,\n    candles: CandlestickData[],\n    quote: StockData,\n    accountSize: number = 100000\n  ): StrategySetup | null {\n    if (candles.length < 50) return null\n\n    const closes = candles.map(c => c.close)\n    const volumes = candles.map(c => c.volume)\n    const currentPrice = quote.price\n\n    // Calculate technical indicators (adjusted for shorter history)\n    const sma50 = TechnicalIndicators.sma(closes, Math.min(50, closes.length - 1))\n    const ema8 = TechnicalIndicators.ema(closes, Math.min(8, closes.length - 1))\n    const atr = this.calculateATR(candles, Math.min(14, candles.length - 1))\n\n    const current50SMA = sma50[sma50.length - 1]\n    const current8EMA = ema8[ema8.length - 1]\n    const currentATR = atr[atr.length - 1]\n\n    // Basic qualification filters (using 50-day SMA)\n    if (!this.passesBasicFilters(quote, quote.volume, current50SMA, currentPrice)) {\n      return null\n    }\n\n    // Check if in clear uptrend above 50-SMA (adjusted from 200-SMA)\n    if (currentPrice <= current50SMA) return null\n\n    // Check 8-EMA behavior - should be \"hugging\" the 8-EMA\n    const distanceFrom8EMA = Math.abs(currentPrice - current8EMA)\n    const emaDistancePercent = (distanceFrom8EMA / currentPrice) * 100\n    \n    // Should be close to 8-EMA (within 2-3% for quality trend-follow)\n    if (emaDistancePercent > 3.0) return null\n\n    // Check for recent breakout or EMA reclaim\n    const recentEMAReclaim = this.checkEMAReclaim(closes, ema8, 5) // Last 5 days\n    if (!recentEMAReclaim) return null\n\n    // Volume expansion check\n    const avgVolume = TechnicalIndicators.sma(volumes, 20)\n    const currentAvgVolume = avgVolume[avgVolume.length - 1]\n    const volumeExpansion = quote.volume / currentAvgVolume\n    \n    if (volumeExpansion < 1.2) return null // Need some volume expansion\n\n    // Calculate room to next resistance\n    const roomToResistance = this.calculateRoomToResistance(candles, currentPrice, currentATR)\n    if (roomToResistance < 1.5) return null // Need more room for trend-follow\n\n    // Position sizing (risk 1% of account)\n    const riskPercent = 1.0\n    const stopDistance = currentPrice - current8EMA // Stop at 8-EMA break\n    const riskAmount = accountSize * (riskPercent / 100)\n    const positionSize = Math.floor(riskAmount / stopDistance)\n\n    // Targets: Scale at resistance levels\n    const targets = [\n      currentPrice * 1.05, // 5% first target\n      currentPrice * 1.10, // 10% second target\n      currentPrice * 1.15  // 15% extended target\n    ]\n\n    const confidence = this.calculateBreakoutConfidence(\n      emaDistancePercent, volumeExpansion, roomToResistance, quote.changePercent\n    )\n\n    return {\n      strategy: 'technical_breakout',\n      confidence,\n      entryPrice: currentPrice,\n      stopLoss: current8EMA,\n      targets,\n      positionSize,\n      riskAmount,\n      holdingPeriod: 'days_to_weeks',\n      keyLevel: current8EMA,\n      invalidation: `Daily close below 8-EMA (${current8EMA.toFixed(2)})`,\n      notes: [\n        'Enter on afternoon reclaim of 8-EMA',\n        'Add only on higher-low pullbacks to 8-EMA',\n        'Scale partials at resistance levels',\n        'Exit on daily close below 8-EMA'\n      ],\n      // Precise entry execution\n      preciseEntry: {\n        price: current8EMA * 1.002, // Slightly above 8-EMA for confirmation\n        orderType: 'limit',\n        timing: 'Afternoon reclaim or first pullback to 8-EMA',\n        conditions: [\n          `Price reclaiming ${current8EMA.toFixed(2)} (8-EMA) with volume`,\n          `Above ${current50SMA.toFixed(2)} (50-day SMA)`,\n          `Volume expansion above ${(quote.volume * 1.2).toLocaleString()}`,\n          'No major resistance overhead'\n        ],\n        urgency: 'breakout_confirmation'\n      },\n      // Precise exit execution\n      preciseExit: {\n        stopLoss: {\n          price: current8EMA * 0.998, // Slightly below 8-EMA\n          orderType: 'stop',\n          reason: '8-EMA breakdown invalidates trend-follow setup',\n          triggerConditions: [\n            'Daily close below 8-EMA',\n            'Intraday break with volume',\n            'Loss of 50-SMA support'\n          ]\n        },\n        takeProfits: [\n          {\n            price: targets[0],\n            percentage: 25,\n            target: 'R1 - First resistance (5%)',\n            reasoning: 'Take partial profits at first resistance',\n            orderType: 'limit'\n          },\n          {\n            price: targets[1],\n            percentage: 35,\n            target: 'R2 - Major resistance (10%)',\n            reasoning: 'Scale out at major resistance level',\n            orderType: 'limit'\n          },\n          {\n            price: targets[2],\n            percentage: 40,\n            target: 'R3 - Extension (15%)',\n            reasoning: 'Final exit on extended breakout',\n            orderType: 'limit'\n          }\n        ]\n      },\n      // Risk management details\n      riskManagement: {\n        maxRiskDollars: riskAmount,\n        accountRiskPercent: riskPercent,\n        sharesForRisk: positionSize,\n        invalidationPrice: current8EMA,\n        timeStopHours: 72, // 3 trading days max hold if no progress\n        maxDrawdownPercent: 3.0\n      },\n      // Execution plan\n      executionPlan: {\n        entryInstructions: [\n          '1. Wait for afternoon reclaim of 8-EMA',\n          '2. Confirm volume expansion on breakout',\n          '3. Place limit order above 8-EMA',\n          '4. Only enter on higher-low pullbacks'\n        ],\n        exitInstructions: [\n          '1. Set stop-loss below 8-EMA immediately',\n          '2. Scale out 25% at first resistance',\n          '3. Trail stop to breakeven after R1',\n          '4. Exit remaining on 8-EMA breakdown'\n        ],\n        monitoringPoints: [\n          '8-EMA as dynamic support/resistance',\n          'Volume confirmation on moves',\n          'Overall market trend alignment',\n          'Sector strength/weakness'\n        ],\n        contingencyPlans: [\n          'If fails at resistance: Tighten stops',\n          'If market weakness: Exit early',\n          'If sector rotation: Consider exit',\n          'If extended: Take more profits'\n        ]\n      }\n    }\n  }\n\n  // Helper methods\n  private static passesBasicFilters(\n    quote: StockData,\n    volume: number,\n    sma50: number,\n    price: number\n  ): boolean {\n    return (\n      price >= this.DEFAULT_CRITERIA.minPrice &&\n      volume >= this.DEFAULT_CRITERIA.minVolume &&\n      (quote.marketCap || 0) >= this.DEFAULT_CRITERIA.minMarketCap &&\n      price > sma50 // Using 50-day SMA instead of 200-day for shorter history\n    )\n  }\n\n  private static calculateATR(candles: CandlestickData[], period: number): number[] {\n    const trueRanges: number[] = []\n    \n    for (let i = 1; i < candles.length; i++) {\n      const high = candles[i].high\n      const low = candles[i].low\n      const prevClose = candles[i - 1].close\n      \n      const tr = Math.max(\n        high - low,\n        Math.abs(high - prevClose),\n        Math.abs(low - prevClose)\n      )\n      \n      trueRanges.push(tr)\n    }\n    \n    return TechnicalIndicators.sma(trueRanges, period)\n  }\n\n  private static calculateVWAP(candle: CandlestickData): number {\n    // Simplified VWAP calculation using typical price\n    return (candle.high + candle.low + candle.close) / 3\n  }\n\n  private static calculateRoomToResistance(\n    candles: CandlestickData[], \n    currentPrice: number, \n    atr: number\n  ): number {\n    // Find recent highs as resistance levels\n    const recentHighs = candles.slice(-20).map(c => c.high)\n    const maxHigh = Math.max(...recentHighs)\n    const roomToHigh = maxHigh - currentPrice\n    return roomToHigh / atr\n  }\n\n  private static checkEMAReclaim(closes: number[], ema8: number[], lookback: number): boolean {\n    // Check if price recently reclaimed 8-EMA\n    for (let i = Math.max(0, closes.length - lookback); i < closes.length - 1; i++) {\n      if (closes[i] < ema8[i] && closes[i + 1] > ema8[i + 1]) {\n        return true // Found a reclaim\n      }\n    }\n    return false\n  }\n\n  private static calculateOvernightConfidence(\n    changePercent: number,\n    holdingGains: number,\n    volume: number,\n    roomToResistance: number\n  ): number {\n    let confidence = 50\n\n    // Change percent bonus\n    if (changePercent > 5) confidence += 15\n    else if (changePercent > 3) confidence += 10\n    else if (changePercent > 2) confidence += 5\n\n    // Holding gains bonus\n    if (holdingGains > 0.8) confidence += 15\n    else if (holdingGains > 0.6) confidence += 10\n    else if (holdingGains > 0.5) confidence += 5\n\n    // Volume bonus\n    if (volume > 2000000) confidence += 10\n    else if (volume > 1000000) confidence += 5\n\n    // Room to resistance\n    if (roomToResistance > 2) confidence += 10\n    else if (roomToResistance > 1.5) confidence += 5\n\n    return Math.min(95, Math.max(30, confidence))\n  }\n\n  private static calculateBreakoutConfidence(\n    emaDistance: number,\n    volumeExpansion: number,\n    roomToResistance: number,\n    changePercent: number\n  ): number {\n    let confidence = 60\n\n    // EMA proximity bonus (closer is better for trend-follow)\n    if (emaDistance < 1) confidence += 15\n    else if (emaDistance < 2) confidence += 10\n    else if (emaDistance < 3) confidence += 5\n\n    // Volume expansion bonus\n    if (volumeExpansion > 2) confidence += 15\n    else if (volumeExpansion > 1.5) confidence += 10\n    else if (volumeExpansion > 1.2) confidence += 5\n\n    // Room to resistance\n    if (roomToResistance > 3) confidence += 15\n    else if (roomToResistance > 2) confidence += 10\n    else if (roomToResistance > 1.5) confidence += 5\n\n    // Positive momentum\n    if (changePercent > 2) confidence += 5\n\n    return Math.min(95, Math.max(40, confidence))\n  }\n}\n", "import { NextRequest, NextResponse } from 'next/server'\nimport { SwingTradingStrategies } from '@/lib/swingStrategies'\nimport { PolygonAPI } from '@/lib/polygon'\nimport { FMPAPI } from '@/lib/fmp'\nimport { format, subDays } from 'date-fns'\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ symbol: string }> }\n) {\n  try {\n    const { symbol } = await params\n    const { searchParams } = new URL(request.url)\n    \n    const accountSize = parseInt(searchParams.get('accountSize') || '100000')\n    const strategy = searchParams.get('strategy') // 'overnight', 'breakout', or 'both'\n    \n    if (!symbol) {\n      return NextResponse.json(\n        { error: 'Symbol parameter is required' },\n        { status: 400 }\n      )\n    }\n\n    // Get stock data\n    const fmpAPI = new FMPAPI(process.env.FMP_API_KEY)\n    const polygonAPI = new PolygonAPI(process.env.POLYGON_API_KEY)\n    \n    const [quote, historicalData] = await Promise.all([\n      fmpAPI.getStockQuote(symbol.toUpperCase()),\n      getHistoricalData(polygonAPI, symbol.toUpperCase())\n    ])\n\n    if (!historicalData || historicalData.length < 30) {\n      return NextResponse.json(\n        { error: 'Insufficient historical data for strategy analysis - need at least 30 days' },\n        { status: 400 }\n      )\n    }\n\n    // Analyze strategies\n    const result: any = {\n      symbol: symbol.toUpperCase(),\n      quote,\n      accountSize,\n      scanTime: new Date().toISOString()\n    }\n\n    if (!strategy || strategy === 'overnight' || strategy === 'both') {\n      result.overnightSetup = SwingTradingStrategies.analyzeOvernightMomentum(\n        symbol.toUpperCase(), historicalData, quote, accountSize\n      )\n    }\n\n    if (!strategy || strategy === 'breakout' || strategy === 'both') {\n      result.breakoutSetup = SwingTradingStrategies.analyzeTechnicalBreakout(\n        symbol.toUpperCase(), historicalData, quote, accountSize\n      )\n    }\n\n    // Determine best strategy if both analyzed\n    if (result.overnightSetup && result.breakoutSetup) {\n      result.bestStrategy = result.overnightSetup.confidence > result.breakoutSetup.confidence \n        ? 'overnight_momentum' \n        : 'technical_breakout'\n      result.overallScore = Math.max(result.overnightSetup.confidence, result.breakoutSetup.confidence) + 5\n    } else if (result.overnightSetup) {\n      result.bestStrategy = 'overnight_momentum'\n      result.overallScore = result.overnightSetup.confidence\n    } else if (result.breakoutSetup) {\n      result.bestStrategy = 'technical_breakout'\n      result.overallScore = result.breakoutSetup.confidence\n    } else {\n      result.overallScore = 0\n      result.message = 'No valid swing trading setups found for current market conditions'\n    }\n    \n    return NextResponse.json(result)\n  } catch (error) {\n    console.error('Error in strategy analysis API:', error)\n    return NextResponse.json(\n      { error: 'Failed to perform strategy analysis' },\n      { status: 500 }\n    )\n  }\n}\n\nasync function getHistoricalData(polygonAPI: PolygonAPI, symbol: string) {\n  const to = format(new Date(), 'yyyy-MM-dd')\n  const from = format(subDays(new Date(), 100), 'yyyy-MM-dd')\n  \n  try {\n    return await polygonAPI.getHistoricalData(symbol, 'day', 1, from, to)\n  } catch (error) {\n    console.warn(`Historical data failed for ${symbol}`)\n    throw error\n  }\n}\n", "import { AppRouteRouteModule } from \"next/dist/esm/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/esm/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/esm/server/lib/patch-fetch\";\nimport { getRequestMeta } from \"next/dist/esm/server/request-meta\";\nimport { getTracer, SpanKind } from \"next/dist/esm/server/lib/trace/tracer\";\nimport { normalizeAppPath } from \"next/dist/esm/shared/lib/router/utils/app-paths\";\nimport { NodeNextRequest, NodeNextResponse } from \"next/dist/esm/server/base-http/node\";\nimport { NextRequestAdapter, signalFromNodeResponse } from \"next/dist/esm/server/web/spec-extension/adapters/next-request\";\nimport { BaseServerSpan } from \"next/dist/esm/server/lib/trace/constants\";\nimport { getRevalidateReason } from \"next/dist/esm/server/instrumentation/utils\";\nimport { sendResponse } from \"next/dist/esm/server/send-response\";\nimport { fromNodeOutgoingHttpHeaders, toNodeOutgoingHttpHeaders } from \"next/dist/esm/server/web/utils\";\nimport { getCacheControlHeader } from \"next/dist/esm/server/lib/cache-control\";\nimport { INFINITE_CACHE, NEXT_CACHE_TAGS_HEADER } from \"next/dist/esm/lib/constants\";\nimport { NoFallbackError } from \"next/dist/esm/shared/lib/no-fallback-error.external\";\nimport { CachedRouteKind } from \"next/dist/esm/server/response-cache\";\nimport * as userland from \"INNER_APP_ROUTE\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/analysis/strategy/[symbol]/route\",\n        pathname: \"/api/analysis/strategy/[symbol]\",\n        filename: \"route\",\n        bundlePath: \"\"\n    },\n    distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n    relativeProjectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || '',\n    resolvedPagePath: \"[project]/swing-trader-ai/src/app/api/analysis/strategy/[symbol]/route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\nexport async function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/analysis/strategy/[symbol]/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (process.env.TURBOPACK) {\n        srcPage = srcPage.replace(/\\/index$/, '') || '/';\n    } else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = process.env.__NEXT_MULTI_ZONE_DRAFT_MODE;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = normalizeAppPath(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = getTracer();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: getRequestMeta(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new NodeNextRequest(req);\n    const nodeNextRes = new NodeNextResponse(res);\n    const nextReq = NextRequestAdapter.fromNodeNextRequest(nodeNextReq, signalFromNodeResponse(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!getRequestMeta(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = toNodeOutgoingHttpHeaders(response.headers);\n                        if (cacheTags) {\n                            headers[NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await sendResponse(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: getRevalidateReason({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!getRequestMeta(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = fromNodeOutgoingHttpHeaders(cacheEntry.value.headers);\n            if (!(getRequestMeta(req, 'minimalMode') && isIsr)) {\n                headers.delete(NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', getCacheControlHeader(cacheEntry.cacheControl));\n            }\n            await sendResponse(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        if (!(err instanceof NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: getRevalidateReason({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await sendResponse(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n"], "names": [], "mappings": "8yCAAA,IAAA,EAAA,EAAA,CAAA,CAAA,OAGA,IAAM,EAAmB,yBACnB,EAAU,QAAQ,GAAG,CAAC,eAAe,AAEpC,OAAM,EACH,MAAc,AAEtB,aAAY,CAAe,CAAE,CAE3B,GADA,IAAI,CAAC,MAAM,CAAG,GAAU,GAAW,GAC/B,CAAC,IAAI,CAAC,MAAM,CACd,CADgB,KACV,AAAI,MAAM,8BAEpB,CAGA,MAAM,cAAc,CAAc,CAAsB,CACtD,GAAI,CAWF,IAAM,EAAO,CATI,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAiB,8CAA8C,EAAE,EAAA,CAAQ,CAC5E,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MACf,AADqB,CAEvB,EAAA,EAGoB,IAAI,CAAC,OAAO,CAAC,EAAE,CACrC,GAAI,CAAC,EACH,IADS,EACH,AAAI,MAAM,CAAC,kBAAkB,EAAE,EAAA,CAAQ,EAG/C,IAAM,EAAS,EAAK,KAAK,EAAI,EACvB,EAAU,EAAO,GAAG,EAAI,CAAC,EACzB,EAAc,EAAO,OAAO,EAAI,CAAC,EACrB,EAAO,SAAS,CAIlC,GAJsC,CAAC,AAIjC,EAAe,CAHH,EAAO,SAAS,EAAI,EAAC,EAGR,CAAC,EAAI,EAAQ,CAAC,EAAI,EAAY,CAAC,CACxD,EAAY,EAAY,CAAC,EAAI,EAAQ,CAAC,CACtC,EAAS,EAAe,EAG9B,MAAO,CACL,OAAQ,EAAO,WAAW,GAC1B,KAAM,EAAO,IAAI,EAAI,EAAO,WAAW,GACvC,MAAO,SACP,EACA,cAPqB,EAAS,EAAa,IAQ3C,OAAQ,EAAQ,CAAC,EAAI,EACrB,UAAW,EAAO,UAAU,CAC5B,GAAI,OACJ,cAAU,CACZ,CACF,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,2CAA4C,GAG1D,GAAI,CAWF,IAAM,EAAO,CAVY,MAAM,EAAA,OAAK,CAAC,GAAG,CACtC,CAAA,EAAG,EAAiB,gBAAgB,EAAE,EAAO,KAAK,CAAC,CACnD,CACE,OAAQ,CACN,SAAU,OACV,OAAQ,IAAI,CAAC,MACf,AADqB,CAEvB,EAAA,EAG4B,IAAI,CAAC,OAAO,CAAC,EAAE,CAC7C,MAAO,CACL,OAAQ,EAAO,WAAW,GAC1B,KAAM,EAAO,WAAW,GACxB,MAAO,EAAK,CAAC,CACb,OAAQ,EAAK,CAAC,CAAG,EAAK,CAAC,CACvB,cAAgB,CAAC,EAAK,CAAC,CAAG,GAAK,AAAC,EAAI,EAAK,CAAC,CAAI,IAC9C,OAAQ,EAAK,CAAC,CACd,eAAW,EACX,QAAI,EACJ,cAAU,CACZ,CACF,CAAE,MAAO,EAAe,CAEtB,MADA,QAAQ,KAAK,CAAC,gCAAiC,GACrC,AAAJ,MAAU,CAAC,0BAA0B,EAAE,EAAA,CAAQ,CACvD,CACF,CACF,CAGA,MAAM,kBACJ,CAAc,CACd,EAAyD,KAAK,CAC9D,EAAqB,CAAC,CACtB,CAAY,CACZ,CAAU,CACkB,CAC5B,GAAI,CACF,IAAM,EAAW,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAiB,gBAAgB,EAAE,EAAO,OAAO,EAAE,EAAW,CAAC,EAAE,EAAS,CAAC,EAAE,EAAK,CAAC,EAAE,EAAA,CAAI,CAC5F,CACE,OAAQ,CACN,SAAU,OACV,KAAM,MACN,MAAO,IACP,OAAQ,IAAI,CAAC,MACf,AADqB,CAEvB,GAGF,GAAI,CAAC,EAAS,IAAI,CAAC,OAAO,EAAI,AAAiC,GAAG,GAA3B,IAAI,CAAC,OAAO,CAAC,MAAM,CAExD,OADA,QAAQ,IAAI,CAAC,CAAC,6BAA6B,EAAE,EAAA,CAAQ,EAC9C,EAAE,CAGX,OAAO,EAAS,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,AAAC,IAAiB,CACjD,IADgD,MACrC,EAAO,CAAC,CACnB,KAAM,EAAO,CAAC,CACd,KAAM,EAAO,CAAC,CACd,IAAK,EAAO,CAAC,CACb,MAAO,EAAO,CAAC,CACf,OAAQ,EAAO,CAAC,CAClB,CAAC,CACH,CAAE,MAAO,EAAO,CASd,MARA,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,EAAO,CAAC,CAAC,CAAE,GAG3D,EAAM,QAAQ,EAAE,CAClB,QAAQ,KAAK,CAAC,CAAC,mBAAmB,EAAE,EAAM,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,EAAM,QAAQ,CAAC,UAAU,CAAA,CAAE,EAC1F,QAAQ,KAAK,CAAC,iBAAkB,EAAM,QAAQ,CAAC,IAAI,GAG/C,AAAI,MAAM,CAAC,oCAAoC,EAAE,EAAO,EAAE,EAAE,EAAM,OAAO,CAAA,CAAE,CACnF,CACF,CAGA,MAAM,kBAAkB,CAAc,CAAE,CACtC,GAAI,CAUF,MAAO,CATU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAiB,sBAAsB,EAAE,EAAA,CAAQ,CACpD,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,CAAC,OAAO,AAC9B,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,kCAAmC,GAC1C,IACT,CACF,CAGA,MAAM,iBAAkB,CACtB,GAAI,CAUF,MAAO,CATU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAiB,oBAAoB,CAAC,CACzC,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,AACtB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,gCAAiC,GACxC,IACT,CACF,CAGA,MAAM,aAAa,CAAa,CAAE,EAAgB,EAAE,CAAE,CACpD,GAAI,CAcF,MAbiB,AAaV,OAbgB,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAiB,qBAAqB,CAAC,CAC1C,CACE,OAAQ,CACN,OAAQ,EACR,OAAQ,SACR,OAAQ,aACR,EACA,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,CAAC,OAAO,EAAI,EAAE,AACpC,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,0BAA2B,GAClC,EAAE,AACX,CACF,CACF,CAG0B,IAAI,gDCzM9B,IAAA,EAAA,EAAA,CAAA,CAAA,OAGA,IAAM,EAAe,wCACf,EAAU,QAAQ,GAAG,CAAC,WAAW,AAEhC,OAAM,EACH,MAAc,AAEtB,aAAY,CAAe,CAAE,CAE3B,GADA,IAAI,CAAC,MAAM,CAAG,GAAU,GAAW,GAC/B,CAAC,IAAI,CAAC,MAAM,CACd,CADgB,KACN,AAAJ,MAAU,0BAEpB,CAGA,MAAM,cAAc,CAAc,CAAsB,CACtD,GAAI,CAUF,IAAM,EAAO,CATI,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,UAAU,EAAE,EAAA,CAAQ,CACpC,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGoB,IAAI,CAAC,EAAE,CAC7B,GAAI,CAAC,EACH,IADS,EACH,AAAI,MAAM,CAAC,yBAAyB,EAAE,EAAA,CAAQ,EAGtD,MAAO,CACL,OAAQ,EAAK,MAAM,CACnB,KAAM,EAAK,IAAI,EAAI,EAAK,MAAM,CAC9B,MAAO,EAAK,KAAK,CACjB,OAAQ,EAAK,MAAM,CACnB,cAAe,EAAK,iBAAiB,CACrC,OAAQ,EAAK,MAAM,CACnB,UAAW,EAAK,SAAS,CACzB,GAAI,EAAK,EAAE,CACX,cAAU,CACZ,CACF,CAAE,MAAO,CAFe,CAER,CAEd,MADA,QAAQ,KAAK,CAAC,iBAH+C,iBAGZ,GAC3C,AAAI,MAAM,CAAC,0BAA0B,EAAE,EAAA,CAAQ,CACvD,CACF,CAGA,MAAM,kBAAkB,CAAc,CAAE,CACtC,GAAI,CAUF,MATiB,AASV,OATgB,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,YAAY,EAAE,EAAA,CAAQ,CACtC,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,CAAC,EAAE,AACzB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,kCAAmC,GAC1C,IACT,CACF,CAGA,MAAM,mBAAmB,CAAc,CAAE,CACvC,GAAI,CAUF,MAAO,CATU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,WAAW,EAAE,EAAA,CAAQ,CACrC,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,CAAC,EAAE,AACzB,CAD0B,AACxB,MAAO,EAAO,CAEd,OADA,KAF6C,GAErC,KAAK,CAAC,mCAAoC,GAC3C,IACT,CACF,CAGA,MAAM,cAAc,CAAc,CAAE,CAClC,GAAI,CAUF,MATiB,AASV,OATgB,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,gBAAgB,EAAE,EAAA,CAAQ,CAC1C,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,CAAC,EAAE,AACzB,CAD0B,AACxB,MAAO,EAAO,CAEd,OADA,MAF8C,EAEtC,KAAK,CAAC,8BAA+B,GACtC,IACT,CACF,CAGA,MAAM,0BAA0B,CAAc,CAAE,CAC9C,GAAI,CAUF,MATiB,AASV,OATgB,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,kCAAkC,EAAE,EAAA,CAAQ,CAC5D,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,AACtB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,0CAA2C,GAClD,EAAE,AACX,CACF,CAGA,MAAM,oBAAoB,CAAa,CAAE,CAAW,CAAE,CACpD,GAAI,CACF,IAAM,EAAc,CAClB,OAAQ,IAAI,CAAC,MAAM,AACrB,EAUA,OARI,IAAM,EAAO,IAAI,CAAG,CAAA,EACpB,IAAI,EAAO,EAAE,CAAG,CAAA,EAOb,CALU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,oBAAoB,CAAC,CACrC,QAAE,CAAO,EAAA,EAGK,IAClB,AADsB,CACpB,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,oCAAqC,GAC5C,EAAE,AACX,CACF,CAGA,MAAM,oBAAoB,CAAa,CAAE,CAAW,CAAE,CACpD,GAAI,CACF,IAAM,EAAc,CAClB,OAAQ,IAAI,CAAC,MAAM,AACrB,EAUA,OARI,GAAM,GAAO,IAAI,CAAG,CAAA,EACpB,IAAI,EAAO,EAAE,CAAG,CAAA,EAOb,CALU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,qBAAqB,CAAC,CACtC,QAAE,CAAO,EAAA,EAGK,IAAI,AACtB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,oCAAqC,GAC5C,EAAE,AACX,CACF,CAGA,MAAM,aAAa,CAAa,CAAE,EAAgB,EAAE,CAAE,CACpD,GAAI,CAYF,MAAO,CAXU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,UAAU,CAAC,CAC3B,CACE,OAAQ,OACN,QACA,EACA,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,AACtB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,0BAA2B,GAClC,EAAE,AACX,CACF,CAGA,MAAM,sBAAuB,CAC3B,GAAI,CAUF,MAAO,CATU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,sBAAsB,CAAC,CACvC,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,AACtB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,qCAAsC,GAC7C,EAAE,AACX,CACF,CAGA,MAAM,gBAAgB,CAAsC,CAAE,CAC5D,GAAI,CAUF,MAAO,CATU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,iBAAiB,EAAE,EAAA,CAAM,CACzC,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,AACtB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,EAAK,CAAC,CAAC,CAAE,GACzC,EAAE,AACX,CACF,CAKA,MAAM,oBAAoB,CAAe,CAAE,EAAe,EAAE,CAAE,CAC5D,GAAI,CACF,IAAM,EAAW,IAAI,KACrB,EAAS,OAAO,CAAC,EAAS,OAAO,GAAK,GACtC,IAAM,EAAS,IAAI,KAcnB,MAAO,CAZU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,oBAAoB,CAAC,CACrC,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,CACnB,KAAM,EAAS,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAC1C,GAAI,EAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CACtC,GAAI,GAAU,CAAE,OAAQ,EAAO,WAAW,EAAG,CAAC,AAChD,CACF,EAAA,EAGc,IAAI,EAAI,EAC1B,AAD4B,CAC1B,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,oCAAqC,GAC5C,EAAE,AACX,CACF,CAGA,MAAM,aAAa,CAAc,CAAE,EAAgB,EAAE,CAAE,CACrD,GAAI,CAYF,MAXiB,AAWV,OAXgB,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,cAAc,CAAC,CAC/B,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,CACnB,QAAS,EAAO,WAAW,SAC3B,CACF,CACF,EAAA,EAGc,IAAI,EAAI,EAAE,AAC5B,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,6BAA8B,GACrC,EAAE,AACX,CACF,CAGA,MAAM,0BAA0B,CAAc,CAAE,EAAe,EAAE,CAAE,CACjE,GAAI,CAWF,MAAO,CAVU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,kCAAkC,EAAE,EAAO,WAAW,GAAA,CAAI,CAC1E,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,CACnB,MAAO,CACT,CACF,EAAA,EAGc,IAAI,EAAI,EAAE,AAC5B,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,0CAA2C,GAClD,EAAE,AACX,CACF,CAGA,MAAM,kBAAkB,CAAc,CAAE,EAAe,EAAE,CAAE,CACzD,GAAI,CACF,IAAM,EAAW,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,mBAAmB,CAAC,CACpC,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,CACnB,OAAQ,EAAO,WAAW,GAC1B,MAAO,AAAO,EAAE,CAClB,CACF,GAII,EAAa,IAAI,KAGvB,OAFA,EAAW,OAAO,CAAC,EAAW,OAPkC,AAO3B,GAAK,GAEnC,CAAC,EAAS,IAAI,EAAI,EAAE,AAAF,EAAI,MAAM,CAAE,AAAD,GAClC,IAAI,KAAK,EAAM,UAAU,GAAK,EAElC,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,kCAAmC,GAC1C,EAAE,AACX,CACF,CAGA,MAAM,cAAc,CAAc,CAAE,EAAe,EAAE,CAAE,CACrD,GAAI,CACF,IAAM,EAAW,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,gBAAgB,EAAE,EAAO,WAAW,GAAA,CAAI,CACxD,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,CACnB,MAAc,EAAP,AAAS,CAClB,CACF,GAII,EAAa,IAAI,KAGvB,OAFA,EAAW,OAAO,CAAC,EAAW,EAP6B,KAOtB,GAAK,GAEnC,CAAC,EAAS,IAAI,EAAI,EAAA,AAAE,EAAE,MAAM,CAAC,AAAC,GACnC,IAAI,KAAK,EAAO,SAAS,GAAK,EAElC,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,8BAA+B,GACtC,EAAE,AACX,CACF,CAGA,MAAM,kBAAkB,CAAc,CAAE,CACtC,GAAI,CAUF,IAAM,EAAO,CATI,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,UAAU,EAAE,EAAO,WAAW,GAAA,CAAI,CAClD,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MACf,AADqB,CAEvB,EAAA,EAGoB,IAAI,CAAC,EAAE,CAC7B,GAAI,CAAC,EAAM,OAAO,KAElB,MAAO,CACL,OAAQ,EAAK,MAAM,CACnB,MAAO,EAAK,KAAK,CACjB,cAAe,EAAK,aAAa,CACjC,OAAQ,EAAK,MAAM,CACnB,cAAe,EAAK,iBAAiB,CACrC,OAAQ,EAAK,MAAM,CACnB,UAAW,EAAK,SAAS,CACzB,UAAW,EAAK,SAAS,CAEzB,eAAgB,EAAK,cAAc,EAAI,EAAK,KAAK,CACjD,gBAAiB,EAAK,eAAe,EAAI,EAAK,MAAM,CACpD,uBAAwB,EAAK,sBAAsB,EAAI,EAAK,iBAAiB,AAC/E,CACF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,mCAAoC,GAC3C,IACT,CACF,CAGA,MAAM,2BAA2B,CAAiB,CAAE,CAClD,GAAI,CACF,IAAM,EAAgB,EAAQ,GAAG,CAAC,GAAK,EAAE,WAAW,IAAI,IAAI,CAAC,KAU7D,MAAO,CATU,AAST,OATe,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,UAAU,EAAE,EAAA,CAAe,CAC3C,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGe,IAAI,EAAI,EAAA,AAAE,EAAE,GAAG,CAAC,AAAC,IAAe,CAC/C,EAD8C,KACtC,EAAK,MAAM,CACnB,MAAO,EAAK,KAAK,CACjB,cAAe,EAAK,aAAa,CACjC,OAAQ,EAAK,MAAM,CACnB,cAAe,EAAK,iBAAiB,CACrC,OAAQ,EAAK,MAAM,CACnB,UAAW,EAAK,SAAS,CACzB,UAAW,EAAK,SAAS,CACzB,eAAgB,EAAK,cAAc,EAAI,EAAK,KAAK,CACjD,gBAAiB,EAAK,eAAe,EAAI,EAAK,MAAM,CACpD,uBAAwB,EAAK,sBAAsB,EAAI,EAAK,iBAAiB,AAC/E,CAAC,EACH,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,6CAA8C,GACrD,EAAE,AACX,CACF,CAGA,MAAM,kBAAkB,CAAc,CAAE,CACtC,GAAI,CAUF,MAAO,CATU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,YAAY,EAAE,EAAO,WAAW,GAAA,CAAI,CACpD,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,CAAC,EAAE,EAAI,IAC7B,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,kCAAmC,GAC1C,IACT,CACF,CACF,CAGsB,IAAI,4DCrbnB,OAAM,EAEX,OAAO,IAAI,CAAc,CAAE,CAAc,CAAY,CACnD,IAAM,EAAmB,EAAE,CAC3B,IAAK,IAAI,EAAI,EAAS,EAAG,EAAI,EAAK,MAAM,CAAE,IAAK,CAC7C,IAAM,EAAM,EAAK,KAAK,CAAC,EAAI,EAAS,EAAG,EAAI,GAAG,MAAM,CAAC,CAAC,EAAG,IAAM,EAAI,EAAG,GACtE,EAAO,IAAI,CAAC,EAAM,EACpB,CACA,OAAO,CACT,CAGA,OAAO,IAAI,CAAc,CAAE,CAAc,CAAY,CACnD,IAAM,EAAmB,EAAE,CACrB,EAAa,GAAK,CAAD,EAAU,CAAC,CAG9B,EAAM,EAAK,KAAK,CAAC,EAAG,GAAQ,MAAM,CAAC,CAAC,EAAG,IAAM,EAAI,EAAG,GAAK,EAC7D,EAAO,IAAI,CAAC,GAEZ,IAAK,IAAI,EAAI,EAAQ,EAAI,EAAK,MAAM,CAAE,IAAK,AAEzC,EAAO,IAAI,CADX,AACY,EADL,CAAI,CAAC,EAAE,CAAG,EAAe,GAAO,EAAI,CAAL,AAAK,CAAU,EAIvD,OAAO,CACT,CAGA,OAAO,IAAI,CAAc,CAAE,EAAiB,EAAE,CAAY,CACxD,IAAM,EAAkB,EAAE,CACpB,EAAmB,EAAE,CAE3B,IAAK,IAAI,EAAI,EAAG,EAAI,EAAK,MAAM,CAAE,IAAK,CACpC,IAAM,EAAS,CAAI,CAAC,EAAE,CAAG,CAAI,CAAC,EAAI,EAAE,CACpC,EAAM,IAAI,CAAC,EAAS,EAAI,EAAS,GACjC,EAAO,IAAI,CAAC,EAAS,EAAI,KAAK,GAAG,CAAC,GAAU,EAC9C,CAEA,IAAM,EAAW,IAAI,CAAC,GAAG,CAAC,EAAO,GAC3B,EAAY,IAAI,CAAC,GAAG,CAAC,EAAQ,GAEnC,OAAO,EAAS,GAAG,CAAC,CAAC,EAAM,IAElB,IAAO,KAAO,CAAD,CADT,EACc,AADP,CAAS,CAAC,EACH,AADK,CACH,CAE/B,CAGA,OAAO,KAAK,CAAc,CAAE,EAAqB,EAAE,CAAE,EAAqB,EAAE,CAAE,EAAuB,CAAC,CAAE,CACtG,IAAM,EAAU,IAAI,CAAC,GAAG,CAAC,EAAM,GACzB,EAAU,IAAI,CAAC,GAAG,CAAC,EAAM,GAIzB,EAAW,EAAQ,KAAK,CADX,AACY,EADC,GACW,GAAG,CAAC,CAAC,EAAM,IAAM,EAAO,CAAO,CAAC,EAAE,EAEvE,EAAa,IAAI,CAAC,GAAG,CAAC,EAAU,GAChC,EAAY,EAAS,KAAK,CAAC,EAAe,GAAG,GAAG,CAAC,CAAC,EAAM,IAAM,EAAO,CAAU,CAAC,EAAE,EAExF,MAAO,CACL,KAAM,EACN,OAAQ,YACR,CACF,CACF,CAGA,OAAO,eAAe,CAAc,CAAE,EAAiB,EAAE,CAAE,EAAiB,CAAC,CAAE,CAc7E,OAZc,AAYP,AAbK,IAAI,CAAC,GAAG,CAAC,EAAM,GACT,GAAG,CAAC,CAAC,EAAK,KAG1B,IAAM,EAAoB,KAAK,IAAI,CADlB,AADH,AAEsB,EAFjB,KAAK,CAAC,EAAG,EAAI,GACT,MAAM,CAAC,CAAC,EAAK,IAAQ,EAAM,KAAK,GAAG,CAAC,EAAM,EAAK,GAAI,GAAK,GAG/E,MAAO,CACL,MAAO,EAAO,EAAoB,EAClC,OAAQ,EACR,MAAO,EAAO,EAAoB,CACpC,CACF,EAGF,CAGA,OAAO,sBAAsB,CAA0B,CAAE,EAAmB,EAAE,CAA+C,CAC3H,IAAM,EAAQ,EAAQ,GAAG,CAAC,GAAK,EAAE,IAAI,EAC/B,EAAO,EAAQ,GAAG,CAAC,GAAK,EAAE,GAAG,EAE7B,EAAuB,EAAE,CACzB,EAAoB,EAAE,CAE5B,IAAK,IAAI,EAAI,EAAU,EAAI,EAAQ,MAAM,CAAG,EAAU,IAAK,CACzD,IAAM,EAAc,CAAK,CAAC,EAAE,CACtB,EAAa,CAAI,CAAC,EAAE,CAGpB,EAAe,EAAM,KAAK,CAAC,EAAI,EAAU,GAAG,KAAK,CAAC,GAAK,GAAK,IAC9C,EAAM,KAAK,CAAC,EAAI,EAAG,EAAI,EAAW,GAAG,KAAK,CAAC,GAAK,GAAK,GAGnE,EAAY,EAAK,KAAK,CAAC,EAAI,EAAU,GAAG,KAAK,CAAC,GAAK,GAAK,IAC7C,EAAK,KAAK,CAAC,EAAI,EAAG,EAAI,EAAW,GAAG,KAAK,CAAC,GAAK,GAAK,GAEjE,GAAc,EAAW,IAAI,CAAC,GAC9B,GAAW,EAAQ,IAAI,CAAC,EAC9B,CAEA,MAAO,SAAE,aAAS,CAAW,CAC/B,CAGA,OAAO,eAAe,CAA0B,CAAE,EAAiB,EAAE,CAAE,CACrE,IAAM,EAAU,EAAQ,GAAG,CAAC,GAAK,EAAE,MAAM,EACnC,EAAY,IAAI,CAAC,GAAG,CAAC,EAAS,GAC9B,EAAgB,CAAO,CAAC,EAAQ,MAAM,CAAG,EAAE,CAC3C,EAAmB,CAAS,CAAC,EAAU,MAAM,CAAG,EAAE,CAExD,MAAO,eACL,EACA,cAAe,EACf,YAAa,EAAgB,EAC7B,aAAc,EAAgB,AAAmB,MACjD,YAAa,EAAmC,GAAnB,CAC/B,CACF,CAGA,OAAO,kBAAkB,CAA0B,CAAwB,CACzE,IAAM,EAAS,EAAQ,GAAG,CAAC,GAAK,EAAE,KAAK,EACjC,EAAmC,EAAE,CAGrC,EAAM,IAAI,CAAC,GAAG,CAAC,GACf,EAAa,CAAG,CAAC,EAAI,MAAM,CAAG,EAAE,CAElC,EAAwC,UACxC,EAAiB,CAAC,KAAK,EAAE,EAAW,OAAO,CAAC,GAAA,CAAI,CAEhD,EAAa,IAAI,AACnB,EAAY,MACZ,GAAkB,2CACT,EAAa,IACtB,AAD0B,EACd,OACZ,GAAkB,+CAElB,GAAkB,kBAGpB,EAAW,IAAI,CAAC,CACd,KAAM,MACN,MAAO,EACP,OAAQ,EACR,YAAa,CACf,GAGA,IAAM,EAAQ,IAAI,CAAC,GAAG,CAAC,EAAQ,IACzB,EAAQ,IAAI,CAAC,GAAG,CAAC,EAAQ,IACzB,EAAe,CAAM,CAAC,EAAO,MAAM,CAAG,EAAE,CACxC,EAAe,CAAK,CAAC,EAAM,MAAM,CAAG,EAAE,CACtC,EAAe,CAAK,CAAC,EAAM,MAAM,CAAG,EAAE,CAExC,EAAuC,UACvC,EAAgB,CAAC,gBAAgB,EAAE,AAAC,EAAC,EAAe,EAAe,CAAC,EAAI,GAAA,CAAG,CAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAE1F,EAAe,GAAgB,EAAe,GAChD,EAAW,MACX,GAAiB,AAF6C,oBAGrD,EAAe,GAAgB,EAAe,GACvD,EAAW,OACX,EAFqE,CAEpD,oBAEjB,GAAiB,mBAGnB,EAAW,IAAI,CAAC,CACd,KAAM,kBACN,MAAO,CAAC,EAAe,GAAe,CAAC,CAAI,IAC3C,OAAQ,EACR,YAAa,CACf,GAGA,IAAM,EAAW,IAAI,CAAC,IAAI,CAAC,GACrB,EAAc,EAAS,IAAI,CAAC,EAAS,IAAI,CAAC,MAAM,CAAG,EAAE,CACrD,EAAgB,EAAS,MAAM,CAAC,EAAS,MAAM,CAAC,MAAM,CAAG,EAAE,CAC3D,EAAmB,EAAS,SAAS,CAAC,EAAS,SAAS,CAAC,MAAM,CAAG,EAAE,CAEtE,EAAyC,UACzC,EAAkB,CAAC,MAAM,EAAE,EAAY,OAAO,CAAC,GAAG,UAAU,EAAE,EAAc,OAAO,CAAC,GAAA,CAAI,CAExF,EAAc,GAAiB,EAAmB,GAAG,AACvD,EAAa,MACb,GAAmB,uBACV,EAAc,GAAiB,EAAmB,GAAG,AAC9D,EAAa,OACb,GAAmB,uBAEnB,GAAmB,uBAGrB,EAAW,IAAI,CAAC,CACd,KAAM,OACN,MAAO,EACP,OAAQ,EACR,YAAa,CACf,GAGA,IAAM,EAAa,IAAI,CAAC,cAAc,CAAC,GACnC,EAA2C,UAC3C,EAAoB,CAAC,QAAQ,EAAE,CAA0B,IAAzB,EAAW,WAAc,AAAH,CAAM,CAAE,OAAO,CAAC,GAAG,YAAY,CAAC,CAmB1F,OAjBI,EAAW,YAAY,EAAE,AAC3B,EAAe,MACf,GAAqB,gCACZ,EAAW,WAAW,EAC/B,AADiC,EAClB,OACf,GAAqB,kCAErB,GAAqB,mBAGvB,EAAW,IAAI,CAAC,CACd,KAAM,SACN,MAAO,EAAW,WAAW,CAC7B,OAAQ,EACR,YAAa,CACf,GAEO,CACT,CACF,gEC3OA,IAAA,EAAA,EAAA,CAAA,CAAA,MA0EO,OAAM,EACX,OAAwB,iBAAuC,CAC7D,SAAU,EACV,UAAW,IACX,aAAc,IACd,cAAe,EACf,aAAa,EACb,oBAAqB,EACrB,oBAAqB,EACrB,cAAe,QACf,YAAa,QACb,gBAAiB,EACjB,uBAAwB,CAC1B,CAAC,AAGD,QAAO,yBACL,CAAc,CACd,CAA0B,CAC1B,CAAgB,CAChB,EAAsB,GAAM,CACN,CACtB,GAAI,EAAQ,MAAM,CAAG,GAAI,OAAO,KAEhC,IAAM,EAAS,EAAQ,GAAG,CAAC,GAAK,EAAE,KAAK,EACjC,EAAQ,EAAQ,GAAG,CAAC,GAAK,EAAE,IAAI,EAC/B,EAAO,EAAQ,GAAG,CAAC,GAAK,EAAE,GAAG,EACnB,EAAQ,GAAG,CAAC,GAAK,EAAE,MAAM,EAEzC,IAAM,EAAe,EAAM,KAAK,CAC1B,EAAgB,EAAM,MAAM,CAC5B,EAAgB,EAAM,aAAa,CAGnC,EAAQ,EAAA,mBAAmB,CAAC,GAAG,CAAC,EAAQ,KAAK,GAAG,CAAC,GAAI,EAAO,MAAM,CAAG,IAAI,AACzE,EAAO,EAAA,mBAAmB,CAAC,GAAG,CAAC,EAAQ,EADkE,GAC7D,GAAG,CAAC,EAAG,EAAO,MAAM,CAAG,IACnE,EAAM,IAAI,CAAC,YAAY,CAAC,EAAS,KAAK,GAAG,CAAC,GAAI,EAAQ,MAAM,CAAG,IAE/D,EAAe,CAAK,CAAC,EAAM,MAAM,CAAG,EAAE,CACtC,EAAc,CAAI,CAAC,EAAK,MAAM,CAAG,EAAE,CACnC,EAAa,CAAG,CAAC,EAAI,MAAM,CAAG,EAAE,CAGtC,GAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAO,EAAe,EAAc,IAK7D,EAAgB,GAGK,AACrB,EAJqB,GAGK,CARkD,EAQ/C,CAHD,AAGE,EAAe,GAHZ,AAG2B,EACzC,IAAI,CAAC,gBAAgB,CAAC,OAJuB,YAIJ,CAR9D,CAQgE,MARzD,CAQgE,IAIzE,IAAM,EAAW,KAAK,GAAG,CADZ,AACa,IADT,CAAC,aAAa,CAAC,EAAQ,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,EACL,IAAf,EAAqB,CAG/C,EAAY,CAAK,CAAC,EAAM,MAAM,CAAG,EAAE,CACnC,EAAW,CAAI,CAAC,EAAK,MAAM,AAJiD,CAI9C,EAAE,CAGhC,EAAsB,CADL,EAAe,CAAA,GADrB,EAAY,CAAA,EAI7B,GAAI,EAFyC,AAEnB,GAAK,OAAO,KAAK,AAG3C,IAAM,EAAmB,IAAI,CAAC,eAHuC,UAGd,CAAC,EAAS,EAAc,GAC/E,GAAI,EAAmB,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAE,OAAO,KAKzE,IAAM,EAA4B,MAAf,EACb,EAAe,IAD2B,CACtB,EADyB,GAAlB,AACF,CAAC,GAFX,EAAe,CAAA,GAK9B,EAAU,CACC,CAJ4B,IAI3C,EACe,KAAf,EACe,KAAM,AAArB,EACD,CAMD,MAAO,CACL,SAAU,EARgC,mBAS1C,WANiB,IAAI,CAAC,4BAA4B,CAClD,EAAe,EAAqB,EAAe,GAMnD,WAAY,EACZ,SAAU,UACV,eACA,aACA,EACA,cAAe,qBACf,EACA,aAAc,CAAC,kBAAkB,EAAE,EAAS,OAAO,CAAC,GAAG,wBAAwB,CAAC,CAChF,MAAO,CACL,qCACA,6CACA,yCACA,2CACD,CAED,aAAc,CACZ,MAAsB,KAAf,EACP,UAAW,QACX,OAAQ,0CACR,WAAY,CACV,CAAC,oBAAoB,EAAE,EAAS,OAAO,CAAC,GAAG,iBAAiB,CAAC,CAC7D,CAAC,aAAa,EAAE,AAAC,CAAgB,IAAA,CAAG,CAAE,cAAc,GAAG,OAAO,CAAC,CAC/D,CAAC,YAAY,EAAE,EAAY,OAAO,CAAC,GAAG,QAAQ,CAAC,CAC/C,+BACD,CACD,QAAS,mBACX,EAEA,YAAa,CACX,SAAU,CACR,MAAkB,KAAX,EACP,UAAW,OACX,OAAQ,4CACR,kBAAmB,CACjB,iCACA,2BACA,2BACD,AACH,EACA,YAAa,CACX,CACE,MAAO,CAAO,CAAC,EAAE,CACjB,WAAY,GACZ,OAAQ,uBACR,UAAW,sCACX,UAAW,OACb,EACA,CACE,MAAO,CAAO,CAAC,EAAE,CACjB,WAAY,GACZ,OAAQ,yBACR,UAAW,gCACX,UAAW,OACb,EACA,CACE,MAAO,CAAO,CAAC,EAAE,CACjB,WAAY,GACZ,OAAQ,qBACR,UAAW,8BACX,UAAW,OACb,EACD,AACH,EAEA,eAAgB,CACd,eAAgB,EAChB,mBArFgB,CAqFI,GACpB,CAtFqB,aAsFN,EACf,kBAAmB,AAvFmC,EAwFtD,cAAe,GACf,mBAAoB,CACtB,EAEA,cAAe,CACb,kBAAmB,CACjB,+CACA,6CACA,oDACA,mCACD,CACD,iBAAkB,CAChB,0CACA,mCACA,wCACA,yCACD,CACD,iBAAkB,CAChB,qCACA,yCACA,uCACA,2BACD,CACD,iBAAkB,CAChB,gDACA,8CACA,gCACA,oCACD,AACH,CACF,CACF,CAGA,OAAO,yBACL,CAAc,CACd,CAA0B,CAC1B,CAAgB,CAChB,EAAsB,GAAM,CACN,CACtB,GAAI,EAAQ,MAAM,CAAG,GAAI,OAAO,KAEhC,IAAM,EAAS,EAAQ,GAAG,CAAC,GAAK,EAAE,KAAK,EACjC,EAAU,EAAQ,GAAG,CAAC,GAAK,EAAE,MAAM,EACnC,EAAe,EAAM,KAAK,CAG1B,EAAQ,EAAA,mBAAmB,CAAC,GAAG,CAAC,EAAQ,KAAK,GAAG,CAAC,GAAI,EAAO,MAAM,CAAG,IACrE,EAAO,EAAA,mBAAmB,CAAC,GAAG,CAAC,EAAQ,KAAK,GAAG,CAAC,EAAG,EAAO,MAAM,CAAG,IACnE,EAAM,IAAI,CAAC,YAAY,CAAC,EAAS,KAAK,GAAG,CAAC,GAAI,EAAQ,MAAM,CAAG,IAE/D,EAAe,CAAK,CAAC,EAAM,MAAM,CAAG,EAAE,CACtC,EAAc,CAAI,CAAC,EAAK,MAAM,CAAG,EAAE,CACnC,EAAa,CAAG,CAAC,EAAI,MAAM,CAAG,EAAE,CAGtC,GAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAO,EAAM,MAAM,CAAE,EAAc,IAK5D,GAAgB,EAJlB,MAD6E,CACtE,KAIyB,AAIlC,IAAM,EADmB,AACG,CAJa,IAGX,GAAG,CAAC,EAAe,GACF,EAAgB,IAG/D,GAAI,EAAqB,GAIrB,CADqB,AACpB,IADwB,CAAC,aACP,EADsB,CAAC,EAAQ,EAAM,AAC9B,GAJA,AAGiC,OAH1B,KAOrC,EAJ6E,EAIvE,EAAY,EAAA,mBAAmB,CAAC,GAAG,CAAC,EAAS,IAC7C,EAAmB,CAAS,CAAC,EAAU,MAAM,CAAG,EAAE,CAClD,EAAkB,EAAM,MAAM,CAAG,EAEvC,GAAI,EAAkB,IAAK,OAAO,KAAK,AAGvC,IAAM,EAAmB,IAAI,CAAC,kBAHsC,OAGb,CAAC,EAAS,EAAc,GAC/E,GAAI,EAAmB,IAAK,OAAO,KAAK,AAKxC,IAAM,EAA4B,IAAf,EACb,EAAe,KAAK,CADsB,GAAG,CAAlB,AACF,CAAC,GAFX,EAAe,CAAA,EAAY,CAK1C,AAToE,EAS1D,CACC,CAJ4B,IAI3C,EACe,IAAf,EACe,KARqD,AAQpE,AAAqB,EACtB,CAMD,MAAO,CACL,SAAU,GARiC,kBAS3C,WANiB,IAAI,CAAC,2BAA2B,CACjD,EAAoB,EAAiB,EAAkB,EAAM,aAAa,EAM1E,WAAY,EACZ,SAAU,UACV,eACA,EACA,aACA,cAAe,gBACf,SAAU,EACV,aAAc,CAAC,yBAAyB,EAAE,EAAY,OAAO,CAAC,GAAG,CAAC,CAAC,CACnE,MAAO,CACL,sCACA,4CACA,sCACA,kCACD,CAED,aAAc,CACZ,MAAqB,MAAd,EACP,UAAW,QACX,OAAQ,+CACR,WAAY,CACV,CAAC,iBAAiB,EAAE,EAAY,OAAO,CAAC,GAAG,oBAAoB,CAAC,CAChE,CAAC,MAAM,EAAE,EAAa,OAAO,CAAC,GAAG,aAAa,CAAC,CAC/C,CAAC,uBAAuB,EAAE,CAAgB,IAAf,EAAM,MAAM,AAAG,CAAG,CAAE,cAAc,GAAA,CAAI,CACjE,+BACD,CACD,QAAS,uBACX,EAEA,YAAa,CACX,SAAU,CACR,MAAqB,KAAd,EACP,UAAW,OACX,OAAQ,iDACR,kBAAmB,CACjB,0BACA,6BACA,yBACD,AACH,EACA,YAAa,CACX,CACE,MAAO,CAAO,CAAC,EAAE,CACjB,WAAY,GACZ,OAAQ,6BACR,UAAW,2CACX,UAAW,OACb,EACA,CACE,MAAO,CAAO,CAAC,EAAE,CACjB,WAAY,GACZ,OAAQ,8BACR,UAAW,sCACX,UAAW,OACb,EACA,CACE,MAAO,CAAO,CAAC,EAAE,CACjB,WAAY,GACZ,OAAQ,uBACR,UAAW,kCACX,UAAW,OACb,EACD,AACH,EAEA,eAAgB,CACd,eAAgB,EAChB,mBArFgB,CAqFI,CACpB,cAAe,EACf,kBAAmB,EACnB,cAAe,GACf,mBAAoB,CACtB,EAEA,cAAe,CACb,kBAAmB,CACjB,yCACA,0CACA,mCACA,wCACD,CACD,iBAAkB,CAChB,2CACA,uCACA,sCACA,uCACD,CACD,iBAAkB,CAChB,sCACA,+BACA,iCACA,2BACD,CACD,iBAAkB,CAChB,wCACA,iCACA,oCACA,iCACD,AACH,CACF,CACF,CAGA,OAAe,mBACb,CAAgB,CAChB,CAAc,CACd,CAAa,CACb,CAAa,CACJ,CACT,OACE,GAAS,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EACvC,GAAU,IAAI,CAAC,gBAAgB,CAAC,SAAS,EACzC,CAAC,EAAM,SAAS,GAAI,CAAC,EAAK,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAC5D,EAAQ,CAEZ,CAEA,IAJkB,GAIH,aAAa,CAA0B,CAAE,CAAc,CAAY,CAChF,IAAM,EAAuB,EAAE,CAE/B,IAAK,IAAI,EAAI,EAAG,EAAI,EAAQ,MAAM,CAAE,IAAK,CACvC,AARwE,IAQlE,EAAO,CAAO,CAAC,EAAE,CAAC,IAAI,CACtB,EAAM,CAAO,CAAC,EAAE,CAAC,GAAG,CACpB,EAAY,CAAO,CAAC,EAAI,EAAE,CAAC,KAAK,CAEhC,EAAK,KAAK,GAAG,CACjB,EAAO,EACP,KAAK,GAAG,CAAC,EAAO,GAChB,KAAK,GAAG,CAAC,EAAM,IAGjB,EAAW,IAAI,CAAC,EAClB,CAEA,OAAO,EAAA,mBAAmB,CAAC,GAAG,CAAC,EAAY,EAC7C,CAEA,OAAe,cAAc,CAAuB,CAAU,CAE5D,MAAO,CAAC,EAAO,IAAI,CAAG,EAAO,GAAG,CAAG,EAAO,KAAA,AAAK,EAAI,CACrD,CAEA,OAAe,0BACb,CAA0B,CAC1B,CAAoB,CACpB,CAAW,CACH,CAKR,MADmB,AACZ,CAFS,KAAK,GAAG,IADJ,AACQ,EADA,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,GAAK,EAAE,IAAI,GAEzB,CAAA,EACT,CACtB,CAEA,OAAe,gBAAgB,CAAgB,CAAE,CAAc,CAAE,CAAgB,CAAW,CAE1F,IAAK,IAAI,EAAI,KAAK,GAAG,CAAC,EAAG,EAAO,MAAM,CAAG,GAAW,EAAI,EAAO,MAAM,CAAG,EAAG,IAAK,AAC9E,GAAI,CAAM,CAAC,EAAE,CAAG,CAAI,CAAC,EAAE,EAAI,CAAM,CAAC,EAAI,EAAE,CAAG,CAAI,CAAC,EAAI,EAAE,CACpD,CADsD,MAC/C,EAGX,GAHgB,IAGT,CACT,CAEA,OAAe,KANqB,wBAOlC,CAAqB,CACrB,CAAoB,CACpB,CAAc,CACd,CAAwB,CAChB,CACR,IAAI,EAAa,GAoBjB,OAjBI,EAAgB,EAAG,GAAc,GAC5B,EAAgB,EAAG,GAAc,GACjC,EAAgB,IAAG,IAAc,EAGtC,EAAe,GAAK,GAAc,GAC7B,EAAe,GAAK,GAAc,GAClC,EAAe,IAAK,KAAc,EAGvC,EAAS,IAAS,GAAc,GAC3B,EAAS,MAAS,IAAc,EAGrC,EAAmB,EAAG,GAAc,GAC/B,EAAmB,MAAK,IAAc,EAExC,KAAK,GAAG,CAAC,GAAI,KAAK,GAAG,CAAC,GAAI,GACnC,CAEA,OAAe,4BACb,CAAmB,CACnB,CAAuB,CACvB,CAAwB,CACxB,CAAqB,CACb,CACR,IAAI,EAAa,GAoBjB,OAjBI,EAAc,EAAG,GAAc,GAC1B,EAAc,EAAG,GAAc,GAC/B,EAAc,GAAG,KAAc,EAGpC,EAAkB,EAAG,GAAc,GAC9B,EAAkB,IAAK,GAAc,GACrC,EAAkB,MAAK,IAAc,EAG1C,EAAmB,EAAG,GAAc,GAC/B,EAAmB,EAAG,GAAc,GACpC,EAAmB,MAAK,IAAc,EAG3C,EAAgB,IAAG,IAAc,EAE9B,KAAK,GAAG,CAAC,GAAI,KAAK,GAAG,CAAC,GAAI,GACnC,CACF,0LEjjBA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,CAAA,CAAA,OAAA,IAAA,EAAA,EAAA,CAAA,CAAA,8BDfA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,OAEO,eAAe,EACpB,CAAoB,CACpB,QAAE,CAAM,CAA2C,EAEnD,GAAI,CACF,GAAM,CAAE,QAAM,CAAE,CAAG,MAAM,EACnB,cAAE,CAAY,CAAE,CAAG,IAAI,IAAI,EAAQ,GAAG,EAEtC,EAAc,SAAS,EAAa,GAAG,CAAC,gBAAkB,UAC1D,EAAW,EAAa,GAAG,CAAC,YAAY,AAE9C,GAAI,CAAC,EACH,MADW,CACJ,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,GAJ6E,GAItE,8BAA+B,EACxC,CAAE,OAAQ,GAAI,GAKlB,IAAM,EAAS,IAAI,EAAA,MAAM,CAAC,QAAQ,GAAG,CAAC,WAAW,EAC3C,EAAa,IAAI,EAAA,UAAU,CAAC,QAAQ,GAAG,CAAC,eAAe,EAEvD,CAAC,EAAO,EAAe,CAAG,MAAM,QAAQ,GAAG,CAAC,CAChD,EAAO,aAAa,CAAC,EAAO,WAAW,IACvC,EAAkB,EAAY,EAAO,WAAW,IACjD,EAED,GAAI,CAAC,GAAkB,EAAe,MAAM,CAAG,GAC7C,CADiD,MAC1C,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,MAAO,4EAA6E,EACtF,CAAE,OAAQ,GAAI,GAKlB,IAAM,EAAc,CAClB,OAAQ,EAAO,WAAW,SAC1B,EACA,cACA,SAAU,IAAI,OAAO,WAAW,EAClC,EA+BA,OA7BI,AAAC,GAAyB,cAAb,GAAyC,QAAQ,CAArB,IAC3C,EAAO,cAAc,CAAG,EAAA,sBAAsB,CAAC,wBAAwB,CACrE,EAAO,WAAW,GAAI,EAAgB,EAAO,EAAA,EAI7C,AAAC,GAAyB,aAAb,GAAwC,QAAQ,CAArB,IAC1C,EAAO,aAAa,CAAG,EAAA,sBAAsB,CAAC,wBAAwB,CACpE,EAAO,WAAW,GAAI,EAAgB,EAAO,EAAA,EAK7C,EAAO,cAAc,EAAI,EAAO,aAAa,EAAE,AACjD,EAAO,YAAY,CAAG,EAAO,cAAc,CAAC,UAAU,CAAG,EAAO,aAAa,CAAC,UAAU,CACpF,qBACA,qBACJ,EAAO,YAAY,CAAG,KAAK,GAAG,CAAC,EAAO,cAAc,CAAC,UAAU,CAAE,EAAO,aAAa,CAAC,UAAU,EAAI,GAC3F,EAAO,cAAc,EAAE,AAChC,EAAO,YAAY,CAAG,qBACtB,EAAO,YAAY,CAAG,EAAO,cAAc,CAAC,UAAU,EAC7C,EAAO,aAAa,EAAE,AAC/B,EAAO,YAAY,CAAG,qBACtB,EAAO,YAAY,CAAG,EAAO,aAAa,CAAC,UAAU,GAErD,EAAO,YAAY,CAAG,EACtB,EAAO,OAAO,CAAG,qEAGZ,EAAA,YAAY,CAAC,IAAI,CAAC,EAC3B,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,kCAAmC,GAC1C,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,MAAO,qCAAsC,EAC/C,CAAE,OAAQ,GAAI,EAElB,CACF,CAEA,eAAe,EAAkB,CAAsB,CAAE,CAAc,EACrE,IAAM,EAAK,CAAA,EAAA,EAAA,MAAA,AAAM,EAAC,IAAI,KAAQ,cACxB,EAAO,CAAA,EAAA,EAAA,MAAA,AAAM,EAAC,CAAA,EAAA,EAAA,OAAA,AAAO,EAAC,IAAI,KAAQ,KAAM,cAE9C,GAAI,CACF,OAAO,MAAM,EAAW,iBAAiB,CAAC,EAAQ,MAAO,EAAG,EAAM,EACpE,CAAE,MAAO,EAAO,CAEd,MADA,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,EAAA,CAAQ,EAC7C,CACR,CACF,CCjFA,IAAA,EAAA,EAAA,CAAA,CAAA,MAIA,IAAM,EAAc,IAAI,EAAA,mBAAmB,CAAC,CACxC,WAAY,CACR,KAAM,EAAA,SAAS,CAAC,SAAS,CACzB,KAAM,wCACN,SAAU,kCACV,SAAU,QACV,WAAY,EAChB,EACA,QAAS,CAAA,OACT,IADiD,eACc,CAA3C,EACpB,iBAAkB,4EAClB,iBAZqB,GAarB,SAAA,CACJ,GAIM,kBAAE,CAAgB,sBAAE,CAAoB,aAAE,CAAW,CAAE,CAAG,EAChE,SAAS,IACL,MAAO,CAAA,EAAA,EAAA,UAAA,AAAW,EAAC,kBACf,uBACA,CACJ,EACJ,CAEO,eAAe,EAAQ,CAAG,CAAE,CAAG,CAAE,CAAG,EACvC,IAAI,EACJ,IAAI,EAAU,wCAKV,EAAU,EAAQ,OAAO,CAAC,WAAY,KAAO,IAMjD,IAAM,EAAgB,MAAM,EAAY,OAAO,CAAC,EAAK,EAAK,SACtD,EACA,mBAHE,CAAA,CAIN,GACA,GAAI,CAAC,EAID,OAHA,EAAI,IADY,MACF,CAAG,IACjB,EAAI,GAAG,CAAC,eACS,MAAjB,CAAwB,CAApB,IAAyB,KAAhB,EAAoB,EAAI,SAAS,CAAC,IAAI,CAAC,EAAK,QAAQ,OAAO,IACjE,KAEX,GAAM,SAAE,CAAO,QAAE,CAAM,CAAE,YAAU,aAAE,CAAW,mBAAE,CAAiB,qBAAE,CAAmB,sBAAE,CAAoB,yBAAE,CAAuB,kBAAE,CAAgB,CAAE,CAAG,EACxJ,EAAoB,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,GACvC,GAAQ,EAAQ,EAAkB,aAAa,CAAC,EAAkB,EAAI,EAAkB,MAAM,CAAC,EAAA,AAAiB,EACpH,GAAI,GAAS,CAAC,EAAa,CACvB,IAAM,GAAgB,CAAQ,EAAkB,MAAM,CAAC,EAAiB,CAClE,EAAgB,EAAkB,aAAa,CAAC,EAAkB,CACxE,GAAI,IAC+B,IAA3B,EAAc,KADH,GACW,EAAc,CAAC,EACrC,MAAM,IAAI,EAAA,CAD0C,cAC3B,AAGrC,CACA,IAAI,EAAW,MACX,GAAU,EAAY,IAAb,CAAkB,EAAK,EAAD,EAG/B,EAAW,AAAa,OAHqB,KAC7C,EAAW,CAAA,EAEwB,IAAM,CAAA,EAE7C,IAAM,GACgB,IAAtB,EAAY,EAAkB,GAAb,EAEjB,CAAC,EAKK,EAAe,GAAS,CAAC,EACzB,EAAS,EAAI,MAAM,EAAI,MACvB,EAAS,CAAA,EAAA,EAAA,SAAA,AAAS,IAClB,EAAa,EAAO,WAVyE,OAUvD,GACtC,EAAU,QACZ,oBACA,EACA,WAAY,CACR,aAAc,CACV,iBAAiB,CAAQ,EAAW,YAAY,CAAC,eAAe,CAChE,gBAAgB,CAAQ,EAAW,YAAY,CAAC,cAAc,AAClE,0BACA,EACA,iBAAkB,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,oBACtC,kBAA2E,AAAxD,OAAC,EAA2B,EAAW,YAAA,AAAY,EAAY,KAAK,EAAI,EAAyB,SAAS,cAC7H,EACA,UAAW,EAAI,SAAS,CACxB,QAAS,AAAC,IACN,EAAI,EAAE,CAAC,QAAS,EACpB,EACA,sBAAkB,EAClB,8BAA+B,CAAC,EAAO,EAAU,IAAe,EAAY,cAAc,CAAC,EAAK,EAAO,EAAc,EACzH,EACA,cAAe,SACX,CACJ,CACJ,EACM,EAAc,IAAI,EAAA,eAAe,CAAC,GAClC,EAAc,IAAI,EAAA,gBAAgB,CAAC,GACnC,EAAU,EAAA,kBAAkB,CAAC,mBAAmB,CAAC,EAAa,CAAA,EAAA,EAAA,sBAAA,AAAsB,EAAC,IAC3F,GAAI,CACA,IAAM,EAAoB,MAAO,GACtB,EAAY,MAAM,CAAC,EAAS,GAAS,OAAO,CAAC,KAChD,GAAI,CAAC,EAAM,OACX,EAAK,aAAa,CAAC,CACf,mBAAoB,EAAI,UAAU,CAClC,YAAY,CAChB,GACA,IAAM,EAAqB,EAAO,qBAAqB,GAEvD,GAAI,CAAC,EACD,OAEJ,GAAI,EAAmB,GAAG,CAAC,EAHF,kBAGwB,EAAA,cAAc,CAAC,aAAa,CAAE,YAC3E,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,EAAmB,GAAG,CAAC,kBAAkB,qEAAqE,CAAC,EAG9J,IAAM,EAAQ,EAAmB,GAAG,CAAC,cACrC,GAAI,EAAO,CACP,IAAM,EAAO,CAAA,EAAG,EAAO,CAAC,EAAE,EAAA,CAAO,CACjC,EAAK,aAAa,CAAC,CACf,aAAc,EACd,aAAc,EACd,iBAAkB,CACtB,GACA,EAAK,UAAU,CAAC,EACpB,MACI,CADG,CACE,UAAU,CAAC,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAE9C,GAEE,EAAiB,MAAO,QACtB,EA0FI,EAzFR,IAAM,EAAoB,MAAO,oBAAE,CAAkB,CAAE,IACnD,GAAI,CACA,GAAI,CAAC,CAAA,EAAA,EAAA,cAAc,AAAd,EAAe,EAAK,gBAAkB,GAAwB,GAA2B,CAAC,EAK3F,OAJA,EAAI,SAD2G,CACjG,CAAG,IAEjB,EAAI,SAAS,CAAC,iBAAkB,eAChC,EAAI,GAAG,CAAC,gCACD,KAEX,IAAM,EAAW,MAAM,EAAkB,GACzC,EAAI,YAAY,CAAG,EAAQ,UAAU,CAAC,YAAY,CAClD,IAAI,EAAmB,EAAQ,UAAU,CAAC,gBAAgB,CAGtD,GACI,EAAI,SAAS,EAAE,CACf,CAFc,CAEV,SAAS,CAAC,GACd,OAAmB,GAG3B,IAAM,EAAY,EAAQ,UAAU,CAAC,aAAa,CAGlD,IAAI,EA6BA,OADA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,EAAU,EAAQ,UAAU,CAAC,gBAAgB,EACnF,IA7BA,EACP,IAAM,EAAO,MAAM,EAAS,IAAI,GAE1B,EAAU,CAAA,EAAA,EAAA,yBAAA,AAAyB,EAAC,EAAS,OAAO,EACtD,IACA,CAAO,CAAC,EAAA,GADG,mBACmB,CAAC,CAAG,CAAA,EAElC,CAAC,CAAO,CAAC,eAAe,EAAI,EAAK,IAAI,EAAE,CACvC,CAAO,CAAC,eAAe,CAAG,EAAK,IAAA,AAAI,EAEvC,IAAM,EAAa,KAAkD,IAA3C,EAAQ,UAAU,CAAC,mBAAmB,IAAoB,EAAQ,UAAU,CAAC,mBAAmB,EAAI,EAAA,cAAA,AAAc,GAAG,AAAQ,EAAQ,UAAU,CAAC,mBAAmB,CACvL,EAAS,AAA8C,SAAvC,EAAQ,UAAU,CAAC,eAAe,EAAoB,EAAQ,UAAU,CAAC,eAAe,EAAI,EAAA,cAAc,CAAG,OAAY,EAAQ,UAAU,CAAC,eAAe,CAcjL,MAZmB,CAYZ,AAXH,MAAO,CACH,KAAM,EAAA,eAAe,CAAC,SAAS,CAC/B,OAAQ,EAAS,MAAM,CACvB,KAAM,OAAO,IAAI,CAAC,MAAM,EAAK,WAAW,YACxC,CACJ,EACA,aAAc,YACV,SACA,CACJ,CACJ,CAEJ,CAKJ,CAAE,KALS,CAKF,EAAK,CAcV,MAX0B,MAAtB,EAA6B,KAAK,EAAI,EAAmB,OAAA,AAAO,EAAE,CAClE,MAAM,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,CAClC,oCACA,CACJ,EACJ,EAAG,GAED,CACV,CACJ,EACM,EAAa,MAAM,EAAY,cAAc,CAAC,KAChD,EACA,sBACA,EACA,UAAW,EAAA,SAAS,CAAC,SAAS,CAC9B,YAAY,oBACZ,EACA,kBAAmB,wBACnB,0BACA,oBACA,EACA,UAAW,EAAI,SAAS,AAC5B,GAEA,GAAI,CAAC,EACD,KADQ,EACD,KAEX,GAAI,CAAe,MAAd,CAAqB,EAAS,AAA0C,GAA9C,IAAK,EAAoB,EAAW,KAAA,AAAK,EAAY,KAAK,EAAI,EAAkB,IAAI,IAAM,EAAA,eAAe,CAAC,SAAS,CAE9I,CAFgJ,KAE1I,OAAO,cAAc,CAAC,AAAI,MAAM,CAAC,kDAAkD,EAAgB,MAAd,CAAqB,EAAS,AAA2C,GAA/C,IAAK,EAAqB,EAAW,KAAA,AAAK,EAAY,KAAK,EAAI,EAAmB,IAAI,CAAA,CAAE,EAAG,oBAAqB,CACjO,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,EAEA,CAAC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAgB,AACrC,EAAI,SAAS,CAAC,iBAAkB,EAAuB,cAAgB,EAAW,MAAM,CAAG,OAAS,EAAW,OAAO,CAAG,QAAU,OAGnI,GACA,EAAI,QADS,CACA,CAAC,gBAAiB,2DAEnC,IAAM,EAAU,CAAA,EAAA,EAAA,2BAAA,AAA2B,EAAC,EAAW,KAAK,CAAC,OAAO,EAapE,MAZI,AAAE,CAAD,AAAC,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GACxC,EAD6C,AACrC,GADwC,GAClC,CAAC,EAAA,sBAAsB,GAIrC,EAAW,YAAY,EAAK,EAAD,AAAK,SAAS,CAAC,kBAAqB,EAAD,AAAS,GAAG,CAAC,kBAAkB,AAC7F,EAAQ,GAAG,CAAC,gBAAiB,CAAA,EAAA,EAAA,qBAAA,AAAqB,EAAC,EAAW,YAAY,GAE9E,MAAM,CAAA,EAAA,EAAA,YAAY,AAAZ,EAAa,EAAa,EAAa,IAAI,SAAS,EAAW,KAAK,CAAC,IAAI,CAAE,SAC7E,EACA,OAAQ,EAAW,KAAK,CAAC,MAAM,EAAI,GACvC,IACO,IACX,EAGI,EACA,MAAM,EAAe,EADT,CAGZ,MAAM,EAAO,qBAAqB,CAAC,EAAI,OAAO,CAAE,IAAI,EAAO,KAAK,CAAC,EAAA,cAAc,CAAC,aAAa,CAAE,CACvF,SAAU,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAChC,KAAM,EAAA,QAAQ,CAAC,MAAM,CACrB,WAAY,CACR,cAAe,EACf,cAAe,EAAI,GAAG,AAC1B,CACJ,EAAG,GAEf,CAAE,MAAO,EAAK,CAcV,GAbI,AAAE,CAAD,YAAgB,EAAA,eAAe,EAChC,CADmC,KAC7B,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,cAClC,uBACA,CACJ,EACJ,GAIA,EAAO,MAAM,EAKjB,OAHA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,KAAM,CAC5D,OAAQ,GACZ,IACO,IACX,CACJ,EAEA,qCAAqC", "ignoreList": [5]}