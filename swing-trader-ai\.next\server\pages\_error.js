var R=require("../chunks/ssr/[turbopack]_runtime.js")("server/pages/_error.js")
R.c("server/chunks/ssr/6bf44_2dcd094b._.js")
R.c("server/chunks/ssr/[root-of-the-server]__e6a4d965._.js")
R.c("server/chunks/ssr/6bf44_next_9070ed66._.js")
R.c("server/chunks/ssr/6bf44_da0899d0._.js")
R.c("server/chunks/ssr/[externals]_next_dist_shared_lib_no-fallback-error_external_59b92b38.js")
R.m("[project]/swing-trader-ai/node_modules/next/dist/esm/build/templates/pages.js { INNER_PAGE => \"[project]/swing-trader-ai/node_modules/next/error.js [ssr] (ecmascript)\", INNER_DOCUMENT => \"[project]/swing-trader-ai/node_modules/next/document.js [ssr] (ecmascript)\", INNER_APP => \"[project]/swing-trader-ai/node_modules/next/app.js [ssr] (ecmascript)\" } [ssr] (ecmascript)")
module.exports=R.m("[project]/swing-trader-ai/node_modules/next/dist/esm/build/templates/pages.js { INNER_PAGE => \"[project]/swing-trader-ai/node_modules/next/error.js [ssr] (ecmascript)\", INNER_DOCUMENT => \"[project]/swing-trader-ai/node_modules/next/document.js [ssr] (ecmascript)\", INNER_APP => \"[project]/swing-trader-ai/node_modules/next/app.js [ssr] (ecmascript)\" } [ssr] (ecmascript)").exports
