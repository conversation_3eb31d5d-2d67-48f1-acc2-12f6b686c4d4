import { 
  Catalyst, 
  CatalystType, 
  CatalystTier, 
  CatalystImpact, 
  CatalystImpactMeasurement 
} from '@/types/trading'
import { FMPAPI } from './fmp'
import { PolygonAPI } from './polygon'

export class CatalystDetectionEngine {
  private fmpAPI: FMPAPI
  private polygonAPI: PolygonAPI
  private catalystCache: Map<string, Catalyst[]> = new Map()
  private impactMeasurements: Map<string, CatalystImpactMeasurement> = new Map()

  constructor(fmpApiKey?: string, polygonApiKey?: string) {
    this.fmpAPI = new FMPAPI(fmpApiKey)
    this.polygonAPI = new PolygonAPI(polygonApiKey)
  }

  /**
   * Detect catalysts for a specific symbol
   */
  async detectCatalysts(symbol: string): Promise<Catalyst[]> {
    const catalysts: Catalyst[] = []

    try {
      // Check cache first (5-minute cache)
      const cacheKey = `${symbol}_${Math.floor(Date.now() / (5 * 60 * 1000))}`
      if (this.catalystCache.has(cacheKey)) {
        return this.catalystCache.get(cacheKey)!
      }

      // Detect different types of catalysts in parallel
      const [
        earningsCatalysts,
        newsCatalysts,
        analystCatalysts,
        insiderCatalysts,
        secFilingCatalysts
      ] = await Promise.all([
        this.detectEarningsCatalysts(symbol),
        this.detectNewsCatalysts(symbol),
        this.detectAnalystCatalysts(symbol),
        this.detectInsiderCatalysts(symbol),
        this.detectSECFilingCatalysts(symbol)
      ])

      catalysts.push(
        ...earningsCatalysts,
        ...newsCatalysts,
        ...analystCatalysts,
        ...insiderCatalysts,
        ...secFilingCatalysts
      )

      // Sort by quality score and freshness
      catalysts.sort((a, b) => {
        const freshnessWeight = this.getFreshnessWeight(a.freshness) - this.getFreshnessWeight(b.freshness)
        if (freshnessWeight !== 0) return freshnessWeight
        return b.qualityScore - a.qualityScore
      })

      // Cache results
      this.catalystCache.set(cacheKey, catalysts)

      return catalysts
    } catch (error) {
      console.error(`Error detecting catalysts for ${symbol}:`, error)
      return []
    }
  }

  /**
   * Detect earnings-related catalysts
   */
  private async detectEarningsCatalysts(symbol: string): Promise<Catalyst[]> {
    const catalysts: Catalyst[] = []

    try {
      // Get recent earnings data from FMP
      const earningsData = await this.fmpAPI.getEarningsCalendar(symbol, 30) // Last 30 days
      
      for (const earnings of earningsData) {
        if (this.isEarningsBeat(earnings)) {
          const catalyst: Catalyst = {
            id: `earnings_${symbol}_${earnings.date}`,
            symbol,
            type: 'earnings_beat_guidance',
            tier: 'tier_1', // Highest priority
            impact: 'bullish',
            title: `${symbol} Beats Earnings Expectations`,
            description: `Q${earnings.quarter} earnings beat: EPS ${earnings.actualEPS} vs ${earnings.estimatedEPS} expected`,
            source: 'FMP Earnings Data',
            announcementTime: earnings.date,
            discoveredTime: new Date().toISOString(),
            qualityScore: this.calculateEarningsQualityScore(earnings),
            freshness: this.calculateFreshness(earnings.date),
            estimatedDuration: 'short_term',
            verified: true,
            tags: ['earnings', 'beat', 'guidance'],
            metadata: {
              actualEPS: earnings.actualEPS,
              estimatedEPS: earnings.estimatedEPS,
              beatPercent: ((earnings.actualEPS - earnings.estimatedEPS) / earnings.estimatedEPS) * 100,
              guidanceRaised: earnings.guidanceRaised || false
            }
          }
          catalysts.push(catalyst)
        }
      }
    } catch (error) {
      console.error(`Error detecting earnings catalysts for ${symbol}:`, error)
    }

    return catalysts
  }

  /**
   * Detect news-related catalysts
   */
  private async detectNewsCatalysts(symbol: string): Promise<Catalyst[]> {
    const catalysts: Catalyst[] = []

    try {
      // Get recent news from FMP
      const newsData = await this.fmpAPI.getStockNews(symbol, 50) // Last 50 articles
      
      for (const news of newsData) {
        const catalystType = this.classifyNewsAsCatalyst(news)
        if (catalystType) {
          const catalyst: Catalyst = {
            id: `news_${symbol}_${news.publishedDate}_${news.title.slice(0, 20).replace(/\s+/g, '_')}`,
            symbol,
            type: catalystType.type,
            tier: catalystType.tier,
            impact: catalystType.impact,
            title: news.title,
            description: news.text?.slice(0, 200) + '...' || news.title,
            source: news.site,
            sourceUrl: news.url,
            announcementTime: news.publishedDate,
            discoveredTime: new Date().toISOString(),
            qualityScore: this.calculateNewsQualityScore(news, catalystType.type),
            freshness: this.calculateFreshness(news.publishedDate),
            estimatedDuration: this.estimateNewsDuration(catalystType.type),
            verified: this.isReliableNewsSource(news.site),
            tags: this.extractNewsKeywords(news.title + ' ' + (news.text || '')),
            metadata: {
              site: news.site,
              sentiment: news.sentiment || 'neutral'
            }
          }
          catalysts.push(catalyst)
        }
      }
    } catch (error) {
      console.error(`Error detecting news catalysts for ${symbol}:`, error)
    }

    return catalysts
  }

  /**
   * Detect analyst upgrade/downgrade catalysts
   */
  private async detectAnalystCatalysts(symbol: string): Promise<Catalyst[]> {
    const catalysts: Catalyst[] = []

    try {
      // Get analyst recommendations from FMP
      const analystData = await this.fmpAPI.getAnalystRecommendations(symbol, 30)
      
      for (const recommendation of analystData) {
        if (this.isSignificantAnalystChange(recommendation)) {
          const isUpgrade = recommendation.newGrade > recommendation.previousGrade
          const catalyst: Catalyst = {
            id: `analyst_${symbol}_${recommendation.date}_${recommendation.analystCompany}`,
            symbol,
            type: isUpgrade ? 'analyst_upgrade' : 'analyst_downgrade',
            tier: 'tier_2',
            impact: isUpgrade ? 'bullish' : 'bearish',
            title: `${recommendation.analystCompany} ${isUpgrade ? 'Upgrades' : 'Downgrades'} ${symbol}`,
            description: `${recommendation.analystName} at ${recommendation.analystCompany} ${isUpgrade ? 'upgraded' : 'downgraded'} to ${recommendation.newGrade}`,
            source: 'FMP Analyst Data',
            announcementTime: recommendation.date,
            discoveredTime: new Date().toISOString(),
            qualityScore: this.calculateAnalystQualityScore(recommendation),
            freshness: this.calculateFreshness(recommendation.date),
            estimatedDuration: 'medium_term',
            verified: true,
            tags: ['analyst', isUpgrade ? 'upgrade' : 'downgrade', recommendation.analystCompany.toLowerCase()],
            metadata: {
              analystCompany: recommendation.analystCompany,
              analystName: recommendation.analystName,
              previousGrade: recommendation.previousGrade,
              newGrade: recommendation.newGrade,
              priceTarget: recommendation.priceTarget
            }
          }
          catalysts.push(catalyst)
        }
      }
    } catch (error) {
      console.error(`Error detecting analyst catalysts for ${symbol}:`, error)
    }

    return catalysts
  }

  /**
   * Detect insider trading catalysts
   */
  private async detectInsiderCatalysts(symbol: string): Promise<Catalyst[]> {
    const catalysts: Catalyst[] = []

    try {
      // Get insider trading data from FMP
      const insiderData = await this.fmpAPI.getInsiderTrading(symbol, 30)
      
      for (const trade of insiderData) {
        if (this.isSignificantInsiderTrade(trade)) {
          const isBuying = trade.transactionType.toLowerCase().includes('buy') || 
                          trade.transactionType.toLowerCase().includes('purchase')
          
          const catalyst: Catalyst = {
            id: `insider_${symbol}_${trade.filingDate}_${trade.reportingName}`,
            symbol,
            type: isBuying ? 'insider_buying' : 'insider_selling',
            tier: 'tier_2',
            impact: isBuying ? 'bullish' : 'bearish',
            title: `${trade.reportingName} ${isBuying ? 'Buys' : 'Sells'} ${symbol} Shares`,
            description: `${trade.reportingName} (${trade.typeOfOwner}) ${trade.transactionType} ${trade.securitiesTransacted} shares at $${trade.price}`,
            source: 'SEC Insider Trading Filings',
            announcementTime: trade.filingDate,
            discoveredTime: new Date().toISOString(),
            qualityScore: this.calculateInsiderQualityScore(trade),
            freshness: this.calculateFreshness(trade.filingDate),
            estimatedDuration: 'medium_term',
            verified: true,
            tags: ['insider', isBuying ? 'buying' : 'selling', trade.typeOfOwner.toLowerCase()],
            metadata: {
              reportingName: trade.reportingName,
              typeOfOwner: trade.typeOfOwner,
              transactionType: trade.transactionType,
              securitiesTransacted: trade.securitiesTransacted,
              price: trade.price,
              dollarValue: trade.securitiesTransacted * trade.price
            }
          }
          catalysts.push(catalyst)
        }
      }
    } catch (error) {
      console.error(`Error detecting insider catalysts for ${symbol}:`, error)
    }

    return catalysts
  }

  /**
   * Detect SEC filing catalysts
   */
  private async detectSECFilingCatalysts(symbol: string): Promise<Catalyst[]> {
    const catalysts: Catalyst[] = []

    try {
      // Get recent SEC filings from FMP
      const filings = await this.fmpAPI.getSECFilings(symbol, 30)
      
      for (const filing of filings) {
        if (this.isSignificantSECFiling(filing)) {
          const catalyst: Catalyst = {
            id: `sec_${symbol}_${filing.filedDate}_${filing.type}`,
            symbol,
            type: 'sec_filing',
            tier: this.getSECFilingTier(filing.type),
            impact: this.getSECFilingImpact(filing.type),
            title: `${symbol} Files ${filing.type}`,
            description: `${filing.type} filing: ${filing.description || 'SEC regulatory filing'}`,
            source: 'SEC EDGAR Database',
            sourceUrl: filing.link,
            announcementTime: filing.filedDate,
            discoveredTime: new Date().toISOString(),
            qualityScore: this.calculateSECFilingQualityScore(filing),
            freshness: this.calculateFreshness(filing.filedDate),
            estimatedDuration: this.estimateSECFilingDuration(filing.type),
            verified: true,
            tags: ['sec', 'filing', filing.type.toLowerCase()],
            metadata: {
              filingType: filing.type,
              cik: filing.cik,
              acceptedDate: filing.acceptedDate
            }
          }
          catalysts.push(catalyst)
        }
      }
    } catch (error) {
      console.error(`Error detecting SEC filing catalysts for ${symbol}:`, error)
    }

    return catalysts
  }

  // Helper methods for catalyst classification and scoring
  private getFreshnessWeight(freshness: string): number {
    switch (freshness) {
      case 'fresh': return 3
      case 'moderate': return 2
      case 'stale': return 1
      default: return 0
    }
  }

  private calculateFreshness(dateString: string): 'fresh' | 'moderate' | 'stale' {
    const date = new Date(dateString)
    const now = new Date()
    const hoursAgo = (now.getTime() - date.getTime()) / (1000 * 60 * 60)
    
    if (hoursAgo < 24) return 'fresh'
    if (hoursAgo < 72) return 'moderate'
    return 'stale'
  }

  private isEarningsBeat(earnings: any): boolean {
    return earnings.actualEPS > earnings.estimatedEPS && 
           (earnings.guidanceRaised || earnings.actualEPS > earnings.estimatedEPS * 1.05)
  }

  private calculateEarningsQualityScore(earnings: any): number {
    let score = 5 // Base score
    
    // Beat percentage
    const beatPercent = ((earnings.actualEPS - earnings.estimatedEPS) / earnings.estimatedEPS) * 100
    if (beatPercent > 20) score += 3
    else if (beatPercent > 10) score += 2
    else if (beatPercent > 5) score += 1
    
    // Guidance raised
    if (earnings.guidanceRaised) score += 2
    
    // Revenue beat
    if (earnings.actualRevenue > earnings.estimatedRevenue) score += 1
    
    return Math.min(10, score)
  }

  private classifyNewsAsCatalyst(news: any): { type: CatalystType, tier: CatalystTier, impact: CatalystImpact } | null {
    const title = news.title.toLowerCase()
    const text = (news.text || '').toLowerCase()
    const content = title + ' ' + text

    // FDA/Drug related
    if (content.includes('fda') && (content.includes('approval') || content.includes('approved'))) {
      return { type: 'fda_approval', tier: 'tier_1', impact: 'bullish' }
    }
    
    if (content.includes('trial') && (content.includes('positive') || content.includes('successful'))) {
      return { type: 'drug_trial_results', tier: 'tier_1', impact: 'bullish' }
    }

    // Contract/Partnership
    if (content.includes('contract') && (content.includes('win') || content.includes('awarded'))) {
      return { type: 'contract_win', tier: 'tier_1', impact: 'bullish' }
    }
    
    if (content.includes('partnership') || content.includes('collaboration')) {
      return { type: 'partnership', tier: 'tier_1', impact: 'bullish' }
    }

    // M&A
    if (content.includes('merger') || content.includes('acquisition') || content.includes('buyout')) {
      return { type: 'merger_acquisition', tier: 'tier_1', impact: 'bullish' }
    }

    // Stock split
    if (content.includes('stock split') || content.includes('share split')) {
      return { type: 'stock_split', tier: 'tier_2', impact: 'bullish' }
    }

    return null
  }

  private calculateNewsQualityScore(news: any, catalystType: CatalystType): number {
    let score = 5 // Base score
    
    // Source reliability
    if (this.isReliableNewsSource(news.site)) score += 2
    
    // Catalyst type importance
    if (['fda_approval', 'merger_acquisition', 'earnings_beat_guidance'].includes(catalystType)) {
      score += 2
    }
    
    // Sentiment
    if (news.sentiment === 'positive') score += 1
    else if (news.sentiment === 'negative') score -= 1
    
    return Math.max(1, Math.min(10, score))
  }

  private isReliableNewsSource(site: string): boolean {
    const reliableSources = [
      'reuters.com', 'bloomberg.com', 'wsj.com', 'cnbc.com', 
      'marketwatch.com', 'yahoo.com', 'sec.gov', 'fda.gov'
    ]
    return reliableSources.some(source => site.toLowerCase().includes(source))
  }

  private extractNewsKeywords(text: string): string[] {
    const keywords = []
    const content = text.toLowerCase()
    
    const keywordMap = {
      'earnings': ['earnings', 'eps', 'revenue', 'profit'],
      'fda': ['fda', 'approval', 'drug', 'trial'],
      'merger': ['merger', 'acquisition', 'buyout', 'takeover'],
      'partnership': ['partnership', 'collaboration', 'alliance'],
      'contract': ['contract', 'deal', 'agreement'],
      'upgrade': ['upgrade', 'raised', 'increased'],
      'downgrade': ['downgrade', 'lowered', 'reduced']
    }
    
    for (const [category, terms] of Object.entries(keywordMap)) {
      if (terms.some(term => content.includes(term))) {
        keywords.push(category)
      }
    }
    
    return keywords
  }

  private estimateNewsDuration(catalystType: CatalystType): 'intraday' | 'short_term' | 'medium_term' | 'long_term' {
    switch (catalystType) {
      case 'earnings_beat_guidance':
      case 'fda_approval':
      case 'merger_acquisition':
        return 'short_term'
      case 'analyst_upgrade':
      case 'analyst_downgrade':
      case 'partnership':
        return 'medium_term'
      case 'stock_split':
        return 'long_term'
      default:
        return 'short_term'
    }
  }

  private isSignificantAnalystChange(recommendation: any): boolean {
    // Check if it's a meaningful grade change
    const gradeChange = Math.abs(recommendation.newGrade - recommendation.previousGrade)
    return gradeChange >= 1 && recommendation.priceTarget > 0
  }

  private calculateAnalystQualityScore(recommendation: any): number {
    let score = 5 // Base score
    
    // Analyst firm reputation (simplified)
    const topFirms = ['goldman sachs', 'morgan stanley', 'jp morgan', 'bank of america']
    if (topFirms.some(firm => recommendation.analystCompany.toLowerCase().includes(firm))) {
      score += 2
    }
    
    // Grade change magnitude
    const gradeChange = Math.abs(recommendation.newGrade - recommendation.previousGrade)
    if (gradeChange >= 2) score += 2
    else if (gradeChange >= 1) score += 1
    
    // Price target change
    if (recommendation.priceTargetChange > 10) score += 1
    
    return Math.min(10, score)
  }

  private isSignificantInsiderTrade(trade: any): boolean {
    const dollarValue = trade.securitiesTransacted * trade.price
    return dollarValue >= 1000000 && // $1M+ transactions
           trade.typeOfOwner !== 'Other' // Exclude generic "Other" category
  }

  private calculateInsiderQualityScore(trade: any): number {
    let score = 5 // Base score
    
    const dollarValue = trade.securitiesTransacted * trade.price
    
    // Transaction size
    if (dollarValue >= ********) score += 3 // $10M+
    else if (dollarValue >= 5000000) score += 2 // $5M+
    else if (dollarValue >= 1000000) score += 1 // $1M+
    
    // Insider type
    if (trade.typeOfOwner.toLowerCase().includes('ceo') || 
        trade.typeOfOwner.toLowerCase().includes('cfo')) {
      score += 2
    } else if (trade.typeOfOwner.toLowerCase().includes('director')) {
      score += 1
    }
    
    return Math.min(10, score)
  }

  private isSignificantSECFiling(filing: any): boolean {
    const significantFilings = ['8-K', '10-K', '10-Q', '13D', '13G', 'S-1', 'S-4']
    return significantFilings.includes(filing.type)
  }

  private getSECFilingTier(filingType: string): CatalystTier {
    const tier1Filings = ['8-K', '13D', 'S-4'] // Material events, activist investors, M&A
    const tier2Filings = ['10-K', '10-Q', '13G'] // Regular reports, passive investors
    
    if (tier1Filings.includes(filingType)) return 'tier_1'
    if (tier2Filings.includes(filingType)) return 'tier_2'
    return 'tier_3'
  }

  private getSECFilingImpact(filingType: string): CatalystImpact {
    // Most SEC filings are neutral until analyzed
    return 'neutral'
  }

  private calculateSECFilingQualityScore(filing: any): number {
    let score = 5 // Base score
    
    // Filing type importance
    if (['8-K', '13D'].includes(filing.type)) score += 2
    else if (['10-K', '10-Q'].includes(filing.type)) score += 1
    
    return Math.min(10, score)
  }

  private estimateSECFilingDuration(filingType: string): 'intraday' | 'short_term' | 'medium_term' | 'long_term' {
    switch (filingType) {
      case '8-K': return 'short_term' // Material events
      case '13D': return 'medium_term' // Activist investors
      case 'S-4': return 'long_term' // M&A registration
      default: return 'medium_term'
    }
  }
}
