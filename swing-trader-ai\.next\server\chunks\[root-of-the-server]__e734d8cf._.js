module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},14747,(e,t,r)=>{t.exports=e.x("path",()=>require("path"))},78006,e=>{"use strict";e.s(["PolygonAPI",()=>n]);var t=e.i(55362);let r="https://api.polygon.io",a=process.env.POLYGON_API_KEY;class n{apiKey;constructor(e){if(this.apiKey=e||a||"",!this.apiKey)throw Error("Polygon API key is required")}async getStockQuote(e){try{let a=(await t.default.get(`${r}/v2/snapshot/locale/us/markets/stocks/tickers/${e}`,{params:{apikey:this.apiKey}})).data.results[0];if(!a)throw Error(`No data found for ${e}`);let n=a.value||a,s=n.day||{},o=n.prevDay||{};n.lastQuote;let i=(n.lastTrade||{}).p||s.c||o.c,c=o.c||s.o,l=i-c;return{symbol:e.toUpperCase(),name:n.name||e.toUpperCase(),price:i,change:l,changePercent:l/c*100,volume:s.v||0,marketCap:n.market_cap,pe:void 0,dividend:void 0}}catch(a){console.error("Error fetching stock quote from Polygon:",a);try{let a=(await t.default.get(`${r}/v2/aggs/ticker/${e}/prev`,{params:{adjusted:"true",apikey:this.apiKey}})).data.results[0];return{symbol:e.toUpperCase(),name:e.toUpperCase(),price:a.c,change:a.c-a.o,changePercent:(a.c-a.o)/a.o*100,volume:a.v,marketCap:void 0,pe:void 0,dividend:void 0}}catch(t){throw console.error("Polygon fallback also failed:",t),Error(`Failed to fetch quote for ${e}`)}}}async getHistoricalData(e,a="day",n=1,s,o){try{let i=await t.default.get(`${r}/v2/aggs/ticker/${e}/range/${n}/${a}/${s}/${o}`,{params:{adjusted:"true",sort:"asc",limit:5e4,apikey:this.apiKey}});if(!i.data.results||0===i.data.results.length)return console.warn(`No historical data found for ${e}`),[];return i.data.results.map(e=>({timestamp:e.t,open:e.o,high:e.h,low:e.l,close:e.c,volume:e.v}))}catch(t){throw console.error(`Error fetching historical data for ${e}:`,t),t.response&&(console.error(`Polygon API Error: ${t.response.status} - ${t.response.statusText}`),console.error("Response data:",t.response.data)),Error(`Failed to fetch historical data for ${e}: ${t.message}`)}}async getCompanyDetails(e){try{return(await t.default.get(`${r}/v3/reference/tickers/${e}`,{params:{apikey:this.apiKey}})).data.results}catch(e){return console.error("Error fetching company details:",e),null}}async getMarketStatus(){try{return(await t.default.get(`${r}/v1/marketstatus/now`,{params:{apikey:this.apiKey}})).data}catch(e){return console.error("Error fetching market status:",e),null}}async searchStocks(e,a=10){try{return(await t.default.get(`${r}/v3/reference/tickers`,{params:{search:e,market:"stocks",active:"true",limit:a,apikey:this.apiKey}})).data.results||[]}catch(e){return console.error("Error searching stocks:",e),[]}}}new n},29547,e=>{"use strict";e.s(["FMPAPI",()=>n]);var t=e.i(55362);let r="https://financialmodelingprep.com/api",a=process.env.FMP_API_KEY;class n{apiKey;constructor(e){if(this.apiKey=e||a||"",!this.apiKey)throw Error("FMP API key is required")}async getStockQuote(e){try{let a=(await t.default.get(`${r}/v3/quote/${e}`,{params:{apikey:this.apiKey}})).data[0];if(!a)throw Error(`No data found for symbol ${e}`);return{symbol:a.symbol,name:a.name||a.symbol,price:a.price,change:a.change,changePercent:a.changesPercentage,volume:a.volume,marketCap:a.marketCap,pe:a.pe,dividend:void 0}}catch(t){throw console.error("Error fetching FMP stock quote:",t),Error(`Failed to fetch quote for ${e}`)}}async getCompanyProfile(e){try{return(await t.default.get(`${r}/v3/profile/${e}`,{params:{apikey:this.apiKey}})).data[0]}catch(e){return console.error("Error fetching company profile:",e),null}}async getFinancialRatios(e){try{return(await t.default.get(`${r}/v3/ratios/${e}`,{params:{apikey:this.apiKey}})).data[0]}catch(e){return console.error("Error fetching financial ratios:",e),null}}async getKeyMetrics(e){try{return(await t.default.get(`${r}/v3/key-metrics/${e}`,{params:{apikey:this.apiKey}})).data[0]}catch(e){return console.error("Error fetching key metrics:",e),null}}async getAnalystRecommendations(e){try{return(await t.default.get(`${r}/v3/analyst-stock-recommendations/${e}`,{params:{apikey:this.apiKey}})).data}catch(e){return console.error("Error fetching analyst recommendations:",e),[]}}async getEarningsCalendar(e,a){try{let n={apikey:this.apiKey};return e&&(n.from=e),a&&(n.to=a),(await t.default.get(`${r}/v3/earning_calendar`,{params:n})).data}catch(e){return console.error("Error fetching earnings calendar:",e),[]}}async getEconomicCalendar(e,a){try{let n={apikey:this.apiKey};return e&&(n.from=e),a&&(n.to=a),(await t.default.get(`${r}/v3/economic_calendar`,{params:n})).data}catch(e){return console.error("Error fetching economic calendar:",e),[]}}async searchStocks(e,a=10){try{return(await t.default.get(`${r}/v3/search`,{params:{query:e,limit:a,apikey:this.apiKey}})).data}catch(e){return console.error("Error searching stocks:",e),[]}}async getSectorPerformance(){try{return(await t.default.get(`${r}/v3/sector-performance`,{params:{apikey:this.apiKey}})).data}catch(e){return console.error("Error fetching sector performance:",e),[]}}async getMarketMovers(e){try{return(await t.default.get(`${r}/v3/stock_market/${e}`,{params:{apikey:this.apiKey}})).data}catch(t){return console.error(`Error fetching market ${e}:`,t),[]}}async getEarningsCalendar(e,a=30){try{let n=new Date;n.setDate(n.getDate()-a);let s=new Date;return(await t.default.get(`${r}/v3/earning_calendar`,{params:{apikey:this.apiKey,from:n.toISOString().split("T")[0],to:s.toISOString().split("T")[0],...e&&{symbol:e.toUpperCase()}}})).data||[]}catch(e){return console.error("Error fetching earnings calendar:",e),[]}}async getStockNews(e,a=50){try{return(await t.default.get(`${r}/v3/stock_news`,{params:{apikey:this.apiKey,tickers:e.toUpperCase(),limit:a}})).data||[]}catch(e){return console.error("Error fetching stock news:",e),[]}}async getAnalystRecommendations(e,a=30){try{return(await t.default.get(`${r}/v3/analyst-stock-recommendations/${e.toUpperCase()}`,{params:{apikey:this.apiKey,limit:a}})).data||[]}catch(e){return console.error("Error fetching analyst recommendations:",e),[]}}async getInsiderTrading(e,a=30){try{let n=await t.default.get(`${r}/v4/insider-trading`,{params:{apikey:this.apiKey,symbol:e.toUpperCase(),limit:5*a}}),s=new Date;return s.setDate(s.getDate()-a),(n.data||[]).filter(e=>new Date(e.filingDate)>=s)}catch(e){return console.error("Error fetching insider trading:",e),[]}}async getSECFilings(e,a=30){try{let n=await t.default.get(`${r}/v3/sec_filings/${e.toUpperCase()}`,{params:{apikey:this.apiKey,limit:2*a}}),s=new Date;return s.setDate(s.getDate()-a),(n.data||[]).filter(e=>new Date(e.filedDate)>=s)}catch(e){return console.error("Error fetching SEC filings:",e),[]}}async getPreMarketQuote(e){try{let a=(await t.default.get(`${r}/v3/quote/${e.toUpperCase()}`,{params:{apikey:this.apiKey}})).data[0];if(!a)return null;return{symbol:a.symbol,price:a.price,previousClose:a.previousClose,change:a.change,changePercent:a.changesPercentage,volume:a.volume,marketCap:a.marketCap,avgVolume:a.avgVolume,preMarketPrice:a.preMarketPrice||a.price,preMarketChange:a.preMarketChange||a.change,preMarketChangePercent:a.preMarketChangePercent||a.changesPercentage}}catch(e){return console.error("Error fetching pre-market quote:",e),null}}async getMultiplePreMarketQuotes(e){try{let a=e.map(e=>e.toUpperCase()).join(",");return((await t.default.get(`${r}/v3/quote/${a}`,{params:{apikey:this.apiKey}})).data||[]).map(e=>({symbol:e.symbol,price:e.price,previousClose:e.previousClose,change:e.change,changePercent:e.changesPercentage,volume:e.volume,marketCap:e.marketCap,avgVolume:e.avgVolume,preMarketPrice:e.preMarketPrice||e.price,preMarketChange:e.preMarketChange||e.change,preMarketChangePercent:e.preMarketChangePercent||e.changesPercentage}))}catch(e){return console.error("Error fetching multiple pre-market quotes:",e),[]}}async getCompanyProfile(e){try{return(await t.default.get(`${r}/v3/profile/${e.toUpperCase()}`,{params:{apikey:this.apiKey}})).data[0]||null}catch(e){return console.error("Error fetching company profile:",e),null}}}new n},86997,(e,t,r)=>{},56831,e=>{"use strict";e.s(["handler",()=>S,"patchFetch",()=>$,"routeModule",()=>x,"serverHooks",()=>R,"workAsyncStorage",()=>P,"workUnitAsyncStorage",()=>C],56831);var t=e.i(11971),r=e.i(6780),a=e.i(51842),n=e.i(62950),s=e.i(21346),o=e.i(30506),i=e.i(63077),c=e.i(34765),l=e.i(64182),p=e.i(85062),u=e.i(51548),d=e.i(95133),g=e.i(8819),h=e.i(41050),y=e.i(93695);e.i(96641);var m=e.i(3893);e.s(["GET",()=>k,"POST",()=>w],1557);var f=e.i(59169),v=e.i(80412);async function k(e){try{let{searchParams:t}=new URL(e.url),r=parseFloat(t.get("minGap")||"3"),a=parseFloat(t.get("maxGap")||"15"),n=t.get("universe")?.split(",").filter(Boolean),s=t.get("catalystTypes")?.split(",").filter(Boolean),o="true"===t.get("perfectPickOnly"),i=parseInt(t.get("limit")||"50");console.log("📊 Gap Scanner API called with params:",{minGap:r,maxGap:a,customUniverse:n?.length||"default",catalystTypes:s,perfectPickOnly:o,limit:i});let c=new v.PreMarketGapScanner(process.env.FMP_API_KEY,process.env.POLYGON_API_KEY),l=(o?await c.getPerfectPickCandidates(n):s&&s.length>0?await c.getCatalystTypeResults(s,n):await c.getGapRangeResults(r,a,n)).slice(0,i),p=c.getScanSummary(l),u={success:!0,data:{results:l,summary:p,scanParams:{minGap:r,maxGap:a,universeSize:n?.length||"default",catalystTypes:s,perfectPickOnly:o,limit:i},timestamp:new Date().toISOString()}};return f.NextResponse.json(u)}catch(e){return console.error("Error in Gap Scanner API:",e),f.NextResponse.json({success:!1,error:"Failed to run gap scan",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function w(e){try{let{action:t,data:r}=await e.json(),a=new v.PreMarketGapScanner(process.env.FMP_API_KEY,process.env.POLYGON_API_KEY);switch(t){case"update_results":let n=await a.updateScanResults(r.results);return f.NextResponse.json({success:!0,data:{results:n}});case"get_scheduled_times":let s=a.getScheduledScanTimes();return f.NextResponse.json({success:!0,data:{scheduledTimes:s}});default:return f.NextResponse.json({success:!1,error:"Invalid action"},{status:400})}}catch(e){return console.error("Error in Gap Scanner POST API:",e),f.NextResponse.json({success:!1,error:"Failed to process gap scanner request",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}var E=e.i(1557);let x=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/scanner/gap-scan/route",pathname:"/api/scanner/gap-scan",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/swing-trader-ai/src/app/api/scanner/gap-scan/route.ts",nextConfigOutput:"",userland:E}),{workAsyncStorage:P,workUnitAsyncStorage:C,serverHooks:R}=x;function $(){return(0,a.patchFetch)({workAsyncStorage:P,workUnitAsyncStorage:C})}async function S(e,t,a){var f;let v="/api/scanner/gap-scan/route";v=v.replace(/\/index$/,"")||"/";let k=await x.prepare(e,t,{srcPage:v,multiZoneDraftMode:!1});if(!k)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:w,params:E,nextConfig:P,isDraftMode:C,prerenderManifest:R,routerServerContext:$,isOnDemandRevalidate:S,revalidateOnlyGenerated:A,resolvedPathname:K}=k,_=(0,o.normalizeAppPath)(v),M=!!(R.dynamicRoutes[_]||R.routes[K]);if(M&&!C){let e=!!R.routes[K],t=R.dynamicRoutes[_];if(t&&!1===t.fallback&&!e)throw new y.NoFallbackError}let b=null;!M||x.isDev||C||(b="/index"===(b=K)?"/":b);let N=!0===x.isDev||!M,q=M&&!N,I=e.method||"GET",O=(0,s.getTracer)(),T=O.getActiveScopeSpan(),U={params:E,prerenderManifest:R,renderOpts:{experimental:{cacheComponents:!!P.experimental.cacheComponents,authInterrupts:!!P.experimental.authInterrupts},supportsDynamicResponse:N,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(f=P.experimental)?void 0:f.cacheLife,isRevalidate:q,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>x.onRequestError(e,t,a,$)},sharedContext:{buildId:w}},D=new i.NodeNextRequest(e),j=new i.NodeNextResponse(t),F=c.NextRequestAdapter.fromNodeNextRequest(D,(0,c.signalFromNodeResponse)(t));try{let o=async r=>x.handle(F,U).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=O.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==l.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${I} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${I} ${e.url}`)}),i=async s=>{var i,c;let l=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&S&&A&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let i=await o(s);e.fetchMetrics=U.renderOpts.fetchMetrics;let c=U.renderOpts.pendingWaitUntil;c&&a.waitUntil&&(a.waitUntil(c),c=void 0);let l=U.renderOpts.collectedTags;if(!M)return await (0,u.sendResponse)(D,j,i,U.renderOpts.pendingWaitUntil),null;{let e=await i.blob(),t=(0,d.toNodeOutgoingHttpHeaders)(i.headers);l&&(t[h.NEXT_CACHE_TAGS_HEADER]=l),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==U.renderOpts.collectedRevalidate&&!(U.renderOpts.collectedRevalidate>=h.INFINITE_CACHE)&&U.renderOpts.collectedRevalidate,a=void 0===U.renderOpts.collectedExpire||U.renderOpts.collectedExpire>=h.INFINITE_CACHE?void 0:U.renderOpts.collectedExpire;return{value:{kind:m.CachedRouteKind.APP_ROUTE,status:i.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await x.onRequestError(e,t,{routerKind:"App Router",routePath:v,routeType:"route",revalidateReason:(0,p.getRevalidateReason)({isRevalidate:q,isOnDemandRevalidate:S})},$),t}},y=await x.handleResponse({req:e,nextConfig:P,cacheKey:b,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:R,isRoutePPREnabled:!1,isOnDemandRevalidate:S,revalidateOnlyGenerated:A,responseGenerator:l,waitUntil:a.waitUntil});if(!M)return null;if((null==y||null==(i=y.value)?void 0:i.kind)!==m.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==y||null==(c=y.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",S?"REVALIDATED":y.isMiss?"MISS":y.isStale?"STALE":"HIT"),C&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let f=(0,d.fromNodeOutgoingHttpHeaders)(y.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&M||f.delete(h.NEXT_CACHE_TAGS_HEADER),!y.cacheControl||t.getHeader("Cache-Control")||f.get("Cache-Control")||f.set("Cache-Control",(0,g.getCacheControlHeader)(y.cacheControl)),await (0,u.sendResponse)(D,j,new Response(y.value.body,{headers:f,status:y.value.status||200})),null};T?await i(T):await O.withPropagatedContext(e.headers,()=>O.trace(l.BaseServerSpan.handleRequest,{spanName:`${I} ${e.url}`,kind:s.SpanKind.SERVER,attributes:{"http.method":I,"http.target":e.url}},i))}catch(t){if(t instanceof y.NoFallbackError||await x.onRequestError(e,t,{routerKind:"App Router",routePath:_,routeType:"route",revalidateReason:(0,p.getRevalidateReason)({isRevalidate:q,isOnDemandRevalidate:S})}),M)throw t;return await (0,u.sendResponse)(D,j,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__e734d8cf._.js.map