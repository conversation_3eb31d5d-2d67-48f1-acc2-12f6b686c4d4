var R=require("../../../chunks/[turbopack]_runtime.js")("server/app/api/trading/route.js")
R.c("server/chunks/6bf44_next_c342be22._.js")
R.c("server/chunks/6bf44_@alpacahq_alpaca-trade-api_dist_c2d4f232._.js")
R.c("server/chunks/6bf44_lodash_425f2de2._.js")
R.c("server/chunks/eec84_ws_f1798c3c._.js")
R.c("server/chunks/6bf44_@stoqey_ib_dist_07ed8ba7._.js")
R.c("server/chunks/6bf44_rxjs_dist_cjs_5c50c2dc._.js")
R.c("server/chunks/6bf44_d96e1298._.js")
R.c("server/chunks/[root-of-the-server]__66cde483._.js")
R.m("[project]/swing-trader-ai/.next-internal/server/app/api/trading/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/swing-trader-ai/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/swing-trader-ai/src/app/api/trading/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/swing-trader-ai/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/swing-trader-ai/src/app/api/trading/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
