module.exports=[74770,e=>{"use strict";e.s(["EnhancedSwingScanner",()=>i,"enhancedSwingScanner",()=>s]);var t=e.i(96341),r=e.i(78006),a=e.i(29547),n=e.i(86678),o=e.i(97669);class i{fmpAPI;polygonAPI;accountSize;constructor(e=1e5){this.fmpAPI=new a.FMPAPI(process.env.FMP_API_KEY),this.polygonAPI=new r.PolygonAPI(process.env.POLYGON_API_KEY),this.accountSize=e}async scanWithStrategies(e,t=5){let r=Date.now(),a=[],n=[];console.log(`Starting enhanced strategy scan of ${e.length} stocks...`);let o=this.getMarketConditions();for(let r=0;r<e.length;r+=t){let o=e.slice(r,r+t),i=o.map(e=>this.scanSingleStockStrategies(e));(await Promise.allSettled(i)).forEach((e,t)=>{let r=o[t];"fulfilled"===e.status&&e.value?a.push(e.value):(n.push(r),console.warn(`Failed to scan ${r}:`,"rejected"===e.status?e.reason:"Unknown error"))}),r+t<e.length&&await new Promise(e=>setTimeout(e,1e3))}a.sort((e,t)=>t.overallScore-e.overallScore),a.forEach((e,t)=>{e.rank=t+1});let i=a.filter(e=>e.overnightSetup).length,s=a.filter(e=>e.breakoutSetup).length,l=a.filter(e=>e.overnightSetup&&e.breakoutSetup).length,c=Date.now()-r;return{totalScanned:e.length,overnightSetups:i,breakoutSetups:s,bothStrategies:l,topSetups:a.slice(0,25),scanDuration:c,marketConditions:o}}async scanSingleStockStrategies(e){try{let[r,a]=await Promise.all([this.fmpAPI.getStockQuote(e),this.getHistoricalData(e)]);if(!a||a.length<30)throw Error("Insufficient historical data - need at least 30 days");let n=t.SwingTradingStrategies.analyzeOvernightMomentum(e,a,r,this.accountSize),o=t.SwingTradingStrategies.analyzeTechnicalBreakout(e,a,r,this.accountSize);if(!n&&!o)return null;let{bestStrategy:i,overallScore:s}=this.calculateBestStrategy(n,o),l=this.generateAlerts(n,o,r),c=this.generateRiskWarnings(n,o,r);return{symbol:e,name:r.name,sector:this.getSectorForSymbol(e),quote:r,overnightSetup:n||void 0,breakoutSetup:o||void 0,bestStrategy:i,overallScore:s,rank:0,scanTime:new Date().toISOString(),alerts:l,riskWarnings:c}}catch(t){return console.error(`Error scanning ${e}:`,t),t instanceof Error&&(console.error(`Error message: ${t.message}`),console.error(`Error stack: ${t.stack}`)),null}}async getHistoricalData(e){let t=(0,n.format)(new Date,"yyyy-MM-dd"),r=(0,n.format)((0,o.subDays)(new Date,100),"yyyy-MM-dd");try{console.log(`Fetching historical data for ${e} from ${r} to ${t}`);let a=await this.polygonAPI.getHistoricalData(e,"day",1,r,t);if(0===a.length)throw console.warn(`No historical data returned for ${e}`),Error("No historical data available");return console.log(`Successfully fetched ${a.length} days of data for ${e}`),a}catch(t){throw console.error(`Failed to fetch historical data for ${e}:`,t),t}}calculateBestStrategy(e,t){if(!e&&!t)return{overallScore:0};if(e&&!t)return{bestStrategy:"overnight_momentum",overallScore:e.confidence};if(t&&!e)return{bestStrategy:"technical_breakout",overallScore:t.confidence};if(e&&t)if(e.confidence>t.confidence)return{bestStrategy:"overnight_momentum",overallScore:e.confidence+5};else return{bestStrategy:"technical_breakout",overallScore:t.confidence+5};return{overallScore:0}}generateAlerts(e,t,r){let a=[];return e&&(a.push(`🚀 OVERNIGHT MOMENTUM: Entry ${e.entryPrice.toFixed(2)}, Target ${e.targets[0].toFixed(2)}`),a.push(`⏰ Execute in final 30-60 min before close`),a.push(`🛑 Stop: ${e.stopLoss.toFixed(2)} (${((e.entryPrice-e.stopLoss)/e.entryPrice*100).toFixed(1)}% risk)`)),t&&(a.push(`📈 BREAKOUT SETUP: Entry ${t.entryPrice.toFixed(2)}, riding 8-EMA`),a.push(`🎯 Targets: ${t.targets.map(e=>e.toFixed(2)).join(", ")}`),a.push(`🛑 Stop: Daily close below ${t.stopLoss.toFixed(2)}`)),r&&r.changePercent>5&&a.push(`🔥 Strong momentum: +${r.changePercent.toFixed(1)}% today`),a}generateRiskWarnings(e,t,r){let a=[];return e&&(a.push(`⚠️ Overnight gap risk - size down vs intraday trades`),r&&r.changePercent>8&&a.push(`⚠️ Extended move (+${r.changePercent.toFixed(1)}%) - consider smaller size`)),r&&1e9>(r.marketCap||0)&&a.push(`⚠️ Small cap overnight risk - volatile gaps possible`),r&&r.volume<1e6&&a.push(`⚠️ Lower volume - may have liquidity issues`),a}getMarketConditions(){let e=new Date,t=new Date(e.toLocaleString("en-US",{timeZone:"America/New_York"})),r=t.getHours(),a=t.getMinutes(),n=r+a/60,o=e.getHours(),i=e.getMinutes(),s=t.getDay(),l=s>=1&&s<=5;return{timeOfDay:`${o.toString().padStart(2,"0")}:${i.toString().padStart(2,"0")} Local (${r.toString().padStart(2,"0")}:${a.toString().padStart(2,"0")} ET)`,isOptimalScanTime:l&&n>=12&&n<=16,marketHours:l&&n>=9.5&&n<=16,etTime:`${r.toString().padStart(2,"0")}:${a.toString().padStart(2,"0")} ET`,isWeekday:l}}getSectorForSymbol(e){return["MSFT","NVDA","GOOG","GOOGL","META","AVGO","TSM","ORCL","CSCO","AMD","ASML","MU","LRCX","PLTR","APP","NET","DDOG","ZS","SHOP","SOUN","IONQ","RGTI","RIOT","HUT","IREN","ASTS","NBIS"].includes(e)?"Technology":["JPM","BAC","MS","SCHW","C","HOOD","SOFI","TIGR","FUTU"].includes(e)?"Financial Services":["JNJ","ABBV","MRK","GILD"].includes(e)?"Healthcare":["GE","CAT","BA","GEV","UAL","VRT","RKLB"].includes(e)?"Industrial":["AEM","NEM","PAAS","BTG","HL","MP","AG"].includes(e)?"Materials":["AMZN","DIS","SBUX","MO","DASH","GM","NCLH","CELH","LEVI","ELF","ETSY","W"].includes(e)?"Consumer":["NFLX","RBLX","BILI"].includes(e)?"Communication Services":["CEG","VST","CCJ"].includes(e)?"Energy":"Other"}async quickStrategyScan(e){return(await this.scanWithStrategies(e,8)).topSetups}}let s=new i},6413,e=>{"use strict";e.s(["handler",()=>C,"patchFetch",()=>T,"routeModule",()=>R,"serverHooks",()=>P,"workAsyncStorage",()=>w,"workUnitAsyncStorage",()=>A],6413);var t=e.i(11971),r=e.i(6780),a=e.i(51842),n=e.i(62950),o=e.i(21346),i=e.i(30506),s=e.i(63077),l=e.i(34765),c=e.i(64182),u=e.i(85062),d=e.i(51548),p=e.i(95133),g=e.i(8819),h=e.i(41050),S=e.i(93695);e.i(96641);var m=e.i(3893);e.s(["GET",()=>E],78042);var f=e.i(59169),y=e.i(5744);async function E(t){try{let r,{searchParams:a}=new URL(t.url),n=a.get("type")||"quick",o=parseInt(a.get("accountSize")||"100000"),i=parseInt(a.get("limit")||"20");console.log(`Starting ${n} strategy scan...`);let s=new(e.r(74770)).EnhancedSwingScanner(o),l={...r="full"===n?await s.scanWithStrategies(y.ALL_SYMBOLS,3):await s.scanWithStrategies(y.PRIORITY_SYMBOLS,6),topSetups:r.topSetups.slice(0,i)};return f.NextResponse.json(l)}catch(e){return console.error("Error in strategy scanner API:",e),f.NextResponse.json({error:"Failed to perform strategy scan"},{status:500})}}var v=e.i(78042);let R=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/scanner/strategies/route",pathname:"/api/scanner/strategies",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/swing-trader-ai/src/app/api/scanner/strategies/route.ts",nextConfigOutput:"",userland:v}),{workAsyncStorage:w,workUnitAsyncStorage:A,serverHooks:P}=R;function T(){return(0,a.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:A})}async function C(e,t,a){var f;let y="/api/scanner/strategies/route";y=y.replace(/\/index$/,"")||"/";let E=await R.prepare(e,t,{srcPage:y,multiZoneDraftMode:!1});if(!E)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:v,params:w,nextConfig:A,isDraftMode:P,prerenderManifest:T,routerServerContext:C,isOnDemandRevalidate:O,revalidateOnlyGenerated:M,resolvedPathname:x}=E,I=(0,i.normalizeAppPath)(y),N=!!(T.dynamicRoutes[I]||T.routes[x]);if(N&&!P){let e=!!T.routes[x],t=T.dynamicRoutes[I];if(t&&!1===t.fallback&&!e)throw new S.NoFallbackError}let k=null;!N||R.isDev||P||(k="/index"===(k=x)?"/":k);let $=!0===R.isDev||!N,b=N&&!$,D=e.method||"GET",H=(0,o.getTracer)(),L=H.getActiveScopeSpan(),F={params:w,prerenderManifest:T,renderOpts:{experimental:{cacheComponents:!!A.experimental.cacheComponents,authInterrupts:!!A.experimental.authInterrupts},supportsDynamicResponse:$,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(f=A.experimental)?void 0:f.cacheLife,isRevalidate:b,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>R.onRequestError(e,t,a,C)},sharedContext:{buildId:v}},_=new s.NodeNextRequest(e),U=new s.NodeNextResponse(t),B=l.NextRequestAdapter.fromNodeNextRequest(_,(0,l.signalFromNodeResponse)(t));try{let i=async r=>R.handle(B,F).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=H.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==c.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${D} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${D} ${e.url}`)}),s=async o=>{var s,l;let c=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&O&&M&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let s=await i(o);e.fetchMetrics=F.renderOpts.fetchMetrics;let l=F.renderOpts.pendingWaitUntil;l&&a.waitUntil&&(a.waitUntil(l),l=void 0);let c=F.renderOpts.collectedTags;if(!N)return await (0,d.sendResponse)(_,U,s,F.renderOpts.pendingWaitUntil),null;{let e=await s.blob(),t=(0,p.toNodeOutgoingHttpHeaders)(s.headers);c&&(t[h.NEXT_CACHE_TAGS_HEADER]=c),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==F.renderOpts.collectedRevalidate&&!(F.renderOpts.collectedRevalidate>=h.INFINITE_CACHE)&&F.renderOpts.collectedRevalidate,a=void 0===F.renderOpts.collectedExpire||F.renderOpts.collectedExpire>=h.INFINITE_CACHE?void 0:F.renderOpts.collectedExpire;return{value:{kind:m.CachedRouteKind.APP_ROUTE,status:s.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await R.onRequestError(e,t,{routerKind:"App Router",routePath:y,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:b,isOnDemandRevalidate:O})},C),t}},S=await R.handleResponse({req:e,nextConfig:A,cacheKey:k,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:T,isRoutePPREnabled:!1,isOnDemandRevalidate:O,revalidateOnlyGenerated:M,responseGenerator:c,waitUntil:a.waitUntil});if(!N)return null;if((null==S||null==(s=S.value)?void 0:s.kind)!==m.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==S||null==(l=S.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",O?"REVALIDATED":S.isMiss?"MISS":S.isStale?"STALE":"HIT"),P&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let f=(0,p.fromNodeOutgoingHttpHeaders)(S.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&N||f.delete(h.NEXT_CACHE_TAGS_HEADER),!S.cacheControl||t.getHeader("Cache-Control")||f.get("Cache-Control")||f.set("Cache-Control",(0,g.getCacheControlHeader)(S.cacheControl)),await (0,d.sendResponse)(_,U,new Response(S.value.body,{headers:f,status:S.value.status||200})),null};L?await s(L):await H.withPropagatedContext(e.headers,()=>H.trace(c.BaseServerSpan.handleRequest,{spanName:`${D} ${e.url}`,kind:o.SpanKind.SERVER,attributes:{"http.method":D,"http.target":e.url}},s))}catch(t){if(t instanceof S.NoFallbackError||await R.onRequestError(e,t,{routerKind:"App Router",routePath:I,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:b,isOnDemandRevalidate:O})}),N)throw t;return await (0,d.sendResponse)(_,U,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=swing-trader-ai_6a6b8f95._.js.map