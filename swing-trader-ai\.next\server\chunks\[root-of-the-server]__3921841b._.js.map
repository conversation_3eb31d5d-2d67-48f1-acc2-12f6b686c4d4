{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/lib/ibkr.ts"], "sourcesContent": ["import { I<PERSON>pi, EventName, ErrorCode, Contract, Order, OrderState } from '@stoqey/ib';\n\nexport interface IBKRConfig {\n  host: string;\n  port: number;\n  clientId: number;\n  paperTrading: boolean;\n}\n\nexport interface IBKRPosition {\n  symbol: string;\n  position: number;\n  marketPrice: number;\n  marketValue: number;\n  averageCost: number;\n  unrealizedPNL: number;\n  realizedPNL: number;\n}\n\nexport interface IBKROrder {\n  orderId: number;\n  symbol: string;\n  action: 'BUY' | 'SELL';\n  quantity: number;\n  orderType: string;\n  price?: number;\n  status: string;\n  filled: number;\n  remaining: number;\n}\n\nexport interface IBKRAccountSummary {\n  totalCashValue: number;\n  netLiquidation: number;\n  grossPositionValue: number;\n  availableFunds: number;\n  buyingPower: number;\n  unrealizedPnL: number;\n  realizedPnL: number;\n}\n\nexport class IBKRAPI {\n  private ib: IBApi;\n  private config: IBKRConfig;\n  private connected: boolean = false;\n  private nextOrderId: number = 1;\n  private positions: Map<string, IBKRPosition> = new Map();\n  private orders: Map<number, IBKROrder> = new Map();\n  private accountSummary: IBKRAccountSummary | null = null;\n\n  constructor(config: IBKRConfig) {\n    this.config = config;\n    this.ib = new IBApi({\n      host: config.host,\n      port: config.port,\n      clientId: config.clientId,\n    });\n\n    this.setupEventHandlers();\n  }\n\n  private setupEventHandlers() {\n    // Connection events\n    this.ib.on(EventName.connected, () => {\n      console.log('✅ Connected to IBKR');\n      this.connected = true;\n      this.requestNextOrderId();\n      this.requestAccountSummary();\n      this.requestPositions();\n    });\n\n    this.ib.on(EventName.disconnected, () => {\n      console.log('❌ Disconnected from IBKR');\n      this.connected = false;\n    });\n\n    this.ib.on(EventName.error, (err, code, reqId) => {\n      console.error(`IBKR Error ${code}:`, err);\n    });\n\n    // Order management\n    this.ib.on(EventName.nextValidId, (orderId: number) => {\n      this.nextOrderId = orderId;\n      console.log(`Next valid order ID: ${orderId}`);\n    });\n\n    this.ib.on(EventName.orderStatus, (orderId, status, filled, remaining, avgFillPrice, permId, parentId, lastFillPrice, clientId, whyHeld, mktCapPrice) => {\n      const order = this.orders.get(orderId);\n      if (order) {\n        order.status = status;\n        order.filled = filled;\n        order.remaining = remaining;\n        this.orders.set(orderId, order);\n      }\n    });\n\n    // Position updates\n    this.ib.on(EventName.position, (account, contract, position, avgCost) => {\n      const symbol = contract.symbol;\n      const existingPosition = this.positions.get(symbol) || {\n        symbol,\n        position: 0,\n        marketPrice: 0,\n        marketValue: 0,\n        averageCost: 0,\n        unrealizedPNL: 0,\n        realizedPNL: 0\n      };\n\n      existingPosition.position = position;\n      existingPosition.averageCost = avgCost;\n      this.positions.set(symbol, existingPosition);\n    });\n\n    // Account summary\n    this.ib.on(EventName.accountSummary, (reqId, account, tag, value, currency) => {\n      if (!this.accountSummary) {\n        this.accountSummary = {\n          totalCashValue: 0,\n          netLiquidation: 0,\n          grossPositionValue: 0,\n          availableFunds: 0,\n          buyingPower: 0,\n          unrealizedPnL: 0,\n          realizedPnL: 0\n        };\n      }\n\n      switch (tag) {\n        case 'TotalCashValue':\n          this.accountSummary.totalCashValue = parseFloat(value);\n          break;\n        case 'NetLiquidation':\n          this.accountSummary.netLiquidation = parseFloat(value);\n          break;\n        case 'GrossPositionValue':\n          this.accountSummary.grossPositionValue = parseFloat(value);\n          break;\n        case 'AvailableFunds':\n          this.accountSummary.availableFunds = parseFloat(value);\n          break;\n        case 'BuyingPower':\n          this.accountSummary.buyingPower = parseFloat(value);\n          break;\n        case 'UnrealizedPnL':\n          this.accountSummary.unrealizedPnL = parseFloat(value);\n          break;\n        case 'RealizedPnL':\n          this.accountSummary.realizedPnL = parseFloat(value);\n          break;\n      }\n    });\n  }\n\n  async connect(): Promise<void> {\n    return new Promise((resolve, reject) => {\n      if (this.connected) {\n        resolve();\n        return;\n      }\n\n      const timeout = setTimeout(() => {\n        reject(new Error('Connection timeout'));\n      }, 10000);\n\n      this.ib.once(EventName.connected, () => {\n        clearTimeout(timeout);\n        resolve();\n      });\n\n      this.ib.once(EventName.error, (err) => {\n        clearTimeout(timeout);\n        reject(err);\n      });\n\n      this.ib.connect();\n    });\n  }\n\n  disconnect(): void {\n    if (this.connected) {\n      this.ib.disconnect();\n    }\n  }\n\n  private requestNextOrderId(): void {\n    this.ib.reqIds(1);\n  }\n\n  private requestAccountSummary(): void {\n    this.ib.reqAccountSummary(1, 'All', 'TotalCashValue,NetLiquidation,GrossPositionValue,AvailableFunds,BuyingPower,UnrealizedPnL,RealizedPnL');\n  }\n\n  private requestPositions(): void {\n    this.ib.reqPositions();\n  }\n\n  // Create a stock contract\n  private createStockContract(symbol: string): Contract {\n    return {\n      symbol: symbol.toUpperCase(),\n      secType: 'STK',\n      exchange: 'SMART',\n      currency: 'USD',\n    };\n  }\n\n  // Place a market order\n  async placeMarketOrder(symbol: string, action: 'BUY' | 'SELL', quantity: number): Promise<number> {\n    if (!this.connected) {\n      throw new Error('Not connected to IBKR');\n    }\n\n    const contract = this.createStockContract(symbol);\n    const order: Order = {\n      orderId: this.nextOrderId,\n      action,\n      totalQuantity: quantity,\n      orderType: 'MKT',\n    };\n\n    // Store order for tracking\n    this.orders.set(this.nextOrderId, {\n      orderId: this.nextOrderId,\n      symbol: symbol.toUpperCase(),\n      action,\n      quantity,\n      orderType: 'MKT',\n      status: 'Submitted',\n      filled: 0,\n      remaining: quantity\n    });\n\n    this.ib.placeOrder(this.nextOrderId, contract, order);\n    const orderId = this.nextOrderId;\n    this.nextOrderId++;\n\n    return orderId;\n  }\n\n  // Place a limit order\n  async placeLimitOrder(symbol: string, action: 'BUY' | 'SELL', quantity: number, price: number): Promise<number> {\n    if (!this.connected) {\n      throw new Error('Not connected to IBKR');\n    }\n\n    const contract = this.createStockContract(symbol);\n    const order: Order = {\n      orderId: this.nextOrderId,\n      action,\n      totalQuantity: quantity,\n      orderType: 'LMT',\n      lmtPrice: price,\n    };\n\n    // Store order for tracking\n    this.orders.set(this.nextOrderId, {\n      orderId: this.nextOrderId,\n      symbol: symbol.toUpperCase(),\n      action,\n      quantity,\n      orderType: 'LMT',\n      price,\n      status: 'Submitted',\n      filled: 0,\n      remaining: quantity\n    });\n\n    this.ib.placeOrder(this.nextOrderId, contract, order);\n    const orderId = this.nextOrderId;\n    this.nextOrderId++;\n\n    return orderId;\n  }\n\n  // Place a stop-loss order\n  async placeStopOrder(symbol: string, action: 'BUY' | 'SELL', quantity: number, stopPrice: number): Promise<number> {\n    if (!this.connected) {\n      throw new Error('Not connected to IBKR');\n    }\n\n    const contract = this.createStockContract(symbol);\n    const order: Order = {\n      orderId: this.nextOrderId,\n      action,\n      totalQuantity: quantity,\n      orderType: 'STP',\n      auxPrice: stopPrice,\n    };\n\n    // Store order for tracking\n    this.orders.set(this.nextOrderId, {\n      orderId: this.nextOrderId,\n      symbol: symbol.toUpperCase(),\n      action,\n      quantity,\n      orderType: 'STP',\n      price: stopPrice,\n      status: 'Submitted',\n      filled: 0,\n      remaining: quantity\n    });\n\n    this.ib.placeOrder(this.nextOrderId, contract, order);\n    const orderId = this.nextOrderId;\n    this.nextOrderId++;\n\n    return orderId;\n  }\n\n  // Cancel an order\n  async cancelOrder(orderId: number): Promise<void> {\n    if (!this.connected) {\n      throw new Error('Not connected to IBKR');\n    }\n\n    this.ib.cancelOrder(orderId);\n  }\n\n  // Get account summary\n  getAccountSummary(): IBKRAccountSummary | null {\n    return this.accountSummary;\n  }\n\n  // Get all positions\n  getPositions(): IBKRPosition[] {\n    return Array.from(this.positions.values());\n  }\n\n  // Get position for specific symbol\n  getPosition(symbol: string): IBKRPosition | null {\n    return this.positions.get(symbol.toUpperCase()) || null;\n  }\n\n  // Get all orders\n  getOrders(): IBKROrder[] {\n    return Array.from(this.orders.values());\n  }\n\n  // Get specific order\n  getOrder(orderId: number): IBKROrder | null {\n    return this.orders.get(orderId) || null;\n  }\n\n  // Check if connected\n  isConnected(): boolean {\n    return this.connected;\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAyCO,MAAM;IACH,GAAU;IACV,OAAmB;IACnB,YAAqB,MAAM;IAC3B,cAAsB,EAAE;IACxB,YAAuC,IAAI,MAAM;IACjD,SAAiC,IAAI,MAAM;IAC3C,iBAA4C,KAAK;IAEzD,YAAY,MAAkB,CAAE;QAC9B,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,EAAE,GAAG,IAAI,mLAAK,CAAC;YAClB,MAAM,OAAO,IAAI;YACjB,MAAM,OAAO,IAAI;YACjB,UAAU,OAAO,QAAQ;QAC3B;QAEA,IAAI,CAAC,kBAAkB;IACzB;IAEQ,qBAAqB;QAC3B,oBAAoB;QACpB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,uLAAS,CAAC,SAAS,EAAE;YAC9B,QAAQ,GAAG,CAAC;YACZ,IAAI,CAAC,SAAS,GAAG;YACjB,IAAI,CAAC,kBAAkB;YACvB,IAAI,CAAC,qBAAqB;YAC1B,IAAI,CAAC,gBAAgB;QACvB;QAEA,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,uLAAS,CAAC,YAAY,EAAE;YACjC,QAAQ,GAAG,CAAC;YACZ,IAAI,CAAC,SAAS,GAAG;QACnB;QAEA,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,uLAAS,CAAC,KAAK,EAAE,CAAC,KAAK,MAAM;YACtC,QAAQ,KAAK,CAAC,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,EAAE;QACvC;QAEA,mBAAmB;QACnB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,uLAAS,CAAC,WAAW,EAAE,CAAC;YACjC,IAAI,CAAC,WAAW,GAAG;YACnB,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,SAAS;QAC/C;QAEA,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,uLAAS,CAAC,WAAW,EAAE,CAAC,SAAS,QAAQ,QAAQ,WAAW,cAAc,QAAQ,UAAU,eAAe,UAAU,SAAS;YACvI,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;YAC9B,IAAI,OAAO;gBACT,MAAM,MAAM,GAAG;gBACf,MAAM,MAAM,GAAG;gBACf,MAAM,SAAS,GAAG;gBAClB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS;YAC3B;QACF;QAEA,mBAAmB;QACnB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,uLAAS,CAAC,QAAQ,EAAE,CAAC,SAAS,UAAU,UAAU;YAC3D,MAAM,SAAS,SAAS,MAAM;YAC9B,MAAM,mBAAmB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW;gBACrD;gBACA,UAAU;gBACV,aAAa;gBACb,aAAa;gBACb,aAAa;gBACb,eAAe;gBACf,aAAa;YACf;YAEA,iBAAiB,QAAQ,GAAG;YAC5B,iBAAiB,WAAW,GAAG;YAC/B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ;QAC7B;QAEA,kBAAkB;QAClB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,uLAAS,CAAC,cAAc,EAAE,CAAC,OAAO,SAAS,KAAK,OAAO;YAChE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBACxB,IAAI,CAAC,cAAc,GAAG;oBACpB,gBAAgB;oBAChB,gBAAgB;oBAChB,oBAAoB;oBACpB,gBAAgB;oBAChB,aAAa;oBACb,eAAe;oBACf,aAAa;gBACf;YACF;YAEA,OAAQ;gBACN,KAAK;oBACH,IAAI,CAAC,cAAc,CAAC,cAAc,GAAG,WAAW;oBAChD;gBACF,KAAK;oBACH,IAAI,CAAC,cAAc,CAAC,cAAc,GAAG,WAAW;oBAChD;gBACF,KAAK;oBACH,IAAI,CAAC,cAAc,CAAC,kBAAkB,GAAG,WAAW;oBACpD;gBACF,KAAK;oBACH,IAAI,CAAC,cAAc,CAAC,cAAc,GAAG,WAAW;oBAChD;gBACF,KAAK;oBACH,IAAI,CAAC,cAAc,CAAC,WAAW,GAAG,WAAW;oBAC7C;gBACF,KAAK;oBACH,IAAI,CAAC,cAAc,CAAC,aAAa,GAAG,WAAW;oBAC/C;gBACF,KAAK;oBACH,IAAI,CAAC,cAAc,CAAC,WAAW,GAAG,WAAW;oBAC7C;YACJ;QACF;IACF;IAEA,MAAM,UAAyB;QAC7B,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,IAAI,IAAI,CAAC,SAAS,EAAE;gBAClB;gBACA;YACF;YAEA,MAAM,UAAU,WAAW;gBACzB,OAAO,IAAI,MAAM;YACnB,GAAG;YAEH,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,uLAAS,CAAC,SAAS,EAAE;gBAChC,aAAa;gBACb;YACF;YAEA,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,uLAAS,CAAC,KAAK,EAAE,CAAC;gBAC7B,aAAa;gBACb,OAAO;YACT;YAEA,IAAI,CAAC,EAAE,CAAC,OAAO;QACjB;IACF;IAEA,aAAmB;QACjB,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,EAAE,CAAC,UAAU;QACpB;IACF;IAEQ,qBAA2B;QACjC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC;IACjB;IAEQ,wBAA8B;QACpC,IAAI,CAAC,EAAE,CAAC,iBAAiB,CAAC,GAAG,OAAO;IACtC;IAEQ,mBAAyB;QAC/B,IAAI,CAAC,EAAE,CAAC,YAAY;IACtB;IAEA,0BAA0B;IAClB,oBAAoB,MAAc,EAAY;QACpD,OAAO;YACL,QAAQ,OAAO,WAAW;YAC1B,SAAS;YACT,UAAU;YACV,UAAU;QACZ;IACF;IAEA,uBAAuB;IACvB,MAAM,iBAAiB,MAAc,EAAE,MAAsB,EAAE,QAAgB,EAAmB;QAChG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,IAAI,CAAC,mBAAmB,CAAC;QAC1C,MAAM,QAAe;YACnB,SAAS,IAAI,CAAC,WAAW;YACzB;YACA,eAAe;YACf,WAAW;QACb;QAEA,2BAA2B;QAC3B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE;YAChC,SAAS,IAAI,CAAC,WAAW;YACzB,QAAQ,OAAO,WAAW;YAC1B;YACA;YACA,WAAW;YACX,QAAQ;YACR,QAAQ;YACR,WAAW;QACb;QAEA,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU;QAC/C,MAAM,UAAU,IAAI,CAAC,WAAW;QAChC,IAAI,CAAC,WAAW;QAEhB,OAAO;IACT;IAEA,sBAAsB;IACtB,MAAM,gBAAgB,MAAc,EAAE,MAAsB,EAAE,QAAgB,EAAE,KAAa,EAAmB;QAC9G,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,IAAI,CAAC,mBAAmB,CAAC;QAC1C,MAAM,QAAe;YACnB,SAAS,IAAI,CAAC,WAAW;YACzB;YACA,eAAe;YACf,WAAW;YACX,UAAU;QACZ;QAEA,2BAA2B;QAC3B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE;YAChC,SAAS,IAAI,CAAC,WAAW;YACzB,QAAQ,OAAO,WAAW;YAC1B;YACA;YACA,WAAW;YACX;YACA,QAAQ;YACR,QAAQ;YACR,WAAW;QACb;QAEA,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU;QAC/C,MAAM,UAAU,IAAI,CAAC,WAAW;QAChC,IAAI,CAAC,WAAW;QAEhB,OAAO;IACT;IAEA,0BAA0B;IAC1B,MAAM,eAAe,MAAc,EAAE,MAAsB,EAAE,QAAgB,EAAE,SAAiB,EAAmB;QACjH,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,IAAI,CAAC,mBAAmB,CAAC;QAC1C,MAAM,QAAe;YACnB,SAAS,IAAI,CAAC,WAAW;YACzB;YACA,eAAe;YACf,WAAW;YACX,UAAU;QACZ;QAEA,2BAA2B;QAC3B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE;YAChC,SAAS,IAAI,CAAC,WAAW;YACzB,QAAQ,OAAO,WAAW;YAC1B;YACA;YACA,WAAW;YACX,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,WAAW;QACb;QAEA,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU;QAC/C,MAAM,UAAU,IAAI,CAAC,WAAW;QAChC,IAAI,CAAC,WAAW;QAEhB,OAAO;IACT;IAEA,kBAAkB;IAClB,MAAM,YAAY,OAAe,EAAiB;QAChD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC;IACtB;IAEA,sBAAsB;IACtB,oBAA+C;QAC7C,OAAO,IAAI,CAAC,cAAc;IAC5B;IAEA,oBAAoB;IACpB,eAA+B;QAC7B,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM;IACzC;IAEA,mCAAmC;IACnC,YAAY,MAAc,EAAuB;QAC/C,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,WAAW,OAAO;IACrD;IAEA,iBAAiB;IACjB,YAAyB;QACvB,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;IACtC;IAEA,qBAAqB;IACrB,SAAS,OAAe,EAAoB;QAC1C,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY;IACrC;IAEA,qBAAqB;IACrB,cAAuB;QACrB,OAAO,IAAI,CAAC,SAAS;IACvB;AACF", "debugId": null}}, {"offset": {"line": 413, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/lib/fmp.ts"], "sourcesContent": ["import axios from 'axios'\nimport { StockData } from '@/types/trading'\n\nconst FMP_BASE_URL = 'https://financialmodelingprep.com/api'\nconst API_KEY = process.env.FMP_API_KEY\n\nexport class FMPAPI {\n  private apiKey: string\n\n  constructor(apiKey?: string) {\n    this.apiKey = apiKey || API_KEY || ''\n    if (!this.apiKey) {\n      throw new Error('FMP API key is required')\n    }\n  }\n\n  // Get real-time stock quote\n  async getStockQuote(symbol: string): Promise<StockData> {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/quote/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      const data = response.data[0]\n      if (!data) {\n        throw new Error(`No data found for symbol ${symbol}`)\n      }\n\n      return {\n        symbol: data.symbol,\n        name: data.name || data.symbol,\n        price: data.price,\n        change: data.change,\n        changePercent: data.changesPercentage,\n        volume: data.volume,\n        marketCap: data.marketCap,\n        pe: data.pe,\n        dividend: undefined // Will be fetched separately if needed\n      }\n    } catch (error) {\n      console.error('Error fetching FMP stock quote:', error)\n      throw new Error(`Failed to fetch quote for ${symbol}`)\n    }\n  }\n\n  // Get company profile\n  async getCompanyProfile(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/profile/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data[0]\n    } catch (error) {\n      console.error('Error fetching company profile:', error)\n      return null\n    }\n  }\n\n  // Get financial ratios\n  async getFinancialRatios(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/ratios/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data[0] // Most recent ratios\n    } catch (error) {\n      console.error('Error fetching financial ratios:', error)\n      return null\n    }\n  }\n\n  // Get key metrics\n  async getKeyMetrics(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/key-metrics/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data[0] // Most recent metrics\n    } catch (error) {\n      console.error('Error fetching key metrics:', error)\n      return null\n    }\n  }\n\n  // Get analyst recommendations\n  async getAnalystRecommendations(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/analyst-stock-recommendations/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error fetching analyst recommendations:', error)\n      return []\n    }\n  }\n\n  // Get earnings calendar\n  async getEarningsCalendar(from?: string, to?: string) {\n    try {\n      const params: any = {\n        apikey: this.apiKey\n      }\n\n      if (from) params.from = from\n      if (to) params.to = to\n\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/earning_calendar`,\n        { params }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error fetching earnings calendar:', error)\n      return []\n    }\n  }\n\n  // Get economic calendar\n  async getEconomicCalendar(from?: string, to?: string) {\n    try {\n      const params: any = {\n        apikey: this.apiKey\n      }\n\n      if (from) params.from = from\n      if (to) params.to = to\n\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/economic_calendar`,\n        { params }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error fetching economic calendar:', error)\n      return []\n    }\n  }\n\n  // Search for stocks\n  async searchStocks(query: string, limit: number = 10) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/search`,\n        {\n          params: {\n            query,\n            limit,\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error searching stocks:', error)\n      return []\n    }\n  }\n\n  // Get sector performance\n  async getSectorPerformance() {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/sector-performance`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error fetching sector performance:', error)\n      return []\n    }\n  }\n\n  // Get market gainers/losers\n  async getMarketMovers(type: 'gainers' | 'losers' | 'actives') {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/stock_market/${type}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error(`Error fetching market ${type}:`, error)\n      return []\n    }\n  }\n\n  // ===== CATALYST DETECTION ENDPOINTS =====\n\n  // Get earnings calendar for catalyst detection\n  async getEarningsCalendar(symbol?: string, days: number = 30) {\n    try {\n      const fromDate = new Date()\n      fromDate.setDate(fromDate.getDate() - days)\n      const toDate = new Date()\n\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/earning_calendar`,\n        {\n          params: {\n            apikey: this.apiKey,\n            from: fromDate.toISOString().split('T')[0],\n            to: toDate.toISOString().split('T')[0],\n            ...(symbol && { symbol: symbol.toUpperCase() })\n          }\n        }\n      )\n\n      return response.data || []\n    } catch (error) {\n      console.error('Error fetching earnings calendar:', error)\n      return []\n    }\n  }\n\n  // Get stock news for catalyst detection\n  async getStockNews(symbol: string, limit: number = 50) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/stock_news`,\n        {\n          params: {\n            apikey: this.apiKey,\n            tickers: symbol.toUpperCase(),\n            limit\n          }\n        }\n      )\n\n      return response.data || []\n    } catch (error) {\n      console.error('Error fetching stock news:', error)\n      return []\n    }\n  }\n\n  // Get analyst recommendations\n  async getAnalystRecommendations(symbol: string, days: number = 30) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/analyst-stock-recommendations/${symbol.toUpperCase()}`,\n        {\n          params: {\n            apikey: this.apiKey,\n            limit: days\n          }\n        }\n      )\n\n      return response.data || []\n    } catch (error) {\n      console.error('Error fetching analyst recommendations:', error)\n      return []\n    }\n  }\n\n  // Get insider trading data\n  async getInsiderTrading(symbol: string, days: number = 30) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v4/insider-trading`,\n        {\n          params: {\n            apikey: this.apiKey,\n            symbol: symbol.toUpperCase(),\n            limit: days * 5 // Approximate multiple to get enough data\n          }\n        }\n      )\n\n      // Filter to last N days\n      const cutoffDate = new Date()\n      cutoffDate.setDate(cutoffDate.getDate() - days)\n\n      return (response.data || []).filter((trade: any) =>\n        new Date(trade.filingDate) >= cutoffDate\n      )\n    } catch (error) {\n      console.error('Error fetching insider trading:', error)\n      return []\n    }\n  }\n\n  // Get SEC filings\n  async getSECFilings(symbol: string, days: number = 30) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/sec_filings/${symbol.toUpperCase()}`,\n        {\n          params: {\n            apikey: this.apiKey,\n            limit: days * 2 // Get more filings to filter by date\n          }\n        }\n      )\n\n      // Filter to last N days\n      const cutoffDate = new Date()\n      cutoffDate.setDate(cutoffDate.getDate() - days)\n\n      return (response.data || []).filter((filing: any) =>\n        new Date(filing.filedDate) >= cutoffDate\n      )\n    } catch (error) {\n      console.error('Error fetching SEC filings:', error)\n      return []\n    }\n  }\n\n  // Get pre-market quotes for gap scanning\n  async getPreMarketQuote(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/quote/${symbol.toUpperCase()}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      const data = response.data[0]\n      if (!data) return null\n\n      return {\n        symbol: data.symbol,\n        price: data.price,\n        previousClose: data.previousClose,\n        change: data.change,\n        changePercent: data.changesPercentage,\n        volume: data.volume,\n        marketCap: data.marketCap,\n        avgVolume: data.avgVolume,\n        // Pre-market specific data (if available)\n        preMarketPrice: data.preMarketPrice || data.price,\n        preMarketChange: data.preMarketChange || data.change,\n        preMarketChangePercent: data.preMarketChangePercent || data.changesPercentage\n      }\n    } catch (error) {\n      console.error('Error fetching pre-market quote:', error)\n      return null\n    }\n  }\n\n  // Get multiple pre-market quotes efficiently\n  async getMultiplePreMarketQuotes(symbols: string[]) {\n    try {\n      const symbolsString = symbols.map(s => s.toUpperCase()).join(',')\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/quote/${symbolsString}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return (response.data || []).map((data: any) => ({\n        symbol: data.symbol,\n        price: data.price,\n        previousClose: data.previousClose,\n        change: data.change,\n        changePercent: data.changesPercentage,\n        volume: data.volume,\n        marketCap: data.marketCap,\n        avgVolume: data.avgVolume,\n        preMarketPrice: data.preMarketPrice || data.price,\n        preMarketChange: data.preMarketChange || data.change,\n        preMarketChangePercent: data.preMarketChangePercent || data.changesPercentage\n      }))\n    } catch (error) {\n      console.error('Error fetching multiple pre-market quotes:', error)\n      return []\n    }\n  }\n\n  // Get company profile for additional context\n  async getCompanyProfile(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/profile/${symbol.toUpperCase()}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data[0] || null\n    } catch (error) {\n      console.error('Error fetching company profile:', error)\n      return null\n    }\n  }\n}\n\n// Create a singleton instance\nexport const fmpAPI = new FMPAPI()\n"], "names": [], "mappings": ";;;;;;AAAA;;AAGA,MAAM,eAAe;AACrB,MAAM,UAAU,QAAQ,GAAG,CAAC,WAAW;AAEhC,MAAM;IACH,OAAc;IAEtB,YAAY,MAAe,CAAE;QAC3B,IAAI,CAAC,MAAM,GAAG,UAAU,WAAW;QACnC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,4BAA4B;IAC5B,MAAM,cAAc,MAAc,EAAsB;QACtD,IAAI;YACF,MAAM,WAAW,MAAM,2KAAK,CAAC,GAAG,CAC9B,GAAG,aAAa,UAAU,EAAE,QAAQ,EACpC;gBACE,QAAQ;oBACN,QAAQ,IAAI,CAAC,MAAM;gBACrB;YACF;YAGF,MAAM,OAAO,SAAS,IAAI,CAAC,EAAE;YAC7B,IAAI,CAAC,MAAM;gBACT,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,QAAQ;YACtD;YAEA,OAAO;gBACL,QAAQ,KAAK,MAAM;gBACnB,MAAM,KAAK,IAAI,IAAI,KAAK,MAAM;gBAC9B,OAAO,KAAK,KAAK;gBACjB,QAAQ,KAAK,MAAM;gBACnB,eAAe,KAAK,iBAAiB;gBACrC,QAAQ,KAAK,MAAM;gBACnB,WAAW,KAAK,SAAS;gBACzB,IAAI,KAAK,EAAE;gBACX,UAAU,UAAU,uCAAuC;YAC7D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,QAAQ;QACvD;IACF;IAEA,sBAAsB;IACtB,MAAM,kBAAkB,MAAc,EAAE;QACtC,IAAI;YACF,MAAM,WAAW,MAAM,2KAAK,CAAC,GAAG,CAC9B,GAAG,aAAa,YAAY,EAAE,QAAQ,EACtC;gBACE,QAAQ;oBACN,QAAQ,IAAI,CAAC,MAAM;gBACrB;YACF;YAGF,OAAO,SAAS,IAAI,CAAC,EAAE;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO;QACT;IACF;IAEA,uBAAuB;IACvB,MAAM,mBAAmB,MAAc,EAAE;QACvC,IAAI;YACF,MAAM,WAAW,MAAM,2KAAK,CAAC,GAAG,CAC9B,GAAG,aAAa,WAAW,EAAE,QAAQ,EACrC;gBACE,QAAQ;oBACN,QAAQ,IAAI,CAAC,MAAM;gBACrB;YACF;YAGF,OAAO,SAAS,IAAI,CAAC,EAAE,CAAC,qBAAqB;;QAC/C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;QACT;IACF;IAEA,kBAAkB;IAClB,MAAM,cAAc,MAAc,EAAE;QAClC,IAAI;YACF,MAAM,WAAW,MAAM,2KAAK,CAAC,GAAG,CAC9B,GAAG,aAAa,gBAAgB,EAAE,QAAQ,EAC1C;gBACE,QAAQ;oBACN,QAAQ,IAAI,CAAC,MAAM;gBACrB;YACF;YAGF,OAAO,SAAS,IAAI,CAAC,EAAE,CAAC,sBAAsB;;QAChD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO;QACT;IACF;IAEA,8BAA8B;IAC9B,MAAM,0BAA0B,MAAc,EAAE;QAC9C,IAAI;YACF,MAAM,WAAW,MAAM,2KAAK,CAAC,GAAG,CAC9B,GAAG,aAAa,kCAAkC,EAAE,QAAQ,EAC5D;gBACE,QAAQ;oBACN,QAAQ,IAAI,CAAC,MAAM;gBACrB;YACF;YAGF,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YACzD,OAAO,EAAE;QACX;IACF;IAEA,wBAAwB;IACxB,MAAM,oBAAoB,IAAa,EAAE,EAAW,EAAE;QACpD,IAAI;YACF,MAAM,SAAc;gBAClB,QAAQ,IAAI,CAAC,MAAM;YACrB;YAEA,IAAI,MAAM,OAAO,IAAI,GAAG;YACxB,IAAI,IAAI,OAAO,EAAE,GAAG;YAEpB,MAAM,WAAW,MAAM,2KAAK,CAAC,GAAG,CAC9B,GAAG,aAAa,oBAAoB,CAAC,EACrC;gBAAE;YAAO;YAGX,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO,EAAE;QACX;IACF;IAEA,wBAAwB;IACxB,MAAM,oBAAoB,IAAa,EAAE,EAAW,EAAE;QACpD,IAAI;YACF,MAAM,SAAc;gBAClB,QAAQ,IAAI,CAAC,MAAM;YACrB;YAEA,IAAI,MAAM,OAAO,IAAI,GAAG;YACxB,IAAI,IAAI,OAAO,EAAE,GAAG;YAEpB,MAAM,WAAW,MAAM,2KAAK,CAAC,GAAG,CAC9B,GAAG,aAAa,qBAAqB,CAAC,EACtC;gBAAE;YAAO;YAGX,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO,EAAE;QACX;IACF;IAEA,oBAAoB;IACpB,MAAM,aAAa,KAAa,EAAE,QAAgB,EAAE,EAAE;QACpD,IAAI;YACF,MAAM,WAAW,MAAM,2KAAK,CAAC,GAAG,CAC9B,GAAG,aAAa,UAAU,CAAC,EAC3B;gBACE,QAAQ;oBACN;oBACA;oBACA,QAAQ,IAAI,CAAC,MAAM;gBACrB;YACF;YAGF,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,EAAE;QACX;IACF;IAEA,yBAAyB;IACzB,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,2KAAK,CAAC,GAAG,CAC9B,GAAG,aAAa,sBAAsB,CAAC,EACvC;gBACE,QAAQ;oBACN,QAAQ,IAAI,CAAC,MAAM;gBACrB;YACF;YAGF,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,OAAO,EAAE;QACX;IACF;IAEA,4BAA4B;IAC5B,MAAM,gBAAgB,IAAsC,EAAE;QAC5D,IAAI;YACF,MAAM,WAAW,MAAM,2KAAK,CAAC,GAAG,CAC9B,GAAG,aAAa,iBAAiB,EAAE,MAAM,EACzC;gBACE,QAAQ;oBACN,QAAQ,IAAI,CAAC,MAAM;gBACrB;YACF;YAGF,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC,EAAE;YAChD,OAAO,EAAE;QACX;IACF;IAEA,2CAA2C;IAE3C,+CAA+C;IAC/C,MAAM,oBAAoB,MAAe,EAAE,OAAe,EAAE,EAAE;QAC5D,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;YACtC,MAAM,SAAS,IAAI;YAEnB,MAAM,WAAW,MAAM,2KAAK,CAAC,GAAG,CAC9B,GAAG,aAAa,oBAAoB,CAAC,EACrC;gBACE,QAAQ;oBACN,QAAQ,IAAI,CAAC,MAAM;oBACnB,MAAM,SAAS,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oBAC1C,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oBACtC,GAAI,UAAU;wBAAE,QAAQ,OAAO,WAAW;oBAAG,CAAC;gBAChD;YACF;YAGF,OAAO,SAAS,IAAI,IAAI,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO,EAAE;QACX;IACF;IAEA,wCAAwC;IACxC,MAAM,aAAa,MAAc,EAAE,QAAgB,EAAE,EAAE;QACrD,IAAI;YACF,MAAM,WAAW,MAAM,2KAAK,CAAC,GAAG,CAC9B,GAAG,aAAa,cAAc,CAAC,EAC/B;gBACE,QAAQ;oBACN,QAAQ,IAAI,CAAC,MAAM;oBACnB,SAAS,OAAO,WAAW;oBAC3B;gBACF;YACF;YAGF,OAAO,SAAS,IAAI,IAAI,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO,EAAE;QACX;IACF;IAEA,8BAA8B;IAC9B,MAAM,0BAA0B,MAAc,EAAE,OAAe,EAAE,EAAE;QACjE,IAAI;YACF,MAAM,WAAW,MAAM,2KAAK,CAAC,GAAG,CAC9B,GAAG,aAAa,kCAAkC,EAAE,OAAO,WAAW,IAAI,EAC1E;gBACE,QAAQ;oBACN,QAAQ,IAAI,CAAC,MAAM;oBACnB,OAAO;gBACT;YACF;YAGF,OAAO,SAAS,IAAI,IAAI,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YACzD,OAAO,EAAE;QACX;IACF;IAEA,2BAA2B;IAC3B,MAAM,kBAAkB,MAAc,EAAE,OAAe,EAAE,EAAE;QACzD,IAAI;YACF,MAAM,WAAW,MAAM,2KAAK,CAAC,GAAG,CAC9B,GAAG,aAAa,mBAAmB,CAAC,EACpC;gBACE,QAAQ;oBACN,QAAQ,IAAI,CAAC,MAAM;oBACnB,QAAQ,OAAO,WAAW;oBAC1B,OAAO,OAAO,EAAE,0CAA0C;gBAC5D;YACF;YAGF,wBAAwB;YACxB,MAAM,aAAa,IAAI;YACvB,WAAW,OAAO,CAAC,WAAW,OAAO,KAAK;YAE1C,OAAO,CAAC,SAAS,IAAI,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC,QACnC,IAAI,KAAK,MAAM,UAAU,KAAK;QAElC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO,EAAE;QACX;IACF;IAEA,kBAAkB;IAClB,MAAM,cAAc,MAAc,EAAE,OAAe,EAAE,EAAE;QACrD,IAAI;YACF,MAAM,WAAW,MAAM,2KAAK,CAAC,GAAG,CAC9B,GAAG,aAAa,gBAAgB,EAAE,OAAO,WAAW,IAAI,EACxD;gBACE,QAAQ;oBACN,QAAQ,IAAI,CAAC,MAAM;oBACnB,OAAO,OAAO,EAAE,qCAAqC;gBACvD;YACF;YAGF,wBAAwB;YACxB,MAAM,aAAa,IAAI;YACvB,WAAW,OAAO,CAAC,WAAW,OAAO,KAAK;YAE1C,OAAO,CAAC,SAAS,IAAI,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC,SACnC,IAAI,KAAK,OAAO,SAAS,KAAK;QAElC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO,EAAE;QACX;IACF;IAEA,yCAAyC;IACzC,MAAM,kBAAkB,MAAc,EAAE;QACtC,IAAI;YACF,MAAM,WAAW,MAAM,2KAAK,CAAC,GAAG,CAC9B,GAAG,aAAa,UAAU,EAAE,OAAO,WAAW,IAAI,EAClD;gBACE,QAAQ;oBACN,QAAQ,IAAI,CAAC,MAAM;gBACrB;YACF;YAGF,MAAM,OAAO,SAAS,IAAI,CAAC,EAAE;YAC7B,IAAI,CAAC,MAAM,OAAO;YAElB,OAAO;gBACL,QAAQ,KAAK,MAAM;gBACnB,OAAO,KAAK,KAAK;gBACjB,eAAe,KAAK,aAAa;gBACjC,QAAQ,KAAK,MAAM;gBACnB,eAAe,KAAK,iBAAiB;gBACrC,QAAQ,KAAK,MAAM;gBACnB,WAAW,KAAK,SAAS;gBACzB,WAAW,KAAK,SAAS;gBACzB,0CAA0C;gBAC1C,gBAAgB,KAAK,cAAc,IAAI,KAAK,KAAK;gBACjD,iBAAiB,KAAK,eAAe,IAAI,KAAK,MAAM;gBACpD,wBAAwB,KAAK,sBAAsB,IAAI,KAAK,iBAAiB;YAC/E;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;QACT;IACF;IAEA,6CAA6C;IAC7C,MAAM,2BAA2B,OAAiB,EAAE;QAClD,IAAI;YACF,MAAM,gBAAgB,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,WAAW,IAAI,IAAI,CAAC;YAC7D,MAAM,WAAW,MAAM,2KAAK,CAAC,GAAG,CAC9B,GAAG,aAAa,UAAU,EAAE,eAAe,EAC3C;gBACE,QAAQ;oBACN,QAAQ,IAAI,CAAC,MAAM;gBACrB;YACF;YAGF,OAAO,CAAC,SAAS,IAAI,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,OAAc,CAAC;oBAC/C,QAAQ,KAAK,MAAM;oBACnB,OAAO,KAAK,KAAK;oBACjB,eAAe,KAAK,aAAa;oBACjC,QAAQ,KAAK,MAAM;oBACnB,eAAe,KAAK,iBAAiB;oBACrC,QAAQ,KAAK,MAAM;oBACnB,WAAW,KAAK,SAAS;oBACzB,WAAW,KAAK,SAAS;oBACzB,gBAAgB,KAAK,cAAc,IAAI,KAAK,KAAK;oBACjD,iBAAiB,KAAK,eAAe,IAAI,KAAK,MAAM;oBACpD,wBAAwB,KAAK,sBAAsB,IAAI,KAAK,iBAAiB;gBAC/E,CAAC;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,OAAO,EAAE;QACX;IACF;IAEA,6CAA6C;IAC7C,MAAM,kBAAkB,MAAc,EAAE;QACtC,IAAI;YACF,MAAM,WAAW,MAAM,2KAAK,CAAC,GAAG,CAC9B,GAAG,aAAa,YAAY,EAAE,OAAO,WAAW,IAAI,EACpD;gBACE,QAAQ;oBACN,QAAQ,IAAI,CAAC,MAAM;gBACrB;YACF;YAGF,OAAO,SAAS,IAAI,CAAC,EAAE,IAAI;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO;QACT;IACF;AACF;AAGO,MAAM,SAAS,IAAI", "debugId": null}}, {"offset": {"line": 762, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/app/api/data-source/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { IBKRAPI } from '@/lib/ibkr'\nimport { FMPAPI } from '@/lib/fmp'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const action = searchParams.get('action')\n\n    switch (action) {\n      case 'check_ibkr':\n        return await checkIBKRConnection()\n      \n      case 'check_fmp':\n        return await checkFMPConnection()\n      \n      case 'status':\n        return await getDataSourceStatus()\n      \n      default:\n        return NextResponse.json(\n          { success: false, error: 'Invalid action' },\n          { status: 400 }\n        )\n    }\n  } catch (error) {\n    console.error('Error in data source API:', error)\n    return NextResponse.json(\n      { \n        success: false, \n        error: 'Failed to check data sources',\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    )\n  }\n}\n\nasync function checkIBKRConnection() {\n  try {\n    const ibkrAPI = new IBKRAPI({\n      host: '127.0.0.1',\n      port: 4002, // IB Gateway paper trading port\n      clientId: 1,\n      paperTrading: true\n    })\n\n    const isConnected = await ibkrAPI.connect()\n    \n    if (isConnected) {\n      // Test with a simple quote request\n      const testQuote = await ibkrAPI.getMarketData('SPY')\n      \n      return NextResponse.json({\n        success: true,\n        data: {\n          connected: true,\n          source: 'IBKR',\n          testQuote: testQuote ? 'Success' : 'Failed',\n          message: 'IBKR connection successful'\n        }\n      })\n    } else {\n      return NextResponse.json({\n        success: false,\n        data: {\n          connected: false,\n          source: 'IBKR',\n          message: 'IBKR connection failed - ensure TWS/IB Gateway is running'\n        }\n      })\n    }\n  } catch (error) {\n    return NextResponse.json({\n      success: false,\n      data: {\n        connected: false,\n        source: 'IBKR',\n        error: error instanceof Error ? error.message : 'Unknown error',\n        message: 'IBKR connection error'\n      }\n    })\n  }\n}\n\nasync function checkFMPConnection() {\n  try {\n    const fmpAPI = new FMPAPI(process.env.FMP_API_KEY)\n    \n    // Test with a simple quote request\n    const testQuote = await fmpAPI.getStockQuote('SPY')\n    \n    return NextResponse.json({\n      success: true,\n      data: {\n        connected: true,\n        source: 'FMP',\n        testQuote: testQuote ? 'Success' : 'Failed',\n        message: 'FMP API connection successful'\n      }\n    })\n  } catch (error) {\n    const isRateLimit = error instanceof Error && error.message.includes('429')\n    \n    return NextResponse.json({\n      success: false,\n      data: {\n        connected: false,\n        source: 'FMP',\n        error: error instanceof Error ? error.message : 'Unknown error',\n        isRateLimit,\n        message: isRateLimit ? 'FMP API rate limit exceeded' : 'FMP API connection error'\n      }\n    })\n  }\n}\n\nasync function getDataSourceStatus() {\n  try {\n    const [ibkrResult, fmpResult] = await Promise.all([\n      checkIBKRConnection(),\n      checkFMPConnection()\n    ])\n\n    const ibkrData = await ibkrResult.json()\n    const fmpData = await fmpResult.json()\n\n    const recommendedSource = ibkrData.success ? 'IBKR' : \n                             fmpData.success ? 'FMP' : 'NONE'\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        ibkr: ibkrData.data,\n        fmp: fmpData.data,\n        recommended: recommendedSource,\n        timestamp: new Date().toISOString()\n      }\n    })\n  } catch (error) {\n    return NextResponse.json({\n      success: false,\n      error: 'Failed to check data source status',\n      details: error instanceof Error ? error.message : 'Unknown error'\n    })\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const { action, config } = body\n\n    switch (action) {\n      case 'set_preferred_source':\n        // Store preferred data source in environment or database\n        // For now, just return success\n        return NextResponse.json({\n          success: true,\n          data: {\n            preferredSource: config.source,\n            message: `Preferred data source set to ${config.source}`\n          }\n        })\n\n      case 'test_connection':\n        if (config.source === 'IBKR') {\n          return await checkIBKRConnection()\n        } else if (config.source === 'FMP') {\n          return await checkFMPConnection()\n        } else {\n          return NextResponse.json(\n            { success: false, error: 'Invalid source specified' },\n            { status: 400 }\n          )\n        }\n\n      default:\n        return NextResponse.json(\n          { success: false, error: 'Invalid action' },\n          { status: 400 }\n        )\n    }\n  } catch (error) {\n    console.error('Error in data source POST API:', error)\n    return NextResponse.json(\n      { \n        success: false, \n        error: 'Failed to process data source request',\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC;QAEhC,OAAQ;YACN,KAAK;gBACH,OAAO,MAAM;YAEf,KAAK;gBACH,OAAO,MAAM;YAEf,KAAK;gBACH,OAAO,MAAM;YAEf;gBACE,OAAO,yKAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,OAAO;gBAAiB,GAC1C;oBAAE,QAAQ;gBAAI;QAEpB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,yKAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,eAAe;IACb,IAAI;QACF,MAAM,UAAU,IAAI,wJAAO,CAAC;YAC1B,MAAM;YACN,MAAM;YACN,UAAU;YACV,cAAc;QAChB;QAEA,MAAM,cAAc,MAAM,QAAQ,OAAO;QAEzC,IAAI,aAAa;YACf,mCAAmC;YACnC,MAAM,YAAY,MAAM,QAAQ,aAAa,CAAC;YAE9C,OAAO,yKAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;oBACJ,WAAW;oBACX,QAAQ;oBACR,WAAW,YAAY,YAAY;oBACnC,SAAS;gBACX;YACF;QACF,OAAO;YACL,OAAO,yKAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;oBACJ,WAAW;oBACX,QAAQ;oBACR,SAAS;gBACX;YACF;QACF;IACF,EAAE,OAAO,OAAO;QACd,OAAO,yKAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,WAAW;gBACX,QAAQ;gBACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD,SAAS;YACX;QACF;IACF;AACF;AAEA,eAAe;IACb,IAAI;QACF,MAAM,SAAS,IAAI,sJAAM,CAAC,QAAQ,GAAG,CAAC,WAAW;QAEjD,mCAAmC;QACnC,MAAM,YAAY,MAAM,OAAO,aAAa,CAAC;QAE7C,OAAO,yKAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,WAAW;gBACX,QAAQ;gBACR,WAAW,YAAY,YAAY;gBACnC,SAAS;YACX;QACF;IACF,EAAE,OAAO,OAAO;QACd,MAAM,cAAc,iBAAiB,SAAS,MAAM,OAAO,CAAC,QAAQ,CAAC;QAErE,OAAO,yKAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,WAAW;gBACX,QAAQ;gBACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD;gBACA,SAAS,cAAc,gCAAgC;YACzD;QACF;IACF;AACF;AAEA,eAAe;IACb,IAAI;QACF,MAAM,CAAC,YAAY,UAAU,GAAG,MAAM,QAAQ,GAAG,CAAC;YAChD;YACA;SACD;QAED,MAAM,WAAW,MAAM,WAAW,IAAI;QACtC,MAAM,UAAU,MAAM,UAAU,IAAI;QAEpC,MAAM,oBAAoB,SAAS,OAAO,GAAG,SACpB,QAAQ,OAAO,GAAG,QAAQ;QAEnD,OAAO,yKAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,MAAM,SAAS,IAAI;gBACnB,KAAK,QAAQ,IAAI;gBACjB,aAAa;gBACb,WAAW,IAAI,OAAO,WAAW;YACnC;QACF;IACF,EAAE,OAAO,OAAO;QACd,OAAO,yKAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD;IACF;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG;QAE3B,OAAQ;YACN,KAAK;gBACH,yDAAyD;gBACzD,+BAA+B;gBAC/B,OAAO,yKAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;wBACJ,iBAAiB,OAAO,MAAM;wBAC9B,SAAS,CAAC,6BAA6B,EAAE,OAAO,MAAM,EAAE;oBAC1D;gBACF;YAEF,KAAK;gBACH,IAAI,OAAO,MAAM,KAAK,QAAQ;oBAC5B,OAAO,MAAM;gBACf,OAAO,IAAI,OAAO,MAAM,KAAK,OAAO;oBAClC,OAAO,MAAM;gBACf,OAAO;oBACL,OAAO,yKAAY,CAAC,IAAI,CACtB;wBAAE,SAAS;wBAAO,OAAO;oBAA2B,GACpD;wBAAE,QAAQ;oBAAI;gBAElB;YAEF;gBACE,OAAO,yKAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,OAAO;gBAAiB,GAC1C;oBAAE,QAAQ;gBAAI;QAEpB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO,yKAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}