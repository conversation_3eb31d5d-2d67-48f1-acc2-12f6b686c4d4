{"version": 3, "sources": ["turbopack:///[project]/swing-trader-ai/src/lib/polygon.ts", "turbopack:///[project]/swing-trader-ai/src/lib/fmp.ts", "turbopack:///[project]/swing-trader-ai/src/lib/indicators.ts", "turbopack:///[project]/swing-trader-ai/src/data/watchlist.ts"], "sourcesContent": ["import axios from 'axios'\nimport { CandlestickData, StockData } from '@/types/trading'\n\nconst POLYGON_BASE_URL = 'https://api.polygon.io'\nconst API_KEY = process.env.POLYGON_API_KEY\n\nexport class PolygonAPI {\n  private apiKey: string\n\n  constructor(apiKey?: string) {\n    this.apiKey = apiKey || API_KEY || ''\n    if (!this.apiKey) {\n      throw new Error('Polygon API key is required')\n    }\n  }\n\n  // Get current stock quote using Polygon's snapshot endpoint (better for paid plans)\n  async getStockQuote(symbol: string): Promise<StockData> {\n    try {\n      // Use snapshot endpoint for real-time data (available on paid plans)\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v2/snapshot/locale/us/markets/stocks/tickers/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      const data = response.data.results[0]\n      if (!data) {\n        throw new Error(`No data found for ${symbol}`)\n      }\n\n      const ticker = data.value || data\n      const dayData = ticker.day || {}\n      const prevDayData = ticker.prevDay || {}\n      const lastQuote = ticker.lastQuote || {}\n      const lastTrade = ticker.lastTrade || {}\n\n      // Use the most recent price available\n      const currentPrice = lastTrade.p || dayData.c || prevDayData.c\n      const prevClose = prevDayData.c || dayData.o\n      const change = currentPrice - prevClose\n      const changePercent = (change / prevClose) * 100\n\n      return {\n        symbol: symbol.toUpperCase(),\n        name: ticker.name || symbol.toUpperCase(),\n        price: currentPrice,\n        change,\n        changePercent,\n        volume: dayData.v || 0,\n        marketCap: ticker.market_cap,\n        pe: undefined,\n        dividend: undefined\n      }\n    } catch (error) {\n      console.error('Error fetching stock quote from Polygon:', error)\n\n      // Fallback to previous day data if snapshot fails\n      try {\n        const fallbackResponse = await axios.get(\n          `${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/prev`,\n          {\n            params: {\n              adjusted: 'true',\n              apikey: this.apiKey\n            }\n          }\n        )\n\n        const data = fallbackResponse.data.results[0]\n        return {\n          symbol: symbol.toUpperCase(),\n          name: symbol.toUpperCase(),\n          price: data.c,\n          change: data.c - data.o,\n          changePercent: ((data.c - data.o) / data.o) * 100,\n          volume: data.v,\n          marketCap: undefined,\n          pe: undefined,\n          dividend: undefined\n        }\n      } catch (fallbackError) {\n        console.error('Polygon fallback also failed:', fallbackError)\n        throw new Error(`Failed to fetch quote for ${symbol}`)\n      }\n    }\n  }\n\n  // Get historical candlestick data (optimized for paid plans)\n  async getHistoricalData(\n    symbol: string,\n    timespan: 'minute' | 'hour' | 'day' | 'week' | 'month' = 'day',\n    multiplier: number = 1,\n    from: string,\n    to: string\n  ): Promise<CandlestickData[]> {\n    try {\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/range/${multiplier}/${timespan}/${from}/${to}`,\n        {\n          params: {\n            adjusted: 'true',\n            sort: 'asc',\n            limit: 50000, // Higher limit for paid plans\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      if (!response.data.results || response.data.results.length === 0) {\n        console.warn(`No historical data found for ${symbol}`)\n        return []\n      }\n\n      return response.data.results.map((candle: any) => ({\n        timestamp: candle.t,\n        open: candle.o,\n        high: candle.h,\n        low: candle.l,\n        close: candle.c,\n        volume: candle.v\n      }))\n    } catch (error) {\n      console.error(`Error fetching historical data for ${symbol}:`, error)\n\n      // Log the specific error for debugging\n      if (error.response) {\n        console.error(`Polygon API Error: ${error.response.status} - ${error.response.statusText}`)\n        console.error('Response data:', error.response.data)\n      }\n\n      throw new Error(`Failed to fetch historical data for ${symbol}: ${error.message}`)\n    }\n  }\n\n  // Get company details\n  async getCompanyDetails(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v3/reference/tickers/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data.results\n    } catch (error) {\n      console.error('Error fetching company details:', error)\n      return null\n    }\n  }\n\n  // Get market status\n  async getMarketStatus() {\n    try {\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v1/marketstatus/now`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error fetching market status:', error)\n      return null\n    }\n  }\n\n  // Search for stocks\n  async searchStocks(query: string, limit: number = 10) {\n    try {\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v3/reference/tickers`,\n        {\n          params: {\n            search: query,\n            market: 'stocks',\n            active: 'true',\n            limit,\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data.results || []\n    } catch (error) {\n      console.error('Error searching stocks:', error)\n      return []\n    }\n  }\n}\n\n// Create a singleton instance\nexport const polygonAPI = new PolygonAPI()\n", "import axios from 'axios'\nimport { StockData } from '@/types/trading'\n\nconst FMP_BASE_URL = 'https://financialmodelingprep.com/api'\nconst API_KEY = process.env.FMP_API_KEY\n\nexport class FMPAPI {\n  private apiKey: string\n\n  constructor(apiKey?: string) {\n    this.apiKey = apiKey || API_KEY || ''\n    if (!this.apiKey) {\n      throw new Error('FMP API key is required')\n    }\n  }\n\n  // Get real-time stock quote\n  async getStockQuote(symbol: string): Promise<StockData> {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/quote/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      const data = response.data[0]\n      if (!data) {\n        throw new Error(`No data found for symbol ${symbol}`)\n      }\n\n      return {\n        symbol: data.symbol,\n        name: data.name || data.symbol,\n        price: data.price,\n        change: data.change,\n        changePercent: data.changesPercentage,\n        volume: data.volume,\n        marketCap: data.marketCap,\n        pe: data.pe,\n        dividend: undefined // Will be fetched separately if needed\n      }\n    } catch (error) {\n      console.error('Error fetching FMP stock quote:', error)\n      throw new Error(`Failed to fetch quote for ${symbol}`)\n    }\n  }\n\n  // Get company profile\n  async getCompanyProfile(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/profile/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data[0]\n    } catch (error) {\n      console.error('Error fetching company profile:', error)\n      return null\n    }\n  }\n\n  // Get financial ratios\n  async getFinancialRatios(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/ratios/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data[0] // Most recent ratios\n    } catch (error) {\n      console.error('Error fetching financial ratios:', error)\n      return null\n    }\n  }\n\n  // Get key metrics\n  async getKeyMetrics(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/key-metrics/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data[0] // Most recent metrics\n    } catch (error) {\n      console.error('Error fetching key metrics:', error)\n      return null\n    }\n  }\n\n  // Get analyst recommendations\n  async getAnalystRecommendations(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/analyst-stock-recommendations/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error fetching analyst recommendations:', error)\n      return []\n    }\n  }\n\n  // Get earnings calendar\n  async getEarningsCalendar(from?: string, to?: string) {\n    try {\n      const params: any = {\n        apikey: this.apiKey\n      }\n\n      if (from) params.from = from\n      if (to) params.to = to\n\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/earning_calendar`,\n        { params }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error fetching earnings calendar:', error)\n      return []\n    }\n  }\n\n  // Get economic calendar\n  async getEconomicCalendar(from?: string, to?: string) {\n    try {\n      const params: any = {\n        apikey: this.apiKey\n      }\n\n      if (from) params.from = from\n      if (to) params.to = to\n\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/economic_calendar`,\n        { params }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error fetching economic calendar:', error)\n      return []\n    }\n  }\n\n  // Search for stocks\n  async searchStocks(query: string, limit: number = 10) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/search`,\n        {\n          params: {\n            query,\n            limit,\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error searching stocks:', error)\n      return []\n    }\n  }\n\n  // Get sector performance\n  async getSectorPerformance() {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/sector-performance`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error fetching sector performance:', error)\n      return []\n    }\n  }\n\n  // Get market gainers/losers\n  async getMarketMovers(type: 'gainers' | 'losers' | 'actives') {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/stock_market/${type}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error(`Error fetching market ${type}:`, error)\n      return []\n    }\n  }\n\n  // ===== CATALYST DETECTION ENDPOINTS =====\n\n  // Get earnings calendar for catalyst detection\n  async getEarningsCalendar(symbol?: string, days: number = 30) {\n    try {\n      const fromDate = new Date()\n      fromDate.setDate(fromDate.getDate() - days)\n      const toDate = new Date()\n\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/earning_calendar`,\n        {\n          params: {\n            apikey: this.apiKey,\n            from: fromDate.toISOString().split('T')[0],\n            to: toDate.toISOString().split('T')[0],\n            ...(symbol && { symbol: symbol.toUpperCase() })\n          }\n        }\n      )\n\n      return response.data || []\n    } catch (error) {\n      console.error('Error fetching earnings calendar:', error)\n      return []\n    }\n  }\n\n  // Get stock news for catalyst detection\n  async getStockNews(symbol: string, limit: number = 50) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/stock_news`,\n        {\n          params: {\n            apikey: this.apiKey,\n            tickers: symbol.toUpperCase(),\n            limit\n          }\n        }\n      )\n\n      return response.data || []\n    } catch (error) {\n      console.error('Error fetching stock news:', error)\n      return []\n    }\n  }\n\n  // Get analyst recommendations\n  async getAnalystRecommendations(symbol: string, days: number = 30) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/analyst-stock-recommendations/${symbol.toUpperCase()}`,\n        {\n          params: {\n            apikey: this.apiKey,\n            limit: days\n          }\n        }\n      )\n\n      return response.data || []\n    } catch (error) {\n      console.error('Error fetching analyst recommendations:', error)\n      return []\n    }\n  }\n\n  // Get insider trading data\n  async getInsiderTrading(symbol: string, days: number = 30) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v4/insider-trading`,\n        {\n          params: {\n            apikey: this.apiKey,\n            symbol: symbol.toUpperCase(),\n            limit: days * 5 // Approximate multiple to get enough data\n          }\n        }\n      )\n\n      // Filter to last N days\n      const cutoffDate = new Date()\n      cutoffDate.setDate(cutoffDate.getDate() - days)\n\n      return (response.data || []).filter((trade: any) =>\n        new Date(trade.filingDate) >= cutoffDate\n      )\n    } catch (error) {\n      console.error('Error fetching insider trading:', error)\n      return []\n    }\n  }\n\n  // Get SEC filings\n  async getSECFilings(symbol: string, days: number = 30) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/sec_filings/${symbol.toUpperCase()}`,\n        {\n          params: {\n            apikey: this.apiKey,\n            limit: days * 2 // Get more filings to filter by date\n          }\n        }\n      )\n\n      // Filter to last N days\n      const cutoffDate = new Date()\n      cutoffDate.setDate(cutoffDate.getDate() - days)\n\n      return (response.data || []).filter((filing: any) =>\n        new Date(filing.filedDate) >= cutoffDate\n      )\n    } catch (error) {\n      console.error('Error fetching SEC filings:', error)\n      return []\n    }\n  }\n\n  // Get pre-market quotes for gap scanning\n  async getPreMarketQuote(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/quote/${symbol.toUpperCase()}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      const data = response.data[0]\n      if (!data) return null\n\n      return {\n        symbol: data.symbol,\n        price: data.price,\n        previousClose: data.previousClose,\n        change: data.change,\n        changePercent: data.changesPercentage,\n        volume: data.volume,\n        marketCap: data.marketCap,\n        avgVolume: data.avgVolume,\n        // Pre-market specific data (if available)\n        preMarketPrice: data.preMarketPrice || data.price,\n        preMarketChange: data.preMarketChange || data.change,\n        preMarketChangePercent: data.preMarketChangePercent || data.changesPercentage\n      }\n    } catch (error) {\n      console.error('Error fetching pre-market quote:', error)\n      return null\n    }\n  }\n\n  // Get multiple pre-market quotes efficiently\n  async getMultiplePreMarketQuotes(symbols: string[]) {\n    try {\n      const symbolsString = symbols.map(s => s.toUpperCase()).join(',')\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/quote/${symbolsString}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return (response.data || []).map((data: any) => ({\n        symbol: data.symbol,\n        price: data.price,\n        previousClose: data.previousClose,\n        change: data.change,\n        changePercent: data.changesPercentage,\n        volume: data.volume,\n        marketCap: data.marketCap,\n        avgVolume: data.avgVolume,\n        preMarketPrice: data.preMarketPrice || data.price,\n        preMarketChange: data.preMarketChange || data.change,\n        preMarketChangePercent: data.preMarketChangePercent || data.changesPercentage\n      }))\n    } catch (error) {\n      console.error('Error fetching multiple pre-market quotes:', error)\n      return []\n    }\n  }\n\n  // Get company profile for additional context\n  async getCompanyProfile(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/profile/${symbol.toUpperCase()}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data[0] || null\n    } catch (error) {\n      console.error('Error fetching company profile:', error)\n      return null\n    }\n  }\n}\n\n// Create a singleton instance\nexport const fmpAPI = new FMPAPI()\n", "import { CandlestickData, TechnicalIndicator } from '@/types/trading'\n\nexport class TechnicalIndicators {\n  // Simple Moving Average\n  static sma(data: number[], period: number): number[] {\n    const result: number[] = []\n    for (let i = period - 1; i < data.length; i++) {\n      const sum = data.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0)\n      result.push(sum / period)\n    }\n    return result\n  }\n\n  // Exponential Moving Average\n  static ema(data: number[], period: number): number[] {\n    const result: number[] = []\n    const multiplier = 2 / (period + 1)\n    \n    // Start with SMA for first value\n    let ema = data.slice(0, period).reduce((a, b) => a + b, 0) / period\n    result.push(ema)\n    \n    for (let i = period; i < data.length; i++) {\n      ema = (data[i] * multiplier) + (ema * (1 - multiplier))\n      result.push(ema)\n    }\n    \n    return result\n  }\n\n  // Relative Strength Index\n  static rsi(data: number[], period: number = 14): number[] {\n    const gains: number[] = []\n    const losses: number[] = []\n    \n    for (let i = 1; i < data.length; i++) {\n      const change = data[i] - data[i - 1]\n      gains.push(change > 0 ? change : 0)\n      losses.push(change < 0 ? Math.abs(change) : 0)\n    }\n    \n    const avgGains = this.sma(gains, period)\n    const avgLosses = this.sma(losses, period)\n    \n    return avgGains.map((gain, i) => {\n      const rs = gain / avgLosses[i]\n      return 100 - (100 / (1 + rs))\n    })\n  }\n\n  // MACD (Moving Average Convergence Divergence)\n  static macd(data: number[], fastPeriod: number = 12, slowPeriod: number = 26, signalPeriod: number = 9) {\n    const fastEMA = this.ema(data, fastPeriod)\n    const slowEMA = this.ema(data, slowPeriod)\n    \n    // Align arrays (slowEMA starts later)\n    const startIndex = slowPeriod - fastPeriod\n    const macdLine = fastEMA.slice(startIndex).map((fast, i) => fast - slowEMA[i])\n    \n    const signalLine = this.ema(macdLine, signalPeriod)\n    const histogram = macdLine.slice(signalPeriod - 1).map((macd, i) => macd - signalLine[i])\n    \n    return {\n      macd: macdLine,\n      signal: signalLine,\n      histogram\n    }\n  }\n\n  // Bollinger Bands\n  static bollingerBands(data: number[], period: number = 20, stdDev: number = 2) {\n    const sma = this.sma(data, period)\n    const bands = sma.map((avg, i) => {\n      const slice = data.slice(i, i + period)\n      const variance = slice.reduce((sum, val) => sum + Math.pow(val - avg, 2), 0) / period\n      const standardDeviation = Math.sqrt(variance)\n      \n      return {\n        upper: avg + (standardDeviation * stdDev),\n        middle: avg,\n        lower: avg - (standardDeviation * stdDev)\n      }\n    })\n    \n    return bands\n  }\n\n  // Support and Resistance Levels\n  static findSupportResistance(candles: CandlestickData[], lookback: number = 20): { support: number[], resistance: number[] } {\n    const highs = candles.map(c => c.high)\n    const lows = candles.map(c => c.low)\n    \n    const resistance: number[] = []\n    const support: number[] = []\n    \n    for (let i = lookback; i < candles.length - lookback; i++) {\n      const currentHigh = highs[i]\n      const currentLow = lows[i]\n      \n      // Check if current high is a local maximum\n      const isResistance = highs.slice(i - lookback, i).every(h => h <= currentHigh) &&\n                          highs.slice(i + 1, i + lookback + 1).every(h => h <= currentHigh)\n      \n      // Check if current low is a local minimum\n      const isSupport = lows.slice(i - lookback, i).every(l => l >= currentLow) &&\n                       lows.slice(i + 1, i + lookback + 1).every(l => l >= currentLow)\n      \n      if (isResistance) resistance.push(currentHigh)\n      if (isSupport) support.push(currentLow)\n    }\n    \n    return { support, resistance }\n  }\n\n  // Volume analysis\n  static volumeAnalysis(candles: CandlestickData[], period: number = 20) {\n    const volumes = candles.map(c => c.volume)\n    const avgVolume = this.sma(volumes, period)\n    const currentVolume = volumes[volumes.length - 1]\n    const currentAvgVolume = avgVolume[avgVolume.length - 1]\n\n    return {\n      currentVolume,\n      averageVolume: currentAvgVolume,\n      volumeRatio: currentVolume / currentAvgVolume,\n      isHighVolume: currentVolume > currentAvgVolume * 1.5,\n      isLowVolume: currentVolume < currentAvgVolume * 0.5\n    }\n  }\n\n  // Swing Trading Analysis\n  static analyzeSwingSetup(candles: CandlestickData[]): TechnicalIndicator[] {\n    const closes = candles.map(c => c.close)\n    const indicators: TechnicalIndicator[] = []\n\n    // RSI Analysis\n    const rsi = this.rsi(closes)\n    const currentRSI = rsi[rsi.length - 1]\n\n    let rsiSignal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'\n    let rsiDescription = `RSI: ${currentRSI.toFixed(2)}`\n\n    if (currentRSI < 30) {\n      rsiSignal = 'BUY'\n      rsiDescription += ' - Oversold condition, potential bounce'\n    } else if (currentRSI > 70) {\n      rsiSignal = 'SELL'\n      rsiDescription += ' - Overbought condition, potential pullback'\n    } else {\n      rsiDescription += ' - Neutral zone'\n    }\n\n    indicators.push({\n      name: 'RSI',\n      value: currentRSI,\n      signal: rsiSignal,\n      description: rsiDescription\n    })\n\n    // Moving Average Analysis\n    const sma20 = this.sma(closes, 20)\n    const sma50 = this.sma(closes, 50)\n    const currentPrice = closes[closes.length - 1]\n    const currentSMA20 = sma20[sma20.length - 1]\n    const currentSMA50 = sma50[sma50.length - 1]\n\n    let maSignal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'\n    let maDescription = `Price vs SMA20: ${((currentPrice / currentSMA20 - 1) * 100).toFixed(2)}%`\n\n    if (currentPrice > currentSMA20 && currentSMA20 > currentSMA50) {\n      maSignal = 'BUY'\n      maDescription += ' - Bullish trend'\n    } else if (currentPrice < currentSMA20 && currentSMA20 < currentSMA50) {\n      maSignal = 'SELL'\n      maDescription += ' - Bearish trend'\n    } else {\n      maDescription += ' - Mixed signals'\n    }\n\n    indicators.push({\n      name: 'Moving Averages',\n      value: (currentPrice / currentSMA20 - 1) * 100,\n      signal: maSignal,\n      description: maDescription\n    })\n\n    // MACD Analysis\n    const macdData = this.macd(closes)\n    const currentMACD = macdData.macd[macdData.macd.length - 1]\n    const currentSignal = macdData.signal[macdData.signal.length - 1]\n    const currentHistogram = macdData.histogram[macdData.histogram.length - 1]\n\n    let macdSignal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'\n    let macdDescription = `MACD: ${currentMACD.toFixed(4)}, Signal: ${currentSignal.toFixed(4)}`\n\n    if (currentMACD > currentSignal && currentHistogram > 0) {\n      macdSignal = 'BUY'\n      macdDescription += ' - Bullish momentum'\n    } else if (currentMACD < currentSignal && currentHistogram < 0) {\n      macdSignal = 'SELL'\n      macdDescription += ' - Bearish momentum'\n    } else {\n      macdDescription += ' - Momentum shifting'\n    }\n\n    indicators.push({\n      name: 'MACD',\n      value: currentHistogram,\n      signal: macdSignal,\n      description: macdDescription\n    })\n\n    // Volume Analysis\n    const volumeData = this.volumeAnalysis(candles)\n    let volumeSignal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'\n    let volumeDescription = `Volume: ${(volumeData.volumeRatio * 100).toFixed(0)}% of average`\n\n    if (volumeData.isHighVolume) {\n      volumeSignal = 'BUY'\n      volumeDescription += ' - High volume confirms move'\n    } else if (volumeData.isLowVolume) {\n      volumeSignal = 'SELL'\n      volumeDescription += ' - Low volume, weak conviction'\n    } else {\n      volumeDescription += ' - Normal volume'\n    }\n\n    indicators.push({\n      name: 'Volume',\n      value: volumeData.volumeRatio,\n      signal: volumeSignal,\n      description: volumeDescription\n    })\n\n    return indicators\n  }\n}\n", "export interface WatchlistStock {\n  symbol: string\n  name: string\n  sector: string\n  marketCap: 'large' | 'mid' | 'small'\n}\n\nexport const SWING_TRADING_WATCHLIST: WatchlistStock[] = [\n  // Large Cap Tech Giants\n  { symbol: 'MSFT', name: 'Microsoft Corp', sector: 'Technology', marketCap: 'large' },\n  { symbol: 'NVDA', name: 'NVIDIA Corp', sector: 'Technology', marketCap: 'large' },\n  { symbol: 'AMZN', name: 'Amazon.com Inc', sector: 'Consumer Discretionary', marketCap: 'large' },\n  { symbol: 'GOOG', name: 'Alphabet Inc Class C', sector: 'Technology', marketCap: 'large' },\n  { symbol: 'GOOGL', name: 'Alphabet Inc Class A', sector: 'Technology', marketCap: 'large' },\n  { symbol: 'META', name: 'Meta Platforms Inc', sector: 'Technology', marketCap: 'large' },\n  { symbol: 'AVGO', name: 'Broadcom Inc', sector: 'Technology', marketCap: 'large' },\n  { symbol: 'TSM', name: 'Taiwan Semiconductor', sector: 'Technology', marketCap: 'large' },\n  { symbol: 'ORCL', name: 'Oracle Corp', sector: 'Technology', marketCap: 'large' },\n  { symbol: 'NFLX', name: 'Netflix Inc', sector: 'Communication Services', marketCap: 'large' },\n  { symbol: 'CSCO', name: 'Cisco Systems Inc', sector: 'Technology', marketCap: 'large' },\n  { symbol: 'AMD', name: 'Advanced Micro Devices Inc', sector: 'Technology', marketCap: 'large' },\n\n  // Financial Services\n  { symbol: 'JPM', name: 'JPMorgan Chase & Co', sector: 'Financial Services', marketCap: 'large' },\n  { symbol: 'BAC', name: 'Bank of America Corp', sector: 'Financial Services', marketCap: 'large' },\n  { symbol: 'MS', name: 'Morgan Stanley', sector: 'Financial Services', marketCap: 'large' },\n  { symbol: 'SCHW', name: 'Charles Schwab Corp', sector: 'Financial Services', marketCap: 'large' },\n  { symbol: 'C', name: 'Citigroup Inc', sector: 'Financial Services', marketCap: 'large' },\n\n  // Healthcare & Pharmaceuticals\n  { symbol: 'JNJ', name: 'Johnson & Johnson', sector: 'Healthcare', marketCap: 'large' },\n  { symbol: 'ABBV', name: 'AbbVie Inc', sector: 'Healthcare', marketCap: 'large' },\n  { symbol: 'MRK', name: 'Merck & Co Inc', sector: 'Healthcare', marketCap: 'large' },\n  { symbol: 'GILD', name: 'Gilead Sciences Inc', sector: 'Healthcare', marketCap: 'large' },\n\n  // Industrial & Manufacturing\n  { symbol: 'GE', name: 'General Electric Co', sector: 'Industrial', marketCap: 'large' },\n  { symbol: 'CAT', name: 'Caterpillar Inc', sector: 'Industrial', marketCap: 'large' },\n  { symbol: 'BA', name: 'Boeing Co', sector: 'Industrial', marketCap: 'large' },\n  { symbol: 'GEV', name: 'GE Vernova Inc', sector: 'Industrial', marketCap: 'large' },\n\n  // Semiconductors\n  { symbol: 'ASML', name: 'ASML Holding NV', sector: 'Technology', marketCap: 'large' },\n  { symbol: 'MU', name: 'Micron Technology Inc', sector: 'Technology', marketCap: 'large' },\n  { symbol: 'LRCX', name: 'Lam Research Corp', sector: 'Technology', marketCap: 'large' },\n\n  // Consumer & Retail\n  { symbol: 'DIS', name: 'Walt Disney Co', sector: 'Communication Services', marketCap: 'large' },\n  { symbol: 'SBUX', name: 'Starbucks Corp', sector: 'Consumer Discretionary', marketCap: 'large' },\n  { symbol: 'MO', name: 'Altria Group Inc', sector: 'Consumer Staples', marketCap: 'large' },\n\n  // Energy & Utilities\n  { symbol: 'CEG', name: 'Constellation Energy Corp', sector: 'Utilities', marketCap: 'large' },\n  { symbol: 'VST', name: 'Vistra Corp', sector: 'Utilities', marketCap: 'mid' },\n\n  // Automotive\n  { symbol: 'GM', name: 'General Motors Co', sector: 'Consumer Discretionary', marketCap: 'large' },\n\n  // Growth & Tech Mid-Caps\n  { symbol: 'PLTR', name: 'Palantir Technologies Inc', sector: 'Technology', marketCap: 'mid' },\n  { symbol: 'APP', name: 'Applovin Corp', sector: 'Technology', marketCap: 'mid' },\n  { symbol: 'DASH', name: 'DoorDash Inc', sector: 'Consumer Discretionary', marketCap: 'mid' },\n  { symbol: 'NET', name: 'Cloudflare Inc', sector: 'Technology', marketCap: 'mid' },\n  { symbol: 'DDOG', name: 'Datadog Inc', sector: 'Technology', marketCap: 'mid' },\n  { symbol: 'ZS', name: 'Zscaler Inc', sector: 'Technology', marketCap: 'mid' },\n  { symbol: 'SHOP', name: 'Shopify Inc', sector: 'Technology', marketCap: 'mid' },\n  { symbol: 'RBLX', name: 'Roblox Corp', sector: 'Communication Services', marketCap: 'mid' },\n\n  // Mining & Commodities\n  { symbol: 'AEM', name: 'Agnico Eagle Mines Ltd', sector: 'Materials', marketCap: 'mid' },\n  { symbol: 'NEM', name: 'Newmont Corp', sector: 'Materials', marketCap: 'large' },\n  { symbol: 'CCJ', name: 'Cameco Corp', sector: 'Energy', marketCap: 'mid' },\n  { symbol: 'PAAS', name: 'Pan American Silver Corp', sector: 'Materials', marketCap: 'small' },\n  { symbol: 'BTG', name: 'B2Gold Corp', sector: 'Materials', marketCap: 'small' },\n  { symbol: 'HL', name: 'Hecla Mining Co', sector: 'Materials', marketCap: 'small' },\n  { symbol: 'MP', name: 'MP Materials Corp', sector: 'Materials', marketCap: 'small' },\n  { symbol: 'AG', name: 'First Majestic Silver Corp', sector: 'Materials', marketCap: 'small' },\n\n  // Transportation & Travel\n  { symbol: 'UAL', name: 'United Airlines Holdings Inc', sector: 'Industrial', marketCap: 'mid' },\n  { symbol: 'NCLH', name: 'Norwegian Cruise Line', sector: 'Consumer Discretionary', marketCap: 'mid' },\n\n  // Fintech & Trading\n  { symbol: 'HOOD', name: 'Robinhood Markets Inc', sector: 'Financial Services', marketCap: 'mid' },\n  { symbol: 'SOFI', name: 'SoFi Technologies Inc', sector: 'Financial Services', marketCap: 'small' },\n\n  // Consumer Brands\n  { symbol: 'CELH', name: 'Celsius Holdings Inc', sector: 'Consumer Staples', marketCap: 'small' },\n  { symbol: 'LEVI', name: 'Levi Strauss & Co', sector: 'Consumer Discretionary', marketCap: 'small' },\n  { symbol: 'ELF', name: 'e.l.f. Beauty Inc', sector: 'Consumer Discretionary', marketCap: 'small' },\n  { symbol: 'ETSY', name: 'Etsy Inc', sector: 'Consumer Discretionary', marketCap: 'mid' },\n  { symbol: 'W', name: 'Wayfair Inc', sector: 'Consumer Discretionary', marketCap: 'mid' },\n\n  // Crypto & Blockchain\n  { symbol: 'RIOT', name: 'Riot Platforms Inc', sector: 'Technology', marketCap: 'small' },\n  { symbol: 'HUT', name: 'Hut 8 Corp', sector: 'Technology', marketCap: 'small' },\n  { symbol: 'IREN', name: 'IREN Ltd', sector: 'Technology', marketCap: 'small' },\n\n  // International & Emerging\n  { symbol: 'BILI', name: 'Bilibili Inc', sector: 'Communication Services', marketCap: 'small' },\n  { symbol: 'TIGR', name: 'UP Fintech Holding Ltd', sector: 'Financial Services', marketCap: 'small' },\n  { symbol: 'FUTU', name: 'Futu Holdings Ltd', sector: 'Financial Services', marketCap: 'small' },\n  { symbol: 'NBIS', name: 'Nebius Group NV', sector: 'Technology', marketCap: 'small' },\n\n  // Emerging Tech & AI\n  { symbol: 'SOUN', name: 'SoundHound AI Inc', sector: 'Technology', marketCap: 'small' },\n  { symbol: 'IONQ', name: 'IonQ Inc', sector: 'Technology', marketCap: 'small' },\n  { symbol: 'RGTI', name: 'Rigetti Computing Inc', sector: 'Technology', marketCap: 'small' },\n\n  // Aerospace & Defense\n  { symbol: 'RKLB', name: 'Rocket Lab Corp', sector: 'Industrial', marketCap: 'small' },\n  { symbol: 'ASTS', name: 'AST SpaceMobile Inc', sector: 'Technology', marketCap: 'small' },\n\n  // Infrastructure & Utilities\n  { symbol: 'VRT', name: 'Vertiv Holdings Co', sector: 'Industrial', marketCap: 'mid' },\n]\n\n// Group stocks by sector for analysis\nexport const STOCKS_BY_SECTOR = SWING_TRADING_WATCHLIST.reduce((acc, stock) => {\n  if (!acc[stock.sector]) {\n    acc[stock.sector] = []\n  }\n  acc[stock.sector].push(stock)\n  return acc\n}, {} as Record<string, WatchlistStock[]>)\n\n// Get all symbols as array\nexport const ALL_SYMBOLS = SWING_TRADING_WATCHLIST.map(stock => stock.symbol)\n\n// Priority symbols for faster scanning (top liquid stocks)\nexport const PRIORITY_SYMBOLS = [\n  'MSFT', 'NVDA', 'AMZN', 'GOOG', 'GOOGL', 'META', 'AVGO', 'TSM', \n  'JPM', 'NFLX', 'ORCL', 'JNJ', 'BAC', 'ABBV', 'ASML', 'PLTR'\n]\n"], "names": [], "mappings": "8yCAAA,IAAA,EAAA,EAAA,CAAA,CAAA,OAGA,IAAM,EAAmB,yBACnB,EAAU,QAAQ,GAAG,CAAC,eAAe,AAEpC,OAAM,EACH,MAAc,AAEtB,aAAY,CAAe,CAAE,CAE3B,GADA,IAAI,CAAC,MAAM,CAAG,GAAU,GAAW,GAC/B,CAAC,IAAI,CAAC,MAAM,CACd,CADgB,KACV,AAAI,MAAM,8BAEpB,CAGA,MAAM,cAAc,CAAc,CAAsB,CACtD,GAAI,CAWF,IAAM,EAAO,AATI,OAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAiB,8CAA8C,EAAE,EAAA,CAAQ,CAC5E,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGoB,IAAI,CAAC,OAAO,CAAC,EAAE,CACrC,GAAI,CAAC,EACH,IADS,EACH,AAAI,MAAM,CAAC,kBAAkB,EAAE,EAAA,CAAQ,EAG/C,IAAM,EAAS,EAAK,KAAK,EAAI,EACvB,EAAU,EAAO,GAAG,EAAI,CAAC,EACzB,EAAc,EAAO,OAAO,EAAI,CAAC,EACrB,EAAO,SAAS,CAIlC,GAJsC,CAAC,AAIjC,EAAe,CAHH,EAAO,SAAS,EAAI,CAAC,GAGR,CAAC,EAAI,EAAQ,CAAC,EAAI,EAAY,CAAC,CACxD,EAAY,EAAY,CAAC,EAAI,EAAQ,CAAC,CACtC,EAAS,EAAe,EAG9B,MAAO,CACL,OAAQ,EAAO,WAAW,GAC1B,KAAM,EAAO,IAAI,EAAI,EAAO,WAAW,GACvC,MAAO,SACP,EACA,cAPqB,EAAS,EAAa,IAQ3C,OAAQ,EAAQ,CAAC,EAAI,EACrB,UAAW,EAAO,UAAU,CAC5B,QAAI,EACJ,cAAU,CACZ,CACF,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,2CAA4C,GAG1D,GAAI,CAWF,IAAM,EAAO,CAVY,MAAM,EAAA,OAAK,CAAC,GAAG,CACtC,CAAA,EAAG,EAAiB,gBAAgB,EAAE,EAAO,KAAK,CAAC,CACnD,CACE,OAAQ,CACN,SAAU,OACV,OAAQ,IAAI,CAAC,MACf,AADqB,CAEvB,EAAA,EAG4B,IAAI,CAAC,OAAO,CAAC,EAAE,CAC7C,MAAO,CACL,OAAQ,EAAO,WAAW,GAC1B,KAAM,EAAO,WAAW,GACxB,MAAO,EAAK,CAAC,CACb,OAAQ,EAAK,CAAC,CAAG,EAAK,CAAC,CACvB,cAAgB,CAAC,EAAK,CAAC,CAAG,EAAK,CAAC,EAAI,EAAK,CAAC,CAAI,IAC9C,OAAQ,EAAK,CAAC,CACd,eAAW,EACX,QAAI,EACJ,SAAU,MACZ,CACF,CAAE,MAAO,EAAe,CAEtB,MADA,QAAQ,KAAK,CAAC,gCAAiC,GACzC,AAAI,MAAM,CAAC,0BAA0B,EAAE,EAAA,CAAQ,CACvD,CACF,CACF,CAGA,MAAM,kBACJ,CAAc,CACd,EAAyD,KAAK,CAC9D,EAAqB,CAAC,CACtB,CAAY,CACZ,CAAU,CACkB,CAC5B,GAAI,CACF,IAAM,EAAW,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAiB,gBAAgB,EAAE,EAAO,OAAO,EAAE,EAAW,CAAC,EAAE,EAAS,CAAC,EAAE,EAAK,CAAC,EAAE,EAAA,CAAI,CAC5F,CACE,OAAQ,CACN,SAAU,OACV,KAAM,MACN,MAAO,IACP,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,GAGF,GAAI,CAAC,EAAS,IAAI,CAAC,OAAO,EAAqC,GAAG,CAApC,EAAS,IAAI,CAAC,OAAO,CAAC,MAAM,CAExD,OADA,QAAQ,IAAI,CAAC,CAAC,6BAA6B,EAAE,EAAA,CAAQ,EAC9C,EAAE,CAGX,OAAO,EAAS,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,AAAC,IAAiB,CACjD,IADgD,MACrC,EAAO,CAAC,CACnB,KAAM,EAAO,CAAC,CACd,KAAM,EAAO,CAAC,CACd,IAAK,EAAO,CAAC,CACb,MAAO,EAAO,CAAC,CACf,OAAQ,EAAO,CAAC,CAClB,CAAC,CACH,CAAE,MAAO,EAAO,CASd,MARA,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,EAAO,CAAC,CAAC,CAAE,GAG3D,EAAM,QAAQ,EAAE,CAClB,QAAQ,KAAK,CAAC,CAAC,mBAAmB,EAAE,EAAM,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,EAAM,QAAQ,CAAC,UAAU,CAAA,CAAE,EAC1F,QAAQ,KAAK,CAAC,iBAAkB,EAAM,QAAQ,CAAC,IAAI,GAG/C,AAAI,MAAM,CAAC,oCAAoC,EAAE,EAAO,EAAE,EAAE,EAAM,OAAO,CAAA,CAAE,CACnF,CACF,CAGA,MAAM,kBAAkB,CAAc,CAAE,CACtC,GAAI,CAUF,MAAO,CATU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAiB,sBAAsB,EAAE,EAAA,CAAQ,CACpD,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,CAAC,OAAO,AAC9B,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,kCAAmC,GAC1C,IACT,CACF,CAGA,MAAM,iBAAkB,CACtB,GAAI,CAUF,MAAO,CATU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAiB,oBAAoB,CAAC,CACzC,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,AACtB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,gCAAiC,GACxC,IACT,CACF,CAGA,MAAM,aAAa,CAAa,CAAE,EAAgB,EAAE,CAAE,CACpD,GAAI,CAcF,MAbiB,AAaV,OAbgB,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAiB,qBAAqB,CAAC,CAC1C,CACE,OAAQ,CACN,OAAQ,EACR,OAAQ,SACR,OAAQ,aACR,EACA,OAAQ,IAAI,CAAC,MACf,AADqB,CAEvB,EAAA,EAGc,IAAI,CAAC,OAAO,EAAI,EAAE,AACpC,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,0BAA2B,GAClC,EACT,AADW,CAEb,CACF,CAG0B,IAAI,gDCzM9B,IAAA,EAAA,EAAA,CAAA,CAAA,OAGA,IAAM,EAAe,wCACf,EAAU,QAAQ,GAAG,CAAC,WAAW,AAEhC,OAAM,EACH,MAAc,AAEtB,aAAY,CAAe,CAAE,CAE3B,GADA,IAAI,CAAC,MAAM,CAAG,GAAU,GAAW,GAC/B,CAAC,IAAI,CAAC,MAAM,CACd,CADgB,KACV,AAAI,MAAM,0BAEpB,CAGA,MAAM,cAAc,CAAc,CAAsB,CACtD,GAAI,CAUF,IAAM,EAAO,CATI,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,UAAU,EAAE,EAAA,CAAQ,CACpC,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGoB,IAAI,CAAC,EAAE,CAC7B,GAAI,CAAC,EACH,IADS,EACH,AAAI,MAAM,CAAC,yBAAyB,EAAE,EAAA,CAAQ,EAGtD,MAAO,CACL,OAAQ,EAAK,MAAM,CACnB,KAAM,EAAK,IAAI,EAAI,EAAK,MAAM,CAC9B,MAAO,EAAK,KAAK,CACjB,OAAQ,EAAK,MAAM,CACnB,cAAe,EAAK,iBAAiB,CACrC,OAAQ,EAAK,MAAM,CACnB,UAAW,EAAK,SAAS,CACzB,GAAI,EAAK,EAAE,CACX,cAAU,CACZ,CACF,CAAE,MAAO,CAFe,CAER,CAEd,MADA,QAAQ,KAAK,CAAC,iBAH+C,iBAGZ,GAC3C,AAAI,MAAM,CAAC,0BAA0B,EAAE,EAAA,CAAQ,CACvD,CACF,CAGA,MAAM,kBAAkB,CAAc,CAAE,CACtC,GAAI,CAUF,MAAO,CATU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,YAAY,EAAE,EAAA,CAAQ,CACtC,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,CAAC,EAAE,AACzB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,kCAAmC,GAC1C,IACT,CACF,CAGA,MAAM,mBAAmB,CAAc,CAAE,CACvC,GAAI,CAUF,MAAO,CATU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,WAAW,EAAE,EAAA,CAAQ,CACrC,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,CAAC,EAAE,AACzB,CAD0B,AACxB,MAAO,EAAO,CAEd,OADA,KAF6C,GAErC,KAAK,CAAC,mCAAoC,GAC3C,IACT,CACF,CAGA,MAAM,cAAc,CAAc,CAAE,CAClC,GAAI,CAUF,MAAO,CATU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,gBAAgB,EAAE,EAAA,CAAQ,CAC1C,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,CAAC,EAAE,AACzB,CAD0B,AACxB,MAAO,EAAO,CAEd,OADA,MAF8C,EAEtC,KAAK,CAAC,8BAA+B,GACtC,IACT,CACF,CAGA,MAAM,0BAA0B,CAAc,CAAE,CAC9C,GAAI,CAUF,MAAO,AATU,OAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,kCAAkC,EAAE,EAAA,CAAQ,CAC5D,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,AACtB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,0CAA2C,GAClD,EAAE,AACX,CACF,CAGA,MAAM,oBAAoB,CAAa,CAAE,CAAW,CAAE,CACpD,GAAI,CACF,IAAM,EAAc,CAClB,OAAQ,IAAI,CAAC,MAAM,AACrB,EAUA,OARI,IAAM,EAAO,IAAI,CAAG,CAAA,EACpB,IAAI,EAAO,EAAE,CAAG,CAAA,EAOb,CALU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,oBAAoB,CAAC,CACrC,QAAE,CAAO,EAAA,EAGK,IAAI,AACtB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,oCAAqC,GAC5C,EAAE,AACX,CACF,CAGA,MAAM,oBAAoB,CAAa,CAAE,CAAW,CAAE,CACpD,GAAI,CACF,IAAM,EAAc,CAClB,OAAQ,IAAI,CAAC,MAAM,AACrB,EAUA,OARI,IAAM,EAAO,IAAI,CAAG,CAAA,EACpB,IAAI,EAAO,EAAE,CAAG,CAAA,EAOb,CALU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,qBAAqB,CAAC,CACtC,CAAE,QAAO,EAAA,EAGK,IAAI,AACtB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,oCAAqC,GAC5C,EACT,AADW,CAEb,CAGA,MAAM,aAAa,CAAa,CAAE,EAAgB,EAAE,CAAE,CACpD,GAAI,CAYF,MAAO,CAXU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,UAAU,CAAC,CAC3B,CACE,OAAQ,OACN,QACA,EACA,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,AACtB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,0BAA2B,GAClC,EAAE,AACX,CACF,CAGA,MAAM,sBAAuB,CAC3B,GAAI,CAUF,MAAO,CATU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,sBAAsB,CAAC,CACvC,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MACf,AADqB,CAEvB,EAAA,EAGc,IAAI,AACtB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,qCAAsC,GAC7C,EAAE,AACX,CACF,CAGA,MAAM,gBAAgB,CAAsC,CAAE,CAC5D,GAAI,CAUF,MAAO,CATU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,iBAAiB,EAAE,EAAA,CAAM,CACzC,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAClB,AADsB,CACpB,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,EAAK,CAAC,CAAC,CAAE,GACzC,EAAE,AACX,CACF,CAKA,MAAM,oBAAoB,CAAe,CAAE,EAAe,EAAE,CAAE,CAC5D,GAAI,CACF,IAAM,EAAW,IAAI,KACrB,EAAS,OAAO,CAAC,EAAS,OAAO,GAAK,GACtC,IAAM,EAAS,IAAI,KAcnB,MAAO,CAZU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,oBAAoB,CAAC,CACrC,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,CACnB,KAAM,EAAS,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAC1C,GAAI,EAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CACtC,GAAI,GAAU,CAAE,OAAQ,EAAO,WAAW,EAAG,CAAC,AAChD,CACF,EAAA,EAGc,IAAI,EAAI,EAAE,AAC5B,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,oCAAqC,GAC5C,EAAE,AACX,CACF,CAGA,MAAM,aAAa,CAAc,CAAE,EAAgB,EAAE,CAAE,CACrD,GAAI,CAYF,MAXiB,AAWV,OAXgB,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,cAAc,CAAC,CAC/B,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,CACnB,QAAS,EAAO,WAAW,SAC3B,CACF,CACF,EAAA,EAGc,IAAI,EAAI,EAAE,AAC5B,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,6BAA8B,GACrC,EAAE,AACX,CACF,CAGA,MAAM,0BAA0B,CAAc,CAAE,EAAe,EAAE,CAAE,CACjE,GAAI,CAWF,MAAO,CAVU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,kCAAkC,EAAE,EAAO,WAAW,GAAA,CAAI,CAC1E,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,CACnB,MAAO,CACT,CACF,EAAA,EAGc,IAAI,EAAI,EAAE,AAC5B,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,0CAA2C,GAClD,EAAE,AACX,CACF,CAGA,MAAM,kBAAkB,CAAc,CAAE,EAAe,EAAE,CAAE,CACzD,GAAI,CACF,IAAM,EAAW,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,mBAAmB,CAAC,CACpC,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,CACnB,OAAQ,EAAO,WAAW,GAC1B,MAAc,EAAP,AAAS,CAClB,CACF,GAII,EAAa,IAAI,KAGvB,OAFA,EAAW,OAAO,CAAC,EAAW,OAAO,AAP2B,GAOtB,GAEnC,CAAC,EAAS,IAAI,EAAI,EAAA,AAAE,EAAE,MAAM,CAAC,AAAC,GACnC,IAAI,KAAK,EAAM,UAAU,GAAK,EAElC,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,kCAAmC,GAC1C,EAAE,AACX,CACF,CAGA,MAAM,cAAc,CAAc,CAAE,EAAe,EAAE,CAAE,CACrD,GAAI,CACF,IAAM,EAAW,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,gBAAgB,EAAE,EAAO,WAAW,GAAA,CAAI,CACxD,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,CACnB,MAAc,EAAP,AAAS,CAClB,CACF,GAII,EAAa,IAAI,KAGvB,OAFA,EAAW,OAAO,CAAC,EAAW,EAP6B,KAOtB,GAAK,GAEnC,CAAC,EAAS,IAAI,EAAI,EAAA,AAAE,EAAE,MAAM,CAAE,AAAD,GAClC,IAAI,KAAK,EAAO,SAAS,GAAK,EAElC,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,8BAA+B,GACtC,EAAE,AACX,CACF,CAGA,MAAM,kBAAkB,CAAc,CAAE,CACtC,GAAI,CAUF,IAAM,EAAO,CATI,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,UAAU,EAAE,EAAO,WAAW,GAAA,CAAI,CAClD,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGoB,IAAI,CAAC,EAAE,CAC7B,GAAI,CAAC,EAAM,OAAO,KAElB,MAAO,CACL,OAAQ,EAAK,MAAM,CACnB,MAAO,EAAK,KAAK,CACjB,cAAe,EAAK,aAAa,CACjC,OAAQ,EAAK,MAAM,CACnB,cAAe,EAAK,iBAAiB,CACrC,OAAQ,EAAK,MAAM,CACnB,UAAW,EAAK,SAAS,CACzB,UAAW,EAAK,SAAS,CAEzB,eAAgB,EAAK,cAAc,EAAI,EAAK,KAAK,CACjD,gBAAiB,EAAK,eAAe,EAAI,EAAK,MAAM,CACpD,uBAAwB,EAAK,sBAAsB,EAAI,EAAK,iBAAiB,AAC/E,CACF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,mCAAoC,GAC3C,IACT,CACF,CAGA,MAAM,2BAA2B,CAAiB,CAAE,CAClD,GAAI,CACF,IAAM,EAAgB,EAAQ,GAAG,CAAC,GAAK,EAAE,WAAW,IAAI,IAAI,CAAC,KAU7D,MAAO,AAAC,EATS,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,UAAU,EAAE,EAAA,CAAe,CAC3C,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGe,IAAI,EAAI,EAAA,AAAE,EAAE,GAAG,CAAE,AAAD,IAAgB,CAC/C,EAD8C,KACtC,EAAK,MAAM,CACnB,MAAO,EAAK,KAAK,CACjB,cAAe,EAAK,aAAa,CACjC,OAAQ,EAAK,MAAM,CACnB,cAAe,EAAK,iBAAiB,CACrC,OAAQ,EAAK,MAAM,CACnB,UAAW,EAAK,SAAS,CACzB,UAAW,EAAK,SAAS,CACzB,eAAgB,EAAK,cAAc,EAAI,EAAK,KAAK,CACjD,gBAAiB,EAAK,eAAe,EAAI,EAAK,MAAM,CACpD,uBAAwB,EAAK,sBAAsB,EAAI,EAAK,iBAAiB,CAC/E,CAAC,CACH,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,6CAA8C,GACrD,EAAE,AACX,CACF,CAGA,MAAM,kBAAkB,CAAc,CAAE,CACtC,GAAI,CAUF,MAAO,CATU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,YAAY,EAAE,EAAO,WAAW,GAAA,CAAI,CACpD,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,CAAC,EAAE,EAAI,IAC7B,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,kCAAmC,GAC1C,IACT,CACF,CACF,CAGsB,IAAI,4DCrbnB,OAAM,EAEX,OAAO,IAAI,CAAc,CAAE,CAAc,CAAY,CACnD,IAAM,EAAmB,EAAE,CAC3B,IAAK,IAAI,EAAI,EAAS,EAAG,EAAI,EAAK,MAAM,CAAE,IAAK,CAC7C,IAAM,EAAM,EAAK,KAAK,CAAC,EAAI,EAAS,EAAG,EAAI,GAAG,MAAM,CAAC,CAAC,EAAG,IAAM,EAAI,EAAG,GACtE,EAAO,IAAI,CAAC,EAAM,EACpB,CACA,OAAO,CACT,CAGA,OAAO,IAAI,CAAc,CAAE,CAAc,CAAY,CACnD,IAAM,EAAmB,EAAE,CACrB,EAAa,EAAK,EAAD,EAAU,CAAC,CAG9B,EAAM,EAAK,KAAK,CAAC,EAAG,GAAQ,MAAM,CAAC,CAAC,EAAG,IAAM,EAAI,EAAG,GAAK,EAC7D,EAAO,IAAI,CAAC,GAEZ,IAAK,IAAI,EAAI,EAAQ,EAAI,EAAK,MAAM,CAAE,IAAK,AAEzC,EAAO,IAAI,CADX,AACY,EADL,CAAI,CAAC,EAAE,CAAG,EAAe,EAAO,GAAI,CAAL,AAAK,CAAU,EAIvD,OAAO,CACT,CAGA,OAAO,IAAI,CAAc,CAAE,EAAiB,EAAE,CAAY,CACxD,IAAM,EAAkB,EAAE,CACpB,EAAmB,EAAE,CAE3B,IAAK,IAAI,EAAI,EAAG,EAAI,EAAK,MAAM,CAAE,IAAK,CACpC,IAAM,EAAS,CAAI,CAAC,EAAE,CAAG,CAAI,CAAC,EAAI,EAAE,CACpC,EAAM,IAAI,CAAC,EAAS,EAAI,EAAS,GACjC,EAAO,IAAI,CAAC,EAAS,EAAI,KAAK,GAAG,CAAC,GAAU,EAC9C,CAEA,IAAM,EAAW,IAAI,CAAC,GAAG,CAAC,EAAO,GAC3B,EAAY,IAAI,CAAC,GAAG,CAAC,EAAQ,GAEnC,OAAO,EAAS,GAAG,CAAC,CAAC,EAAM,IAElB,IAAO,KAAO,CAAD,CADT,EAAO,AACO,CADE,CAAC,EAAE,AACL,CAAE,CAE/B,CAGA,OAAO,KAAK,CAAc,CAAE,EAAqB,EAAE,CAAE,EAAqB,EAAE,CAAE,EAAuB,CAAC,CAAE,CACtG,IAAM,EAAU,IAAI,CAAC,GAAG,CAAC,EAAM,GACzB,EAAU,IAAI,CAAC,GAAG,CAAC,EAAM,GAIzB,EAAW,EAAQ,KAAK,CADX,AACY,EADC,GACW,GAAG,CAAC,CAAC,EAAM,IAAM,EAAO,CAAO,CAAC,EAAE,EAEvE,EAAa,IAAI,CAAC,GAAG,CAAC,EAAU,GAChC,EAAY,EAAS,KAAK,CAAC,EAAe,GAAG,GAAG,CAAC,CAAC,EAAM,IAAM,EAAO,CAAU,CAAC,EAAE,EAExF,MAAO,CACL,KAAM,EACN,OAAQ,YACR,CACF,CACF,CAGA,OAAO,eAAe,CAAc,CAAE,EAAiB,EAAE,CAAE,EAAiB,CAAC,CAAE,CAc7E,OAbY,AACE,AAYP,IAbS,CAAC,GAAG,CAAC,EAAM,GACT,GAAG,CAAC,CAAC,EAAK,KAG1B,IAAM,EAAoB,KAAK,IAAI,CAFrB,AACG,AACmB,EAFjB,KAAK,CAAC,EAAG,EAAI,GACT,MAAM,CAAC,CAAC,EAAK,IAAQ,EAAM,KAAK,GAAG,CAAC,EAAM,EAAK,GAAI,GAAK,GAG/E,MAAO,CACL,MAAO,EAAO,EAAoB,EAClC,OAAQ,EACR,MAAO,EAAO,EAAoB,CACpC,CACF,EAGF,CAGA,OAAO,sBAAsB,CAA0B,CAAE,EAAmB,EAAE,CAA+C,CAC3H,IAAM,EAAQ,EAAQ,GAAG,CAAC,GAAK,EAAE,IAAI,EAC/B,EAAO,EAAQ,GAAG,CAAC,GAAK,EAAE,GAAG,EAE7B,EAAuB,EAAE,CACzB,EAAoB,EAAE,CAE5B,IAAK,IAAI,EAAI,EAAU,EAAI,EAAQ,MAAM,CAAG,EAAU,IAAK,CACzD,IAAM,EAAc,CAAK,CAAC,EAAE,CACtB,EAAa,CAAI,CAAC,EAAE,CAGpB,EAAe,EAAM,KAAK,CAAC,EAAI,EAAU,GAAG,KAAK,CAAC,GAAK,GAAK,IAC9C,EAAM,KAAK,CAAC,EAAI,EAAG,EAAI,EAAW,GAAG,KAAK,CAAC,GAAK,GAAK,GAGnE,EAAY,EAAK,KAAK,CAAC,EAAI,EAAU,GAAG,KAAK,CAAC,GAAK,GAAK,IAC7C,EAAK,KAAK,CAAC,EAAI,EAAG,EAAI,EAAW,GAAG,KAAK,CAAC,GAAK,GAAK,GAEjE,GAAc,EAAW,IAAI,CAAC,GAC9B,GAAW,EAAQ,IAAI,CAAC,EAC9B,CAEA,MAAO,SAAE,aAAS,CAAW,CAC/B,CAGA,OAAO,eAAe,CAA0B,CAAE,EAAiB,EAAE,CAAE,CACrE,IAAM,EAAU,EAAQ,GAAG,CAAC,GAAK,EAAE,MAAM,EACnC,EAAY,IAAI,CAAC,GAAG,CAAC,EAAS,GAC9B,EAAgB,CAAO,CAAC,EAAQ,MAAM,CAAG,EAAE,CAC3C,EAAmB,CAAS,CAAC,EAAU,MAAM,CAAG,EAAE,CAExD,MAAO,CACL,gBACA,cAAe,EACf,YAAa,EAAgB,EAC7B,aAAc,EAAmC,IAAnB,EAC9B,YAAa,EAAmC,GAAnB,CAC/B,CACF,CAGA,OAAO,kBAAkB,CAA0B,CAAwB,CACzE,IAAM,EAAS,EAAQ,GAAG,CAAC,GAAK,EAAE,KAAK,EACjC,EAAmC,EAAE,CAGrC,EAAM,IAAI,CAAC,GAAG,CAAC,GACf,EAAa,CAAG,CAAC,EAAI,MAAM,CAAG,EAAE,CAElC,EAAwC,UACxC,EAAiB,CAAC,KAAK,EAAE,EAAW,OAAO,CAAC,GAAA,CAAI,CAEhD,EAAa,IAAI,AACnB,EAAY,MACZ,GAAkB,2CACT,EAAa,IAAI,AAC1B,EAAY,OACZ,GAAkB,+CAElB,GAAkB,kBAGpB,EAAW,IAAI,CAAC,CACd,KAAM,MACN,MAAO,EACP,OAAQ,EACR,YAAa,CACf,GAGA,IAAM,EAAQ,IAAI,CAAC,GAAG,CAAC,EAAQ,IACzB,EAAQ,IAAI,CAAC,GAAG,CAAC,EAAQ,IACzB,EAAe,CAAM,CAAC,EAAO,MAAM,CAAG,EAAE,CACxC,EAAe,CAAK,CAAC,EAAM,MAAM,CAAG,EAAE,CACtC,EAAe,CAAK,CAAC,EAAM,MAAM,CAAG,EAAE,CAExC,EAAuC,UACvC,EAAgB,CAAC,gBAAgB,EAAE,CAAC,CAAC,EAAe,GAAe,CAAC,CAAI,GAAA,CAAG,CAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAE1F,EAAe,GAAgB,EAAe,GAChD,EAAW,MACX,GAF8D,AAE7C,oBACR,EAAe,GAAgB,EAAe,GACvD,EAAW,OACX,EAFqE,CAEpD,oBAEjB,GAAiB,mBAGnB,EAAW,IAAI,CAAC,CACd,KAAM,kBACN,MAAO,CAAC,EAAe,GAAe,CAAC,CAAI,IAC3C,OAAQ,EACR,YAAa,CACf,GAGA,IAAM,EAAW,IAAI,CAAC,IAAI,CAAC,GACrB,EAAc,EAAS,IAAI,CAAC,EAAS,IAAI,CAAC,MAAM,CAAG,EAAE,CACrD,EAAgB,EAAS,MAAM,CAAC,EAAS,MAAM,CAAC,MAAM,CAAG,EAAE,CAC3D,EAAmB,EAAS,SAAS,CAAC,EAAS,SAAS,CAAC,MAAM,CAAG,EAAE,CAEtE,EAAyC,UACzC,EAAkB,CAAC,MAAM,EAAE,EAAY,OAAO,CAAC,GAAG,UAAU,EAAE,EAAc,OAAO,CAAC,GAAA,CAAI,CAExF,EAAc,GAAiB,EAAmB,GAAG,AACvD,EAAa,MACb,GAAmB,uBACV,EAAc,GAAiB,EAAmB,GAAG,AAC9D,EAAa,OACb,GAAmB,uBAEnB,GAAmB,uBAGrB,EAAW,IAAI,CAAC,CACd,KAAM,OACN,MAAO,EACP,OAAQ,EACR,YAAa,CACf,GAGA,IAAM,EAAa,IAAI,CAAC,cAAc,CAAC,GACnC,EAA2C,UAC3C,EAAoB,CAAC,QAAQ,EAAE,CAA0B,IAAzB,EAAW,WAAW,AAAG,CAAG,CAAE,OAAO,CAAC,GAAG,YAAY,CAAC,CAmB1F,OAjBI,EAAW,YAAY,EAAE,AAC3B,EAAe,MACf,GAAqB,gCACZ,EAAW,WAAW,EAAE,AACjC,EAAe,OACf,GAAqB,kCAErB,GAAqB,mBAGvB,EAAW,IAAI,CAAC,CACd,KAAM,SACN,MAAO,EAAW,WAAW,CAC7B,OAAQ,EACR,YAAa,CACf,GAEO,CACT,CACF,sGCrOO,IAAM,EAA4C,CAEvD,CAAE,OAAQ,OAAQ,KAAM,iBAAkB,OAAQ,aAAc,UAAW,OAAQ,EACnF,CAAE,OAAQ,OAAQ,KAAM,cAAe,OAAQ,aAAc,UAAW,OAAQ,EAChF,CAAE,OAAQ,OAAQ,KAAM,iBAAkB,OAAQ,yBAA0B,UAAW,OAAQ,EAC/F,CAAE,OAAQ,OAAQ,KAAM,uBAAwB,OAAQ,aAAc,UAAW,OAAQ,EACzF,CAAE,OAAQ,QAAS,KAAM,uBAAwB,OAAQ,aAAc,UAAW,OAAQ,EAC1F,CAAE,OAAQ,OAAQ,KAAM,qBAAsB,OAAQ,aAAc,UAAW,OAAQ,EACvF,CAAE,OAAQ,OAAQ,KAAM,eAAgB,OAAQ,aAAc,UAAW,OAAQ,EACjF,CAAE,OAAQ,MAAO,KAAM,uBAAwB,OAAQ,aAAc,UAAW,OAAQ,EACxF,CAAE,OAAQ,OAAQ,KAAM,cAAe,OAAQ,aAAc,UAAW,OAAQ,EAChF,CAAE,OAAQ,OAAQ,KAAM,cAAe,OAAQ,yBAA0B,UAAW,OAAQ,EAC5F,CAAE,OAAQ,OAAQ,KAAM,oBAAqB,OAAQ,aAAc,UAAW,OAAQ,EACtF,CAAE,OAAQ,MAAO,KAAM,6BAA8B,OAAQ,aAAc,UAAW,OAAQ,EAG9F,CAAE,OAAQ,MAAO,KAAM,sBAAuB,OAAQ,qBAAsB,UAAW,OAAQ,EAC/F,CAAE,OAAQ,MAAO,KAAM,uBAAwB,OAAQ,qBAAsB,UAAW,OAAQ,EAChG,CAAE,OAAQ,KAAM,KAAM,iBAAkB,OAAQ,qBAAsB,UAAW,OAAQ,EACzF,CAAE,OAAQ,OAAQ,KAAM,sBAAuB,OAAQ,qBAAsB,UAAW,OAAQ,EAChG,CAAE,OAAQ,IAAK,KAAM,gBAAiB,OAAQ,qBAAsB,UAAW,OAAQ,EAGvF,CAAE,OAAQ,MAAO,KAAM,oBAAqB,OAAQ,aAAc,UAAW,OAAQ,EACrF,CAAE,OAAQ,OAAQ,KAAM,aAAc,OAAQ,aAAc,UAAW,OAAQ,EAC/E,CAAE,OAAQ,MAAO,KAAM,iBAAkB,OAAQ,aAAc,UAAW,OAAQ,EAClF,CAAE,OAAQ,OAAQ,KAAM,sBAAuB,OAAQ,aAAc,UAAW,OAAQ,EAGxF,CAAE,OAAQ,KAAM,KAAM,sBAAuB,OAAQ,aAAc,UAAW,OAAQ,EACtF,CAAE,OAAQ,MAAO,KAAM,kBAAmB,OAAQ,aAAc,UAAW,OAAQ,EACnF,CAAE,OAAQ,KAAM,KAAM,YAAa,OAAQ,aAAc,UAAW,OAAQ,EAC5E,CAAE,OAAQ,MAAO,KAAM,iBAAkB,OAAQ,aAAc,UAAW,OAAQ,EAGlF,CAAE,OAAQ,OAAQ,KAAM,kBAAmB,OAAQ,aAAc,UAAW,OAAQ,EACpF,CAAE,OAAQ,KAAM,KAAM,wBAAyB,OAAQ,aAAc,UAAW,OAAQ,EACxF,CAAE,OAAQ,OAAQ,KAAM,oBAAqB,OAAQ,aAAc,UAAW,OAAQ,EAGtF,CAAE,OAAQ,MAAO,KAAM,iBAAkB,OAAQ,yBAA0B,UAAW,OAAQ,EAC9F,CAAE,OAAQ,OAAQ,KAAM,iBAAkB,OAAQ,yBAA0B,UAAW,OAAQ,EAC/F,CAAE,OAAQ,KAAM,KAAM,mBAAoB,OAAQ,mBAAoB,UAAW,OAAQ,EAGzF,CAAE,OAAQ,MAAO,KAAM,4BAA6B,OAAQ,YAAa,UAAW,OAAQ,EAC5F,CAAE,OAAQ,MAAO,KAAM,cAAe,OAAQ,YAAa,UAAW,KAAM,EAG5E,CAAE,OAAQ,KAAM,KAAM,oBAAqB,OAAQ,yBAA0B,UAAW,OAAQ,EAGhG,CAAE,OAAQ,OAAQ,KAAM,4BAA6B,OAAQ,aAAc,UAAW,KAAM,EAC5F,CAAE,OAAQ,MAAO,KAAM,gBAAiB,OAAQ,aAAc,UAAW,KAAM,EAC/E,CAAE,OAAQ,OAAQ,KAAM,eAAgB,OAAQ,yBAA0B,UAAW,KAAM,EAC3F,CAAE,OAAQ,MAAO,KAAM,iBAAkB,OAAQ,aAAc,UAAW,KAAM,EAChF,CAAE,OAAQ,OAAQ,KAAM,cAAe,OAAQ,aAAc,UAAW,KAAM,EAC9E,CAAE,OAAQ,KAAM,KAAM,cAAe,OAAQ,aAAc,UAAW,KAAM,EAC5E,CAAE,OAAQ,OAAQ,KAAM,cAAe,OAAQ,aAAc,UAAW,KAAM,EAC9E,CAAE,OAAQ,OAAQ,KAAM,cAAe,OAAQ,yBAA0B,UAAW,KAAM,EAG1F,CAAE,OAAQ,MAAO,KAAM,yBAA0B,OAAQ,YAAa,UAAW,KAAM,EACvF,CAAE,OAAQ,MAAO,KAAM,eAAgB,OAAQ,YAAa,UAAW,OAAQ,EAC/E,CAAE,OAAQ,MAAO,KAAM,cAAe,OAAQ,SAAU,UAAW,KAAM,EACzE,CAAE,OAAQ,OAAQ,KAAM,2BAA4B,OAAQ,YAAa,UAAW,OAAQ,EAC5F,CAAE,OAAQ,MAAO,KAAM,cAAe,OAAQ,YAAa,UAAW,OAAQ,EAC9E,CAAE,OAAQ,KAAM,KAAM,kBAAmB,OAAQ,YAAa,UAAW,OAAQ,EACjF,CAAE,OAAQ,KAAM,KAAM,oBAAqB,OAAQ,YAAa,UAAW,OAAQ,EACnF,CAAE,OAAQ,KAAM,KAAM,6BAA8B,OAAQ,YAAa,UAAW,OAAQ,EAG5F,CAAE,OAAQ,MAAO,KAAM,+BAAgC,OAAQ,aAAc,UAAW,KAAM,EAC9F,CAAE,OAAQ,OAAQ,KAAM,wBAAyB,OAAQ,yBAA0B,UAAW,KAAM,EAGpG,CAAE,OAAQ,OAAQ,KAAM,wBAAyB,OAAQ,qBAAsB,UAAW,KAAM,EAChG,CAAE,OAAQ,OAAQ,KAAM,wBAAyB,OAAQ,qBAAsB,UAAW,OAAQ,EAGlG,CAAE,OAAQ,OAAQ,KAAM,uBAAwB,OAAQ,mBAAoB,UAAW,OAAQ,EAC/F,CAAE,OAAQ,OAAQ,KAAM,oBAAqB,OAAQ,yBAA0B,UAAW,OAAQ,EAClG,CAAE,OAAQ,MAAO,KAAM,oBAAqB,OAAQ,yBAA0B,UAAW,OAAQ,EACjG,CAAE,OAAQ,OAAQ,KAAM,WAAY,OAAQ,yBAA0B,UAAW,KAAM,EACvF,CAAE,OAAQ,IAAK,KAAM,cAAe,OAAQ,yBAA0B,UAAW,KAAM,EAGvF,CAAE,OAAQ,OAAQ,KAAM,qBAAsB,OAAQ,aAAc,UAAW,OAAQ,EACvF,CAAE,OAAQ,MAAO,KAAM,aAAc,OAAQ,aAAc,UAAW,OAAQ,EAC9E,CAAE,OAAQ,OAAQ,KAAM,WAAY,OAAQ,aAAc,UAAW,OAAQ,EAG7E,CAAE,OAAQ,OAAQ,KAAM,eAAgB,OAAQ,yBAA0B,UAAW,OAAQ,EAC7F,CAAE,OAAQ,OAAQ,KAAM,yBAA0B,OAAQ,qBAAsB,UAAW,OAAQ,EACnG,CAAE,OAAQ,OAAQ,KAAM,oBAAqB,OAAQ,qBAAsB,UAAW,OAAQ,EAC9F,CAAE,OAAQ,OAAQ,KAAM,kBAAmB,OAAQ,aAAc,UAAW,OAAQ,EAGpF,CAAE,OAAQ,OAAQ,KAAM,oBAAqB,OAAQ,aAAc,UAAW,OAAQ,EACtF,CAAE,OAAQ,OAAQ,KAAM,WAAY,OAAQ,aAAc,UAAW,OAAQ,EAC7E,CAAE,OAAQ,OAAQ,KAAM,wBAAyB,OAAQ,aAAc,UAAW,OAAQ,EAG1F,CAAE,OAAQ,OAAQ,KAAM,kBAAmB,OAAQ,aAAc,UAAW,OAAQ,EACpF,CAAE,OAAQ,OAAQ,KAAM,sBAAuB,OAAQ,aAAc,UAAW,OAAQ,EAGxF,CAAE,OAAQ,MAAO,KAAM,qBAAsB,OAAQ,aAAc,UAAW,KAAM,EACrF,CAGY,EAAmB,EAAwB,MAAM,CAAC,CAAC,EAAK,KAC9D,AAAD,CAAI,CAAC,EAAM,MAAM,CAAC,EAAE,CACtB,CAAG,CAAC,EAAM,MAAM,CAAC,CAAG,EAAA,AAAE,EAExB,CAAG,CAAC,EAAM,MAAM,CAAC,CAAC,IAAI,CAAC,GAChB,GACN,CAAC,GAGS,EAAc,EAAwB,GAAG,CAAC,GAAS,EAAM,MAAM,EAG/D,EAAmB,CAC9B,OAAQ,OAAQ,OAAQ,OAAQ,QAAS,OAAQ,OAAQ,MACzD,MAAO,OAAQ,OAAQ,MAAO,MAAO,OAAQ,OAAQ,OACtD"}