import { CandlestickData, StockData, SwingTradingAnalysis } from '@/types/trading'
import { TechnicalIndicators } from './indicators'

export interface StrategySetup {
  strategy: 'overnight_momentum' | 'technical_breakout'
  confidence: number
  entryPrice: number
  stopLoss: number
  targets: number[]
  positionSize: number
  riskAmount: number
  holdingPeriod: 'overnight' | 'days_to_weeks'
  keyLevel: number
  invalidation: string
  notes: string[]
  // Precise trading execution details
  preciseEntry: {
    price: number
    orderType: 'market' | 'limit'
    timing: string
    conditions: string[]
    urgency: 'immediate' | 'wait_for_pullback' | 'breakout_confirmation'
  }
  preciseExit: {
    stopLoss: {
      price: number
      orderType: 'stop' | 'stop_limit'
      reason: string
      triggerConditions: string[]
    }
    takeProfits: Array<{
      price: number
      percentage: number // % of position to sell
      target: string // "R1", "R2", "Extension", etc.
      reasoning: string
      orderType: 'limit' | 'market'
    }>
  }
  riskManagement: {
    maxRiskDollars: number
    accountRiskPercent: number
    sharesForRisk: number
    invalidationPrice: number
    timeStopHours: number
    maxDrawdownPercent: number
  }
  executionPlan: {
    entryInstructions: string[]
    exitInstructions: string[]
    monitoringPoints: string[]
    contingencyPlans: string[]
  }
}

export interface SwingSetupCriteria {
  // Basic filters
  minPrice: number
  minVolume: number
  minMarketCap: number
  minATRPercent: number
  
  // Technical requirements
  above200SMA: boolean
  maxDistanceFrom8EMA: number // in ATR units
  minRoomToResistance: number // in ATR units
  
  // Timing
  scanTimeStart: string // "12:00"
  scanTimeEnd: string   // "16:00"
  
  // Risk management
  maxRiskPerTrade: number // percentage of account
  maxConcurrentPositions: number
}

export class SwingTradingStrategies {
  private static readonly DEFAULT_CRITERIA: SwingSetupCriteria = {
    minPrice: 5.0,
    minVolume: 500000,
    minMarketCap: **********, // $1B (lowered from $800M to be more inclusive)
    minATRPercent: 2.0,
    above200SMA: true,
    maxDistanceFrom8EMA: 2.0, // 2x ATR
    minRoomToResistance: 1.0, // 1 ATR minimum
    scanTimeStart: "12:00",
    scanTimeEnd: "16:00",
    maxRiskPerTrade: 1.0, // 1% max risk
    maxConcurrentPositions: 3
  }

  // Strategy #1: Overnight Momentum Continuation
  static analyzeOvernightMomentum(
    symbol: string,
    candles: CandlestickData[],
    quote: StockData,
    accountSize: number = 100000
  ): StrategySetup | null {
    if (candles.length < 50) return null

    const closes = candles.map(c => c.close)
    const highs = candles.map(c => c.high)
    const lows = candles.map(c => c.low)
    const volumes = candles.map(c => c.volume)
    
    const currentPrice = quote.price
    const currentVolume = quote.volume
    const changePercent = quote.changePercent

    // Calculate technical indicators (adjusted for shorter history)
    const sma50 = TechnicalIndicators.sma(closes, Math.min(50, closes.length - 1)) // Use 50-day instead of 200-day
    const ema8 = TechnicalIndicators.ema(closes, Math.min(8, closes.length - 1))
    const atr = this.calculateATR(candles, Math.min(14, candles.length - 1))

    const current50SMA = sma50[sma50.length - 1]
    const current8EMA = ema8[ema8.length - 1]
    const currentATR = atr[atr.length - 1]

    // Basic qualification filters (using 50-day SMA instead of 200-day)
    if (!this.passesBasicFilters(quote, currentVolume, current50SMA, currentPrice)) {
      return null
    }

    // Check if it's a top intraday gainer (top decile movers)
    if (changePercent < 2.0) return null // Minimum 2% gain for momentum

    // Check distance from 8-EMA (not wildly extended)
    const distanceFrom8EMA = Math.abs(currentPrice - current8EMA) / currentATR
    if (distanceFrom8EMA > this.DEFAULT_CRITERIA.maxDistanceFrom8EMA) return null

    // Look for defended intraday level (simplified - using VWAP proxy)
    const vwap = this.calculateVWAP(candles.slice(-1)[0])
    const keyLevel = Math.max(vwap, currentPrice * 0.98) // Approximate defended level

    // Check if holding gains (>50% of day's range)
    const todayHigh = highs[highs.length - 1]
    const todayLow = lows[lows.length - 1]
    const dayRange = todayHigh - todayLow
    const currentFromLow = currentPrice - todayLow
    const holdingGainsPercent = currentFromLow / dayRange
    
    if (holdingGainsPercent < 0.5) return null // Must hold >50% of range

    // Calculate room to next resistance
    const roomToResistance = this.calculateRoomToResistance(candles, currentPrice, currentATR)
    if (roomToResistance < this.DEFAULT_CRITERIA.minRoomToResistance) return null

    // Position sizing (risk 0.5-1% of account)
    const riskPercent = 0.75 // 0.75% risk for overnight holds
    const stopDistance = currentPrice - keyLevel
    const riskAmount = accountSize * (riskPercent / 100)
    const positionSize = Math.floor(riskAmount / stopDistance)

    // Targets: Pre-market scale at 3-5%, opening hour at 5-8%
    const targets = [
      currentPrice * 1.03, // 3% pre-market target
      currentPrice * 1.05, // 5% opening hour target
      currentPrice * 1.08  // 8% extended target
    ]

    const confidence = this.calculateOvernightConfidence(
      changePercent, holdingGainsPercent, currentVolume, roomToResistance
    )

    return {
      strategy: 'overnight_momentum',
      confidence,
      entryPrice: currentPrice,
      stopLoss: keyLevel,
      targets,
      positionSize,
      riskAmount,
      holdingPeriod: 'overnight',
      keyLevel,
      invalidation: `Daily close below ${keyLevel.toFixed(2)} or gap down below level`,
      notes: [
        'Enter final 30-60 min before close',
        'Exit pre-market on strength or first 45min',
        'Hard stop if gaps below defended level',
        'Scale out aggressively if gaps >1 ATR up'
      ],
      // Precise entry execution
      preciseEntry: {
        price: currentPrice * 0.999, // Slightly below current for better fill
        orderType: 'limit',
        timing: 'Final 30-60 minutes before market close',
        conditions: [
          `Stock holding above ${keyLevel.toFixed(2)} (defended level)`,
          `Volume above ${(currentVolume * 0.8).toLocaleString()} shares`,
          `Price above ${current8EMA.toFixed(2)} (8-EMA)`,
          'No late-day selling pressure'
        ],
        urgency: 'wait_for_pullback'
      },
      // Precise exit execution
      preciseExit: {
        stopLoss: {
          price: keyLevel * 0.995, // Slightly below key level
          orderType: 'stop',
          reason: 'Defended level broken - invalidates setup',
          triggerConditions: [
            'Any close below defended level',
            'Gap down below key level',
            'Heavy selling into close'
          ]
        },
        takeProfits: [
          {
            price: targets[0],
            percentage: 33,
            target: 'T1 - Pre-market (3%)',
            reasoning: 'Take profits on pre-market strength',
            orderType: 'limit'
          },
          {
            price: targets[1],
            percentage: 33,
            target: 'T2 - Opening hour (5%)',
            reasoning: 'Scale out on opening momentum',
            orderType: 'limit'
          },
          {
            price: targets[2],
            percentage: 34,
            target: 'T3 - Extended (8%)',
            reasoning: 'Final exit on extended move',
            orderType: 'limit'
          }
        ]
      },
      // Risk management details
      riskManagement: {
        maxRiskDollars: riskAmount,
        accountRiskPercent: riskPercent,
        sharesForRisk: positionSize,
        invalidationPrice: keyLevel,
        timeStopHours: 18, // Exit by next day close if no movement
        maxDrawdownPercent: 2.0
      },
      // Execution plan
      executionPlan: {
        entryInstructions: [
          '1. Wait for final 30-60 minutes before close',
          '2. Confirm stock is holding defended level',
          '3. Place limit order slightly below current price',
          '4. Cancel if not filled by close'
        ],
        exitInstructions: [
          '1. Set stop-loss immediately after fill',
          '2. Monitor pre-market for gap up',
          '3. Scale out 1/3 at each target level',
          '4. Exit all by 10:15 AM if no momentum'
        ],
        monitoringPoints: [
          'Pre-market price action and volume',
          'Opening gap and first 15-minute candle',
          'Key level defense throughout session',
          'Overall market sentiment'
        ],
        contingencyPlans: [
          'If gaps down: Exit immediately at market open',
          'If gaps up >2%: Scale out more aggressively',
          'If sideways: Exit by 10:15 AM',
          'If market weakness: Tighten stops'
        ]
      }
    }
  }

  // Strategy #2: Technical Breakout Trend-Follow (8-EMA + 200-SMA)
  static analyzeTechnicalBreakout(
    symbol: string,
    candles: CandlestickData[],
    quote: StockData,
    accountSize: number = 100000
  ): StrategySetup | null {
    if (candles.length < 50) return null

    const closes = candles.map(c => c.close)
    const volumes = candles.map(c => c.volume)
    const currentPrice = quote.price

    // Calculate technical indicators (adjusted for shorter history)
    const sma50 = TechnicalIndicators.sma(closes, Math.min(50, closes.length - 1))
    const ema8 = TechnicalIndicators.ema(closes, Math.min(8, closes.length - 1))
    const atr = this.calculateATR(candles, Math.min(14, candles.length - 1))

    const current50SMA = sma50[sma50.length - 1]
    const current8EMA = ema8[ema8.length - 1]
    const currentATR = atr[atr.length - 1]

    // Basic qualification filters (using 50-day SMA)
    if (!this.passesBasicFilters(quote, quote.volume, current50SMA, currentPrice)) {
      return null
    }

    // Check if in clear uptrend above 50-SMA (adjusted from 200-SMA)
    if (currentPrice <= current50SMA) return null

    // Check 8-EMA behavior - should be "hugging" the 8-EMA
    const distanceFrom8EMA = Math.abs(currentPrice - current8EMA)
    const emaDistancePercent = (distanceFrom8EMA / currentPrice) * 100
    
    // Should be close to 8-EMA (within 2-3% for quality trend-follow)
    if (emaDistancePercent > 3.0) return null

    // Check for recent breakout or EMA reclaim
    const recentEMAReclaim = this.checkEMAReclaim(closes, ema8, 5) // Last 5 days
    if (!recentEMAReclaim) return null

    // Volume expansion check
    const avgVolume = TechnicalIndicators.sma(volumes, 20)
    const currentAvgVolume = avgVolume[avgVolume.length - 1]
    const volumeExpansion = quote.volume / currentAvgVolume
    
    if (volumeExpansion < 1.2) return null // Need some volume expansion

    // Calculate room to next resistance
    const roomToResistance = this.calculateRoomToResistance(candles, currentPrice, currentATR)
    if (roomToResistance < 1.5) return null // Need more room for trend-follow

    // Position sizing (risk 1% of account)
    const riskPercent = 1.0
    const stopDistance = currentPrice - current8EMA // Stop at 8-EMA break
    const riskAmount = accountSize * (riskPercent / 100)
    const positionSize = Math.floor(riskAmount / stopDistance)

    // Targets: Scale at resistance levels
    const targets = [
      currentPrice * 1.05, // 5% first target
      currentPrice * 1.10, // 10% second target
      currentPrice * 1.15  // 15% extended target
    ]

    const confidence = this.calculateBreakoutConfidence(
      emaDistancePercent, volumeExpansion, roomToResistance, quote.changePercent
    )

    return {
      strategy: 'technical_breakout',
      confidence,
      entryPrice: currentPrice,
      stopLoss: current8EMA,
      targets,
      positionSize,
      riskAmount,
      holdingPeriod: 'days_to_weeks',
      keyLevel: current8EMA,
      invalidation: `Daily close below 8-EMA (${current8EMA.toFixed(2)})`,
      notes: [
        'Enter on afternoon reclaim of 8-EMA',
        'Add only on higher-low pullbacks to 8-EMA',
        'Scale partials at resistance levels',
        'Exit on daily close below 8-EMA'
      ],
      // Precise entry execution
      preciseEntry: {
        price: current8EMA * 1.002, // Slightly above 8-EMA for confirmation
        orderType: 'limit',
        timing: 'Afternoon reclaim or first pullback to 8-EMA',
        conditions: [
          `Price reclaiming ${current8EMA.toFixed(2)} (8-EMA) with volume`,
          `Above ${current50SMA.toFixed(2)} (50-day SMA)`,
          `Volume expansion above ${(quote.volume * 1.2).toLocaleString()}`,
          'No major resistance overhead'
        ],
        urgency: 'breakout_confirmation'
      },
      // Precise exit execution
      preciseExit: {
        stopLoss: {
          price: current8EMA * 0.998, // Slightly below 8-EMA
          orderType: 'stop',
          reason: '8-EMA breakdown invalidates trend-follow setup',
          triggerConditions: [
            'Daily close below 8-EMA',
            'Intraday break with volume',
            'Loss of 50-SMA support'
          ]
        },
        takeProfits: [
          {
            price: targets[0],
            percentage: 25,
            target: 'R1 - First resistance (5%)',
            reasoning: 'Take partial profits at first resistance',
            orderType: 'limit'
          },
          {
            price: targets[1],
            percentage: 35,
            target: 'R2 - Major resistance (10%)',
            reasoning: 'Scale out at major resistance level',
            orderType: 'limit'
          },
          {
            price: targets[2],
            percentage: 40,
            target: 'R3 - Extension (15%)',
            reasoning: 'Final exit on extended breakout',
            orderType: 'limit'
          }
        ]
      },
      // Risk management details
      riskManagement: {
        maxRiskDollars: riskAmount,
        accountRiskPercent: riskPercent,
        sharesForRisk: positionSize,
        invalidationPrice: current8EMA,
        timeStopHours: 72, // 3 trading days max hold if no progress
        maxDrawdownPercent: 3.0
      },
      // Execution plan
      executionPlan: {
        entryInstructions: [
          '1. Wait for afternoon reclaim of 8-EMA',
          '2. Confirm volume expansion on breakout',
          '3. Place limit order above 8-EMA',
          '4. Only enter on higher-low pullbacks'
        ],
        exitInstructions: [
          '1. Set stop-loss below 8-EMA immediately',
          '2. Scale out 25% at first resistance',
          '3. Trail stop to breakeven after R1',
          '4. Exit remaining on 8-EMA breakdown'
        ],
        monitoringPoints: [
          '8-EMA as dynamic support/resistance',
          'Volume confirmation on moves',
          'Overall market trend alignment',
          'Sector strength/weakness'
        ],
        contingencyPlans: [
          'If fails at resistance: Tighten stops',
          'If market weakness: Exit early',
          'If sector rotation: Consider exit',
          'If extended: Take more profits'
        ]
      }
    }
  }

  // Helper methods
  private static passesBasicFilters(
    quote: StockData,
    volume: number,
    sma50: number,
    price: number
  ): boolean {
    return (
      price >= this.DEFAULT_CRITERIA.minPrice &&
      volume >= this.DEFAULT_CRITERIA.minVolume &&
      (quote.marketCap || 0) >= this.DEFAULT_CRITERIA.minMarketCap &&
      price > sma50 // Using 50-day SMA instead of 200-day for shorter history
    )
  }

  private static calculateATR(candles: CandlestickData[], period: number): number[] {
    const trueRanges: number[] = []
    
    for (let i = 1; i < candles.length; i++) {
      const high = candles[i].high
      const low = candles[i].low
      const prevClose = candles[i - 1].close
      
      const tr = Math.max(
        high - low,
        Math.abs(high - prevClose),
        Math.abs(low - prevClose)
      )
      
      trueRanges.push(tr)
    }
    
    return TechnicalIndicators.sma(trueRanges, period)
  }

  private static calculateVWAP(candle: CandlestickData): number {
    // Simplified VWAP calculation using typical price
    return (candle.high + candle.low + candle.close) / 3
  }

  private static calculateRoomToResistance(
    candles: CandlestickData[], 
    currentPrice: number, 
    atr: number
  ): number {
    // Find recent highs as resistance levels
    const recentHighs = candles.slice(-20).map(c => c.high)
    const maxHigh = Math.max(...recentHighs)
    const roomToHigh = maxHigh - currentPrice
    return roomToHigh / atr
  }

  private static checkEMAReclaim(closes: number[], ema8: number[], lookback: number): boolean {
    // Check if price recently reclaimed 8-EMA
    for (let i = Math.max(0, closes.length - lookback); i < closes.length - 1; i++) {
      if (closes[i] < ema8[i] && closes[i + 1] > ema8[i + 1]) {
        return true // Found a reclaim
      }
    }
    return false
  }

  private static calculateOvernightConfidence(
    changePercent: number,
    holdingGains: number,
    volume: number,
    roomToResistance: number
  ): number {
    let confidence = 50

    // Change percent bonus
    if (changePercent > 5) confidence += 15
    else if (changePercent > 3) confidence += 10
    else if (changePercent > 2) confidence += 5

    // Holding gains bonus
    if (holdingGains > 0.8) confidence += 15
    else if (holdingGains > 0.6) confidence += 10
    else if (holdingGains > 0.5) confidence += 5

    // Volume bonus
    if (volume > 2000000) confidence += 10
    else if (volume > 1000000) confidence += 5

    // Room to resistance
    if (roomToResistance > 2) confidence += 10
    else if (roomToResistance > 1.5) confidence += 5

    return Math.min(95, Math.max(30, confidence))
  }

  private static calculateBreakoutConfidence(
    emaDistance: number,
    volumeExpansion: number,
    roomToResistance: number,
    changePercent: number
  ): number {
    let confidence = 60

    // EMA proximity bonus (closer is better for trend-follow)
    if (emaDistance < 1) confidence += 15
    else if (emaDistance < 2) confidence += 10
    else if (emaDistance < 3) confidence += 5

    // Volume expansion bonus
    if (volumeExpansion > 2) confidence += 15
    else if (volumeExpansion > 1.5) confidence += 10
    else if (volumeExpansion > 1.2) confidence += 5

    // Room to resistance
    if (roomToResistance > 3) confidence += 15
    else if (roomToResistance > 2) confidence += 10
    else if (roomToResistance > 1.5) confidence += 5

    // Positive momentum
    if (changePercent > 2) confidence += 5

    return Math.min(95, Math.max(40, confidence))
  }
}
