module.exports=[18230,e=>{"use strict";e.s(["swingScanner",()=>s]);var t=e.i(17673),r=e.i(78006),n=e.i(29547),a=e.i(86678),o=e.i(97669);let s=new class{fmpAPI;polygonAPI;constructor(){this.fmpAPI=new n.FMPAPI(process.env.FMP_API_KEY),this.polygonAPI=new r.PolygonAPI(process.env.POLYGON_API_KEY)}async scanStocks(e,t=5){let r=Date.now(),n=[],a=[],o={};console.log(`Starting scan of ${e.length} stocks...`);for(let r=0;r<e.length;r+=t){let s=e.slice(r,r+t),i=s.map(e=>this.scanSingleStock(e));(await Promise.allSettled(i)).forEach((e,t)=>{let r=s[t];if("fulfilled"===e.status&&e.value){n.push(e.value);let t=e.value.sector;o[t]=(o[t]||0)+1}else a.push(r),console.warn(`Failed to scan ${r}:`,"rejected"===e.status?e.reason:"Unknown error")}),r+t<e.length&&await new Promise(e=>setTimeout(e,1e3))}n.sort((e,t)=>t.score-e.score),n.forEach((e,t)=>{e.rank=t+1});let s=Date.now()-r;return{totalScanned:e.length,successfulScans:n.length,failedScans:a.length,topOpportunities:n.slice(0,20),sectorBreakdown:o,scanDuration:s}}async scanSingleStock(e){try{let[r,n]=await Promise.all([this.fmpAPI.getStockQuote(e),this.getHistoricalData(e)]);if(!n||n.length<50)throw Error("Insufficient historical data");let a=t.SwingTradingAnalyzer.analyzeSwingTrade(e,n),o=this.calculateSwingScore(r,a);return{symbol:e,name:r.name,sector:this.getSectorForSymbol(e),quote:r,analysis:a,score:o,rank:0,scanTime:new Date().toISOString()}}catch(t){return console.error(`Error scanning ${e}:`,t),null}}async getHistoricalData(e){let t=(0,a.format)(new Date,"yyyy-MM-dd"),r=(0,a.format)((0,o.subDays)(new Date,100),"yyyy-MM-dd");try{return await this.polygonAPI.getHistoricalData(e,"day",1,r,t)}catch(t){throw console.warn(`Polygon failed for ${e}, trying alternative...`),t}}calculateSwingScore(e,t){let r;r=0+.4*t.confidence;let n=t.riskRewardRatio;n>=3?r+=20:n>=2?r+=15:n>=1.5?r+=10:n>=1&&(r+=5);let a=t.indicators.find(e=>"Volume"===e.name);a&&("BUY"===a.signal&&a.value>1.5?r+=15:"BUY"===a.signal?r+=10:"NEUTRAL"===a.signal&&(r+=5)),"BULLISH"===t.trend?r+=15:"BEARISH"===t.trend?r+=10:r+=5;let o=t.indicators.filter(e=>"BUY"===e.signal).length,s=t.indicators.filter(e=>"SELL"===e.signal).length,i=t.indicators.length;o>s?r+=o/i*10:s>o&&(r+=s/i*8);let l=Math.abs(e.changePercent);switch(l>5?r+=10:l>2?r+=7:l>1?r+=5:l<.5&&(r-=5),t.recommendation){case"STRONG_BUY":r+=10;break;case"BUY":r+=7;break;case"STRONG_SELL":r+=8;break;case"SELL":r+=5;break;case"NO_TRADE":r-=10}return Math.max(0,Math.min(100,r))}getSectorForSymbol(e){return["MSFT","NVDA","GOOG","GOOGL","META","AVGO","TSM","ORCL","CSCO","AMD","ASML","MU","LRCX","PLTR","APP","NET","DDOG","ZS","SHOP","SOUN","IONQ","RGTI","RIOT","HUT","IREN","ASTS","NBIS"].includes(e)?"Technology":["JPM","BAC","MS","SCHW","C","HOOD","SOFI","TIGR","FUTU"].includes(e)?"Financial Services":["JNJ","ABBV","MRK","GILD"].includes(e)?"Healthcare":["GE","CAT","BA","GEV","UAL","VRT","RKLB"].includes(e)?"Industrial":["AEM","NEM","PAAS","BTG","HL","MP","AG"].includes(e)?"Materials":["AMZN","DIS","SBUX","MO","DASH","GM","NCLH","CELH","LEVI","ELF","ETSY","W"].includes(e)?"Consumer":["NFLX","RBLX","BILI"].includes(e)?"Communication Services":["CEG","VST","CCJ"].includes(e)?"Energy":"Other"}async quickScan(e){return(await this.scanStocks(e,8)).topOpportunities}}},49443,(e,t,r)=>{},56525,e=>{"use strict";e.s(["handler",()=>P,"patchFetch",()=>T,"routeModule",()=>y,"serverHooks",()=>O,"workAsyncStorage",()=>v,"workUnitAsyncStorage",()=>C],56525);var t=e.i(11971),r=e.i(6780),n=e.i(51842),a=e.i(62950),o=e.i(21346),s=e.i(30506),i=e.i(63077),l=e.i(34765),c=e.i(64182),u=e.i(85062),d=e.i(51548),p=e.i(95133),h=e.i(8819),g=e.i(41050),R=e.i(93695);e.i(96641);var S=e.i(3893);e.s(["GET",()=>w],82621);var E=e.i(59169),f=e.i(18230),m=e.i(5744);async function w(e,{params:t}){try{let{sector:r}=await t,{searchParams:n}=new URL(e.url),a=parseInt(n.get("limit")||"10"),o=decodeURIComponent(r),s=m.STOCKS_BY_SECTOR[o];if(!s||0===s.length)return E.NextResponse.json({error:`No stocks found for sector: ${o}`},{status:404});let i=s.map(e=>e.symbol);console.log(`Starting sector scan for ${o}: ${i.length} stocks...`);let l=await f.swingScanner.scanStocks(i,5),c={...l,sector:o,topOpportunities:l.topOpportunities.slice(0,a)};return E.NextResponse.json(c)}catch(e){return console.error("Error in sector scanner API:",e),E.NextResponse.json({error:"Failed to perform sector stock scan"},{status:500})}}var A=e.i(82621);let y=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/scanner/sector/[sector]/route",pathname:"/api/scanner/sector/[sector]",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/swing-trader-ai/src/app/api/scanner/sector/[sector]/route.ts",nextConfigOutput:"",userland:A}),{workAsyncStorage:v,workUnitAsyncStorage:C,serverHooks:O}=y;function T(){return(0,n.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:C})}async function P(e,t,n){var E;let f="/api/scanner/sector/[sector]/route";f=f.replace(/\/index$/,"")||"/";let m=await y.prepare(e,t,{srcPage:f,multiZoneDraftMode:!1});if(!m)return t.statusCode=400,t.end("Bad Request"),null==n.waitUntil||n.waitUntil.call(n,Promise.resolve()),null;let{buildId:w,params:A,nextConfig:v,isDraftMode:C,prerenderManifest:O,routerServerContext:T,isOnDemandRevalidate:P,revalidateOnlyGenerated:I,resolvedPathname:N}=m,M=(0,s.normalizeAppPath)(f),x=!!(O.dynamicRoutes[M]||O.routes[N]);if(x&&!C){let e=!!O.routes[N],t=O.dynamicRoutes[M];if(t&&!1===t.fallback&&!e)throw new R.NoFallbackError}let U=null;!x||y.isDev||C||(U="/index"===(U=N)?"/":U);let k=!0===y.isDev||!x,L=x&&!k,H=e.method||"GET",D=(0,o.getTracer)(),_=D.getActiveScopeSpan(),b={params:A,prerenderManifest:O,renderOpts:{experimental:{cacheComponents:!!v.experimental.cacheComponents,authInterrupts:!!v.experimental.authInterrupts},supportsDynamicResponse:k,incrementalCache:(0,a.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(E=v.experimental)?void 0:E.cacheLife,isRevalidate:L,waitUntil:n.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,n)=>y.onRequestError(e,t,n,T)},sharedContext:{buildId:w}},B=new i.NodeNextRequest(e),G=new i.NodeNextResponse(t),F=l.NextRequestAdapter.fromNodeNextRequest(B,(0,l.signalFromNodeResponse)(t));try{let s=async r=>y.handle(F,b).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let n=D.getRootSpanAttributes();if(!n)return;if(n.get("next.span_type")!==c.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${n.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let a=n.get("next.route");if(a){let e=`${H} ${a}`;r.setAttributes({"next.route":a,"http.route":a,"next.span_name":e}),r.updateName(e)}else r.updateName(`${H} ${e.url}`)}),i=async o=>{var i,l;let c=async({previousCacheEntry:r})=>{try{if(!(0,a.getRequestMeta)(e,"minimalMode")&&P&&I&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let i=await s(o);e.fetchMetrics=b.renderOpts.fetchMetrics;let l=b.renderOpts.pendingWaitUntil;l&&n.waitUntil&&(n.waitUntil(l),l=void 0);let c=b.renderOpts.collectedTags;if(!x)return await (0,d.sendResponse)(B,G,i,b.renderOpts.pendingWaitUntil),null;{let e=await i.blob(),t=(0,p.toNodeOutgoingHttpHeaders)(i.headers);c&&(t[g.NEXT_CACHE_TAGS_HEADER]=c),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==b.renderOpts.collectedRevalidate&&!(b.renderOpts.collectedRevalidate>=g.INFINITE_CACHE)&&b.renderOpts.collectedRevalidate,n=void 0===b.renderOpts.collectedExpire||b.renderOpts.collectedExpire>=g.INFINITE_CACHE?void 0:b.renderOpts.collectedExpire;return{value:{kind:S.CachedRouteKind.APP_ROUTE,status:i.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:n}}}}catch(t){throw(null==r?void 0:r.isStale)&&await y.onRequestError(e,t,{routerKind:"App Router",routePath:f,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:L,isOnDemandRevalidate:P})},T),t}},R=await y.handleResponse({req:e,nextConfig:v,cacheKey:U,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:O,isRoutePPREnabled:!1,isOnDemandRevalidate:P,revalidateOnlyGenerated:I,responseGenerator:c,waitUntil:n.waitUntil});if(!x)return null;if((null==R||null==(i=R.value)?void 0:i.kind)!==S.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==R||null==(l=R.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,a.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",P?"REVALIDATED":R.isMiss?"MISS":R.isStale?"STALE":"HIT"),C&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let E=(0,p.fromNodeOutgoingHttpHeaders)(R.value.headers);return(0,a.getRequestMeta)(e,"minimalMode")&&x||E.delete(g.NEXT_CACHE_TAGS_HEADER),!R.cacheControl||t.getHeader("Cache-Control")||E.get("Cache-Control")||E.set("Cache-Control",(0,h.getCacheControlHeader)(R.cacheControl)),await (0,d.sendResponse)(B,G,new Response(R.value.body,{headers:E,status:R.value.status||200})),null};_?await i(_):await D.withPropagatedContext(e.headers,()=>D.trace(c.BaseServerSpan.handleRequest,{spanName:`${H} ${e.url}`,kind:o.SpanKind.SERVER,attributes:{"http.method":H,"http.target":e.url}},i))}catch(t){if(t instanceof R.NoFallbackError||await y.onRequestError(e,t,{routerKind:"App Router",routePath:M,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:L,isOnDemandRevalidate:P})}),x)throw t;return await (0,d.sendResponse)(B,G,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=swing-trader-ai_b9ea14bc._.js.map