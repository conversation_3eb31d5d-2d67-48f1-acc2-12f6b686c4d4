{"version": 3, "sources": ["turbopack:///[project]/swing-trader-ai/src/lib/catalystDetection.ts", "turbopack:///[project]/swing-trader-ai/src/lib/preMarketGapScanner.ts"], "sourcesContent": ["import { \n  Catalyst, \n  CatalystType, \n  CatalystTier, \n  CatalystImpact, \n  CatalystImpactMeasurement \n} from '@/types/trading'\nimport { FMPAPI } from './fmp'\nimport { PolygonAPI } from './polygon'\n\nexport class CatalystDetectionEngine {\n  private fmpAPI: FMPAPI\n  private polygonAPI: PolygonAPI\n  private catalystCache: Map<string, Catalyst[]> = new Map()\n  private impactMeasurements: Map<string, CatalystImpactMeasurement> = new Map()\n\n  constructor(fmpApiKey?: string, polygonApiKey?: string) {\n    this.fmpAPI = new FMPAPI(fmpApiKey)\n    this.polygonAPI = new PolygonAPI(polygonApiKey)\n  }\n\n  /**\n   * Detect catalysts for a specific symbol\n   */\n  async detectCatalysts(symbol: string): Promise<Catalyst[]> {\n    const catalysts: Catalyst[] = []\n\n    try {\n      // Check cache first (5-minute cache)\n      const cacheKey = `${symbol}_${Math.floor(Date.now() / (5 * 60 * 1000))}`\n      if (this.catalystCache.has(cacheKey)) {\n        return this.catalystCache.get(cacheKey)!\n      }\n\n      // Detect different types of catalysts in parallel\n      const [\n        earningsCatalysts,\n        newsCatalysts,\n        analystCatalysts,\n        insiderCatalysts,\n        secFilingCatalysts\n      ] = await Promise.all([\n        this.detectEarningsCatalysts(symbol),\n        this.detectNewsCatalysts(symbol),\n        this.detectAnalystCatalysts(symbol),\n        this.detectInsiderCatalysts(symbol),\n        this.detectSECFilingCatalysts(symbol)\n      ])\n\n      catalysts.push(\n        ...earningsCatalysts,\n        ...newsCatalysts,\n        ...analystCatalysts,\n        ...insiderCatalysts,\n        ...secFilingCatalysts\n      )\n\n      // Sort by quality score and freshness\n      catalysts.sort((a, b) => {\n        const freshnessWeight = this.getFreshnessWeight(a.freshness) - this.getFreshnessWeight(b.freshness)\n        if (freshnessWeight !== 0) return freshnessWeight\n        return b.qualityScore - a.qualityScore\n      })\n\n      // Cache results\n      this.catalystCache.set(cacheKey, catalysts)\n\n      return catalysts\n    } catch (error) {\n      console.error(`Error detecting catalysts for ${symbol}:`, error)\n      return []\n    }\n  }\n\n  /**\n   * Detect earnings-related catalysts\n   */\n  private async detectEarningsCatalysts(symbol: string): Promise<Catalyst[]> {\n    const catalysts: Catalyst[] = []\n\n    try {\n      // Get recent earnings data from FMP\n      const earningsData = await this.fmpAPI.getEarningsCalendar(symbol, 30) // Last 30 days\n      \n      for (const earnings of earningsData) {\n        if (this.isEarningsBeat(earnings)) {\n          const catalyst: Catalyst = {\n            id: `earnings_${symbol}_${earnings.date}`,\n            symbol,\n            type: 'earnings_beat_guidance',\n            tier: 'tier_1', // Highest priority\n            impact: 'bullish',\n            title: `${symbol} Beats Earnings Expectations`,\n            description: `Q${earnings.quarter} earnings beat: EPS ${earnings.actualEPS} vs ${earnings.estimatedEPS} expected`,\n            source: 'FMP Earnings Data',\n            announcementTime: earnings.date,\n            discoveredTime: new Date().toISOString(),\n            qualityScore: this.calculateEarningsQualityScore(earnings),\n            freshness: this.calculateFreshness(earnings.date),\n            estimatedDuration: 'short_term',\n            verified: true,\n            tags: ['earnings', 'beat', 'guidance'],\n            metadata: {\n              actualEPS: earnings.actualEPS,\n              estimatedEPS: earnings.estimatedEPS,\n              beatPercent: ((earnings.actualEPS - earnings.estimatedEPS) / earnings.estimatedEPS) * 100,\n              guidanceRaised: earnings.guidanceRaised || false\n            }\n          }\n          catalysts.push(catalyst)\n        }\n      }\n    } catch (error) {\n      console.error(`Error detecting earnings catalysts for ${symbol}:`, error)\n    }\n\n    return catalysts\n  }\n\n  /**\n   * Detect news-related catalysts\n   */\n  private async detectNewsCatalysts(symbol: string): Promise<Catalyst[]> {\n    const catalysts: Catalyst[] = []\n\n    try {\n      // Get recent news from FMP\n      const newsData = await this.fmpAPI.getStockNews(symbol, 50) // Last 50 articles\n      \n      for (const news of newsData) {\n        const catalystType = this.classifyNewsAsCatalyst(news)\n        if (catalystType) {\n          const catalyst: Catalyst = {\n            id: `news_${symbol}_${news.publishedDate}_${news.title.slice(0, 20).replace(/\\s+/g, '_')}`,\n            symbol,\n            type: catalystType.type,\n            tier: catalystType.tier,\n            impact: catalystType.impact,\n            title: news.title,\n            description: news.text?.slice(0, 200) + '...' || news.title,\n            source: news.site,\n            sourceUrl: news.url,\n            announcementTime: news.publishedDate,\n            discoveredTime: new Date().toISOString(),\n            qualityScore: this.calculateNewsQualityScore(news, catalystType.type),\n            freshness: this.calculateFreshness(news.publishedDate),\n            estimatedDuration: this.estimateNewsDuration(catalystType.type),\n            verified: this.isReliableNewsSource(news.site),\n            tags: this.extractNewsKeywords(news.title + ' ' + (news.text || '')),\n            metadata: {\n              site: news.site,\n              sentiment: news.sentiment || 'neutral'\n            }\n          }\n          catalysts.push(catalyst)\n        }\n      }\n    } catch (error) {\n      console.error(`Error detecting news catalysts for ${symbol}:`, error)\n    }\n\n    return catalysts\n  }\n\n  /**\n   * Detect analyst upgrade/downgrade catalysts\n   */\n  private async detectAnalystCatalysts(symbol: string): Promise<Catalyst[]> {\n    const catalysts: Catalyst[] = []\n\n    try {\n      // Get analyst recommendations from FMP\n      const analystData = await this.fmpAPI.getAnalystRecommendations(symbol, 30)\n      \n      for (const recommendation of analystData) {\n        if (this.isSignificantAnalystChange(recommendation)) {\n          const isUpgrade = recommendation.newGrade > recommendation.previousGrade\n          const catalyst: Catalyst = {\n            id: `analyst_${symbol}_${recommendation.date}_${recommendation.analystCompany}`,\n            symbol,\n            type: isUpgrade ? 'analyst_upgrade' : 'analyst_downgrade',\n            tier: 'tier_2',\n            impact: isUpgrade ? 'bullish' : 'bearish',\n            title: `${recommendation.analystCompany} ${isUpgrade ? 'Upgrades' : 'Downgrades'} ${symbol}`,\n            description: `${recommendation.analystName} at ${recommendation.analystCompany} ${isUpgrade ? 'upgraded' : 'downgraded'} to ${recommendation.newGrade}`,\n            source: 'FMP Analyst Data',\n            announcementTime: recommendation.date,\n            discoveredTime: new Date().toISOString(),\n            qualityScore: this.calculateAnalystQualityScore(recommendation),\n            freshness: this.calculateFreshness(recommendation.date),\n            estimatedDuration: 'medium_term',\n            verified: true,\n            tags: ['analyst', isUpgrade ? 'upgrade' : 'downgrade', recommendation.analystCompany.toLowerCase()],\n            metadata: {\n              analystCompany: recommendation.analystCompany,\n              analystName: recommendation.analystName,\n              previousGrade: recommendation.previousGrade,\n              newGrade: recommendation.newGrade,\n              priceTarget: recommendation.priceTarget\n            }\n          }\n          catalysts.push(catalyst)\n        }\n      }\n    } catch (error) {\n      console.error(`Error detecting analyst catalysts for ${symbol}:`, error)\n    }\n\n    return catalysts\n  }\n\n  /**\n   * Detect insider trading catalysts\n   */\n  private async detectInsiderCatalysts(symbol: string): Promise<Catalyst[]> {\n    const catalysts: Catalyst[] = []\n\n    try {\n      // Get insider trading data from FMP\n      const insiderData = await this.fmpAPI.getInsiderTrading(symbol, 30)\n      \n      for (const trade of insiderData) {\n        if (this.isSignificantInsiderTrade(trade)) {\n          const isBuying = trade.transactionType.toLowerCase().includes('buy') || \n                          trade.transactionType.toLowerCase().includes('purchase')\n          \n          const catalyst: Catalyst = {\n            id: `insider_${symbol}_${trade.filingDate}_${trade.reportingName}`,\n            symbol,\n            type: isBuying ? 'insider_buying' : 'insider_selling',\n            tier: 'tier_2',\n            impact: isBuying ? 'bullish' : 'bearish',\n            title: `${trade.reportingName} ${isBuying ? 'Buys' : 'Sells'} ${symbol} Shares`,\n            description: `${trade.reportingName} (${trade.typeOfOwner}) ${trade.transactionType} ${trade.securitiesTransacted} shares at $${trade.price}`,\n            source: 'SEC Insider Trading Filings',\n            announcementTime: trade.filingDate,\n            discoveredTime: new Date().toISOString(),\n            qualityScore: this.calculateInsiderQualityScore(trade),\n            freshness: this.calculateFreshness(trade.filingDate),\n            estimatedDuration: 'medium_term',\n            verified: true,\n            tags: ['insider', isBuying ? 'buying' : 'selling', trade.typeOfOwner.toLowerCase()],\n            metadata: {\n              reportingName: trade.reportingName,\n              typeOfOwner: trade.typeOfOwner,\n              transactionType: trade.transactionType,\n              securitiesTransacted: trade.securitiesTransacted,\n              price: trade.price,\n              dollarValue: trade.securitiesTransacted * trade.price\n            }\n          }\n          catalysts.push(catalyst)\n        }\n      }\n    } catch (error) {\n      console.error(`Error detecting insider catalysts for ${symbol}:`, error)\n    }\n\n    return catalysts\n  }\n\n  /**\n   * Detect SEC filing catalysts\n   */\n  private async detectSECFilingCatalysts(symbol: string): Promise<Catalyst[]> {\n    const catalysts: Catalyst[] = []\n\n    try {\n      // Get recent SEC filings from FMP\n      const filings = await this.fmpAPI.getSECFilings(symbol, 30)\n      \n      for (const filing of filings) {\n        if (this.isSignificantSECFiling(filing)) {\n          const catalyst: Catalyst = {\n            id: `sec_${symbol}_${filing.filedDate}_${filing.type}`,\n            symbol,\n            type: 'sec_filing',\n            tier: this.getSECFilingTier(filing.type),\n            impact: this.getSECFilingImpact(filing.type),\n            title: `${symbol} Files ${filing.type}`,\n            description: `${filing.type} filing: ${filing.description || 'SEC regulatory filing'}`,\n            source: 'SEC EDGAR Database',\n            sourceUrl: filing.link,\n            announcementTime: filing.filedDate,\n            discoveredTime: new Date().toISOString(),\n            qualityScore: this.calculateSECFilingQualityScore(filing),\n            freshness: this.calculateFreshness(filing.filedDate),\n            estimatedDuration: this.estimateSECFilingDuration(filing.type),\n            verified: true,\n            tags: ['sec', 'filing', filing.type.toLowerCase()],\n            metadata: {\n              filingType: filing.type,\n              cik: filing.cik,\n              acceptedDate: filing.acceptedDate\n            }\n          }\n          catalysts.push(catalyst)\n        }\n      }\n    } catch (error) {\n      console.error(`Error detecting SEC filing catalysts for ${symbol}:`, error)\n    }\n\n    return catalysts\n  }\n\n  // Helper methods for catalyst classification and scoring\n  private getFreshnessWeight(freshness: string): number {\n    switch (freshness) {\n      case 'fresh': return 3\n      case 'moderate': return 2\n      case 'stale': return 1\n      default: return 0\n    }\n  }\n\n  private calculateFreshness(dateString: string): 'fresh' | 'moderate' | 'stale' {\n    const date = new Date(dateString)\n    const now = new Date()\n    const hoursAgo = (now.getTime() - date.getTime()) / (1000 * 60 * 60)\n    \n    if (hoursAgo < 24) return 'fresh'\n    if (hoursAgo < 72) return 'moderate'\n    return 'stale'\n  }\n\n  private isEarningsBeat(earnings: any): boolean {\n    return earnings.actualEPS > earnings.estimatedEPS && \n           (earnings.guidanceRaised || earnings.actualEPS > earnings.estimatedEPS * 1.05)\n  }\n\n  private calculateEarningsQualityScore(earnings: any): number {\n    let score = 5 // Base score\n    \n    // Beat percentage\n    const beatPercent = ((earnings.actualEPS - earnings.estimatedEPS) / earnings.estimatedEPS) * 100\n    if (beatPercent > 20) score += 3\n    else if (beatPercent > 10) score += 2\n    else if (beatPercent > 5) score += 1\n    \n    // Guidance raised\n    if (earnings.guidanceRaised) score += 2\n    \n    // Revenue beat\n    if (earnings.actualRevenue > earnings.estimatedRevenue) score += 1\n    \n    return Math.min(10, score)\n  }\n\n  private classifyNewsAsCatalyst(news: any): { type: CatalystType, tier: CatalystTier, impact: CatalystImpact } | null {\n    const title = news.title.toLowerCase()\n    const text = (news.text || '').toLowerCase()\n    const content = title + ' ' + text\n\n    // FDA/Drug related\n    if (content.includes('fda') && (content.includes('approval') || content.includes('approved'))) {\n      return { type: 'fda_approval', tier: 'tier_1', impact: 'bullish' }\n    }\n    \n    if (content.includes('trial') && (content.includes('positive') || content.includes('successful'))) {\n      return { type: 'drug_trial_results', tier: 'tier_1', impact: 'bullish' }\n    }\n\n    // Contract/Partnership\n    if (content.includes('contract') && (content.includes('win') || content.includes('awarded'))) {\n      return { type: 'contract_win', tier: 'tier_1', impact: 'bullish' }\n    }\n    \n    if (content.includes('partnership') || content.includes('collaboration')) {\n      return { type: 'partnership', tier: 'tier_1', impact: 'bullish' }\n    }\n\n    // M&A\n    if (content.includes('merger') || content.includes('acquisition') || content.includes('buyout')) {\n      return { type: 'merger_acquisition', tier: 'tier_1', impact: 'bullish' }\n    }\n\n    // Stock split\n    if (content.includes('stock split') || content.includes('share split')) {\n      return { type: 'stock_split', tier: 'tier_2', impact: 'bullish' }\n    }\n\n    return null\n  }\n\n  private calculateNewsQualityScore(news: any, catalystType: CatalystType): number {\n    let score = 5 // Base score\n    \n    // Source reliability\n    if (this.isReliableNewsSource(news.site)) score += 2\n    \n    // Catalyst type importance\n    if (['fda_approval', 'merger_acquisition', 'earnings_beat_guidance'].includes(catalystType)) {\n      score += 2\n    }\n    \n    // Sentiment\n    if (news.sentiment === 'positive') score += 1\n    else if (news.sentiment === 'negative') score -= 1\n    \n    return Math.max(1, Math.min(10, score))\n  }\n\n  private isReliableNewsSource(site: string): boolean {\n    const reliableSources = [\n      'reuters.com', 'bloomberg.com', 'wsj.com', 'cnbc.com', \n      'marketwatch.com', 'yahoo.com', 'sec.gov', 'fda.gov'\n    ]\n    return reliableSources.some(source => site.toLowerCase().includes(source))\n  }\n\n  private extractNewsKeywords(text: string): string[] {\n    const keywords = []\n    const content = text.toLowerCase()\n    \n    const keywordMap = {\n      'earnings': ['earnings', 'eps', 'revenue', 'profit'],\n      'fda': ['fda', 'approval', 'drug', 'trial'],\n      'merger': ['merger', 'acquisition', 'buyout', 'takeover'],\n      'partnership': ['partnership', 'collaboration', 'alliance'],\n      'contract': ['contract', 'deal', 'agreement'],\n      'upgrade': ['upgrade', 'raised', 'increased'],\n      'downgrade': ['downgrade', 'lowered', 'reduced']\n    }\n    \n    for (const [category, terms] of Object.entries(keywordMap)) {\n      if (terms.some(term => content.includes(term))) {\n        keywords.push(category)\n      }\n    }\n    \n    return keywords\n  }\n\n  private estimateNewsDuration(catalystType: CatalystType): 'intraday' | 'short_term' | 'medium_term' | 'long_term' {\n    switch (catalystType) {\n      case 'earnings_beat_guidance':\n      case 'fda_approval':\n      case 'merger_acquisition':\n        return 'short_term'\n      case 'analyst_upgrade':\n      case 'analyst_downgrade':\n      case 'partnership':\n        return 'medium_term'\n      case 'stock_split':\n        return 'long_term'\n      default:\n        return 'short_term'\n    }\n  }\n\n  private isSignificantAnalystChange(recommendation: any): boolean {\n    // Check if it's a meaningful grade change\n    const gradeChange = Math.abs(recommendation.newGrade - recommendation.previousGrade)\n    return gradeChange >= 1 && recommendation.priceTarget > 0\n  }\n\n  private calculateAnalystQualityScore(recommendation: any): number {\n    let score = 5 // Base score\n    \n    // Analyst firm reputation (simplified)\n    const topFirms = ['goldman sachs', 'morgan stanley', 'jp morgan', 'bank of america']\n    if (topFirms.some(firm => recommendation.analystCompany.toLowerCase().includes(firm))) {\n      score += 2\n    }\n    \n    // Grade change magnitude\n    const gradeChange = Math.abs(recommendation.newGrade - recommendation.previousGrade)\n    if (gradeChange >= 2) score += 2\n    else if (gradeChange >= 1) score += 1\n    \n    // Price target change\n    if (recommendation.priceTargetChange > 10) score += 1\n    \n    return Math.min(10, score)\n  }\n\n  private isSignificantInsiderTrade(trade: any): boolean {\n    const dollarValue = trade.securitiesTransacted * trade.price\n    return dollarValue >= 1000000 && // $1M+ transactions\n           trade.typeOfOwner !== 'Other' // Exclude generic \"Other\" category\n  }\n\n  private calculateInsiderQualityScore(trade: any): number {\n    let score = 5 // Base score\n    \n    const dollarValue = trade.securitiesTransacted * trade.price\n    \n    // Transaction size\n    if (dollarValue >= ********) score += 3 // $10M+\n    else if (dollarValue >= 5000000) score += 2 // $5M+\n    else if (dollarValue >= 1000000) score += 1 // $1M+\n    \n    // Insider type\n    if (trade.typeOfOwner.toLowerCase().includes('ceo') || \n        trade.typeOfOwner.toLowerCase().includes('cfo')) {\n      score += 2\n    } else if (trade.typeOfOwner.toLowerCase().includes('director')) {\n      score += 1\n    }\n    \n    return Math.min(10, score)\n  }\n\n  private isSignificantSECFiling(filing: any): boolean {\n    const significantFilings = ['8-K', '10-K', '10-Q', '13D', '13G', 'S-1', 'S-4']\n    return significantFilings.includes(filing.type)\n  }\n\n  private getSECFilingTier(filingType: string): CatalystTier {\n    const tier1Filings = ['8-K', '13D', 'S-4'] // Material events, activist investors, M&A\n    const tier2Filings = ['10-K', '10-Q', '13G'] // Regular reports, passive investors\n    \n    if (tier1Filings.includes(filingType)) return 'tier_1'\n    if (tier2Filings.includes(filingType)) return 'tier_2'\n    return 'tier_3'\n  }\n\n  private getSECFilingImpact(filingType: string): CatalystImpact {\n    // Most SEC filings are neutral until analyzed\n    return 'neutral'\n  }\n\n  private calculateSECFilingQualityScore(filing: any): number {\n    let score = 5 // Base score\n    \n    // Filing type importance\n    if (['8-K', '13D'].includes(filing.type)) score += 2\n    else if (['10-K', '10-Q'].includes(filing.type)) score += 1\n    \n    return Math.min(10, score)\n  }\n\n  private estimateSECFilingDuration(filingType: string): 'intraday' | 'short_term' | 'medium_term' | 'long_term' {\n    switch (filingType) {\n      case '8-K': return 'short_term' // Material events\n      case '13D': return 'medium_term' // Activist investors\n      case 'S-4': return 'long_term' // M&A registration\n      default: return 'medium_term'\n    }\n  }\n}\n", "import { PreMarketGapScan, Catalyst } from '@/types/trading'\nimport { FMPAPI } from './fmp'\nimport { PolygonAPI } from './polygon'\nimport { CatalystDetectionEngine } from './catalystDetection'\n\nexport class PreMarketGapScanner {\n  private fmpAPI: FMPAPI\n  private polygonAPI: PolygonAPI\n  private catalystEngine: CatalystDetectionEngine\n  \n  // Default universe of 65+ stocks to scan\n  private readonly SCAN_UNIVERSE = [\n    // Mega caps\n    'AAPL', 'MSFT', 'GOOGL', 'GOOG', 'AMZN', 'NVDA', 'META', 'TSLA', 'BRK.B', 'UNH',\n    'JNJ', 'XOM', 'JPM', 'V', 'PG', 'HD', 'CVX', 'MA', 'BAC', 'ABBV',\n    'PFE', 'AVGO', 'KO', 'MRK', 'PEP', 'TMO', 'COST', 'DIS', 'ABT', 'ACN',\n    'MCD', 'CSCO', 'LIN', 'VZ', 'ADBE', 'WMT', 'CRM', 'NFLX', 'DHR', 'NKE',\n    'TXN', 'NEE', 'BMY', 'ORCL', 'PM', 'RTX', 'UPS', 'QCOM', 'T', 'LOW',\n    \n    // High-beta growth stocks\n    'AMD', 'CRM', 'SNOW', 'PLTR', 'ROKU', 'ZM', 'DOCU', 'PTON', 'SHOP', 'SQ',\n    'PYPL', 'UBER', 'LYFT', 'ABNB', 'COIN', 'RBLX', 'U', 'DKNG', 'CRWD', 'ZS',\n    \n    // Biotech/Pharma (catalyst-heavy)\n    'GILD', 'BIIB', 'REGN', 'VRTX', 'ILMN', 'MRNA', 'BNTX', 'AMGN', 'CELG', 'ISRG'\n  ]\n\n  constructor(fmpApiKey?: string, polygonApiKey?: string) {\n    this.fmpAPI = new FMPAPI(fmpApiKey)\n    this.polygonAPI = new PolygonAPI(polygonApiKey)\n    this.catalystEngine = new CatalystDetectionEngine(fmpApiKey, polygonApiKey)\n  }\n\n  /**\n   * Run comprehensive pre-market gap scan\n   */\n  async runGapScan(customUniverse?: string[]): Promise<PreMarketGapScan[]> {\n    const universe = customUniverse || this.SCAN_UNIVERSE\n    const results: PreMarketGapScan[] = []\n\n    console.log(`🔍 Starting pre-market gap scan on ${universe.length} symbols...`)\n\n    try {\n      // Get pre-market quotes for all symbols\n      const quotes = await this.fmpAPI.getMultiplePreMarketQuotes(universe)\n      \n      // Process each quote in parallel\n      const scanPromises = quotes.map(quote => this.processSingleStock(quote))\n      const scanResults = await Promise.all(scanPromises)\n      \n      // Filter out null results and sort by gap percentage\n      const validResults = scanResults\n        .filter((result): result is PreMarketGapScan => result !== null)\n        .sort((a, b) => b.gapPercent - a.gapPercent)\n\n      console.log(`✅ Gap scan complete. Found ${validResults.length} results.`)\n      \n      return validResults\n    } catch (error) {\n      console.error('Error running gap scan:', error)\n      return []\n    }\n  }\n\n  /**\n   * Process a single stock for gap scan criteria\n   */\n  private async processSingleStock(quote: any): Promise<PreMarketGapScan | null> {\n    try {\n      const symbol = quote.symbol\n      const currentPrice = quote.preMarketPrice || quote.price\n      const previousClose = quote.previousClose\n      \n      if (!currentPrice || !previousClose || previousClose <= 0) {\n        return null\n      }\n\n      const gapPercent = ((currentPrice - previousClose) / previousClose) * 100\n      \n      // Quick filter: only process stocks with 3%+ gaps\n      if (gapPercent < 3.0) {\n        return null\n      }\n\n      // Get additional data\n      const [companyProfile, catalysts] = await Promise.all([\n        this.fmpAPI.getCompanyProfile(symbol),\n        this.catalystEngine.detectCatalysts(symbol)\n      ])\n\n      if (!companyProfile) {\n        return null\n      }\n\n      // Calculate pre-market metrics\n      const preMarketVolume = quote.volume || 0\n      const avgDailyVolume = quote.avgVolume || 1\n      const preMarketDollarVolume = preMarketVolume * currentPrice\n      const marketCap = quote.marketCap || companyProfile.mktCap || 0\n\n      // Check all criteria\n      const criteriaChecks = {\n        priceAbove1Dollar: currentPrice > 1.0,\n        gapAbove3Percent: gapPercent >= 3.0,\n        marketCapAbove800M: marketCap >= *********,\n        preMarketVolumeAbove20K: preMarketVolume >= 20000,\n        preMarketDollarVolumeAbove1M: preMarketDollarVolume >= 1000000,\n        excludesPennyStocks: currentPrice > 1.0 && marketCap >= *********,\n        hasCatalyst: catalysts.length > 0\n      }\n\n      const meetsAllCriteria = Object.values(criteriaChecks).every(check => check)\n\n      // Get the best catalyst (highest quality score)\n      const bestCatalyst = catalysts.length > 0 \n        ? catalysts.reduce((best, current) => \n            current.qualityScore > best.qualityScore ? current : best\n          )\n        : undefined\n\n      const result: PreMarketGapScan = {\n        symbol,\n        name: companyProfile.companyName || symbol,\n        sector: companyProfile.sector || 'Unknown',\n        price: currentPrice,\n        previousClose,\n        gapPercent,\n        preMarketHigh: currentPrice, // Simplified - would need intraday data for actual PMH\n        preMarketLow: currentPrice * 0.98, // Estimated based on current price\n        preMarketVolume,\n        preMarketDollarVolume,\n        marketCap,\n        averageDailyVolume: avgDailyVolume,\n        catalyst: bestCatalyst,\n        scanTime: new Date().toISOString(),\n        meetsAllCriteria,\n        criteriaChecks\n      }\n\n      return result\n    } catch (error) {\n      console.error(`Error processing ${quote.symbol}:`, error)\n      return null\n    }\n  }\n\n  /**\n   * Get filtered results that meet all Perfect-Pick criteria\n   */\n  async getPerfectPickCandidates(customUniverse?: string[]): Promise<PreMarketGapScan[]> {\n    const allResults = await this.runGapScan(customUniverse)\n    \n    return allResults.filter(result => \n      result.meetsAllCriteria && \n      result.catalyst && \n      result.catalyst.tier === 'tier_1' // Only highest tier catalysts\n    )\n  }\n\n  /**\n   * Get results by gap percentage ranges\n   */\n  async getGapRangeResults(\n    minGap: number = 3, \n    maxGap: number = 15, \n    customUniverse?: string[]\n  ): Promise<PreMarketGapScan[]> {\n    const allResults = await this.runGapScan(customUniverse)\n    \n    return allResults.filter(result => \n      result.gapPercent >= minGap && \n      result.gapPercent <= maxGap\n    )\n  }\n\n  /**\n   * Get results by catalyst type\n   */\n  async getCatalystTypeResults(\n    catalystTypes: string[], \n    customUniverse?: string[]\n  ): Promise<PreMarketGapScan[]> {\n    const allResults = await this.runGapScan(customUniverse)\n    \n    return allResults.filter(result => \n      result.catalyst && \n      catalystTypes.includes(result.catalyst.type)\n    )\n  }\n\n  /**\n   * Get scheduled scan times (4 AM, 6 AM, 8 AM, 9 AM EST)\n   */\n  getScheduledScanTimes(): Date[] {\n    const now = new Date()\n    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())\n    \n    const scanTimes = [\n      new Date(today.getTime() + 4 * 60 * 60 * 1000), // 4 AM EST\n      new Date(today.getTime() + 6 * 60 * 60 * 1000), // 6 AM EST\n      new Date(today.getTime() + 8 * 60 * 60 * 1000), // 8 AM EST\n      new Date(today.getTime() + 9 * 60 * 60 * 1000)  // 9 AM EST\n    ]\n    \n    // Adjust for EST (UTC-5) or EDT (UTC-4)\n    const isEDT = this.isDaylightSavingTime(now)\n    const offsetHours = isEDT ? 4 : 5\n    \n    return scanTimes.map(time => \n      new Date(time.getTime() + offsetHours * 60 * 60 * 1000)\n    )\n  }\n\n  /**\n   * Check if current time is during daylight saving time\n   */\n  private isDaylightSavingTime(date: Date): boolean {\n    const year = date.getFullYear()\n    \n    // DST starts second Sunday in March\n    const dstStart = new Date(year, 2, 1) // March 1st\n    dstStart.setDate(dstStart.getDate() + (7 - dstStart.getDay()) + 7) // Second Sunday\n    \n    // DST ends first Sunday in November\n    const dstEnd = new Date(year, 10, 1) // November 1st\n    dstEnd.setDate(dstEnd.getDate() + (7 - dstEnd.getDay())) // First Sunday\n    \n    return date >= dstStart && date < dstEnd\n  }\n\n  /**\n   * Get real-time updates for existing scan results\n   */\n  async updateScanResults(existingResults: PreMarketGapScan[]): Promise<PreMarketGapScan[]> {\n    const symbols = existingResults.map(result => result.symbol)\n    const updatedQuotes = await this.fmpAPI.getMultiplePreMarketQuotes(symbols)\n    \n    const updatedResults: PreMarketGapScan[] = []\n    \n    for (const quote of updatedQuotes) {\n      const existingResult = existingResults.find(r => r.symbol === quote.symbol)\n      if (!existingResult) continue\n      \n      const currentPrice = quote.preMarketPrice || quote.price\n      const gapPercent = ((currentPrice - quote.previousClose) / quote.previousClose) * 100\n      \n      const updatedResult: PreMarketGapScan = {\n        ...existingResult,\n        price: currentPrice,\n        gapPercent,\n        preMarketVolume: quote.volume || 0,\n        preMarketDollarVolume: (quote.volume || 0) * currentPrice,\n        scanTime: new Date().toISOString()\n      }\n      \n      // Re-check criteria with updated data\n      updatedResult.criteriaChecks.gapAbove3Percent = gapPercent >= 3.0\n      updatedResult.criteriaChecks.preMarketVolumeAbove20K = updatedResult.preMarketVolume >= 20000\n      updatedResult.criteriaChecks.preMarketDollarVolumeAbove1M = updatedResult.preMarketDollarVolume >= 1000000\n      updatedResult.meetsAllCriteria = Object.values(updatedResult.criteriaChecks).every(check => check)\n      \n      updatedResults.push(updatedResult)\n    }\n    \n    return updatedResults.sort((a, b) => b.gapPercent - a.gapPercent)\n  }\n\n  /**\n   * Get summary statistics for scan results\n   */\n  getScanSummary(results: PreMarketGapScan[]) {\n    const totalScanned = this.SCAN_UNIVERSE.length\n    const gapsFound = results.length\n    const perfectPicks = results.filter(r => r.meetsAllCriteria).length\n    const withCatalysts = results.filter(r => r.catalyst).length\n    \n    const avgGap = results.length > 0 \n      ? results.reduce((sum, r) => sum + r.gapPercent, 0) / results.length \n      : 0\n    \n    const sectorBreakdown = results.reduce((acc, result) => {\n      acc[result.sector] = (acc[result.sector] || 0) + 1\n      return acc\n    }, {} as Record<string, number>)\n    \n    const catalystTypeBreakdown = results\n      .filter(r => r.catalyst)\n      .reduce((acc, result) => {\n        const type = result.catalyst!.type\n        acc[type] = (acc[type] || 0) + 1\n        return acc\n      }, {} as Record<string, number>)\n    \n    return {\n      totalScanned,\n      gapsFound,\n      perfectPicks,\n      withCatalysts,\n      avgGap: Math.round(avgGap * 100) / 100,\n      sectorBreakdown,\n      catalystTypeBreakdown,\n      scanTime: new Date().toISOString()\n    }\n  }\n}\n"], "names": [], "mappings": "8EAOA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MAEO,OAAM,EACH,MAAc,CACd,UAAsB,AACtB,eAAyC,IAAI,GAAK,CAClD,mBAA6D,IAAI,GAAK,AAE9E,aAAY,CAAkB,CAAE,CAAsB,CAAE,CACtD,IAAI,CAAC,MAAM,CAAG,IAAI,EAAA,MAAM,CAAC,GACzB,IAAI,CAAC,UAAU,CAAG,IAAI,EAAA,UAAU,CAAC,EACnC,CAKA,MAAM,gBAAgB,CAAc,CAAuB,CACzD,IAAM,EAAwB,EAAE,CAEhC,GAAI,CAEF,IAAM,EAAW,CAAA,EAAG,EAAO,CAAC,EAAE,KAAK,KAAK,CAAC,KAAK,GAAG,GAAM,EAAD,EAAK,CAAS,CAAI,CACxE,EADgE,CAC5D,GADgE,CAC5D,CAAC,aAAa,CAAC,GAAG,CAAC,GACzB,OAAO,CAD6B,GACzB,CAAC,aAAa,CAAC,GAAG,CAAC,GAIhC,GAAM,CACJ,EACA,EACA,EACA,EACA,EACD,CAAG,MAAM,QAAQ,GAAG,CAAC,CACpB,IAAI,CAAC,uBAAuB,CAAC,GAC7B,IAAI,CAAC,mBAAmB,CAAC,GACzB,IAAI,CAAC,sBAAsB,CAAC,GAC5B,IAAI,CAAC,sBAAsB,CAAC,GAC5B,IAAI,CAAC,wBAAwB,CAAC,GAC/B,EAoBD,OAlBA,EAAU,IAAI,IACT,KACA,KACA,KACA,KACA,GAIL,EAAU,IAAI,CAAC,CAAC,EAAG,KACjB,IAAM,EAAkB,IAAI,CAAC,kBAAkB,CAAC,EAAE,SAAS,EAAI,IAAI,CAAC,kBAAkB,CAAC,EAAE,SAAS,SAClG,AAAwB,GAAG,CAAvB,EAA8B,EAC3B,EAAE,YAAY,CAAG,EAAE,YAC5B,AADwC,GAIxC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAAU,GAE1B,CACT,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,CAAC,8BAA8B,EAAE,EAAO,CAAC,CAAC,CAAE,GACnD,EAAE,AACX,CACF,CAKA,MAAc,wBAAwB,CAAc,CAAuB,CACzE,IAAM,EAAwB,EAAE,CAEhC,GAAI,CAIF,IAAK,IAAM,KAFU,MAAM,CAEJ,GAFQ,CAAC,MAAM,CAAC,EAEF,iBAFqB,CAAC,EAAQ,GAAA,EAGjE,AAHqE,GAGjE,IAAI,CAAC,OAH2E,OAG7D,CAAC,GAAW,CACjC,IAAM,EAAqB,CACzB,GAAI,CAAC,SAAS,EAAE,EAAO,CAAC,EAAE,EAAS,IAAI,CAAA,CAAE,QACzC,EACA,KAAM,yBACN,KAAM,SACN,OAAQ,UACR,MAAO,CAAA,EAAG,EAAO,4BAA4B,CAAC,CAC9C,YAAa,CAAC,CAAC,EAAE,EAAS,OAAO,CAAC,oBAAoB,EAAE,EAAS,SAAS,CAAC,IAAI,EAAE,EAAS,YAAY,CAAC,SAAS,CAAC,CACjH,OAAQ,oBACR,iBAAkB,EAAS,IAAI,CAC/B,eAAgB,IAAI,OAAO,WAAW,GACtC,aAAc,IAAI,CAAC,6BAA6B,CAAC,GACjD,UAAW,IAAI,CAAC,kBAAkB,CAAC,EAAS,IAAI,EAChD,kBAAmB,aACnB,UAAU,EACV,KAAM,CAAC,WAAY,OAAQ,WAAW,CACtC,SAAU,CACR,UAAW,EAAS,SAAS,CAC7B,aAAc,EAAS,YAAY,CACnC,YAAc,CAAC,EAAS,SAAS,CAAG,EAAS,YAAA,AAAY,EAAI,EAAS,YAAY,CAAI,IACtF,eAAgB,EAAS,cAAc,GAAI,CAC7C,CACF,EACA,EAAU,IAAI,CAAC,EACjB,CAEJ,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,CAAC,uCAAuC,EAAE,EAAO,CAAC,CAAC,CAAE,EACrE,CAEA,OAAO,CACT,CAKA,MAAc,oBAAoB,CAAc,CAAuB,CACrE,IAAM,EAAwB,EAAE,CAEhC,GAAI,CAIF,IAAK,IAAM,KAFM,GAEE,GAFI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,EAAQ,GAAA,EAAI,AAE/B,CAC3B,IAAM,EAAe,IAAI,CAAC,OAHmD,eAG7B,CAAC,GACjD,GAAI,EAAc,CAChB,IAAM,EAAqB,CACzB,GAAI,CAAC,KAAK,EAAE,EAAO,CAAC,EAAE,EAAK,aAAa,CAAC,CAAC,EAAE,EAAK,KAAK,CAAC,KAAK,CAAC,EAAG,IAAI,OAAO,CAAC,OAAQ,KAAA,CAAM,QAC1F,EACA,KAAM,EAAa,IAAI,CACvB,KAAM,EAAa,IAAI,CACvB,OAAQ,EAAa,MAAM,CAC3B,MAAO,EAAK,KAAK,CACjB,YAAa,EAAK,IAAI,EAAE,MAAM,EAAG,KAAO,MACxC,GADiD,IACzC,CAD8C,CACzC,IAAI,AAD0C,CAE3D,UAAW,EAAK,GAAG,CACnB,iBAAkB,EAAK,aAAa,CACpC,eAAgB,IAAI,OAAO,WAAW,GACtC,aAAc,IAAI,CAAC,yBAAyB,CAAC,EAAM,EAAa,IAAI,EACpE,UAAW,IAAI,CAAC,kBAAkB,CAAC,EAAK,aAAa,EACrD,kBAAmB,IAAI,CAAC,oBAAoB,CAAC,EAAa,IAAI,EAC9D,SAAU,IAAI,CAAC,oBAAoB,CAAC,EAAK,IAAI,EAC7C,KAAM,IAAI,CAAC,mBAAmB,CAAC,EAAK,KAAK,CAAG,KAAO,CAAD,CAAM,IAAI,EAAI,EAAA,CAAE,EAClE,SAAU,CACR,KAAM,EAAK,IAAI,CACf,UAAW,EAAK,SAAS,EAAI,SAC/B,CACF,EACA,EAAU,IAAI,CAAC,EACjB,CACF,CACF,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,EAAO,CAAC,CAAC,CAAE,EACjE,CAEA,OAAO,CACT,CAKA,MAAc,uBAAuB,CAAc,CAAuB,CACxE,IAAM,EAAwB,EAAE,CAEhC,GAAI,CAIF,IAAK,IAAM,KAFS,MAAM,IAAI,CAAC,EAEF,IAFQ,CAAC,OAEI,kBAFqB,CAAC,EAAQ,GAAA,EAGtE,GAAI,IAAI,CAAC,0BAA0B,CAAC,GAAiB,CACnD,IAAM,EAAY,EAAe,QAAQ,CAAG,EAAe,aAAa,CAClE,EAAqB,CACzB,GAAI,CAAC,QAAQ,EAAE,EAAO,CAAC,EAAE,EAAe,IAAI,CAAC,CAAC,EAAE,EAAe,cAAc,CAAA,CAAE,QAC/E,EACA,KAAM,EAAY,kBAAoB,oBACtC,KAAM,SACN,OAAQ,EAAY,UAAY,UAChC,MAAO,CAAA,EAAG,EAAe,cAAc,CAAC,CAAC,EAAE,EAAY,WAAa,aAAa,CAAC,EAAE,EAAA,CAAQ,CAC5F,YAAa,CAAA,EAAG,EAAe,WAAW,CAAC,IAAI,EAAE,EAAe,cAAc,CAAC,CAAC,EAAE,EAAY,WAAa,aAAa,IAAI,EAAE,EAAe,QAAQ,CAAA,CAAE,CACvJ,OAAQ,mBACR,iBAAkB,EAAe,IAAI,CACrC,eAAgB,IAAI,OAAO,WAAW,GACtC,aAAc,IAAI,CAAC,4BAA4B,CAAC,GAChD,UAAW,IAAI,CAAC,kBAAkB,CAAC,EAAe,IAAI,EACtD,kBAAmB,cACnB,UAAU,EACV,KAAM,CAAC,UAAW,EAAY,UAAY,YAAa,EAAe,cAAc,CAAC,WAAW,GAAG,CACnG,SAAU,CACR,eAAgB,EAAe,cAAc,CAC7C,YAAa,EAAe,WAAW,CACvC,cAAe,EAAe,aAAa,CAC3C,SAAU,EAAe,QAAQ,CACjC,YAAa,EAAe,WAAW,AACzC,CACF,EACA,EAAU,IAAI,CAAC,EACjB,CAEJ,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,CAAC,sCAAsC,EAAE,EAAO,CAAC,CAAC,CAAE,EACpE,CAEA,OAAO,CACT,CAKA,MAAc,uBAAuB,CAAc,CAAuB,CACxE,IAAM,EAAwB,EAAE,CAEhC,GAAI,CAIF,IAAK,IAAM,KAFS,IAEA,EAFM,IAAI,CAAC,KAEE,CAFI,CAAC,iBAAiB,CAAC,EAAQ,GAAA,EAG9D,GAAI,IAAI,CAAC,yBAAyB,CAAC,GAAQ,CACzC,IAAM,EAAW,EAAM,eAAe,CAAC,WAAW,GAAG,QAAQ,CAAC,QAC9C,EAAM,eAAe,CAAC,WAAW,GAAG,QAAQ,CAAC,YAEvD,EAAqB,CACzB,GAAI,CAAC,QAAQ,EAAE,EAAO,CAAC,EAAE,EAAM,UAAU,CAAC,CAAC,EAAE,EAAM,aAAa,CAAA,CAAE,QAClE,EACA,KAAM,EAAW,iBAAmB,kBACpC,KAAM,SACN,OAAQ,EAAW,UAAY,UAC/B,MAAO,CAAA,EAAG,EAAM,aAAa,CAAC,CAAC,EAAE,EAAW,OAAS,QAAQ,CAAC,EAAE,EAAO,OAAO,CAAC,CAC/E,YAAa,CAAA,EAAG,EAAM,aAAa,CAAC,EAAE,EAAE,EAAM,WAAW,CAAC,EAAE,EAAE,EAAM,eAAe,CAAC,CAAC,EAAE,EAAM,oBAAoB,CAAC,YAAY,EAAE,EAAM,KAAK,CAAA,CAAE,CAC7I,OAAQ,8BACR,iBAAkB,EAAM,UAAU,CAClC,eAAgB,IAAI,OAAO,WAAW,GACtC,aAAc,IAAI,CAAC,4BAA4B,CAAC,GAChD,UAAW,IAAI,CAAC,kBAAkB,CAAC,EAAM,UAAU,EACnD,kBAAmB,cACnB,UAAU,EACV,KAAM,CAAC,UAAW,EAAW,SAAW,UAAW,EAAM,WAAW,CAAC,WAAW,GAAG,CACnF,SAAU,CACR,cAAe,EAAM,aAAa,CAClC,YAAa,EAAM,WAAW,CAC9B,gBAAiB,EAAM,eAAe,CACtC,qBAAsB,EAAM,oBAAoB,CAChD,MAAO,EAAM,KAAK,CAClB,YAAa,EAAM,oBAAoB,CAAG,EAAM,KAAK,AACvD,CACF,EACA,EAAU,IAAI,CAAC,EACjB,CAEJ,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,CAAC,sCAAsC,EAAE,EAAO,CAAC,CAAC,CAAE,EACpE,CAEA,OAAO,CACT,CAKA,MAAc,yBAAyB,CAAc,CAAuB,CAC1E,IAAM,EAAwB,EAAE,CAEhC,GAAI,CAIF,IAAK,IAAM,KAFK,KAEK,CAFC,IAAI,CAAC,EAEG,IAFG,CAAC,aAAa,CAAC,EAAQ,GAAA,EAGtD,GAAI,IAAI,CAAC,sBAAsB,CAAC,GAAS,CACvC,IAAM,EAAqB,CACzB,GAAI,CAAC,IAAI,EAAE,EAAO,CAAC,EAAE,EAAO,SAAS,CAAC,CAAC,EAAE,EAAO,IAAI,CAAA,CAAE,QACtD,EACA,KAAM,aACN,KAAM,IAAI,CAAC,gBAAgB,CAAC,EAAO,IAAI,EACvC,OAAQ,IAAI,CAAC,kBAAkB,CAAC,EAAO,IAAI,EAC3C,MAAO,CAAA,EAAG,EAAO,OAAO,EAAE,EAAO,IAAI,CAAA,CAAE,CACvC,YAAa,CAAA,EAAG,EAAO,IAAI,CAAC,SAAS,EAAE,EAAO,WAAW,EAAI,wBAAA,CAAyB,CACtF,OAAQ,qBACR,UAAW,EAAO,IAAI,CACtB,iBAAkB,EAAO,SAAS,CAClC,eAAgB,IAAI,OAAO,WAAW,GACtC,aAAc,IAAI,CAAC,8BAA8B,CAAC,GAClD,UAAW,IAAI,CAAC,kBAAkB,CAAC,EAAO,SAAS,EACnD,kBAAmB,IAAI,CAAC,yBAAyB,CAAC,EAAO,IAAI,EAC7D,SAAU,GACV,KAAM,CAAC,MAAO,SAAU,EAAO,IAAI,CAAC,WAAW,GAAG,CAClD,SAAU,CACR,WAAY,EAAO,IAAI,CACvB,IAAK,EAAO,GAAG,CACf,aAAc,EAAO,YAAY,AACnC,CACF,EACA,EAAU,IAAI,CAAC,EACjB,CAEJ,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,CAAC,yCAAyC,EAAE,EAAO,CAAC,CAAC,CAAE,EACvE,CAEA,OAAO,CACT,CAGQ,mBAAmB,CAAiB,CAAU,CACpD,OAAQ,GACN,IAAK,QAAS,OAAO,CACrB,KAAK,WAAY,OAAO,CACxB,KAAK,QAAS,OAAO,CACrB,SAAS,OAAO,CAClB,CACF,CAEQ,mBAAmB,CAAkB,CAAkC,CAC7E,IAAM,EAAO,IAAI,KAAK,GAEhB,EAAW,CADL,AACM,IADF,OACM,OAAO,GAAK,EAAK,OAAO,EAAA,CAAE,CAAK,GAAD,IAAQ,KAAK,AAEjE,AAAI,EAF+D,AAEpD,GAAW,CAAP,OACf,EAAW,GAAW,CAAP,UACZ,OACT,CAEQ,eAAe,CAAa,CAAW,CAC7C,OAAO,EAAS,SAAS,CAAG,EAAS,YAAY,GACzC,CAAD,CAAU,cAAc,EAAI,EAAS,SAAS,CAA2B,KAAxB,EAAS,YAAY,AAAG,CAAI,AACtF,CAEQ,8BAA8B,CAAa,CAAU,CAC3D,IAAI,EAAQ,EAAE,AAGR,EAAe,CAAC,EAAS,QAHJ,CAGa,CAAG,EAAS,YAAA,AAAY,EAAI,EAAS,YAAY,CAAI,IAW7F,OAVI,EAAc,GAAI,GAAS,EACtB,EAAc,GAAI,GAAS,EAC3B,EAAc,IAAG,IAAS,EAG/B,EAAS,cAAc,GAAE,GAAS,GAGlC,EAAS,aAAa,CAAG,EAAS,gBAAgB,GAAE,IAAS,EAE1D,KAAK,GAAG,CAAC,GAAI,EACtB,CAEQ,uBAAuB,CAAS,CAA6E,CACnH,IAEM,EAFA,AAAQ,AAEE,EAFG,KAAK,CAAC,WAAW,GAEZ,IADX,CAAC,CACgB,CADX,IAAI,EAAI,EAAA,CAAE,CAAE,WAAW,UAI1C,AAAI,EAAQ,QAAQ,CAAC,SAAW,CAAD,CAAS,QAAQ,CAAC,aAAe,EAAQ,QAAQ,CAAC,WAAA,CAAW,CACnF,CAAE,CADoF,IAC9E,eAAgB,KAAM,SAAU,OAAQ,SAAU,EAG/D,EAAQ,QAAQ,CAAC,WAAa,CAAD,CAAS,QAAQ,CAAC,aAAe,EAAQ,QAAQ,CAAC,aAAA,CAAa,CACvF,CAAE,CADwF,IAClF,qBAAsB,KAAM,SAAU,OAAQ,SAAU,EAIrE,EAAQ,QAAQ,CAAC,cAAgB,CAAD,CAAS,QAAQ,CAAC,QAAU,EAAQ,QAAQ,CAAC,UAAA,CAAU,CAClF,CAAE,CADmF,IAC7E,eAAgB,KAAM,SAAU,OAAQ,SAAU,EAG/D,EAAQ,QAAQ,CAAC,gBAAkB,EAAQ,QAAQ,CAAC,iBAC/C,CAAE,AAD+D,KACzD,cAAe,KAAM,SAAU,OAAQ,SAAU,EAI9D,EAAQ,QAAQ,CAAC,WAAa,EAAQ,QAAQ,CAAC,gBAAkB,EAAQ,QAAQ,CAAC,UAC7E,CADwF,AACtF,KAAM,qBAAsB,KAAM,SAAU,OAAQ,SAAU,EAIrE,EAAQ,QAAQ,CAAC,gBAAkB,EAAQ,QAAQ,CAAC,eAC/C,CAAE,AAD6D,KACvD,cAAe,KAAM,SAAU,OAAQ,SAAU,EAG3D,IACT,CAEQ,0BAA0B,CAAS,CAAE,CAA0B,CAAU,CAC/E,IAAI,EAAQ,EAAE,AAcd,OAXI,IAAI,CAAC,CAHkB,mBAGE,CAAC,EAAK,IAAI,GAAG,KAAS,EAG/C,CAAC,eAAgB,qBAAsB,yBAAyB,CAAC,QAAQ,CAAC,KAC5E,IAAS,EAIY,IALsE,SAKzF,EAAK,SAAS,CAAiB,GAAS,EAChB,aAAnB,EAAK,SAAS,GAAiB,IAAS,EAE1C,KAAK,GAAG,CAAC,EAAG,KAAK,GAAG,CAAC,GAAI,GAClC,CAEQ,qBAAqB,CAAY,CAAW,CAKlD,MAAO,AAJiB,CACtB,cAAe,gBAAiB,UAAW,WAC3C,kBAAmB,YAAa,UAAW,UAC5C,CACsB,IAAI,CAAC,GAAU,EAAK,WAAW,GAAG,QAAQ,CAAC,GACpE,CAEQ,oBAAoB,CAAY,CAAY,CAClD,IAAM,EAAW,EAAE,CACb,EAAU,EAAK,WAAW,GAYhC,IAAK,GAAM,CAAC,EAAU,EAAM,GAAI,OAAO,OAAO,CAV3B,AAU4B,CAT7C,SAAY,CAAC,CAS6C,UATjC,MAAO,UAAW,SAAS,CACpD,IAAO,CAAC,MAAO,WAAY,OAAQ,QAAQ,CAC3C,OAAU,CAAC,SAAU,cAAe,SAAU,WAAW,CACzD,YAAe,CAAC,cAAe,gBAAiB,WAAW,CAC3D,SAAY,CAAC,WAAY,OAAQ,YAAY,CAC7C,QAAW,CAAC,UAAW,SAAU,YAAY,CAC7C,UAAa,CAAC,YAAa,UAAW,UAAU,AAClD,GAGM,EAAM,IAAI,CAAC,GAAQ,EAAQ,QAAQ,CAAC,KACtC,EAAS,CADqC,GACjC,CAAC,GAIlB,OAAO,CACT,CAEQ,qBAAqB,CAA0B,CAA2D,CAChH,OAAQ,GACN,IAAK,yBACL,IAAK,eACL,IAAK,qBAQL,QAPE,MAAO,YACT,KAAK,kBACL,IAAK,oBACL,IAAK,cACH,MAAO,aACT,KAAK,cACH,MAAO,WAGX,CACF,CAEQ,2BAA2B,CAAmB,CAAW,CAG/D,OADoB,AACb,KADkB,GAAG,CAAC,EAAe,QAAQ,CAAG,EAAe,aAAa,GAC7D,GAAK,EAAe,WAAW,CAAG,CAC1D,CAEQ,6BAA6B,CAAmB,CAAU,CAChE,IAAI,EAAQ,EAAE,AAGG,AACb,CADc,YAHS,IAGQ,iBAAkB,YAAa,kBAAkB,CACvE,IAAI,CAAC,GAAQ,EAAe,cAAc,CAAC,WAAW,GAAG,QAAQ,CAAC,MAC7E,EADqF,EAC5E,EAIX,IAAM,EAAc,KAAK,GAAG,CAAC,EAAe,QAAQ,CAAG,EAAe,aAAa,EAOnF,OANI,GAAe,EAAG,GAAS,EACtB,GAAe,IAAG,IAAS,EAGhC,EAAe,iBAAiB,CAAG,KAAI,IAAS,EAE7C,KAAK,GAAG,CAAC,GAAI,EACtB,CAEQ,0BAA0B,CAAU,CAAW,CAErD,OADoB,AACb,EADmB,oBAAoB,CAAG,EAAM,KAAK,EACtC,KACO,MADI,EACI,EAA9B,EAAM,WAAW,AAC1B,CAEQ,EAJ+C,iBACmB,UAGrC,CAAU,CAAU,CACvD,IAAI,EAAQ,EAAE,AAER,EAAc,EAAM,SAFC,WAEmB,CAAG,EAAM,KAAK,CAe5D,OAZI,GAAe,IAAU,GAAS,EAC7B,CAD+B,EAChB,IAAS,EADe,CACN,EACjC,CADmC,EACpB,KAD2B,CAClB,IAAS,EAGtC,CAHwC,CAGlC,MAHyC,KAG9B,CAAC,WAAW,GAAG,QAAQ,CAAC,QACzC,EAAM,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,OAC3C,CADmD,EAC1C,EACA,EAAM,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,aAAa,CAC/D,IAAS,EAGJ,KAAK,GAAG,CAAC,GAAI,EACtB,CAEQ,uBAAuB,CAAW,CAAW,CAEnD,MAD2B,AACpB,CADqB,MAAO,OAAQ,OAAQ,MAAO,MAAO,MAAO,MAAM,CACpD,QAAQ,CAAC,EAAO,IAAI,CAChD,CAEQ,iBAAiB,CAAkB,CAAgB,OACpC,AAGrB,AAAI,CAHkB,MAAO,MAAO,MAAM,CAGzB,AAH0B,QAGlB,CAAC,GAAoB,SAC1C,AAHiB,CAEkB,AAFjB,OAAQ,OAAQ,MAAM,CAAC,AAG5B,AAJqE,QAI7D,CAAC,GAAoB,SACvC,CADgC,OAEzC,CAEQ,OAP4E,YAOzD,CAAkB,CAAkB,CAE7D,MAAO,SACT,CAEQ,+BAA+B,CAAW,CAAU,CAC1D,IAAI,EAAQ,EAAE,AAMd,MAHI,CAAC,MAHsB,AAGf,MAAM,CAAC,QAAQ,CAAC,EAAO,IAAI,EAAG,GAAS,EAC1C,CAAC,OAAQ,OAAO,CAAC,QAAQ,CAAC,EAAO,IAAI,IAAG,IAAS,EAEnD,KAAK,GAAG,CAAC,GAAI,EACtB,CAEQ,0BAA0B,CAAkB,CAA2D,CAC7G,OAAQ,GACN,IAAK,MAAO,MAAO,YACnB,CADgC,IAC3B,MAEL,QAHkD,AACtC,MAAO,aACnB,CADiC,IAC5B,MAAO,MAAO,KADmC,MAGxD,CAFiC,AAGnC,CACF,kBAJwD,2CCxhBxD,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MAEO,OAAM,EACH,MAAc,CACd,UAAsB,CACtB,cAAuC,CAG9B,cAAgB,CAE/B,OAAQ,OAAQ,QAAS,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,QAAS,MAC1E,MAAO,MAAO,MAAO,IAAK,KAAM,KAAM,MAAO,KAAM,MAAO,OAC1D,MAAO,OAAQ,KAAM,MAAO,MAAO,MAAO,OAAQ,MAAO,MAAO,MAChE,MAAO,OAAQ,MAAO,KAAM,OAAQ,MAAO,MAAO,OAAQ,MAAO,MACjE,MAAO,MAAO,MAAO,OAAQ,KAAM,MAAO,MAAO,OAAQ,IAAK,MAG9D,MAAO,MAAO,OAAQ,OAAQ,OAAQ,KAAM,OAAQ,OAAQ,OAAQ,KACpE,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,IAAK,OAAQ,OAAQ,KAGrE,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OACzE,AAED,aAAY,CAAkB,CAAE,CAAsB,CAAE,CACtD,IAAI,CAAC,MAAM,CAAG,IAAI,EAAA,MAAM,CAAC,GACzB,IAAI,CAAC,UAAU,CAAG,IAAI,EAAA,UAAU,CAAC,GACjC,IAAI,CAAC,cAAc,CAAG,IAAI,EAAA,uBAAuB,CAAC,EAAW,EAC/D,CAKA,MAAM,WAAW,CAAyB,CAA+B,CACvE,IAAM,EAAW,GAAkB,IAAI,CAAC,aAAa,CAGrD,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,EAAS,MAAM,CAAC,WAAW,CAAC,EAE9E,GAAI,CAKF,IAAM,EAAe,CAHN,MAAM,IAAI,CAAC,MAAM,CAAC,0BAA0B,CAAC,EAAA,EAGhC,GAAG,CAAC,GAAS,IAAI,CAAC,kBAAkB,CAAC,IAI3D,EAAe,CAHD,MAAM,QAAQ,GAAG,CAAC,EAAA,EAInC,MAAM,CAAC,AAAC,GAAkD,OAAX,GAC/C,IAAI,CAAC,CAAC,EAAG,IAAM,EAAE,UAAU,CAAG,EAAE,UAAU,EAI7C,OAFA,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,EAAa,MAAM,CAAC,SAAS,CAAC,EAEjE,CACT,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,0BAA2B,GAClC,EAAE,AACX,CACF,CAKA,MAAc,mBAAmB,CAAU,CAAoC,CAC7E,GAAI,CACF,IAAM,EAAS,EAAM,MAAM,CACrB,EAAe,EAAM,cAAc,EAAI,EAAM,KAAK,CAClD,EAAgB,EAAM,aAAa,CAEzC,GAAI,CAAC,GAAgB,CAAC,GAAiB,GAAiB,EACtD,CADyD,MAClD,KAGT,IAAM,EAAc,CAAC,EAAe,CAAA,CAAa,CAAI,EAAiB,IAGtE,GAAI,EAAa,EACf,GADoB,IACb,KAIT,GAAM,CAAC,EAAgB,EAAU,CAAG,MAAM,QAAQ,GAAG,CAAC,CACpD,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,GAC9B,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,GACrC,EAED,GAAI,CAAC,EACH,OAAO,KAIT,EALqB,EAKf,EAAkB,EAAM,MAAM,EAAI,EAClC,EAAiB,EAAM,SAAS,EAAI,EACpC,EAAwB,EAAkB,EAC1C,EAAY,EAAM,SAAS,EAAI,EAAe,MAAM,EAAI,EAGxD,EAAiB,CACrB,kBAAmB,EAAe,EAClC,iBAAkB,GAAc,EAChC,mBAAoB,GAAa,IACjC,wBAAyB,GAAmB,IAC5C,6BAA8B,GAAyB,IACvD,oBAAqB,EAAe,GAAO,GAAa,IACxD,YAAa,EAAU,MAAM,CAAG,CAClC,EAEM,EAAmB,OAAO,MAAM,CAAC,GAAgB,KAAK,CAAC,GAAS,GAGhE,EAAe,EAAU,MAAM,CAAG,EACpC,EAAU,MAAM,CAAC,CAAC,EAAM,IACtB,EAAQ,YAAY,CAAG,EAAK,YAAY,CAAG,EAAU,QAEvD,EAqBJ,MAnBiC,CAmB1B,OAlBL,EACA,KAAM,EAAe,WAAW,EAAI,EACpC,OAAQ,EAAe,MAAM,EAAI,UACjC,MAAO,EACP,2BACA,EACA,cAAe,EACf,aAA6B,IAAf,kBACd,wBACA,YACA,EACA,mBAAoB,EACpB,SAAU,EACV,SAAU,IAAI,OAAO,WAAW,oBAChC,iBACA,CACF,CAGF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,CAAC,iBAAiB,EAAE,EAAM,MAAM,CAAC,CAAC,CAAC,CAAE,GAC5C,IACT,CACF,CAKA,MAAM,yBAAyB,CAAyB,CAA+B,CAGrF,MAAO,CAFY,MAAM,IAAI,CAAC,UAAU,CAAC,EAAA,EAEvB,MAAM,CAAC,GACvB,EAAO,gBAAgB,EACvB,EAAO,QAAQ,EACf,AAAyB,SAAS,IAA3B,QAAQ,CAAC,IAAI,CAExB,CAKA,MAAM,KAP8D,cAQlE,EAAiB,CAAC,CAClB,EAAiB,EAAE,CACnB,CAAyB,CACI,CAG7B,MAAO,CAFY,MAAM,IAAI,CAAC,UAAU,CAAC,EAAA,EAEvB,MAAM,CAAC,GACvB,EAAO,UAAU,EAAI,GACrB,EAAO,UAAU,EAAI,EAEzB,CAKA,MAAM,uBACJ,CAAuB,CACvB,CAAyB,CACI,CAG7B,MAAO,CAFY,MAAM,IAAI,CAAC,UAAU,CAAC,EAAA,EAEvB,MAAM,CAAC,GACvB,EAAO,QAAQ,EACf,EAAc,QAAQ,CAAC,EAAO,QAAQ,CAAC,IAAI,EAE/C,CAKA,uBAAgC,CAC9B,IAAM,EAAM,IAAI,KACV,EAAQ,IAAI,KAAK,EAAI,WAAW,GAAI,EAAI,QAAQ,GAAI,EAAI,OAAO,IAE/D,EAAY,CAChB,IAAI,KAAK,EAAM,OAAO,GAAK,IAAI,GAC/B,EADoC,EAChC,GADqC,EAChC,EAAM,OAAO,GAAK,IAAI,GAC/B,EADoC,EAChC,GADqC,EAChC,EAAM,OAAO,GAAK,IAAI,GAC/B,EADoC,EAChC,GADqC,EAChC,EAAM,OAAO,GAAK,IAAI,GAChC,CAIK,CALgC,CAIxB,AACM,IADF,AAJyB,CAIxB,KAJ+B,WAAW,IAItB,CAAC,GACZ,EAAI,EAEhC,OAAO,EAAU,GAAG,CAAC,GACnB,IAAI,KAAK,EAAK,OAAO,GAAmB,KAAd,AAAmB,KAEjD,AAFsD,CAO9C,qBAAqB,CAAU,CAAW,CAChD,IAAM,EAAO,EAAK,WAAW,GAGvB,EAAW,IAAI,KAAK,EAAM,EAAG,GAAG,AACtC,EAAS,OAAO,CAAC,EAAS,AADwB,OACjB,IAAM,CAAD,CAAK,EAAS,MAAM,EAAA,CAAE,CAAI,GAGhE,CAHmE,GAG7D,EAAS,IAAI,KAAK,EAAM,AAHqD,GAGjD,GAAG,AAGrC,OAFA,EAAO,MAD6C,CACtC,CAAC,EAAO,OAAO,IAAM,CAAD,CAAK,EAAO,MAAM,EAAA,CAAE,EAE/C,EAFkD,CAE1C,GAAY,EAAO,CACpC,CAKA,MAAM,CARoE,iBAQlD,CAAmC,CAA+B,CACxF,IAAM,EAAU,EAAgB,GAAG,CAAC,GAAU,EAAO,MAAM,EACrD,EAAgB,MAAM,IAAI,CAAC,MAAM,CAAC,0BAA0B,CAAC,GAE7D,EAAqC,EAAE,CAE7C,IAAK,IAAM,KAAS,EAAe,CACjC,IAAM,EAAiB,EAAgB,IAAI,CAAC,GAAK,EAAE,MAAM,GAAK,EAAM,MAAM,EAC1E,GAAI,CAAC,EAAgB,SAErB,IAAM,EAAe,EAAM,cAAc,EAAI,EAAM,KAAK,CAClD,EAAc,CAAC,EAAe,EAAM,aAAA,AAAa,EAAI,EAAM,aAAa,CAAI,IAE5E,EAAkC,CACtC,GAAG,CAAc,CACjB,MAAO,aACP,EACA,gBAAiB,EAAM,MAAM,EAAI,EACjC,sBAAuB,CAAC,EAAM,MAAM,GAAI,CAAC,CAAI,EAC7C,SAAU,IAAI,OAAO,WAAW,EAClC,EAGA,EAAc,cAAc,CAAC,gBAAgB,CAAG,GAAc,EAC9D,EAAc,cAAc,CAAC,uBAAuB,CAAG,EAAc,eAAe,EAAI,IACxF,EAAc,cAAc,CAAC,4BAA4B,CAAG,EAAc,qBAAqB,EAAI,IACnG,EAAc,gBAAgB,CAAG,OAAO,MAAM,CAAC,EAAc,cAAc,EAAE,KAAK,CAAC,GAAS,GAE5F,EAAe,IAAI,CAAC,EACtB,CAEA,OAAO,EAAe,IAAI,CAAC,CAAC,EAAG,IAAM,EAAE,UAAU,CAAG,EAAE,UAAU,CAClE,CAKA,eAAe,CAA2B,CAAE,CAC1C,IAAM,EAAe,IAAI,CAAC,aAAa,CAAC,MAAM,CACxC,EAAY,EAAQ,MAAM,CAC1B,EAAe,EAAQ,MAAM,CAAC,GAAK,EAAE,gBAAgB,EAAE,MAAM,CAC7D,EAAgB,EAAQ,MAAM,CAAC,GAAK,EAAE,QAAQ,EAAE,MAAM,CAEtD,EAAS,EAAQ,MAAM,CAAG,EAC5B,EAAQ,MAAM,CAAC,CAAC,EAAK,IAAM,EAAM,EAAE,UAAU,CAAE,GAAK,EAAQ,MAAM,CAClE,EAeJ,MAAO,cACL,YACA,eACA,gBACA,EACA,OAAQ,KAAK,KAAK,CAAU,IAAT,GAAgB,IACnC,gBAnBsB,EAAQ,MAAM,CAAC,CAAC,EAAK,KAC3C,CAAG,CAAC,EAAO,MAAM,CAAC,CAAG,CAAC,CAAG,CAAC,EAAO,MAAM,CAAC,EAAI,CAAC,EAAI,EAC1C,GACN,CAAC,GAiBF,sBAf4B,EAC3B,MAAM,CAAC,GAAK,EAAE,QAAQ,EACtB,MAAM,CAAC,CAAC,EAAK,KACZ,IAAM,EAAO,EAAO,QAAQ,CAAE,IAAI,CAElC,OADA,CAAG,CAAC,EAAK,CAAG,CAAC,CAAG,CAAC,EAAK,GAAI,CAAC,CAAI,EACxB,CACT,EAAG,CAAC,GAUJ,SAAU,IAAI,OAAO,WAAW,EAClC,CACF,CACF"}