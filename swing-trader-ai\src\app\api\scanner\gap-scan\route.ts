import { NextRequest, NextResponse } from 'next/server'
import { PreMarketGapScanner } from '@/lib/preMarketGapScanner'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    const minGap = parseFloat(searchParams.get('minGap') || '3')
    const maxGap = parseFloat(searchParams.get('maxGap') || '15')
    const customUniverse = searchParams.get('universe')?.split(',').filter(Boolean)
    const catalystTypes = searchParams.get('catalystTypes')?.split(',').filter(Boolean)
    const perfectPickOnly = searchParams.get('perfectPickOnly') === 'true'
    const limit = parseInt(searchParams.get('limit') || '50')

    console.log('📊 Gap Scanner API called with params:', {
      minGap,
      maxGap,
      customUniverse: customUniverse?.length || 'default',
      catalystTypes,
      perfectPickOnly,
      limit
    })

    // Initialize Gap Scanner
    const gapScanner = new PreMarketGapScanner(
      process.env.FMP_API_KEY,
      process.env.POLYGON_API_KEY
    )

    let results
    
    if (perfectPickOnly) {
      // Get only Perfect-Pick candidates
      results = await gapScanner.getPerfectPickCandidates(customUniverse)
    } else if (catalystTypes && catalystTypes.length > 0) {
      // Filter by catalyst types
      results = await gapScanner.getCatalystTypeResults(catalystTypes, customUniverse)
    } else {
      // Get results by gap range
      results = await gapScanner.getGapRangeResults(minGap, maxGap, customUniverse)
    }

    // Limit results
    const limitedResults = results.slice(0, limit)

    // Get summary statistics
    const summary = gapScanner.getScanSummary(limitedResults)

    const response = {
      success: true,
      data: {
        results: limitedResults,
        summary,
        scanParams: {
          minGap,
          maxGap,
          universeSize: customUniverse?.length || 'default',
          catalystTypes,
          perfectPickOnly,
          limit
        },
        timestamp: new Date().toISOString()
      }
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error in Gap Scanner API:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to run gap scan',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, data } = body

    const gapScanner = new PreMarketGapScanner(
      process.env.FMP_API_KEY,
      process.env.POLYGON_API_KEY
    )

    switch (action) {
      case 'update_results':
        const updatedResults = await gapScanner.updateScanResults(data.results)
        return NextResponse.json({
          success: true,
          data: { results: updatedResults }
        })

      case 'get_scheduled_times':
        const scheduledTimes = gapScanner.getScheduledScanTimes()
        return NextResponse.json({
          success: true,
          data: { scheduledTimes }
        })

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Error in Gap Scanner POST API:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process gap scanner request',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
