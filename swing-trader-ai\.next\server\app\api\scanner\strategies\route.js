/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/scanner/strategies/route";
exports.ids = ["app/api/scanner/strategies/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fscanner%2Fstrategies%2Froute&page=%2Fapi%2Fscanner%2Fstrategies%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscanner%2Fstrategies%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fscanner%2Fstrategies%2Froute&page=%2Fapi%2Fscanner%2Fstrategies%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscanner%2Fstrategies%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var C_Users_SJFit_Desktop_shittyidea_swing_trader_ai_src_app_api_scanner_strategies_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/scanner/strategies/route.ts */ \"(rsc)/./src/app/api/scanner/strategies/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/scanner/strategies/route\",\n        pathname: \"/api/scanner/strategies\",\n        filename: \"route\",\n        bundlePath: \"app/api/scanner/strategies/route\"\n    },\n    distDir: \".next\" || 0,\n    relativeProjectDir:  false || '',\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\shittyidea\\\\swing-trader-ai\\\\src\\\\app\\\\api\\\\scanner\\\\strategies\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_SJFit_Desktop_shittyidea_swing_trader_ai_src_app_api_scanner_strategies_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/scanner/strategies/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = false;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        if (!(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fscanner%2Fstrategies%2Froute&page=%2Fapi%2Fscanner%2Fstrategies%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscanner%2Fstrategies%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/scanner/strategies/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/scanner/strategies/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _data_watchlist__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/data/watchlist */ \"(rsc)/./src/data/watchlist.ts\");\n\n\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const scanType = searchParams.get('type') || 'quick' // quick, full\n        ;\n        const accountSize = parseInt(searchParams.get('accountSize') || '100000');\n        const limit = parseInt(searchParams.get('limit') || '20');\n        console.log(`Starting ${scanType} strategy scan...`);\n        // Set account size for position sizing\n        const scanner = new (__webpack_require__(/*! @/lib/enhancedSwingScanner */ \"(rsc)/./src/lib/enhancedSwingScanner.ts\").EnhancedSwingScanner)(accountSize);\n        let summary;\n        if (scanType === 'full') {\n            summary = await scanner.scanWithStrategies(_data_watchlist__WEBPACK_IMPORTED_MODULE_1__.ALL_SYMBOLS, 3); // Slower for full scan\n        } else {\n            summary = await scanner.scanWithStrategies(_data_watchlist__WEBPACK_IMPORTED_MODULE_1__.PRIORITY_SYMBOLS, 6); // Faster for quick scan\n        }\n        // Limit results if requested\n        const limitedSummary = {\n            ...summary,\n            topSetups: summary.topSetups.slice(0, limit)\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(limitedSummary);\n    } catch (error) {\n        console.error('Error in strategy scanner API:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to perform strategy scan'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/scanner/strategies/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/data/watchlist.ts":
/*!*******************************!*\
  !*** ./src/data/watchlist.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALL_SYMBOLS: () => (/* binding */ ALL_SYMBOLS),\n/* harmony export */   PRIORITY_SYMBOLS: () => (/* binding */ PRIORITY_SYMBOLS),\n/* harmony export */   STOCKS_BY_SECTOR: () => (/* binding */ STOCKS_BY_SECTOR),\n/* harmony export */   SWING_TRADING_WATCHLIST: () => (/* binding */ SWING_TRADING_WATCHLIST)\n/* harmony export */ });\nconst SWING_TRADING_WATCHLIST = [\n    // Large Cap Tech Giants\n    {\n        symbol: 'MSFT',\n        name: 'Microsoft Corp',\n        sector: 'Technology',\n        marketCap: 'large'\n    },\n    {\n        symbol: 'NVDA',\n        name: 'NVIDIA Corp',\n        sector: 'Technology',\n        marketCap: 'large'\n    },\n    {\n        symbol: 'AMZN',\n        name: 'Amazon.com Inc',\n        sector: 'Consumer Discretionary',\n        marketCap: 'large'\n    },\n    {\n        symbol: 'GOOG',\n        name: 'Alphabet Inc Class C',\n        sector: 'Technology',\n        marketCap: 'large'\n    },\n    {\n        symbol: 'GOOGL',\n        name: 'Alphabet Inc Class A',\n        sector: 'Technology',\n        marketCap: 'large'\n    },\n    {\n        symbol: 'META',\n        name: 'Meta Platforms Inc',\n        sector: 'Technology',\n        marketCap: 'large'\n    },\n    {\n        symbol: 'AVGO',\n        name: 'Broadcom Inc',\n        sector: 'Technology',\n        marketCap: 'large'\n    },\n    {\n        symbol: 'TSM',\n        name: 'Taiwan Semiconductor',\n        sector: 'Technology',\n        marketCap: 'large'\n    },\n    {\n        symbol: 'ORCL',\n        name: 'Oracle Corp',\n        sector: 'Technology',\n        marketCap: 'large'\n    },\n    {\n        symbol: 'NFLX',\n        name: 'Netflix Inc',\n        sector: 'Communication Services',\n        marketCap: 'large'\n    },\n    {\n        symbol: 'CSCO',\n        name: 'Cisco Systems Inc',\n        sector: 'Technology',\n        marketCap: 'large'\n    },\n    {\n        symbol: 'AMD',\n        name: 'Advanced Micro Devices Inc',\n        sector: 'Technology',\n        marketCap: 'large'\n    },\n    // Financial Services\n    {\n        symbol: 'JPM',\n        name: 'JPMorgan Chase & Co',\n        sector: 'Financial Services',\n        marketCap: 'large'\n    },\n    {\n        symbol: 'BAC',\n        name: 'Bank of America Corp',\n        sector: 'Financial Services',\n        marketCap: 'large'\n    },\n    {\n        symbol: 'MS',\n        name: 'Morgan Stanley',\n        sector: 'Financial Services',\n        marketCap: 'large'\n    },\n    {\n        symbol: 'SCHW',\n        name: 'Charles Schwab Corp',\n        sector: 'Financial Services',\n        marketCap: 'large'\n    },\n    {\n        symbol: 'C',\n        name: 'Citigroup Inc',\n        sector: 'Financial Services',\n        marketCap: 'large'\n    },\n    // Healthcare & Pharmaceuticals\n    {\n        symbol: 'JNJ',\n        name: 'Johnson & Johnson',\n        sector: 'Healthcare',\n        marketCap: 'large'\n    },\n    {\n        symbol: 'ABBV',\n        name: 'AbbVie Inc',\n        sector: 'Healthcare',\n        marketCap: 'large'\n    },\n    {\n        symbol: 'MRK',\n        name: 'Merck & Co Inc',\n        sector: 'Healthcare',\n        marketCap: 'large'\n    },\n    {\n        symbol: 'GILD',\n        name: 'Gilead Sciences Inc',\n        sector: 'Healthcare',\n        marketCap: 'large'\n    },\n    // Industrial & Manufacturing\n    {\n        symbol: 'GE',\n        name: 'General Electric Co',\n        sector: 'Industrial',\n        marketCap: 'large'\n    },\n    {\n        symbol: 'CAT',\n        name: 'Caterpillar Inc',\n        sector: 'Industrial',\n        marketCap: 'large'\n    },\n    {\n        symbol: 'BA',\n        name: 'Boeing Co',\n        sector: 'Industrial',\n        marketCap: 'large'\n    },\n    {\n        symbol: 'GEV',\n        name: 'GE Vernova Inc',\n        sector: 'Industrial',\n        marketCap: 'large'\n    },\n    // Semiconductors\n    {\n        symbol: 'ASML',\n        name: 'ASML Holding NV',\n        sector: 'Technology',\n        marketCap: 'large'\n    },\n    {\n        symbol: 'MU',\n        name: 'Micron Technology Inc',\n        sector: 'Technology',\n        marketCap: 'large'\n    },\n    {\n        symbol: 'LRCX',\n        name: 'Lam Research Corp',\n        sector: 'Technology',\n        marketCap: 'large'\n    },\n    // Consumer & Retail\n    {\n        symbol: 'DIS',\n        name: 'Walt Disney Co',\n        sector: 'Communication Services',\n        marketCap: 'large'\n    },\n    {\n        symbol: 'SBUX',\n        name: 'Starbucks Corp',\n        sector: 'Consumer Discretionary',\n        marketCap: 'large'\n    },\n    {\n        symbol: 'MO',\n        name: 'Altria Group Inc',\n        sector: 'Consumer Staples',\n        marketCap: 'large'\n    },\n    // Energy & Utilities\n    {\n        symbol: 'CEG',\n        name: 'Constellation Energy Corp',\n        sector: 'Utilities',\n        marketCap: 'large'\n    },\n    {\n        symbol: 'VST',\n        name: 'Vistra Corp',\n        sector: 'Utilities',\n        marketCap: 'mid'\n    },\n    // Automotive\n    {\n        symbol: 'GM',\n        name: 'General Motors Co',\n        sector: 'Consumer Discretionary',\n        marketCap: 'large'\n    },\n    // Growth & Tech Mid-Caps\n    {\n        symbol: 'PLTR',\n        name: 'Palantir Technologies Inc',\n        sector: 'Technology',\n        marketCap: 'mid'\n    },\n    {\n        symbol: 'APP',\n        name: 'Applovin Corp',\n        sector: 'Technology',\n        marketCap: 'mid'\n    },\n    {\n        symbol: 'DASH',\n        name: 'DoorDash Inc',\n        sector: 'Consumer Discretionary',\n        marketCap: 'mid'\n    },\n    {\n        symbol: 'NET',\n        name: 'Cloudflare Inc',\n        sector: 'Technology',\n        marketCap: 'mid'\n    },\n    {\n        symbol: 'DDOG',\n        name: 'Datadog Inc',\n        sector: 'Technology',\n        marketCap: 'mid'\n    },\n    {\n        symbol: 'ZS',\n        name: 'Zscaler Inc',\n        sector: 'Technology',\n        marketCap: 'mid'\n    },\n    {\n        symbol: 'SHOP',\n        name: 'Shopify Inc',\n        sector: 'Technology',\n        marketCap: 'mid'\n    },\n    {\n        symbol: 'RBLX',\n        name: 'Roblox Corp',\n        sector: 'Communication Services',\n        marketCap: 'mid'\n    },\n    // Mining & Commodities\n    {\n        symbol: 'AEM',\n        name: 'Agnico Eagle Mines Ltd',\n        sector: 'Materials',\n        marketCap: 'mid'\n    },\n    {\n        symbol: 'NEM',\n        name: 'Newmont Corp',\n        sector: 'Materials',\n        marketCap: 'large'\n    },\n    {\n        symbol: 'CCJ',\n        name: 'Cameco Corp',\n        sector: 'Energy',\n        marketCap: 'mid'\n    },\n    {\n        symbol: 'PAAS',\n        name: 'Pan American Silver Corp',\n        sector: 'Materials',\n        marketCap: 'small'\n    },\n    {\n        symbol: 'BTG',\n        name: 'B2Gold Corp',\n        sector: 'Materials',\n        marketCap: 'small'\n    },\n    {\n        symbol: 'HL',\n        name: 'Hecla Mining Co',\n        sector: 'Materials',\n        marketCap: 'small'\n    },\n    {\n        symbol: 'MP',\n        name: 'MP Materials Corp',\n        sector: 'Materials',\n        marketCap: 'small'\n    },\n    {\n        symbol: 'AG',\n        name: 'First Majestic Silver Corp',\n        sector: 'Materials',\n        marketCap: 'small'\n    },\n    // Transportation & Travel\n    {\n        symbol: 'UAL',\n        name: 'United Airlines Holdings Inc',\n        sector: 'Industrial',\n        marketCap: 'mid'\n    },\n    {\n        symbol: 'NCLH',\n        name: 'Norwegian Cruise Line',\n        sector: 'Consumer Discretionary',\n        marketCap: 'mid'\n    },\n    // Fintech & Trading\n    {\n        symbol: 'HOOD',\n        name: 'Robinhood Markets Inc',\n        sector: 'Financial Services',\n        marketCap: 'mid'\n    },\n    {\n        symbol: 'SOFI',\n        name: 'SoFi Technologies Inc',\n        sector: 'Financial Services',\n        marketCap: 'small'\n    },\n    // Consumer Brands\n    {\n        symbol: 'CELH',\n        name: 'Celsius Holdings Inc',\n        sector: 'Consumer Staples',\n        marketCap: 'small'\n    },\n    {\n        symbol: 'LEVI',\n        name: 'Levi Strauss & Co',\n        sector: 'Consumer Discretionary',\n        marketCap: 'small'\n    },\n    {\n        symbol: 'ELF',\n        name: 'e.l.f. Beauty Inc',\n        sector: 'Consumer Discretionary',\n        marketCap: 'small'\n    },\n    {\n        symbol: 'ETSY',\n        name: 'Etsy Inc',\n        sector: 'Consumer Discretionary',\n        marketCap: 'mid'\n    },\n    {\n        symbol: 'W',\n        name: 'Wayfair Inc',\n        sector: 'Consumer Discretionary',\n        marketCap: 'mid'\n    },\n    // Crypto & Blockchain\n    {\n        symbol: 'RIOT',\n        name: 'Riot Platforms Inc',\n        sector: 'Technology',\n        marketCap: 'small'\n    },\n    {\n        symbol: 'HUT',\n        name: 'Hut 8 Corp',\n        sector: 'Technology',\n        marketCap: 'small'\n    },\n    {\n        symbol: 'IREN',\n        name: 'IREN Ltd',\n        sector: 'Technology',\n        marketCap: 'small'\n    },\n    // International & Emerging\n    {\n        symbol: 'BILI',\n        name: 'Bilibili Inc',\n        sector: 'Communication Services',\n        marketCap: 'small'\n    },\n    {\n        symbol: 'TIGR',\n        name: 'UP Fintech Holding Ltd',\n        sector: 'Financial Services',\n        marketCap: 'small'\n    },\n    {\n        symbol: 'FUTU',\n        name: 'Futu Holdings Ltd',\n        sector: 'Financial Services',\n        marketCap: 'small'\n    },\n    {\n        symbol: 'NBIS',\n        name: 'Nebius Group NV',\n        sector: 'Technology',\n        marketCap: 'small'\n    },\n    // Emerging Tech & AI\n    {\n        symbol: 'SOUN',\n        name: 'SoundHound AI Inc',\n        sector: 'Technology',\n        marketCap: 'small'\n    },\n    {\n        symbol: 'IONQ',\n        name: 'IonQ Inc',\n        sector: 'Technology',\n        marketCap: 'small'\n    },\n    {\n        symbol: 'RGTI',\n        name: 'Rigetti Computing Inc',\n        sector: 'Technology',\n        marketCap: 'small'\n    },\n    // Aerospace & Defense\n    {\n        symbol: 'RKLB',\n        name: 'Rocket Lab Corp',\n        sector: 'Industrial',\n        marketCap: 'small'\n    },\n    {\n        symbol: 'ASTS',\n        name: 'AST SpaceMobile Inc',\n        sector: 'Technology',\n        marketCap: 'small'\n    },\n    // Infrastructure & Utilities\n    {\n        symbol: 'VRT',\n        name: 'Vertiv Holdings Co',\n        sector: 'Industrial',\n        marketCap: 'mid'\n    }\n];\n// Group stocks by sector for analysis\nconst STOCKS_BY_SECTOR = SWING_TRADING_WATCHLIST.reduce((acc, stock)=>{\n    if (!acc[stock.sector]) {\n        acc[stock.sector] = [];\n    }\n    acc[stock.sector].push(stock);\n    return acc;\n}, {});\n// Get all symbols as array\nconst ALL_SYMBOLS = SWING_TRADING_WATCHLIST.map((stock)=>stock.symbol);\n// Priority symbols for faster scanning (top liquid stocks)\nconst PRIORITY_SYMBOLS = [\n    'MSFT',\n    'NVDA',\n    'AMZN',\n    'GOOG',\n    'GOOGL',\n    'META',\n    'AVGO',\n    'TSM',\n    'JPM',\n    'NFLX',\n    'ORCL',\n    'JNJ',\n    'BAC',\n    'ABBV',\n    'ASML',\n    'PLTR'\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/data/watchlist.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/enhancedSwingScanner.ts":
/*!*****************************************!*\
  !*** ./src/lib/enhancedSwingScanner.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EnhancedSwingScanner: () => (/* binding */ EnhancedSwingScanner),\n/* harmony export */   enhancedSwingScanner: () => (/* binding */ enhancedSwingScanner)\n/* harmony export */ });\n/* harmony import */ var _swingStrategies__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./swingStrategies */ \"(rsc)/./src/lib/swingStrategies.ts\");\n/* harmony import */ var _polygon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./polygon */ \"(rsc)/./src/lib/polygon.ts\");\n/* harmony import */ var _barrel_optimize_names_format_subDays_date_fns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=format,subDays!=!date-fns */ \"(rsc)/./node_modules/date-fns/format.js\");\n/* harmony import */ var _barrel_optimize_names_format_subDays_date_fns__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=format,subDays!=!date-fns */ \"(rsc)/./node_modules/date-fns/subDays.js\");\n\n\n\nclass EnhancedSwingScanner {\n    constructor(accountSize = 100000){\n        this.polygonAPI = new _polygon__WEBPACK_IMPORTED_MODULE_1__.PolygonAPI(process.env.POLYGON_API_KEY);\n        this.accountSize = accountSize;\n    }\n    // Main enhanced scanning function\n    async scanWithStrategies(symbols, maxConcurrent = 5) {\n        const startTime = Date.now();\n        const results = [];\n        const failed = [];\n        console.log(`Starting enhanced strategy scan of ${symbols.length} stocks...`);\n        // Check if we're in optimal scan time (12:00-16:00 ET)\n        const marketConditions = this.getMarketConditions();\n        // Process stocks in batches\n        for(let i = 0; i < symbols.length; i += maxConcurrent){\n            const batch = symbols.slice(i, i + maxConcurrent);\n            const batchPromises = batch.map((symbol)=>this.scanSingleStockStrategies(symbol));\n            const batchResults = await Promise.allSettled(batchPromises);\n            batchResults.forEach((result, index)=>{\n                const symbol = batch[index];\n                if (result.status === 'fulfilled' && result.value) {\n                    results.push(result.value);\n                } else {\n                    failed.push(symbol);\n                    console.warn(`Failed to scan ${symbol}:`, result.status === 'rejected' ? result.reason : 'Unknown error');\n                }\n            });\n            // Rate limiting delay\n            if (i + maxConcurrent < symbols.length) {\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n            }\n        }\n        // Sort by overall score and assign ranks\n        results.sort((a, b)=>b.overallScore - a.overallScore);\n        results.forEach((result, index)=>{\n            result.rank = index + 1;\n        });\n        // Calculate summary statistics\n        const overnightSetups = results.filter((r)=>r.overnightSetup).length;\n        const breakoutSetups = results.filter((r)=>r.breakoutSetup).length;\n        const bothStrategies = results.filter((r)=>r.overnightSetup && r.breakoutSetup).length;\n        const scanDuration = Date.now() - startTime;\n        return {\n            totalScanned: symbols.length,\n            overnightSetups,\n            breakoutSetups,\n            bothStrategies,\n            topSetups: results.slice(0, 25),\n            scanDuration,\n            marketConditions\n        };\n    }\n    // Scan individual stock for both strategies\n    async scanSingleStockStrategies(symbol) {\n        try {\n            // Get stock quote and historical data\n            const [quote, historicalData] = await Promise.all([\n                this.polygonAPI.getStockQuote(symbol),\n                this.getHistoricalData(symbol)\n            ]);\n            if (!historicalData || historicalData.length < 30) {\n                throw new Error('Insufficient historical data - need at least 30 days');\n            }\n            // Analyze both strategies\n            const overnightSetup = _swingStrategies__WEBPACK_IMPORTED_MODULE_0__.SwingTradingStrategies.analyzeOvernightMomentum(symbol, historicalData, quote, this.accountSize);\n            const breakoutSetup = _swingStrategies__WEBPACK_IMPORTED_MODULE_0__.SwingTradingStrategies.analyzeTechnicalBreakout(symbol, historicalData, quote, this.accountSize);\n            // Skip if no valid setups\n            if (!overnightSetup && !breakoutSetup) {\n                return null;\n            }\n            // Determine best strategy and overall score\n            const { bestStrategy, overallScore } = this.calculateBestStrategy(overnightSetup, breakoutSetup);\n            // Generate alerts and warnings\n            const alerts = this.generateAlerts(overnightSetup, breakoutSetup, quote);\n            const riskWarnings = this.generateRiskWarnings(overnightSetup, breakoutSetup, quote);\n            return {\n                symbol,\n                name: quote.name,\n                sector: this.getSectorForSymbol(symbol),\n                quote,\n                overnightSetup: overnightSetup || undefined,\n                breakoutSetup: breakoutSetup || undefined,\n                bestStrategy,\n                overallScore,\n                rank: 0,\n                scanTime: new Date().toISOString(),\n                alerts,\n                riskWarnings\n            };\n        } catch (error) {\n            console.error(`Error scanning ${symbol}:`, error);\n            if (error instanceof Error) {\n                console.error(`Error message: ${error.message}`);\n                console.error(`Error stack: ${error.stack}`);\n            }\n            return null;\n        }\n    }\n    // Get historical data with optimized API usage\n    async getHistoricalData(symbol) {\n        const to = (0,_barrel_optimize_names_format_subDays_date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(new Date(), 'yyyy-MM-dd');\n        const from = (0,_barrel_optimize_names_format_subDays_date_fns__WEBPACK_IMPORTED_MODULE_2__.format)((0,_barrel_optimize_names_format_subDays_date_fns__WEBPACK_IMPORTED_MODULE_3__.subDays)(new Date(), 100), 'yyyy-MM-dd') // 100 days should be sufficient\n        ;\n        try {\n            console.log(`Fetching historical data for ${symbol} from ${from} to ${to}`);\n            const data = await this.polygonAPI.getHistoricalData(symbol, 'day', 1, from, to);\n            if (data.length === 0) {\n                console.warn(`No historical data returned for ${symbol}`);\n                throw new Error('No historical data available');\n            }\n            console.log(`Successfully fetched ${data.length} days of data for ${symbol}`);\n            return data;\n        } catch (error) {\n            console.error(`Failed to fetch historical data for ${symbol}:`, error);\n            throw error;\n        }\n    }\n    // Calculate best strategy and overall score\n    calculateBestStrategy(overnight, breakout) {\n        if (!overnight && !breakout) {\n            return {\n                overallScore: 0\n            };\n        }\n        if (overnight && !breakout) {\n            return {\n                bestStrategy: 'overnight_momentum',\n                overallScore: overnight.confidence\n            };\n        }\n        if (breakout && !overnight) {\n            return {\n                bestStrategy: 'technical_breakout',\n                overallScore: breakout.confidence\n            };\n        }\n        if (overnight && breakout) {\n            // Both strategies valid - choose higher confidence\n            if (overnight.confidence > breakout.confidence) {\n                return {\n                    bestStrategy: 'overnight_momentum',\n                    overallScore: overnight.confidence + 5\n                } // Bonus for multiple setups\n                ;\n            } else {\n                return {\n                    bestStrategy: 'technical_breakout',\n                    overallScore: breakout.confidence + 5\n                };\n            }\n        }\n        return {\n            overallScore: 0\n        };\n    }\n    // Generate trading alerts\n    generateAlerts(overnight, breakout, quote) {\n        const alerts = [];\n        if (overnight) {\n            alerts.push(`🚀 OVERNIGHT MOMENTUM: Entry ${overnight.entryPrice.toFixed(2)}, Target ${overnight.targets[0].toFixed(2)}`);\n            alerts.push(`⏰ Execute in final 30-60 min before close`);\n            alerts.push(`🛑 Stop: ${overnight.stopLoss.toFixed(2)} (${((overnight.entryPrice - overnight.stopLoss) / overnight.entryPrice * 100).toFixed(1)}% risk)`);\n        }\n        if (breakout) {\n            alerts.push(`📈 BREAKOUT SETUP: Entry ${breakout.entryPrice.toFixed(2)}, riding 8-EMA`);\n            alerts.push(`🎯 Targets: ${breakout.targets.map((t)=>t.toFixed(2)).join(', ')}`);\n            alerts.push(`🛑 Stop: Daily close below ${breakout.stopLoss.toFixed(2)}`);\n        }\n        if (quote && quote.changePercent > 5) {\n            alerts.push(`🔥 Strong momentum: +${quote.changePercent.toFixed(1)}% today`);\n        }\n        return alerts;\n    }\n    // Generate risk warnings\n    generateRiskWarnings(overnight, breakout, quote) {\n        const warnings = [];\n        if (overnight) {\n            warnings.push(`⚠️ Overnight gap risk - size down vs intraday trades`);\n            if (quote && quote.changePercent > 8) {\n                warnings.push(`⚠️ Extended move (+${quote.changePercent.toFixed(1)}%) - consider smaller size`);\n            }\n        }\n        if (quote && (quote.marketCap || 0) < 1000000000) {\n            warnings.push(`⚠️ Small cap overnight risk - volatile gaps possible`);\n        }\n        if (quote && quote.volume < 1000000) {\n            warnings.push(`⚠️ Lower volume - may have liquidity issues`);\n        }\n        return warnings;\n    }\n    // Get market conditions with proper timezone handling\n    getMarketConditions() {\n        const now = new Date();\n        // Get current time in Eastern Time (market timezone)\n        const etNow = new Date(now.toLocaleString(\"en-US\", {\n            timeZone: \"America/New_York\"\n        }));\n        const etHour = etNow.getHours();\n        const etMinute = etNow.getMinutes();\n        const etTimeDecimal = etHour + etMinute / 60;\n        // Get local time for display\n        const localHour = now.getHours();\n        const localMinute = now.getMinutes();\n        // Check if it's a weekday (Monday = 1, Friday = 5)\n        const dayOfWeek = etNow.getDay();\n        const isWeekday = dayOfWeek >= 1 && dayOfWeek <= 5;\n        return {\n            timeOfDay: `${localHour.toString().padStart(2, '0')}:${localMinute.toString().padStart(2, '0')} Local (${etHour.toString().padStart(2, '0')}:${etMinute.toString().padStart(2, '0')} ET)`,\n            isOptimalScanTime: isWeekday && etTimeDecimal >= 12 && etTimeDecimal <= 16,\n            marketHours: isWeekday && etTimeDecimal >= 9.5 && etTimeDecimal <= 16,\n            etTime: `${etHour.toString().padStart(2, '0')}:${etMinute.toString().padStart(2, '0')} ET`,\n            isWeekday\n        };\n    }\n    // Get sector for symbol (reuse from previous implementation)\n    getSectorForSymbol(symbol) {\n        const techSymbols = [\n            'MSFT',\n            'NVDA',\n            'GOOG',\n            'GOOGL',\n            'META',\n            'AVGO',\n            'TSM',\n            'ORCL',\n            'CSCO',\n            'AMD',\n            'ASML',\n            'MU',\n            'LRCX',\n            'PLTR',\n            'APP',\n            'NET',\n            'DDOG',\n            'ZS',\n            'SHOP',\n            'SOUN',\n            'IONQ',\n            'RGTI',\n            'RIOT',\n            'HUT',\n            'IREN',\n            'ASTS',\n            'NBIS'\n        ];\n        const financialSymbols = [\n            'JPM',\n            'BAC',\n            'MS',\n            'SCHW',\n            'C',\n            'HOOD',\n            'SOFI',\n            'TIGR',\n            'FUTU'\n        ];\n        const healthcareSymbols = [\n            'JNJ',\n            'ABBV',\n            'MRK',\n            'GILD'\n        ];\n        const industrialSymbols = [\n            'GE',\n            'CAT',\n            'BA',\n            'GEV',\n            'UAL',\n            'VRT',\n            'RKLB'\n        ];\n        const materialsSymbols = [\n            'AEM',\n            'NEM',\n            'PAAS',\n            'BTG',\n            'HL',\n            'MP',\n            'AG'\n        ];\n        const consumerSymbols = [\n            'AMZN',\n            'DIS',\n            'SBUX',\n            'MO',\n            'DASH',\n            'GM',\n            'NCLH',\n            'CELH',\n            'LEVI',\n            'ELF',\n            'ETSY',\n            'W'\n        ];\n        const communicationSymbols = [\n            'NFLX',\n            'RBLX',\n            'BILI'\n        ];\n        const energySymbols = [\n            'CEG',\n            'VST',\n            'CCJ'\n        ];\n        if (techSymbols.includes(symbol)) return 'Technology';\n        if (financialSymbols.includes(symbol)) return 'Financial Services';\n        if (healthcareSymbols.includes(symbol)) return 'Healthcare';\n        if (industrialSymbols.includes(symbol)) return 'Industrial';\n        if (materialsSymbols.includes(symbol)) return 'Materials';\n        if (consumerSymbols.includes(symbol)) return 'Consumer';\n        if (communicationSymbols.includes(symbol)) return 'Communication Services';\n        if (energySymbols.includes(symbol)) return 'Energy';\n        return 'Other';\n    }\n    // Quick scan with strategies\n    async quickStrategyScan(prioritySymbols) {\n        const summary = await this.scanWithStrategies(prioritySymbols, 8);\n        return summary.topSetups;\n    }\n}\n// Create singleton instance\nconst enhancedSwingScanner = new EnhancedSwingScanner();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/enhancedSwingScanner.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/indicators.ts":
/*!*******************************!*\
  !*** ./src/lib/indicators.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TechnicalIndicators: () => (/* binding */ TechnicalIndicators)\n/* harmony export */ });\nclass TechnicalIndicators {\n    // Simple Moving Average\n    static sma(data, period) {\n        const result = [];\n        for(let i = period - 1; i < data.length; i++){\n            const sum = data.slice(i - period + 1, i + 1).reduce((a, b)=>a + b, 0);\n            result.push(sum / period);\n        }\n        return result;\n    }\n    // Exponential Moving Average\n    static ema(data, period) {\n        const result = [];\n        const multiplier = 2 / (period + 1);\n        // Start with SMA for first value\n        let ema = data.slice(0, period).reduce((a, b)=>a + b, 0) / period;\n        result.push(ema);\n        for(let i = period; i < data.length; i++){\n            ema = data[i] * multiplier + ema * (1 - multiplier);\n            result.push(ema);\n        }\n        return result;\n    }\n    // Relative Strength Index\n    static rsi(data, period = 14) {\n        const gains = [];\n        const losses = [];\n        for(let i = 1; i < data.length; i++){\n            const change = data[i] - data[i - 1];\n            gains.push(change > 0 ? change : 0);\n            losses.push(change < 0 ? Math.abs(change) : 0);\n        }\n        const avgGains = this.sma(gains, period);\n        const avgLosses = this.sma(losses, period);\n        return avgGains.map((gain, i)=>{\n            const rs = gain / avgLosses[i];\n            return 100 - 100 / (1 + rs);\n        });\n    }\n    // MACD (Moving Average Convergence Divergence)\n    static macd(data, fastPeriod = 12, slowPeriod = 26, signalPeriod = 9) {\n        const fastEMA = this.ema(data, fastPeriod);\n        const slowEMA = this.ema(data, slowPeriod);\n        // Align arrays (slowEMA starts later)\n        const startIndex = slowPeriod - fastPeriod;\n        const macdLine = fastEMA.slice(startIndex).map((fast, i)=>fast - slowEMA[i]);\n        const signalLine = this.ema(macdLine, signalPeriod);\n        const histogram = macdLine.slice(signalPeriod - 1).map((macd, i)=>macd - signalLine[i]);\n        return {\n            macd: macdLine,\n            signal: signalLine,\n            histogram\n        };\n    }\n    // Bollinger Bands\n    static bollingerBands(data, period = 20, stdDev = 2) {\n        const sma = this.sma(data, period);\n        const bands = sma.map((avg, i)=>{\n            const slice = data.slice(i, i + period);\n            const variance = slice.reduce((sum, val)=>sum + Math.pow(val - avg, 2), 0) / period;\n            const standardDeviation = Math.sqrt(variance);\n            return {\n                upper: avg + standardDeviation * stdDev,\n                middle: avg,\n                lower: avg - standardDeviation * stdDev\n            };\n        });\n        return bands;\n    }\n    // Support and Resistance Levels\n    static findSupportResistance(candles, lookback = 20) {\n        const highs = candles.map((c)=>c.high);\n        const lows = candles.map((c)=>c.low);\n        const resistance = [];\n        const support = [];\n        for(let i = lookback; i < candles.length - lookback; i++){\n            const currentHigh = highs[i];\n            const currentLow = lows[i];\n            // Check if current high is a local maximum\n            const isResistance = highs.slice(i - lookback, i).every((h)=>h <= currentHigh) && highs.slice(i + 1, i + lookback + 1).every((h)=>h <= currentHigh);\n            // Check if current low is a local minimum\n            const isSupport = lows.slice(i - lookback, i).every((l)=>l >= currentLow) && lows.slice(i + 1, i + lookback + 1).every((l)=>l >= currentLow);\n            if (isResistance) resistance.push(currentHigh);\n            if (isSupport) support.push(currentLow);\n        }\n        return {\n            support,\n            resistance\n        };\n    }\n    // Volume analysis\n    static volumeAnalysis(candles, period = 20) {\n        const volumes = candles.map((c)=>c.volume);\n        const avgVolume = this.sma(volumes, period);\n        const currentVolume = volumes[volumes.length - 1];\n        const currentAvgVolume = avgVolume[avgVolume.length - 1];\n        return {\n            currentVolume,\n            averageVolume: currentAvgVolume,\n            volumeRatio: currentVolume / currentAvgVolume,\n            isHighVolume: currentVolume > currentAvgVolume * 1.5,\n            isLowVolume: currentVolume < currentAvgVolume * 0.5\n        };\n    }\n    // Swing Trading Analysis\n    static analyzeSwingSetup(candles) {\n        const closes = candles.map((c)=>c.close);\n        const indicators = [];\n        // RSI Analysis\n        const rsi = this.rsi(closes);\n        const currentRSI = rsi[rsi.length - 1];\n        let rsiSignal = 'NEUTRAL';\n        let rsiDescription = `RSI: ${currentRSI.toFixed(2)}`;\n        if (currentRSI < 30) {\n            rsiSignal = 'BUY';\n            rsiDescription += ' - Oversold condition, potential bounce';\n        } else if (currentRSI > 70) {\n            rsiSignal = 'SELL';\n            rsiDescription += ' - Overbought condition, potential pullback';\n        } else {\n            rsiDescription += ' - Neutral zone';\n        }\n        indicators.push({\n            name: 'RSI',\n            value: currentRSI,\n            signal: rsiSignal,\n            description: rsiDescription\n        });\n        // Moving Average Analysis\n        const sma20 = this.sma(closes, 20);\n        const sma50 = this.sma(closes, 50);\n        const currentPrice = closes[closes.length - 1];\n        const currentSMA20 = sma20[sma20.length - 1];\n        const currentSMA50 = sma50[sma50.length - 1];\n        let maSignal = 'NEUTRAL';\n        let maDescription = `Price vs SMA20: ${((currentPrice / currentSMA20 - 1) * 100).toFixed(2)}%`;\n        if (currentPrice > currentSMA20 && currentSMA20 > currentSMA50) {\n            maSignal = 'BUY';\n            maDescription += ' - Bullish trend';\n        } else if (currentPrice < currentSMA20 && currentSMA20 < currentSMA50) {\n            maSignal = 'SELL';\n            maDescription += ' - Bearish trend';\n        } else {\n            maDescription += ' - Mixed signals';\n        }\n        indicators.push({\n            name: 'Moving Averages',\n            value: (currentPrice / currentSMA20 - 1) * 100,\n            signal: maSignal,\n            description: maDescription\n        });\n        // MACD Analysis\n        const macdData = this.macd(closes);\n        const currentMACD = macdData.macd[macdData.macd.length - 1];\n        const currentSignal = macdData.signal[macdData.signal.length - 1];\n        const currentHistogram = macdData.histogram[macdData.histogram.length - 1];\n        let macdSignal = 'NEUTRAL';\n        let macdDescription = `MACD: ${currentMACD.toFixed(4)}, Signal: ${currentSignal.toFixed(4)}`;\n        if (currentMACD > currentSignal && currentHistogram > 0) {\n            macdSignal = 'BUY';\n            macdDescription += ' - Bullish momentum';\n        } else if (currentMACD < currentSignal && currentHistogram < 0) {\n            macdSignal = 'SELL';\n            macdDescription += ' - Bearish momentum';\n        } else {\n            macdDescription += ' - Momentum shifting';\n        }\n        indicators.push({\n            name: 'MACD',\n            value: currentHistogram,\n            signal: macdSignal,\n            description: macdDescription\n        });\n        // Volume Analysis\n        const volumeData = this.volumeAnalysis(candles);\n        let volumeSignal = 'NEUTRAL';\n        let volumeDescription = `Volume: ${(volumeData.volumeRatio * 100).toFixed(0)}% of average`;\n        if (volumeData.isHighVolume) {\n            volumeSignal = 'BUY';\n            volumeDescription += ' - High volume confirms move';\n        } else if (volumeData.isLowVolume) {\n            volumeSignal = 'SELL';\n            volumeDescription += ' - Low volume, weak conviction';\n        } else {\n            volumeDescription += ' - Normal volume';\n        }\n        indicators.push({\n            name: 'Volume',\n            value: volumeData.volumeRatio,\n            signal: volumeSignal,\n            description: volumeDescription\n        });\n        return indicators;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/indicators.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/polygon.ts":
/*!****************************!*\
  !*** ./src/lib/polygon.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PolygonAPI: () => (/* binding */ PolygonAPI),\n/* harmony export */   polygonAPI: () => (/* binding */ polygonAPI)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/lib/axios.js\");\n\nconst POLYGON_BASE_URL = 'https://api.polygon.io';\nconst API_KEY = process.env.POLYGON_API_KEY;\nclass PolygonAPI {\n    constructor(apiKey){\n        this.apiKey = apiKey || API_KEY || '';\n        if (!this.apiKey) {\n            throw new Error('Polygon API key is required');\n        }\n    }\n    // Get current stock quote using Polygon's snapshot endpoint (better for paid plans)\n    async getStockQuote(symbol) {\n        try {\n            // Use snapshot endpoint for real-time data (available on paid plans)\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${POLYGON_BASE_URL}/v2/snapshot/locale/us/markets/stocks/tickers/${symbol}`, {\n                params: {\n                    apikey: this.apiKey\n                }\n            });\n            if (!response.data || !response.data.results || response.data.results.length === 0) {\n                throw new Error(`No data found for ${symbol}`);\n            }\n            const data = response.data.results[0];\n            const ticker = data.value || data;\n            const dayData = ticker.day || {};\n            const prevDayData = ticker.prevDay || {};\n            const lastQuote = ticker.lastQuote || {};\n            const lastTrade = ticker.lastTrade || {};\n            // Use the most recent price available\n            const currentPrice = lastTrade.p || dayData.c || prevDayData.c;\n            const prevClose = prevDayData.c || dayData.o;\n            const change = currentPrice - prevClose;\n            const changePercent = change / prevClose * 100;\n            return {\n                symbol: symbol.toUpperCase(),\n                name: ticker.name || symbol.toUpperCase(),\n                price: currentPrice,\n                change,\n                changePercent,\n                volume: dayData.v || 0,\n                marketCap: ticker.market_cap,\n                pe: undefined,\n                dividend: undefined\n            };\n        } catch (error) {\n            console.error('Error fetching stock quote from Polygon:', error);\n            // Fallback to previous day data if snapshot fails\n            try {\n                const fallbackResponse = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/prev`, {\n                    params: {\n                        adjusted: 'true',\n                        apikey: this.apiKey\n                    }\n                });\n                const data = fallbackResponse.data.results[0];\n                return {\n                    symbol: symbol.toUpperCase(),\n                    name: symbol.toUpperCase(),\n                    price: data.c,\n                    change: data.c - data.o,\n                    changePercent: (data.c - data.o) / data.o * 100,\n                    volume: data.v,\n                    marketCap: undefined,\n                    pe: undefined,\n                    dividend: undefined\n                };\n            } catch (fallbackError) {\n                console.error('Polygon fallback also failed:', fallbackError);\n                throw new Error(`Failed to fetch quote for ${symbol}`);\n            }\n        }\n    }\n    // Get historical candlestick data (optimized for paid plans)\n    async getHistoricalData(symbol, timespan = 'day', multiplier = 1, from, to) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/range/${multiplier}/${timespan}/${from}/${to}`, {\n                params: {\n                    adjusted: 'true',\n                    sort: 'asc',\n                    limit: 50000,\n                    apikey: this.apiKey\n                }\n            });\n            if (!response.data.results || response.data.results.length === 0) {\n                console.warn(`No historical data found for ${symbol}`);\n                return [];\n            }\n            return response.data.results.map((candle)=>({\n                    timestamp: candle.t,\n                    open: candle.o,\n                    high: candle.h,\n                    low: candle.l,\n                    close: candle.c,\n                    volume: candle.v\n                }));\n        } catch (error) {\n            console.error(`Error fetching historical data for ${symbol}:`, error);\n            // Log the specific error for debugging\n            if (error.response) {\n                console.error(`Polygon API Error: ${error.response.status} - ${error.response.statusText}`);\n                console.error('Response data:', error.response.data);\n            }\n            throw new Error(`Failed to fetch historical data for ${symbol}: ${error.message}`);\n        }\n    }\n    // Get company details\n    async getCompanyDetails(symbol) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${POLYGON_BASE_URL}/v3/reference/tickers/${symbol}`, {\n                params: {\n                    apikey: this.apiKey\n                }\n            });\n            return response.data.results;\n        } catch (error) {\n            console.error('Error fetching company details:', error);\n            return null;\n        }\n    }\n    // Get market status\n    async getMarketStatus() {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${POLYGON_BASE_URL}/v1/marketstatus/now`, {\n                params: {\n                    apikey: this.apiKey\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching market status:', error);\n            return null;\n        }\n    }\n    // Search for stocks\n    async searchStocks(query, limit = 10) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${POLYGON_BASE_URL}/v3/reference/tickers`, {\n                params: {\n                    search: query,\n                    market: 'stocks',\n                    active: 'true',\n                    limit,\n                    apikey: this.apiKey\n                }\n            });\n            return response.data.results || [];\n        } catch (error) {\n            console.error('Error searching stocks:', error);\n            return [];\n        }\n    }\n}\n// Create a singleton instance\nconst polygonAPI = new PolygonAPI();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3BvbHlnb24udHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXlCO0FBR3pCLE1BQU1DLG1CQUFtQjtBQUN6QixNQUFNQyxVQUFVQyxRQUFRQyxHQUFHLENBQUNDLGVBQWU7QUFFcEMsTUFBTUM7SUFHWCxZQUFZQyxNQUFlLENBQUU7UUFDM0IsSUFBSSxDQUFDQSxNQUFNLEdBQUdBLFVBQVVMLFdBQVc7UUFDbkMsSUFBSSxDQUFDLElBQUksQ0FBQ0ssTUFBTSxFQUFFO1lBQ2hCLE1BQU0sSUFBSUMsTUFBTTtRQUNsQjtJQUNGO0lBRUEsb0ZBQW9GO0lBQ3BGLE1BQU1DLGNBQWNDLE1BQWMsRUFBc0I7UUFDdEQsSUFBSTtZQUNGLHFFQUFxRTtZQUNyRSxNQUFNQyxXQUFXLE1BQU1YLDZDQUFLQSxDQUFDWSxHQUFHLENBQzlCLEdBQUdYLGlCQUFpQiw4Q0FBOEMsRUFBRVMsUUFBUSxFQUM1RTtnQkFDRUcsUUFBUTtvQkFDTkMsUUFBUSxJQUFJLENBQUNQLE1BQU07Z0JBQ3JCO1lBQ0Y7WUFHRixJQUFJLENBQUNJLFNBQVNJLElBQUksSUFBSSxDQUFDSixTQUFTSSxJQUFJLENBQUNDLE9BQU8sSUFBSUwsU0FBU0ksSUFBSSxDQUFDQyxPQUFPLENBQUNDLE1BQU0sS0FBSyxHQUFHO2dCQUNsRixNQUFNLElBQUlULE1BQU0sQ0FBQyxrQkFBa0IsRUFBRUUsUUFBUTtZQUMvQztZQUVBLE1BQU1LLE9BQU9KLFNBQVNJLElBQUksQ0FBQ0MsT0FBTyxDQUFDLEVBQUU7WUFFckMsTUFBTUUsU0FBU0gsS0FBS0ksS0FBSyxJQUFJSjtZQUM3QixNQUFNSyxVQUFVRixPQUFPRyxHQUFHLElBQUksQ0FBQztZQUMvQixNQUFNQyxjQUFjSixPQUFPSyxPQUFPLElBQUksQ0FBQztZQUN2QyxNQUFNQyxZQUFZTixPQUFPTSxTQUFTLElBQUksQ0FBQztZQUN2QyxNQUFNQyxZQUFZUCxPQUFPTyxTQUFTLElBQUksQ0FBQztZQUV2QyxzQ0FBc0M7WUFDdEMsTUFBTUMsZUFBZUQsVUFBVUUsQ0FBQyxJQUFJUCxRQUFRUSxDQUFDLElBQUlOLFlBQVlNLENBQUM7WUFDOUQsTUFBTUMsWUFBWVAsWUFBWU0sQ0FBQyxJQUFJUixRQUFRVSxDQUFDO1lBQzVDLE1BQU1DLFNBQVNMLGVBQWVHO1lBQzlCLE1BQU1HLGdCQUFnQixTQUFVSCxZQUFhO1lBRTdDLE9BQU87Z0JBQ0xuQixRQUFRQSxPQUFPdUIsV0FBVztnQkFDMUJDLE1BQU1oQixPQUFPZ0IsSUFBSSxJQUFJeEIsT0FBT3VCLFdBQVc7Z0JBQ3ZDRSxPQUFPVDtnQkFDUEs7Z0JBQ0FDO2dCQUNBSSxRQUFRaEIsUUFBUWlCLENBQUMsSUFBSTtnQkFDckJDLFdBQVdwQixPQUFPcUIsVUFBVTtnQkFDNUJDLElBQUlDO2dCQUNKQyxVQUFVRDtZQUNaO1FBQ0YsRUFBRSxPQUFPRSxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw0Q0FBNENBO1lBRTFELGtEQUFrRDtZQUNsRCxJQUFJO2dCQUNGLE1BQU1FLG1CQUFtQixNQUFNN0MsNkNBQUtBLENBQUNZLEdBQUcsQ0FDdEMsR0FBR1gsaUJBQWlCLGdCQUFnQixFQUFFUyxPQUFPLEtBQUssQ0FBQyxFQUNuRDtvQkFDRUcsUUFBUTt3QkFDTmlDLFVBQVU7d0JBQ1ZoQyxRQUFRLElBQUksQ0FBQ1AsTUFBTTtvQkFDckI7Z0JBQ0Y7Z0JBR0YsTUFBTVEsT0FBTzhCLGlCQUFpQjlCLElBQUksQ0FBQ0MsT0FBTyxDQUFDLEVBQUU7Z0JBQzdDLE9BQU87b0JBQ0xOLFFBQVFBLE9BQU91QixXQUFXO29CQUMxQkMsTUFBTXhCLE9BQU91QixXQUFXO29CQUN4QkUsT0FBT3BCLEtBQUthLENBQUM7b0JBQ2JHLFFBQVFoQixLQUFLYSxDQUFDLEdBQUdiLEtBQUtlLENBQUM7b0JBQ3ZCRSxlQUFlLENBQUVqQixLQUFLYSxDQUFDLEdBQUdiLEtBQUtlLENBQUMsSUFBSWYsS0FBS2UsQ0FBQyxHQUFJO29CQUM5Q00sUUFBUXJCLEtBQUtzQixDQUFDO29CQUNkQyxXQUFXRztvQkFDWEQsSUFBSUM7b0JBQ0pDLFVBQVVEO2dCQUNaO1lBQ0YsRUFBRSxPQUFPTSxlQUFlO2dCQUN0QkgsUUFBUUQsS0FBSyxDQUFDLGlDQUFpQ0k7Z0JBQy9DLE1BQU0sSUFBSXZDLE1BQU0sQ0FBQywwQkFBMEIsRUFBRUUsUUFBUTtZQUN2RDtRQUNGO0lBQ0Y7SUFFQSw2REFBNkQ7SUFDN0QsTUFBTXNDLGtCQUNKdEMsTUFBYyxFQUNkdUMsV0FBeUQsS0FBSyxFQUM5REMsYUFBcUIsQ0FBQyxFQUN0QkMsSUFBWSxFQUNaQyxFQUFVLEVBQ2tCO1FBQzVCLElBQUk7WUFDRixNQUFNekMsV0FBVyxNQUFNWCw2Q0FBS0EsQ0FBQ1ksR0FBRyxDQUM5QixHQUFHWCxpQkFBaUIsZ0JBQWdCLEVBQUVTLE9BQU8sT0FBTyxFQUFFd0MsV0FBVyxDQUFDLEVBQUVELFNBQVMsQ0FBQyxFQUFFRSxLQUFLLENBQUMsRUFBRUMsSUFBSSxFQUM1RjtnQkFDRXZDLFFBQVE7b0JBQ05pQyxVQUFVO29CQUNWTyxNQUFNO29CQUNOQyxPQUFPO29CQUNQeEMsUUFBUSxJQUFJLENBQUNQLE1BQU07Z0JBQ3JCO1lBQ0Y7WUFHRixJQUFJLENBQUNJLFNBQVNJLElBQUksQ0FBQ0MsT0FBTyxJQUFJTCxTQUFTSSxJQUFJLENBQUNDLE9BQU8sQ0FBQ0MsTUFBTSxLQUFLLEdBQUc7Z0JBQ2hFMkIsUUFBUVcsSUFBSSxDQUFDLENBQUMsNkJBQTZCLEVBQUU3QyxRQUFRO2dCQUNyRCxPQUFPLEVBQUU7WUFDWDtZQUVBLE9BQU9DLFNBQVNJLElBQUksQ0FBQ0MsT0FBTyxDQUFDd0MsR0FBRyxDQUFDLENBQUNDLFNBQWlCO29CQUNqREMsV0FBV0QsT0FBT0UsQ0FBQztvQkFDbkJDLE1BQU1ILE9BQU8zQixDQUFDO29CQUNkK0IsTUFBTUosT0FBT0ssQ0FBQztvQkFDZEMsS0FBS04sT0FBT08sQ0FBQztvQkFDYkMsT0FBT1IsT0FBTzdCLENBQUM7b0JBQ2ZRLFFBQVFxQixPQUFPcEIsQ0FBQztnQkFDbEI7UUFDRixFQUFFLE9BQU9NLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLENBQUMsbUNBQW1DLEVBQUVqQyxPQUFPLENBQUMsQ0FBQyxFQUFFaUM7WUFFL0QsdUNBQXVDO1lBQ3ZDLElBQUlBLE1BQU1oQyxRQUFRLEVBQUU7Z0JBQ2xCaUMsUUFBUUQsS0FBSyxDQUFDLENBQUMsbUJBQW1CLEVBQUVBLE1BQU1oQyxRQUFRLENBQUN1RCxNQUFNLENBQUMsR0FBRyxFQUFFdkIsTUFBTWhDLFFBQVEsQ0FBQ3dELFVBQVUsRUFBRTtnQkFDMUZ2QixRQUFRRCxLQUFLLENBQUMsa0JBQWtCQSxNQUFNaEMsUUFBUSxDQUFDSSxJQUFJO1lBQ3JEO1lBRUEsTUFBTSxJQUFJUCxNQUFNLENBQUMsb0NBQW9DLEVBQUVFLE9BQU8sRUFBRSxFQUFFaUMsTUFBTXlCLE9BQU8sRUFBRTtRQUNuRjtJQUNGO0lBRUEsc0JBQXNCO0lBQ3RCLE1BQU1DLGtCQUFrQjNELE1BQWMsRUFBRTtRQUN0QyxJQUFJO1lBQ0YsTUFBTUMsV0FBVyxNQUFNWCw2Q0FBS0EsQ0FBQ1ksR0FBRyxDQUM5QixHQUFHWCxpQkFBaUIsc0JBQXNCLEVBQUVTLFFBQVEsRUFDcEQ7Z0JBQ0VHLFFBQVE7b0JBQ05DLFFBQVEsSUFBSSxDQUFDUCxNQUFNO2dCQUNyQjtZQUNGO1lBR0YsT0FBT0ksU0FBU0ksSUFBSSxDQUFDQyxPQUFPO1FBQzlCLEVBQUUsT0FBTzJCLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLG1DQUFtQ0E7WUFDakQsT0FBTztRQUNUO0lBQ0Y7SUFFQSxvQkFBb0I7SUFDcEIsTUFBTTJCLGtCQUFrQjtRQUN0QixJQUFJO1lBQ0YsTUFBTTNELFdBQVcsTUFBTVgsNkNBQUtBLENBQUNZLEdBQUcsQ0FDOUIsR0FBR1gsaUJBQWlCLG9CQUFvQixDQUFDLEVBQ3pDO2dCQUNFWSxRQUFRO29CQUNOQyxRQUFRLElBQUksQ0FBQ1AsTUFBTTtnQkFDckI7WUFDRjtZQUdGLE9BQU9JLFNBQVNJLElBQUk7UUFDdEIsRUFBRSxPQUFPNEIsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsaUNBQWlDQTtZQUMvQyxPQUFPO1FBQ1Q7SUFDRjtJQUVBLG9CQUFvQjtJQUNwQixNQUFNNEIsYUFBYUMsS0FBYSxFQUFFbEIsUUFBZ0IsRUFBRSxFQUFFO1FBQ3BELElBQUk7WUFDRixNQUFNM0MsV0FBVyxNQUFNWCw2Q0FBS0EsQ0FBQ1ksR0FBRyxDQUM5QixHQUFHWCxpQkFBaUIscUJBQXFCLENBQUMsRUFDMUM7Z0JBQ0VZLFFBQVE7b0JBQ040RCxRQUFRRDtvQkFDUkUsUUFBUTtvQkFDUkMsUUFBUTtvQkFDUnJCO29CQUNBeEMsUUFBUSxJQUFJLENBQUNQLE1BQU07Z0JBQ3JCO1lBQ0Y7WUFHRixPQUFPSSxTQUFTSSxJQUFJLENBQUNDLE9BQU8sSUFBSSxFQUFFO1FBQ3BDLEVBQUUsT0FBTzJCLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDJCQUEyQkE7WUFDekMsT0FBTyxFQUFFO1FBQ1g7SUFDRjtBQUNGO0FBRUEsOEJBQThCO0FBQ3ZCLE1BQU1pQyxhQUFhLElBQUl0RSxhQUFZIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFNKRml0XFxEZXNrdG9wXFxzaGl0dHlpZGVhXFxzd2luZy10cmFkZXItYWlcXHNyY1xcbGliXFxwb2x5Z29uLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBheGlvcyBmcm9tICdheGlvcydcbmltcG9ydCB7IENhbmRsZXN0aWNrRGF0YSwgU3RvY2tEYXRhIH0gZnJvbSAnQC90eXBlcy90cmFkaW5nJ1xuXG5jb25zdCBQT0xZR09OX0JBU0VfVVJMID0gJ2h0dHBzOi8vYXBpLnBvbHlnb24uaW8nXG5jb25zdCBBUElfS0VZID0gcHJvY2Vzcy5lbnYuUE9MWUdPTl9BUElfS0VZXG5cbmV4cG9ydCBjbGFzcyBQb2x5Z29uQVBJIHtcbiAgcHJpdmF0ZSBhcGlLZXk6IHN0cmluZ1xuXG4gIGNvbnN0cnVjdG9yKGFwaUtleT86IHN0cmluZykge1xuICAgIHRoaXMuYXBpS2V5ID0gYXBpS2V5IHx8IEFQSV9LRVkgfHwgJydcbiAgICBpZiAoIXRoaXMuYXBpS2V5KSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1BvbHlnb24gQVBJIGtleSBpcyByZXF1aXJlZCcpXG4gICAgfVxuICB9XG5cbiAgLy8gR2V0IGN1cnJlbnQgc3RvY2sgcXVvdGUgdXNpbmcgUG9seWdvbidzIHNuYXBzaG90IGVuZHBvaW50IChiZXR0ZXIgZm9yIHBhaWQgcGxhbnMpXG4gIGFzeW5jIGdldFN0b2NrUXVvdGUoc3ltYm9sOiBzdHJpbmcpOiBQcm9taXNlPFN0b2NrRGF0YT4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBVc2Ugc25hcHNob3QgZW5kcG9pbnQgZm9yIHJlYWwtdGltZSBkYXRhIChhdmFpbGFibGUgb24gcGFpZCBwbGFucylcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MuZ2V0KFxuICAgICAgICBgJHtQT0xZR09OX0JBU0VfVVJMfS92Mi9zbmFwc2hvdC9sb2NhbGUvdXMvbWFya2V0cy9zdG9ja3MvdGlja2Vycy8ke3N5bWJvbH1gLFxuICAgICAgICB7XG4gICAgICAgICAgcGFyYW1zOiB7XG4gICAgICAgICAgICBhcGlrZXk6IHRoaXMuYXBpS2V5XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICApXG5cbiAgICAgIGlmICghcmVzcG9uc2UuZGF0YSB8fCAhcmVzcG9uc2UuZGF0YS5yZXN1bHRzIHx8IHJlc3BvbnNlLmRhdGEucmVzdWx0cy5sZW5ndGggPT09IDApIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBObyBkYXRhIGZvdW5kIGZvciAke3N5bWJvbH1gKVxuICAgICAgfVxuXG4gICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UuZGF0YS5yZXN1bHRzWzBdXG5cbiAgICAgIGNvbnN0IHRpY2tlciA9IGRhdGEudmFsdWUgfHwgZGF0YVxuICAgICAgY29uc3QgZGF5RGF0YSA9IHRpY2tlci5kYXkgfHwge31cbiAgICAgIGNvbnN0IHByZXZEYXlEYXRhID0gdGlja2VyLnByZXZEYXkgfHwge31cbiAgICAgIGNvbnN0IGxhc3RRdW90ZSA9IHRpY2tlci5sYXN0UXVvdGUgfHwge31cbiAgICAgIGNvbnN0IGxhc3RUcmFkZSA9IHRpY2tlci5sYXN0VHJhZGUgfHwge31cblxuICAgICAgLy8gVXNlIHRoZSBtb3N0IHJlY2VudCBwcmljZSBhdmFpbGFibGVcbiAgICAgIGNvbnN0IGN1cnJlbnRQcmljZSA9IGxhc3RUcmFkZS5wIHx8IGRheURhdGEuYyB8fCBwcmV2RGF5RGF0YS5jXG4gICAgICBjb25zdCBwcmV2Q2xvc2UgPSBwcmV2RGF5RGF0YS5jIHx8IGRheURhdGEub1xuICAgICAgY29uc3QgY2hhbmdlID0gY3VycmVudFByaWNlIC0gcHJldkNsb3NlXG4gICAgICBjb25zdCBjaGFuZ2VQZXJjZW50ID0gKGNoYW5nZSAvIHByZXZDbG9zZSkgKiAxMDBcblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3ltYm9sOiBzeW1ib2wudG9VcHBlckNhc2UoKSxcbiAgICAgICAgbmFtZTogdGlja2VyLm5hbWUgfHwgc3ltYm9sLnRvVXBwZXJDYXNlKCksXG4gICAgICAgIHByaWNlOiBjdXJyZW50UHJpY2UsXG4gICAgICAgIGNoYW5nZSxcbiAgICAgICAgY2hhbmdlUGVyY2VudCxcbiAgICAgICAgdm9sdW1lOiBkYXlEYXRhLnYgfHwgMCxcbiAgICAgICAgbWFya2V0Q2FwOiB0aWNrZXIubWFya2V0X2NhcCxcbiAgICAgICAgcGU6IHVuZGVmaW5lZCxcbiAgICAgICAgZGl2aWRlbmQ6IHVuZGVmaW5lZFxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBzdG9jayBxdW90ZSBmcm9tIFBvbHlnb246JywgZXJyb3IpXG5cbiAgICAgIC8vIEZhbGxiYWNrIHRvIHByZXZpb3VzIGRheSBkYXRhIGlmIHNuYXBzaG90IGZhaWxzXG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBmYWxsYmFja1Jlc3BvbnNlID0gYXdhaXQgYXhpb3MuZ2V0KFxuICAgICAgICAgIGAke1BPTFlHT05fQkFTRV9VUkx9L3YyL2FnZ3MvdGlja2VyLyR7c3ltYm9sfS9wcmV2YCxcbiAgICAgICAgICB7XG4gICAgICAgICAgICBwYXJhbXM6IHtcbiAgICAgICAgICAgICAgYWRqdXN0ZWQ6ICd0cnVlJyxcbiAgICAgICAgICAgICAgYXBpa2V5OiB0aGlzLmFwaUtleVxuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgKVxuXG4gICAgICAgIGNvbnN0IGRhdGEgPSBmYWxsYmFja1Jlc3BvbnNlLmRhdGEucmVzdWx0c1swXVxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIHN5bWJvbDogc3ltYm9sLnRvVXBwZXJDYXNlKCksXG4gICAgICAgICAgbmFtZTogc3ltYm9sLnRvVXBwZXJDYXNlKCksXG4gICAgICAgICAgcHJpY2U6IGRhdGEuYyxcbiAgICAgICAgICBjaGFuZ2U6IGRhdGEuYyAtIGRhdGEubyxcbiAgICAgICAgICBjaGFuZ2VQZXJjZW50OiAoKGRhdGEuYyAtIGRhdGEubykgLyBkYXRhLm8pICogMTAwLFxuICAgICAgICAgIHZvbHVtZTogZGF0YS52LFxuICAgICAgICAgIG1hcmtldENhcDogdW5kZWZpbmVkLFxuICAgICAgICAgIHBlOiB1bmRlZmluZWQsXG4gICAgICAgICAgZGl2aWRlbmQ6IHVuZGVmaW5lZFxuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChmYWxsYmFja0Vycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1BvbHlnb24gZmFsbGJhY2sgYWxzbyBmYWlsZWQ6JywgZmFsbGJhY2tFcnJvcilcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZmV0Y2ggcXVvdGUgZm9yICR7c3ltYm9sfWApXG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLy8gR2V0IGhpc3RvcmljYWwgY2FuZGxlc3RpY2sgZGF0YSAob3B0aW1pemVkIGZvciBwYWlkIHBsYW5zKVxuICBhc3luYyBnZXRIaXN0b3JpY2FsRGF0YShcbiAgICBzeW1ib2w6IHN0cmluZyxcbiAgICB0aW1lc3BhbjogJ21pbnV0ZScgfCAnaG91cicgfCAnZGF5JyB8ICd3ZWVrJyB8ICdtb250aCcgPSAnZGF5JyxcbiAgICBtdWx0aXBsaWVyOiBudW1iZXIgPSAxLFxuICAgIGZyb206IHN0cmluZyxcbiAgICB0bzogc3RyaW5nXG4gICk6IFByb21pc2U8Q2FuZGxlc3RpY2tEYXRhW10+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvcy5nZXQoXG4gICAgICAgIGAke1BPTFlHT05fQkFTRV9VUkx9L3YyL2FnZ3MvdGlja2VyLyR7c3ltYm9sfS9yYW5nZS8ke211bHRpcGxpZXJ9LyR7dGltZXNwYW59LyR7ZnJvbX0vJHt0b31gLFxuICAgICAgICB7XG4gICAgICAgICAgcGFyYW1zOiB7XG4gICAgICAgICAgICBhZGp1c3RlZDogJ3RydWUnLFxuICAgICAgICAgICAgc29ydDogJ2FzYycsXG4gICAgICAgICAgICBsaW1pdDogNTAwMDAsIC8vIEhpZ2hlciBsaW1pdCBmb3IgcGFpZCBwbGFuc1xuICAgICAgICAgICAgYXBpa2V5OiB0aGlzLmFwaUtleVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgKVxuXG4gICAgICBpZiAoIXJlc3BvbnNlLmRhdGEucmVzdWx0cyB8fCByZXNwb25zZS5kYXRhLnJlc3VsdHMubGVuZ3RoID09PSAwKSB7XG4gICAgICAgIGNvbnNvbGUud2FybihgTm8gaGlzdG9yaWNhbCBkYXRhIGZvdW5kIGZvciAke3N5bWJvbH1gKVxuICAgICAgICByZXR1cm4gW11cbiAgICAgIH1cblxuICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGEucmVzdWx0cy5tYXAoKGNhbmRsZTogYW55KSA9PiAoe1xuICAgICAgICB0aW1lc3RhbXA6IGNhbmRsZS50LFxuICAgICAgICBvcGVuOiBjYW5kbGUubyxcbiAgICAgICAgaGlnaDogY2FuZGxlLmgsXG4gICAgICAgIGxvdzogY2FuZGxlLmwsXG4gICAgICAgIGNsb3NlOiBjYW5kbGUuYyxcbiAgICAgICAgdm9sdW1lOiBjYW5kbGUudlxuICAgICAgfSkpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoYEVycm9yIGZldGNoaW5nIGhpc3RvcmljYWwgZGF0YSBmb3IgJHtzeW1ib2x9OmAsIGVycm9yKVxuXG4gICAgICAvLyBMb2cgdGhlIHNwZWNpZmljIGVycm9yIGZvciBkZWJ1Z2dpbmdcbiAgICAgIGlmIChlcnJvci5yZXNwb25zZSkge1xuICAgICAgICBjb25zb2xlLmVycm9yKGBQb2x5Z29uIEFQSSBFcnJvcjogJHtlcnJvci5yZXNwb25zZS5zdGF0dXN9IC0gJHtlcnJvci5yZXNwb25zZS5zdGF0dXNUZXh0fWApXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1Jlc3BvbnNlIGRhdGE6JywgZXJyb3IucmVzcG9uc2UuZGF0YSlcbiAgICAgIH1cblxuICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZmV0Y2ggaGlzdG9yaWNhbCBkYXRhIGZvciAke3N5bWJvbH06ICR7ZXJyb3IubWVzc2FnZX1gKVxuICAgIH1cbiAgfVxuXG4gIC8vIEdldCBjb21wYW55IGRldGFpbHNcbiAgYXN5bmMgZ2V0Q29tcGFueURldGFpbHMoc3ltYm9sOiBzdHJpbmcpIHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvcy5nZXQoXG4gICAgICAgIGAke1BPTFlHT05fQkFTRV9VUkx9L3YzL3JlZmVyZW5jZS90aWNrZXJzLyR7c3ltYm9sfWAsXG4gICAgICAgIHtcbiAgICAgICAgICBwYXJhbXM6IHtcbiAgICAgICAgICAgIGFwaWtleTogdGhpcy5hcGlLZXlcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIClcblxuICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGEucmVzdWx0c1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBjb21wYW55IGRldGFpbHM6JywgZXJyb3IpXG4gICAgICByZXR1cm4gbnVsbFxuICAgIH1cbiAgfVxuXG4gIC8vIEdldCBtYXJrZXQgc3RhdHVzXG4gIGFzeW5jIGdldE1hcmtldFN0YXR1cygpIHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvcy5nZXQoXG4gICAgICAgIGAke1BPTFlHT05fQkFTRV9VUkx9L3YxL21hcmtldHN0YXR1cy9ub3dgLFxuICAgICAgICB7XG4gICAgICAgICAgcGFyYW1zOiB7XG4gICAgICAgICAgICBhcGlrZXk6IHRoaXMuYXBpS2V5XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICApXG5cbiAgICAgIHJldHVybiByZXNwb25zZS5kYXRhXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIG1hcmtldCBzdGF0dXM6JywgZXJyb3IpXG4gICAgICByZXR1cm4gbnVsbFxuICAgIH1cbiAgfVxuXG4gIC8vIFNlYXJjaCBmb3Igc3RvY2tzXG4gIGFzeW5jIHNlYXJjaFN0b2NrcyhxdWVyeTogc3RyaW5nLCBsaW1pdDogbnVtYmVyID0gMTApIHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvcy5nZXQoXG4gICAgICAgIGAke1BPTFlHT05fQkFTRV9VUkx9L3YzL3JlZmVyZW5jZS90aWNrZXJzYCxcbiAgICAgICAge1xuICAgICAgICAgIHBhcmFtczoge1xuICAgICAgICAgICAgc2VhcmNoOiBxdWVyeSxcbiAgICAgICAgICAgIG1hcmtldDogJ3N0b2NrcycsXG4gICAgICAgICAgICBhY3RpdmU6ICd0cnVlJyxcbiAgICAgICAgICAgIGxpbWl0LFxuICAgICAgICAgICAgYXBpa2V5OiB0aGlzLmFwaUtleVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgKVxuXG4gICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YS5yZXN1bHRzIHx8IFtdXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHNlYXJjaGluZyBzdG9ja3M6JywgZXJyb3IpXG4gICAgICByZXR1cm4gW11cbiAgICB9XG4gIH1cbn1cblxuLy8gQ3JlYXRlIGEgc2luZ2xldG9uIGluc3RhbmNlXG5leHBvcnQgY29uc3QgcG9seWdvbkFQSSA9IG5ldyBQb2x5Z29uQVBJKClcbiJdLCJuYW1lcyI6WyJheGlvcyIsIlBPTFlHT05fQkFTRV9VUkwiLCJBUElfS0VZIiwicHJvY2VzcyIsImVudiIsIlBPTFlHT05fQVBJX0tFWSIsIlBvbHlnb25BUEkiLCJhcGlLZXkiLCJFcnJvciIsImdldFN0b2NrUXVvdGUiLCJzeW1ib2wiLCJyZXNwb25zZSIsImdldCIsInBhcmFtcyIsImFwaWtleSIsImRhdGEiLCJyZXN1bHRzIiwibGVuZ3RoIiwidGlja2VyIiwidmFsdWUiLCJkYXlEYXRhIiwiZGF5IiwicHJldkRheURhdGEiLCJwcmV2RGF5IiwibGFzdFF1b3RlIiwibGFzdFRyYWRlIiwiY3VycmVudFByaWNlIiwicCIsImMiLCJwcmV2Q2xvc2UiLCJvIiwiY2hhbmdlIiwiY2hhbmdlUGVyY2VudCIsInRvVXBwZXJDYXNlIiwibmFtZSIsInByaWNlIiwidm9sdW1lIiwidiIsIm1hcmtldENhcCIsIm1hcmtldF9jYXAiLCJwZSIsInVuZGVmaW5lZCIsImRpdmlkZW5kIiwiZXJyb3IiLCJjb25zb2xlIiwiZmFsbGJhY2tSZXNwb25zZSIsImFkanVzdGVkIiwiZmFsbGJhY2tFcnJvciIsImdldEhpc3RvcmljYWxEYXRhIiwidGltZXNwYW4iLCJtdWx0aXBsaWVyIiwiZnJvbSIsInRvIiwic29ydCIsImxpbWl0Iiwid2FybiIsIm1hcCIsImNhbmRsZSIsInRpbWVzdGFtcCIsInQiLCJvcGVuIiwiaGlnaCIsImgiLCJsb3ciLCJsIiwiY2xvc2UiLCJzdGF0dXMiLCJzdGF0dXNUZXh0IiwibWVzc2FnZSIsImdldENvbXBhbnlEZXRhaWxzIiwiZ2V0TWFya2V0U3RhdHVzIiwic2VhcmNoU3RvY2tzIiwicXVlcnkiLCJzZWFyY2giLCJtYXJrZXQiLCJhY3RpdmUiLCJwb2x5Z29uQVBJIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/polygon.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/swingStrategies.ts":
/*!************************************!*\
  !*** ./src/lib/swingStrategies.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SwingTradingStrategies: () => (/* binding */ SwingTradingStrategies)\n/* harmony export */ });\n/* harmony import */ var _indicators__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./indicators */ \"(rsc)/./src/lib/indicators.ts\");\n\nclass SwingTradingStrategies {\n    static{\n        this.DEFAULT_CRITERIA = {\n            minPrice: 5.0,\n            minVolume: 500000,\n            minMarketCap: *********,\n            minATRPercent: 2.0,\n            above200SMA: true,\n            maxDistanceFrom8EMA: 2.0,\n            minRoomToResistance: 1.0,\n            scanTimeStart: \"12:00\",\n            scanTimeEnd: \"16:00\",\n            maxRiskPerTrade: 1.0,\n            maxConcurrentPositions: 3\n        };\n    }\n    // Strategy #1: Overnight Momentum Continuation\n    static analyzeOvernightMomentum(symbol, candles, quote, accountSize = 100000) {\n        if (candles.length < 50) return null;\n        const closes = candles.map((c)=>c.close);\n        const highs = candles.map((c)=>c.high);\n        const lows = candles.map((c)=>c.low);\n        const volumes = candles.map((c)=>c.volume);\n        const currentPrice = quote.price;\n        const currentVolume = quote.volume;\n        const changePercent = quote.changePercent;\n        // Calculate technical indicators (adjusted for shorter history)\n        const sma50 = _indicators__WEBPACK_IMPORTED_MODULE_0__.TechnicalIndicators.sma(closes, Math.min(50, closes.length - 1)) // Use 50-day instead of 200-day\n        ;\n        const ema8 = _indicators__WEBPACK_IMPORTED_MODULE_0__.TechnicalIndicators.ema(closes, Math.min(8, closes.length - 1));\n        const atr = this.calculateATR(candles, Math.min(14, candles.length - 1));\n        const current50SMA = sma50[sma50.length - 1];\n        const current8EMA = ema8[ema8.length - 1];\n        const currentATR = atr[atr.length - 1];\n        // Basic qualification filters (using 50-day SMA instead of 200-day)\n        if (!this.passesBasicFilters(quote, currentVolume, current50SMA, currentPrice)) {\n            return null;\n        }\n        // Check if it's a top intraday gainer (top decile movers)\n        if (changePercent < 2.0) return null // Minimum 2% gain for momentum\n        ;\n        // Check distance from 8-EMA (not wildly extended)\n        const distanceFrom8EMA = Math.abs(currentPrice - current8EMA) / currentATR;\n        if (distanceFrom8EMA > this.DEFAULT_CRITERIA.maxDistanceFrom8EMA) return null;\n        // Look for defended intraday level (simplified - using VWAP proxy)\n        const vwap = this.calculateVWAP(candles.slice(-1)[0]);\n        const keyLevel = Math.max(vwap, currentPrice * 0.98) // Approximate defended level\n        ;\n        // Check if holding gains (>50% of day's range)\n        const todayHigh = highs[highs.length - 1];\n        const todayLow = lows[lows.length - 1];\n        const dayRange = todayHigh - todayLow;\n        const currentFromLow = currentPrice - todayLow;\n        const holdingGainsPercent = currentFromLow / dayRange;\n        if (holdingGainsPercent < 0.5) return null // Must hold >50% of range\n        ;\n        // Calculate room to next resistance\n        const roomToResistance = this.calculateRoomToResistance(candles, currentPrice, currentATR);\n        if (roomToResistance < this.DEFAULT_CRITERIA.minRoomToResistance) return null;\n        // Position sizing (risk 0.5-1% of account)\n        const riskPercent = 0.75 // 0.75% risk for overnight holds\n        ;\n        const stopDistance = currentPrice - keyLevel;\n        const riskAmount = accountSize * (riskPercent / 100);\n        const positionSize = Math.floor(riskAmount / stopDistance);\n        // Targets: Pre-market scale at 3-5%, opening hour at 5-8%\n        const targets = [\n            currentPrice * 1.03,\n            currentPrice * 1.05,\n            currentPrice * 1.08 // 8% extended target\n        ];\n        const confidence = this.calculateOvernightConfidence(changePercent, holdingGainsPercent, currentVolume, roomToResistance);\n        return {\n            strategy: 'overnight_momentum',\n            confidence,\n            entryPrice: currentPrice,\n            stopLoss: keyLevel,\n            targets,\n            positionSize,\n            riskAmount,\n            holdingPeriod: 'overnight',\n            keyLevel,\n            invalidation: `Daily close below ${keyLevel.toFixed(2)} or gap down below level`,\n            notes: [\n                'Enter final 30-60 min before close',\n                'Exit pre-market on strength or first 45min',\n                'Hard stop if gaps below defended level',\n                'Scale out aggressively if gaps >1 ATR up'\n            ],\n            // Precise entry execution\n            preciseEntry: {\n                price: currentPrice * 0.999,\n                orderType: 'limit',\n                timing: 'Final 30-60 minutes before market close',\n                conditions: [\n                    `Stock holding above ${keyLevel.toFixed(2)} (defended level)`,\n                    `Volume above ${(currentVolume * 0.8).toLocaleString()} shares`,\n                    `Price above ${current8EMA.toFixed(2)} (8-EMA)`,\n                    'No late-day selling pressure'\n                ],\n                urgency: 'wait_for_pullback'\n            },\n            // Precise exit execution\n            preciseExit: {\n                stopLoss: {\n                    price: keyLevel * 0.995,\n                    orderType: 'stop',\n                    reason: 'Defended level broken - invalidates setup',\n                    triggerConditions: [\n                        'Any close below defended level',\n                        'Gap down below key level',\n                        'Heavy selling into close'\n                    ]\n                },\n                takeProfits: [\n                    {\n                        price: targets[0],\n                        percentage: 33,\n                        target: 'T1 - Pre-market (3%)',\n                        reasoning: 'Take profits on pre-market strength',\n                        orderType: 'limit'\n                    },\n                    {\n                        price: targets[1],\n                        percentage: 33,\n                        target: 'T2 - Opening hour (5%)',\n                        reasoning: 'Scale out on opening momentum',\n                        orderType: 'limit'\n                    },\n                    {\n                        price: targets[2],\n                        percentage: 34,\n                        target: 'T3 - Extended (8%)',\n                        reasoning: 'Final exit on extended move',\n                        orderType: 'limit'\n                    }\n                ]\n            },\n            // Risk management details\n            riskManagement: {\n                maxRiskDollars: riskAmount,\n                accountRiskPercent: riskPercent,\n                sharesForRisk: positionSize,\n                invalidationPrice: keyLevel,\n                timeStopHours: 18,\n                maxDrawdownPercent: 2.0\n            },\n            // Execution plan\n            executionPlan: {\n                entryInstructions: [\n                    '1. Wait for final 30-60 minutes before close',\n                    '2. Confirm stock is holding defended level',\n                    '3. Place limit order slightly below current price',\n                    '4. Cancel if not filled by close'\n                ],\n                exitInstructions: [\n                    '1. Set stop-loss immediately after fill',\n                    '2. Monitor pre-market for gap up',\n                    '3. Scale out 1/3 at each target level',\n                    '4. Exit all by 10:15 AM if no momentum'\n                ],\n                monitoringPoints: [\n                    'Pre-market price action and volume',\n                    'Opening gap and first 15-minute candle',\n                    'Key level defense throughout session',\n                    'Overall market sentiment'\n                ],\n                contingencyPlans: [\n                    'If gaps down: Exit immediately at market open',\n                    'If gaps up >2%: Scale out more aggressively',\n                    'If sideways: Exit by 10:15 AM',\n                    'If market weakness: Tighten stops'\n                ]\n            }\n        };\n    }\n    // Strategy #2: Technical Breakout Trend-Follow (8-EMA + 200-SMA)\n    static analyzeTechnicalBreakout(symbol, candles, quote, accountSize = 100000) {\n        if (candles.length < 50) return null;\n        const closes = candles.map((c)=>c.close);\n        const volumes = candles.map((c)=>c.volume);\n        const currentPrice = quote.price;\n        // Calculate technical indicators (adjusted for shorter history)\n        const sma50 = _indicators__WEBPACK_IMPORTED_MODULE_0__.TechnicalIndicators.sma(closes, Math.min(50, closes.length - 1));\n        const ema8 = _indicators__WEBPACK_IMPORTED_MODULE_0__.TechnicalIndicators.ema(closes, Math.min(8, closes.length - 1));\n        const atr = this.calculateATR(candles, Math.min(14, candles.length - 1));\n        const current50SMA = sma50[sma50.length - 1];\n        const current8EMA = ema8[ema8.length - 1];\n        const currentATR = atr[atr.length - 1];\n        // Basic qualification filters (using 50-day SMA)\n        if (!this.passesBasicFilters(quote, quote.volume, current50SMA, currentPrice)) {\n            return null;\n        }\n        // Check if in clear uptrend above 50-SMA (adjusted from 200-SMA)\n        if (currentPrice <= current50SMA) return null;\n        // Check 8-EMA behavior - should be \"hugging\" the 8-EMA\n        const distanceFrom8EMA = Math.abs(currentPrice - current8EMA);\n        const emaDistancePercent = distanceFrom8EMA / currentPrice * 100;\n        // Should be close to 8-EMA (within 2-3% for quality trend-follow)\n        if (emaDistancePercent > 3.0) return null;\n        // Check for recent breakout or EMA reclaim\n        const recentEMAReclaim = this.checkEMAReclaim(closes, ema8, 5) // Last 5 days\n        ;\n        if (!recentEMAReclaim) return null;\n        // Volume expansion check\n        const avgVolume = _indicators__WEBPACK_IMPORTED_MODULE_0__.TechnicalIndicators.sma(volumes, 20);\n        const currentAvgVolume = avgVolume[avgVolume.length - 1];\n        const volumeExpansion = quote.volume / currentAvgVolume;\n        if (volumeExpansion < 1.2) return null // Need some volume expansion\n        ;\n        // Calculate room to next resistance\n        const roomToResistance = this.calculateRoomToResistance(candles, currentPrice, currentATR);\n        if (roomToResistance < 1.5) return null // Need more room for trend-follow\n        ;\n        // Position sizing (risk 1% of account)\n        const riskPercent = 1.0;\n        const stopDistance = currentPrice - current8EMA // Stop at 8-EMA break\n        ;\n        const riskAmount = accountSize * (riskPercent / 100);\n        const positionSize = Math.floor(riskAmount / stopDistance);\n        // Targets: Scale at resistance levels\n        const targets = [\n            currentPrice * 1.05,\n            currentPrice * 1.10,\n            currentPrice * 1.15 // 15% extended target\n        ];\n        const confidence = this.calculateBreakoutConfidence(emaDistancePercent, volumeExpansion, roomToResistance, quote.changePercent);\n        return {\n            strategy: 'technical_breakout',\n            confidence,\n            entryPrice: currentPrice,\n            stopLoss: current8EMA,\n            targets,\n            positionSize,\n            riskAmount,\n            holdingPeriod: 'days_to_weeks',\n            keyLevel: current8EMA,\n            invalidation: `Daily close below 8-EMA (${current8EMA.toFixed(2)})`,\n            notes: [\n                'Enter on afternoon reclaim of 8-EMA',\n                'Add only on higher-low pullbacks to 8-EMA',\n                'Scale partials at resistance levels',\n                'Exit on daily close below 8-EMA'\n            ],\n            // Precise entry execution\n            preciseEntry: {\n                price: current8EMA * 1.002,\n                orderType: 'limit',\n                timing: 'Afternoon reclaim or first pullback to 8-EMA',\n                conditions: [\n                    `Price reclaiming ${current8EMA.toFixed(2)} (8-EMA) with volume`,\n                    `Above ${current50SMA.toFixed(2)} (50-day SMA)`,\n                    `Volume expansion above ${(quote.volume * 1.2).toLocaleString()}`,\n                    'No major resistance overhead'\n                ],\n                urgency: 'breakout_confirmation'\n            },\n            // Precise exit execution\n            preciseExit: {\n                stopLoss: {\n                    price: current8EMA * 0.998,\n                    orderType: 'stop',\n                    reason: '8-EMA breakdown invalidates trend-follow setup',\n                    triggerConditions: [\n                        'Daily close below 8-EMA',\n                        'Intraday break with volume',\n                        'Loss of 50-SMA support'\n                    ]\n                },\n                takeProfits: [\n                    {\n                        price: targets[0],\n                        percentage: 25,\n                        target: 'R1 - First resistance (5%)',\n                        reasoning: 'Take partial profits at first resistance',\n                        orderType: 'limit'\n                    },\n                    {\n                        price: targets[1],\n                        percentage: 35,\n                        target: 'R2 - Major resistance (10%)',\n                        reasoning: 'Scale out at major resistance level',\n                        orderType: 'limit'\n                    },\n                    {\n                        price: targets[2],\n                        percentage: 40,\n                        target: 'R3 - Extension (15%)',\n                        reasoning: 'Final exit on extended breakout',\n                        orderType: 'limit'\n                    }\n                ]\n            },\n            // Risk management details\n            riskManagement: {\n                maxRiskDollars: riskAmount,\n                accountRiskPercent: riskPercent,\n                sharesForRisk: positionSize,\n                invalidationPrice: current8EMA,\n                timeStopHours: 72,\n                maxDrawdownPercent: 3.0\n            },\n            // Execution plan\n            executionPlan: {\n                entryInstructions: [\n                    '1. Wait for afternoon reclaim of 8-EMA',\n                    '2. Confirm volume expansion on breakout',\n                    '3. Place limit order above 8-EMA',\n                    '4. Only enter on higher-low pullbacks'\n                ],\n                exitInstructions: [\n                    '1. Set stop-loss below 8-EMA immediately',\n                    '2. Scale out 25% at first resistance',\n                    '3. Trail stop to breakeven after R1',\n                    '4. Exit remaining on 8-EMA breakdown'\n                ],\n                monitoringPoints: [\n                    '8-EMA as dynamic support/resistance',\n                    'Volume confirmation on moves',\n                    'Overall market trend alignment',\n                    'Sector strength/weakness'\n                ],\n                contingencyPlans: [\n                    'If fails at resistance: Tighten stops',\n                    'If market weakness: Exit early',\n                    'If sector rotation: Consider exit',\n                    'If extended: Take more profits'\n                ]\n            }\n        };\n    }\n    // Helper methods\n    static passesBasicFilters(quote, volume, sma50, price) {\n        return price >= this.DEFAULT_CRITERIA.minPrice && volume >= this.DEFAULT_CRITERIA.minVolume && (quote.marketCap || 0) >= this.DEFAULT_CRITERIA.minMarketCap && price > sma50 // Using 50-day SMA instead of 200-day for shorter history\n        ;\n    }\n    static calculateATR(candles, period) {\n        const trueRanges = [];\n        for(let i = 1; i < candles.length; i++){\n            const high = candles[i].high;\n            const low = candles[i].low;\n            const prevClose = candles[i - 1].close;\n            const tr = Math.max(high - low, Math.abs(high - prevClose), Math.abs(low - prevClose));\n            trueRanges.push(tr);\n        }\n        return _indicators__WEBPACK_IMPORTED_MODULE_0__.TechnicalIndicators.sma(trueRanges, period);\n    }\n    static calculateVWAP(candle) {\n        // Simplified VWAP calculation using typical price\n        return (candle.high + candle.low + candle.close) / 3;\n    }\n    static calculateRoomToResistance(candles, currentPrice, atr) {\n        // Find recent highs as resistance levels\n        const recentHighs = candles.slice(-20).map((c)=>c.high);\n        const maxHigh = Math.max(...recentHighs);\n        const roomToHigh = maxHigh - currentPrice;\n        return roomToHigh / atr;\n    }\n    static checkEMAReclaim(closes, ema8, lookback) {\n        // Check if price recently reclaimed 8-EMA\n        for(let i = Math.max(0, closes.length - lookback); i < closes.length - 1; i++){\n            if (closes[i] < ema8[i] && closes[i + 1] > ema8[i + 1]) {\n                return true // Found a reclaim\n                ;\n            }\n        }\n        return false;\n    }\n    static calculateOvernightConfidence(changePercent, holdingGains, volume, roomToResistance) {\n        let confidence = 50;\n        // Change percent bonus\n        if (changePercent > 5) confidence += 15;\n        else if (changePercent > 3) confidence += 10;\n        else if (changePercent > 2) confidence += 5;\n        // Holding gains bonus\n        if (holdingGains > 0.8) confidence += 15;\n        else if (holdingGains > 0.6) confidence += 10;\n        else if (holdingGains > 0.5) confidence += 5;\n        // Volume bonus\n        if (volume > 2000000) confidence += 10;\n        else if (volume > 1000000) confidence += 5;\n        // Room to resistance\n        if (roomToResistance > 2) confidence += 10;\n        else if (roomToResistance > 1.5) confidence += 5;\n        return Math.min(95, Math.max(30, confidence));\n    }\n    static calculateBreakoutConfidence(emaDistance, volumeExpansion, roomToResistance, changePercent) {\n        let confidence = 60;\n        // EMA proximity bonus (closer is better for trend-follow)\n        if (emaDistance < 1) confidence += 15;\n        else if (emaDistance < 2) confidence += 10;\n        else if (emaDistance < 3) confidence += 5;\n        // Volume expansion bonus\n        if (volumeExpansion > 2) confidence += 15;\n        else if (volumeExpansion > 1.5) confidence += 10;\n        else if (volumeExpansion > 1.2) confidence += 5;\n        // Room to resistance\n        if (roomToResistance > 3) confidence += 15;\n        else if (roomToResistance > 2) confidence += 10;\n        else if (roomToResistance > 1.5) confidence += 5;\n        // Positive momentum\n        if (changePercent > 2) confidence += 5;\n        return Math.min(95, Math.max(40, confidence));\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/swingStrategies.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/date-fns"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fscanner%2Fstrategies%2Froute&page=%2Fapi%2Fscanner%2Fstrategies%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscanner%2Fstrategies%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();