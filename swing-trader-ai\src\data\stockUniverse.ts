/**
 * Comprehensive stock universe for swing trading
 * Focus on liquid, volatile large-cap stocks with good technical patterns
 */

export interface StockInfo {
  symbol: string
  name: string
  sector: string
  marketCap: number // in billions
  avgVolume: number // average daily volume
  volatility: 'High' | 'Medium' | 'Low'
  swingTradingRating: number // 1-10 scale
}

export const SWING_TRADING_UNIVERSE: StockInfo[] = [
  // Technology - High Growth & Volatility
  { symbol: 'AAPL', name: 'Apple Inc.', sector: 'Technology', marketCap: 3000, avgVolume: 50000000, volatility: 'Medium', swingTradingRating: 9 },
  { symbol: 'MSFT', name: 'Microsoft Corporation', sector: 'Technology', marketCap: 2800, avgVolume: 25000000, volatility: 'Medium', swingTradingRating: 8 },
  { symbol: 'NVDA', name: 'NVIDIA Corporation', sector: 'Technology', marketCap: 1800, avgVolume: 45000000, volatility: 'High', swingTradingRating: 10 },
  { symbol: 'GOOGL', name: 'Alphabet Inc. Class A', sector: 'Technology', marketCap: 1700, avgVolume: 25000000, volatility: 'Medium', swingTradingRating: 8 },
  { symbol: 'GOOG', name: 'Alphabet Inc. Class C', sector: 'Technology', marketCap: 1700, avgVolume: ********, volatility: 'Medium', swingTradingRating: 8 },
  { symbol: 'META', name: 'Meta Platforms Inc.', sector: 'Technology', marketCap: 800, avgVolume: ********, volatility: 'High', swingTradingRating: 9 },
  { symbol: 'TSLA', name: 'Tesla Inc.', sector: 'Technology', marketCap: 800, avgVolume: 75000000, volatility: 'High', swingTradingRating: 10 },
  { symbol: 'AMZN', name: 'Amazon.com Inc.', sector: 'Technology', marketCap: 1500, avgVolume: ********, volatility: 'Medium', swingTradingRating: 8 },
  { symbol: 'NFLX', name: 'Netflix Inc.', sector: 'Technology', marketCap: 200, avgVolume: 8000000, volatility: 'High', swingTradingRating: 9 },
  { symbol: 'ORCL', name: 'Oracle Corporation', sector: 'Technology', marketCap: 350, avgVolume: ********, volatility: 'Medium', swingTradingRating: 7 },
  { symbol: 'CRM', name: 'Salesforce Inc.', sector: 'Technology', marketCap: 250, avgVolume: 6000000, volatility: 'High', swingTradingRating: 8 },
  { symbol: 'ADBE', name: 'Adobe Inc.', sector: 'Technology', marketCap: 220, avgVolume: 3000000, volatility: 'Medium', swingTradingRating: 7 },
  { symbol: 'AVGO', name: 'Broadcom Inc.', sector: 'Technology', marketCap: 600, avgVolume: 2000000, volatility: 'Medium', swingTradingRating: 8 },
  { symbol: 'TSM', name: 'Taiwan Semiconductor', sector: 'Technology', marketCap: 500, avgVolume: ********, volatility: 'Medium', swingTradingRating: 8 },
  { symbol: 'ASML', name: 'ASML Holding N.V.', sector: 'Technology', marketCap: 300, avgVolume: 1500000, volatility: 'High', swingTradingRating: 8 },
  { symbol: 'AMD', name: 'Advanced Micro Devices', sector: 'Technology', marketCap: 220, avgVolume: 45000000, volatility: 'High', swingTradingRating: 9 },
  { symbol: 'INTC', name: 'Intel Corporation', sector: 'Technology', marketCap: 200, avgVolume: 25000000, volatility: 'Medium', swingTradingRating: 7 },
  { symbol: 'QCOM', name: 'QUALCOMM Incorporated', sector: 'Technology', marketCap: 180, avgVolume: 8000000, volatility: 'Medium', swingTradingRating: 8 },
  { symbol: 'PLTR', name: 'Palantir Technologies', sector: 'Technology', marketCap: 60, avgVolume: ********, volatility: 'High', swingTradingRating: 9 },
  { symbol: 'SNOW', name: 'Snowflake Inc.', sector: 'Technology', marketCap: 50, avgVolume: 4000000, volatility: 'High', swingTradingRating: 8 },

  // Financial Services - Interest Rate Sensitive
  { symbol: 'JPM', name: 'JPMorgan Chase & Co.', sector: 'Financial', marketCap: 500, avgVolume: ********, volatility: 'Medium', swingTradingRating: 8 },
  { symbol: 'BAC', name: 'Bank of America Corp.', sector: 'Financial', marketCap: 300, avgVolume: ********, volatility: 'Medium', swingTradingRating: 8 },
  { symbol: 'WFC', name: 'Wells Fargo & Company', sector: 'Financial', marketCap: 180, avgVolume: ********, volatility: 'Medium', swingTradingRating: 7 },
  { symbol: 'GS', name: 'Goldman Sachs Group', sector: 'Financial', marketCap: 120, avgVolume: 2500000, volatility: 'High', swingTradingRating: 8 },
  { symbol: 'MS', name: 'Morgan Stanley', sector: 'Financial', marketCap: 150, avgVolume: 8000000, volatility: 'High', swingTradingRating: 8 },
  { symbol: 'C', name: 'Citigroup Inc.', sector: 'Financial', marketCap: 120, avgVolume: ********, volatility: 'High', swingTradingRating: 8 },
  { symbol: 'BRK.B', name: 'Berkshire Hathaway B', sector: 'Financial', marketCap: 900, avgVolume: 4000000, volatility: 'Low', swingTradingRating: 6 },
  { symbol: 'V', name: 'Visa Inc.', sector: 'Financial', marketCap: 500, avgVolume: 6000000, volatility: 'Medium', swingTradingRating: 7 },
  { symbol: 'MA', name: 'Mastercard Inc.', sector: 'Financial', marketCap: 400, avgVolume: 3000000, volatility: 'Medium', swingTradingRating: 7 },
  { symbol: 'PYPL', name: 'PayPal Holdings Inc.', sector: 'Financial', marketCap: 70, avgVolume: ********, volatility: 'High', swingTradingRating: 8 },

  // Healthcare & Biotech - Defensive with Growth
  { symbol: 'JNJ', name: 'Johnson & Johnson', sector: 'Healthcare', marketCap: 450, avgVolume: 7000000, volatility: 'Low', swingTradingRating: 6 },
  { symbol: 'UNH', name: 'UnitedHealth Group', sector: 'Healthcare', marketCap: 500, avgVolume: 3000000, volatility: 'Medium', swingTradingRating: 7 },
  { symbol: 'PFE', name: 'Pfizer Inc.', sector: 'Healthcare', marketCap: 160, avgVolume: 25000000, volatility: 'Medium', swingTradingRating: 7 },
  { symbol: 'ABBV', name: 'AbbVie Inc.', sector: 'Healthcare', marketCap: 300, avgVolume: 6000000, volatility: 'Medium', swingTradingRating: 7 },
  { symbol: 'TMO', name: 'Thermo Fisher Scientific', sector: 'Healthcare', marketCap: 200, avgVolume: 1500000, volatility: 'Medium', swingTradingRating: 7 },
  { symbol: 'DHR', name: 'Danaher Corporation', sector: 'Healthcare', marketCap: 180, avgVolume: 2000000, volatility: 'Medium', swingTradingRating: 7 },
  { symbol: 'BMY', name: 'Bristol Myers Squibb', sector: 'Healthcare', marketCap: 120, avgVolume: 10000000, volatility: 'Medium', swingTradingRating: 7 },
  { symbol: 'AMGN', name: 'Amgen Inc.', sector: 'Healthcare', marketCap: 150, avgVolume: 2500000, volatility: 'Medium', swingTradingRating: 7 },
  { symbol: 'GILD', name: 'Gilead Sciences Inc.', sector: 'Healthcare', marketCap: 80, avgVolume: 6000000, volatility: 'Medium', swingTradingRating: 7 },
  { symbol: 'MRNA', name: 'Moderna Inc.', sector: 'Healthcare', marketCap: 30, avgVolume: 8000000, volatility: 'High', swingTradingRating: 9 },

  // Consumer & Retail - Economic Sensitive
  { symbol: 'WMT', name: 'Walmart Inc.', sector: 'Consumer', marketCap: 600, avgVolume: 8000000, volatility: 'Low', swingTradingRating: 6 },
  { symbol: 'HD', name: 'Home Depot Inc.', sector: 'Consumer', marketCap: 350, avgVolume: 3000000, volatility: 'Medium', swingTradingRating: 7 },
  { symbol: 'MCD', name: 'McDonald\'s Corporation', sector: 'Consumer', marketCap: 200, avgVolume: 2500000, volatility: 'Low', swingTradingRating: 6 },
  { symbol: 'NKE', name: 'Nike Inc.', sector: 'Consumer', marketCap: 150, avgVolume: 6000000, volatility: 'Medium', swingTradingRating: 7 },
  { symbol: 'SBUX', name: 'Starbucks Corporation', sector: 'Consumer', marketCap: 110, avgVolume: 6000000, volatility: 'Medium', swingTradingRating: 7 },
  { symbol: 'TGT', name: 'Target Corporation', sector: 'Consumer', marketCap: 70, avgVolume: 4000000, volatility: 'High', swingTradingRating: 8 },
  { symbol: 'COST', name: 'Costco Wholesale Corp.', sector: 'Consumer', marketCap: 350, avgVolume: 2000000, volatility: 'Medium', swingTradingRating: 7 },
  { symbol: 'LOW', name: 'Lowe\'s Companies Inc.', sector: 'Consumer', marketCap: 150, avgVolume: 3000000, volatility: 'Medium', swingTradingRating: 7 },

  // Energy - Commodity Driven
  { symbol: 'XOM', name: 'Exxon Mobil Corporation', sector: 'Energy', marketCap: 450, avgVolume: ********, volatility: 'High', swingTradingRating: 8 },
  { symbol: 'CVX', name: 'Chevron Corporation', sector: 'Energy', marketCap: 300, avgVolume: ********, volatility: 'Medium', swingTradingRating: 7 },
  { symbol: 'COP', name: 'ConocoPhillips', sector: 'Energy', marketCap: 150, avgVolume: 8000000, volatility: 'High', swingTradingRating: 8 },
  { symbol: 'SLB', name: 'Schlumberger Limited', sector: 'Energy', marketCap: 60, avgVolume: ********, volatility: 'High', swingTradingRating: 8 },
  { symbol: 'EOG', name: 'EOG Resources Inc.', sector: 'Energy', marketCap: 70, avgVolume: 4000000, volatility: 'High', swingTradingRating: 8 },

  // Industrial & Materials
  { symbol: 'CAT', name: 'Caterpillar Inc.', sector: 'Industrial', marketCap: 180, avgVolume: 3000000, volatility: 'High', swingTradingRating: 8 },
  { symbol: 'BA', name: 'Boeing Company', sector: 'Industrial', marketCap: 120, avgVolume: 8000000, volatility: 'High', swingTradingRating: 9 },
  { symbol: 'GE', name: 'General Electric Co.', sector: 'Industrial', marketCap: 180, avgVolume: 45000000, volatility: 'High', swingTradingRating: 9 },
  { symbol: 'MMM', name: '3M Company', sector: 'Industrial', marketCap: 60, avgVolume: 3000000, volatility: 'Medium', swingTradingRating: 7 },
  { symbol: 'HON', name: 'Honeywell International', sector: 'Industrial', marketCap: 140, avgVolume: 2500000, volatility: 'Medium', swingTradingRating: 7 },

  // Communication & Media
  { symbol: 'DIS', name: 'Walt Disney Company', sector: 'Media', marketCap: 180, avgVolume: ********, volatility: 'High', swingTradingRating: 8 },
  { symbol: 'CMCSA', name: 'Comcast Corporation', sector: 'Media', marketCap: 150, avgVolume: ********, volatility: 'Medium', swingTradingRating: 7 },
  { symbol: 'VZ', name: 'Verizon Communications', sector: 'Telecom', marketCap: 170, avgVolume: ********, volatility: 'Low', swingTradingRating: 6 },
  { symbol: 'T', name: 'AT&T Inc.', sector: 'Telecom', marketCap: 120, avgVolume: ********, volatility: 'Medium', swingTradingRating: 7 },

  // High-Volatility Growth Stocks
  { symbol: 'ROKU', name: 'Roku Inc.', sector: 'Technology', marketCap: 5, avgVolume: 8000000, volatility: 'High', swingTradingRating: 9 },
  { symbol: 'SHOP', name: 'Shopify Inc.', sector: 'Technology', marketCap: 80, avgVolume: 3000000, volatility: 'High', swingTradingRating: 9 },
  { symbol: 'SQ', name: 'Block Inc.', sector: 'Financial', marketCap: 40, avgVolume: 8000000, volatility: 'High', swingTradingRating: 9 },
  { symbol: 'UBER', name: 'Uber Technologies', sector: 'Technology', marketCap: 150, avgVolume: ********, volatility: 'High', swingTradingRating: 9 },
  { symbol: 'LYFT', name: 'Lyft Inc.', sector: 'Technology', marketCap: 6, avgVolume: 4000000, volatility: 'High', swingTradingRating: 8 },
  { symbol: 'DASH', name: 'DoorDash Inc.', sector: 'Technology', marketCap: 50, avgVolume: 3000000, volatility: 'High', swingTradingRating: 8 },
  { symbol: 'COIN', name: 'Coinbase Global Inc.', sector: 'Financial', marketCap: 50, avgVolume: 8000000, volatility: 'High', swingTradingRating: 9 },
  { symbol: 'HOOD', name: 'Robinhood Markets Inc.', sector: 'Financial', marketCap: 15, avgVolume: 10000000, volatility: 'High', swingTradingRating: 9 },

  // REITs & Utilities (Lower volatility but good for certain strategies)
  { symbol: 'SPG', name: 'Simon Property Group', sector: 'REIT', marketCap: 50, avgVolume: 2000000, volatility: 'Medium', swingTradingRating: 6 },
  { symbol: 'PLD', name: 'Prologis Inc.', sector: 'REIT', marketCap: 120, avgVolume: 2500000, volatility: 'Medium', swingTradingRating: 6 },
  { symbol: 'NEE', name: 'NextEra Energy Inc.', sector: 'Utilities', marketCap: 150, avgVolume: 8000000, volatility: 'Low', swingTradingRating: 6 },
]

// Helper functions
export const getStocksByVolatility = (volatility: 'High' | 'Medium' | 'Low'): StockInfo[] => {
  return SWING_TRADING_UNIVERSE.filter(stock => stock.volatility === volatility)
}

export const getStocksBySector = (sector: string): StockInfo[] => {
  return SWING_TRADING_UNIVERSE.filter(stock => stock.sector === sector)
}

export const getTopSwingTradingStocks = (limit: number = 50): StockInfo[] => {
  return SWING_TRADING_UNIVERSE
    .sort((a, b) => b.swingTradingRating - a.swingTradingRating)
    .slice(0, limit)
}

export const getHighVolumeStocks = (minVolume: number = 10000000): StockInfo[] => {
  return SWING_TRADING_UNIVERSE.filter(stock => stock.avgVolume >= minVolume)
}

// Default stock symbols for quick access
export const DEFAULT_SWING_SYMBOLS = SWING_TRADING_UNIVERSE.map(stock => stock.symbol)

// High-priority stocks for scanning (top swing trading candidates)
export const PRIORITY_SWING_SYMBOLS = getTopSwingTradingStocks(30).map(stock => stock.symbol)

// Sector-based symbol lists
export const TECH_SYMBOLS = getStocksBySector('Technology').map(stock => stock.symbol)
export const FINANCIAL_SYMBOLS = getStocksBySector('Financial').map(stock => stock.symbol)
export const HEALTHCARE_SYMBOLS = getStocksBySector('Healthcare').map(stock => stock.symbol)
export const ENERGY_SYMBOLS = getStocksBySector('Energy').map(stock => stock.symbol)
