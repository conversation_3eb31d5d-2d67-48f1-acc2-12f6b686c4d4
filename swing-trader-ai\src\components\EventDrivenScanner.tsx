'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { 
  TrendingUp, 
  Zap, 
  Target, 
  AlertTriangle, 
  Clock, 
  DollarSign,
  Activity,
  Loader2,
  RefreshCw,
  Filter,
  Star
} from 'lucide-react'
import { PerfectPickSetup, PreMarketGapScan, Catalyst } from '@/types/trading'
import { PerfectPickSetupCard } from './PerfectPickSetupCard'
import { alertSystem } from '@/lib/alertSystem'
import { formatCurrency, formatPercentage } from '@/lib/utils'

interface EventDrivenScannerProps {
  accountSize?: number
  riskPercent?: number
}

export function EventDrivenScanner({ 
  accountSize = 100000, 
  riskPercent = 2 
}: EventDrivenScannerProps) {
  const [perfectPickSetups, setPerfectPickSetups] = useState<PerfectPickSetup[]>([])
  const [gapScanResults, setGapScanResults] = useState<PreMarketGapScan[]>([])
  const [catalysts, setCatalysts] = useState<Catalyst[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('perfect-pick')
  const [lastScanTime, setLastScanTime] = useState<string>('')
  const [scanSummary, setScanSummary] = useState<any>(null)

  // Auto-refresh every 15 minutes during market hours
  useEffect(() => {
    const interval = setInterval(() => {
      const now = new Date()
      const hour = now.getHours()
      // Refresh during pre-market (4-9:30 AM EST) and market hours (9:30 AM - 4 PM EST)
      if ((hour >= 4 && hour < 21)) {
        handleRefreshScan()
      }
    }, 15 * 60 * 1000) // 15 minutes

    return () => clearInterval(interval)
  }, [])

  const handlePerfectPickScan = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/scanner/perfect-pick?accountSize=${accountSize}&riskPercent=${riskPercent}&limit=20`)
      const data = await response.json()
      
      if (data.success) {
        setPerfectPickSetups(data.data.setups)
        setScanSummary(data.data.summary)
        setLastScanTime(new Date().toLocaleTimeString())

        // Generate alerts for new Perfect-Pick setups
        alertSystem.monitorPerfectPickSetups(data.data.setups)
      }
    } catch (error) {
      console.error('Error running Perfect-Pick scan:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleGapScan = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/scanner/gap-scan?minGap=3&maxGap=15&limit=30')
      const data = await response.json()
      
      if (data.success) {
        setGapScanResults(data.data.results)
        setLastScanTime(new Date().toLocaleTimeString())

        // Generate alerts for significant gaps
        alertSystem.monitorGapScans(data.data.results)
      }
    } catch (error) {
      console.error('Error running gap scan:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCatalystScan = async () => {
    setIsLoading(true)
    try {
      // Get catalysts for top gap stocks
      const symbols = gapScanResults.slice(0, 10).map(result => result.symbol).join(',')
      const response = await fetch(`/api/catalyst/detect?symbols=${symbols}&minQuality=6&limit=50`)
      const data = await response.json()
      
      if (data.success) {
        setCatalysts(data.data.catalysts)
        setLastScanTime(new Date().toLocaleTimeString())

        // Generate alerts for high-quality catalysts
        data.data.catalysts
          .filter((catalyst: Catalyst) => catalyst.qualityScore >= 7)
          .forEach((catalyst: Catalyst) => alertSystem.createCatalystAlert(catalyst))
      }
    } catch (error) {
      console.error('Error running catalyst scan:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleRefreshScan = () => {
    switch (activeTab) {
      case 'perfect-pick':
        handlePerfectPickScan()
        break
      case 'gap-scan':
        handleGapScan()
        break
      case 'catalysts':
        handleCatalystScan()
        break
    }
  }

  const getCatalystBadgeColor = (catalyst: Catalyst) => {
    if (catalyst.tier === 'tier_1') return 'bg-green-500'
    if (catalyst.tier === 'tier_2') return 'bg-yellow-500'
    return 'bg-gray-500'
  }

  const getCatalystImpactIcon = (impact: string) => {
    switch (impact) {
      case 'bullish': return <TrendingUp className="h-4 w-4 text-green-500" />
      case 'bearish': return <AlertTriangle className="h-4 w-4 text-red-500" />
      default: return <Activity className="h-4 w-4 text-gray-500" />
    }
  }

  const getSetupGradeColor = (grade: string) => {
    if (grade.startsWith('A')) return 'text-green-600 bg-green-50'
    if (grade.startsWith('B')) return 'text-blue-600 bg-blue-50'
    if (grade.startsWith('C')) return 'text-yellow-600 bg-yellow-50'
    return 'text-red-600 bg-red-50'
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Event-Driven Scanner</h2>
          <p className="text-muted-foreground">
            Perfect-Pick Trading System with Catalyst Detection
          </p>
        </div>
        <div className="flex items-center gap-2">
          {lastScanTime && (
            <span className="text-sm text-muted-foreground">
              Last scan: {lastScanTime}
            </span>
          )}
          <Button 
            onClick={handleRefreshScan} 
            disabled={isLoading}
            variant="outline"
            size="sm"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            Refresh
          </Button>
        </div>
      </div>

      {/* Scan Summary */}
      {scanSummary && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Scan Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{scanSummary.totalSetups || 0}</div>
                <div className="text-sm text-muted-foreground">Total Setups</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{scanSummary.avgScore || 0}</div>
                <div className="text-sm text-muted-foreground">Avg Score</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{formatPercentage(scanSummary.avgGap || 0)}</div>
                <div className="text-sm text-muted-foreground">Avg Gap</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {Object.keys(scanSummary.catalystBreakdown || {}).length}
                </div>
                <div className="text-sm text-muted-foreground">Catalyst Types</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Scanner Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="perfect-pick" className="flex items-center gap-2">
            <Star className="h-4 w-4" />
            Perfect-Pick
          </TabsTrigger>
          <TabsTrigger value="gap-scan" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Gap Scanner
          </TabsTrigger>
          <TabsTrigger value="catalysts" className="flex items-center gap-2">
            <Zap className="h-4 w-4" />
            Catalysts
          </TabsTrigger>
        </TabsList>

        {/* Perfect-Pick Tab */}
        <TabsContent value="perfect-pick" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Perfect-Pick Setups</h3>
            <Button onClick={handlePerfectPickScan} disabled={isLoading}>
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Target className="h-4 w-4 mr-2" />
              )}
              Scan Perfect-Picks
            </Button>
          </div>

          <div className="space-y-6">
            {perfectPickSetups.map((setup) => (
              <PerfectPickSetupCard
                key={setup.symbol}
                setup={setup}
                onExecuteTrade={(setup) => {
                  console.log('Execute trade for:', setup.symbol)
                  // TODO: Implement trade execution
                }}
                onViewChart={(symbol) => {
                  console.log('View chart for:', symbol)
                  // TODO: Implement chart viewing
                }}
                onGenerateEntryTrigger={async (symbol, preMarketHigh) => {
                  console.log('Generate entry trigger for:', symbol)
                  try {
                    const response = await fetch('/api/scanner/perfect-pick', {
                      method: 'POST',
                      headers: { 'Content-Type': 'application/json' },
                      body: JSON.stringify({
                        action: 'generate_entry_trigger',
                        data: { symbol, preMarketHigh }
                      })
                    })
                    const data = await response.json()
                    if (data.success) {
                      // Update the setup with the new entry trigger
                      console.log('Entry trigger generated:', data.data.entryTrigger)
                    }
                  } catch (error) {
                    console.error('Error generating entry trigger:', error)
                  }
                }}
              />
            ))}
          </div>

          {perfectPickSetups.length === 0 && !isLoading && (
            <Card>
              <CardContent className="text-center py-8">
                <Target className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground">
                  No Perfect-Pick setups found. Click "Scan Perfect-Picks" to search for opportunities.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Gap Scanner Tab */}
        <TabsContent value="gap-scan" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Pre-Market Gap Scanner</h3>
            <Button onClick={handleGapScan} disabled={isLoading}>
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <TrendingUp className="h-4 w-4 mr-2" />
              )}
              Scan Gaps
            </Button>
          </div>

          <div className="grid gap-3">
            {gapScanResults.map((result) => (
              <Card key={result.symbol} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div>
                        <div className="font-semibold">{result.symbol}</div>
                        <div className="text-sm text-muted-foreground">{result.name}</div>
                      </div>
                      {result.catalyst && (
                        <Badge className={getCatalystBadgeColor(result.catalyst)}>
                          {result.catalyst.type.replace(/_/g, ' ')}
                        </Badge>
                      )}
                      {result.meetsAllCriteria && (
                        <Badge className="bg-green-500">Perfect-Pick</Badge>
                      )}
                    </div>
                    <div className="text-right">
                      <div className="font-bold">{formatCurrency(result.price)}</div>
                      <div className="text-sm text-green-600">
                        +{formatPercentage(result.gapPercent)}
                      </div>
                    </div>
                  </div>
                  
                  <div className="mt-3 grid grid-cols-3 gap-4 text-xs text-muted-foreground">
                    <div>Vol: {(result.preMarketVolume / 1000).toFixed(0)}K</div>
                    <div>MCap: ${(result.marketCap / 1e9).toFixed(1)}B</div>
                    <div>Sector: {result.sector}</div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {gapScanResults.length === 0 && !isLoading && (
            <Card>
              <CardContent className="text-center py-8">
                <TrendingUp className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground">
                  No gap opportunities found. Click "Scan Gaps" to search for pre-market movers.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Catalysts Tab */}
        <TabsContent value="catalysts" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Real-Time Catalysts</h3>
            <Button onClick={handleCatalystScan} disabled={isLoading}>
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Zap className="h-4 w-4 mr-2" />
              )}
              Scan Catalysts
            </Button>
          </div>

          <div className="grid gap-3">
            {catalysts.map((catalyst) => (
              <Card key={catalyst.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="font-semibold">{catalyst.symbol}</span>
                        {getCatalystImpactIcon(catalyst.impact)}
                        <Badge className={getCatalystBadgeColor(catalyst)}>
                          {catalyst.tier.replace('_', ' ').toUpperCase()}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {catalyst.freshness}
                        </Badge>
                      </div>
                      <h4 className="font-medium mb-1">{catalyst.title}</h4>
                      <p className="text-sm text-muted-foreground mb-2">
                        {catalyst.description}
                      </p>
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <span>Quality: {catalyst.qualityScore}/10</span>
                        <span>Source: {catalyst.source}</span>
                        <span className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {new Date(catalyst.announcementTime).toLocaleString()}
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {catalysts.length === 0 && !isLoading && (
            <Card>
              <CardContent className="text-center py-8">
                <Zap className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground">
                  No catalysts found. Click "Scan Catalysts" to detect market-moving events.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
