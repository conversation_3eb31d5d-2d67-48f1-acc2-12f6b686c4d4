{"version": 3, "sources": ["turbopack:///[project]/swing-trader-ai/src/lib/swingStrategies.ts"], "sourcesContent": ["import { CandlestickData, StockData, SwingTradingAnalysis } from '@/types/trading'\nimport { TechnicalIndicators } from './indicators'\n\nexport interface StrategySetup {\n  strategy: 'overnight_momentum' | 'technical_breakout'\n  confidence: number\n  entryPrice: number\n  stopLoss: number\n  targets: number[]\n  positionSize: number\n  riskAmount: number\n  holdingPeriod: 'overnight' | 'days_to_weeks'\n  keyLevel: number\n  invalidation: string\n  notes: string[]\n  // Precise trading execution details\n  preciseEntry: {\n    price: number\n    orderType: 'market' | 'limit'\n    timing: string\n    conditions: string[]\n    urgency: 'immediate' | 'wait_for_pullback' | 'breakout_confirmation'\n  }\n  preciseExit: {\n    stopLoss: {\n      price: number\n      orderType: 'stop' | 'stop_limit'\n      reason: string\n      triggerConditions: string[]\n    }\n    takeProfits: Array<{\n      price: number\n      percentage: number // % of position to sell\n      target: string // \"R1\", \"R2\", \"Extension\", etc.\n      reasoning: string\n      orderType: 'limit' | 'market'\n    }>\n  }\n  riskManagement: {\n    maxRiskDollars: number\n    accountRiskPercent: number\n    sharesForRisk: number\n    invalidationPrice: number\n    timeStopHours: number\n    maxDrawdownPercent: number\n  }\n  executionPlan: {\n    entryInstructions: string[]\n    exitInstructions: string[]\n    monitoringPoints: string[]\n    contingencyPlans: string[]\n  }\n}\n\nexport interface SwingSetupCriteria {\n  // Basic filters\n  minPrice: number\n  minVolume: number\n  minMarketCap: number\n  minATRPercent: number\n  \n  // Technical requirements\n  above200SMA: boolean\n  maxDistanceFrom8EMA: number // in ATR units\n  minRoomToResistance: number // in ATR units\n  \n  // Timing\n  scanTimeStart: string // \"12:00\"\n  scanTimeEnd: string   // \"16:00\"\n  \n  // Risk management\n  maxRiskPerTrade: number // percentage of account\n  maxConcurrentPositions: number\n}\n\nexport class SwingTradingStrategies {\n  private static readonly DEFAULT_CRITERIA: SwingSetupCriteria = {\n    minPrice: 5.0,\n    minVolume: 500000,\n    minMarketCap: *********, // $800M\n    minATRPercent: 2.0,\n    above200SMA: true,\n    maxDistanceFrom8EMA: 2.0, // 2x ATR\n    minRoomToResistance: 1.0, // 1 ATR minimum\n    scanTimeStart: \"12:00\",\n    scanTimeEnd: \"16:00\",\n    maxRiskPerTrade: 1.0, // 1% max risk\n    maxConcurrentPositions: 3\n  }\n\n  // Strategy #1: Overnight Momentum Continuation\n  static analyzeOvernightMomentum(\n    symbol: string,\n    candles: CandlestickData[],\n    quote: StockData,\n    accountSize: number = 100000\n  ): StrategySetup | null {\n    if (candles.length < 50) return null\n\n    const closes = candles.map(c => c.close)\n    const highs = candles.map(c => c.high)\n    const lows = candles.map(c => c.low)\n    const volumes = candles.map(c => c.volume)\n    \n    const currentPrice = quote.price\n    const currentVolume = quote.volume\n    const changePercent = quote.changePercent\n\n    // Calculate technical indicators (adjusted for shorter history)\n    const sma50 = TechnicalIndicators.sma(closes, Math.min(50, closes.length - 1)) // Use 50-day instead of 200-day\n    const ema8 = TechnicalIndicators.ema(closes, Math.min(8, closes.length - 1))\n    const atr = this.calculateATR(candles, Math.min(14, candles.length - 1))\n\n    const current50SMA = sma50[sma50.length - 1]\n    const current8EMA = ema8[ema8.length - 1]\n    const currentATR = atr[atr.length - 1]\n\n    // Basic qualification filters (using 50-day SMA instead of 200-day)\n    if (!this.passesBasicFilters(quote, currentVolume, current50SMA, currentPrice)) {\n      return null\n    }\n\n    // Check if it's a top intraday gainer (top decile movers)\n    if (changePercent < 2.0) return null // Minimum 2% gain for momentum\n\n    // Check distance from 8-EMA (not wildly extended)\n    const distanceFrom8EMA = Math.abs(currentPrice - current8EMA) / currentATR\n    if (distanceFrom8EMA > this.DEFAULT_CRITERIA.maxDistanceFrom8EMA) return null\n\n    // Look for defended intraday level (simplified - using VWAP proxy)\n    const vwap = this.calculateVWAP(candles.slice(-1)[0])\n    const keyLevel = Math.max(vwap, currentPrice * 0.98) // Approximate defended level\n\n    // Check if holding gains (>50% of day's range)\n    const todayHigh = highs[highs.length - 1]\n    const todayLow = lows[lows.length - 1]\n    const dayRange = todayHigh - todayLow\n    const currentFromLow = currentPrice - todayLow\n    const holdingGainsPercent = currentFromLow / dayRange\n    \n    if (holdingGainsPercent < 0.5) return null // Must hold >50% of range\n\n    // Calculate room to next resistance\n    const roomToResistance = this.calculateRoomToResistance(candles, currentPrice, currentATR)\n    if (roomToResistance < this.DEFAULT_CRITERIA.minRoomToResistance) return null\n\n    // Position sizing (risk 0.5-1% of account)\n    const riskPercent = 0.75 // 0.75% risk for overnight holds\n    const stopDistance = currentPrice - keyLevel\n    const riskAmount = accountSize * (riskPercent / 100)\n    const positionSize = Math.floor(riskAmount / stopDistance)\n\n    // Targets: Pre-market scale at 3-5%, opening hour at 5-8%\n    const targets = [\n      currentPrice * 1.03, // 3% pre-market target\n      currentPrice * 1.05, // 5% opening hour target\n      currentPrice * 1.08  // 8% extended target\n    ]\n\n    const confidence = this.calculateOvernightConfidence(\n      changePercent, holdingGainsPercent, currentVolume, roomToResistance\n    )\n\n    return {\n      strategy: 'overnight_momentum',\n      confidence,\n      entryPrice: currentPrice,\n      stopLoss: keyLevel,\n      targets,\n      positionSize,\n      riskAmount,\n      holdingPeriod: 'overnight',\n      keyLevel,\n      invalidation: `Daily close below ${keyLevel.toFixed(2)} or gap down below level`,\n      notes: [\n        'Enter final 30-60 min before close',\n        'Exit pre-market on strength or first 45min',\n        'Hard stop if gaps below defended level',\n        'Scale out aggressively if gaps >1 ATR up'\n      ],\n      // Precise entry execution\n      preciseEntry: {\n        price: currentPrice * 0.999, // Slightly below current for better fill\n        orderType: 'limit',\n        timing: 'Final 30-60 minutes before market close',\n        conditions: [\n          `Stock holding above ${keyLevel.toFixed(2)} (defended level)`,\n          `Volume above ${(currentVolume * 0.8).toLocaleString()} shares`,\n          `Price above ${current8EMA.toFixed(2)} (8-EMA)`,\n          'No late-day selling pressure'\n        ],\n        urgency: 'wait_for_pullback'\n      },\n      // Precise exit execution\n      preciseExit: {\n        stopLoss: {\n          price: keyLevel * 0.995, // Slightly below key level\n          orderType: 'stop',\n          reason: 'Defended level broken - invalidates setup',\n          triggerConditions: [\n            'Any close below defended level',\n            'Gap down below key level',\n            'Heavy selling into close'\n          ]\n        },\n        takeProfits: [\n          {\n            price: targets[0],\n            percentage: 33,\n            target: 'T1 - Pre-market (3%)',\n            reasoning: 'Take profits on pre-market strength',\n            orderType: 'limit'\n          },\n          {\n            price: targets[1],\n            percentage: 33,\n            target: 'T2 - Opening hour (5%)',\n            reasoning: 'Scale out on opening momentum',\n            orderType: 'limit'\n          },\n          {\n            price: targets[2],\n            percentage: 34,\n            target: 'T3 - Extended (8%)',\n            reasoning: 'Final exit on extended move',\n            orderType: 'limit'\n          }\n        ]\n      },\n      // Risk management details\n      riskManagement: {\n        maxRiskDollars: riskAmount,\n        accountRiskPercent: riskPercent,\n        sharesForRisk: positionSize,\n        invalidationPrice: keyLevel,\n        timeStopHours: 18, // Exit by next day close if no movement\n        maxDrawdownPercent: 2.0\n      },\n      // Execution plan\n      executionPlan: {\n        entryInstructions: [\n          '1. Wait for final 30-60 minutes before close',\n          '2. Confirm stock is holding defended level',\n          '3. Place limit order slightly below current price',\n          '4. Cancel if not filled by close'\n        ],\n        exitInstructions: [\n          '1. Set stop-loss immediately after fill',\n          '2. Monitor pre-market for gap up',\n          '3. Scale out 1/3 at each target level',\n          '4. Exit all by 10:15 AM if no momentum'\n        ],\n        monitoringPoints: [\n          'Pre-market price action and volume',\n          'Opening gap and first 15-minute candle',\n          'Key level defense throughout session',\n          'Overall market sentiment'\n        ],\n        contingencyPlans: [\n          'If gaps down: Exit immediately at market open',\n          'If gaps up >2%: Scale out more aggressively',\n          'If sideways: Exit by 10:15 AM',\n          'If market weakness: Tighten stops'\n        ]\n      }\n    }\n  }\n\n  // Strategy #2: Technical Breakout Trend-Follow (8-EMA + 200-SMA)\n  static analyzeTechnicalBreakout(\n    symbol: string,\n    candles: CandlestickData[],\n    quote: StockData,\n    accountSize: number = 100000\n  ): StrategySetup | null {\n    if (candles.length < 50) return null\n\n    const closes = candles.map(c => c.close)\n    const volumes = candles.map(c => c.volume)\n    const currentPrice = quote.price\n\n    // Calculate technical indicators (adjusted for shorter history)\n    const sma50 = TechnicalIndicators.sma(closes, Math.min(50, closes.length - 1))\n    const ema8 = TechnicalIndicators.ema(closes, Math.min(8, closes.length - 1))\n    const atr = this.calculateATR(candles, Math.min(14, candles.length - 1))\n\n    const current50SMA = sma50[sma50.length - 1]\n    const current8EMA = ema8[ema8.length - 1]\n    const currentATR = atr[atr.length - 1]\n\n    // Basic qualification filters (using 50-day SMA)\n    if (!this.passesBasicFilters(quote, quote.volume, current50SMA, currentPrice)) {\n      return null\n    }\n\n    // Check if in clear uptrend above 50-SMA (adjusted from 200-SMA)\n    if (currentPrice <= current50SMA) return null\n\n    // Check 8-EMA behavior - should be \"hugging\" the 8-EMA\n    const distanceFrom8EMA = Math.abs(currentPrice - current8EMA)\n    const emaDistancePercent = (distanceFrom8EMA / currentPrice) * 100\n    \n    // Should be close to 8-EMA (within 2-3% for quality trend-follow)\n    if (emaDistancePercent > 3.0) return null\n\n    // Check for recent breakout or EMA reclaim\n    const recentEMAReclaim = this.checkEMAReclaim(closes, ema8, 5) // Last 5 days\n    if (!recentEMAReclaim) return null\n\n    // Volume expansion check\n    const avgVolume = TechnicalIndicators.sma(volumes, 20)\n    const currentAvgVolume = avgVolume[avgVolume.length - 1]\n    const volumeExpansion = quote.volume / currentAvgVolume\n    \n    if (volumeExpansion < 1.2) return null // Need some volume expansion\n\n    // Calculate room to next resistance\n    const roomToResistance = this.calculateRoomToResistance(candles, currentPrice, currentATR)\n    if (roomToResistance < 1.5) return null // Need more room for trend-follow\n\n    // Position sizing (risk 1% of account)\n    const riskPercent = 1.0\n    const stopDistance = currentPrice - current8EMA // Stop at 8-EMA break\n    const riskAmount = accountSize * (riskPercent / 100)\n    const positionSize = Math.floor(riskAmount / stopDistance)\n\n    // Targets: Scale at resistance levels\n    const targets = [\n      currentPrice * 1.05, // 5% first target\n      currentPrice * 1.10, // 10% second target\n      currentPrice * 1.15  // 15% extended target\n    ]\n\n    const confidence = this.calculateBreakoutConfidence(\n      emaDistancePercent, volumeExpansion, roomToResistance, quote.changePercent\n    )\n\n    return {\n      strategy: 'technical_breakout',\n      confidence,\n      entryPrice: currentPrice,\n      stopLoss: current8EMA,\n      targets,\n      positionSize,\n      riskAmount,\n      holdingPeriod: 'days_to_weeks',\n      keyLevel: current8EMA,\n      invalidation: `Daily close below 8-EMA (${current8EMA.toFixed(2)})`,\n      notes: [\n        'Enter on afternoon reclaim of 8-EMA',\n        'Add only on higher-low pullbacks to 8-EMA',\n        'Scale partials at resistance levels',\n        'Exit on daily close below 8-EMA'\n      ],\n      // Precise entry execution\n      preciseEntry: {\n        price: current8EMA * 1.002, // Slightly above 8-EMA for confirmation\n        orderType: 'limit',\n        timing: 'Afternoon reclaim or first pullback to 8-EMA',\n        conditions: [\n          `Price reclaiming ${current8EMA.toFixed(2)} (8-EMA) with volume`,\n          `Above ${current50SMA.toFixed(2)} (50-day SMA)`,\n          `Volume expansion above ${(quote.volume * 1.2).toLocaleString()}`,\n          'No major resistance overhead'\n        ],\n        urgency: 'breakout_confirmation'\n      },\n      // Precise exit execution\n      preciseExit: {\n        stopLoss: {\n          price: current8EMA * 0.998, // Slightly below 8-EMA\n          orderType: 'stop',\n          reason: '8-EMA breakdown invalidates trend-follow setup',\n          triggerConditions: [\n            'Daily close below 8-EMA',\n            'Intraday break with volume',\n            'Loss of 50-SMA support'\n          ]\n        },\n        takeProfits: [\n          {\n            price: targets[0],\n            percentage: 25,\n            target: 'R1 - First resistance (5%)',\n            reasoning: 'Take partial profits at first resistance',\n            orderType: 'limit'\n          },\n          {\n            price: targets[1],\n            percentage: 35,\n            target: 'R2 - Major resistance (10%)',\n            reasoning: 'Scale out at major resistance level',\n            orderType: 'limit'\n          },\n          {\n            price: targets[2],\n            percentage: 40,\n            target: 'R3 - Extension (15%)',\n            reasoning: 'Final exit on extended breakout',\n            orderType: 'limit'\n          }\n        ]\n      },\n      // Risk management details\n      riskManagement: {\n        maxRiskDollars: riskAmount,\n        accountRiskPercent: riskPercent,\n        sharesForRisk: positionSize,\n        invalidationPrice: current8EMA,\n        timeStopHours: 72, // 3 trading days max hold if no progress\n        maxDrawdownPercent: 3.0\n      },\n      // Execution plan\n      executionPlan: {\n        entryInstructions: [\n          '1. Wait for afternoon reclaim of 8-EMA',\n          '2. Confirm volume expansion on breakout',\n          '3. Place limit order above 8-EMA',\n          '4. Only enter on higher-low pullbacks'\n        ],\n        exitInstructions: [\n          '1. Set stop-loss below 8-EMA immediately',\n          '2. Scale out 25% at first resistance',\n          '3. Trail stop to breakeven after R1',\n          '4. Exit remaining on 8-EMA breakdown'\n        ],\n        monitoringPoints: [\n          '8-EMA as dynamic support/resistance',\n          'Volume confirmation on moves',\n          'Overall market trend alignment',\n          'Sector strength/weakness'\n        ],\n        contingencyPlans: [\n          'If fails at resistance: Tighten stops',\n          'If market weakness: Exit early',\n          'If sector rotation: Consider exit',\n          'If extended: Take more profits'\n        ]\n      }\n    }\n  }\n\n  // Helper methods\n  private static passesBasicFilters(\n    quote: StockData,\n    volume: number,\n    sma50: number,\n    price: number\n  ): boolean {\n    return (\n      price >= this.DEFAULT_CRITERIA.minPrice &&\n      volume >= this.DEFAULT_CRITERIA.minVolume &&\n      (quote.marketCap || 0) >= this.DEFAULT_CRITERIA.minMarketCap &&\n      price > sma50 // Using 50-day SMA instead of 200-day for shorter history\n    )\n  }\n\n  private static calculateATR(candles: CandlestickData[], period: number): number[] {\n    const trueRanges: number[] = []\n    \n    for (let i = 1; i < candles.length; i++) {\n      const high = candles[i].high\n      const low = candles[i].low\n      const prevClose = candles[i - 1].close\n      \n      const tr = Math.max(\n        high - low,\n        Math.abs(high - prevClose),\n        Math.abs(low - prevClose)\n      )\n      \n      trueRanges.push(tr)\n    }\n    \n    return TechnicalIndicators.sma(trueRanges, period)\n  }\n\n  private static calculateVWAP(candle: CandlestickData): number {\n    // Simplified VWAP calculation using typical price\n    return (candle.high + candle.low + candle.close) / 3\n  }\n\n  private static calculateRoomToResistance(\n    candles: CandlestickData[], \n    currentPrice: number, \n    atr: number\n  ): number {\n    // Find recent highs as resistance levels\n    const recentHighs = candles.slice(-20).map(c => c.high)\n    const maxHigh = Math.max(...recentHighs)\n    const roomToHigh = maxHigh - currentPrice\n    return roomToHigh / atr\n  }\n\n  private static checkEMAReclaim(closes: number[], ema8: number[], lookback: number): boolean {\n    // Check if price recently reclaimed 8-EMA\n    for (let i = Math.max(0, closes.length - lookback); i < closes.length - 1; i++) {\n      if (closes[i] < ema8[i] && closes[i + 1] > ema8[i + 1]) {\n        return true // Found a reclaim\n      }\n    }\n    return false\n  }\n\n  private static calculateOvernightConfidence(\n    changePercent: number,\n    holdingGains: number,\n    volume: number,\n    roomToResistance: number\n  ): number {\n    let confidence = 50\n\n    // Change percent bonus\n    if (changePercent > 5) confidence += 15\n    else if (changePercent > 3) confidence += 10\n    else if (changePercent > 2) confidence += 5\n\n    // Holding gains bonus\n    if (holdingGains > 0.8) confidence += 15\n    else if (holdingGains > 0.6) confidence += 10\n    else if (holdingGains > 0.5) confidence += 5\n\n    // Volume bonus\n    if (volume > 2000000) confidence += 10\n    else if (volume > 1000000) confidence += 5\n\n    // Room to resistance\n    if (roomToResistance > 2) confidence += 10\n    else if (roomToResistance > 1.5) confidence += 5\n\n    return Math.min(95, Math.max(30, confidence))\n  }\n\n  private static calculateBreakoutConfidence(\n    emaDistance: number,\n    volumeExpansion: number,\n    roomToResistance: number,\n    changePercent: number\n  ): number {\n    let confidence = 60\n\n    // EMA proximity bonus (closer is better for trend-follow)\n    if (emaDistance < 1) confidence += 15\n    else if (emaDistance < 2) confidence += 10\n    else if (emaDistance < 3) confidence += 5\n\n    // Volume expansion bonus\n    if (volumeExpansion > 2) confidence += 15\n    else if (volumeExpansion > 1.5) confidence += 10\n    else if (volumeExpansion > 1.2) confidence += 5\n\n    // Room to resistance\n    if (roomToResistance > 3) confidence += 15\n    else if (roomToResistance > 2) confidence += 10\n    else if (roomToResistance > 1.5) confidence += 5\n\n    // Positive momentum\n    if (changePercent > 2) confidence += 5\n\n    return Math.min(95, Math.max(40, confidence))\n  }\n}\n"], "names": [], "mappings": "6EACA,IAAA,EAAA,EAAA,CAAA,CAAA,MA0EO,OAAM,EACX,OAAwB,iBAAuC,CAC7D,SAAU,EACV,UAAW,IACX,aAAc,IACd,cAAe,EACf,aAAa,EACb,oBAAqB,EACrB,oBAAqB,EACrB,cAAe,QACf,YAAa,QACb,gBAAiB,EACjB,uBAAwB,CAC1B,CAAC,AAGD,QAAO,yBACL,CAAc,CACd,CAA0B,CAC1B,CAAgB,CAChB,EAAsB,GAAM,CACN,CACtB,GAAI,EAAQ,MAAM,CAAG,GAAI,OAAO,KAEhC,IAAM,EAAS,EAAQ,GAAG,CAAC,GAAK,EAAE,KAAK,EACjC,EAAQ,EAAQ,GAAG,CAAC,GAAK,EAAE,IAAI,EAC/B,EAAO,EAAQ,GAAG,CAAC,GAAK,EAAE,GAAG,EACnB,EAAQ,GAAG,CAAC,GAAK,EAAE,MAAM,EAEzC,IAAM,EAAe,EAAM,KAAK,CAC1B,EAAgB,EAAM,MAAM,CAC5B,EAAgB,EAAM,aAAa,CAGnC,EAAQ,EAAA,mBAAmB,CAAC,GAAG,CAAC,EAAQ,KAAK,GAAG,CAAC,GAAI,EAAO,MAAM,CAAG,IAAI,AACzE,EAAO,EAAA,mBAAmB,CAAC,GAAG,CAAC,EAAQ,EADkE,GAC7D,GAAG,CAAC,EAAG,EAAO,MAAM,CAAG,IACnE,EAAM,IAAI,CAAC,YAAY,CAAC,EAAS,KAAK,GAAG,CAAC,GAAI,EAAQ,MAAM,CAAG,IAE/D,EAAe,CAAK,CAAC,EAAM,MAAM,CAAG,EAAE,CACtC,EAAc,CAAI,CAAC,EAAK,MAAM,CAAG,EAAE,CACnC,EAAa,CAAG,CAAC,EAAI,MAAM,CAAG,EAAE,CAGtC,GAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAO,EAAe,EAAc,IAK7D,EAAgB,GAGK,AACrB,EAJqB,GAGK,CARkD,EAQ/C,CAHD,AAGE,EAAe,GAHZ,AAG2B,EACzC,IAAI,CAAC,gBAAgB,CAAC,OAJuB,YAIJ,CAR9D,CAQgE,MARzD,CAQgE,IAIzE,IAAM,EAAW,KAAK,GAAG,CAAC,AADb,IAAI,CAAC,aAAa,CAAC,EAAQ,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,EACL,IAAf,EAAqB,CAG/C,EAAY,CAAK,CAAC,EAAM,MAAM,CAAG,EAAE,CACnC,EAAW,CAAI,CAAC,EAAK,MAAM,AAJiD,CAI9C,EAAE,CAGhC,EAAsB,CADL,EAAe,CAAA,GADrB,EAAY,CAAA,EAI7B,GAAI,EAFyC,AAEnB,GAAK,OAAO,KAAK,AAG3C,IAAM,EAAmB,IAAI,CAAC,eAHuC,UAGd,CAAC,EAAS,EAAc,GAC/E,GAAI,EAAmB,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAE,OAAO,KAKzE,IAAM,EAA4B,MAAf,EACb,EAAe,IAD2B,CACtB,EADyB,GAAlB,AACF,CAAC,GAFX,EAAe,CAAA,GAK9B,EAAU,CACC,CAJ4B,IAI3C,EACe,KAAf,EACe,KAAf,AAAqB,EACtB,CAMD,MAAO,CACL,SAAU,EARgC,mBAS1C,WANiB,IAAI,CAAC,4BAA4B,CAClD,EAAe,EAAqB,EAAe,GAMnD,WAAY,EACZ,SAAU,UACV,eACA,aACA,EACA,cAAe,qBACf,EACA,aAAc,CAAC,kBAAkB,EAAE,EAAS,OAAO,CAAC,GAAG,wBAAwB,CAAC,CAChF,MAAO,CACL,qCACA,6CACA,yCACA,2CACD,CAED,aAAc,CACZ,MAAsB,KAAf,EACP,UAAW,QACX,OAAQ,0CACR,WAAY,CACV,CAAC,oBAAoB,EAAE,EAAS,OAAO,CAAC,GAAG,iBAAiB,CAAC,CAC7D,CAAC,aAAa,EAAE,CAAiB,GAAhB,CAAgB,CAAG,CAAE,cAAc,GAAG,OAAO,CAAC,CAC/D,CAAC,YAAY,EAAE,EAAY,OAAO,CAAC,GAAG,QAAQ,CAAC,CAC/C,+BACD,CACD,QAAS,mBACX,EAEA,YAAa,CACX,SAAU,CACR,MAAkB,KAAX,EACP,UAAW,OACX,OAAQ,4CACR,kBAAmB,CACjB,iCACA,2BACA,2BAEJ,AADG,EAEH,YAAa,CACX,CACE,MAAO,CAAO,CAAC,EAAE,CACjB,WAAY,GACZ,OAAQ,uBACR,UAAW,sCACX,UAAW,OACb,EACA,CACE,MAAO,CAAO,CAAC,EAAE,CACjB,WAAY,GACZ,OAAQ,yBACR,UAAW,gCACX,UAAW,OACb,EACA,CACE,MAAO,CAAO,CAAC,EAAE,CACjB,WAAY,GACZ,OAAQ,qBACR,UAAW,8BACX,UAAW,OACb,EACD,AACH,EAEA,eAAgB,CACd,eAAgB,EAChB,mBArFgB,CAqFI,GACpB,CAtFqB,aAsFN,EACf,kBAAmB,AAvFmC,EAwFtD,cAAe,GACf,mBAAoB,CACtB,EAEA,cAAe,CACb,kBAAmB,CACjB,+CACA,6CACA,oDACA,mCACD,CACD,iBAAkB,CAChB,0CACA,mCACA,wCACA,yCACD,CACD,iBAAkB,CAChB,qCACA,yCACA,uCACA,2BACD,CACD,iBAAkB,CAChB,gDACA,8CACA,gCACA,oCAEJ,AADG,CAEL,CACF,CAGA,OAAO,yBACL,CAAc,CACd,CAA0B,CAC1B,CAAgB,CAChB,EAAsB,GAAM,CACN,CACtB,GAAI,EAAQ,MAAM,CAAG,GAAI,OAAO,KAEhC,IAAM,EAAS,EAAQ,GAAG,CAAC,GAAK,EAAE,KAAK,EACjC,EAAU,EAAQ,GAAG,CAAC,GAAK,EAAE,MAAM,EACnC,EAAe,EAAM,KAAK,CAG1B,EAAQ,EAAA,mBAAmB,CAAC,GAAG,CAAC,EAAQ,KAAK,GAAG,CAAC,GAAI,EAAO,MAAM,CAAG,IACrE,EAAO,EAAA,mBAAmB,CAAC,GAAG,CAAC,EAAQ,KAAK,GAAG,CAAC,EAAG,EAAO,MAAM,CAAG,IACnE,EAAM,IAAI,CAAC,YAAY,CAAC,EAAS,KAAK,GAAG,CAAC,GAAI,EAAQ,MAAM,CAAG,IAE/D,EAAe,CAAK,CAAC,EAAM,MAAM,CAAG,EAAE,CACtC,EAAc,CAAI,CAAC,EAAK,MAAM,CAAG,EAAE,CACnC,EAAa,CAAG,CAAC,EAAI,MAAM,CAAG,EAAE,CAGtC,GAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAO,EAAM,MAAM,CAAE,EAAc,IAK5D,GAAgB,EAJlB,MAD6E,CACtE,KAIyB,AAIlC,IAAM,EADmB,AACG,CAJa,IAGX,GAAG,CAAC,EAAe,GACF,EAAgB,IAG/D,GAAI,EAAqB,GAIrB,CAAC,AADoB,IAAI,CAAC,aACP,EADsB,CAAC,EAAQ,EAAM,AAC9B,GADiC,AAHjC,OAAO,KAOrC,EAJ6E,EAIvE,EAAY,EAAA,mBAAmB,CAAC,GAAG,CAAC,EAAS,IAC7C,EAAmB,CAAS,CAAC,EAAU,MAAM,CAAG,EAAE,CAClD,EAAkB,EAAM,MAAM,CAAG,EAEvC,GAAI,EAAkB,IAAK,OAAO,KAAK,AAGvC,IAAM,EAAmB,IAAI,CAAC,kBAHsC,OAGb,CAAC,EAAS,EAAc,GAC/E,GAAI,EAAmB,IAAK,OAAO,KAAK,AAKxC,IAAM,EAA4B,IAAf,EACb,EAAe,KAAK,CADsB,GAAG,CAAlB,AACF,CAAC,GAFX,EAAe,CAAA,EAAY,CAJ0B,AASpE,EAAU,CACC,CAJ4B,IAI3C,EACe,IAAf,EACe,KAAM,AAArB,AARoE,EASrE,CAMD,MAAO,CACL,SAAU,GARiC,kBAS3C,WANiB,IAAI,CAAC,2BAA2B,CACjD,EAAoB,EAAiB,EAAkB,EAAM,aAAa,EAM1E,WAAY,EACZ,SAAU,UACV,eACA,aACA,EACA,cAAe,gBACf,SAAU,EACV,aAAc,CAAC,yBAAyB,EAAE,EAAY,OAAO,CAAC,GAAG,CAAC,CAAC,CACnE,MAAO,CACL,sCACA,4CACA,sCACA,kCACD,CAED,aAAc,CACZ,MAAqB,AAAd,QACP,UAAW,QACX,OAAQ,+CACR,WAAY,CACV,CAAC,iBAAiB,EAAE,EAAY,OAAO,CAAC,GAAG,oBAAoB,CAAC,CAChE,CAAC,MAAM,EAAE,EAAa,OAAO,CAAC,GAAG,aAAa,CAAC,CAC/C,CAAC,uBAAuB,EAAE,CAAgB,IAAf,EAAM,MAAM,AAAG,CAAG,CAAE,cAAc,GAAA,CAAI,CACjE,+BACD,CACD,QAAS,uBACX,EAEA,YAAa,CACX,SAAU,CACR,MAAqB,KAAd,EACP,UAAW,OACX,OAAQ,iDACR,kBAAmB,CACjB,0BACA,6BACA,yBACD,AACH,EACA,YAAa,CACX,CACE,MAAO,CAAO,CAAC,EAAE,CACjB,WAAY,GACZ,OAAQ,6BACR,UAAW,2CACX,UAAW,OACb,EACA,CACE,MAAO,CAAO,CAAC,EAAE,CACjB,WAAY,GACZ,OAAQ,8BACR,UAAW,sCACX,UAAW,OACb,EACA,CACE,MAAO,CAAO,CAAC,EAAE,CACjB,WAAY,GACZ,OAAQ,uBACR,UAAW,kCACX,UAAW,OACb,EACD,AACH,EAEA,eAAgB,CACd,eAAgB,EAChB,mBArFgB,CAqFI,CACpB,cAAe,EACf,kBAAmB,EACnB,cAAe,GACf,mBAAoB,CACtB,EAEA,cAAe,CACb,kBAAmB,CACjB,yCACA,0CACA,mCACA,wCACD,CACD,iBAAkB,CAChB,2CACA,uCACA,sCACA,uCACD,CACD,iBAAkB,CAChB,sCACA,+BACA,iCACA,2BACD,CACD,iBAAkB,CAChB,wCACA,iCACA,oCACA,iCACD,AACH,CACF,CACF,CAGA,OAAe,mBACb,CAAgB,CAChB,CAAc,CACd,CAAa,CACb,CAAa,CACJ,CACT,OACE,GAAS,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EACvC,GAAU,IAAI,CAAC,gBAAgB,CAAC,SAAS,EACzC,CAAC,EAAM,SAAS,GAAI,CAAC,EAAK,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAC5D,EAAQ,CAEZ,CAEA,IAJkB,GAIH,aAAa,CAA0B,CAAE,CAAc,CAAY,CAChF,IAAM,EAAuB,EAAE,CAE/B,IAAK,IAAI,EAAI,EAAG,EAAI,EAAQ,MAAM,CAAE,IAAK,CACvC,AARwE,IAQlE,EAAO,CAAO,CAAC,EAAE,CAAC,IAAI,CACtB,EAAM,CAAO,CAAC,EAAE,CAAC,GAAG,CACpB,EAAY,CAAO,CAAC,EAAI,EAAE,CAAC,KAAK,CAEhC,EAAK,KAAK,GAAG,CACjB,EAAO,EACP,KAAK,GAAG,CAAC,EAAO,GAChB,KAAK,GAAG,CAAC,EAAM,IAGjB,EAAW,IAAI,CAAC,EAClB,CAEA,OAAO,EAAA,mBAAmB,CAAC,GAAG,CAAC,EAAY,EAC7C,CAEA,OAAe,cAAc,CAAuB,CAAU,CAE5D,MAAO,AAAC,GAAO,IAAI,CAAG,EAAO,GAAG,CAAG,EAAO,KAAA,AAAK,EAAI,CACrD,CAEA,OAAe,0BACb,CAA0B,CAC1B,CAAoB,CACpB,CAAW,CACH,CAKR,MAAO,CAFS,AACG,KADE,GAAG,IADJ,AACQ,EADA,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,GAAK,EAAE,IAAI,GAEzB,CAAA,EACT,CACtB,CAEA,OAAe,gBAAgB,CAAgB,CAAE,CAAc,CAAE,CAAgB,CAAW,CAE1F,IAAK,IAAI,EAAI,KAAK,GAAG,CAAC,EAAG,EAAO,MAAM,CAAG,GAAW,EAAI,EAAO,MAAM,CAAG,EAAG,IAAK,AAC9E,GAAI,CAAM,CAAC,EAAE,CAAG,CAAI,CAAC,EAAE,EAAI,CAAM,CAAC,EAAI,EAAE,CAAG,CAAI,CAAC,EAAI,EAAE,CACpD,CADsD,MAC/C,EAGX,GAHgB,IAGT,CACT,CAEA,OAAe,KANqB,wBAOlC,CAAqB,CACrB,CAAoB,CACpB,CAAc,CACd,CAAwB,CAChB,CACR,IAAI,EAAa,GAoBjB,OAjBI,EAAgB,EAAG,GAAc,GAC5B,EAAgB,EAAG,GAAc,GACjC,EAAgB,IAAG,IAAc,EAGtC,EAAe,GAAK,GAAc,GAC7B,EAAe,GAAK,GAAc,GAClC,EAAe,KAAK,IAAc,EAGvC,EAAS,IAAS,GAAc,GAC3B,EAAS,MAAS,IAAc,EAGrC,EAAmB,EAAG,GAAc,GAC/B,EAAmB,MAAK,IAAc,EAExC,KAAK,GAAG,CAAC,GAAI,KAAK,GAAG,CAAC,GAAI,GACnC,CAEA,OAAe,4BACb,CAAmB,CACnB,CAAuB,CACvB,CAAwB,CACxB,CAAqB,CACb,CACR,IAAI,EAAa,GAoBjB,OAjBI,EAAc,EAAG,GAAc,GAC1B,EAAc,EAAG,GAAc,GAC/B,EAAc,GAAG,KAAc,EAGpC,EAAkB,EAAG,GAAc,GAC9B,EAAkB,IAAK,GAAc,GACrC,EAAkB,MAAK,IAAc,EAG1C,EAAmB,EAAG,GAAc,GAC/B,EAAmB,EAAG,GAAc,GACpC,EAAmB,MAAK,IAAc,EAG3C,EAAgB,IAAG,IAAc,EAE9B,KAAK,GAAG,CAAC,GAAI,KAAK,GAAG,CAAC,GAAI,GACnC,CACF"}