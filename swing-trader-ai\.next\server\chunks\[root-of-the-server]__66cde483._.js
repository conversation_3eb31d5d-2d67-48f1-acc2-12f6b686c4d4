module.exports = [
"[project]/swing-trader-ai/.next-internal/server/app/api/trading/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/fs [external] (fs, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}),
"[externals]/path [external] (path, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}),
"[externals]/http [external] (http, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}),
"[externals]/https [external] (https, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}),
"[externals]/url [external] (url, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}),
"[externals]/stream [external] (stream, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}),
"[externals]/assert [external] (assert, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}),
"[externals]/tty [external] (tty, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}),
"[externals]/util [external] (util, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}),
"[externals]/os [external] (os, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}),
"[externals]/zlib [external] (zlib, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}),
"[externals]/events [external] (events, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}),
"[externals]/net [external] (net, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}),
"[externals]/tls [external] (tls, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}),
"[externals]/crypto [external] (crypto, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}),
"[externals]/buffer [external] (buffer, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}),
"[project]/swing-trader-ai/src/lib/alpaca.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "AlpacaAPI",
    ()=>AlpacaAPI,
    "alpacaAPI",
    ()=>alpacaAPI
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$alpacahq$2f$alpaca$2d$trade$2d$api$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/index.js [app-route] (ecmascript)");
;
class AlpacaAPI {
    client;
    constructor(){
        this.client = new __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$alpacahq$2f$alpaca$2d$trade$2d$api$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"]({
            keyId: process.env.ALPACA_API_KEY,
            secretKey: process.env.ALPACA_SECRET_KEY,
            baseUrl: process.env.ALPACA_BASE_URL || 'https://paper-api.alpaca.markets',
            usePolygon: false
        });
    }
    // Get account information
    async getAccount() {
        try {
            return await this.client.getAccount();
        } catch (error) {
            console.error('Error fetching account:', error);
            throw error;
        }
    }
    // Get account positions
    async getPositions() {
        try {
            return await this.client.getPositions();
        } catch (error) {
            console.error('Error fetching positions:', error);
            throw error;
        }
    }
    // Get orders
    async getOrders(status) {
        try {
            return await this.client.getOrders({
                status: status || 'all',
                limit: 100,
                nested: true,
                until: undefined,
                after: undefined,
                direction: undefined,
                symbols: undefined
            });
        } catch (error) {
            console.error('Error fetching orders:', error);
            throw error;
        }
    }
    // Place a market order
    async placeMarketOrder(symbol, qty, side, timeInForce = 'day') {
        try {
            return await this.client.createOrder({
                symbol,
                qty,
                side,
                type: 'market',
                time_in_force: timeInForce
            });
        } catch (error) {
            console.error('Error placing market order:', error);
            throw error;
        }
    }
    // Place a limit order
    async placeLimitOrder(symbol, qty, side, limitPrice, timeInForce = 'day') {
        try {
            return await this.client.createOrder({
                symbol,
                qty,
                side,
                type: 'limit',
                limit_price: limitPrice,
                time_in_force: timeInForce
            });
        } catch (error) {
            console.error('Error placing limit order:', error);
            throw error;
        }
    }
    // Place a stop-loss order
    async placeStopLossOrder(symbol, qty, side, stopPrice, timeInForce = 'day') {
        try {
            return await this.client.createOrder({
                symbol,
                qty,
                side,
                type: 'stop',
                stop_price: stopPrice,
                time_in_force: timeInForce
            });
        } catch (error) {
            console.error('Error placing stop-loss order:', error);
            throw error;
        }
    }
    // Place a bracket order (entry + stop loss + take profit)
    async placeBracketOrder(symbol, qty, side, limitPrice, stopLoss, takeProfit, timeInForce = 'day') {
        try {
            return await this.client.createOrder({
                symbol,
                qty,
                side,
                type: 'limit',
                limit_price: limitPrice,
                time_in_force: timeInForce,
                order_class: 'bracket',
                stop_loss: {
                    stop_price: stopLoss
                },
                take_profit: {
                    limit_price: takeProfit
                }
            });
        } catch (error) {
            console.error('Error placing bracket order:', error);
            throw error;
        }
    }
    // Cancel an order
    async cancelOrder(orderId) {
        try {
            return await this.client.cancelOrder(orderId);
        } catch (error) {
            console.error('Error canceling order:', error);
            throw error;
        }
    }
    // Cancel all orders
    async cancelAllOrders() {
        try {
            return await this.client.cancelAllOrders();
        } catch (error) {
            console.error('Error canceling all orders:', error);
            throw error;
        }
    }
    // Get portfolio history
    async getPortfolioHistory(period) {
        try {
            return await this.client.getPortfolioHistory({
                period: period || '1M',
                timeframe: '1Day'
            });
        } catch (error) {
            console.error('Error fetching portfolio history:', error);
            throw error;
        }
    }
    // Get market calendar
    async getMarketCalendar(start, end) {
        try {
            return await this.client.getCalendar({
                start,
                end
            });
        } catch (error) {
            console.error('Error fetching market calendar:', error);
            throw error;
        }
    }
    // Check if market is open
    async isMarketOpen() {
        try {
            const clock = await this.client.getClock();
            return clock.is_open;
        } catch (error) {
            console.error('Error checking market status:', error);
            return false;
        }
    }
    // Get buying power
    async getBuyingPower() {
        try {
            const account = await this.getAccount();
            return parseFloat(account.buying_power);
        } catch (error) {
            console.error('Error fetching buying power:', error);
            return 0;
        }
    }
    // Calculate position size based on risk
    calculatePositionSize(accountValue, riskPercentage, entryPrice, stopLoss) {
        const riskAmount = accountValue * (riskPercentage / 100);
        const riskPerShare = Math.abs(entryPrice - stopLoss);
        return Math.floor(riskAmount / riskPerShare);
    }
}
const alpacaAPI = new AlpacaAPI();
}),
"[project]/swing-trader-ai/src/lib/ibkr.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "IBKRAPI",
    ()=>IBKRAPI
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/node_modules/@stoqey/ib/dist/index.js [app-route] (ecmascript)");
;
class IBKRAPI {
    ib;
    config;
    connected = false;
    nextOrderId = 1;
    positions = new Map();
    orders = new Map();
    accountSummary = null;
    constructor(config){
        this.config = config;
        this.ib = new __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["IBApi"]({
            host: config.host,
            port: config.port,
            clientId: config.clientId
        });
        this.setupEventHandlers();
    }
    setupEventHandlers() {
        // Connection events
        this.ib.on(__TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].connected, ()=>{
            console.log('✅ Connected to IBKR');
            this.connected = true;
            this.requestNextOrderId();
            this.requestAccountSummary();
            this.requestPositions();
        });
        this.ib.on(__TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].disconnected, ()=>{
            console.log('❌ Disconnected from IBKR');
            this.connected = false;
        });
        this.ib.on(__TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].error, (err, code, reqId)=>{
            console.error(`IBKR Error ${code}:`, err);
        });
        // Order management
        this.ib.on(__TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].nextValidId, (orderId)=>{
            this.nextOrderId = orderId;
            console.log(`Next valid order ID: ${orderId}`);
        });
        this.ib.on(__TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].orderStatus, (orderId, status, filled, remaining, avgFillPrice, permId, parentId, lastFillPrice, clientId, whyHeld, mktCapPrice)=>{
            const order = this.orders.get(orderId);
            if (order) {
                order.status = status;
                order.filled = filled;
                order.remaining = remaining;
                this.orders.set(orderId, order);
            }
        });
        // Position updates
        this.ib.on(__TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].position, (account, contract, position, avgCost)=>{
            const symbol = contract.symbol;
            const existingPosition = this.positions.get(symbol) || {
                symbol,
                position: 0,
                marketPrice: 0,
                marketValue: 0,
                averageCost: 0,
                unrealizedPNL: 0,
                realizedPNL: 0
            };
            existingPosition.position = position;
            existingPosition.averageCost = avgCost;
            this.positions.set(symbol, existingPosition);
        });
        // Account summary
        this.ib.on(__TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].accountSummary, (reqId, account, tag, value, currency)=>{
            if (!this.accountSummary) {
                this.accountSummary = {
                    totalCashValue: 0,
                    netLiquidation: 0,
                    grossPositionValue: 0,
                    availableFunds: 0,
                    buyingPower: 0,
                    unrealizedPnL: 0,
                    realizedPnL: 0
                };
            }
            switch(tag){
                case 'TotalCashValue':
                    this.accountSummary.totalCashValue = parseFloat(value);
                    break;
                case 'NetLiquidation':
                    this.accountSummary.netLiquidation = parseFloat(value);
                    break;
                case 'GrossPositionValue':
                    this.accountSummary.grossPositionValue = parseFloat(value);
                    break;
                case 'AvailableFunds':
                    this.accountSummary.availableFunds = parseFloat(value);
                    break;
                case 'BuyingPower':
                    this.accountSummary.buyingPower = parseFloat(value);
                    break;
                case 'UnrealizedPnL':
                    this.accountSummary.unrealizedPnL = parseFloat(value);
                    break;
                case 'RealizedPnL':
                    this.accountSummary.realizedPnL = parseFloat(value);
                    break;
            }
        });
    }
    async connect() {
        return new Promise((resolve, reject)=>{
            if (this.connected) {
                resolve();
                return;
            }
            const timeout = setTimeout(()=>{
                reject(new Error('Connection timeout'));
            }, 10000);
            this.ib.once(__TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].connected, ()=>{
                clearTimeout(timeout);
                resolve();
            });
            this.ib.once(__TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].error, (err)=>{
                clearTimeout(timeout);
                reject(err);
            });
            this.ib.connect();
        });
    }
    disconnect() {
        if (this.connected) {
            this.ib.disconnect();
        }
    }
    requestNextOrderId() {
        this.ib.reqIds(1);
    }
    requestAccountSummary() {
        this.ib.reqAccountSummary(1, 'All', 'TotalCashValue,NetLiquidation,GrossPositionValue,AvailableFunds,BuyingPower,UnrealizedPnL,RealizedPnL');
    }
    requestPositions() {
        this.ib.reqPositions();
    }
    // Create a stock contract
    createStockContract(symbol) {
        return {
            symbol: symbol.toUpperCase(),
            secType: 'STK',
            exchange: 'SMART',
            currency: 'USD'
        };
    }
    // Place a market order
    async placeMarketOrder(symbol, action, quantity) {
        if (!this.connected) {
            throw new Error('Not connected to IBKR');
        }
        const contract = this.createStockContract(symbol);
        const order = {
            orderId: this.nextOrderId,
            action,
            totalQuantity: quantity,
            orderType: 'MKT'
        };
        // Store order for tracking
        this.orders.set(this.nextOrderId, {
            orderId: this.nextOrderId,
            symbol: symbol.toUpperCase(),
            action,
            quantity,
            orderType: 'MKT',
            status: 'Submitted',
            filled: 0,
            remaining: quantity
        });
        this.ib.placeOrder(this.nextOrderId, contract, order);
        const orderId = this.nextOrderId;
        this.nextOrderId++;
        return orderId;
    }
    // Place a limit order
    async placeLimitOrder(symbol, action, quantity, price) {
        if (!this.connected) {
            throw new Error('Not connected to IBKR');
        }
        const contract = this.createStockContract(symbol);
        const order = {
            orderId: this.nextOrderId,
            action,
            totalQuantity: quantity,
            orderType: 'LMT',
            lmtPrice: price
        };
        // Store order for tracking
        this.orders.set(this.nextOrderId, {
            orderId: this.nextOrderId,
            symbol: symbol.toUpperCase(),
            action,
            quantity,
            orderType: 'LMT',
            price,
            status: 'Submitted',
            filled: 0,
            remaining: quantity
        });
        this.ib.placeOrder(this.nextOrderId, contract, order);
        const orderId = this.nextOrderId;
        this.nextOrderId++;
        return orderId;
    }
    // Place a stop-loss order
    async placeStopOrder(symbol, action, quantity, stopPrice) {
        if (!this.connected) {
            throw new Error('Not connected to IBKR');
        }
        const contract = this.createStockContract(symbol);
        const order = {
            orderId: this.nextOrderId,
            action,
            totalQuantity: quantity,
            orderType: 'STP',
            auxPrice: stopPrice
        };
        // Store order for tracking
        this.orders.set(this.nextOrderId, {
            orderId: this.nextOrderId,
            symbol: symbol.toUpperCase(),
            action,
            quantity,
            orderType: 'STP',
            price: stopPrice,
            status: 'Submitted',
            filled: 0,
            remaining: quantity
        });
        this.ib.placeOrder(this.nextOrderId, contract, order);
        const orderId = this.nextOrderId;
        this.nextOrderId++;
        return orderId;
    }
    // Cancel an order
    async cancelOrder(orderId) {
        if (!this.connected) {
            throw new Error('Not connected to IBKR');
        }
        this.ib.cancelOrder(orderId);
    }
    // Get account summary
    getAccountSummary() {
        return this.accountSummary;
    }
    // Get all positions
    getPositions() {
        return Array.from(this.positions.values());
    }
    // Get position for specific symbol
    getPosition(symbol) {
        return this.positions.get(symbol.toUpperCase()) || null;
    }
    // Get all orders
    getOrders() {
        return Array.from(this.orders.values());
    }
    // Get specific order
    getOrder(orderId) {
        return this.orders.get(orderId) || null;
    }
    // Check if connected
    isConnected() {
        return this.connected;
    }
}
}),
"[project]/swing-trader-ai/src/lib/tradingBroker.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "UnifiedTradingBroker",
    ()=>UnifiedTradingBroker
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$alpaca$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/src/lib/alpaca.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$ibkr$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/src/lib/ibkr.ts [app-route] (ecmascript)");
;
;
class UnifiedTradingBroker {
    alpacaAPI = null;
    ibkrAPI = null;
    activeBroker;
    constructor(brokerType = 'alpaca'){
        this.activeBroker = brokerType;
    }
    // Initialize Alpaca
    async initializeAlpaca(apiKey, secretKey, baseUrl) {
        this.alpacaAPI = new __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$alpaca$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AlpacaAPI"](apiKey, secretKey, baseUrl);
        if (this.activeBroker === 'alpaca') {
            console.log('✅ Alpaca API initialized');
        }
    }
    // Initialize IBKR
    async initializeIBKR(config) {
        this.ibkrAPI = new __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$ibkr$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["IBKRAPI"](config);
        if (this.activeBroker === 'ibkr') {
            await this.ibkrAPI.connect();
            console.log('✅ IBKR API initialized and connected');
        }
    }
    // Switch active broker
    async switchBroker(brokerType) {
        this.activeBroker = brokerType;
        if (brokerType === 'ibkr' && this.ibkrAPI && !this.ibkrAPI.isConnected()) {
            await this.ibkrAPI.connect();
        }
        console.log(`✅ Switched to ${brokerType.toUpperCase()} broker`);
    }
    // Get active broker
    getActiveBroker() {
        return this.activeBroker;
    }
    // Place a trade
    async placeTrade(request) {
        if (this.activeBroker === 'alpaca') {
            return this.placeAlpacaTrade(request);
        } else if (this.activeBroker === 'ibkr') {
            return this.placeIBKRTrade(request);
        } else {
            throw new Error('No active broker configured');
        }
    }
    async placeAlpacaTrade(request) {
        if (!this.alpacaAPI) {
            throw new Error('Alpaca API not initialized');
        }
        let alpacaOrder;
        switch(request.orderType){
            case 'market':
                alpacaOrder = await this.alpacaAPI.placeMarketOrder(request.symbol, request.side, request.quantity);
                break;
            case 'limit':
                if (!request.price) throw new Error('Limit price required for limit order');
                alpacaOrder = await this.alpacaAPI.placeLimitOrder(request.symbol, request.side, request.quantity, request.price);
                break;
            case 'stop':
                if (!request.stopPrice) throw new Error('Stop price required for stop order');
                alpacaOrder = await this.alpacaAPI.placeStopOrder(request.symbol, request.side, request.quantity, request.stopPrice);
                break;
            default:
                throw new Error(`Order type ${request.orderType} not supported for Alpaca`);
        }
        return {
            id: alpacaOrder.id,
            symbol: request.symbol,
            side: request.side,
            quantity: request.quantity,
            orderType: request.orderType,
            price: request.price,
            stopPrice: request.stopPrice,
            status: alpacaOrder.status,
            filledQuantity: parseFloat(alpacaOrder.filled_qty || '0'),
            remainingQuantity: request.quantity - parseFloat(alpacaOrder.filled_qty || '0'),
            broker: 'alpaca'
        };
    }
    async placeIBKRTrade(request) {
        if (!this.ibkrAPI) {
            throw new Error('IBKR API not initialized');
        }
        let orderId;
        const action = request.side.toUpperCase();
        switch(request.orderType){
            case 'market':
                orderId = await this.ibkrAPI.placeMarketOrder(request.symbol, action, request.quantity);
                break;
            case 'limit':
                if (!request.price) throw new Error('Limit price required for limit order');
                orderId = await this.ibkrAPI.placeLimitOrder(request.symbol, action, request.quantity, request.price);
                break;
            case 'stop':
                if (!request.stopPrice) throw new Error('Stop price required for stop order');
                orderId = await this.ibkrAPI.placeStopOrder(request.symbol, action, request.quantity, request.stopPrice);
                break;
            default:
                throw new Error(`Order type ${request.orderType} not supported for IBKR`);
        }
        return {
            id: orderId.toString(),
            symbol: request.symbol,
            side: request.side,
            quantity: request.quantity,
            orderType: request.orderType,
            price: request.price,
            stopPrice: request.stopPrice,
            status: 'Submitted',
            filledQuantity: 0,
            remainingQuantity: request.quantity,
            broker: 'ibkr'
        };
    }
    // Cancel an order
    async cancelOrder(orderId) {
        if (this.activeBroker === 'alpaca') {
            if (!this.alpacaAPI) throw new Error('Alpaca API not initialized');
            await this.alpacaAPI.cancelOrder(orderId);
        } else if (this.activeBroker === 'ibkr') {
            if (!this.ibkrAPI) throw new Error('IBKR API not initialized');
            await this.ibkrAPI.cancelOrder(parseInt(orderId));
        }
    }
    // Get account information
    async getAccount() {
        if (this.activeBroker === 'alpaca') {
            return this.getAlpacaAccount();
        } else if (this.activeBroker === 'ibkr') {
            return this.getIBKRAccount();
        } else {
            throw new Error('No active broker configured');
        }
    }
    async getAlpacaAccount() {
        if (!this.alpacaAPI) throw new Error('Alpaca API not initialized');
        const account = await this.alpacaAPI.getAccount();
        const positions = await this.alpacaAPI.getPositions();
        return {
            totalValue: parseFloat(account.portfolio_value),
            cashValue: parseFloat(account.cash),
            buyingPower: parseFloat(account.buying_power),
            unrealizedPL: parseFloat(account.unrealized_pl || '0'),
            realizedPL: 0,
            positions: positions.map((pos)=>({
                    symbol: pos.symbol,
                    quantity: Math.abs(parseFloat(pos.qty)),
                    marketValue: parseFloat(pos.market_value || '0'),
                    averagePrice: parseFloat(pos.avg_entry_price || '0'),
                    unrealizedPL: parseFloat(pos.unrealized_pl || '0'),
                    side: parseFloat(pos.qty) > 0 ? 'long' : 'short'
                }))
        };
    }
    async getIBKRAccount() {
        if (!this.ibkrAPI) throw new Error('IBKR API not initialized');
        const accountSummary = this.ibkrAPI.getAccountSummary();
        const positions = this.ibkrAPI.getPositions();
        if (!accountSummary) {
            throw new Error('IBKR account summary not available');
        }
        return {
            totalValue: accountSummary.netLiquidation,
            cashValue: accountSummary.totalCashValue,
            buyingPower: accountSummary.buyingPower,
            unrealizedPL: accountSummary.unrealizedPnL,
            realizedPL: accountSummary.realizedPnL,
            positions: positions.map((pos)=>({
                    symbol: pos.symbol,
                    quantity: Math.abs(pos.position),
                    marketValue: pos.marketValue,
                    averagePrice: pos.averageCost,
                    unrealizedPL: pos.unrealizedPNL,
                    side: pos.position > 0 ? 'long' : 'short'
                }))
        };
    }
    // Get all orders
    async getOrders() {
        if (this.activeBroker === 'alpaca') {
            if (!this.alpacaAPI) throw new Error('Alpaca API not initialized');
            const orders = await this.alpacaAPI.getOrders();
            return orders.map((order)=>({
                    id: order.id,
                    symbol: order.symbol,
                    side: order.side,
                    quantity: parseFloat(order.qty),
                    orderType: order.order_type,
                    price: order.limit_price ? parseFloat(order.limit_price) : undefined,
                    stopPrice: order.stop_price ? parseFloat(order.stop_price) : undefined,
                    status: order.status,
                    filledQuantity: parseFloat(order.filled_qty || '0'),
                    remainingQuantity: parseFloat(order.qty) - parseFloat(order.filled_qty || '0'),
                    broker: 'alpaca'
                }));
        } else if (this.activeBroker === 'ibkr') {
            if (!this.ibkrAPI) throw new Error('IBKR API not initialized');
            const orders = this.ibkrAPI.getOrders();
            return orders.map((order)=>({
                    id: order.orderId.toString(),
                    symbol: order.symbol,
                    side: order.action.toLowerCase(),
                    quantity: order.quantity,
                    orderType: order.orderType.toLowerCase(),
                    price: order.price,
                    status: order.status,
                    filledQuantity: order.filled,
                    remainingQuantity: order.remaining,
                    broker: 'ibkr'
                }));
        } else {
            throw new Error('No active broker configured');
        }
    }
    // Get positions
    async getPositions() {
        const account = await this.getAccount();
        return account.positions;
    }
    // Check if broker is connected/ready
    isReady() {
        if (this.activeBroker === 'alpaca') {
            return this.alpacaAPI !== null;
        } else if (this.activeBroker === 'ibkr') {
            return this.ibkrAPI !== null && this.ibkrAPI.isConnected();
        }
        return false;
    }
    // Disconnect (mainly for IBKR)
    disconnect() {
        if (this.ibkrAPI) {
            this.ibkrAPI.disconnect();
        }
    }
}
}),
"[project]/swing-trader-ai/src/app/api/trading/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "GET",
    ()=>GET,
    "POST",
    ()=>POST
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$tradingBroker$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/src/lib/tradingBroker.ts [app-route] (ecmascript)");
;
;
// Global broker instance (in production, you'd want proper session management)
let tradingBroker = null;
// Initialize the trading broker
async function initializeBroker() {
    if (!tradingBroker) {
        tradingBroker = new __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$tradingBroker$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UnifiedTradingBroker"]('alpaca'); // Default to Alpaca
        // Initialize Alpaca if credentials are available
        const alpacaKey = process.env.ALPACA_API_KEY;
        const alpacaSecret = process.env.ALPACA_SECRET_KEY;
        const alpacaBaseUrl = process.env.ALPACA_BASE_URL || 'https://paper-api.alpaca.markets';
        if (alpacaKey && alpacaSecret) {
            await tradingBroker.initializeAlpaca(alpacaKey, alpacaSecret, alpacaBaseUrl);
        }
        // Initialize IBKR if credentials are available
        const ibkrHost = process.env.IBKR_HOST || 'localhost';
        const ibkrPort = parseInt(process.env.IBKR_PORT || '7497'); // Paper trading port
        const ibkrClientId = parseInt(process.env.IBKR_CLIENT_ID || '1');
        try {
            await tradingBroker.initializeIBKR({
                host: ibkrHost,
                port: ibkrPort,
                clientId: ibkrClientId,
                paperTrading: true
            });
        } catch (error) {
            console.warn('IBKR initialization failed (this is normal if TWS/Gateway is not running):', error);
        }
    }
    return tradingBroker;
}
async function GET(request) {
    try {
        const broker = await initializeBroker();
        const { searchParams } = new URL(request.url);
        const action = searchParams.get('action');
        switch(action){
            case 'account':
                const account = await broker.getAccount();
                return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    data: account,
                    broker: broker.getActiveBroker()
                });
            case 'positions':
                const positions = await broker.getPositions();
                return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    data: positions,
                    broker: broker.getActiveBroker()
                });
            case 'orders':
                const orders = await broker.getOrders();
                return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    data: orders,
                    broker: broker.getActiveBroker()
                });
            case 'status':
                return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    data: {
                        activeBroker: broker.getActiveBroker(),
                        isReady: broker.isReady(),
                        brokers: {
                            alpaca: process.env.ALPACA_API_KEY ? 'configured' : 'not configured',
                            ibkr: process.env.IBKR_HOST ? 'configured' : 'not configured'
                        }
                    }
                });
            default:
                return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: false,
                    error: 'Invalid action. Use: account, positions, orders, or status'
                }, {
                    status: 400
                });
        }
    } catch (error) {
        console.error('Trading API error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const broker = await initializeBroker();
        const body = await request.json();
        const { action } = body;
        switch(action){
            case 'place_order':
                const tradeRequest = {
                    symbol: body.symbol,
                    side: body.side,
                    quantity: body.quantity,
                    orderType: body.orderType || 'market',
                    price: body.price,
                    stopPrice: body.stopPrice,
                    timeInForce: body.timeInForce || 'day'
                };
                const order = await broker.placeTrade(tradeRequest);
                return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    data: order,
                    message: `${tradeRequest.side.toUpperCase()} order placed for ${tradeRequest.quantity} shares of ${tradeRequest.symbol}`
                });
            case 'cancel_order':
                if (!body.orderId) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                        success: false,
                        error: 'Order ID is required'
                    }, {
                        status: 400
                    });
                }
                await broker.cancelOrder(body.orderId);
                return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    message: `Order ${body.orderId} cancelled`
                });
            case 'switch_broker':
                if (!body.broker || ![
                    'alpaca',
                    'ibkr'
                ].includes(body.broker)) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                        success: false,
                        error: 'Valid broker required (alpaca or ibkr)'
                    }, {
                        status: 400
                    });
                }
                await broker.switchBroker(body.broker);
                return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    data: {
                        activeBroker: broker.getActiveBroker(),
                        isReady: broker.isReady()
                    },
                    message: `Switched to ${body.broker.toUpperCase()}`
                });
            case 'execute_swing_trade':
                // Execute a complete swing trade setup
                const { symbol, strategy, accountSize, riskPercentage = 1.0, entryPrice, stopLoss, takeProfit, quantity: customQuantity, orderType = 'limit' } = body;
                if (!symbol || !entryPrice || !stopLoss) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                        success: false,
                        error: 'Symbol, entry price, and stop loss are required'
                    }, {
                        status: 400
                    });
                }
                // Calculate position size based on risk or use custom quantity
                let finalQuantity = customQuantity;
                let riskAmount = 0;
                let riskPerShare = 0;
                if (!customQuantity) {
                    riskAmount = (accountSize || 100000) * (riskPercentage / 100);
                    riskPerShare = Math.abs(entryPrice - stopLoss);
                    finalQuantity = Math.floor(riskAmount / riskPerShare);
                } else {
                    riskPerShare = Math.abs(entryPrice - stopLoss);
                    riskAmount = finalQuantity * riskPerShare;
                }
                if (finalQuantity <= 0) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                        success: false,
                        error: 'Calculated position size is too small'
                    }, {
                        status: 400
                    });
                }
                // Place the entry order
                const entryOrderRequest = {
                    symbol,
                    side: 'buy',
                    quantity: finalQuantity,
                    orderType: orderType,
                    price: orderType === 'limit' ? entryPrice : undefined
                };
                const entryOrder = await broker.placeTrade(entryOrderRequest);
                // Prepare stop loss and take profit orders (bracket order simulation)
                const stopLossOrder = {
                    symbol,
                    side: 'sell',
                    quantity: finalQuantity,
                    orderType: 'stop',
                    stopPrice: stopLoss
                };
                const takeProfitOrder = takeProfit ? {
                    symbol,
                    side: 'sell',
                    quantity: finalQuantity,
                    orderType: 'limit',
                    price: takeProfit
                } : null;
                return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    data: {
                        orderId: entryOrder.id || entryOrder.orderId,
                        entryOrder,
                        strategy,
                        positionSize: finalQuantity,
                        riskAmount,
                        riskPerShare,
                        stopLoss,
                        takeProfit,
                        orderType,
                        stopLossOrder,
                        takeProfitOrder,
                        executionTime: new Date().toISOString()
                    },
                    message: `Swing trade executed: ${orderType.toUpperCase()} order for ${finalQuantity} shares of ${symbol} at $${entryPrice}`
                });
            default:
                return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: false,
                    error: 'Invalid action. Use: place_order, cancel_order, switch_broker, or execute_swing_trade'
                }, {
                    status: 400
                });
        }
    } catch (error) {
        console.error('Trading API error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__66cde483._.js.map