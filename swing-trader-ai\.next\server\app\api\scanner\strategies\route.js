var R=require("../../../../chunks/[turbopack]_runtime.js")("server/app/api/scanner/strategies/route.js")
R.c("server/chunks/6bf44_next_f3741d49._.js")
R.c("server/chunks/6bf44_axios_lib_4f26ea03._.js")
R.c("server/chunks/6bf44_mime-db_6bff638e._.js")
R.c("server/chunks/6bf44_f2f2abb1._.js")
R.c("server/chunks/[root-of-the-server]__fc77cf7a._.js")
R.m("[project]/swing-trader-ai/.next-internal/server/app/api/scanner/strategies/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/swing-trader-ai/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/swing-trader-ai/src/app/api/scanner/strategies/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/swing-trader-ai/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/swing-trader-ai/src/app/api/scanner/strategies/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
