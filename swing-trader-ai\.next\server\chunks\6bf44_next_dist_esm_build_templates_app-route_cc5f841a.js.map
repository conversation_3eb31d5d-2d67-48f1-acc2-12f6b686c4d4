{"version": 3, "sources": ["turbopack:///[project]/swing-trader-ai/node_modules/next/dist/esm/build/templates/app-route.js", "turbopack:///[project]/swing-trader-ai/src/app/api/scanner/perfect-pick/route.ts", "turbopack:///[project]/swing-trader-ai/src/lib/technicalGateAnalysis.ts", "turbopack:///[project]/swing-trader-ai/src/lib/perfectPickTradingSystem.ts"], "sourcesContent": ["import { AppRouteRouteModule } from \"next/dist/esm/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/esm/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/esm/server/lib/patch-fetch\";\nimport { getRequestMeta } from \"next/dist/esm/server/request-meta\";\nimport { getTracer, SpanKind } from \"next/dist/esm/server/lib/trace/tracer\";\nimport { normalizeAppPath } from \"next/dist/esm/shared/lib/router/utils/app-paths\";\nimport { NodeNextRequest, NodeNextResponse } from \"next/dist/esm/server/base-http/node\";\nimport { NextRequestAdapter, signalFromNodeResponse } from \"next/dist/esm/server/web/spec-extension/adapters/next-request\";\nimport { BaseServerSpan } from \"next/dist/esm/server/lib/trace/constants\";\nimport { getRevalidateReason } from \"next/dist/esm/server/instrumentation/utils\";\nimport { sendResponse } from \"next/dist/esm/server/send-response\";\nimport { fromNodeOutgoingHttpHeaders, toNodeOutgoingHttpHeaders } from \"next/dist/esm/server/web/utils\";\nimport { getCacheControlHeader } from \"next/dist/esm/server/lib/cache-control\";\nimport { INFINITE_CACHE, NEXT_CACHE_TAGS_HEADER } from \"next/dist/esm/lib/constants\";\nimport { NoFallbackError } from \"next/dist/esm/shared/lib/no-fallback-error.external\";\nimport { CachedRouteKind } from \"next/dist/esm/server/response-cache\";\nimport * as userland from \"INNER_APP_ROUTE\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/scanner/perfect-pick/route\",\n        pathname: \"/api/scanner/perfect-pick\",\n        filename: \"route\",\n        bundlePath: \"\"\n    },\n    distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n    relativeProjectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || '',\n    resolvedPagePath: \"[project]/swing-trader-ai/src/app/api/scanner/perfect-pick/route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\nexport async function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/scanner/perfect-pick/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (process.env.TURBOPACK) {\n        srcPage = srcPage.replace(/\\/index$/, '') || '/';\n    } else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = process.env.__NEXT_MULTI_ZONE_DRAFT_MODE;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = normalizeAppPath(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = getTracer();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: getRequestMeta(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new NodeNextRequest(req);\n    const nodeNextRes = new NodeNextResponse(res);\n    const nextReq = NextRequestAdapter.fromNodeNextRequest(nodeNextReq, signalFromNodeResponse(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!getRequestMeta(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = toNodeOutgoingHttpHeaders(response.headers);\n                        if (cacheTags) {\n                            headers[NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await sendResponse(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: getRevalidateReason({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!getRequestMeta(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = fromNodeOutgoingHttpHeaders(cacheEntry.value.headers);\n            if (!(getRequestMeta(req, 'minimalMode') && isIsr)) {\n                headers.delete(NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', getCacheControlHeader(cacheEntry.cacheControl));\n            }\n            await sendResponse(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        if (!(err instanceof NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: getRevalidateReason({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await sendResponse(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n", "import { NextRequest, NextResponse } from 'next/server'\nimport { PerfectPickTradingSystem } from '@/lib/perfectPickTradingSystem'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    \n    const accountSize = parseInt(searchParams.get('accountSize') || '100000')\n    const riskPercent = parseFloat(searchParams.get('riskPercent') || '2')\n    const customUniverse = searchParams.get('universe')?.split(',').filter(Boolean)\n    const limit = parseInt(searchParams.get('limit') || '20')\n\n    console.log('🎯 Perfect-Pick API called with params:', {\n      accountSize,\n      riskPercent,\n      customUniverse: customUniverse?.length || 'default',\n      limit\n    })\n\n    // Initialize Perfect-Pick Trading System\n    const perfectPickSystem = new PerfectPickTradingSystem(\n      process.env.FMP_API_KEY,\n      process.env.POLYGON_API_KEY\n    )\n\n    // Run the complete Perfect-Pick scan\n    const setups = await perfectPickSystem.runPerfectPickScan(\n      accountSize,\n      riskPercent,\n      customUniverse\n    )\n\n    // Limit results\n    const limitedSetups = setups.slice(0, limit)\n\n    // Get summary statistics\n    const summary = perfectPickSystem.getSetupSummary(limitedSetups)\n\n    const response = {\n      success: true,\n      data: {\n        setups: limitedSetups,\n        summary,\n        scanParams: {\n          accountSize,\n          riskPercent,\n          universeSize: customUniverse?.length || 'default',\n          limit\n        },\n        timestamp: new Date().toISOString()\n      }\n    }\n\n    return NextResponse.json(response)\n  } catch (error) {\n    console.error('Error in Perfect-Pick API:', error)\n    return NextResponse.json(\n      { \n        success: false, \n        error: 'Failed to run Perfect-Pick scan',\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const { action, data } = body\n\n    const perfectPickSystem = new PerfectPickTradingSystem(\n      process.env.FMP_API_KEY,\n      process.env.POLYGON_API_KEY\n    )\n\n    switch (action) {\n      case 'update_setups':\n        const updatedSetups = await perfectPickSystem.updatePerfectPickSetups(data.setups)\n        return NextResponse.json({\n          success: true,\n          data: { setups: updatedSetups }\n        })\n\n      case 'generate_entry_trigger':\n        const entryTrigger = await perfectPickSystem.generateEntryTrigger(\n          data.symbol,\n          data.preMarketHigh\n        )\n        return NextResponse.json({\n          success: true,\n          data: { entryTrigger }\n        })\n\n      default:\n        return NextResponse.json(\n          { success: false, error: 'Invalid action' },\n          { status: 400 }\n        )\n    }\n  } catch (error) {\n    console.error('Error in Perfect-Pick POST API:', error)\n    return NextResponse.json(\n      { \n        success: false, \n        error: 'Failed to process Perfect-Pick request',\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    )\n  }\n}\n", "import { TechnicalGateAnalysis, CandlestickData } from '@/types/trading'\nimport { TechnicalIndicators } from './indicators'\nimport { PolygonAPI } from './polygon'\nimport { FMPAPI } from './fmp'\n\nexport class TechnicalGateAnalyzer {\n  private polygonAPI: PolygonAPI\n  private fmpAPI: FMPAPI\n\n  constructor(polygonApiKey?: string, fmpApiKey?: string) {\n    this.polygonAPI = new PolygonAPI(polygonApiKey)\n    this.fmpAPI = new FMPAPI(fmpApiKey)\n  }\n\n  /**\n   * Perform comprehensive technical gate analysis\n   */\n  async analyzeTechnicalGate(symbol: string): Promise<TechnicalGateAnalysis | null> {\n    try {\n      // Get historical data (need at least 200 days for SMA200)\n      const historicalData = await this.getHistoricalData(symbol, 250)\n      \n      if (!historicalData || historicalData.length < 200) {\n        console.error(`Insufficient data for ${symbol} - need at least 200 days`)\n        return null\n      }\n\n      const currentPrice = historicalData[historicalData.length - 1].close\n      \n      // Calculate technical indicators\n      const sma200 = TechnicalIndicators.calculateSMA(historicalData, 200)\n      const ema8 = TechnicalIndicators.calculateEMA(historicalData, 8)\n      const vwap = this.calculateVWAP(historicalData.slice(-20)) // 20-day VWAP\n      \n      // Analyze trend confirmation\n      const dailyTrendConfirmed = this.analyzeDailyTrend(historicalData)\n      \n      // Check moving average conditions\n      const aboveSMA200 = currentPrice > sma200[sma200.length - 1]\n      const aboveEMA8 = currentPrice > ema8[ema8.length - 1]\n      const respectsEMA8 = this.checkEMA8Respect(historicalData, ema8)\n      \n      // Check for all-time high\n      const isAtAllTimeHigh = this.checkAllTimeHigh(historicalData)\n      \n      // Check for clean breakout\n      const hasCleanBreakout = this.checkCleanBreakout(historicalData)\n      \n      // Check volume expansion\n      const volumeExpansion = this.checkVolumeExpansion(historicalData)\n      \n      // Calculate resistance and support levels\n      const resistanceLevels = this.calculateResistanceLevels(historicalData)\n      const supportLevels = this.calculateSupportLevels(historicalData)\n      \n      // Calculate overall grade and score\n      const gateScore = this.calculateGateScore({\n        dailyTrendConfirmed,\n        aboveSMA200,\n        aboveEMA8,\n        respectsEMA8,\n        isAtAllTimeHigh,\n        hasCleanBreakout,\n        volumeExpansion\n      })\n      \n      const overallGrade = this.calculateOverallGrade(gateScore)\n      \n      const analysis: TechnicalGateAnalysis = {\n        symbol,\n        dailyTrendConfirmed,\n        aboveSMA200,\n        aboveEMA8,\n        respectsEMA8,\n        isAtAllTimeHigh,\n        hasCleanBreakout,\n        volumeExpansion,\n        overallGrade,\n        gateScore,\n        resistanceLevels,\n        supportLevels,\n        keyTechnicalLevels: {\n          sma200: sma200[sma200.length - 1],\n          ema8: ema8[ema8.length - 1],\n          vwap,\n          previousHigh: Math.max(...historicalData.slice(-20).map(d => d.high)),\n          previousLow: Math.min(...historicalData.slice(-20).map(d => d.low))\n        }\n      }\n      \n      return analysis\n    } catch (error) {\n      console.error(`Error analyzing technical gate for ${symbol}:`, error)\n      return null\n    }\n  }\n\n  /**\n   * Get historical candlestick data\n   */\n  private async getHistoricalData(symbol: string, days: number): Promise<CandlestickData[]> {\n    try {\n      const endDate = new Date()\n      const startDate = new Date()\n      startDate.setDate(startDate.getDate() - days)\n      \n      return await this.polygonAPI.getHistoricalData(\n        symbol,\n        startDate.toISOString().split('T')[0],\n        endDate.toISOString().split('T')[0],\n        '1',\n        'day'\n      )\n    } catch (error) {\n      console.error(`Error fetching historical data for ${symbol}:`, error)\n      return []\n    }\n  }\n\n  /**\n   * Analyze daily trend confirmation (higher highs, higher lows over 20+ days)\n   */\n  private analyzeDailyTrend(data: CandlestickData[]): boolean {\n    if (data.length < 20) return false\n    \n    const recent20Days = data.slice(-20)\n    const first10Days = recent20Days.slice(0, 10)\n    const last10Days = recent20Days.slice(10)\n    \n    const firstPeriodHigh = Math.max(...first10Days.map(d => d.high))\n    const firstPeriodLow = Math.min(...first10Days.map(d => d.low))\n    const lastPeriodHigh = Math.max(...last10Days.map(d => d.high))\n    const lastPeriodLow = Math.min(...last10Days.map(d => d.low))\n    \n    // Check for higher highs and higher lows\n    return lastPeriodHigh > firstPeriodHigh && lastPeriodLow > firstPeriodLow\n  }\n\n  /**\n   * Check if stock consistently respects/reclaims 8-EMA\n   */\n  private checkEMA8Respect(data: CandlestickData[], ema8: number[]): boolean {\n    if (data.length < 20 || ema8.length < 20) return false\n    \n    const recent20Days = data.slice(-20)\n    const recent20EMA = ema8.slice(-20)\n    \n    let respectCount = 0\n    \n    for (let i = 0; i < recent20Days.length; i++) {\n      const candle = recent20Days[i]\n      const emaValue = recent20EMA[i]\n      \n      // Check if low didn't break significantly below EMA8 (allow 2% cushion)\n      if (candle.low >= emaValue * 0.98) {\n        respectCount++\n      }\n    }\n    \n    // Stock respects EMA8 if it holds above it 70% of the time\n    return respectCount / recent20Days.length >= 0.7\n  }\n\n  /**\n   * Check if stock is at or near all-time high\n   */\n  private checkAllTimeHigh(data: CandlestickData[]): boolean {\n    const currentPrice = data[data.length - 1].close\n    const allTimeHigh = Math.max(...data.map(d => d.high))\n    \n    // Consider \"at ATH\" if within 5% of all-time high\n    return currentPrice >= allTimeHigh * 0.95\n  }\n\n  /**\n   * Check for clean breakout from consolidation patterns\n   */\n  private checkCleanBreakout(data: CandlestickData[]): boolean {\n    if (data.length < 30) return false\n    \n    const recent30Days = data.slice(-30)\n    const last5Days = recent30Days.slice(-5)\n    const consolidationPeriod = recent30Days.slice(-30, -5)\n    \n    // Calculate consolidation range\n    const consolidationHigh = Math.max(...consolidationPeriod.map(d => d.high))\n    const consolidationLow = Math.min(...consolidationPeriod.map(d => d.low))\n    const consolidationRange = (consolidationHigh - consolidationLow) / consolidationLow\n    \n    // Check if recent price broke above consolidation with volume\n    const recentHigh = Math.max(...last5Days.map(d => d.high))\n    const recentVolume = last5Days.reduce((sum, d) => sum + d.volume, 0) / last5Days.length\n    const avgVolume = consolidationPeriod.reduce((sum, d) => sum + d.volume, 0) / consolidationPeriod.length\n    \n    // Clean breakout criteria:\n    // 1. Consolidation range < 20% (tight consolidation)\n    // 2. Recent high > consolidation high\n    // 3. Volume expansion on breakout\n    return consolidationRange < 0.20 && \n           recentHigh > consolidationHigh && \n           recentVolume > avgVolume * 1.5\n  }\n\n  /**\n   * Check for volume expansion on breakout days\n   */\n  private checkVolumeExpansion(data: CandlestickData[]): boolean {\n    if (data.length < 20) return false\n    \n    const recent5Days = data.slice(-5)\n    const previous20Days = data.slice(-25, -5)\n    \n    const recentAvgVolume = recent5Days.reduce((sum, d) => sum + d.volume, 0) / recent5Days.length\n    const historicalAvgVolume = previous20Days.reduce((sum, d) => sum + d.volume, 0) / previous20Days.length\n    \n    // Volume expansion if recent volume is 150%+ of historical average\n    return recentAvgVolume > historicalAvgVolume * 1.5\n  }\n\n  /**\n   * Calculate VWAP (Volume Weighted Average Price)\n   */\n  private calculateVWAP(data: CandlestickData[]): number {\n    let totalVolume = 0\n    let totalVolumePrice = 0\n    \n    for (const candle of data) {\n      const typicalPrice = (candle.high + candle.low + candle.close) / 3\n      totalVolumePrice += typicalPrice * candle.volume\n      totalVolume += candle.volume\n    }\n    \n    return totalVolume > 0 ? totalVolumePrice / totalVolume : 0\n  }\n\n  /**\n   * Calculate resistance levels using pivot highs\n   */\n  private calculateResistanceLevels(data: CandlestickData[]): number[] {\n    const resistanceLevels: number[] = []\n    const lookback = 5 // Look for pivots with 5 days on each side\n    \n    for (let i = lookback; i < data.length - lookback; i++) {\n      const current = data[i]\n      let isPivotHigh = true\n      \n      // Check if current high is higher than surrounding highs\n      for (let j = i - lookback; j <= i + lookback; j++) {\n        if (j !== i && data[j].high >= current.high) {\n          isPivotHigh = false\n          break\n        }\n      }\n      \n      if (isPivotHigh) {\n        resistanceLevels.push(current.high)\n      }\n    }\n    \n    // Return top 5 most recent resistance levels\n    return resistanceLevels.slice(-5).sort((a, b) => b - a)\n  }\n\n  /**\n   * Calculate support levels using pivot lows\n   */\n  private calculateSupportLevels(data: CandlestickData[]): number[] {\n    const supportLevels: number[] = []\n    const lookback = 5\n    \n    for (let i = lookback; i < data.length - lookback; i++) {\n      const current = data[i]\n      let isPivotLow = true\n      \n      // Check if current low is lower than surrounding lows\n      for (let j = i - lookback; j <= i + lookback; j++) {\n        if (j !== i && data[j].low <= current.low) {\n          isPivotLow = false\n          break\n        }\n      }\n      \n      if (isPivotLow) {\n        supportLevels.push(current.low)\n      }\n    }\n    \n    // Return top 5 most recent support levels\n    return supportLevels.slice(-5).sort((a, b) => b - a)\n  }\n\n  /**\n   * Calculate overall gate score (0-100)\n   */\n  private calculateGateScore(conditions: {\n    dailyTrendConfirmed: boolean\n    aboveSMA200: boolean\n    aboveEMA8: boolean\n    respectsEMA8: boolean\n    isAtAllTimeHigh: boolean\n    hasCleanBreakout: boolean\n    volumeExpansion: boolean\n  }): number {\n    let score = 0\n    \n    // Required conditions (higher weight)\n    if (conditions.dailyTrendConfirmed) score += 20\n    if (conditions.aboveSMA200) score += 20\n    if (conditions.aboveEMA8) score += 15\n    \n    // Premium conditions (bonus points)\n    if (conditions.respectsEMA8) score += 15\n    if (conditions.isAtAllTimeHigh) score += 15\n    if (conditions.hasCleanBreakout) score += 10\n    if (conditions.volumeExpansion) score += 5\n    \n    return Math.min(100, score)\n  }\n\n  /**\n   * Calculate overall grade based on score\n   */\n  private calculateOverallGrade(score: number): 'A' | 'B' | 'C' | 'D' | 'F' {\n    if (score >= 90) return 'A'\n    if (score >= 80) return 'B'\n    if (score >= 70) return 'C'\n    if (score >= 60) return 'D'\n    return 'F'\n  }\n\n  /**\n   * Batch analyze multiple symbols\n   */\n  async batchAnalyzeTechnicalGates(symbols: string[]): Promise<TechnicalGateAnalysis[]> {\n    const results: TechnicalGateAnalysis[] = []\n    \n    // Process in chunks to avoid API rate limits\n    const chunkSize = 5\n    for (let i = 0; i < symbols.length; i += chunkSize) {\n      const chunk = symbols.slice(i, i + chunkSize)\n      const chunkPromises = chunk.map(symbol => this.analyzeTechnicalGate(symbol))\n      const chunkResults = await Promise.all(chunkPromises)\n      \n      results.push(...chunkResults.filter((result): result is TechnicalGateAnalysis => result !== null))\n      \n      // Small delay between chunks\n      if (i + chunkSize < symbols.length) {\n        await new Promise(resolve => setTimeout(resolve, 1000))\n      }\n    }\n    \n    return results.sort((a, b) => b.gateScore - a.gateScore)\n  }\n}\n", "import { \n  PerfectPickSetup, \n  PreMarketGapScan, \n  TechnicalGateAnalysis, \n  IntradayEntryTrigger,\n  Catalyst,\n  CandlestickData\n} from '@/types/trading'\nimport { PreMarketGapScanner } from './preMarketGapScanner'\nimport { TechnicalGateAnalyzer } from './technicalGateAnalysis'\nimport { CatalystDetectionEngine } from './catalystDetection'\nimport { PolygonAPI } from './polygon'\nimport { FMPAPI } from './fmp'\n\nexport class PerfectPickTradingSystem {\n  private gapScanner: PreMarketGapScanner\n  private technicalAnalyzer: TechnicalGateAnalyzer\n  private catalystEngine: CatalystDetectionEngine\n  private polygonAPI: PolygonAPI\n  private fmpAPI: FMPAPI\n\n  constructor(fmpApiKey?: string, polygonApiKey?: string) {\n    this.gapScanner = new PreMarketGapScanner(fmpApiKey, polygonApiKey)\n    this.technicalAnalyzer = new TechnicalGateAnalyzer(polygonApiKey, fmpApiKey)\n    this.catalystEngine = new CatalystDetectionEngine(fmpApiKey, polygonApiKey)\n    this.polygonAPI = new PolygonAPI(polygonApiKey)\n    this.fmpAPI = new FMPAPI(fmpApiKey)\n  }\n\n  /**\n   * Run complete Perfect-Pick analysis pipeline\n   */\n  async runPerfectPickScan(\n    accountSize: number = 100000,\n    riskPercent: number = 2,\n    customUniverse?: string[]\n  ): Promise<PerfectPickSetup[]> {\n    console.log('🎯 Starting Perfect-Pick Trading System scan...')\n\n    try {\n      // Step 1: Pre-Market Gap Scan\n      console.log('📊 Running pre-market gap scan...')\n      const gapResults = await this.gapScanner.runGapScan(customUniverse)\n      \n      // Filter for basic criteria first\n      const qualifiedGaps = gapResults.filter(gap => \n        gap.gapPercent >= 3.0 && \n        gap.gapPercent <= 15.0 && // Exclude over-extended gaps\n        gap.marketCap >= ********* &&\n        gap.price > 1.0\n      )\n\n      console.log(`✅ Found ${qualifiedGaps.length} qualified gap candidates`)\n\n      if (qualifiedGaps.length === 0) {\n        return []\n      }\n\n      // Step 2: Technical Gate Analysis\n      console.log('🔍 Running technical gate analysis...')\n      const symbols = qualifiedGaps.map(gap => gap.symbol)\n      const technicalAnalyses = await this.technicalAnalyzer.batchAnalyzeTechnicalGates(symbols)\n      \n      // Filter for passing technical gates (Grade B or better)\n      const passingTechnical = technicalAnalyses.filter(analysis => \n        ['A', 'B'].includes(analysis.overallGrade) &&\n        analysis.aboveSMA200 &&\n        analysis.aboveEMA8\n      )\n\n      console.log(`✅ ${passingTechnical.length} stocks passed technical gate`)\n\n      // Step 3: Combine and create Perfect-Pick setups\n      const perfectPickSetups: PerfectPickSetup[] = []\n\n      for (const gapResult of qualifiedGaps) {\n        const technicalAnalysis = passingTechnical.find(t => t.symbol === gapResult.symbol)\n        if (!technicalAnalysis) continue\n\n        const catalyst = gapResult.catalyst\n        if (!catalyst || !this.isValidCatalyst(catalyst)) continue\n\n        // Create Perfect-Pick setup\n        const setup = await this.createPerfectPickSetup(\n          gapResult,\n          technicalAnalysis,\n          catalyst,\n          accountSize,\n          riskPercent\n        )\n\n        if (setup && this.validatePerfectPickSetup(setup)) {\n          perfectPickSetups.push(setup)\n        }\n      }\n\n      // Sort by overall score\n      perfectPickSetups.sort((a, b) => b.overallScore - a.overallScore)\n\n      console.log(`🎯 Generated ${perfectPickSetups.length} Perfect-Pick setups`)\n      \n      return perfectPickSetups\n    } catch (error) {\n      console.error('Error running Perfect-Pick scan:', error)\n      return []\n    }\n  }\n\n  /**\n   * Create a complete Perfect-Pick setup\n   */\n  private async createPerfectPickSetup(\n    gapScan: PreMarketGapScan,\n    technicalGate: TechnicalGateAnalysis,\n    catalyst: Catalyst,\n    accountSize: number,\n    riskPercent: number\n  ): Promise<PerfectPickSetup | null> {\n    try {\n      const symbol = gapScan.symbol\n      const currentPrice = gapScan.price\n      \n      // Calculate risk management\n      const preMarketLow = gapScan.preMarketLow\n      const stopLoss = preMarketLow * 0.99 // Slightly below PML for safety\n      const riskPerShare = currentPrice - stopLoss\n      \n      if (riskPerShare <= 0) {\n        return null // Invalid risk setup\n      }\n\n      const maxRiskAmount = accountSize * (riskPercent / 100)\n      const positionSize = Math.floor(maxRiskAmount / riskPerShare)\n      const maxPositionValue = accountSize * 0.05 // 5% max position size\n      const maxShares = Math.floor(maxPositionValue / currentPrice)\n      \n      const finalPositionSize = Math.min(positionSize, maxShares)\n      const actualRiskAmount = finalPositionSize * riskPerShare\n\n      // Calculate reward targets (minimum 3:1 R/R required)\n      const target3R = currentPrice + (riskPerShare * 3)\n      const target4R = currentPrice + (riskPerShare * 4)\n      const target5R = currentPrice + (riskPerShare * 5)\n      const riskRewardRatio = 3 // Minimum required\n\n      // Check for exclusion reasons\n      const exclusionReasons = this.checkExclusionCriteria(gapScan, technicalGate, catalyst)\n\n      // Validation checks\n      const validationChecks = {\n        hasValidCatalyst: this.isValidCatalyst(catalyst),\n        meetsGapCriteria: gapScan.meetsAllCriteria,\n        passesTechnicalGate: ['A', 'B'].includes(technicalGate.overallGrade),\n        hasEntryTrigger: true, // Will be determined intraday\n        meetsRiskReward: riskRewardRatio >= 3,\n        noExclusionFlags: exclusionReasons.length === 0\n      }\n\n      // Calculate overall score\n      const overallScore = this.calculateOverallScore(gapScan, technicalGate, catalyst, validationChecks)\n      const setupGrade = this.calculateSetupGrade(overallScore)\n\n      const setup: PerfectPickSetup = {\n        symbol,\n        name: gapScan.name,\n        catalyst,\n        gapScan,\n        technicalGate,\n        riskManagement: {\n          entryPrice: currentPrice,\n          stopLoss,\n          stopLossType: 'pre_market_low',\n          riskPerShare,\n          positionSize: finalPositionSize,\n          accountRiskPercent: riskPercent,\n          maxPositionPercent: 5\n        },\n        rewardPlanning: {\n          riskRewardRatio,\n          target3R,\n          target4R,\n          target5R,\n          scaleOutPlan: [\n            { level: 3, percentage: 25 }, // Take 25% at 3R\n            { level: 4, percentage: 25 }, // Take 25% at 4R\n            { level: 5, percentage: 25 }  // Take 25% at 5R, hold 25% for trend\n          ]\n        },\n        overallScore,\n        setupGrade,\n        exclusionReasons,\n        validationChecks,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      }\n\n      return setup\n    } catch (error) {\n      console.error(`Error creating Perfect-Pick setup for ${gapScan.symbol}:`, error)\n      return null\n    }\n  }\n\n  /**\n   * Generate intraday entry trigger\n   */\n  async generateEntryTrigger(symbol: string, preMarketHigh: number): Promise<IntradayEntryTrigger | null> {\n    try {\n      // Get current intraday data\n      const currentQuote = await this.fmpAPI.getStockQuote(symbol)\n      const currentPrice = currentQuote.price\n      \n      // Calculate VWAP (simplified - would need intraday data for accurate VWAP)\n      const vwap = currentPrice * 0.995 // Approximation\n      \n      // Determine entry signal type\n      let entrySignalType: 'pmh_break' | 'vwap_pullback' | 'first_candle_close'\n      let urgency: 'immediate' | 'wait_for_pullback' | 'breakout_confirmation'\n      let conditions: string[] = []\n\n      if (currentPrice > preMarketHigh) {\n        entrySignalType = 'pmh_break'\n        urgency = 'immediate'\n        conditions.push('Clean break above pre-market high')\n      } else if (currentPrice <= vwap && currentPrice > vwap * 0.98) {\n        entrySignalType = 'vwap_pullback'\n        urgency = 'wait_for_pullback'\n        conditions.push('Pullback to VWAP support')\n      } else {\n        entrySignalType = 'first_candle_close'\n        urgency = 'breakout_confirmation'\n        conditions.push('Wait for first 5-min candle close above PMH')\n      }\n\n      // Check volume confirmation (simplified)\n      const volumeConfirmation = currentQuote.volume > (currentQuote.volume || 0) * 1.5\n      const vwapRising = true // Would need historical VWAP data to determine\n      const noMajorResistance = true // Would need to check against resistance levels\n\n      const trigger: IntradayEntryTrigger = {\n        symbol,\n        preMarketHigh,\n        preMarketLow: preMarketHigh * 0.95, // Approximation\n        vwap,\n        entrySignalType,\n        entryPrice: currentPrice,\n        entryTime: new Date().toISOString(),\n        volumeConfirmation,\n        vwapRising,\n        noMajorResistance,\n        triggerValid: volumeConfirmation && vwapRising && noMajorResistance,\n        urgency,\n        conditions\n      }\n\n      return trigger\n    } catch (error) {\n      console.error(`Error generating entry trigger for ${symbol}:`, error)\n      return null\n    }\n  }\n\n  /**\n   * Validate catalyst quality and tier\n   */\n  private isValidCatalyst(catalyst: Catalyst): boolean {\n    // Tier 1 catalysts (highest priority)\n    const tier1Types = [\n      'earnings_beat_guidance',\n      'fda_approval',\n      'drug_trial_results',\n      'contract_win',\n      'partnership',\n      'merger_acquisition'\n    ]\n\n    // Tier 2 catalysts (secondary)\n    const tier2Types = [\n      'analyst_upgrade',\n      'stock_split',\n      'sector_rotation'\n    ]\n\n    const isValidType = tier1Types.includes(catalyst.type) || tier2Types.includes(catalyst.type)\n    const isFresh = catalyst.freshness === 'fresh' || catalyst.freshness === 'moderate'\n    const hasQuality = catalyst.qualityScore >= 6\n    const isVerified = catalyst.verified\n\n    return isValidType && isFresh && hasQuality && isVerified\n  }\n\n  /**\n   * Check for exclusion criteria\n   */\n  private checkExclusionCriteria(\n    gapScan: PreMarketGapScan,\n    technicalGate: TechnicalGateAnalysis,\n    catalyst: Catalyst\n  ): string[] {\n    const exclusions: string[] = []\n\n    // Anti-pattern filters\n    if (!technicalGate.dailyTrendConfirmed) {\n      exclusions.push('Stock not in confirmed daily uptrend')\n    }\n\n    if (!technicalGate.aboveSMA200) {\n      exclusions.push('Stock below 200-day SMA')\n    }\n\n    if (gapScan.gapPercent > 15) {\n      exclusions.push('Gap too extended (>15%)')\n    }\n\n    if (gapScan.averageDailyVolume < 500000) {\n      exclusions.push('Low liquidity (avg daily volume <500K)')\n    }\n\n    if (catalyst.impact === 'bearish') {\n      exclusions.push('Negative catalyst detected')\n    }\n\n    if (catalyst.freshness === 'stale') {\n      exclusions.push('Catalyst is stale (>72 hours old)')\n    }\n\n    return exclusions\n  }\n\n  /**\n   * Calculate overall setup score (0-100)\n   */\n  private calculateOverallScore(\n    gapScan: PreMarketGapScan,\n    technicalGate: TechnicalGateAnalysis,\n    catalyst: Catalyst,\n    validationChecks: any\n  ): number {\n    let score = 0\n\n    // Gap quality (25 points max)\n    score += Math.min(25, gapScan.gapPercent * 2) // 3% gap = 6 points, 10% gap = 20 points\n\n    // Technical gate score (35 points max)\n    score += (technicalGate.gateScore / 100) * 35\n\n    // Catalyst quality (25 points max)\n    score += (catalyst.qualityScore / 10) * 25\n\n    // Validation bonus (15 points max)\n    const validationCount = Object.values(validationChecks).filter(Boolean).length\n    score += (validationCount / Object.keys(validationChecks).length) * 15\n\n    return Math.min(100, Math.round(score))\n  }\n\n  /**\n   * Calculate setup grade\n   */\n  private calculateSetupGrade(score: number): 'A+' | 'A' | 'B+' | 'B' | 'C+' | 'C' | 'D' | 'F' {\n    if (score >= 95) return 'A+'\n    if (score >= 90) return 'A'\n    if (score >= 85) return 'B+'\n    if (score >= 80) return 'B'\n    if (score >= 75) return 'C+'\n    if (score >= 70) return 'C'\n    if (score >= 60) return 'D'\n    return 'F'\n  }\n\n  /**\n   * Validate complete Perfect-Pick setup\n   */\n  private validatePerfectPickSetup(setup: PerfectPickSetup): boolean {\n    const checks = setup.validationChecks\n    \n    // Must pass all critical checks\n    const criticalChecks = [\n      checks.hasValidCatalyst,\n      checks.meetsGapCriteria,\n      checks.passesTechnicalGate,\n      checks.meetsRiskReward,\n      checks.noExclusionFlags\n    ]\n\n    return criticalChecks.every(check => check) && setup.overallScore >= 70\n  }\n\n  /**\n   * Get setup summary statistics\n   */\n  getSetupSummary(setups: PerfectPickSetup[]) {\n    const totalSetups = setups.length\n    const gradeBreakdown = setups.reduce((acc, setup) => {\n      acc[setup.setupGrade] = (acc[setup.setupGrade] || 0) + 1\n      return acc\n    }, {} as Record<string, number>)\n\n    const catalystBreakdown = setups.reduce((acc, setup) => {\n      acc[setup.catalyst.type] = (acc[setup.catalyst.type] || 0) + 1\n      return acc\n    }, {} as Record<string, number>)\n\n    const avgScore = totalSetups > 0 \n      ? setups.reduce((sum, setup) => sum + setup.overallScore, 0) / totalSetups \n      : 0\n\n    const avgGap = totalSetups > 0\n      ? setups.reduce((sum, setup) => sum + setup.gapScan.gapPercent, 0) / totalSetups\n      : 0\n\n    return {\n      totalSetups,\n      avgScore: Math.round(avgScore * 100) / 100,\n      avgGap: Math.round(avgGap * 100) / 100,\n      gradeBreakdown,\n      catalystBreakdown,\n      generatedAt: new Date().toISOString()\n    }\n  }\n\n  /**\n   * Update existing setups with current market data\n   */\n  async updatePerfectPickSetups(setups: PerfectPickSetup[]): Promise<PerfectPickSetup[]> {\n    const updatedSetups: PerfectPickSetup[] = []\n\n    for (const setup of setups) {\n      try {\n        // Get current quote\n        const currentQuote = await this.fmpAPI.getStockQuote(setup.symbol)\n        const currentPrice = currentQuote.price\n\n        // Update entry trigger if needed\n        const entryTrigger = await this.generateEntryTrigger(setup.symbol, setup.gapScan.preMarketHigh)\n\n        // Update the setup\n        const updatedSetup: PerfectPickSetup = {\n          ...setup,\n          entryTrigger,\n          riskManagement: {\n            ...setup.riskManagement,\n            entryPrice: currentPrice\n          },\n          updatedAt: new Date().toISOString()\n        }\n\n        updatedSetups.push(updatedSetup)\n      } catch (error) {\n        console.error(`Error updating setup for ${setup.symbol}:`, error)\n        updatedSetups.push(setup) // Keep original if update fails\n      }\n    }\n\n    return updatedSetups\n  }\n}\n"], "names": [], "mappings": "qLAAA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,CAAA,CAAA,OAAA,IAAA,EAAA,EAAA,CAAA,CAAA,4CCfA,IAAA,EAAA,EAAA,CAAA,CAAA,OEQA,EAAA,EAAA,CAAA,CAAA,ODPA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MAEO,OAAM,EACH,UAAsB,CACtB,MAAc,AAEtB,aAAY,CAAsB,CAAE,CAAkB,CAAE,CACtD,IAAI,CAAC,UAAU,CAAG,IAAI,EAAA,UAAU,CAAC,GACjC,IAAI,CAAC,MAAM,CAAG,IAAI,EAAA,MAAM,CAAC,EAC3B,CAKA,MAAM,qBAAqB,CAAc,CAAyC,CAChF,GAAI,CAEF,IAAM,EAAiB,MAAM,IAAI,CAAC,iBAAiB,CAAC,EAAQ,KAE5D,GAAI,CAAC,GAAkB,EAAe,MAAM,CAAG,IAE7C,CAFkD,MAClD,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,EAAO,yBAAyB,CAAC,EACjE,KAGT,IAAM,EAAe,CAAc,CAAC,EAAe,MAAM,CAAG,EAAE,CAAC,KAAK,CAG9D,EAAS,EAAA,mBAAmB,CAAC,YAAY,CAAC,EAAgB,KAC1D,EAAO,EAAA,mBAAmB,CAAC,YAAY,CAAC,EAAgB,GACxD,EAAO,IAAI,CAAC,aAAa,CAAC,EAAe,KAAK,CAAC,CAAC,KAAK,AAGrD,EAAsB,IAAI,CAAC,OAHwC,UAGvB,CAAC,GAG7C,EAAc,EAAe,CAAM,CAAC,EAAO,MAAM,CAAG,EAAE,CACtD,EAAY,EAAe,CAAI,CAAC,EAAK,MAAM,CAAG,EAAE,CAChD,EAAe,IAAI,CAAC,gBAAgB,CAAC,EAAgB,GAGrD,EAAkB,IAAI,CAAC,gBAAgB,CAAC,GAGxC,EAAmB,IAAI,CAAC,kBAAkB,CAAC,GAG3C,EAAkB,IAAI,CAAC,oBAAoB,CAAC,GAG5C,EAAmB,IAAI,CAAC,yBAAyB,CAAC,GAClD,EAAgB,IAAI,CAAC,sBAAsB,CAAC,GAG5C,EAAY,IAAI,CAAC,kBAAkB,CAAC,CACxC,kCACA,YACA,eACA,kBACA,mBACA,kBACA,CACF,GAEM,EAAe,IAAI,CAAC,qBAAqB,CAAC,GAwBhD,MAtBwC,CAsBjC,OArBL,sBACA,EACA,wBACA,eACA,kBACA,mBACA,kBACA,eACA,YACA,mBACA,EACA,gBACA,mBAAoB,CAClB,OAAQ,CAAM,CAAC,EAAO,MAAM,CAAG,EAAE,CACjC,KAAM,CAAI,CAAC,EAAK,MAAM,CAAG,EAAE,MAC3B,EACA,aAAc,KAAK,GAAG,IAAI,EAAe,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,GAAK,EAAE,IAAI,GACnE,YAAa,KAAK,GAAG,IAAI,EAAe,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,GAAK,EAAE,GAAG,EACnE,CACF,CAGF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,EAAO,CAAC,CAAC,CAAE,GACxD,IACT,CACF,CAKA,MAAc,kBAAkB,CAAc,CAAE,CAAY,CAA8B,CACxF,GAAI,CACF,IAAM,EAAU,IAAI,KACd,EAAY,IAAI,KAGtB,OAFA,EAAU,OAAO,CAAC,EAAU,OAAO,GAAK,GAEjC,MAAM,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAC5C,EACA,EAAU,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CACrC,EAAQ,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CACnC,IACA,MAEJ,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,EAAO,CAAC,CAAC,CAAE,GACxD,EAAE,AACX,CACF,CAKA,kBAA0B,CAAuB,CAAW,CAC1D,GAAI,EAAK,MAAM,CAAG,GAAI,OAAO,EAE7B,IAAM,EAAe,EAAK,KAAK,CAAC,CAAC,IAC3B,EAAc,EAAa,KAAK,CAAC,EAAG,IACpC,EAAa,EAAa,KAAK,CAAC,IAEhC,EAAkB,KAAK,GAAG,IAAI,EAAY,GAAG,CAAC,GAAK,EAAE,IAAI,GACzD,EAAiB,KAAK,GAAG,IAAI,EAAY,GAAG,CAAC,GAAK,EAAE,GAAG,GACvD,EAAiB,KAAK,GAAG,IAAI,EAAW,GAAG,CAAC,GAAK,EAAE,IAAI,GACvD,EAAgB,KAAK,GAAG,IAAI,EAAW,GAAG,CAAC,GAAK,EAAE,GAAG,GAG3D,OAAO,EAAiB,GAAmB,EAAgB,CAC7D,CAKA,iBAAyB,CAAuB,CAAE,CAAc,CAAW,CACzE,GAAI,EAAK,MAAM,CAAG,IAAM,EAAK,MAAM,CAAG,GAAI,MAAO,GAEjD,IAAM,EAAe,EAAK,KAAK,CAAC,CAAC,IAC3B,EAAc,EAAK,KAAK,CAAC,CAAC,IAE5B,EAAe,EAEnB,IAAK,IAAI,EAAI,EAAG,EAAI,EAAa,MAAM,CAAE,IAAK,CAC5C,IAAM,EAAS,CAAY,CAAC,EAAE,CACxB,EAAW,CAAW,CAAC,EAAE,CAG3B,EAAO,GAAG,EAAe,IAAX,EAAiB,CACjC,GAEJ,CAGA,OAAO,EAAe,EAAa,MAAM,EAAI,EAC/C,CAKQ,iBAAiB,CAAuB,CAAW,CAKzD,OAAO,AAJc,CAAI,CAAC,EAAK,MAAM,CAAG,EAAE,CAAC,KAAK,EAIzB,AAAc,IAHjB,KAAK,GAAG,IAAI,EAAK,GAAG,CAAC,GAAK,EAAE,IAAI,EAItD,CAKQ,mBAAmB,CAAuB,CAAW,CAC3D,GAAI,EAAK,MAAM,CAAG,GAAI,MAAO,GAE7B,IAAM,EAAe,EAAK,KAAK,CAAC,CAAC,IAC3B,EAAY,EAAa,KAAK,CAAC,CAAC,GAChC,EAAsB,EAAa,KAAK,CAAC,CAAC,GAAI,CAAC,GAG/C,EAAoB,KAAK,GAAG,IAAI,EAAoB,GAAG,CAAC,GAAK,EAAE,IAAI,GACnE,EAAmB,KAAK,GAAG,IAAI,EAAoB,GAAG,CAAC,GAAK,EAAE,GAAG,GAIjE,EAAa,KAAK,GAAG,IAAI,EAAU,GAAG,CAAC,GAAK,EAAE,IAAI,GAClD,EAAe,EAAU,MAAM,CAAC,CAAC,EAAK,IAAM,EAAM,EAAE,MAAM,CAAE,GAAK,EAAU,MAAM,CACjF,EAAY,EAAoB,MAAM,CAAC,CAAC,EAAK,IAAM,EAAM,EAAE,MAAM,CAAE,GAAK,EAAoB,MAAM,CAMxG,MAAO,CAXqB,EAAoB,CAAA,CAAgB,CAAI,EAWxC,IACrB,EAAa,GACb,EAAe,AAAY,KACpC,CAKQ,qBAAqB,CAAuB,CAAW,CAC7D,GAAI,EAAK,MAAM,CAAG,GAAI,OAAO,EAE7B,IAAM,EAAc,EAAK,KAAK,CAAC,CAAC,GAC1B,EAAiB,EAAK,KAAK,CAAC,CAAC,GAAI,CAAC,GAMxC,OAJwB,AAIjB,EAJ6B,MAAM,CAAC,CAAC,EAAK,IAAM,EAAM,EAAE,MAAM,CAAE,GAAK,EAAY,MAAM,CAIrE,AAAsB,KAHnB,EAAe,MAAM,CAAC,CAAC,EAAK,IAAM,EAAM,EAAE,MAAM,CAAE,GAAK,EAAe,MAAA,AAAM,CAI1G,CAKQ,cAAc,CAAuB,CAAU,CACrD,IAAI,EAAc,EACd,EAAmB,EAEvB,IAAK,IAAM,KAAU,EAEnB,GAFyB,AACH,AACF,GADS,IAAI,CAAG,EAAO,GAAG,CAAG,EAAO,KAAA,AAAK,EAAI,EAC9B,EAAO,MAAM,CAChD,GAAe,EAAO,MAAM,CAG9B,OAAO,EAAc,EAAI,EAAmB,EAAc,CAC5D,CAKQ,0BAA0B,CAAuB,CAAY,CACnE,IAAM,EAA6B,EAAE,CAGrC,IAAK,IAAI,IAAI,AAAU,EAAI,EAAK,MAAM,GAAG,AAAU,IAAK,CACtD,IAAM,EAAU,CAAI,CAAC,EAAE,CACnB,GAAc,EAGlB,IAAK,IAAI,EAAI,IAAc,AAAV,GAAe,EAPjB,EAOqB,AAAU,AAP7B,IAOkC,AACjD,GAAI,IAAM,GAAK,CAAI,CAAC,EAAE,CAAC,IAAI,EAAI,EAAQ,IAAI,CAAE,CAC3C,GAAc,EACd,KACF,AAX0D,CAcxD,GACF,EAAiB,IAAI,CAAC,EAAQ,CADf,GACmB,CAEtC,CAGA,OAAO,EAAiB,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAG,IAAM,EAAI,EACvD,CAKQ,uBAAuB,CAAuB,CAAY,CAChE,IAAM,EAA0B,EAAE,CAGlC,IAAK,IAAI,IAAI,AAAU,EAAI,EAAK,MAAM,GAAG,AAAU,IAAK,CACtD,IAAM,EAAU,CAAI,CAAC,EAAE,CACnB,GAAa,EAGjB,IAAK,IAAI,EAAI,IAAI,AAAU,GAAK,EAPjB,EAOqB,AAAU,IAAK,AACjD,GAAI,IAAM,GAAK,CAAI,CAAC,EAAE,CAAC,GAAG,EAAI,EAAQ,GAAG,CAAE,CACzC,GAAa,EACb,KACF,CAGE,GACF,EAAc,IAAI,CAAC,EADL,AACa,GAAG,CAElC,CAGA,OAAO,EAAc,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAG,IAAM,EAAI,EACpD,CAKQ,mBAAmB,CAQ1B,CAAU,CACT,IAAI,EAAQ,EAaZ,OAVI,EAAW,mBAAmB,GAAE,GAAS,EAAA,EACzC,EAAW,WAAW,GAAE,GAAS,EAAA,EACjC,EAAW,SAAS,GAAE,GAAS,EAAA,EAG/B,EAAW,YAAY,GAAE,GAAS,EAAA,EAClC,EAAW,eAAe,GAAE,GAAS,EAAA,EACrC,EAAW,gBAAgB,GAAE,GAAS,EAAA,EACtC,EAAW,eAAe,GAAE,IAAS,EAElC,KAAK,GAAG,CAAC,IAAK,EACvB,CAKQ,sBAAsB,CAAa,CAA+B,QACxE,AAAI,GAAS,GAAW,CAAP,GACb,GAAS,GAAW,CAAP,GACb,GAAS,GAAW,CAAP,GACb,GAAS,GAAW,CAAP,GACV,GACT,CAKA,MAAM,2BAA2B,CAAiB,CAAoC,CACpF,IAAM,EAAmC,EAAE,CAI3C,IAAK,IAAI,EAAI,EAAG,EAAI,EAAQ,MAAM,CAAE,KAAK,AAAW,CAElD,IAAM,EADQ,AACQ,EADA,KAAK,CAAC,EAAG,IAAI,CACP,GAAG,CAAC,GAAU,IAAI,CAAC,oBAAoB,CAAC,IAC9D,EAAe,MAAM,QAAQ,GAAG,CAAC,GAEvC,EAAQ,IAAI,IAAI,EAAa,MAAM,CAAC,AAAC,GAA4C,AAAW,WAGxF,EATY,EASR,AAAY,EAAQ,MAAM,EAAE,AAClC,MAAM,IAAI,QAAQ,GAAW,WAAW,EAAS,KAErD,CAEA,OAAO,EAAQ,IAAI,CAAC,CAAC,EAAG,IAAM,EAAE,SAAS,CAAG,EAAE,SAAS,CACzD,CACF,CCvVA,IAAA,EAAA,EAAA,CAAA,CAAA,MAIO,OAAM,EACH,UAA+B,CAC/B,iBAAwC,AACxC,eAAuC,AACvC,WAAsB,CACtB,MAAc,AAEtB,aAAY,CAAkB,CAAE,CAAsB,CAAE,CACtD,IAAI,CAAC,UAAU,CAAG,IAAI,EAAA,mBAAmB,CAAC,EAAW,GACrD,IAAI,CAAC,iBAAiB,CAAG,IAAI,EAAsB,EAAe,GAClE,IAAI,CAAC,cAAc,CAAG,IAAI,EAAA,uBAAuB,CAAC,EAAW,GAC7D,IAAI,CAAC,UAAU,CAAG,IAAI,EAAA,UAAU,CAAC,GACjC,IAAI,CAAC,MAAM,CAAG,IAAI,EAAA,MAAM,CAAC,EAC3B,CAKA,MAAM,mBACJ,EAAsB,GAAM,CAC5B,EAAsB,CAAC,CACvB,CAAyB,CACI,CAC7B,QAAQ,GAAG,CAAC,mDAEZ,GAAI,CAEF,QAAQ,GAAG,CAAC,qCAIZ,IAAM,EAAgB,CAHH,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,EAAA,EAGnB,MAAM,CAAC,GACtC,EAAI,UAAU,EAAI,GAClB,EAAI,UAAU,EAAI,IAClB,EAAI,EADsB,OACb,EAAI,KACjB,EAAI,KAAK,CAAG,GAKd,GAFA,CALyD,OAKjD,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAc,MAAM,CAAC,yBAAyB,CAAC,EAEzC,AAAzB,GAA4B,GAAd,MAAM,CACtB,MAAO,EAAE,CAIX,QAAQ,GAAG,CAAC,yCACZ,IAAM,EAAU,EAAc,GAAG,CAAC,GAAO,EAAI,MAAM,EAI7C,EAAmB,CAHC,MAAM,IAAI,CAAC,iBAAiB,CAAC,0BAA0B,CAAC,EAAA,EAGvC,MAAM,CAAC,GAChD,CAAC,IAAK,IAAI,CAAC,QAAQ,CAAC,EAAS,YAAY,GACzC,EAAS,WAAW,EACpB,EAAS,SAAS,EAGpB,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,EAAiB,MAAM,CAAC,6BAA6B,CAAC,EAGvE,IAAM,EAAwC,EAAE,CAEhD,IAAK,IAAM,KAAa,EAAe,CACrC,IAAM,EAAoB,EAAiB,IAAI,CAAC,GAAK,EAAE,MAAM,GAAK,EAAU,MAAM,EAClF,GAAI,CAAC,EAAmB,SAExB,IAAM,EAAW,EAAU,QAAQ,CACnC,GAAI,CAAC,GAAY,CAAC,IAAI,CAAC,eAAe,CAAC,GAAW,SAGlD,IAAM,EAAQ,MAAM,IAAI,CAAC,sBAAsB,CAC7C,EACA,EACA,EACA,EACA,GAGE,GAAS,IAAI,CAAC,wBAAwB,CAAC,IACzC,EAAkB,EAD+B,EAC3B,CAAC,EAE3B,CAOA,OAJA,EAAkB,IAAI,CAAC,CAAC,EAAG,IAAM,EAAE,YAAY,CAAG,EAAE,YAAY,EAEhE,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,EAAkB,MAAM,CAAC,oBAAoB,CAAC,EAEnE,CACT,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,mCAAoC,GAC3C,EAAE,AACX,CACF,CAKA,MAAc,uBACZ,CAAyB,CACzB,CAAoC,CACpC,CAAkB,CAClB,CAAmB,CACnB,CAAmB,CACe,CAClC,GAAI,CACF,IAAM,EAAS,EAAQ,MAAM,CACvB,EAAe,EAAQ,KAAK,CAG5B,EAAe,EAAQ,YAAY,CACnC,EAA0B,IAAf,CAAoB,CAC/B,EAAe,EAAe,EAEpC,GAAI,GAAgB,EAClB,CADqB,MACd,KAIT,AAJc,IAIR,CAR+D,CAQhD,KAAK,KAAK,CADM,AACL,EADmB,EAHhB,CAGmB,CAAhC,EAC0B,GAE1C,EAAY,KAAK,EAHa,GAGR,CAAC,AADU,IAAd,CAAmB,CACI,GAE1C,EAAoB,KAAK,GAAG,CAAC,EAAc,GAU3C,EAAmB,CAb0C,GAatC,CAAC,sBAAsB,CAAC,EAAS,EAAe,GAGvE,EAAmB,CACvB,iBAAkB,IAAI,CAAC,eAAe,CAAC,GACvC,iBAAkB,EAAQ,gBAAgB,CAC1C,oBAAqB,CAAC,IAAK,IAAI,CAAC,QAAQ,CAAC,EAAc,YAAY,EACnE,iBAAiB,EACjB,iBAAiB,EACjB,iBADoC,AACU,IAA5B,EAAiB,MAAM,AAC3C,EAGM,EAAe,IAAI,CAAC,qBAAqB,CAAC,EAAS,EAAe,EAAU,GAC5E,EAAa,IAAI,CAAC,mBAAmB,CAAC,GAoC5C,MAlCgC,CAkCzB,OAjCL,EACA,KAAM,EAAQ,IAAI,UAClB,UACA,gBACA,EACA,eAAgB,CACd,WAAY,WACZ,EACA,aAAc,8BACd,EACA,aAAc,EACd,mBAAoB,EACpB,mBAAoB,CACtB,EACA,eAAgB,CACd,gBAnCoB,EAAE,AAoCtB,SAvCa,EAA+B,EAAf,EAwC7B,IArCyC,KAF5B,EAA+B,EAAf,EAwC7B,SAvCa,EAA+B,EAAf,EAwC7B,aAAc,CACZ,CAAE,MAAO,EAAG,WAAY,EAAG,EAC3B,CAAE,MAAO,EAAG,WAAY,EAAG,EAC3B,CAAE,MAAO,EAAG,WAAY,EAAG,EAAG,AAC/B,AACH,eACA,aACA,SAJuE,UAKvE,mBACA,EACA,UAAW,IAAI,OAAO,WAAW,GACjC,UAAW,IAAI,OAAO,WAAW,EACnC,CAGF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,CAAC,sCAAsC,EAAE,EAAQ,MAAM,CAAC,CAAC,CAAC,CAAE,GACnE,IACT,CACF,CAKA,MAAM,qBAAqB,CAAc,CAAE,CAAqB,CAAwC,CACtG,GAAI,CAEF,IAOI,EACA,EARE,EAAe,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,GAC/C,EAAe,EAAa,KAAK,CAGjC,EAAsB,KAAf,CAAqB,CAK9B,EAAuB,EAAE,CAEzB,EAAe,GACjB,EAAkB,GAR8B,OAOhB,EAEhC,EAAU,YACV,EAAW,IAAI,CAAC,sCACP,GAAgB,GAAQ,EAAsB,IAAP,EAAa,CAC7D,EAAkB,gBAClB,EAAU,oBACV,EAAW,IAAI,CAAC,8BAEhB,EAAkB,qBAClB,EAAU,wBACV,EAAW,IAAI,CAAC,gDAIlB,IAAM,EAAqB,EAAa,MAAM,CAAgC,AAA7B,KAAC,EAAa,MAAM,GAAI,CAAC,CAoB1E,MAhBsC,CAgB/B,OAfL,gBACA,EACA,aAAc,AAAgB,WAC9B,kBACA,EACA,WAAY,EACZ,UAAW,IAAI,OAAO,WAAW,sBACjC,EACA,YAZiB,EAajB,GAbsB,kBActB,aAAc,GAbU,KAAK,MAc7B,EAfqE,MAcjC,KAEpC,CACF,CAGF,CAAE,MANoD,AAM7C,EAAO,CAEd,OADA,QAAQ,EApBuE,GAoBlE,CAAC,CAAC,mCAAmC,EAAE,EAAO,CAAC,CAAC,CAAE,GACxD,IACT,CACF,CAKQ,gBAAgB,CAAkB,CAAW,CAkBnD,IAAM,EAhBa,AAgBC,CAflB,yBACA,eACA,qBACA,eACA,cACA,qBACD,CAS8B,QAAQ,CAAC,EAAS,IAAI,GAAK,AANvC,CACjB,kBACA,cACA,kBACD,CAEoE,QAAQ,CAAC,EAAS,IAAI,EACrF,EAAU,AAAuB,YAAd,SAAS,EAAuC,aAAvB,EAAS,SAAS,CAC9D,EAAa,EAAS,YAAY,EAAI,EACtC,EAAa,EAAS,QAAQ,CAEpC,OAAO,GAAe,GAAW,GAAc,CACjD,CAKQ,uBACN,CAAyB,CACzB,CAAoC,CACpC,CAAkB,CACR,CACV,IAAM,EAAuB,EAAE,CA2B/B,OAxBI,AAAC,EAAc,mBAAmB,EAAE,AACtC,EAAW,IAAI,CAAC,wCAGd,AAAC,EAAc,WAAW,EAAE,AAC9B,EAAW,IAAI,CAAC,2BAGd,EAAQ,UAAU,CAAG,IAAI,AAC3B,EAAW,IAAI,CAAC,2BAGd,EAAQ,kBAAkB,CAAG,KAC/B,EAAW,CAD4B,GACxB,CAAC,0CAGM,WAAW,CAA/B,EAAS,MAAM,EACjB,EAAW,IAAI,CAAC,8BAGS,SAAS,CAAhC,EAAS,SAAS,EACpB,EAAW,IAAI,CAAC,qCAGX,CACT,CAKA,sBACE,CAAyB,CACzB,CAAoC,CACpC,CAAkB,CAClB,CAAqB,CACb,CACR,IAAI,EAeJ,OAAO,KAAK,GAAG,CAAC,IAAK,KAAK,KAAK,CAF/B,AAEgC,EAfpB,EAGH,KAAK,GAAG,CAAC,GAAyB,EAArB,EAAwB,AAAhB,UAAU,EAG/B,EAAe,SAAS,CAAG,IAAO,GAGjC,EAAS,QANoE,IAMxD,CAAG,GAAM,GAI9B,AADc,OAAO,MAAM,CAAC,GAAkB,MAAM,CAAC,SAAS,MAAM,CAClD,OAAO,IAAI,CAAC,GAAkB,MAAM,CAAI,IAGtE,CAKQ,oBAAoB,CAAa,CAAoD,QACvF,AAAJ,GAAa,GAAW,CAAP,IACb,GAAS,GAAW,CAAP,GACb,GAAS,GAAW,CAAP,IACb,GAAS,GAAW,CAAP,GACb,GAAS,GAAW,CAAP,IACb,GAAS,GAAW,CAAP,GACb,GAAS,GAAW,CAAP,GACV,GACT,CAKQ,yBAAyB,CAAuB,CAAW,CACjE,IAAM,EAAS,EAAM,gBAAgB,CAWrC,MARuB,AAQhB,CAPL,EAAO,gBAAgB,CACvB,EAAO,gBAAgB,CACvB,EAAO,mBAAmB,CAC1B,EAAO,eAAe,CACtB,EAAO,gBAAgB,CACxB,CAEqB,KAAK,CAAC,GAAS,IAAU,EAAM,YAAY,EAAI,EACvE,CAKA,gBAAgB,CAA0B,CAAE,CAC1C,IAAM,EAAc,EAAO,MAAM,CAC3B,EAAiB,EAAO,MAAM,CAAC,CAAC,EAAK,KACzC,CAAG,CAAC,EAAM,UAAU,CAAC,CAAG,CAAC,CAAG,CAAC,EAAM,UAAU,CAAC,GAAI,CAAC,CAAI,EAChD,GACN,CAAC,GAEE,EAAoB,EAAO,MAAM,CAAC,CAAC,EAAK,KAC5C,CAAG,CAAC,EAAM,QAAQ,CAAC,IAAI,CAAC,CAAG,CAAC,CAAG,CAAC,EAAM,QAAQ,CAAC,IAAI,CAAC,GAAI,CAAC,CAAI,EACtD,GACN,CAAC,GAEE,EAAW,EAAc,EAC3B,EAAO,MAAM,CAAC,CAAC,EAAK,IAAU,EAAM,EAAM,YAAY,CAAE,GAAK,EAC7D,EAEE,EAAS,EAAc,EACzB,EAAO,MAAM,CAAC,CAAC,EAAK,IAAU,EAAM,EAAM,OAAO,CAAC,UAAU,CAAE,GAAK,EACnE,EAEJ,MAAO,aACL,EACA,SAAU,KAAK,KAAK,CAAY,IAAX,GAAkB,IACvC,OAAQ,KAAK,KAAK,CAAU,IAAT,GAAgB,mBACnC,oBACA,EACA,YAAa,IAAI,OAAO,WAAW,EACrC,CACF,CAKA,MAAM,wBAAwB,CAA0B,CAA+B,CACrF,IAAM,EAAoC,EAAE,CAE5C,IAAK,IAAM,KAAS,EAClB,GAAI,CAGF,CAJwB,GAIlB,EAAe,CADA,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,EAAM,OAAM,EAC/B,KAAK,CAGjC,EAAe,MAAM,IAAI,CAAC,oBAAoB,CAAC,EAAM,MAAM,CAAE,EAAM,OAAO,CAAC,aAAa,EAGxF,EAAiC,CACrC,GAAG,CAAK,cACR,EACA,eAAgB,CACd,GAAG,EAAM,cAAc,CACvB,WAAY,CACd,EACA,UAAW,IAAI,OAAO,WAAW,EACnC,EAEA,EAAc,IAAI,CAAC,EACrB,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,EAAM,MAAM,CAAC,CAAC,CAAC,CAAE,GAC3D,EAAc,IAAI,CAAC,EACrB,CAGF,KAJ8B,EAIvB,CACT,CACF,CFrcO,eAAe,EAAI,CAAoB,EAC5C,GAAI,CACF,GAAM,AE6bwD,cF7btD,CAAY,CAAE,CAAG,IAAI,IAAI,EAAQ,GAAG,EAEtC,EAAc,SAAS,EAAa,GAAG,CAAC,gBAAkB,UAC1D,EAAc,WAAW,EAAa,GAAG,CAAC,gBAAkB,KAC5D,EAAiB,EAAa,GAAG,CAAC,aAAa,MAAM,KAAK,OAAO,SACjE,EAAQ,SAAS,EAAa,GAAG,CAAC,UAAY,MAEpD,QAAQ,GAAG,CAAC,0CAA2C,aACrD,cACA,EACA,eAAgB,GAAgB,QAAU,gBAC1C,CACF,GAGA,IAAM,EAAoB,IAAI,EAC5B,QAAQ,GAAG,CAAC,WAAW,CACvB,QAAQ,GAAG,CAAC,eAAe,EAWvB,EAAgB,CAPP,MAAM,EAAkB,kBAAkB,CACvD,EACA,EACA,EAAA,EAI2B,KAAK,CAAC,EAAG,GAGhC,EAAU,EAAkB,eAAe,CAAC,GAE5C,EAAW,CACf,SAAS,EACT,KAAM,CACJ,OAAQ,EACR,UACA,WAAY,aACV,cACA,EACA,aAAc,GAAgB,QAAU,gBACxC,CACF,EACA,UAAW,IAAI,OAAO,WAAW,EACnC,CACF,EAEA,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,EAC3B,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,6BAA8B,GACrC,EAAA,YAAY,CAAC,IAAI,CACtB,CACE,QAAS,GACT,MAAO,kCACP,QAAS,aAAiB,MAAQ,EAAM,OAAO,CAAG,eACpD,EACA,CAAE,OAAQ,GAAI,EAElB,CACF,CAEO,eAAe,EAAK,CAAoB,EAC7C,GAAI,CAEF,GAAM,QAAE,CAAM,MAAE,CAAI,CAAE,CADT,EACY,IADN,EAAQ,IAAI,GAGzB,EAAoB,IAAI,EAC5B,QAAQ,GAAG,CAAC,WAAW,CACvB,QAAQ,GAAG,CAAC,eAAe,EAG7B,OAAQ,GACN,IAAK,gBACH,IAAM,EAAgB,MAAM,EAAkB,uBAAuB,CAAC,EAAK,MAAM,EACjF,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,CACvB,SAAS,EACT,KAAM,CAAE,OAAQ,CAAc,CAChC,EAEF,KAAK,yBACH,IAAM,EAAe,MAAM,EAAkB,oBAAoB,CAC/D,EAAK,MAAM,CACX,EAAK,aAAa,EAEpB,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,CACvB,SAAS,EACT,KAAM,cAAE,CAAa,CACvB,EAEF,SACE,OAAO,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,SAAS,EAAO,MAAO,gBAAiB,EAC1C,CAAE,OAAQ,GAAI,EAEpB,CACF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,kCAAmC,GAC1C,EAAA,YAAY,CAAC,IAAI,CACtB,CACE,SAAS,EACT,MAAO,yCACP,QAAS,aAAiB,MAAQ,EAAM,OAAO,CAAG,eACpD,EACA,CAAE,OAAQ,GAAI,EAElB,CACF,CDhGA,IAAA,EAAA,EAAA,CAAA,CAAA,OAIA,IAAM,EAAc,IAAI,EAAA,mBAAmB,CAAC,CACxC,WAAY,CACR,KAAM,EAAA,SAAS,CAAC,SAAS,CACzB,KAAM,kCACN,SAAU,4BACV,SAAU,QACV,WAAY,EAChB,EACA,QAAS,CAAA,OACT,IADiD,eACc,CAA3C,EACpB,iBAAkB,sEAClB,iBAZqB,GAarB,SAAA,CACJ,GAIM,kBAAE,CAAgB,sBAAE,CAAoB,aAAE,CAAW,CAAE,CAAG,EAChE,SAAS,IACL,MAAO,CAAA,EAAA,EAAA,UAAA,AAAW,EAAC,kBACf,EACA,sBACJ,EACJ,CAEO,eAAe,EAAQ,CAAG,CAAE,CAAG,CAAE,CAAG,EACvC,IAAI,EACJ,IAAI,EAAU,kCAKV,EAAU,EAAQ,OAAO,CAAC,WAAY,KAAO,IAMjD,IAAM,EAAgB,MAAM,EAAY,OAAO,CAAC,EAAK,EAAK,SACtD,EACA,mBAHE,CAAA,CAIN,GACA,GAAI,CAAC,EAID,OAHA,EAAI,IADY,MACF,CAAG,IACjB,EAAI,GAAG,CAAC,eACS,MAAjB,CAAwB,CAApB,IAAyB,KAAhB,EAAoB,EAAI,SAAS,CAAC,IAAI,CAAC,EAAK,QAAQ,OAAO,IACjE,KAEX,GAAM,SAAE,CAAO,QAAE,CAAM,YAAE,CAAU,aAAE,CAAW,mBAAE,CAAiB,qBAAE,CAAmB,CAAE,sBAAoB,yBAAE,CAAuB,kBAAE,CAAgB,CAAE,CAAG,EACxJ,EAAoB,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,GACvC,GAAQ,CAAQ,GAAkB,aAAa,CAAC,EAAkB,EAAI,EAAkB,MAAM,CAAC,EAAA,AAAiB,EACpH,GAAI,GAAS,CAAC,EAAa,CACvB,IAAM,GAAgB,CAAQ,EAAkB,MAAM,CAAC,EAAiB,CAClE,EAAgB,EAAkB,aAAa,CAAC,EAAkB,CACxE,GAAI,IAC+B,IAA3B,EAAc,KADH,GACW,EAAc,CAAC,EACrC,MAAM,IAAI,EAAA,CAD0C,cAC3B,AAGrC,CACA,IAAI,EAAW,MACX,GAAU,EAAY,IAAb,CAAkB,EAAK,EAAD,EAG/B,EAAW,AAAa,OAHqB,KAC7C,EAAW,CAAA,EAEwB,IAAM,CAAA,EAE7C,IAAM,EACgB,KAAtB,EAAY,CAAkB,IAAb,EAEjB,CAAC,EAKK,EAAe,GAAS,CAAC,EACzB,EAAS,EAAI,MAAM,EAAI,MACvB,EAAS,CAAA,EAAA,EAAA,SAAA,AAAS,IAClB,EAAa,EAAO,UAVyE,QAUvD,GACtC,EAAU,CACZ,SACA,oBACA,WAAY,CACR,aAAc,CACV,iBAAiB,CAAQ,EAAW,YAAY,CAAC,eAAe,CAChE,gBAAgB,CAAQ,EAAW,YAAY,CAAC,cAAc,AAClE,EACA,0BACA,iBAAkB,CAAA,EAAA,EAAA,cAAc,AAAd,EAAe,EAAK,oBACtC,kBAAmB,AAAwD,OAAvD,EAA2B,EAAW,YAAA,AAAY,EAAY,KAAK,EAAI,EAAyB,SAAS,cAC7H,EACA,UAAW,EAAI,SAAS,CACxB,QAAU,AAAD,IACL,EAAI,EAAE,CAAC,QAAS,EACpB,EACA,sBAAkB,EAClB,8BAA+B,CAAC,EAAO,EAAU,IAAe,EAAY,cAAc,CAAC,EAAK,EAAO,EAAc,EACzH,EACA,cAAe,SACX,CACJ,CACJ,EACM,EAAc,IAAI,EAAA,eAAe,CAAC,GAClC,EAAc,IAAI,EAAA,gBAAgB,CAAC,GACnC,EAAU,EAAA,kBAAkB,CAAC,mBAAmB,CAAC,EAAa,CAAA,EAAA,EAAA,sBAAA,AAAsB,EAAC,IAC3F,GAAI,CACA,IAAM,EAAoB,MAAO,GACtB,EAAY,MAAM,CAAC,EAAS,GAAS,OAAO,CAAC,KAChD,GAAI,CAAC,EAAM,OACX,EAAK,aAAa,CAAC,CACf,mBAAoB,EAAI,UAAU,CAClC,YAAY,CAChB,GACA,IAAM,EAAqB,EAAO,qBAAqB,GAEvD,GAAI,CAAC,EACD,OAEJ,GAAI,EAAmB,GAAG,CAAC,EAHF,kBAGwB,EAAA,cAAc,CAAC,aAAa,CAAE,YAC3E,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,EAAmB,GAAG,CAAC,kBAAkB,qEAAqE,CAAC,EAG9J,IAAM,EAAQ,EAAmB,GAAG,CAAC,cACrC,GAAI,EAAO,CACP,IAAM,EAAO,CAAA,EAAG,EAAO,CAAC,EAAE,EAAA,CAAO,CACjC,EAAK,aAAa,CAAC,CACf,aAAc,EACd,aAAc,EACd,iBAAkB,CACtB,GACA,EAAK,UAAU,CAAC,EACpB,MACI,CADG,CACE,UAAU,CAAC,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAE9C,GAEE,EAAiB,MAAO,QACtB,EA0FI,EAzFR,IAAM,EAAoB,MAAO,oBAAE,CAAkB,CAAE,IACnD,GAAI,CACA,GAAI,CAAC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GAAwB,GAA2B,CAAC,EAK3F,OAJA,EAAI,SAD2G,CACjG,CAAG,IAEjB,EAAI,SAAS,CAAC,iBAAkB,eAChC,EAAI,GAAG,CAAC,gCACD,KAEX,IAAM,EAAW,MAAM,EAAkB,GACzC,EAAI,YAAY,CAAG,EAAQ,UAAU,CAAC,YAAY,CAClD,IAAI,EAAmB,EAAQ,UAAU,CAAC,gBAAgB,CAGtD,GACI,EAAI,SAAS,EAAE,CACf,CAFc,CAEV,SAAS,CAAC,GACd,OAAmB,GAG3B,IAAM,EAAY,EAAQ,UAAU,CAAC,aAAa,CAGlD,IAAI,EA6BA,OADA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,EAAU,EAAQ,UAAU,CAAC,gBAAgB,EACnF,IA7BA,EACP,IAAM,EAAO,MAAM,EAAS,IAAI,GAE1B,EAAU,CAAA,EAAA,EAAA,yBAAA,AAAyB,EAAC,EAAS,OAAO,EACtD,IACA,CAAO,CAAC,EAAA,GADG,mBACmB,CAAC,CAAG,CAAA,EAElC,CAAC,CAAO,CAAC,eAAe,EAAI,EAAK,IAAI,EAAE,CACvC,CAAO,CAAC,eAAe,CAAG,EAAK,IAAA,AAAI,EAEvC,IAAM,EAAa,KAAkD,IAA3C,EAAQ,UAAU,CAAC,mBAAmB,IAAoB,EAAQ,UAAU,CAAC,mBAAmB,EAAI,EAAA,cAAA,AAAc,GAAW,AAAR,EAAgB,UAAU,CAAC,mBAAmB,CACvL,EAAS,KAA8C,IAAvC,EAAQ,UAAU,CAAC,eAAe,EAAoB,EAAQ,UAAU,CAAC,eAAe,EAAI,EAAA,cAAc,MAAG,EAAY,EAAQ,UAAU,CAAC,eAAe,CAcjL,MAZmB,CACf,AAWG,MAXI,CACH,KAAM,EAAA,eAAe,CAAC,SAAS,CAC/B,OAAQ,EAAS,MAAM,CACvB,KAAM,OAAO,IAAI,CAAC,MAAM,EAAK,WAAW,YACxC,CACJ,EACA,aAAc,YACV,SACA,CACJ,CACJ,CAEJ,CAKJ,CAAE,KALS,CAKF,EAAK,CAcV,MAX0B,MAAtB,EAA6B,KAAK,EAAI,EAAmB,OAAO,AAAP,EAAS,CAClE,MAAM,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,cAClC,uBACA,CACJ,EACJ,EAAG,GAED,CACV,CACJ,EACM,EAAa,MAAM,EAAY,cAAc,CAAC,KAChD,aACA,WACA,EACA,UAAW,EAAA,SAAS,CAAC,SAAS,CAC9B,YAAY,oBACZ,EACA,kBAAmB,GACnB,+CACA,oBACA,EACA,UAAW,EAAI,SAAS,AAC5B,GAEA,GAAI,CAAC,EACD,KADQ,EACD,KAEX,GAAI,CAAe,MAAd,CAAqB,EAAS,AAA0C,GAA9C,IAAK,EAAoB,EAAW,KAAA,AAAK,EAAY,KAAK,EAAI,EAAkB,IAAI,IAAM,EAAA,eAAe,CAAC,SAAS,CAE9I,CAFgJ,KAE1I,OAAO,cAAc,CAAC,AAAI,MAAM,CAAC,kDAAkD,EAAgB,MAAd,CAAqB,EAAS,AAA2C,GAA/C,IAAK,EAAqB,EAAW,KAAA,AAAK,EAAY,KAAK,EAAI,EAAmB,IAAI,CAAA,CAAE,EAAG,oBAAqB,CACjO,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,EAEA,CAAC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBACrB,AADqC,EACjC,SAAS,CAAC,iBAAkB,EAAuB,cAAgB,EAAW,MAAM,CAAG,OAAS,EAAW,OAAO,CAAG,QAAU,OAGnI,GACA,EAAI,QADS,CACA,CAAC,gBAAiB,2DAEnC,IAAM,EAAU,CAAA,EAAA,EAAA,2BAAA,AAA2B,EAAC,EAAW,KAAK,CAAC,OAAO,EAapE,MAZM,AAAF,CAAE,AAAD,EAAC,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GACxC,EAD6C,AACrC,GADwC,GAClC,CAAC,EAAA,sBAAsB,GAIrC,EAAW,YAAY,EAAK,EAAI,AAAL,SAAc,CAAC,kBAAqB,EAAD,AAAS,GAAG,CAAC,kBAAkB,AAC7F,EAAQ,GAAG,CAAC,gBAAiB,CAAA,EAAA,EAAA,qBAAqB,AAArB,EAAsB,EAAW,YAAY,GAE9E,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,EAAW,KAAK,CAAC,IAAI,CAAE,SAC7E,EACA,OAAQ,EAAW,KAAK,CAAC,MAAM,EAAI,GACvC,IACO,IACX,EAGI,EACA,MAAM,EAAe,EADT,CAGZ,MAAM,EAAO,qBAAqB,CAAC,EAAI,OAAO,CAAE,IAAI,EAAO,KAAK,CAAC,EAAA,cAAc,CAAC,aAAa,CAAE,CACvF,SAAU,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAChC,KAAM,EAAA,QAAQ,CAAC,MAAM,CACrB,WAAY,CACR,cAAe,EACf,cAAe,EAAI,GAAG,AAC1B,CACJ,EAAG,GAEf,CAAE,MAAO,EAAK,CAcV,GAbI,AAAE,CAAD,YAAgB,EAAA,eAAe,EAChC,CADmC,KAC7B,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAmB,AAAnB,EAAoB,cAClC,uBACA,CACJ,EACJ,GAIA,EAAO,MAAM,EAKjB,OAHA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,KAAM,CAC5D,OAAQ,GACZ,IACO,IACX,CACJ,EAEA,qCAAqC", "ignoreList": [0]}