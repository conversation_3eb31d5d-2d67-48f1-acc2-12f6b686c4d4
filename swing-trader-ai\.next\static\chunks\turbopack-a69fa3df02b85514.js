(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,{otherChunks:["static/chunks/6b3ff31a45952c31.js","static/chunks/89a15c563baa1b55.js","static/chunks/554dc80029a3c8a0.js"],runtimeModuleIds:[67463]}]),(()=>{let e;if(!Array.isArray(globalThis.TURBOPACK))return;let t="/_next/",r=new WeakMap;function n(e,t){this.m=e,this.e=t}let o=n.prototype,i=Object.prototype.hasOwnProperty,l="undefined"!=typeof Symbol&&Symbol.toStringTag;function s(e,t,r){i.call(e,t)||Object.defineProperty(e,t,r)}function u(e,t){let r=e[t];return r||(r=a(t),e[t]=r),r}function a(e){return{exports:{},error:void 0,id:e,namespaceObject:void 0}}function c(e,t){s(e,"__esModule",{value:!0}),l&&s(e,l,{value:"Module"});let r=0;for(;r<t.length;){let n=t[r++],o=t[r++];"function"==typeof t[r]?s(e,n,{get:o,set:t[r++],enumerable:!0}):s(e,n,{get:o,enumerable:!0})}Object.seal(e)}o.s=function(e,t){let r,n;null!=t?n=(r=u(this.c,t)).exports:(r=this.m,n=this.e),r.namespaceObject=n,c(n,e)},o.j=function(e,t){var n,o;let l,s,a;null!=t?s=(l=u(this.c,t)).exports:(l=this.m,s=this.e);let c=(n=l,o=s,(a=r.get(n))||(r.set(n,a=[]),n.exports=n.namespaceObject=new Proxy(o,{get(e,t){if(i.call(e,t)||"default"===t||"__esModule"===t)return Reflect.get(e,t);for(let e of a){let r=Reflect.get(e,t);if(void 0!==r)return r}},ownKeys(e){let t=Reflect.ownKeys(e);for(let e of a)for(let r of Reflect.ownKeys(e))"default"===r||t.includes(r)||t.push(r);return t}})),a);"object"==typeof e&&null!==e&&c.push(e)},o.v=function(e,t){(null!=t?u(this.c,t):this.m).exports=e},o.n=function(e,t){let r;(r=null!=t?u(this.c,t):this.m).exports=r.namespaceObject=e};let f=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,p=[null,f({}),f([]),f(f)];function h(e,t,r){let n=[],o=-1;for(let t=e;("object"==typeof t||"function"==typeof t)&&!p.includes(t);t=f(t))for(let r of Object.getOwnPropertyNames(t))n.push(r,function(e,t){return()=>e[t]}(e,r)),-1===o&&"default"===r&&(o=n.length-1);return r&&o>=0||(o>=0?n[o]=()=>e:n.push("default",()=>e)),c(t,n),t}function d(){let e,t;return{promise:new Promise((r,n)=>{t=n,e=r}),resolve:e,reject:t}}o.i=function(e){let t=M(e,this.m);if(t.namespaceObject)return t.namespaceObject;let r=t.exports;return t.namespaceObject=h(r,"function"==typeof r?function(...e){return r.apply(this,e)}:Object.create(null),r&&r.__esModule)},o.A=function(e){return this.r(e)(this.i.bind(this))},o.t="function"==typeof require?require:function(){throw Error("Unexpected use of runtime require")},o.r=function(e){return M(e,this.m).exports},o.f=function(e){function t(t){if(i.call(e,t))return e[t].module();let r=Error(`Cannot find module '${t}'`);throw r.code="MODULE_NOT_FOUND",r}return t.keys=()=>Object.keys(e),t.resolve=t=>{if(i.call(e,t))return e[t].id();let r=Error(`Cannot find module '${t}'`);throw r.code="MODULE_NOT_FOUND",r},t.import=async e=>await t(e),t};let m=Symbol("turbopack queues"),b=Symbol("turbopack exports"),y=Symbol("turbopack error");function O(e){e&&1!==e.status&&(e.status=1,e.forEach(e=>e.queueCount--),e.forEach(e=>e.queueCount--?e.queueCount++:e()))}o.a=function(e,t){let r=this.m,n=t?Object.assign([],{status:-1}):void 0,o=new Set,{resolve:i,reject:l,promise:s}=d(),u=Object.assign(s,{[b]:r.exports,[m]:e=>{n&&e(n),o.forEach(e),u.catch(()=>{})}}),a={get:()=>u,set(e){e!==u&&(u[b]=e)}};Object.defineProperty(r,"exports",a),Object.defineProperty(r,"namespaceObject",a),e(function(e){let t=e.map(e=>{if(null!==e&&"object"==typeof e){if(m in e)return e;if(null!=e&&"object"==typeof e&&"then"in e&&"function"==typeof e.then){let t=Object.assign([],{status:0}),r={[b]:{},[m]:e=>e(t)};return e.then(e=>{r[b]=e,O(t)},e=>{r[y]=e,O(t)}),r}}return{[b]:e,[m]:()=>{}}}),r=()=>t.map(e=>{if(e[y])throw e[y];return e[b]}),{promise:i,resolve:l}=d(),s=Object.assign(()=>l(r),{queueCount:0});function u(e){e!==n&&!o.has(e)&&(o.add(e),e&&0===e.status&&(s.queueCount++,e.push(s)))}return t.map(e=>e[m](u)),s.queueCount?i:r()},function(e){e?l(u[y]=e):i(u[b]),O(n)}),n&&-1===n.status&&(n.status=0)};let g=function(e){let t=new URL(e,"x:/"),r={};for(let e in t)r[e]=t[e];for(let t in r.href=e,r.pathname=e.replace(/[?#].*/,""),r.origin=r.protocol="",r.toString=r.toJSON=(...t)=>e,r)Object.defineProperty(this,t,{enumerable:!0,configurable:!0,value:r[t]})};function w(e,t){throw Error(`Invariant: ${t(e)}`)}g.prototype=URL.prototype,o.U=g,o.z=function(e){throw Error("dynamic usage of require is not supported")},o.g=globalThis;let R=n.prototype;var j=function(e){return e[e.Runtime=0]="Runtime",e[e.Parent=1]="Parent",e[e.Update=2]="Update",e}(j||{});let C=new Map;o.M=C;let _=new Map,U=new Map;async function k(e,t,r){let n;if("string"==typeof r)return $(e,t,T(r));let o=r.included||[],i=o.map(e=>!!C.has(e)||_.get(e));if(i.length>0&&i.every(e=>e))return void await Promise.all(i);let l=r.moduleChunks||[],s=l.map(e=>U.get(e)).filter(e=>e);if(s.length>0){if(s.length===l.length)return void await Promise.all(s);let r=new Set;for(let e of l)U.has(e)||r.add(e);for(let n of r){let r=$(e,t,T(n));U.set(n,r),s.push(r)}n=Promise.all(s)}else{for(let o of(n=$(e,t,T(r.path)),l))U.has(o)||U.set(o,n)}for(let e of o)_.has(e)||_.set(e,n);await n}R.l=function(e){return k(1,this.m.id,e)};let v=Promise.resolve(void 0),P=new WeakMap;function $(t,r,n){let o=e.loadChunkCached(t,n),i=P.get(o);if(void 0===i){let e=P.set.bind(P,o,v);i=o.then(e).catch(e=>{let o;switch(t){case 0:o=`as a runtime dependency of chunk ${r}`;break;case 1:o=`from module ${r}`;break;case 2:o="from an HMR update";break;default:w(t,e=>`Unknown source type: ${e}`)}throw Error(`Failed to load chunk ${n} ${o}${e?`: ${e}`:""}`,e?{cause:e}:void 0)}),P.set(o,i)}return i}function T(e){return`${t}${e.split("/").map(e=>encodeURIComponent(e)).join("/")}`}R.L=function(e){return $(1,this.m.id,e)},R.R=function(e){let t=this.r(e);return t?.default??t},R.P=function(e){return`/ROOT/${e??""}`},R.b=function(e){let t=new Blob([`self.TURBOPACK_WORKER_LOCATION = ${JSON.stringify(location.origin)};
self.TURBOPACK_NEXT_CHUNK_URLS = ${JSON.stringify(e.reverse().map(T),null,2)};
importScripts(...self.TURBOPACK_NEXT_CHUNK_URLS.map(c => self.TURBOPACK_WORKER_LOCATION + c).reverse());`],{type:"text/javascript"});return URL.createObjectURL(t)};let A=/\.js(?:\?[^#]*)?(?:#.*)?$/,S=/\.css(?:\?[^#]*)?(?:#.*)?$/;function E(e){return S.test(e)}o.w=function(t,r,n){return e.loadWebAssembly(1,this.m.id,t,r,n)},o.u=function(t,r){return e.loadWebAssemblyModule(1,this.m.id,t,r)};let K={};o.c=K;let M=(e,t)=>{let r=K[e];if(r){if(r.error)throw r.error;return r}return x(e,j.Parent,t.id)};function x(e,t,r){let o=C.get(e);"function"!=typeof o&&function(e,t,r){let n;switch(t){case 0:n=`as a runtime entry of chunk ${r}`;break;case 1:n=`because it was required from module ${r}`;break;case 2:n="because of an HMR update";break;default:w(t,e=>`Unknown source type: ${e}`)}throw Error(`Module ${e} was instantiated ${n}, but the module factory is not available. It might have been deleted in an HMR update.`)}(e,t,r);let i=a(e),l=i.exports;K[e]=i;let s=new n(i,l);try{o(s,i,l)}catch(e){throw i.error=e,e}return i.namespaceObject&&i.exports!==i.namespaceObject&&h(i.exports,i.namespaceObject),i}function N(r){let n,o=function(e){if("string"==typeof e)return e;let r=decodeURIComponent(("undefined"!=typeof TURBOPACK_NEXT_CHUNK_URLS?TURBOPACK_NEXT_CHUNK_URLS.pop():e.getAttribute("src")).replace(/[?#].*$/,""));return r.startsWith(t)?r.slice(t.length):r}(r[0]);return 2===r.length?n=r[1]:(n=void 0,!function(e,t,r,n){let o=1;for(;o<e.length;){let t=e[o],n=o+1;for(;n<e.length&&"function"!=typeof e[n];)n++;if(n===e.length)throw Error("malformed chunk format, expected a factory function");if(!r.has(t)){let i=e[n];for(Object.defineProperty(i,"name",{value:"__TURBOPACK__module__evaluation__"});o<n;o++)t=e[o],r.set(t,i)}o=n+1}}(r,0,C)),e.registerChunk(o,n)}let L=new Map;(()=>{function t(e){let t=L.get(e);if(!t){let r,n;t={resolved:!1,loadingStarted:!1,promise:new Promise((e,t)=>{r=e,n=t}),resolve:()=>{t.resolved=!0,r()},reject:n},L.set(e,t)}return t}e={async registerChunk(e,r){if(t(T(e)).resolve(),null!=r){for(let e of r.otherChunks)t(T("string"==typeof e?e:e.path));if(await Promise.all(r.otherChunks.map(t=>k(0,e,t))),r.runtimeModuleIds.length>0)for(let t of r.runtimeModuleIds)!function(e,t){let r=K[t];if(r){if(r.error)throw r.error;return}x(t,j.Runtime,e)}(e,t)}},loadChunkCached:(e,r)=>(function(e,r){let n=t(r);if(n.loadingStarted)return n.promise;if(e===j.Runtime)return n.loadingStarted=!0,E(r)&&n.resolve(),n.promise;if("function"==typeof importScripts)if(E(r));else if(A.test(r))self.TURBOPACK_NEXT_CHUNK_URLS.push(r),importScripts(TURBOPACK_WORKER_LOCATION+r);else throw Error(`can't infer type of chunk from URL ${r} in worker`);else{let e=decodeURI(r);if(E(r))if(document.querySelectorAll(`link[rel=stylesheet][href="${r}"],link[rel=stylesheet][href^="${r}?"],link[rel=stylesheet][href="${e}"],link[rel=stylesheet][href^="${e}?"]`).length>0)n.resolve();else{let e=document.createElement("link");e.rel="stylesheet",e.href=r,e.onerror=()=>{n.reject()},e.onload=()=>{n.resolve()},document.head.appendChild(e)}else if(A.test(r)){let t=document.querySelectorAll(`script[src="${r}"],script[src^="${r}?"],script[src="${e}"],script[src^="${e}?"]`);if(t.length>0)for(let e of Array.from(t))e.addEventListener("error",()=>{n.reject()});else{let e=document.createElement("script");e.src=r,e.onerror=()=>{n.reject()},document.head.appendChild(e)}}else throw Error(`can't infer type of chunk from URL ${r}`)}return n.loadingStarted=!0,n.promise})(e,r),async loadWebAssembly(e,t,r,n,o){let i=fetch(T(r)),{instance:l}=await WebAssembly.instantiateStreaming(i,o);return l.exports},async loadWebAssemblyModule(e,t,r,n){let o=fetch(T(r));return await WebAssembly.compileStreaming(o)}}})();let B=globalThis.TURBOPACK;globalThis.TURBOPACK={push:N},B.forEach(N)})();