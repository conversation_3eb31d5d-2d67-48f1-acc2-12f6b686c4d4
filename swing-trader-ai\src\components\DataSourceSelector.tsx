'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  <PERSON>tings,
  Wifi,
  WifiOff,
  CheckCircle,
  AlertTriangle,
  RefreshCw,
  Loader2,
  Database,
  TrendingUp
} from 'lucide-react'

interface DataSourceStatus {
  ibkr: {
    connected: boolean
    source: string
    message: string
    error?: string
  }
  fmp: {
    connected: boolean
    source: string
    message: string
    error?: string
    isRateLimit?: boolean
  }
  recommended: string
  timestamp: string
}

interface DataSourceSelectorProps {
  onSourceChange?: (source: 'IBKR' | 'FMP') => void
  className?: string
}

export function DataSourceSelector({ onSourceChange, className }: DataSourceSelectorProps) {
  const [status, setStatus] = useState<DataSourceStatus | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [selectedSource, setSelectedSource] = useState<'IBKR' | 'FMP'>('IBKR')
  const [isExpanded, setIsExpanded] = useState(false)

  useEffect(() => {
    checkDataSources()
  }, [])

  const checkDataSources = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/data-source?action=status')
      const data = await response.json()
      
      if (data.success) {
        setStatus(data.data)
        setSelectedSource(data.data.recommended === 'IBKR' ? 'IBKR' : 'FMP')
      }
    } catch (error) {
      console.error('Error checking data sources:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSourceChange = (source: 'IBKR' | 'FMP') => {
    setSelectedSource(source)
    onSourceChange?.(source)
  }

  const getStatusIcon = (connected: boolean, hasError?: boolean) => {
    if (hasError) return <AlertTriangle className="h-4 w-4 text-red-500" />
    if (connected) return <CheckCircle className="h-4 w-4 text-green-500" />
    return <WifiOff className="h-4 w-4 text-gray-400" />
  }

  const getStatusBadge = (connected: boolean, isRateLimit?: boolean) => {
    if (isRateLimit) return <Badge className="bg-yellow-500 text-white">Rate Limited</Badge>
    if (connected) return <Badge className="bg-green-500 text-white">Connected</Badge>
    return <Badge variant="outline" className="text-gray-500">Disconnected</Badge>
  }

  if (!isExpanded) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsExpanded(true)}
          className="flex items-center gap-2"
        >
          <Database className="h-4 w-4" />
          <span className="text-sm">
            Data: {selectedSource}
          </span>
          {status && (
            <div className="flex items-center">
              {selectedSource === 'IBKR' 
                ? getStatusIcon(status.ibkr.connected, !!status.ibkr.error)
                : getStatusIcon(status.fmp.connected, !!status.fmp.error)
              }
            </div>
          )}
        </Button>
      </div>
    )
  }

  return (
    <Card className={`w-96 ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Database className="h-5 w-5" />
            Data Sources
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={checkDataSources}
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(false)}
            >
              ×
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* IBKR Option */}
        <div 
          className={`p-4 border rounded-lg cursor-pointer transition-all ${
            selectedSource === 'IBKR' 
              ? 'border-blue-500 bg-blue-50' 
              : 'border-gray-200 hover:border-gray-300'
          }`}
          onClick={() => handleSourceChange('IBKR')}
        >
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              <span className="font-semibold">Interactive Brokers (IBKR)</span>
              {selectedSource === 'IBKR' && (
                <Badge className="bg-blue-500 text-white">Selected</Badge>
              )}
            </div>
            {status && getStatusIcon(status.ibkr.connected, !!status.ibkr.error)}
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Status:</span>
              {status && getStatusBadge(status.ibkr.connected)}
            </div>
            
            {status && (
              <p className="text-xs text-gray-500">
                {status.ibkr.message}
              </p>
            )}
            
            <div className="text-xs text-gray-400">
              ✅ Real-time data • ✅ Pre-market data • ✅ No rate limits • ✅ Direct trading
            </div>
          </div>
        </div>

        {/* FMP Option */}
        <div 
          className={`p-4 border rounded-lg cursor-pointer transition-all ${
            selectedSource === 'FMP' 
              ? 'border-orange-500 bg-orange-50' 
              : 'border-gray-200 hover:border-gray-300'
          }`}
          onClick={() => handleSourceChange('FMP')}
        >
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <Database className="h-5 w-5 text-orange-600" />
              <span className="font-semibold">Financial Modeling Prep (FMP)</span>
              {selectedSource === 'FMP' && (
                <Badge className="bg-orange-500 text-white">Selected</Badge>
              )}
            </div>
            {status && getStatusIcon(status.fmp.connected, !!status.fmp.error)}
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Status:</span>
              {status && getStatusBadge(status.fmp.connected, status.fmp.isRateLimit)}
            </div>
            
            {status && (
              <p className="text-xs text-gray-500">
                {status.fmp.message}
              </p>
            )}
            
            <div className="text-xs text-gray-400">
              ⚠️ Rate limited • ✅ Historical data • ✅ Fundamental data • ❌ No direct trading
            </div>
          </div>
        </div>

        {/* Recommendation */}
        {status && status.recommended && (
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center gap-2 mb-1">
              <CheckCircle className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-blue-800">Recommendation</span>
            </div>
            <p className="text-sm text-blue-700">
              {status.recommended === 'IBKR' 
                ? 'IBKR is recommended for real-time data and direct trading capabilities.'
                : status.recommended === 'FMP'
                ? 'FMP is available as fallback, but may have rate limits.'
                : 'No data sources are currently available. Please check your connections.'
              }
            </p>
          </div>
        )}

        {/* IBKR Setup Instructions */}
        {status && !status.ibkr.connected && (
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center gap-2 mb-1">
              <AlertTriangle className="h-4 w-4 text-yellow-600" />
              <span className="font-medium text-yellow-800">IBKR Setup Required</span>
            </div>
            <div className="text-sm text-yellow-700 space-y-1">
              <p>To use IBKR data:</p>
              <ol className="list-decimal list-inside space-y-1 ml-2">
                <li>Install and open TWS or IB Gateway</li>
                <li>Enable API connections in Global Configuration</li>
                <li>Set Socket Port to 7497 (paper) or 7496 (live)</li>
                <li>Add 127.0.0.1 to trusted IPs</li>
              </ol>
            </div>
          </div>
        )}

        <div className="flex items-center justify-between pt-2">
          <span className="text-xs text-gray-400">
            {status && `Last checked: ${new Date(status.timestamp).toLocaleTimeString()}`}
          </span>
          <Button
            size="sm"
            onClick={() => setIsExpanded(false)}
            variant="outline"
          >
            Done
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
