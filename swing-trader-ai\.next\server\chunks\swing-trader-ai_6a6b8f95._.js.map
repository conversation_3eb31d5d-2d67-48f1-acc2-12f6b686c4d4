{"version": 3, "sources": ["turbopack:///[project]/swing-trader-ai/src/lib/enhancedSwingScanner.ts", "turbopack:///[project]/swing-trader-ai/src/app/api/scanner/strategies/route.ts", "turbopack:///[project]/swing-trader-ai/node_modules/next/dist/esm/build/templates/app-route.js"], "sourcesContent": ["import { SwingTradingStrategies, StrategySetup } from './swingStrategies'\nimport { PolygonAPI } from './polygon'\nimport { FMPAPI } from './fmp'\nimport { StockData, CandlestickData } from '@/types/trading'\nimport { format, subDays } from 'date-fns'\n\nexport interface EnhancedScanResult {\n  symbol: string\n  name: string\n  sector: string\n  quote: StockData\n  overnightSetup?: StrategySetup\n  breakoutSetup?: StrategySetup\n  bestStrategy?: 'overnight_momentum' | 'technical_breakout'\n  overallScore: number\n  rank: number\n  scanTime: string\n  alerts: string[]\n  riskWarnings: string[]\n}\n\nexport interface StrategyScanSummary {\n  totalScanned: number\n  overnightSetups: number\n  breakoutSetups: number\n  bothStrategies: number\n  topSetups: EnhancedScanResult[]\n  scanDuration: number\n  marketConditions: {\n    timeOfDay: string\n    isOptimalScanTime: boolean\n    marketHours: boolean\n  }\n}\n\nexport class EnhancedSwingScanner {\n  private fmpAPI: FMPAPI\n  private polygonAPI: PolygonAPI\n  private accountSize: number\n\n  constructor(accountSize: number = 100000) {\n    this.fmpAPI = new FMPAPI(process.env.FMP_API_KEY)\n    this.polygonAPI = new PolygonAPI(process.env.POLYGON_API_KEY)\n    this.accountSize = accountSize\n  }\n\n  // Main enhanced scanning function\n  async scanWithStrategies(\n    symbols: string[], \n    maxConcurrent: number = 5\n  ): Promise<StrategyScanSummary> {\n    const startTime = Date.now()\n    const results: EnhancedScanResult[] = []\n    const failed: string[] = []\n\n    console.log(`Starting enhanced strategy scan of ${symbols.length} stocks...`)\n\n    // Check if we're in optimal scan time (12:00-16:00 ET)\n    const marketConditions = this.getMarketConditions()\n\n    // Process stocks in batches\n    for (let i = 0; i < symbols.length; i += maxConcurrent) {\n      const batch = symbols.slice(i, i + maxConcurrent)\n      const batchPromises = batch.map(symbol => this.scanSingleStockStrategies(symbol))\n      \n      const batchResults = await Promise.allSettled(batchPromises)\n      \n      batchResults.forEach((result, index) => {\n        const symbol = batch[index]\n        if (result.status === 'fulfilled' && result.value) {\n          results.push(result.value)\n        } else {\n          failed.push(symbol)\n          console.warn(`Failed to scan ${symbol}:`, result.status === 'rejected' ? result.reason : 'Unknown error')\n        }\n      })\n\n      // Rate limiting delay\n      if (i + maxConcurrent < symbols.length) {\n        await new Promise(resolve => setTimeout(resolve, 1000))\n      }\n    }\n\n    // Sort by overall score and assign ranks\n    results.sort((a, b) => b.overallScore - a.overallScore)\n    results.forEach((result, index) => {\n      result.rank = index + 1\n    })\n\n    // Calculate summary statistics\n    const overnightSetups = results.filter(r => r.overnightSetup).length\n    const breakoutSetups = results.filter(r => r.breakoutSetup).length\n    const bothStrategies = results.filter(r => r.overnightSetup && r.breakoutSetup).length\n\n    const scanDuration = Date.now() - startTime\n\n    return {\n      totalScanned: symbols.length,\n      overnightSetups,\n      breakoutSetups,\n      bothStrategies,\n      topSetups: results.slice(0, 25), // Top 25 setups\n      scanDuration,\n      marketConditions\n    }\n  }\n\n  // Scan individual stock for both strategies\n  private async scanSingleStockStrategies(symbol: string): Promise<EnhancedScanResult | null> {\n    try {\n      // Get stock quote and historical data\n      const [quote, historicalData] = await Promise.all([\n        this.fmpAPI.getStockQuote(symbol),\n        this.getHistoricalData(symbol)\n      ])\n\n      if (!historicalData || historicalData.length < 30) {\n        throw new Error('Insufficient historical data - need at least 30 days')\n      }\n\n      // Analyze both strategies\n      const overnightSetup = SwingTradingStrategies.analyzeOvernightMomentum(\n        symbol, historicalData, quote, this.accountSize\n      )\n      \n      const breakoutSetup = SwingTradingStrategies.analyzeTechnicalBreakout(\n        symbol, historicalData, quote, this.accountSize\n      )\n\n      // Skip if no valid setups\n      if (!overnightSetup && !breakoutSetup) {\n        return null\n      }\n\n      // Determine best strategy and overall score\n      const { bestStrategy, overallScore } = this.calculateBestStrategy(overnightSetup, breakoutSetup)\n\n      // Generate alerts and warnings\n      const alerts = this.generateAlerts(overnightSetup, breakoutSetup, quote)\n      const riskWarnings = this.generateRiskWarnings(overnightSetup, breakoutSetup, quote)\n\n      return {\n        symbol,\n        name: quote.name,\n        sector: this.getSectorForSymbol(symbol),\n        quote,\n        overnightSetup: overnightSetup || undefined,\n        breakoutSetup: breakoutSetup || undefined,\n        bestStrategy,\n        overallScore,\n        rank: 0, // Will be set after sorting\n        scanTime: new Date().toISOString(),\n        alerts,\n        riskWarnings\n      }\n    } catch (error) {\n      console.error(`Error scanning ${symbol}:`, error)\n      if (error instanceof Error) {\n        console.error(`Error message: ${error.message}`)\n        console.error(`Error stack: ${error.stack}`)\n      }\n      return null\n    }\n  }\n\n  // Get historical data with optimized API usage\n  private async getHistoricalData(symbol: string): Promise<CandlestickData[]> {\n    const to = format(new Date(), 'yyyy-MM-dd')\n    const from = format(subDays(new Date(), 100), 'yyyy-MM-dd') // 100 days should be sufficient\n\n    try {\n      console.log(`Fetching historical data for ${symbol} from ${from} to ${to}`)\n      const data = await this.polygonAPI.getHistoricalData(symbol, 'day', 1, from, to)\n\n      if (data.length === 0) {\n        console.warn(`No historical data returned for ${symbol}`)\n        throw new Error('No historical data available')\n      }\n\n      console.log(`Successfully fetched ${data.length} days of data for ${symbol}`)\n      return data\n    } catch (error) {\n      console.error(`Failed to fetch historical data for ${symbol}:`, error)\n      throw error\n    }\n  }\n\n  // Calculate best strategy and overall score\n  private calculateBestStrategy(\n    overnight?: StrategySetup | null, \n    breakout?: StrategySetup | null\n  ): { bestStrategy?: 'overnight_momentum' | 'technical_breakout', overallScore: number } {\n    if (!overnight && !breakout) {\n      return { overallScore: 0 }\n    }\n\n    if (overnight && !breakout) {\n      return { bestStrategy: 'overnight_momentum', overallScore: overnight.confidence }\n    }\n\n    if (breakout && !overnight) {\n      return { bestStrategy: 'technical_breakout', overallScore: breakout.confidence }\n    }\n\n    if (overnight && breakout) {\n      // Both strategies valid - choose higher confidence\n      if (overnight.confidence > breakout.confidence) {\n        return { bestStrategy: 'overnight_momentum', overallScore: overnight.confidence + 5 } // Bonus for multiple setups\n      } else {\n        return { bestStrategy: 'technical_breakout', overallScore: breakout.confidence + 5 }\n      }\n    }\n\n    return { overallScore: 0 }\n  }\n\n  // Generate trading alerts\n  private generateAlerts(\n    overnight?: StrategySetup | null,\n    breakout?: StrategySetup | null,\n    quote?: StockData\n  ): string[] {\n    const alerts: string[] = []\n\n    if (overnight) {\n      alerts.push(`🚀 OVERNIGHT MOMENTUM: Entry ${overnight.entryPrice.toFixed(2)}, Target ${overnight.targets[0].toFixed(2)}`)\n      alerts.push(`⏰ Execute in final 30-60 min before close`)\n      alerts.push(`🛑 Stop: ${overnight.stopLoss.toFixed(2)} (${((overnight.entryPrice - overnight.stopLoss) / overnight.entryPrice * 100).toFixed(1)}% risk)`)\n    }\n\n    if (breakout) {\n      alerts.push(`📈 BREAKOUT SETUP: Entry ${breakout.entryPrice.toFixed(2)}, riding 8-EMA`)\n      alerts.push(`🎯 Targets: ${breakout.targets.map(t => t.toFixed(2)).join(', ')}`)\n      alerts.push(`🛑 Stop: Daily close below ${breakout.stopLoss.toFixed(2)}`)\n    }\n\n    if (quote && quote.changePercent > 5) {\n      alerts.push(`🔥 Strong momentum: +${quote.changePercent.toFixed(1)}% today`)\n    }\n\n    return alerts\n  }\n\n  // Generate risk warnings\n  private generateRiskWarnings(\n    overnight?: StrategySetup | null,\n    breakout?: StrategySetup | null,\n    quote?: StockData\n  ): string[] {\n    const warnings: string[] = []\n\n    if (overnight) {\n      warnings.push(`⚠️ Overnight gap risk - size down vs intraday trades`)\n      if (quote && quote.changePercent > 8) {\n        warnings.push(`⚠️ Extended move (+${quote.changePercent.toFixed(1)}%) - consider smaller size`)\n      }\n    }\n\n    if (quote && (quote.marketCap || 0) < 1000000000) {\n      warnings.push(`⚠️ Small cap overnight risk - volatile gaps possible`)\n    }\n\n    if (quote && quote.volume < 1000000) {\n      warnings.push(`⚠️ Lower volume - may have liquidity issues`)\n    }\n\n    return warnings\n  }\n\n  // Get market conditions with proper timezone handling\n  private getMarketConditions() {\n    const now = new Date()\n\n    // Get current time in Eastern Time (market timezone)\n    const etNow = new Date(now.toLocaleString(\"en-US\", {timeZone: \"America/New_York\"}))\n    const etHour = etNow.getHours()\n    const etMinute = etNow.getMinutes()\n    const etTimeDecimal = etHour + etMinute / 60\n\n    // Get local time for display\n    const localHour = now.getHours()\n    const localMinute = now.getMinutes()\n\n    // Check if it's a weekday (Monday = 1, Friday = 5)\n    const dayOfWeek = etNow.getDay()\n    const isWeekday = dayOfWeek >= 1 && dayOfWeek <= 5\n\n    return {\n      timeOfDay: `${localHour.toString().padStart(2, '0')}:${localMinute.toString().padStart(2, '0')} Local (${etHour.toString().padStart(2, '0')}:${etMinute.toString().padStart(2, '0')} ET)`,\n      isOptimalScanTime: isWeekday && etTimeDecimal >= 12 && etTimeDecimal <= 16, // 12:00-16:00 ET on weekdays\n      marketHours: isWeekday && etTimeDecimal >= 9.5 && etTimeDecimal <= 16, // 9:30-16:00 ET on weekdays\n      etTime: `${etHour.toString().padStart(2, '0')}:${etMinute.toString().padStart(2, '0')} ET`,\n      isWeekday\n    }\n  }\n\n  // Get sector for symbol (reuse from previous implementation)\n  private getSectorForSymbol(symbol: string): string {\n    const techSymbols = ['MSFT', 'NVDA', 'GOOG', 'GOOGL', 'META', 'AVGO', 'TSM', 'ORCL', 'CSCO', 'AMD', 'ASML', 'MU', 'LRCX', 'PLTR', 'APP', 'NET', 'DDOG', 'ZS', 'SHOP', 'SOUN', 'IONQ', 'RGTI', 'RIOT', 'HUT', 'IREN', 'ASTS', 'NBIS']\n    const financialSymbols = ['JPM', 'BAC', 'MS', 'SCHW', 'C', 'HOOD', 'SOFI', 'TIGR', 'FUTU']\n    const healthcareSymbols = ['JNJ', 'ABBV', 'MRK', 'GILD']\n    const industrialSymbols = ['GE', 'CAT', 'BA', 'GEV', 'UAL', 'VRT', 'RKLB']\n    const materialsSymbols = ['AEM', 'NEM', 'PAAS', 'BTG', 'HL', 'MP', 'AG']\n    const consumerSymbols = ['AMZN', 'DIS', 'SBUX', 'MO', 'DASH', 'GM', 'NCLH', 'CELH', 'LEVI', 'ELF', 'ETSY', 'W']\n    const communicationSymbols = ['NFLX', 'RBLX', 'BILI']\n    const energySymbols = ['CEG', 'VST', 'CCJ']\n\n    if (techSymbols.includes(symbol)) return 'Technology'\n    if (financialSymbols.includes(symbol)) return 'Financial Services'\n    if (healthcareSymbols.includes(symbol)) return 'Healthcare'\n    if (industrialSymbols.includes(symbol)) return 'Industrial'\n    if (materialsSymbols.includes(symbol)) return 'Materials'\n    if (consumerSymbols.includes(symbol)) return 'Consumer'\n    if (communicationSymbols.includes(symbol)) return 'Communication Services'\n    if (energySymbols.includes(symbol)) return 'Energy'\n    \n    return 'Other'\n  }\n\n  // Quick scan with strategies\n  async quickStrategyScan(prioritySymbols: string[]): Promise<EnhancedScanResult[]> {\n    const summary = await this.scanWithStrategies(prioritySymbols, 8)\n    return summary.topSetups\n  }\n}\n\n// Create singleton instance\nexport const enhancedSwingScanner = new EnhancedSwingScanner()\n", "import { NextRequest, NextResponse } from 'next/server'\nimport { enhancedSwingScanner } from '@/lib/enhancedSwingScanner'\nimport { PRIORITY_SYMBOLS, ALL_SYMBOLS } from '@/data/watchlist'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const scanType = searchParams.get('type') || 'quick' // quick, full\n    const accountSize = parseInt(searchParams.get('accountSize') || '100000')\n    const limit = parseInt(searchParams.get('limit') || '20')\n    \n    console.log(`Starting ${scanType} strategy scan...`)\n    \n    // Set account size for position sizing\n    const scanner = new (require('@/lib/enhancedSwingScanner').EnhancedSwingScanner)(accountSize)\n    \n    let summary\n    if (scanType === 'full') {\n      summary = await scanner.scanWithStrategies(ALL_SYMBOLS, 3) // Slower for full scan\n    } else {\n      summary = await scanner.scanWithStrategies(PRIORITY_SYMBOLS, 6) // Faster for quick scan\n    }\n    \n    // Limit results if requested\n    const limitedSummary = {\n      ...summary,\n      topSetups: summary.topSetups.slice(0, limit)\n    }\n    \n    return NextResponse.json(limitedSummary)\n  } catch (error) {\n    console.error('Error in strategy scanner API:', error)\n    return NextResponse.json(\n      { error: 'Failed to perform strategy scan' },\n      { status: 500 }\n    )\n  }\n}\n", "import { AppRouteRouteModule } from \"next/dist/esm/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/esm/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/esm/server/lib/patch-fetch\";\nimport { getRequestMeta } from \"next/dist/esm/server/request-meta\";\nimport { getTracer, SpanKind } from \"next/dist/esm/server/lib/trace/tracer\";\nimport { normalizeAppPath } from \"next/dist/esm/shared/lib/router/utils/app-paths\";\nimport { NodeNextRequest, NodeNextResponse } from \"next/dist/esm/server/base-http/node\";\nimport { NextRequestAdapter, signalFromNodeResponse } from \"next/dist/esm/server/web/spec-extension/adapters/next-request\";\nimport { BaseServerSpan } from \"next/dist/esm/server/lib/trace/constants\";\nimport { getRevalidateReason } from \"next/dist/esm/server/instrumentation/utils\";\nimport { sendResponse } from \"next/dist/esm/server/send-response\";\nimport { fromNodeOutgoingHttpHeaders, toNodeOutgoingHttpHeaders } from \"next/dist/esm/server/web/utils\";\nimport { getCacheControlHeader } from \"next/dist/esm/server/lib/cache-control\";\nimport { INFINITE_CACHE, NEXT_CACHE_TAGS_HEADER } from \"next/dist/esm/lib/constants\";\nimport { NoFallbackError } from \"next/dist/esm/shared/lib/no-fallback-error.external\";\nimport { CachedRouteKind } from \"next/dist/esm/server/response-cache\";\nimport * as userland from \"INNER_APP_ROUTE\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/scanner/strategies/route\",\n        pathname: \"/api/scanner/strategies\",\n        filename: \"route\",\n        bundlePath: \"\"\n    },\n    distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n    relativeProjectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || '',\n    resolvedPagePath: \"[project]/swing-trader-ai/src/app/api/scanner/strategies/route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\nexport async function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/scanner/strategies/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (process.env.TURBOPACK) {\n        srcPage = srcPage.replace(/\\/index$/, '') || '/';\n    } else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = process.env.__NEXT_MULTI_ZONE_DRAFT_MODE;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = normalizeAppPath(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = getTracer();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: getRequestMeta(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new NodeNextRequest(req);\n    const nodeNextRes = new NodeNextResponse(res);\n    const nextReq = NextRequestAdapter.fromNodeNextRequest(nodeNextReq, signalFromNodeResponse(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!getRequestMeta(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = toNodeOutgoingHttpHeaders(response.headers);\n                        if (cacheTags) {\n                            headers[NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await sendResponse(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: getRevalidateReason({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!getRequestMeta(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = fromNodeOutgoingHttpHeaders(cacheEntry.value.headers);\n            if (!(getRequestMeta(req, 'minimalMode') && isIsr)) {\n                headers.delete(NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', getCacheControlHeader(cacheEntry.cacheControl));\n            }\n            await sendResponse(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        if (!(err instanceof NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: getRevalidateReason({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await sendResponse(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n"], "names": [], "mappings": "wGAAA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,MA+BO,OAAM,EACH,MAAc,CACd,UAAsB,CACtB,WAAmB,AAE3B,aAAY,EAAsB,GAAM,CAAE,CACxC,IAAI,CAAC,MAAM,CAAG,IAAI,EAAA,MAAM,CAAC,QAAQ,GAAG,CAAC,WAAW,EAChD,IAAI,CAAC,UAAU,CAAG,IAAI,EAAA,UAAU,CAAC,QAAQ,GAAG,CAAC,eAAe,EAC5D,IAAI,CAAC,WAAW,CAAG,CACrB,CAGA,MAAM,mBACJ,CAAiB,CACjB,EAAwB,CAAC,CACK,CAC9B,IAAM,EAAY,KAAK,GAAG,GACpB,EAAgC,EAAE,CAClC,EAAmB,EAAE,CAE3B,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,EAAQ,MAAM,CAAC,UAAU,CAAC,EAG5E,IAAM,EAAmB,IAAI,CAAC,mBAAmB,GAGjD,IAAK,IAAI,EAAI,EAAG,EAAI,EAAQ,MAAM,CAAE,GAAK,EAAe,CACtD,IAAM,EAAQ,EAAQ,KAAK,CAAC,EAAG,EAAI,GAC7B,EAAgB,EAAM,GAAG,CAAC,GAAU,IAAI,CAAC,yBAAyB,CAAC,IAIzE,CAFqB,MAAM,QAAQ,UAAU,CAAC,EAAA,EAEjC,OAAO,CAAC,CAAC,EAAQ,KAC5B,IAAM,EAAS,CAAK,CAAC,EAAM,CACL,cAAlB,EAAO,MAAM,EAAoB,EAAO,KAAK,CAC/C,CADiD,CACzC,IAAI,CAAC,EAAO,KAAK,GAEzB,EAAO,IAAI,CAAC,GACZ,QAAQ,IAAI,CAAC,CAAC,eAAe,EAAE,EAAO,CAAC,CAAC,CAAoB,aAAlB,EAAO,MAAM,CAAkB,EAAO,MAAM,CAAG,iBAE7F,GAGI,EAAI,EAAgB,EAAQ,MAAM,EAAE,AACtC,MAAM,IAAI,QAAQ,GAAW,WAAW,EAAS,KAErD,CAGA,EAAQ,IAAI,CAAC,CAAC,EAAG,IAAM,EAAE,YAAY,CAAG,EAAE,YAAY,EACtD,EAAQ,OAAO,CAAC,CAAC,EAAQ,KACvB,EAAO,IAAI,CAAG,EAAQ,CACxB,GAGA,IAAM,EAAkB,EAAQ,MAAM,CAAC,GAAK,EAAE,cAAc,EAAE,MAAM,CAC9D,EAAiB,EAAQ,MAAM,CAAC,GAAK,EAAE,aAAa,EAAE,MAAM,CAC5D,EAAiB,EAAQ,MAAM,CAAC,GAAK,EAAE,cAAc,EAAI,EAAE,aAAa,EAAE,MAAM,CAEhF,EAAe,KAAK,GAAG,GAAK,EAElC,MAAO,CACL,aAAc,EAAQ,MAAM,iBAC5B,iBACA,iBACA,EACA,UAAW,EAAQ,KAAK,CAAC,EAAG,iBAC5B,mBACA,CACF,CACF,CAGA,MAAc,0BAA0B,CAAc,CAAsC,CAC1F,GAAI,CAEF,GAAM,CAAC,EAAO,EAAe,CAAG,MAAM,QAAQ,GAAG,CAAC,CAChD,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,GAC1B,IAAI,CAAC,iBAAiB,CAAC,GACxB,EAED,GAAI,CAAC,GAAkB,EAAe,MAAM,CAAG,GAC7C,CADiD,KAC3C,AAAI,MAAM,wDAIlB,IAAM,EAAiB,EAAA,sBAAsB,CAAC,wBAAwB,CACpE,EAAQ,EAAgB,EAAO,IAAI,CAAC,WAAW,EAG3C,EAAgB,EAAA,sBAAsB,CAAC,wBAAwB,CACnE,EAAQ,EAAgB,EAAO,IAAI,CAAC,WAAW,EAIjD,GAAI,CAAC,GAAkB,CAAC,EACtB,OAAO,KAIT,CALuC,EAKjC,cAAE,CAAY,cAAE,CAAY,CAAE,CAAG,IAAI,CAAC,qBAAqB,CAAC,EAAgB,GAG5E,EAAS,IAAI,CAAC,cAAc,CAAC,EAAgB,EAAe,GAC5D,EAAe,IAAI,CAAC,oBAAoB,CAAC,EAAgB,EAAe,GAE9E,MAAO,QACL,EACA,KAAM,EAAM,IAAI,CAChB,OAAQ,IAAI,CAAC,kBAAkB,CAAC,SAChC,EACA,eAAgB,QAAkB,EAClC,cAAe,QAAiB,eAChC,eACA,EACA,KAAM,EACN,SAAU,IAAI,OAAO,WAAW,UAChC,eACA,CACF,CACF,CAAE,MAAO,EAAO,CAMd,OALA,QAAQ,KAAK,CAAC,CAAC,eAAe,EAAE,EAAO,CAAC,CAAC,CAAE,GACvC,aAAiB,OAAO,CAC1B,QAAQ,KAAK,CAAC,CAAC,eAAe,EAAE,EAAM,OAAO,CAAA,CAAE,EAC/C,QAAQ,KAAK,CAAC,CAAC,aAAa,EAAE,EAAM,KAAK,CAAA,CAAE,GAEtC,IACT,CACF,CAGA,MAAc,kBAAkB,CAAc,CAA8B,CAC1E,IAAM,EAAK,CAAA,EAAA,EAAA,MAAA,AAAM,EAAC,IAAI,KAAQ,cACxB,EAAO,CAAA,EAAA,EAAA,MAAA,AAAM,EAAC,CAAA,EAAA,EAAA,OAAO,AAAP,EAAQ,IAAI,KAAQ,KAAM,cAAc,AAE5D,GAAI,CACF,QAAQ,GAAG,CAAC,CAAC,eAH6E,cAGhD,EAAE,EAAO,MAAM,EAAE,EAAK,IAAI,EAAE,EAAA,CAAI,EAC1E,IAAM,EAAO,MAAM,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,EAAQ,MAAO,EAAG,EAAM,GAE7E,GAAoB,GAAG,CAAnB,EAAK,MAAM,CAEb,MADA,QAAQ,IAAI,CAAC,CAAC,gCAAgC,EAAE,EAAA,CAAQ,EAClD,AAAI,MAAM,gCAIlB,OADA,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,EAAK,MAAM,CAAC,kBAAkB,EAAE,EAAA,CAAQ,EACrE,CACT,CAAE,MAAO,EAAO,CAEd,MADA,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,EAAO,CAAC,CAAC,CAAE,GAC1D,CACR,CACF,CAGQ,sBACN,CAAgC,CAChC,CAA+B,CACuD,CACtF,GAAI,CAAC,GAAa,CAAC,EACjB,MAAO,CAAE,CADkB,YACJ,CAAE,EAG3B,GAAI,GAAa,CAAC,EAChB,MAAO,CAAE,CADiB,YACH,qBAAsB,aAAc,EAAU,UAAU,AAAC,EAGlF,GAAI,GAAY,CAAC,EACf,MAAO,CAAE,EADiB,WACH,qBAAsB,aAAc,EAAS,UAAU,AAAC,EAGjF,GAAI,GAAa,EAEf,GAAI,EAAU,GAFW,OAED,CAAG,EAAS,UAAU,CAC5C,CAD8C,KACvC,CAAE,aAAc,qBAAsB,aAAc,EAAU,UAAU,CAAG,CAAE,EAAE,KAEtF,MAAO,CAAE,aAAc,GAF2F,kBAErE,aAAc,EAAS,UAAU,CAAG,CAAE,EAIvF,MAAO,CAAE,aAAc,CAAE,CAC3B,CAGQ,eACN,CAAgC,CAChC,CAA+B,CAC/B,CAAiB,CACP,CACV,IAAM,EAAmB,EAAE,CAkB3B,OAhBI,IACF,EAAO,IAAI,CADE,AACD,CAAC,6BAA6B,EAAE,EAAU,UAAU,CAAC,OAAO,CAAC,GAAG,SAAS,EAAE,EAAU,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,GAAA,CAAI,EACxH,EAAO,IAAI,CAAC,CAAC,yCAAyC,CAAC,EACvD,EAAO,IAAI,CAAC,CAAC,SAAS,EAAE,EAAU,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,EAAU,UAAU,CAAG,EAAU,QAAQ,AAAR,EAAY,EAAU,UAAU,CAAG,GAAA,CAAG,CAAE,OAAO,CAAC,GAAG,OAAO,CAAC,GAGtJ,IACF,EAAO,IADK,AACD,CAAC,CAAC,yBAAyB,EAAE,EAAS,UAAU,CAAC,OAAO,CAAC,GAAG,cAAc,CAAC,EACtF,EAAO,IAAI,CAAC,CAAC,YAAY,EAAE,EAAS,OAAO,CAAC,GAAG,CAAC,GAAK,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,MAAA,CAAO,EAC/E,EAAO,IAAI,CAAC,CAAC,2BAA2B,EAAE,EAAS,QAAQ,CAAC,OAAO,CAAC,GAAA,CAAI,GAGtE,GAAS,EAAM,aAAa,CAAG,GACjC,AADoC,EAC7B,IAAI,CAAC,CAAC,qBAAqB,EAAE,EAAM,aAAa,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,EAGtE,CACT,CAGQ,qBACN,CAAgC,CAChC,CAA+B,CAC/B,CAAiB,CACP,CACV,IAAM,EAAqB,EAAE,CAiB7B,OAfI,IACF,EAAS,IAAI,CAAC,AADD,CACE,oDAAoD,CAAC,EAChE,GAAS,EAAM,aAAa,CAAG,GAAG,AACpC,EAAS,IAAI,CAAC,CAAC,mBAAmB,EAAE,EAAM,aAAa,CAAC,OAAO,CAAC,GAAG,0BAA0B,CAAC,GAI9F,GAAS,AAAyB,KAAxB,EAAM,KAA8B,IAArB,GAAI,CAAC,EAChC,EAAS,IAAI,CAAC,CAAC,oDAAoD,CAAC,EAGlE,GAAS,EAAM,MAAM,CAAG,KAC1B,EAAS,EAD0B,EACtB,CAAC,CAAC,2CAA2C,CAAC,EAGtD,CACT,CAGQ,qBAAsB,CAC5B,IAAM,EAAM,IAAI,KAGV,EAAQ,IAAI,KAAK,EAAI,cAAc,CAAC,QAAS,CAAC,SAAU,kBAAkB,IAC1E,EAAS,EAAM,QAAQ,GACvB,EAAW,EAAM,UAAU,GAC3B,EAAgB,EAAS,EAAW,GAGpC,EAAY,EAAI,QAAQ,GACxB,EAAc,EAAI,UAAU,GAG5B,EAAY,EAAM,MAAM,GACxB,EAAY,GAAa,GAAK,GAAa,EAEjD,MAAO,CACL,UAAW,CAAA,EAAG,EAAU,QAAQ,GAAG,QAAQ,CAAC,EAAG,KAAK,CAAC,EAAE,EAAY,QAAQ,GAAG,QAAQ,CAAC,EAAG,KAAK,QAAQ,EAAE,EAAO,QAAQ,GAAG,QAAQ,CAAC,EAAG,KAAK,CAAC,EAAE,EAAS,QAAQ,GAAG,QAAQ,CAAC,EAAG,KAAK,IAAI,CAAC,CACzL,kBAAmB,GAAa,GAAiB,IAAM,GAAiB,GACxE,YAAa,GAAa,GAAiB,KAAO,GAAiB,GACnE,OAAQ,CAAA,EAAG,EAAO,QAAQ,GAAG,QAAQ,CAAC,EAAG,KAAK,CAAC,EAAE,EAAS,QAAQ,GAAG,QAAQ,CAAC,EAAG,KAAK,GAAG,CAAC,WAC1F,CACF,CACF,CAGQ,mBAAmB,CAAc,CAAU,OAC7B,AASpB,AAAI,CATiB,OAAQ,OAAQ,OAAQ,QAAS,OAAQ,OAAQ,MAAO,OAAQ,OAAQ,MAAO,OAAQ,KAAM,OAAQ,OAAQ,MAAO,MAAO,OAAQ,KAAM,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,MAAO,OAAQ,OAAQ,OAAO,CASpN,QAAQ,CAAC,GAAgB,MAAP,OART,AASrB,CATsB,MAAO,MAAO,KAAM,OAAQ,IAAK,OAAQ,OAAQ,OAAQ,OAAO,CASrE,QAAQ,CAAC,GAAgB,MAAP,eARb,AAStB,CATuB,MAAO,OAAQ,MAAO,OAAO,CASlC,QAAQ,CAAC,GAAgB,MAAP,OARd,AAStB,CATuB,KAAM,MAAO,KAAM,MAAO,MAAO,MAAO,OAAO,CASpD,QAAQ,CAAC,GAAgB,MAAP,OARf,AASrB,CATsB,MAAO,MAAO,OAAQ,MAAO,KAAM,KAAM,KAAK,CASnD,QAAQ,CAAC,GAAgB,MAAP,MACnC,AAToB,CAAC,OAAQ,MAAO,OAAQ,KAAM,OAAQ,KAAM,OAAQ,OAAQ,OAAQ,MAAO,OAAQ,IAAI,CAS3F,QAAQ,CAAC,GAAgB,MAAP,KART,AASzB,CAT0B,OAAQ,OAAQ,OAAO,CAS5B,QAAQ,CAAC,GAAgB,MAAP,mBARrB,AASlB,CATmB,MAAO,MAAO,MAAM,CASzB,QAAQ,CAAC,GAAgB,MAAP,GAE7B,OACT,CAGA,MAAM,kBAAkB,CAAyB,CAAiC,CAEhF,MAAO,CADS,MAAM,IAAI,CAAC,kBAAkB,CAAC,EAAiB,EAAA,EAChD,SAAS,AAC1B,CACF,CAGO,IAAM,EAAuB,IAAI,sKEvUxC,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,CAAA,CAAA,OAAA,IAAA,EAAA,EAAA,CAAA,CAAA,+BDfA,IAAA,EAAA,EAAA,CAAA,CAAA,OAEA,EAAA,EAAA,CAAA,CAAA,MAEO,eAAe,EAAI,CAAoB,EAC5C,GAAI,CACF,IAUI,EAVE,cAAE,CAAY,CAAE,CAAG,IAAI,IAAI,EAAQ,GAAG,EACtC,EAAW,EAAa,GAAG,CAAC,SAAW,QAAQ,AAC/C,EAAc,SAAS,EAAa,CADyB,EACtB,CAAC,gBAAkB,UAC1D,EAAQ,SAAS,EAAa,GAAG,CAAC,UAAY,MAEpD,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,EAAS,iBAAiB,CAAC,EAGnD,IAAM,EAAU,GAAK,CAAA,EAAA,CAAA,CAAA,MAAA,EAAsC,oBAAoB,CAAE,GAU3E,EAAiB,IANrB,EADE,AAAa,QAAQ,GACb,MAAM,EAAQ,kBAAkB,CAAC,EAAA,WAAW,CAAE,GAE9C,CAFiD,KAE3C,EAAQ,gBAF0D,EAExC,CAAC,EAAA,gBAAgB,CAAE,EAK7D,CACA,CANgE,CAK7D,OAAO,CACC,EAAQ,SAAS,CAAC,GAN2D,EAMtD,CAAC,EAAG,EACxC,EAEA,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,EAC3B,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,iCAAkC,GACzC,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,MAAO,iCAAkC,EAC3C,CAAE,OAAQ,GAAI,EAElB,CACF,CCrBA,IAAA,EAAA,EAAA,CAAA,CAAA,OAIA,IAAM,EAAc,IAAI,EAAA,mBAAmB,CAAC,CACxC,WAAY,CACR,KAAM,EAAA,SAAS,CAAC,SAAS,CACzB,KAAM,gCACN,SAAU,0BACV,SAAU,QACV,WAAY,EAChB,EACA,QAAS,CAAA,OACT,IADiD,eACc,CAA3C,EACpB,iBAAkB,oEAClB,iBAZqB,GAarB,SAAA,CACJ,GAIM,kBAAE,CAAgB,sBAAE,CAAoB,aAAE,CAAW,CAAE,CAAG,EAChE,SAAS,IACL,MAAO,CAAA,EAAA,EAAA,UAAA,AAAW,EAAC,kBACf,EACA,sBACJ,EACJ,CAEO,eAAe,EAAQ,CAAG,CAAE,CAAG,CAAE,CAAG,EACvC,IAAI,EACJ,IAAI,EAAU,gCAKV,EAAU,EAAQ,OAAO,CAAC,WAAY,KAAO,IAMjD,IAAM,EAAgB,MAAM,EAAY,OAAO,CAAC,EAAK,EAAK,SACtD,EACA,mBAHE,CAAA,CAIN,GACA,GAAI,CAAC,EAID,OAHA,EAAI,IADY,MACF,CAAG,IACjB,EAAI,GAAG,CAAC,eACS,MAAjB,CAAwB,CAApB,IAAyB,KAAhB,EAAoB,EAAI,SAAS,CAAC,IAAI,CAAC,EAAK,QAAQ,OAAO,IACjE,KAEX,GAAM,SAAE,CAAO,QAAE,CAAM,CAAE,YAAU,aAAE,CAAW,mBAAE,CAAiB,qBAAE,CAAmB,sBAAE,CAAoB,yBAAE,CAAuB,kBAAE,CAAgB,CAAE,CAAG,EACxJ,EAAoB,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,GACvC,GAAQ,EAAQ,EAAkB,aAAa,CAAC,EAAkB,EAAI,EAAkB,MAAM,CAAC,EAAA,AAAiB,EACpH,GAAI,GAAS,CAAC,EAAa,CACvB,IAAM,EAAgB,EAAQ,EAAkB,MAAM,CAAC,EAAiB,CAClE,EAAgB,EAAkB,aAAa,CAAC,EAAkB,CACxE,GAAI,IAC+B,IAA3B,EAAc,KADH,GACW,EAAc,CAAC,EACrC,MAAM,IAAI,EAAA,CAD0C,cAC3B,AAGrC,CACA,IAAI,EAAW,MACX,GAAU,EAAY,IAAb,CAAkB,EAAK,EAAD,EAG/B,EAAW,AAAa,OAHqB,KAC7C,EAAW,CAAA,EAEwB,IAAM,CAAA,EAE7C,IAAM,GACgB,IAAtB,EAAY,EAAkB,GAAb,EAEjB,CAAC,EAKK,EAAe,GAAS,CAAC,EACzB,EAAS,EAAI,MAAM,EAAI,MACvB,EAAS,CAAA,EAAA,EAAA,SAAS,AAAT,IACT,EAAa,EAAO,WAVyE,OAUvD,GACtC,EAAU,QACZ,EACA,oBACA,WAAY,CACR,aAAc,CACV,iBAAiB,CAAQ,EAAW,YAAY,CAAC,eAAe,CAChE,gBAAgB,CAAQ,EAAW,YAAY,CAAC,cAAc,AAClE,0BACA,EACA,iBAAkB,CAAA,EAAA,EAAA,cAAc,AAAd,EAAe,EAAK,oBACtC,kBAAmB,AAAwD,MAAvD,GAA2B,EAAW,YAAA,AAAY,EAAY,KAAK,EAAI,EAAyB,SAAS,cAC7H,EACA,UAAW,EAAI,SAAS,CACxB,QAAU,AAAD,IACL,EAAI,EAAE,CAAC,QAAS,EACpB,EACA,sBAAkB,EAClB,8BAA+B,CAAC,EAAO,EAAU,IAAe,EAAY,cAAc,CAAC,EAAK,EAAO,EAAc,EACzH,EACA,cAAe,SACX,CACJ,CACJ,EACM,EAAc,IAAI,EAAA,eAAe,CAAC,GAClC,EAAc,IAAI,EAAA,gBAAgB,CAAC,GACnC,EAAU,EAAA,kBAAkB,CAAC,mBAAmB,CAAC,EAAa,CAAA,EAAA,EAAA,sBAAsB,AAAtB,EAAuB,IAC3F,GAAI,CACA,IAAM,EAAoB,MAAO,GACtB,EAAY,MAAM,CAAC,EAAS,GAAS,OAAO,CAAC,KAChD,GAAI,CAAC,EAAM,OACX,EAAK,aAAa,CAAC,CACf,mBAAoB,EAAI,UAAU,CAClC,YAAY,CAChB,GACA,IAAM,EAAqB,EAAO,qBAAqB,GAEvD,GAAI,CAAC,EACD,OAEJ,GAAI,EAAmB,GAAG,CAAC,EAHF,kBAGwB,EAAA,cAAc,CAAC,aAAa,CAAE,YAC3E,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,EAAmB,GAAG,CAAC,kBAAkB,qEAAqE,CAAC,EAG9J,IAAM,EAAQ,EAAmB,GAAG,CAAC,cACrC,GAAI,EAAO,CACP,IAAM,EAAO,CAAA,EAAG,EAAO,CAAC,EAAE,EAAA,CAAO,CACjC,EAAK,aAAa,CAAC,CACf,aAAc,EACd,aAAc,EACd,iBAAkB,CACtB,GACA,EAAK,UAAU,CAAC,EACpB,MACI,CADG,CACE,UAAU,CAAC,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAE9C,GAEE,EAAiB,MAAO,QACtB,EA0FI,EAzFR,IAAM,EAAoB,MAAO,oBAAE,CAAkB,CAAE,IACnD,GAAI,CACA,GAAI,CAAC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GAAwB,GAA2B,CAAC,EAK3F,OAJA,EAAI,SAD2G,CACjG,CAAG,IAEjB,EAAI,SAAS,CAAC,iBAAkB,eAChC,EAAI,GAAG,CAAC,gCACD,KAEX,IAAM,EAAW,MAAM,EAAkB,GACzC,EAAI,YAAY,CAAG,EAAQ,UAAU,CAAC,YAAY,CAClD,IAAI,EAAmB,EAAQ,UAAU,CAAC,gBAAgB,CAGtD,GACI,EAAI,SAAS,EAAE,CACf,CAFc,CAEV,SAAS,CAAC,GACd,OAAmB,GAG3B,IAAM,EAAY,EAAQ,UAAU,CAAC,aAAa,CAGlD,IAAI,EA6BA,OADA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,EAAU,EAAQ,UAAU,CAAC,gBAAgB,EACnF,IA7BA,EACP,IAAM,EAAO,MAAM,EAAS,IAAI,GAE1B,EAAU,CAAA,EAAA,EAAA,yBAAA,AAAyB,EAAC,EAAS,OAAO,EACtD,IACA,CAAO,CAAC,EAAA,GADG,mBACmB,CAAC,CAAG,CAAA,EAElC,CAAC,CAAO,CAAC,eAAe,EAAI,EAAK,IAAI,EAAE,CACvC,CAAO,CAAC,eAAe,CAAG,EAAK,IAAA,AAAI,EAEvC,IAAM,EAAa,KAAkD,IAA3C,EAAQ,UAAU,CAAC,mBAAmB,IAAoB,EAAQ,UAAU,CAAC,mBAAmB,EAAI,EAAA,cAAA,AAAc,GAAG,AAAQ,EAAQ,UAAU,CAAC,mBAAmB,CACvL,EAAuD,AAA9C,SAAO,EAAQ,UAAU,CAAC,eAAe,EAAoB,EAAQ,UAAU,CAAC,eAAe,EAAI,EAAA,cAAc,MAAG,EAAY,EAAQ,UAAU,CAAC,eAAe,CAcjL,MAZmB,CAYZ,AAXH,MAAO,CACH,KAAM,EAAA,eAAe,CAAC,SAAS,CAC/B,OAAQ,EAAS,MAAM,CACvB,KAAM,OAAO,IAAI,CAAC,MAAM,EAAK,WAAW,YACxC,CACJ,EACA,aAAc,YACV,SACA,CACJ,CACJ,CAEJ,CAKJ,CAAE,KALS,CAKF,EAAK,CAcV,MAX0B,MAAtB,EAA6B,KAAK,EAAI,EAAmB,OAAA,AAAO,EAAE,CAClE,MAAM,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,cAClC,uBACA,CACJ,EACJ,EAAG,GAED,CACV,CACJ,EACM,EAAa,MAAM,EAAY,cAAc,CAAC,KAChD,aACA,WACA,EACA,UAAW,EAAA,SAAS,CAAC,SAAS,CAC9B,YAAY,oBACZ,EACA,mBAAmB,uBACnB,0BACA,oBACA,EACA,UAAW,EAAI,SAAS,AAC5B,GAEA,GAAI,CAAC,EACD,KADQ,EACD,KAEX,GAAI,CAAe,MAAd,CAAqB,EAAS,AAA0C,GAA9C,IAAK,EAAoB,EAAW,KAAA,AAAK,EAAY,KAAK,EAAI,EAAkB,IAAI,IAAM,EAAA,eAAe,CAAC,SAAS,CAE9I,CAFgJ,KAE1I,OAAO,cAAc,CAAC,AAAI,MAAM,CAAC,kDAAkD,EAAgB,MAAd,CAAqB,EAAS,AAA2C,GAA/C,GAAK,GAAqB,EAAW,KAAA,AAAK,EAAY,KAAK,EAAI,EAAmB,IAAI,CAAA,CAAE,EAAG,oBAAqB,CACjO,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,EAEA,CAAC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAgB,AACrC,EAAI,SAAS,CAAC,iBAAkB,EAAuB,cAAgB,EAAW,MAAM,CAAG,OAAS,EAAW,OAAO,CAAG,QAAU,OAGnI,GACA,EAAI,QADS,CACA,CAAC,gBAAiB,2DAEnC,IAAM,EAAU,CAAA,EAAA,EAAA,2BAAA,AAA2B,EAAC,EAAW,KAAK,CAAC,OAAO,EAapE,MAZI,AAAE,CAAD,AAAC,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GACxC,EAD6C,AACrC,GADwC,GAClC,CAAC,EAAA,sBAAsB,GAIrC,EAAW,YAAY,EAAK,EAAD,AAAK,SAAS,CAAC,kBAAqB,EAAD,AAAS,GAAG,CAAC,kBAAkB,AAC7F,EAAQ,GAAG,CAAC,gBAAiB,CAAA,EAAA,EAAA,qBAAA,AAAqB,EAAC,EAAW,YAAY,GAE9E,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,EAAW,KAAK,CAAC,IAAI,CAAE,SAC7E,EACA,OAAQ,EAAW,KAAK,CAAC,MAAM,EAAI,GACvC,IACO,IACX,EAGI,EACA,MAAM,EAAe,EADT,CAGZ,MAAM,EAAO,qBAAqB,CAAC,EAAI,OAAO,CAAE,IAAI,EAAO,KAAK,CAAC,EAAA,cAAc,CAAC,aAAa,CAAE,CACvF,SAAU,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAChC,KAAM,EAAA,QAAQ,CAAC,MAAM,CACrB,WAAY,CACR,cAAe,EACf,cAAe,EAAI,GAAG,AAC1B,CACJ,EAAG,GAEf,CAAE,MAAO,EAAK,CAcV,GAbI,AAAE,CAAD,YAAgB,EAAA,eAAe,EAChC,CADmC,KAC7B,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,cAClC,uBACA,CACJ,EACJ,GAIA,EAAO,MAAM,EAKjB,OAHA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,KAAM,CAC5D,OAAQ,GACZ,IACO,IACX,CACJ,EAEA,qCAAqC", "ignoreList": [2]}