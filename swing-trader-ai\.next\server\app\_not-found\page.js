var R=require("../../chunks/ssr/[turbopack]_runtime.js")("server/app/_not-found/page.js")
R.c("server/chunks/ssr/6bf44_8b0d32db._.js")
R.c("server/chunks/ssr/[root-of-the-server]__e8a2741f._.js")
R.c("server/chunks/ssr/swing-trader-ai_src_app_e09a94f1._.js")
R.c("server/chunks/ssr/[root-of-the-server]__99bfaf03._.js")
R.c("server/chunks/ssr/6bf44_next_dist_client_components_dbfef941._.js")
R.c("server/chunks/ssr/6bf44_next_dist_client_components_builtin_forbidden_dc3ba2a5.js")
R.c("server/chunks/ssr/6bf44_next_dist_4e2b338d._.js")
R.c("server/chunks/ssr/[root-of-the-server]__b03f1ff9._.js")
R.m("[project]/swing-trader-ai/.next-internal/server/app/_not-found/page/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/swing-trader-ai/node_modules/next/dist/esm/build/templates/app-page.js?page=/_not-found/page { METADATA_0 => \"[project]/swing-trader-ai/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/swing-trader-ai/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/swing-trader-ai/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/swing-trader-ai/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/swing-trader-ai/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/swing-trader-ai/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/swing-trader-ai/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)")
module.exports=R.m("[project]/swing-trader-ai/node_modules/next/dist/esm/build/templates/app-page.js?page=/_not-found/page { METADATA_0 => \"[project]/swing-trader-ai/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/swing-trader-ai/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/swing-trader-ai/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/swing-trader-ai/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/swing-trader-ai/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/swing-trader-ai/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/swing-trader-ai/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)").exports
