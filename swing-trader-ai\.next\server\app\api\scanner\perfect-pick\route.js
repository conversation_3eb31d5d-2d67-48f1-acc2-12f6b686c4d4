/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/scanner/perfect-pick/route";
exports.ids = ["app/api/scanner/perfect-pick/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fscanner%2Fperfect-pick%2Froute&page=%2Fapi%2Fscanner%2Fperfect-pick%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscanner%2Fperfect-pick%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fscanner%2Fperfect-pick%2Froute&page=%2Fapi%2Fscanner%2Fperfect-pick%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscanner%2Fperfect-pick%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var C_Users_SJFit_Desktop_shittyidea_swing_trader_ai_src_app_api_scanner_perfect_pick_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/scanner/perfect-pick/route.ts */ \"(rsc)/./src/app/api/scanner/perfect-pick/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/scanner/perfect-pick/route\",\n        pathname: \"/api/scanner/perfect-pick\",\n        filename: \"route\",\n        bundlePath: \"app/api/scanner/perfect-pick/route\"\n    },\n    distDir: \".next\" || 0,\n    relativeProjectDir:  false || '',\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\shittyidea\\\\swing-trader-ai\\\\src\\\\app\\\\api\\\\scanner\\\\perfect-pick\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_SJFit_Desktop_shittyidea_swing_trader_ai_src_app_api_scanner_perfect_pick_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/scanner/perfect-pick/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = false;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        if (!(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fscanner%2Fperfect-pick%2Froute&page=%2Fapi%2Fscanner%2Fperfect-pick%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscanner%2Fperfect-pick%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/scanner/perfect-pick/route.ts":
/*!***************************************************!*\
  !*** ./src/app/api/scanner/perfect-pick/route.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_perfectPickTradingSystem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/perfectPickTradingSystem */ \"(rsc)/./src/lib/perfectPickTradingSystem.ts\");\n\n\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const accountSize = parseInt(searchParams.get('accountSize') || '100000');\n        const riskPercent = parseFloat(searchParams.get('riskPercent') || '2');\n        const customUniverse = searchParams.get('universe')?.split(',').filter(Boolean);\n        const limit = parseInt(searchParams.get('limit') || '20');\n        const dataSource = searchParams.get('dataSource') || 'IBKR';\n        const useIBKR = dataSource === 'IBKR';\n        console.log('🎯 Perfect-Pick API called with params:', {\n            accountSize,\n            riskPercent,\n            customUniverse: customUniverse?.length || 'default',\n            limit,\n            dataSource,\n            useIBKR\n        });\n        // Initialize Perfect-Pick Trading System with data source preference\n        const perfectPickSystem = new _lib_perfectPickTradingSystem__WEBPACK_IMPORTED_MODULE_1__.PerfectPickTradingSystem(process.env.POLYGON_API_KEY, useIBKR);\n        // Run the complete Perfect-Pick scan\n        const setups = await perfectPickSystem.runPerfectPickScan(accountSize, riskPercent, customUniverse);\n        // Limit results\n        const limitedSetups = setups.slice(0, limit);\n        // Get summary statistics\n        const summary = perfectPickSystem.getSetupSummary(limitedSetups);\n        const response = {\n            success: true,\n            data: {\n                setups: limitedSetups,\n                summary,\n                scanParams: {\n                    accountSize,\n                    riskPercent,\n                    universeSize: customUniverse?.length || 'default',\n                    limit\n                },\n                timestamp: new Date().toISOString()\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response);\n    } catch (error) {\n        console.error('Error in Perfect-Pick API:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to run Perfect-Pick scan',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { action, data } = body;\n        const perfectPickSystem = new _lib_perfectPickTradingSystem__WEBPACK_IMPORTED_MODULE_1__.PerfectPickTradingSystem(process.env.FMP_API_KEY, process.env.POLYGON_API_KEY);\n        switch(action){\n            case 'update_setups':\n                const updatedSetups = await perfectPickSystem.updatePerfectPickSetups(data.setups);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    data: {\n                        setups: updatedSetups\n                    }\n                });\n            case 'generate_entry_trigger':\n                const entryTrigger = await perfectPickSystem.generateEntryTrigger(data.symbol, data.preMarketHigh);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    data: {\n                        entryTrigger\n                    }\n                });\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: 'Invalid action'\n                }, {\n                    status: 400\n                });\n        }\n    } catch (error) {\n        console.error('Error in Perfect-Pick POST API:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to process Perfect-Pick request',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/scanner/perfect-pick/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/catalystDetection.ts":
/*!**************************************!*\
  !*** ./src/lib/catalystDetection.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CatalystDetectionEngine: () => (/* binding */ CatalystDetectionEngine)\n/* harmony export */ });\n/* harmony import */ var _polygon__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./polygon */ \"(rsc)/./src/lib/polygon.ts\");\n\nclass CatalystDetectionEngine {\n    constructor(polygonApiKey){\n        this.catalystCache = new Map();\n        this.impactMeasurements = new Map();\n        this.polygonAPI = new _polygon__WEBPACK_IMPORTED_MODULE_0__.PolygonAPI(polygonApiKey);\n    }\n    /**\n   * Detect catalysts for a specific symbol\n   */ async detectCatalysts(symbol) {\n        const catalysts = [];\n        try {\n            // Check cache first (5-minute cache)\n            const cacheKey = `${symbol}_${Math.floor(Date.now() / (5 * 60 * 1000))}`;\n            if (this.catalystCache.has(cacheKey)) {\n                return this.catalystCache.get(cacheKey);\n            }\n            // Detect different types of catalysts in parallel\n            const [earningsCatalysts, newsCatalysts, analystCatalysts, insiderCatalysts, secFilingCatalysts] = await Promise.all([\n                this.detectEarningsCatalysts(symbol),\n                this.detectNewsCatalysts(symbol),\n                this.detectAnalystCatalysts(symbol),\n                this.detectInsiderCatalysts(symbol),\n                this.detectSECFilingCatalysts(symbol)\n            ]);\n            catalysts.push(...earningsCatalysts, ...newsCatalysts, ...analystCatalysts, ...insiderCatalysts, ...secFilingCatalysts);\n            // Sort by quality score and freshness\n            catalysts.sort((a, b)=>{\n                const freshnessWeight = this.getFreshnessWeight(a.freshness) - this.getFreshnessWeight(b.freshness);\n                if (freshnessWeight !== 0) return freshnessWeight;\n                return b.qualityScore - a.qualityScore;\n            });\n            // Cache results\n            this.catalystCache.set(cacheKey, catalysts);\n            return catalysts;\n        } catch (error) {\n            console.error(`Error detecting catalysts for ${symbol}:`, error);\n            return [];\n        }\n    }\n    /**\n   * Detect earnings-related catalysts\n   */ async detectEarningsCatalysts(symbol) {\n        const catalysts = [];\n        try {\n            // Get recent earnings data from FMP\n            const earningsData = await this.fmpAPI.getEarningsCalendar(symbol, 30) // Last 30 days\n            ;\n            for (const earnings of earningsData){\n                if (this.isEarningsBeat(earnings)) {\n                    const catalyst = {\n                        id: `earnings_${symbol}_${earnings.date}`,\n                        symbol,\n                        type: 'earnings_beat_guidance',\n                        tier: 'tier_1',\n                        impact: 'bullish',\n                        title: `${symbol} Beats Earnings Expectations`,\n                        description: `Q${earnings.quarter} earnings beat: EPS ${earnings.actualEPS} vs ${earnings.estimatedEPS} expected`,\n                        source: 'FMP Earnings Data',\n                        announcementTime: earnings.date,\n                        discoveredTime: new Date().toISOString(),\n                        qualityScore: this.calculateEarningsQualityScore(earnings),\n                        freshness: this.calculateFreshness(earnings.date),\n                        estimatedDuration: 'short_term',\n                        verified: true,\n                        tags: [\n                            'earnings',\n                            'beat',\n                            'guidance'\n                        ],\n                        metadata: {\n                            actualEPS: earnings.actualEPS,\n                            estimatedEPS: earnings.estimatedEPS,\n                            beatPercent: (earnings.actualEPS - earnings.estimatedEPS) / earnings.estimatedEPS * 100,\n                            guidanceRaised: earnings.guidanceRaised || false\n                        }\n                    };\n                    catalysts.push(catalyst);\n                }\n            }\n        } catch (error) {\n            console.error(`Error detecting earnings catalysts for ${symbol}:`, error);\n        }\n        return catalysts;\n    }\n    /**\n   * Detect news-related catalysts\n   */ async detectNewsCatalysts(symbol) {\n        const catalysts = [];\n        try {\n            // Get recent news from FMP\n            const newsData = await this.fmpAPI.getStockNews(symbol, 50) // Last 50 articles\n            ;\n            for (const news of newsData){\n                const catalystType = this.classifyNewsAsCatalyst(news);\n                if (catalystType) {\n                    const catalyst = {\n                        id: `news_${symbol}_${news.publishedDate}_${news.title.slice(0, 20).replace(/\\s+/g, '_')}`,\n                        symbol,\n                        type: catalystType.type,\n                        tier: catalystType.tier,\n                        impact: catalystType.impact,\n                        title: news.title,\n                        description: news.text?.slice(0, 200) + '...' || 0,\n                        source: news.site,\n                        sourceUrl: news.url,\n                        announcementTime: news.publishedDate,\n                        discoveredTime: new Date().toISOString(),\n                        qualityScore: this.calculateNewsQualityScore(news, catalystType.type),\n                        freshness: this.calculateFreshness(news.publishedDate),\n                        estimatedDuration: this.estimateNewsDuration(catalystType.type),\n                        verified: this.isReliableNewsSource(news.site),\n                        tags: this.extractNewsKeywords(news.title + ' ' + (news.text || '')),\n                        metadata: {\n                            site: news.site,\n                            sentiment: news.sentiment || 'neutral'\n                        }\n                    };\n                    catalysts.push(catalyst);\n                }\n            }\n        } catch (error) {\n            console.error(`Error detecting news catalysts for ${symbol}:`, error);\n        }\n        return catalysts;\n    }\n    /**\n   * Detect analyst upgrade/downgrade catalysts\n   */ async detectAnalystCatalysts(symbol) {\n        const catalysts = [];\n        try {\n            // Get analyst recommendations from FMP\n            const analystData = await this.fmpAPI.getAnalystRecommendations(symbol, 30);\n            for (const recommendation of analystData){\n                if (this.isSignificantAnalystChange(recommendation)) {\n                    const isUpgrade = recommendation.newGrade > recommendation.previousGrade;\n                    const catalyst = {\n                        id: `analyst_${symbol}_${recommendation.date}_${recommendation.analystCompany}`,\n                        symbol,\n                        type: isUpgrade ? 'analyst_upgrade' : 'analyst_downgrade',\n                        tier: 'tier_2',\n                        impact: isUpgrade ? 'bullish' : 'bearish',\n                        title: `${recommendation.analystCompany} ${isUpgrade ? 'Upgrades' : 'Downgrades'} ${symbol}`,\n                        description: `${recommendation.analystName} at ${recommendation.analystCompany} ${isUpgrade ? 'upgraded' : 'downgraded'} to ${recommendation.newGrade}`,\n                        source: 'FMP Analyst Data',\n                        announcementTime: recommendation.date,\n                        discoveredTime: new Date().toISOString(),\n                        qualityScore: this.calculateAnalystQualityScore(recommendation),\n                        freshness: this.calculateFreshness(recommendation.date),\n                        estimatedDuration: 'medium_term',\n                        verified: true,\n                        tags: [\n                            'analyst',\n                            isUpgrade ? 'upgrade' : 'downgrade',\n                            recommendation.analystCompany.toLowerCase()\n                        ],\n                        metadata: {\n                            analystCompany: recommendation.analystCompany,\n                            analystName: recommendation.analystName,\n                            previousGrade: recommendation.previousGrade,\n                            newGrade: recommendation.newGrade,\n                            priceTarget: recommendation.priceTarget\n                        }\n                    };\n                    catalysts.push(catalyst);\n                }\n            }\n        } catch (error) {\n            console.error(`Error detecting analyst catalysts for ${symbol}:`, error);\n        }\n        return catalysts;\n    }\n    /**\n   * Detect insider trading catalysts\n   */ async detectInsiderCatalysts(symbol) {\n        const catalysts = [];\n        try {\n            // Get insider trading data from FMP\n            const insiderData = await this.fmpAPI.getInsiderTrading(symbol, 30);\n            for (const trade of insiderData){\n                if (this.isSignificantInsiderTrade(trade)) {\n                    const isBuying = trade.transactionType.toLowerCase().includes('buy') || trade.transactionType.toLowerCase().includes('purchase');\n                    const catalyst = {\n                        id: `insider_${symbol}_${trade.filingDate}_${trade.reportingName}`,\n                        symbol,\n                        type: isBuying ? 'insider_buying' : 'insider_selling',\n                        tier: 'tier_2',\n                        impact: isBuying ? 'bullish' : 'bearish',\n                        title: `${trade.reportingName} ${isBuying ? 'Buys' : 'Sells'} ${symbol} Shares`,\n                        description: `${trade.reportingName} (${trade.typeOfOwner}) ${trade.transactionType} ${trade.securitiesTransacted} shares at $${trade.price}`,\n                        source: 'SEC Insider Trading Filings',\n                        announcementTime: trade.filingDate,\n                        discoveredTime: new Date().toISOString(),\n                        qualityScore: this.calculateInsiderQualityScore(trade),\n                        freshness: this.calculateFreshness(trade.filingDate),\n                        estimatedDuration: 'medium_term',\n                        verified: true,\n                        tags: [\n                            'insider',\n                            isBuying ? 'buying' : 'selling',\n                            trade.typeOfOwner.toLowerCase()\n                        ],\n                        metadata: {\n                            reportingName: trade.reportingName,\n                            typeOfOwner: trade.typeOfOwner,\n                            transactionType: trade.transactionType,\n                            securitiesTransacted: trade.securitiesTransacted,\n                            price: trade.price,\n                            dollarValue: trade.securitiesTransacted * trade.price\n                        }\n                    };\n                    catalysts.push(catalyst);\n                }\n            }\n        } catch (error) {\n            console.error(`Error detecting insider catalysts for ${symbol}:`, error);\n        }\n        return catalysts;\n    }\n    /**\n   * Detect SEC filing catalysts\n   */ async detectSECFilingCatalysts(symbol) {\n        const catalysts = [];\n        try {\n            // Get recent SEC filings from FMP\n            const filings = await this.fmpAPI.getSECFilings(symbol, 30);\n            for (const filing of filings){\n                if (this.isSignificantSECFiling(filing)) {\n                    const catalyst = {\n                        id: `sec_${symbol}_${filing.filedDate}_${filing.type}`,\n                        symbol,\n                        type: 'sec_filing',\n                        tier: this.getSECFilingTier(filing.type),\n                        impact: this.getSECFilingImpact(filing.type),\n                        title: `${symbol} Files ${filing.type}`,\n                        description: `${filing.type} filing: ${filing.description || 'SEC regulatory filing'}`,\n                        source: 'SEC EDGAR Database',\n                        sourceUrl: filing.link,\n                        announcementTime: filing.filedDate,\n                        discoveredTime: new Date().toISOString(),\n                        qualityScore: this.calculateSECFilingQualityScore(filing),\n                        freshness: this.calculateFreshness(filing.filedDate),\n                        estimatedDuration: this.estimateSECFilingDuration(filing.type),\n                        verified: true,\n                        tags: [\n                            'sec',\n                            'filing',\n                            filing.type.toLowerCase()\n                        ],\n                        metadata: {\n                            filingType: filing.type,\n                            cik: filing.cik,\n                            acceptedDate: filing.acceptedDate\n                        }\n                    };\n                    catalysts.push(catalyst);\n                }\n            }\n        } catch (error) {\n            console.error(`Error detecting SEC filing catalysts for ${symbol}:`, error);\n        }\n        return catalysts;\n    }\n    // Helper methods for catalyst classification and scoring\n    getFreshnessWeight(freshness) {\n        switch(freshness){\n            case 'fresh':\n                return 3;\n            case 'moderate':\n                return 2;\n            case 'stale':\n                return 1;\n            default:\n                return 0;\n        }\n    }\n    calculateFreshness(dateString) {\n        const date = new Date(dateString);\n        const now = new Date();\n        const hoursAgo = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n        if (hoursAgo < 24) return 'fresh';\n        if (hoursAgo < 72) return 'moderate';\n        return 'stale';\n    }\n    isEarningsBeat(earnings) {\n        return earnings.actualEPS > earnings.estimatedEPS && (earnings.guidanceRaised || earnings.actualEPS > earnings.estimatedEPS * 1.05);\n    }\n    calculateEarningsQualityScore(earnings) {\n        let score = 5 // Base score\n        ;\n        // Beat percentage\n        const beatPercent = (earnings.actualEPS - earnings.estimatedEPS) / earnings.estimatedEPS * 100;\n        if (beatPercent > 20) score += 3;\n        else if (beatPercent > 10) score += 2;\n        else if (beatPercent > 5) score += 1;\n        // Guidance raised\n        if (earnings.guidanceRaised) score += 2;\n        // Revenue beat\n        if (earnings.actualRevenue > earnings.estimatedRevenue) score += 1;\n        return Math.min(10, score);\n    }\n    classifyNewsAsCatalyst(news) {\n        const title = news.title.toLowerCase();\n        const text = (news.text || '').toLowerCase();\n        const content = title + ' ' + text;\n        // FDA/Drug related\n        if (content.includes('fda') && (content.includes('approval') || content.includes('approved'))) {\n            return {\n                type: 'fda_approval',\n                tier: 'tier_1',\n                impact: 'bullish'\n            };\n        }\n        if (content.includes('trial') && (content.includes('positive') || content.includes('successful'))) {\n            return {\n                type: 'drug_trial_results',\n                tier: 'tier_1',\n                impact: 'bullish'\n            };\n        }\n        // Contract/Partnership\n        if (content.includes('contract') && (content.includes('win') || content.includes('awarded'))) {\n            return {\n                type: 'contract_win',\n                tier: 'tier_1',\n                impact: 'bullish'\n            };\n        }\n        if (content.includes('partnership') || content.includes('collaboration')) {\n            return {\n                type: 'partnership',\n                tier: 'tier_1',\n                impact: 'bullish'\n            };\n        }\n        // M&A\n        if (content.includes('merger') || content.includes('acquisition') || content.includes('buyout')) {\n            return {\n                type: 'merger_acquisition',\n                tier: 'tier_1',\n                impact: 'bullish'\n            };\n        }\n        // Stock split\n        if (content.includes('stock split') || content.includes('share split')) {\n            return {\n                type: 'stock_split',\n                tier: 'tier_2',\n                impact: 'bullish'\n            };\n        }\n        return null;\n    }\n    calculateNewsQualityScore(news, catalystType) {\n        let score = 5 // Base score\n        ;\n        // Source reliability\n        if (this.isReliableNewsSource(news.site)) score += 2;\n        // Catalyst type importance\n        if ([\n            'fda_approval',\n            'merger_acquisition',\n            'earnings_beat_guidance'\n        ].includes(catalystType)) {\n            score += 2;\n        }\n        // Sentiment\n        if (news.sentiment === 'positive') score += 1;\n        else if (news.sentiment === 'negative') score -= 1;\n        return Math.max(1, Math.min(10, score));\n    }\n    isReliableNewsSource(site) {\n        const reliableSources = [\n            'reuters.com',\n            'bloomberg.com',\n            'wsj.com',\n            'cnbc.com',\n            'marketwatch.com',\n            'yahoo.com',\n            'sec.gov',\n            'fda.gov'\n        ];\n        return reliableSources.some((source)=>site.toLowerCase().includes(source));\n    }\n    extractNewsKeywords(text) {\n        const keywords = [];\n        const content = text.toLowerCase();\n        const keywordMap = {\n            'earnings': [\n                'earnings',\n                'eps',\n                'revenue',\n                'profit'\n            ],\n            'fda': [\n                'fda',\n                'approval',\n                'drug',\n                'trial'\n            ],\n            'merger': [\n                'merger',\n                'acquisition',\n                'buyout',\n                'takeover'\n            ],\n            'partnership': [\n                'partnership',\n                'collaboration',\n                'alliance'\n            ],\n            'contract': [\n                'contract',\n                'deal',\n                'agreement'\n            ],\n            'upgrade': [\n                'upgrade',\n                'raised',\n                'increased'\n            ],\n            'downgrade': [\n                'downgrade',\n                'lowered',\n                'reduced'\n            ]\n        };\n        for (const [category, terms] of Object.entries(keywordMap)){\n            if (terms.some((term)=>content.includes(term))) {\n                keywords.push(category);\n            }\n        }\n        return keywords;\n    }\n    estimateNewsDuration(catalystType) {\n        switch(catalystType){\n            case 'earnings_beat_guidance':\n            case 'fda_approval':\n            case 'merger_acquisition':\n                return 'short_term';\n            case 'analyst_upgrade':\n            case 'analyst_downgrade':\n            case 'partnership':\n                return 'medium_term';\n            case 'stock_split':\n                return 'long_term';\n            default:\n                return 'short_term';\n        }\n    }\n    isSignificantAnalystChange(recommendation) {\n        // Check if it's a meaningful grade change\n        const gradeChange = Math.abs(recommendation.newGrade - recommendation.previousGrade);\n        return gradeChange >= 1 && recommendation.priceTarget > 0;\n    }\n    calculateAnalystQualityScore(recommendation) {\n        let score = 5 // Base score\n        ;\n        // Analyst firm reputation (simplified)\n        const topFirms = [\n            'goldman sachs',\n            'morgan stanley',\n            'jp morgan',\n            'bank of america'\n        ];\n        if (topFirms.some((firm)=>recommendation.analystCompany.toLowerCase().includes(firm))) {\n            score += 2;\n        }\n        // Grade change magnitude\n        const gradeChange = Math.abs(recommendation.newGrade - recommendation.previousGrade);\n        if (gradeChange >= 2) score += 2;\n        else if (gradeChange >= 1) score += 1;\n        // Price target change\n        if (recommendation.priceTargetChange > 10) score += 1;\n        return Math.min(10, score);\n    }\n    isSignificantInsiderTrade(trade) {\n        const dollarValue = trade.securitiesTransacted * trade.price;\n        return dollarValue >= 1000000 && // $1M+ transactions\n        trade.typeOfOwner !== 'Other' // Exclude generic \"Other\" category\n        ;\n    }\n    calculateInsiderQualityScore(trade) {\n        let score = 5 // Base score\n        ;\n        const dollarValue = trade.securitiesTransacted * trade.price;\n        // Transaction size\n        if (dollarValue >= 10000000) score += 3; // $10M+\n        else if (dollarValue >= 5000000) score += 2; // $5M+\n        else if (dollarValue >= 1000000) score += 1; // $1M+\n        // Insider type\n        if (trade.typeOfOwner.toLowerCase().includes('ceo') || trade.typeOfOwner.toLowerCase().includes('cfo')) {\n            score += 2;\n        } else if (trade.typeOfOwner.toLowerCase().includes('director')) {\n            score += 1;\n        }\n        return Math.min(10, score);\n    }\n    isSignificantSECFiling(filing) {\n        const significantFilings = [\n            '8-K',\n            '10-K',\n            '10-Q',\n            '13D',\n            '13G',\n            'S-1',\n            'S-4'\n        ];\n        return significantFilings.includes(filing.type);\n    }\n    getSECFilingTier(filingType) {\n        const tier1Filings = [\n            '8-K',\n            '13D',\n            'S-4'\n        ] // Material events, activist investors, M&A\n        ;\n        const tier2Filings = [\n            '10-K',\n            '10-Q',\n            '13G'\n        ] // Regular reports, passive investors\n        ;\n        if (tier1Filings.includes(filingType)) return 'tier_1';\n        if (tier2Filings.includes(filingType)) return 'tier_2';\n        return 'tier_3';\n    }\n    getSECFilingImpact(filingType) {\n        // Most SEC filings are neutral until analyzed\n        return 'neutral';\n    }\n    calculateSECFilingQualityScore(filing) {\n        let score = 5 // Base score\n        ;\n        // Filing type importance\n        if ([\n            '8-K',\n            '13D'\n        ].includes(filing.type)) score += 2;\n        else if ([\n            '10-K',\n            '10-Q'\n        ].includes(filing.type)) score += 1;\n        return Math.min(10, score);\n    }\n    estimateSECFilingDuration(filingType) {\n        switch(filingType){\n            case '8-K':\n                return 'short_term' // Material events\n                ;\n            case '13D':\n                return 'medium_term' // Activist investors\n                ;\n            case 'S-4':\n                return 'long_term' // M&A registration\n                ;\n            default:\n                return 'medium_term';\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/catalystDetection.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/ibkr.ts":
/*!*************************!*\
  !*** ./src/lib/ibkr.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IBKRAPI: () => (/* binding */ IBKRAPI)\n/* harmony export */ });\n/* harmony import */ var _stoqey_ib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @stoqey/ib */ \"(rsc)/./node_modules/@stoqey/ib/dist/index.js\");\n/* harmony import */ var _stoqey_ib__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_stoqey_ib__WEBPACK_IMPORTED_MODULE_0__);\n\nclass IBKRAPI {\n    constructor(config){\n        this.connected = false;\n        this.nextOrderId = 1;\n        this.positions = new Map();\n        this.orders = new Map();\n        this.accountSummary = null;\n        this.config = config;\n        this.ib = new _stoqey_ib__WEBPACK_IMPORTED_MODULE_0__.IBApi({\n            host: config.host,\n            port: config.port,\n            clientId: config.clientId\n        });\n        this.setupEventHandlers();\n    }\n    setupEventHandlers() {\n        // Connection events\n        this.ib.on(_stoqey_ib__WEBPACK_IMPORTED_MODULE_0__.EventName.connected, ()=>{\n            console.log('✅ Connected to IBKR');\n            this.connected = true;\n            this.requestNextOrderId();\n            this.requestAccountSummary();\n            this.requestPositions();\n        });\n        this.ib.on(_stoqey_ib__WEBPACK_IMPORTED_MODULE_0__.EventName.disconnected, ()=>{\n            console.log('❌ Disconnected from IBKR');\n            this.connected = false;\n        });\n        this.ib.on(_stoqey_ib__WEBPACK_IMPORTED_MODULE_0__.EventName.error, (err, code, reqId)=>{\n            console.error(`IBKR Error ${code}:`, err);\n        });\n        // Order management\n        this.ib.on(_stoqey_ib__WEBPACK_IMPORTED_MODULE_0__.EventName.nextValidId, (orderId)=>{\n            this.nextOrderId = orderId;\n            console.log(`Next valid order ID: ${orderId}`);\n        });\n        this.ib.on(_stoqey_ib__WEBPACK_IMPORTED_MODULE_0__.EventName.orderStatus, (orderId, status, filled, remaining, avgFillPrice, permId, parentId, lastFillPrice, clientId, whyHeld, mktCapPrice)=>{\n            const order = this.orders.get(orderId);\n            if (order) {\n                order.status = status;\n                order.filled = filled;\n                order.remaining = remaining;\n                this.orders.set(orderId, order);\n            }\n        });\n        // Position updates\n        this.ib.on(_stoqey_ib__WEBPACK_IMPORTED_MODULE_0__.EventName.position, (account, contract, position, avgCost)=>{\n            const symbol = contract.symbol;\n            const existingPosition = this.positions.get(symbol) || {\n                symbol,\n                position: 0,\n                marketPrice: 0,\n                marketValue: 0,\n                averageCost: 0,\n                unrealizedPNL: 0,\n                realizedPNL: 0\n            };\n            existingPosition.position = position;\n            existingPosition.averageCost = avgCost;\n            this.positions.set(symbol, existingPosition);\n        });\n        // Account summary\n        this.ib.on(_stoqey_ib__WEBPACK_IMPORTED_MODULE_0__.EventName.accountSummary, (reqId, account, tag, value, currency)=>{\n            if (!this.accountSummary) {\n                this.accountSummary = {\n                    totalCashValue: 0,\n                    netLiquidation: 0,\n                    grossPositionValue: 0,\n                    availableFunds: 0,\n                    buyingPower: 0,\n                    unrealizedPnL: 0,\n                    realizedPnL: 0\n                };\n            }\n            switch(tag){\n                case 'TotalCashValue':\n                    this.accountSummary.totalCashValue = parseFloat(value);\n                    break;\n                case 'NetLiquidation':\n                    this.accountSummary.netLiquidation = parseFloat(value);\n                    break;\n                case 'GrossPositionValue':\n                    this.accountSummary.grossPositionValue = parseFloat(value);\n                    break;\n                case 'AvailableFunds':\n                    this.accountSummary.availableFunds = parseFloat(value);\n                    break;\n                case 'BuyingPower':\n                    this.accountSummary.buyingPower = parseFloat(value);\n                    break;\n                case 'UnrealizedPnL':\n                    this.accountSummary.unrealizedPnL = parseFloat(value);\n                    break;\n                case 'RealizedPnL':\n                    this.accountSummary.realizedPnL = parseFloat(value);\n                    break;\n            }\n        });\n    }\n    async connect() {\n        return new Promise((resolve, reject)=>{\n            if (this.connected) {\n                resolve();\n                return;\n            }\n            const timeout = setTimeout(()=>{\n                reject(new Error('Connection timeout'));\n            }, 10000);\n            this.ib.once(_stoqey_ib__WEBPACK_IMPORTED_MODULE_0__.EventName.connected, ()=>{\n                clearTimeout(timeout);\n                resolve();\n            });\n            this.ib.once(_stoqey_ib__WEBPACK_IMPORTED_MODULE_0__.EventName.error, (err)=>{\n                clearTimeout(timeout);\n                reject(err);\n            });\n            this.ib.connect();\n        });\n    }\n    disconnect() {\n        if (this.connected) {\n            this.ib.disconnect();\n        }\n    }\n    requestNextOrderId() {\n        this.ib.reqIds(1);\n    }\n    requestAccountSummary() {\n        this.ib.reqAccountSummary(1, 'All', 'TotalCashValue,NetLiquidation,GrossPositionValue,AvailableFunds,BuyingPower,UnrealizedPnL,RealizedPnL');\n    }\n    requestPositions() {\n        this.ib.reqPositions();\n    }\n    // Create a stock contract\n    createStockContract(symbol) {\n        return {\n            symbol: symbol.toUpperCase(),\n            secType: 'STK',\n            exchange: 'SMART',\n            currency: 'USD'\n        };\n    }\n    // Place a market order\n    async placeMarketOrder(symbol, action, quantity) {\n        if (!this.connected) {\n            throw new Error('Not connected to IBKR');\n        }\n        const contract = this.createStockContract(symbol);\n        const order = {\n            orderId: this.nextOrderId,\n            action,\n            totalQuantity: quantity,\n            orderType: 'MKT'\n        };\n        // Store order for tracking\n        this.orders.set(this.nextOrderId, {\n            orderId: this.nextOrderId,\n            symbol: symbol.toUpperCase(),\n            action,\n            quantity,\n            orderType: 'MKT',\n            status: 'Submitted',\n            filled: 0,\n            remaining: quantity\n        });\n        this.ib.placeOrder(this.nextOrderId, contract, order);\n        const orderId = this.nextOrderId;\n        this.nextOrderId++;\n        return orderId;\n    }\n    // Place a limit order\n    async placeLimitOrder(symbol, action, quantity, price) {\n        if (!this.connected) {\n            throw new Error('Not connected to IBKR');\n        }\n        const contract = this.createStockContract(symbol);\n        const order = {\n            orderId: this.nextOrderId,\n            action,\n            totalQuantity: quantity,\n            orderType: 'LMT',\n            lmtPrice: price\n        };\n        // Store order for tracking\n        this.orders.set(this.nextOrderId, {\n            orderId: this.nextOrderId,\n            symbol: symbol.toUpperCase(),\n            action,\n            quantity,\n            orderType: 'LMT',\n            price,\n            status: 'Submitted',\n            filled: 0,\n            remaining: quantity\n        });\n        this.ib.placeOrder(this.nextOrderId, contract, order);\n        const orderId = this.nextOrderId;\n        this.nextOrderId++;\n        return orderId;\n    }\n    // Place a stop-loss order\n    async placeStopOrder(symbol, action, quantity, stopPrice) {\n        if (!this.connected) {\n            throw new Error('Not connected to IBKR');\n        }\n        const contract = this.createStockContract(symbol);\n        const order = {\n            orderId: this.nextOrderId,\n            action,\n            totalQuantity: quantity,\n            orderType: 'STP',\n            auxPrice: stopPrice\n        };\n        // Store order for tracking\n        this.orders.set(this.nextOrderId, {\n            orderId: this.nextOrderId,\n            symbol: symbol.toUpperCase(),\n            action,\n            quantity,\n            orderType: 'STP',\n            price: stopPrice,\n            status: 'Submitted',\n            filled: 0,\n            remaining: quantity\n        });\n        this.ib.placeOrder(this.nextOrderId, contract, order);\n        const orderId = this.nextOrderId;\n        this.nextOrderId++;\n        return orderId;\n    }\n    // Cancel an order\n    async cancelOrder(orderId) {\n        if (!this.connected) {\n            throw new Error('Not connected to IBKR');\n        }\n        this.ib.cancelOrder(orderId);\n    }\n    // Get account summary\n    getAccountSummary() {\n        return this.accountSummary;\n    }\n    // Get all positions\n    getPositions() {\n        return Array.from(this.positions.values());\n    }\n    // Get position for specific symbol\n    getPosition(symbol) {\n        return this.positions.get(symbol.toUpperCase()) || null;\n    }\n    // Get all orders\n    getOrders() {\n        return Array.from(this.orders.values());\n    }\n    // Get specific order\n    getOrder(orderId) {\n        return this.orders.get(orderId) || null;\n    }\n    // Check if connected\n    isConnected() {\n        return this.connected;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/ibkr.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/indicators.ts":
/*!*******************************!*\
  !*** ./src/lib/indicators.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TechnicalIndicators: () => (/* binding */ TechnicalIndicators)\n/* harmony export */ });\nclass TechnicalIndicators {\n    // Simple Moving Average\n    static sma(data, period) {\n        const result = [];\n        for(let i = period - 1; i < data.length; i++){\n            const sum = data.slice(i - period + 1, i + 1).reduce((a, b)=>a + b, 0);\n            result.push(sum / period);\n        }\n        return result;\n    }\n    // Exponential Moving Average\n    static ema(data, period) {\n        const result = [];\n        const multiplier = 2 / (period + 1);\n        // Start with SMA for first value\n        let ema = data.slice(0, period).reduce((a, b)=>a + b, 0) / period;\n        result.push(ema);\n        for(let i = period; i < data.length; i++){\n            ema = data[i] * multiplier + ema * (1 - multiplier);\n            result.push(ema);\n        }\n        return result;\n    }\n    // Relative Strength Index\n    static rsi(data, period = 14) {\n        const gains = [];\n        const losses = [];\n        for(let i = 1; i < data.length; i++){\n            const change = data[i] - data[i - 1];\n            gains.push(change > 0 ? change : 0);\n            losses.push(change < 0 ? Math.abs(change) : 0);\n        }\n        const avgGains = this.sma(gains, period);\n        const avgLosses = this.sma(losses, period);\n        return avgGains.map((gain, i)=>{\n            const rs = gain / avgLosses[i];\n            return 100 - 100 / (1 + rs);\n        });\n    }\n    // MACD (Moving Average Convergence Divergence)\n    static macd(data, fastPeriod = 12, slowPeriod = 26, signalPeriod = 9) {\n        const fastEMA = this.ema(data, fastPeriod);\n        const slowEMA = this.ema(data, slowPeriod);\n        // Align arrays (slowEMA starts later)\n        const startIndex = slowPeriod - fastPeriod;\n        const macdLine = fastEMA.slice(startIndex).map((fast, i)=>fast - slowEMA[i]);\n        const signalLine = this.ema(macdLine, signalPeriod);\n        const histogram = macdLine.slice(signalPeriod - 1).map((macd, i)=>macd - signalLine[i]);\n        return {\n            macd: macdLine,\n            signal: signalLine,\n            histogram\n        };\n    }\n    // Bollinger Bands\n    static bollingerBands(data, period = 20, stdDev = 2) {\n        const sma = this.sma(data, period);\n        const bands = sma.map((avg, i)=>{\n            const slice = data.slice(i, i + period);\n            const variance = slice.reduce((sum, val)=>sum + Math.pow(val - avg, 2), 0) / period;\n            const standardDeviation = Math.sqrt(variance);\n            return {\n                upper: avg + standardDeviation * stdDev,\n                middle: avg,\n                lower: avg - standardDeviation * stdDev\n            };\n        });\n        return bands;\n    }\n    // Support and Resistance Levels\n    static findSupportResistance(candles, lookback = 20) {\n        const highs = candles.map((c)=>c.high);\n        const lows = candles.map((c)=>c.low);\n        const resistance = [];\n        const support = [];\n        for(let i = lookback; i < candles.length - lookback; i++){\n            const currentHigh = highs[i];\n            const currentLow = lows[i];\n            // Check if current high is a local maximum\n            const isResistance = highs.slice(i - lookback, i).every((h)=>h <= currentHigh) && highs.slice(i + 1, i + lookback + 1).every((h)=>h <= currentHigh);\n            // Check if current low is a local minimum\n            const isSupport = lows.slice(i - lookback, i).every((l)=>l >= currentLow) && lows.slice(i + 1, i + lookback + 1).every((l)=>l >= currentLow);\n            if (isResistance) resistance.push(currentHigh);\n            if (isSupport) support.push(currentLow);\n        }\n        return {\n            support,\n            resistance\n        };\n    }\n    // Volume analysis\n    static volumeAnalysis(candles, period = 20) {\n        const volumes = candles.map((c)=>c.volume);\n        const avgVolume = this.sma(volumes, period);\n        const currentVolume = volumes[volumes.length - 1];\n        const currentAvgVolume = avgVolume[avgVolume.length - 1];\n        return {\n            currentVolume,\n            averageVolume: currentAvgVolume,\n            volumeRatio: currentVolume / currentAvgVolume,\n            isHighVolume: currentVolume > currentAvgVolume * 1.5,\n            isLowVolume: currentVolume < currentAvgVolume * 0.5\n        };\n    }\n    // Swing Trading Analysis\n    static analyzeSwingSetup(candles) {\n        const closes = candles.map((c)=>c.close);\n        const indicators = [];\n        // RSI Analysis\n        const rsi = this.rsi(closes);\n        const currentRSI = rsi[rsi.length - 1];\n        let rsiSignal = 'NEUTRAL';\n        let rsiDescription = `RSI: ${currentRSI.toFixed(2)}`;\n        if (currentRSI < 30) {\n            rsiSignal = 'BUY';\n            rsiDescription += ' - Oversold condition, potential bounce';\n        } else if (currentRSI > 70) {\n            rsiSignal = 'SELL';\n            rsiDescription += ' - Overbought condition, potential pullback';\n        } else {\n            rsiDescription += ' - Neutral zone';\n        }\n        indicators.push({\n            name: 'RSI',\n            value: currentRSI,\n            signal: rsiSignal,\n            description: rsiDescription\n        });\n        // Moving Average Analysis\n        const sma20 = this.sma(closes, 20);\n        const sma50 = this.sma(closes, 50);\n        const currentPrice = closes[closes.length - 1];\n        const currentSMA20 = sma20[sma20.length - 1];\n        const currentSMA50 = sma50[sma50.length - 1];\n        let maSignal = 'NEUTRAL';\n        let maDescription = `Price vs SMA20: ${((currentPrice / currentSMA20 - 1) * 100).toFixed(2)}%`;\n        if (currentPrice > currentSMA20 && currentSMA20 > currentSMA50) {\n            maSignal = 'BUY';\n            maDescription += ' - Bullish trend';\n        } else if (currentPrice < currentSMA20 && currentSMA20 < currentSMA50) {\n            maSignal = 'SELL';\n            maDescription += ' - Bearish trend';\n        } else {\n            maDescription += ' - Mixed signals';\n        }\n        indicators.push({\n            name: 'Moving Averages',\n            value: (currentPrice / currentSMA20 - 1) * 100,\n            signal: maSignal,\n            description: maDescription\n        });\n        // MACD Analysis\n        const macdData = this.macd(closes);\n        const currentMACD = macdData.macd[macdData.macd.length - 1];\n        const currentSignal = macdData.signal[macdData.signal.length - 1];\n        const currentHistogram = macdData.histogram[macdData.histogram.length - 1];\n        let macdSignal = 'NEUTRAL';\n        let macdDescription = `MACD: ${currentMACD.toFixed(4)}, Signal: ${currentSignal.toFixed(4)}`;\n        if (currentMACD > currentSignal && currentHistogram > 0) {\n            macdSignal = 'BUY';\n            macdDescription += ' - Bullish momentum';\n        } else if (currentMACD < currentSignal && currentHistogram < 0) {\n            macdSignal = 'SELL';\n            macdDescription += ' - Bearish momentum';\n        } else {\n            macdDescription += ' - Momentum shifting';\n        }\n        indicators.push({\n            name: 'MACD',\n            value: currentHistogram,\n            signal: macdSignal,\n            description: macdDescription\n        });\n        // Volume Analysis\n        const volumeData = this.volumeAnalysis(candles);\n        let volumeSignal = 'NEUTRAL';\n        let volumeDescription = `Volume: ${(volumeData.volumeRatio * 100).toFixed(0)}% of average`;\n        if (volumeData.isHighVolume) {\n            volumeSignal = 'BUY';\n            volumeDescription += ' - High volume confirms move';\n        } else if (volumeData.isLowVolume) {\n            volumeSignal = 'SELL';\n            volumeDescription += ' - Low volume, weak conviction';\n        } else {\n            volumeDescription += ' - Normal volume';\n        }\n        indicators.push({\n            name: 'Volume',\n            value: volumeData.volumeRatio,\n            signal: volumeSignal,\n            description: volumeDescription\n        });\n        return indicators;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/indicators.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/perfectPickTradingSystem.ts":
/*!*********************************************!*\
  !*** ./src/lib/perfectPickTradingSystem.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PerfectPickTradingSystem: () => (/* binding */ PerfectPickTradingSystem)\n/* harmony export */ });\n/* harmony import */ var _preMarketGapScanner__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./preMarketGapScanner */ \"(rsc)/./src/lib/preMarketGapScanner.ts\");\n/* harmony import */ var _technicalGateAnalysis__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./technicalGateAnalysis */ \"(rsc)/./src/lib/technicalGateAnalysis.ts\");\n/* harmony import */ var _catalystDetection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./catalystDetection */ \"(rsc)/./src/lib/catalystDetection.ts\");\n/* harmony import */ var _polygon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./polygon */ \"(rsc)/./src/lib/polygon.ts\");\n\n\n\n\nclass PerfectPickTradingSystem {\n    constructor(polygonApiKey, useIBKR = true){\n        this.gapScanner = new _preMarketGapScanner__WEBPACK_IMPORTED_MODULE_0__.PreMarketGapScanner(polygonApiKey, useIBKR);\n        this.technicalAnalyzer = new _technicalGateAnalysis__WEBPACK_IMPORTED_MODULE_1__.TechnicalGateAnalysis(polygonApiKey);\n        this.catalystEngine = new _catalystDetection__WEBPACK_IMPORTED_MODULE_2__.CatalystDetectionEngine(polygonApiKey);\n        this.polygonAPI = new _polygon__WEBPACK_IMPORTED_MODULE_3__.PolygonAPI(polygonApiKey);\n    }\n    /**\n   * Run complete Perfect-Pick analysis pipeline\n   */ async runPerfectPickScan(accountSize = 100000, riskPercent = 2, customUniverse) {\n        console.log('🎯 Starting Perfect-Pick Trading System scan...');\n        try {\n            // Step 1: Pre-Market Gap Scan\n            console.log('📊 Running pre-market gap scan...');\n            const gapResults = await this.gapScanner.runGapScan(customUniverse);\n            // Filter for basic criteria first\n            const qualifiedGaps = gapResults.filter((gap)=>gap.gapPercent >= 3.0 && gap.gapPercent <= 15.0 && // Exclude over-extended gaps\n                gap.marketCap >= ********* && gap.price > 1.0);\n            console.log(`✅ Found ${qualifiedGaps.length} qualified gap candidates`);\n            if (qualifiedGaps.length === 0) {\n                return [];\n            }\n            // Step 2: Technical Gate Analysis\n            console.log('🔍 Running technical gate analysis...');\n            const symbols = qualifiedGaps.map((gap)=>gap.symbol);\n            const technicalAnalyses = await this.technicalAnalyzer.batchAnalyzeTechnicalGates(symbols);\n            // Filter for passing technical gates (Grade B or better)\n            const passingTechnical = technicalAnalyses.filter((analysis)=>[\n                    'A',\n                    'B'\n                ].includes(analysis.overallGrade) && analysis.aboveSMA200 && analysis.aboveEMA8);\n            console.log(`✅ ${passingTechnical.length} stocks passed technical gate`);\n            // Step 3: Combine and create Perfect-Pick setups\n            const perfectPickSetups = [];\n            for (const gapResult of qualifiedGaps){\n                const technicalAnalysis = passingTechnical.find((t)=>t.symbol === gapResult.symbol);\n                if (!technicalAnalysis) continue;\n                const catalyst = gapResult.catalyst;\n                if (!catalyst || !this.isValidCatalyst(catalyst)) continue;\n                // Create Perfect-Pick setup\n                const setup = await this.createPerfectPickSetup(gapResult, technicalAnalysis, catalyst, accountSize, riskPercent);\n                if (setup && this.validatePerfectPickSetup(setup)) {\n                    perfectPickSetups.push(setup);\n                }\n            }\n            // Sort by overall score\n            perfectPickSetups.sort((a, b)=>b.overallScore - a.overallScore);\n            console.log(`🎯 Generated ${perfectPickSetups.length} Perfect-Pick setups`);\n            return perfectPickSetups;\n        } catch (error) {\n            console.error('Error running Perfect-Pick scan:', error);\n            return [];\n        }\n    }\n    /**\n   * Create a complete Perfect-Pick setup\n   */ async createPerfectPickSetup(gapScan, technicalGate, catalyst, accountSize, riskPercent) {\n        try {\n            const symbol = gapScan.symbol;\n            const currentPrice = gapScan.price;\n            // Calculate risk management\n            const preMarketLow = gapScan.preMarketLow;\n            const stopLoss = preMarketLow * 0.99 // Slightly below PML for safety\n            ;\n            const riskPerShare = currentPrice - stopLoss;\n            if (riskPerShare <= 0) {\n                return null // Invalid risk setup\n                ;\n            }\n            const maxRiskAmount = accountSize * (riskPercent / 100);\n            const positionSize = Math.floor(maxRiskAmount / riskPerShare);\n            const maxPositionValue = accountSize * 0.05 // 5% max position size\n            ;\n            const maxShares = Math.floor(maxPositionValue / currentPrice);\n            const finalPositionSize = Math.min(positionSize, maxShares);\n            const actualRiskAmount = finalPositionSize * riskPerShare;\n            // Calculate reward targets (minimum 3:1 R/R required)\n            const target3R = currentPrice + riskPerShare * 3;\n            const target4R = currentPrice + riskPerShare * 4;\n            const target5R = currentPrice + riskPerShare * 5;\n            const riskRewardRatio = 3 // Minimum required\n            ;\n            // Check for exclusion reasons\n            const exclusionReasons = this.checkExclusionCriteria(gapScan, technicalGate, catalyst);\n            // Validation checks\n            const validationChecks = {\n                hasValidCatalyst: this.isValidCatalyst(catalyst),\n                meetsGapCriteria: gapScan.meetsAllCriteria,\n                passesTechnicalGate: [\n                    'A',\n                    'B'\n                ].includes(technicalGate.overallGrade),\n                hasEntryTrigger: true,\n                meetsRiskReward: riskRewardRatio >= 3,\n                noExclusionFlags: exclusionReasons.length === 0\n            };\n            // Calculate overall score\n            const overallScore = this.calculateOverallScore(gapScan, technicalGate, catalyst, validationChecks);\n            const setupGrade = this.calculateSetupGrade(overallScore);\n            const setup = {\n                symbol,\n                name: gapScan.name,\n                catalyst,\n                gapScan,\n                technicalGate,\n                riskManagement: {\n                    entryPrice: currentPrice,\n                    stopLoss,\n                    stopLossType: 'pre_market_low',\n                    riskPerShare,\n                    positionSize: finalPositionSize,\n                    accountRiskPercent: riskPercent,\n                    maxPositionPercent: 5\n                },\n                rewardPlanning: {\n                    riskRewardRatio,\n                    target3R,\n                    target4R,\n                    target5R,\n                    scaleOutPlan: [\n                        {\n                            level: 3,\n                            percentage: 25\n                        },\n                        {\n                            level: 4,\n                            percentage: 25\n                        },\n                        {\n                            level: 5,\n                            percentage: 25\n                        } // Take 25% at 5R, hold 25% for trend\n                    ]\n                },\n                overallScore,\n                setupGrade,\n                exclusionReasons,\n                validationChecks,\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            return setup;\n        } catch (error) {\n            console.error(`Error creating Perfect-Pick setup for ${gapScan.symbol}:`, error);\n            return null;\n        }\n    }\n    /**\n   * Generate intraday entry trigger\n   */ async generateEntryTrigger(symbol, preMarketHigh) {\n        try {\n            // Get current intraday data\n            const currentQuote = await this.fmpAPI.getStockQuote(symbol);\n            const currentPrice = currentQuote.price;\n            // Calculate VWAP (simplified - would need intraday data for accurate VWAP)\n            const vwap = currentPrice * 0.995 // Approximation\n            ;\n            // Determine entry signal type\n            let entrySignalType;\n            let urgency;\n            let conditions = [];\n            if (currentPrice > preMarketHigh) {\n                entrySignalType = 'pmh_break';\n                urgency = 'immediate';\n                conditions.push('Clean break above pre-market high');\n            } else if (currentPrice <= vwap && currentPrice > vwap * 0.98) {\n                entrySignalType = 'vwap_pullback';\n                urgency = 'wait_for_pullback';\n                conditions.push('Pullback to VWAP support');\n            } else {\n                entrySignalType = 'first_candle_close';\n                urgency = 'breakout_confirmation';\n                conditions.push('Wait for first 5-min candle close above PMH');\n            }\n            // Check volume confirmation (simplified)\n            const volumeConfirmation = currentQuote.volume > (currentQuote.volume || 0) * 1.5;\n            const vwapRising = true // Would need historical VWAP data to determine\n            ;\n            const noMajorResistance = true // Would need to check against resistance levels\n            ;\n            const trigger = {\n                symbol,\n                preMarketHigh,\n                preMarketLow: preMarketHigh * 0.95,\n                vwap,\n                entrySignalType,\n                entryPrice: currentPrice,\n                entryTime: new Date().toISOString(),\n                volumeConfirmation,\n                vwapRising,\n                noMajorResistance,\n                triggerValid: volumeConfirmation && vwapRising && noMajorResistance,\n                urgency,\n                conditions\n            };\n            return trigger;\n        } catch (error) {\n            console.error(`Error generating entry trigger for ${symbol}:`, error);\n            return null;\n        }\n    }\n    /**\n   * Validate catalyst quality and tier\n   */ isValidCatalyst(catalyst) {\n        // Tier 1 catalysts (highest priority)\n        const tier1Types = [\n            'earnings_beat_guidance',\n            'fda_approval',\n            'drug_trial_results',\n            'contract_win',\n            'partnership',\n            'merger_acquisition'\n        ];\n        // Tier 2 catalysts (secondary)\n        const tier2Types = [\n            'analyst_upgrade',\n            'stock_split',\n            'sector_rotation'\n        ];\n        const isValidType = tier1Types.includes(catalyst.type) || tier2Types.includes(catalyst.type);\n        const isFresh = catalyst.freshness === 'fresh' || catalyst.freshness === 'moderate';\n        const hasQuality = catalyst.qualityScore >= 6;\n        const isVerified = catalyst.verified;\n        return isValidType && isFresh && hasQuality && isVerified;\n    }\n    /**\n   * Check for exclusion criteria\n   */ checkExclusionCriteria(gapScan, technicalGate, catalyst) {\n        const exclusions = [];\n        // Anti-pattern filters\n        if (!technicalGate.dailyTrendConfirmed) {\n            exclusions.push('Stock not in confirmed daily uptrend');\n        }\n        if (!technicalGate.aboveSMA200) {\n            exclusions.push('Stock below 200-day SMA');\n        }\n        if (gapScan.gapPercent > 15) {\n            exclusions.push('Gap too extended (>15%)');\n        }\n        if (gapScan.averageDailyVolume < 500000) {\n            exclusions.push('Low liquidity (avg daily volume <500K)');\n        }\n        if (catalyst.impact === 'bearish') {\n            exclusions.push('Negative catalyst detected');\n        }\n        if (catalyst.freshness === 'stale') {\n            exclusions.push('Catalyst is stale (>72 hours old)');\n        }\n        return exclusions;\n    }\n    /**\n   * Calculate overall setup score (0-100)\n   */ calculateOverallScore(gapScan, technicalGate, catalyst, validationChecks) {\n        let score = 0;\n        // Gap quality (25 points max)\n        score += Math.min(25, gapScan.gapPercent * 2); // 3% gap = 6 points, 10% gap = 20 points\n        // Technical gate score (35 points max)\n        score += technicalGate.gateScore / 100 * 35;\n        // Catalyst quality (25 points max)\n        score += catalyst.qualityScore / 10 * 25;\n        // Validation bonus (15 points max)\n        const validationCount = Object.values(validationChecks).filter(Boolean).length;\n        score += validationCount / Object.keys(validationChecks).length * 15;\n        return Math.min(100, Math.round(score));\n    }\n    /**\n   * Calculate setup grade\n   */ calculateSetupGrade(score) {\n        if (score >= 95) return 'A+';\n        if (score >= 90) return 'A';\n        if (score >= 85) return 'B+';\n        if (score >= 80) return 'B';\n        if (score >= 75) return 'C+';\n        if (score >= 70) return 'C';\n        if (score >= 60) return 'D';\n        return 'F';\n    }\n    /**\n   * Validate complete Perfect-Pick setup\n   */ validatePerfectPickSetup(setup) {\n        const checks = setup.validationChecks;\n        // Must pass all critical checks\n        const criticalChecks = [\n            checks.hasValidCatalyst,\n            checks.meetsGapCriteria,\n            checks.passesTechnicalGate,\n            checks.meetsRiskReward,\n            checks.noExclusionFlags\n        ];\n        return criticalChecks.every((check)=>check) && setup.overallScore >= 70;\n    }\n    /**\n   * Get setup summary statistics\n   */ getSetupSummary(setups) {\n        const totalSetups = setups.length;\n        const gradeBreakdown = setups.reduce((acc, setup)=>{\n            acc[setup.setupGrade] = (acc[setup.setupGrade] || 0) + 1;\n            return acc;\n        }, {});\n        const catalystBreakdown = setups.reduce((acc, setup)=>{\n            acc[setup.catalyst.type] = (acc[setup.catalyst.type] || 0) + 1;\n            return acc;\n        }, {});\n        const avgScore = totalSetups > 0 ? setups.reduce((sum, setup)=>sum + setup.overallScore, 0) / totalSetups : 0;\n        const avgGap = totalSetups > 0 ? setups.reduce((sum, setup)=>sum + setup.gapScan.gapPercent, 0) / totalSetups : 0;\n        return {\n            totalSetups,\n            avgScore: Math.round(avgScore * 100) / 100,\n            avgGap: Math.round(avgGap * 100) / 100,\n            gradeBreakdown,\n            catalystBreakdown,\n            generatedAt: new Date().toISOString()\n        };\n    }\n    /**\n   * Update existing setups with current market data\n   */ async updatePerfectPickSetups(setups) {\n        const updatedSetups = [];\n        for (const setup of setups){\n            try {\n                // Get current quote\n                const currentQuote = await this.fmpAPI.getStockQuote(setup.symbol);\n                const currentPrice = currentQuote.price;\n                // Update entry trigger if needed\n                const entryTrigger = await this.generateEntryTrigger(setup.symbol, setup.gapScan.preMarketHigh);\n                // Update the setup\n                const updatedSetup = {\n                    ...setup,\n                    entryTrigger,\n                    riskManagement: {\n                        ...setup.riskManagement,\n                        entryPrice: currentPrice\n                    },\n                    updatedAt: new Date().toISOString()\n                };\n                updatedSetups.push(updatedSetup);\n            } catch (error) {\n                console.error(`Error updating setup for ${setup.symbol}:`, error);\n                updatedSetups.push(setup); // Keep original if update fails\n            }\n        }\n        return updatedSetups;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/perfectPickTradingSystem.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/polygon.ts":
/*!****************************!*\
  !*** ./src/lib/polygon.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PolygonAPI: () => (/* binding */ PolygonAPI),\n/* harmony export */   polygonAPI: () => (/* binding */ polygonAPI)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/lib/axios.js\");\n\nconst POLYGON_BASE_URL = 'https://api.polygon.io';\nconst API_KEY = process.env.POLYGON_API_KEY;\nclass PolygonAPI {\n    constructor(apiKey){\n        this.apiKey = apiKey || API_KEY || '';\n        if (!this.apiKey) {\n            throw new Error('Polygon API key is required');\n        }\n    }\n    // Get current stock quote using Polygon's snapshot endpoint (better for paid plans)\n    async getStockQuote(symbol) {\n        try {\n            // Use snapshot endpoint for real-time data (available on paid plans)\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${POLYGON_BASE_URL}/v2/snapshot/locale/us/markets/stocks/tickers/${symbol}`, {\n                params: {\n                    apikey: this.apiKey\n                }\n            });\n            if (!response.data || !response.data.ticker) {\n                throw new Error(`No data found for ${symbol}`);\n            }\n            const data = response.data.ticker;\n            // Extract data from Polygon snapshot response structure\n            const dayData = data.day || {};\n            const prevDayData = data.prevDay || {};\n            const minData = data.min || {};\n            // Use the most recent price available\n            const currentPrice = dayData.c || minData.c || prevDayData.c;\n            const prevClose = prevDayData.c;\n            const change = data.todaysChange || currentPrice - prevClose;\n            const changePercent = data.todaysChangePerc || change / prevClose * 100;\n            return {\n                symbol: symbol.toUpperCase(),\n                name: data.name || symbol.toUpperCase(),\n                price: currentPrice || 0,\n                change: change || 0,\n                changePercent: changePercent || 0,\n                volume: dayData.v || minData.v || 1000000,\n                marketCap: data.market_cap || this.estimateMarketCap(symbol, currentPrice || 0),\n                pe: undefined,\n                dividend: undefined\n            };\n        } catch (error) {\n            console.error('Error fetching stock quote from Polygon:', error);\n            // Fallback to previous day data if snapshot fails\n            try {\n                const fallbackResponse = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/prev`, {\n                    params: {\n                        adjusted: 'true',\n                        apikey: this.apiKey\n                    }\n                });\n                const data = fallbackResponse.data.results[0];\n                return {\n                    symbol: symbol.toUpperCase(),\n                    name: symbol.toUpperCase(),\n                    price: data.c || 0,\n                    change: data.c - data.o || 0,\n                    changePercent: data.o ? (data.c - data.o) / data.o * 100 : 0,\n                    volume: data.v || 1000000,\n                    marketCap: this.estimateMarketCap(symbol, data.c || 0),\n                    pe: undefined,\n                    dividend: undefined\n                };\n            } catch (fallbackError) {\n                console.error('Polygon fallback also failed:', fallbackError);\n                throw new Error(`Failed to fetch quote for ${symbol}`);\n            }\n        }\n    }\n    /**\n   * Estimate market cap based on symbol and price\n   * This is a fallback when Polygon doesn't provide market cap data\n   */ estimateMarketCap(symbol, price) {\n        // Import stock universe data for better estimates\n        const stockEstimates = {\n            // Large cap (>$200B)\n            'AAPL': 3000000000000,\n            'MSFT': 2*********000,\n            'NVDA': 1*********000,\n            'GOOGL': 1700000000000,\n            'GOOG': 1700000000000,\n            'AMZN': 1500000000000,\n            'TSLA': *********000,\n            'META': *********000,\n            'BRK.B': 900000000000,\n            // Mid-large cap ($50B-$200B)\n            'JPM': 500000000000,\n            'V': 500000000000,\n            'UNH': 500000000000,\n            'JNJ': 450000000000,\n            'XOM': 450000000000,\n            'WMT': 600000000000,\n            'PG': 400000000000,\n            'MA': 400000000000,\n            'HD': 350000000000,\n            'CVX': 300000000000,\n            'ABBV': 300000000000,\n            'BAC': 300000000000,\n            'COST': 350000000000,\n            'AVGO': 600000000000,\n            'TSM': 500000000000,\n            // Mid cap ($10B-$50B)\n            'NFLX': 200000000000,\n            'ORCL': 350000000000,\n            'CRM': 250000000000,\n            'ADBE': 220000000000,\n            'AMD': 220000000000,\n            'INTC': 200000000000,\n            'QCOM': 1*********00,\n            'TMO': 200000000000,\n            'DHR': 1*********00,\n            'CAT': 1*********00,\n            'GE': 1*********00,\n            'DIS': 1*********00,\n            'VZ': 170000000000,\n            'PFE': 160000000000,\n            'NKE': 150000000000,\n            'MS': 150000000000,\n            'UBER': 150000000000,\n            'C': 120000000000,\n            'GS': 120000000000,\n            'T': 120000000000,\n            'AMGN': 150000000000,\n            'HON': 140000000000,\n            'LOW': 150000000000,\n            'BMY': 120000000000,\n            'CMCSA': 150000000000,\n            'SBUX': 110000000000,\n            'MMM': 60000000000,\n            // Smaller cap but popular swing trading stocks\n            'PLTR': 60000000000,\n            'SHOP': *********00,\n            'GILD': *********00,\n            'TGT': 70000000000,\n            'COP': 150000000000,\n            'EOG': 70000000000,\n            'SLB': 60000000000,\n            'PYPL': 70000000000,\n            'SQ': 40000000000,\n            'COIN': 50000000000,\n            'DASH': 50000000000,\n            'MRNA': 30000000000,\n            'SNOW': 50000000000,\n            'ROKU': 5000000000,\n            'HOOD': 15000000000,\n            'LYFT': 6000000000,\n            'SPG': 50000000000,\n            'PLD': 120000000000,\n            'NEE': 150000000000\n        };\n        // Return estimated market cap if available, otherwise estimate based on price\n        if (stockEstimates[symbol]) {\n            return stockEstimates[symbol];\n        }\n        // Rough estimation based on price (very approximate)\n        if (price > 500) return 100000000000 // Assume large cap if high price\n        ;\n        if (price > 100) return 50000000000 // Assume mid-large cap\n        ;\n        if (price > 50) return 20000000000 // Assume mid cap\n        ;\n        if (price > 10) return 5000000000 // Assume small-mid cap\n        ;\n        return 1000000000 // Default to $1B minimum for scanning\n        ;\n    }\n    // Get historical candlestick data (optimized for paid plans)\n    async getHistoricalData(symbol, timespan = 'day', multiplier = 1, from, to) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/range/${multiplier}/${timespan}/${from}/${to}`, {\n                params: {\n                    adjusted: 'true',\n                    sort: 'asc',\n                    limit: 50000,\n                    apikey: this.apiKey\n                }\n            });\n            if (!response.data.results || response.data.results.length === 0) {\n                console.warn(`No historical data found for ${symbol}`);\n                return [];\n            }\n            return response.data.results.map((candle)=>({\n                    timestamp: candle.t,\n                    open: candle.o,\n                    high: candle.h,\n                    low: candle.l,\n                    close: candle.c,\n                    volume: candle.v\n                }));\n        } catch (error) {\n            console.error(`Error fetching historical data for ${symbol}:`, error);\n            // Log the specific error for debugging\n            if (error.response) {\n                console.error(`Polygon API Error: ${error.response.status} - ${error.response.statusText}`);\n                console.error('Response data:', error.response.data);\n            }\n            throw new Error(`Failed to fetch historical data for ${symbol}: ${error.message}`);\n        }\n    }\n    // Get company details\n    async getCompanyDetails(symbol) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${POLYGON_BASE_URL}/v3/reference/tickers/${symbol}`, {\n                params: {\n                    apikey: this.apiKey\n                }\n            });\n            return response.data.results;\n        } catch (error) {\n            console.error('Error fetching company details:', error);\n            return null;\n        }\n    }\n    // Get market status\n    async getMarketStatus() {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${POLYGON_BASE_URL}/v1/marketstatus/now`, {\n                params: {\n                    apikey: this.apiKey\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching market status:', error);\n            return null;\n        }\n    }\n    // Search for stocks\n    async searchStocks(query, limit = 10) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${POLYGON_BASE_URL}/v3/reference/tickers`, {\n                params: {\n                    search: query,\n                    market: 'stocks',\n                    active: 'true',\n                    limit,\n                    apikey: this.apiKey\n                }\n            });\n            return response.data.results || [];\n        } catch (error) {\n            console.error('Error searching stocks:', error);\n            return [];\n        }\n    }\n}\n// Create a singleton instance\nconst polygonAPI = new PolygonAPI();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/polygon.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/preMarketGapScanner.ts":
/*!****************************************!*\
  !*** ./src/lib/preMarketGapScanner.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PreMarketGapScanner: () => (/* binding */ PreMarketGapScanner)\n/* harmony export */ });\n/* harmony import */ var _polygon__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./polygon */ \"(rsc)/./src/lib/polygon.ts\");\n/* harmony import */ var _ibkr__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ibkr */ \"(rsc)/./src/lib/ibkr.ts\");\n/* harmony import */ var _catalystDetection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./catalystDetection */ \"(rsc)/./src/lib/catalystDetection.ts\");\n\n\n\nclass PreMarketGapScanner {\n    constructor(polygonApiKey, useIBKR = true){\n        // Default universe of 65+ stocks to scan\n        this.SCAN_UNIVERSE = [\n            // Mega caps\n            'AAPL',\n            'MSFT',\n            'GOOGL',\n            'GOOG',\n            'AMZN',\n            'NVDA',\n            'META',\n            'TSLA',\n            'BRK.B',\n            'UNH',\n            'JNJ',\n            'XOM',\n            'JPM',\n            'V',\n            'PG',\n            'HD',\n            'CVX',\n            'MA',\n            'BAC',\n            'ABBV',\n            'PFE',\n            'AVGO',\n            'KO',\n            'MRK',\n            'PEP',\n            'TMO',\n            'COST',\n            'DIS',\n            'ABT',\n            'ACN',\n            'MCD',\n            'CSCO',\n            'LIN',\n            'VZ',\n            'ADBE',\n            'WMT',\n            'CRM',\n            'NFLX',\n            'DHR',\n            'NKE',\n            'TXN',\n            'NEE',\n            'BMY',\n            'ORCL',\n            'PM',\n            'RTX',\n            'UPS',\n            'QCOM',\n            'T',\n            'LOW',\n            // High-beta growth stocks\n            'AMD',\n            'CRM',\n            'SNOW',\n            'PLTR',\n            'ROKU',\n            'ZM',\n            'DOCU',\n            'PTON',\n            'SHOP',\n            'SQ',\n            'PYPL',\n            'UBER',\n            'LYFT',\n            'ABNB',\n            'COIN',\n            'RBLX',\n            'U',\n            'DKNG',\n            'CRWD',\n            'ZS',\n            // Biotech/Pharma (catalyst-heavy)\n            'GILD',\n            'BIIB',\n            'REGN',\n            'VRTX',\n            'ILMN',\n            'MRNA',\n            'BNTX',\n            'AMGN',\n            'CELG',\n            'ISRG'\n        ];\n        this.polygonAPI = new _polygon__WEBPACK_IMPORTED_MODULE_0__.PolygonAPI(polygonApiKey);\n        this.ibkrAPI = new _ibkr__WEBPACK_IMPORTED_MODULE_1__.IBKRAPI({\n            host: '127.0.0.1',\n            port: 4002,\n            clientId: 1,\n            paperTrading: true\n        });\n        this.catalystEngine = new _catalystDetection__WEBPACK_IMPORTED_MODULE_2__.CatalystDetectionEngine(polygonApiKey);\n        this.useIBKR = useIBKR;\n    }\n    /**\n   * Run comprehensive pre-market gap scan\n   */ async runGapScan(customUniverse) {\n        const universe = customUniverse || this.SCAN_UNIVERSE;\n        const results = [];\n        console.log(`🔍 Starting pre-market gap scan on ${universe.length} symbols...`);\n        try {\n            let quotes = [];\n            if (this.useIBKR) {\n                console.log('📊 Attempting to use IBKR for market data...');\n                try {\n                    // Try to connect to IBKR first\n                    await this.ibkrAPI.connect();\n                    // Get quotes from IBKR\n                    quotes = await this.getIBKRQuotes(universe);\n                    if (quotes.length > 0) {\n                        console.log(`✅ Retrieved ${quotes.length} quotes from IBKR`);\n                    } else {\n                        throw new Error('No quotes received from IBKR');\n                    }\n                } catch (ibkrError) {\n                    console.warn('⚠️ IBKR connection failed, falling back to FMP:', ibkrError);\n                    this.useIBKR = false;\n                }\n            }\n            // Fallback to Polygon if IBKR failed or not enabled\n            if (!this.useIBKR || quotes.length === 0) {\n                console.log('📊 Using Polygon API for market data...');\n                quotes = await this.getPolygonQuotes(universe);\n            }\n            // Process each quote in parallel\n            const scanPromises = quotes.map((quote)=>this.processSingleStock(quote));\n            const scanResults = await Promise.all(scanPromises);\n            // Filter out null results and sort by gap percentage\n            const validResults = scanResults.filter((result)=>result !== null).sort((a, b)=>b.gapPercent - a.gapPercent);\n            console.log(`✅ Gap scan complete. Found ${validResults.length} results.`);\n            return validResults;\n        } catch (error) {\n            console.error('Error running gap scan:', error);\n            return [];\n        }\n    }\n    /**\n   * Get quotes from IBKR\n   */ async getIBKRQuotes(symbols) {\n        try {\n            const quotes = [];\n            // Process symbols in batches to avoid overwhelming IBKR\n            const batchSize = 10;\n            for(let i = 0; i < symbols.length; i += batchSize){\n                const batch = symbols.slice(i, i + batchSize);\n                const batchPromises = batch.map(async (symbol)=>{\n                    try {\n                        const quote = await this.ibkrAPI.getMarketData(symbol);\n                        if (quote) {\n                            return {\n                                symbol: quote.symbol,\n                                price: quote.last || quote.close,\n                                previousClose: quote.previousClose,\n                                change: quote.change,\n                                changesPercentage: quote.changePercent,\n                                volume: quote.volume,\n                                marketCap: quote.marketCap || 0,\n                                avgVolume: quote.avgVolume || 0,\n                                preMarketPrice: quote.last || quote.close,\n                                preMarketChange: quote.change,\n                                preMarketChangePercent: quote.changePercent\n                            };\n                        }\n                        return null;\n                    } catch (error) {\n                        console.error(`Error getting IBKR quote for ${symbol}:`, error);\n                        return null;\n                    }\n                });\n                const batchResults = await Promise.all(batchPromises);\n                quotes.push(...batchResults.filter((q)=>q !== null));\n                // Small delay between batches\n                if (i + batchSize < symbols.length) {\n                    await new Promise((resolve)=>setTimeout(resolve, 100));\n                }\n            }\n            return quotes;\n        } catch (error) {\n            console.error('Error getting IBKR quotes:', error);\n            return [];\n        }\n    }\n    /**\n   * Get quotes from Polygon API\n   */ async getPolygonQuotes(symbols) {\n        try {\n            const quotes = [];\n            // Process symbols in batches to avoid overwhelming Polygon\n            const batchSize = 10;\n            for(let i = 0; i < symbols.length; i += batchSize){\n                const batch = symbols.slice(i, i + batchSize);\n                const batchPromises = batch.map(async (symbol)=>{\n                    try {\n                        // Get current quote from Polygon\n                        const quote = await this.polygonAPI.getStockQuote(symbol);\n                        if (quote) {\n                            return {\n                                symbol: quote.symbol,\n                                price: quote.price,\n                                previousClose: quote.previousClose,\n                                change: quote.change,\n                                changesPercentage: quote.changePercent,\n                                volume: quote.volume,\n                                marketCap: quote.marketCap || 0,\n                                avgVolume: quote.avgVolume || 0,\n                                preMarketPrice: quote.price,\n                                preMarketChange: quote.change,\n                                preMarketChangePercent: quote.changePercent\n                            };\n                        }\n                        return null;\n                    } catch (error) {\n                        console.error(`Error getting Polygon quote for ${symbol}:`, error);\n                        return null;\n                    }\n                });\n                const batchResults = await Promise.all(batchPromises);\n                quotes.push(...batchResults.filter((q)=>q !== null));\n                // Small delay between batches to respect rate limits\n                if (i + batchSize < symbols.length) {\n                    await new Promise((resolve)=>setTimeout(resolve, 100));\n                }\n            }\n            return quotes;\n        } catch (error) {\n            console.error('Error getting Polygon quotes:', error);\n            return [];\n        }\n    }\n    /**\n   * Process a single stock for gap scan criteria\n   */ async processSingleStock(quote) {\n        try {\n            const symbol = quote.symbol;\n            const currentPrice = quote.preMarketPrice || quote.price;\n            const previousClose = quote.previousClose;\n            if (!currentPrice || !previousClose || previousClose <= 0) {\n                return null;\n            }\n            const gapPercent = (currentPrice - previousClose) / previousClose * 100;\n            // Quick filter: only process stocks with 3%+ gaps\n            if (gapPercent < 3.0) {\n                return null;\n            }\n            // Get additional data\n            const [companyProfile, catalysts] = await Promise.all([\n                this.fmpAPI.getCompanyProfile(symbol),\n                this.catalystEngine.detectCatalysts(symbol)\n            ]);\n            if (!companyProfile) {\n                return null;\n            }\n            // Calculate pre-market metrics\n            const preMarketVolume = quote.volume || 0;\n            const avgDailyVolume = quote.avgVolume || 1;\n            const preMarketDollarVolume = preMarketVolume * currentPrice;\n            const marketCap = quote.marketCap || companyProfile.mktCap || 0;\n            // Check all criteria\n            const criteriaChecks = {\n                priceAbove1Dollar: currentPrice > 1.0,\n                gapAbove3Percent: gapPercent >= 3.0,\n                marketCapAbove800M: marketCap >= *********,\n                preMarketVolumeAbove20K: preMarketVolume >= 20000,\n                preMarketDollarVolumeAbove1M: preMarketDollarVolume >= 1000000,\n                excludesPennyStocks: currentPrice > 1.0 && marketCap >= *********,\n                hasCatalyst: catalysts.length > 0\n            };\n            const meetsAllCriteria = Object.values(criteriaChecks).every((check)=>check);\n            // Get the best catalyst (highest quality score)\n            const bestCatalyst = catalysts.length > 0 ? catalysts.reduce((best, current)=>current.qualityScore > best.qualityScore ? current : best) : undefined;\n            const result = {\n                symbol,\n                name: companyProfile.companyName || symbol,\n                sector: companyProfile.sector || 'Unknown',\n                price: currentPrice,\n                previousClose,\n                gapPercent,\n                preMarketHigh: currentPrice,\n                preMarketLow: currentPrice * 0.98,\n                preMarketVolume,\n                preMarketDollarVolume,\n                marketCap,\n                averageDailyVolume: avgDailyVolume,\n                catalyst: bestCatalyst,\n                scanTime: new Date().toISOString(),\n                meetsAllCriteria,\n                criteriaChecks\n            };\n            return result;\n        } catch (error) {\n            console.error(`Error processing ${quote.symbol}:`, error);\n            return null;\n        }\n    }\n    /**\n   * Get filtered results that meet all Perfect-Pick criteria\n   */ async getPerfectPickCandidates(customUniverse) {\n        const allResults = await this.runGapScan(customUniverse);\n        return allResults.filter((result)=>result.meetsAllCriteria && result.catalyst && result.catalyst.tier === 'tier_1' // Only highest tier catalysts\n        );\n    }\n    /**\n   * Get results by gap percentage ranges\n   */ async getGapRangeResults(minGap = 3, maxGap = 15, customUniverse) {\n        const allResults = await this.runGapScan(customUniverse);\n        return allResults.filter((result)=>result.gapPercent >= minGap && result.gapPercent <= maxGap);\n    }\n    /**\n   * Get results by catalyst type\n   */ async getCatalystTypeResults(catalystTypes, customUniverse) {\n        const allResults = await this.runGapScan(customUniverse);\n        return allResults.filter((result)=>result.catalyst && catalystTypes.includes(result.catalyst.type));\n    }\n    /**\n   * Get scheduled scan times (4 AM, 6 AM, 8 AM, 9 AM EST)\n   */ getScheduledScanTimes() {\n        const now = new Date();\n        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n        const scanTimes = [\n            new Date(today.getTime() + 4 * 60 * 60 * 1000),\n            new Date(today.getTime() + 6 * 60 * 60 * 1000),\n            new Date(today.getTime() + 8 * 60 * 60 * 1000),\n            new Date(today.getTime() + 9 * 60 * 60 * 1000) // 9 AM EST\n        ];\n        // Adjust for EST (UTC-5) or EDT (UTC-4)\n        const isEDT = this.isDaylightSavingTime(now);\n        const offsetHours = isEDT ? 4 : 5;\n        return scanTimes.map((time)=>new Date(time.getTime() + offsetHours * 60 * 60 * 1000));\n    }\n    /**\n   * Check if current time is during daylight saving time\n   */ isDaylightSavingTime(date) {\n        const year = date.getFullYear();\n        // DST starts second Sunday in March\n        const dstStart = new Date(year, 2, 1) // March 1st\n        ;\n        dstStart.setDate(dstStart.getDate() + (7 - dstStart.getDay()) + 7); // Second Sunday\n        // DST ends first Sunday in November\n        const dstEnd = new Date(year, 10, 1) // November 1st\n        ;\n        dstEnd.setDate(dstEnd.getDate() + (7 - dstEnd.getDay())); // First Sunday\n        return date >= dstStart && date < dstEnd;\n    }\n    /**\n   * Get real-time updates for existing scan results\n   */ async updateScanResults(existingResults) {\n        const symbols = existingResults.map((result)=>result.symbol);\n        const updatedQuotes = await this.fmpAPI.getMultiplePreMarketQuotes(symbols);\n        const updatedResults = [];\n        for (const quote of updatedQuotes){\n            const existingResult = existingResults.find((r)=>r.symbol === quote.symbol);\n            if (!existingResult) continue;\n            const currentPrice = quote.preMarketPrice || quote.price;\n            const gapPercent = (currentPrice - quote.previousClose) / quote.previousClose * 100;\n            const updatedResult = {\n                ...existingResult,\n                price: currentPrice,\n                gapPercent,\n                preMarketVolume: quote.volume || 0,\n                preMarketDollarVolume: (quote.volume || 0) * currentPrice,\n                scanTime: new Date().toISOString()\n            };\n            // Re-check criteria with updated data\n            updatedResult.criteriaChecks.gapAbove3Percent = gapPercent >= 3.0;\n            updatedResult.criteriaChecks.preMarketVolumeAbove20K = updatedResult.preMarketVolume >= 20000;\n            updatedResult.criteriaChecks.preMarketDollarVolumeAbove1M = updatedResult.preMarketDollarVolume >= 1000000;\n            updatedResult.meetsAllCriteria = Object.values(updatedResult.criteriaChecks).every((check)=>check);\n            updatedResults.push(updatedResult);\n        }\n        return updatedResults.sort((a, b)=>b.gapPercent - a.gapPercent);\n    }\n    /**\n   * Get summary statistics for scan results\n   */ getScanSummary(results) {\n        const totalScanned = this.SCAN_UNIVERSE.length;\n        const gapsFound = results.length;\n        const perfectPicks = results.filter((r)=>r.meetsAllCriteria).length;\n        const withCatalysts = results.filter((r)=>r.catalyst).length;\n        const avgGap = results.length > 0 ? results.reduce((sum, r)=>sum + r.gapPercent, 0) / results.length : 0;\n        const sectorBreakdown = results.reduce((acc, result)=>{\n            acc[result.sector] = (acc[result.sector] || 0) + 1;\n            return acc;\n        }, {});\n        const catalystTypeBreakdown = results.filter((r)=>r.catalyst).reduce((acc, result)=>{\n            const type = result.catalyst.type;\n            acc[type] = (acc[type] || 0) + 1;\n            return acc;\n        }, {});\n        return {\n            totalScanned,\n            gapsFound,\n            perfectPicks,\n            withCatalysts,\n            avgGap: Math.round(avgGap * 100) / 100,\n            sectorBreakdown,\n            catalystTypeBreakdown,\n            scanTime: new Date().toISOString()\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/preMarketGapScanner.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/technicalGateAnalysis.ts":
/*!******************************************!*\
  !*** ./src/lib/technicalGateAnalysis.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TechnicalGateAnalysis: () => (/* binding */ TechnicalGateAnalysis)\n/* harmony export */ });\n/* harmony import */ var _indicators__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./indicators */ \"(rsc)/./src/lib/indicators.ts\");\n/* harmony import */ var _polygon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./polygon */ \"(rsc)/./src/lib/polygon.ts\");\n\n\nclass TechnicalGateAnalysis {\n    constructor(polygonApiKey){\n        this.polygonAPI = new _polygon__WEBPACK_IMPORTED_MODULE_1__.PolygonAPI(polygonApiKey);\n    }\n    /**\n   * Perform comprehensive technical gate analysis\n   */ async analyzeTechnicalGate(symbol) {\n        try {\n            // Get historical data (need at least 200 days for SMA200)\n            const historicalData = await this.getHistoricalData(symbol, 250);\n            if (!historicalData || historicalData.length < 200) {\n                console.error(`Insufficient data for ${symbol} - need at least 200 days`);\n                return null;\n            }\n            const currentPrice = historicalData[historicalData.length - 1].close;\n            // Calculate technical indicators\n            const sma200 = _indicators__WEBPACK_IMPORTED_MODULE_0__.TechnicalIndicators.calculateSMA(historicalData, 200);\n            const ema8 = _indicators__WEBPACK_IMPORTED_MODULE_0__.TechnicalIndicators.calculateEMA(historicalData, 8);\n            const vwap = this.calculateVWAP(historicalData.slice(-20)) // 20-day VWAP\n            ;\n            // Analyze trend confirmation\n            const dailyTrendConfirmed = this.analyzeDailyTrend(historicalData);\n            // Check moving average conditions\n            const aboveSMA200 = currentPrice > sma200[sma200.length - 1];\n            const aboveEMA8 = currentPrice > ema8[ema8.length - 1];\n            const respectsEMA8 = this.checkEMA8Respect(historicalData, ema8);\n            // Check for all-time high\n            const isAtAllTimeHigh = this.checkAllTimeHigh(historicalData);\n            // Check for clean breakout\n            const hasCleanBreakout = this.checkCleanBreakout(historicalData);\n            // Check volume expansion\n            const volumeExpansion = this.checkVolumeExpansion(historicalData);\n            // Calculate resistance and support levels\n            const resistanceLevels = this.calculateResistanceLevels(historicalData);\n            const supportLevels = this.calculateSupportLevels(historicalData);\n            // Calculate overall grade and score\n            const gateScore = this.calculateGateScore({\n                dailyTrendConfirmed,\n                aboveSMA200,\n                aboveEMA8,\n                respectsEMA8,\n                isAtAllTimeHigh,\n                hasCleanBreakout,\n                volumeExpansion\n            });\n            const overallGrade = this.calculateOverallGrade(gateScore);\n            const analysis = {\n                symbol,\n                dailyTrendConfirmed,\n                aboveSMA200,\n                aboveEMA8,\n                respectsEMA8,\n                isAtAllTimeHigh,\n                hasCleanBreakout,\n                volumeExpansion,\n                overallGrade,\n                gateScore,\n                resistanceLevels,\n                supportLevels,\n                keyTechnicalLevels: {\n                    sma200: sma200[sma200.length - 1],\n                    ema8: ema8[ema8.length - 1],\n                    vwap,\n                    previousHigh: Math.max(...historicalData.slice(-20).map((d)=>d.high)),\n                    previousLow: Math.min(...historicalData.slice(-20).map((d)=>d.low))\n                }\n            };\n            return analysis;\n        } catch (error) {\n            console.error(`Error analyzing technical gate for ${symbol}:`, error);\n            return null;\n        }\n    }\n    /**\n   * Get historical candlestick data\n   */ async getHistoricalData(symbol, days) {\n        try {\n            const endDate = new Date();\n            const startDate = new Date();\n            startDate.setDate(startDate.getDate() - days);\n            return await this.polygonAPI.getHistoricalData(symbol, startDate.toISOString().split('T')[0], endDate.toISOString().split('T')[0], '1', 'day');\n        } catch (error) {\n            console.error(`Error fetching historical data for ${symbol}:`, error);\n            return [];\n        }\n    }\n    /**\n   * Analyze daily trend confirmation (higher highs, higher lows over 20+ days)\n   */ analyzeDailyTrend(data) {\n        if (data.length < 20) return false;\n        const recent20Days = data.slice(-20);\n        const first10Days = recent20Days.slice(0, 10);\n        const last10Days = recent20Days.slice(10);\n        const firstPeriodHigh = Math.max(...first10Days.map((d)=>d.high));\n        const firstPeriodLow = Math.min(...first10Days.map((d)=>d.low));\n        const lastPeriodHigh = Math.max(...last10Days.map((d)=>d.high));\n        const lastPeriodLow = Math.min(...last10Days.map((d)=>d.low));\n        // Check for higher highs and higher lows\n        return lastPeriodHigh > firstPeriodHigh && lastPeriodLow > firstPeriodLow;\n    }\n    /**\n   * Check if stock consistently respects/reclaims 8-EMA\n   */ checkEMA8Respect(data, ema8) {\n        if (data.length < 20 || ema8.length < 20) return false;\n        const recent20Days = data.slice(-20);\n        const recent20EMA = ema8.slice(-20);\n        let respectCount = 0;\n        for(let i = 0; i < recent20Days.length; i++){\n            const candle = recent20Days[i];\n            const emaValue = recent20EMA[i];\n            // Check if low didn't break significantly below EMA8 (allow 2% cushion)\n            if (candle.low >= emaValue * 0.98) {\n                respectCount++;\n            }\n        }\n        // Stock respects EMA8 if it holds above it 70% of the time\n        return respectCount / recent20Days.length >= 0.7;\n    }\n    /**\n   * Check if stock is at or near all-time high\n   */ checkAllTimeHigh(data) {\n        const currentPrice = data[data.length - 1].close;\n        const allTimeHigh = Math.max(...data.map((d)=>d.high));\n        // Consider \"at ATH\" if within 5% of all-time high\n        return currentPrice >= allTimeHigh * 0.95;\n    }\n    /**\n   * Check for clean breakout from consolidation patterns\n   */ checkCleanBreakout(data) {\n        if (data.length < 30) return false;\n        const recent30Days = data.slice(-30);\n        const last5Days = recent30Days.slice(-5);\n        const consolidationPeriod = recent30Days.slice(-30, -5);\n        // Calculate consolidation range\n        const consolidationHigh = Math.max(...consolidationPeriod.map((d)=>d.high));\n        const consolidationLow = Math.min(...consolidationPeriod.map((d)=>d.low));\n        const consolidationRange = (consolidationHigh - consolidationLow) / consolidationLow;\n        // Check if recent price broke above consolidation with volume\n        const recentHigh = Math.max(...last5Days.map((d)=>d.high));\n        const recentVolume = last5Days.reduce((sum, d)=>sum + d.volume, 0) / last5Days.length;\n        const avgVolume = consolidationPeriod.reduce((sum, d)=>sum + d.volume, 0) / consolidationPeriod.length;\n        // Clean breakout criteria:\n        // 1. Consolidation range < 20% (tight consolidation)\n        // 2. Recent high > consolidation high\n        // 3. Volume expansion on breakout\n        return consolidationRange < 0.20 && recentHigh > consolidationHigh && recentVolume > avgVolume * 1.5;\n    }\n    /**\n   * Check for volume expansion on breakout days\n   */ checkVolumeExpansion(data) {\n        if (data.length < 20) return false;\n        const recent5Days = data.slice(-5);\n        const previous20Days = data.slice(-25, -5);\n        const recentAvgVolume = recent5Days.reduce((sum, d)=>sum + d.volume, 0) / recent5Days.length;\n        const historicalAvgVolume = previous20Days.reduce((sum, d)=>sum + d.volume, 0) / previous20Days.length;\n        // Volume expansion if recent volume is 150%+ of historical average\n        return recentAvgVolume > historicalAvgVolume * 1.5;\n    }\n    /**\n   * Calculate VWAP (Volume Weighted Average Price)\n   */ calculateVWAP(data) {\n        let totalVolume = 0;\n        let totalVolumePrice = 0;\n        for (const candle of data){\n            const typicalPrice = (candle.high + candle.low + candle.close) / 3;\n            totalVolumePrice += typicalPrice * candle.volume;\n            totalVolume += candle.volume;\n        }\n        return totalVolume > 0 ? totalVolumePrice / totalVolume : 0;\n    }\n    /**\n   * Calculate resistance levels using pivot highs\n   */ calculateResistanceLevels(data) {\n        const resistanceLevels = [];\n        const lookback = 5 // Look for pivots with 5 days on each side\n        ;\n        for(let i = lookback; i < data.length - lookback; i++){\n            const current = data[i];\n            let isPivotHigh = true;\n            // Check if current high is higher than surrounding highs\n            for(let j = i - lookback; j <= i + lookback; j++){\n                if (j !== i && data[j].high >= current.high) {\n                    isPivotHigh = false;\n                    break;\n                }\n            }\n            if (isPivotHigh) {\n                resistanceLevels.push(current.high);\n            }\n        }\n        // Return top 5 most recent resistance levels\n        return resistanceLevels.slice(-5).sort((a, b)=>b - a);\n    }\n    /**\n   * Calculate support levels using pivot lows\n   */ calculateSupportLevels(data) {\n        const supportLevels = [];\n        const lookback = 5;\n        for(let i = lookback; i < data.length - lookback; i++){\n            const current = data[i];\n            let isPivotLow = true;\n            // Check if current low is lower than surrounding lows\n            for(let j = i - lookback; j <= i + lookback; j++){\n                if (j !== i && data[j].low <= current.low) {\n                    isPivotLow = false;\n                    break;\n                }\n            }\n            if (isPivotLow) {\n                supportLevels.push(current.low);\n            }\n        }\n        // Return top 5 most recent support levels\n        return supportLevels.slice(-5).sort((a, b)=>b - a);\n    }\n    /**\n   * Calculate overall gate score (0-100)\n   */ calculateGateScore(conditions) {\n        let score = 0;\n        // Required conditions (higher weight)\n        if (conditions.dailyTrendConfirmed) score += 20;\n        if (conditions.aboveSMA200) score += 20;\n        if (conditions.aboveEMA8) score += 15;\n        // Premium conditions (bonus points)\n        if (conditions.respectsEMA8) score += 15;\n        if (conditions.isAtAllTimeHigh) score += 15;\n        if (conditions.hasCleanBreakout) score += 10;\n        if (conditions.volumeExpansion) score += 5;\n        return Math.min(100, score);\n    }\n    /**\n   * Calculate overall grade based on score\n   */ calculateOverallGrade(score) {\n        if (score >= 90) return 'A';\n        if (score >= 80) return 'B';\n        if (score >= 70) return 'C';\n        if (score >= 60) return 'D';\n        return 'F';\n    }\n    /**\n   * Batch analyze multiple symbols\n   */ async batchAnalyzeTechnicalGates(symbols) {\n        const results = [];\n        // Process in chunks to avoid API rate limits\n        const chunkSize = 5;\n        for(let i = 0; i < symbols.length; i += chunkSize){\n            const chunk = symbols.slice(i, i + chunkSize);\n            const chunkPromises = chunk.map((symbol)=>this.analyzeTechnicalGate(symbol));\n            const chunkResults = await Promise.all(chunkPromises);\n            results.push(...chunkResults.filter((result)=>result !== null));\n            // Small delay between chunks\n            if (i + chunkSize < symbols.length) {\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n            }\n        }\n        return results.sort((a, b)=>b.gateScore - a.gateScore);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/technicalGateAnalysis.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/@stoqey","vendor-chunks/rxjs","vendor-chunks/colors","vendor-chunks/eventemitter3","vendor-chunks/command-buffer","vendor-chunks/function-rate-limit"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fscanner%2Fperfect-pick%2Froute&page=%2Fapi%2Fscanner%2Fperfect-pick%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscanner%2Fperfect-pick%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();