{"version": 3, "sources": ["turbopack:///[project]/swing-trader-ai/src/lib/swingScanner.ts", "turbopack:///[project]/swing-trader-ai/node_modules/next/dist/esm/build/templates/app-route.js", "turbopack:///[project]/swing-trader-ai/src/app/api/scanner/quick/route.ts"], "sourcesContent": ["import { SwingTradingAnalyzer } from './swingAnalysis'\nimport { PolygonAPI } from './polygon'\nimport { FMPAPI } from './fmp'\nimport { SwingTradingAnalysis, StockData } from '@/types/trading'\nimport { format, subDays } from 'date-fns'\n\nexport interface ScanResult {\n  symbol: string\n  name: string\n  sector: string\n  quote: StockData\n  analysis: SwingTradingAnalysis\n  score: number\n  rank: number\n  scanTime: string\n}\n\nexport interface ScanSummary {\n  totalScanned: number\n  successfulScans: number\n  failedScans: number\n  topOpportunities: ScanResult[]\n  sectorBreakdown: Record<string, number>\n  scanDuration: number\n}\n\nexport class SwingTradingScanner {\n  private fmpAPI: FMPAPI\n  private polygonAPI: PolygonAPI\n\n  constructor() {\n    this.fmpAPI = new FMPAPI(process.env.FMP_API_KEY)\n    this.polygonAPI = new PolygonAPI(process.env.POLYGON_API_KEY)\n  }\n\n  // Main scanning function\n  async scanStocks(symbols: string[], maxConcurrent: number = 5): Promise<ScanSummary> {\n    const startTime = Date.now()\n    const results: ScanResult[] = []\n    const failed: string[] = []\n    const sectorBreakdown: Record<string, number> = {}\n\n    console.log(`Starting scan of ${symbols.length} stocks...`)\n\n    // Process stocks in batches to avoid rate limits\n    for (let i = 0; i < symbols.length; i += maxConcurrent) {\n      const batch = symbols.slice(i, i + maxConcurrent)\n      const batchPromises = batch.map(symbol => this.scanSingleStock(symbol))\n      \n      const batchResults = await Promise.allSettled(batchPromises)\n      \n      batchResults.forEach((result, index) => {\n        const symbol = batch[index]\n        if (result.status === 'fulfilled' && result.value) {\n          results.push(result.value)\n          const sector = result.value.sector\n          sectorBreakdown[sector] = (sectorBreakdown[sector] || 0) + 1\n        } else {\n          failed.push(symbol)\n          console.warn(`Failed to scan ${symbol}:`, result.status === 'rejected' ? result.reason : 'Unknown error')\n        }\n      })\n\n      // Add delay between batches to respect rate limits\n      if (i + maxConcurrent < symbols.length) {\n        await new Promise(resolve => setTimeout(resolve, 1000))\n      }\n    }\n\n    // Sort by score and assign ranks\n    results.sort((a, b) => b.score - a.score)\n    results.forEach((result, index) => {\n      result.rank = index + 1\n    })\n\n    const scanDuration = Date.now() - startTime\n\n    return {\n      totalScanned: symbols.length,\n      successfulScans: results.length,\n      failedScans: failed.length,\n      topOpportunities: results.slice(0, 20), // Top 20 opportunities\n      sectorBreakdown,\n      scanDuration\n    }\n  }\n\n  // Scan a single stock\n  private async scanSingleStock(symbol: string): Promise<ScanResult | null> {\n    try {\n      // Get stock quote and historical data in parallel\n      const [quote, historicalData] = await Promise.all([\n        this.fmpAPI.getStockQuote(symbol),\n        this.getHistoricalData(symbol)\n      ])\n\n      if (!historicalData || historicalData.length < 50) {\n        throw new Error('Insufficient historical data')\n      }\n\n      // Perform swing trading analysis\n      const analysis = SwingTradingAnalyzer.analyzeSwingTrade(symbol, historicalData)\n      \n      // Calculate swing trading score\n      const score = this.calculateSwingScore(quote, analysis)\n\n      return {\n        symbol,\n        name: quote.name,\n        sector: this.getSectorForSymbol(symbol),\n        quote,\n        analysis,\n        score,\n        rank: 0, // Will be set after sorting\n        scanTime: new Date().toISOString()\n      }\n    } catch (error) {\n      console.error(`Error scanning ${symbol}:`, error)\n      return null\n    }\n  }\n\n  // Get historical data with fallback\n  private async getHistoricalData(symbol: string) {\n    const to = format(new Date(), 'yyyy-MM-dd')\n    const from = format(subDays(new Date(), 100), 'yyyy-MM-dd')\n\n    try {\n      // Try Polygon first\n      return await this.polygonAPI.getHistoricalData(symbol, 'day', 1, from, to)\n    } catch (error) {\n      console.warn(`Polygon failed for ${symbol}, trying alternative...`)\n      // Could add FMP historical data as fallback here\n      throw error\n    }\n  }\n\n  // Calculate swing trading score (0-100)\n  private calculateSwingScore(quote: StockData, analysis: SwingTradingAnalysis): number {\n    let score = 0\n\n    // Base confidence score (0-40 points)\n    score += analysis.confidence * 0.4\n\n    // Risk/Reward ratio bonus (0-20 points)\n    const rrRatio = analysis.riskRewardRatio\n    if (rrRatio >= 3) score += 20\n    else if (rrRatio >= 2) score += 15\n    else if (rrRatio >= 1.5) score += 10\n    else if (rrRatio >= 1) score += 5\n\n    // Volume factor (0-15 points)\n    const volumeIndicator = analysis.indicators.find(ind => ind.name === 'Volume')\n    if (volumeIndicator) {\n      if (volumeIndicator.signal === 'BUY' && volumeIndicator.value > 1.5) score += 15\n      else if (volumeIndicator.signal === 'BUY') score += 10\n      else if (volumeIndicator.signal === 'NEUTRAL') score += 5\n    }\n\n    // Trend strength bonus (0-15 points)\n    if (analysis.trend === 'BULLISH') {\n      score += 15\n    } else if (analysis.trend === 'BEARISH') {\n      score += 10 // Bearish can also be good for short opportunities\n    } else {\n      score += 5 // Sideways trends can be good for range trading\n    }\n\n    // Technical indicator alignment (0-10 points)\n    const bullishSignals = analysis.indicators.filter(ind => ind.signal === 'BUY').length\n    const bearishSignals = analysis.indicators.filter(ind => ind.signal === 'SELL').length\n    const totalSignals = analysis.indicators.length\n\n    if (bullishSignals > bearishSignals) {\n      score += (bullishSignals / totalSignals) * 10\n    } else if (bearishSignals > bullishSignals) {\n      score += (bearishSignals / totalSignals) * 8 // Slightly less for bearish\n    }\n\n    // Price momentum bonus/penalty (0-10 points)\n    const changePercent = Math.abs(quote.changePercent)\n    if (changePercent > 5) score += 10 // High momentum\n    else if (changePercent > 2) score += 7\n    else if (changePercent > 1) score += 5\n    else if (changePercent < 0.5) score -= 5 // Low momentum penalty\n\n    // Recommendation bonus\n    switch (analysis.recommendation) {\n      case 'STRONG_BUY':\n        score += 10\n        break\n      case 'BUY':\n        score += 7\n        break\n      case 'STRONG_SELL':\n        score += 8 // Good for short opportunities\n        break\n      case 'SELL':\n        score += 5\n        break\n      case 'NO_TRADE':\n        score -= 10\n        break\n    }\n\n    return Math.max(0, Math.min(100, score))\n  }\n\n  // Get sector for symbol (simplified mapping)\n  private getSectorForSymbol(symbol: string): string {\n    const techSymbols = ['MSFT', 'NVDA', 'GOOG', 'GOOGL', 'META', 'AVGO', 'TSM', 'ORCL', 'CSCO', 'AMD', 'ASML', 'MU', 'LRCX', 'PLTR', 'APP', 'NET', 'DDOG', 'ZS', 'SHOP', 'SOUN', 'IONQ', 'RGTI', 'RIOT', 'HUT', 'IREN', 'ASTS', 'NBIS']\n    const financialSymbols = ['JPM', 'BAC', 'MS', 'SCHW', 'C', 'HOOD', 'SOFI', 'TIGR', 'FUTU']\n    const healthcareSymbols = ['JNJ', 'ABBV', 'MRK', 'GILD']\n    const industrialSymbols = ['GE', 'CAT', 'BA', 'GEV', 'UAL', 'VRT', 'RKLB']\n    const materialsSymbols = ['AEM', 'NEM', 'PAAS', 'BTG', 'HL', 'MP', 'AG']\n    const consumerSymbols = ['AMZN', 'DIS', 'SBUX', 'MO', 'DASH', 'GM', 'NCLH', 'CELH', 'LEVI', 'ELF', 'ETSY', 'W']\n    const communicationSymbols = ['NFLX', 'RBLX', 'BILI']\n    const energySymbols = ['CEG', 'VST', 'CCJ']\n\n    if (techSymbols.includes(symbol)) return 'Technology'\n    if (financialSymbols.includes(symbol)) return 'Financial Services'\n    if (healthcareSymbols.includes(symbol)) return 'Healthcare'\n    if (industrialSymbols.includes(symbol)) return 'Industrial'\n    if (materialsSymbols.includes(symbol)) return 'Materials'\n    if (consumerSymbols.includes(symbol)) return 'Consumer'\n    if (communicationSymbols.includes(symbol)) return 'Communication Services'\n    if (energySymbols.includes(symbol)) return 'Energy'\n    \n    return 'Other'\n  }\n\n  // Quick scan of priority stocks\n  async quickScan(prioritySymbols: string[]): Promise<ScanResult[]> {\n    const summary = await this.scanStocks(prioritySymbols, 8)\n    return summary.topOpportunities\n  }\n}\n\n// Create singleton instance\nexport const swingScanner = new SwingTradingScanner()\n", "import { AppRouteRouteModule } from \"next/dist/esm/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/esm/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/esm/server/lib/patch-fetch\";\nimport { getRequestMeta } from \"next/dist/esm/server/request-meta\";\nimport { getTracer, SpanKind } from \"next/dist/esm/server/lib/trace/tracer\";\nimport { normalizeAppPath } from \"next/dist/esm/shared/lib/router/utils/app-paths\";\nimport { NodeNextRequest, NodeNextResponse } from \"next/dist/esm/server/base-http/node\";\nimport { NextRequestAdapter, signalFromNodeResponse } from \"next/dist/esm/server/web/spec-extension/adapters/next-request\";\nimport { BaseServerSpan } from \"next/dist/esm/server/lib/trace/constants\";\nimport { getRevalidateReason } from \"next/dist/esm/server/instrumentation/utils\";\nimport { sendResponse } from \"next/dist/esm/server/send-response\";\nimport { fromNodeOutgoingHttpHeaders, toNodeOutgoingHttpHeaders } from \"next/dist/esm/server/web/utils\";\nimport { getCacheControlHeader } from \"next/dist/esm/server/lib/cache-control\";\nimport { INFINITE_CACHE, NEXT_CACHE_TAGS_HEADER } from \"next/dist/esm/lib/constants\";\nimport { NoFallbackError } from \"next/dist/esm/shared/lib/no-fallback-error.external\";\nimport { CachedRouteKind } from \"next/dist/esm/server/response-cache\";\nimport * as userland from \"INNER_APP_ROUTE\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/scanner/quick/route\",\n        pathname: \"/api/scanner/quick\",\n        filename: \"route\",\n        bundlePath: \"\"\n    },\n    distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n    relativeProjectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || '',\n    resolvedPagePath: \"[project]/swing-trader-ai/src/app/api/scanner/quick/route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\nexport async function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/scanner/quick/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (process.env.TURBOPACK) {\n        srcPage = srcPage.replace(/\\/index$/, '') || '/';\n    } else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = process.env.__NEXT_MULTI_ZONE_DRAFT_MODE;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = normalizeAppPath(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = getTracer();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: getRequestMeta(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new NodeNextRequest(req);\n    const nodeNextRes = new NodeNextResponse(res);\n    const nextReq = NextRequestAdapter.fromNodeNextRequest(nodeNextReq, signalFromNodeResponse(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!getRequestMeta(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = toNodeOutgoingHttpHeaders(response.headers);\n                        if (cacheTags) {\n                            headers[NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await sendResponse(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: getRevalidateReason({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!getRequestMeta(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = fromNodeOutgoingHttpHeaders(cacheEntry.value.headers);\n            if (!(getRequestMeta(req, 'minimalMode') && isIsr)) {\n                headers.delete(NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', getCacheControlHeader(cacheEntry.cacheControl));\n            }\n            await sendResponse(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        if (!(err instanceof NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: getRevalidateReason({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await sendResponse(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n", "import { NextRequest, NextResponse } from 'next/server'\nimport { swingScanner } from '@/lib/swingScanner'\nimport { PRIORITY_SYMBOLS } from '@/data/watchlist'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const limit = parseInt(searchParams.get('limit') || '10')\n    \n    console.log(`Starting quick scan of ${PRIORITY_SYMBOLS.length} priority stocks...`)\n    \n    const results = await swingScanner.quickScan(PRIORITY_SYMBOLS)\n    \n    // Limit results if requested\n    const limitedResults = results.slice(0, limit)\n    \n    return NextResponse.json({\n      totalScanned: PRIORITY_SYMBOLS.length,\n      results: limitedResults,\n      scanTime: new Date().toISOString()\n    })\n  } catch (error) {\n    console.error('Error in quick scanner API:', error)\n    return NextResponse.json(\n      { error: 'Failed to perform quick stock scan' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": "mEAAA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,OA2OO,IAAM,EAAe,IAAI,AArNzB,MAAM,AACH,MAAc,AACd,WAAsB,AAE9B,cAAc,CACZ,IAAI,CAAC,MAAM,CAAG,IAAI,EAAA,MAAM,CAAC,QAAQ,GAAG,CAAC,WAAW,EAChD,IAAI,CAAC,UAAU,CAAG,IAAI,EAAA,UAAU,CAAC,QAAQ,GAAG,CAAC,eAAe,CAC9D,CAGA,MAAM,WAAW,CAAiB,CAAE,EAAwB,CAAC,CAAwB,CACnF,IAAM,EAAY,KAAK,GAAG,GACpB,EAAwB,EAAE,CAC1B,EAAmB,EAAE,CACrB,EAA0C,CAAC,EAEjD,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,EAAQ,MAAM,CAAC,UAAU,CAAC,EAG1D,IAAK,IAAI,EAAI,EAAG,EAAI,EAAQ,MAAM,CAAE,GAAK,EAAe,CACtD,IAAM,EAAQ,EAAQ,KAAK,CAAC,EAAG,EAAI,GAC7B,EAAgB,EAAM,GAAG,CAAC,GAAU,IAAI,CAAC,eAAe,CAAC,IAI/D,CAFqB,MAAM,QAAQ,UAAU,CAAC,EAAA,EAEjC,OAAO,CAAC,CAAC,EAAQ,KAC5B,IAAM,EAAS,CAAK,CAAC,EAAM,CAC3B,GAAsB,cAAlB,EAAO,MAAM,EAAoB,EAAO,KAAK,CAAE,CACjD,EAAQ,IAAI,CAAC,EAAO,KAAK,EACzB,IAAM,EAAS,EAAO,KAAK,CAAC,MAAM,CAClC,CAAe,CAAC,EAAO,CAAG,AAAC,EAAe,CAAC,EAAO,GAAI,CAAC,CAAI,CAC7D,MACE,CADK,CACE,IAAI,CAAC,GACZ,QAAQ,IAAI,CAAC,CAAC,eAAe,EAAE,EAAO,CAAC,CAAC,CAAoB,aAAlB,EAAO,MAAM,CAAkB,EAAO,MAAM,CAAG,gBAE7F,GAGI,EAAI,EAAgB,EAAQ,MAAM,EAAE,AACtC,MAAM,IAAI,QAAQ,GAAW,WAAW,EAAS,KAErD,CAGA,EAAQ,IAAI,CAAC,CAAC,EAAG,IAAM,EAAE,KAAK,CAAG,EAAE,KAAK,EACxC,EAAQ,OAAO,CAAC,CAAC,EAAQ,KACvB,EAAO,IAAI,CAAG,EAAQ,CACxB,GAEA,IAAM,EAAe,KAAK,GAAG,GAAK,EAElC,MAAO,CACL,aAAc,EAAQ,MAAM,CAC5B,gBAAiB,EAAQ,MAAM,CAC/B,YAAa,EAAO,MAAM,CAC1B,iBAAkB,EAAQ,KAAK,CAAC,EAAG,oBACnC,eACA,CACF,CACF,CAGA,MAAc,gBAAgB,CAAc,CAA8B,CACxE,GAAI,CAEF,GAAM,CAAC,EAAO,EAAe,CAAG,MAAM,QAAQ,GAAG,CAAC,CAChD,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,GAC1B,IAAI,CAAC,iBAAiB,CAAC,GACxB,EAED,GAAI,CAAC,GAAkB,EAAe,MAAM,CAAG,GAC7C,CADiD,KAC3C,AAAI,MAAM,gCAIlB,IAAM,EAAW,EAAA,oBAAoB,CAAC,iBAAiB,CAAC,EAAQ,GAG1D,EAAQ,IAAI,CAAC,mBAAmB,CAAC,EAAO,GAE9C,MAAO,QACL,EACA,KAAM,EAAM,IAAI,CAChB,OAAQ,IAAI,CAAC,kBAAkB,CAAC,SAChC,WACA,QACA,EACA,KAAM,EACN,SAAU,IAAI,OAAO,WAAW,EAClC,CACF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,CAAC,eAAe,EAAE,EAAO,CAAC,CAAC,CAAE,GACpC,IACT,CACF,CAGA,MAAc,kBAAkB,CAAc,CAAE,CAC9C,IAAM,EAAK,CAAA,EAAA,EAAA,MAAA,AAAM,EAAC,IAAI,KAAQ,cACxB,EAAO,CAAA,EAAA,EAAA,MAAM,AAAN,EAAO,CAAA,EAAA,EAAA,OAAA,AAAO,EAAC,IAAI,KAAQ,KAAM,cAE9C,GAAI,CAEF,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,EAAQ,MAAO,EAAG,EAAM,EACzE,CAAE,MAAO,EAAO,CAGd,MAFA,QAAQ,IAAI,CAAC,CAAC,mBAAmB,EAAE,EAAO,uBAAuB,CAAC,EAE5D,CACR,CACF,CAGQ,oBAAoB,CAAgB,CAAE,CAA8B,CAAU,KAChF,EAGJ,EAHY,EAGmB,GAAtB,EAAS,UAAU,CAG5B,IAAM,EAAU,EAAS,eAAe,CACpC,GAAW,EAAG,GAAS,GAClB,GAAW,EAAG,GAAS,GACvB,GAAW,IAAK,GAAS,GACzB,GAAW,GAAG,KAAS,EAGhC,IAAM,EAAkB,EAAS,UAAU,CAAC,IAAI,CAAC,GAAoB,WAAb,EAAI,IAAI,EAC5D,IAC6B,QAA3B,EAAgB,GADD,GACO,EAAc,EAAgB,KAAK,CAAG,IAAK,GAAS,GAC1C,QAA3B,EAAgB,MAAM,CAAY,GAAS,GAC3C,AAA2B,cAAX,MAAM,GAAgB,IAAS,GAInC,WAAW,CAA9B,EAAS,KAAK,CAChB,GAAS,GACmB,WAAW,CAA9B,EAAS,KAAK,CACvB,GAAS,GAET,CAFY,EAEH,EAIX,CAJa,GAIP,EAAiB,EAAS,UAAU,CAAC,MAAM,CAAC,GAAO,AAAe,UAAX,MAAM,EAAY,AANd,EAEJ,IAIwB,CAC/E,EAAiB,EAAS,UAAU,CAAC,MAAM,CAAC,GAAsB,SAAf,EAAI,MAAM,EAAa,MAAM,CAChF,EAAe,EAAS,UAAU,CAAC,MAErC,AAF2C,GAE1B,EACnB,GAAU,EAAiB,EAAgB,GAClC,EAAiB,EAFS,EAGnC,GAAU,EAAiB,GAAgB,EAI7C,CAJ+C,CADH,EAKtC,EAAgB,KAAK,GAAG,CAAC,EAAM,YAJsC,CAIzB,EAOlD,OANI,EAAgB,EAAG,GAAS,GACvB,CAD0B,CACV,EAAG,GAAS,EAC5B,EAAgB,EAAG,GAAS,CAFc,CAG1C,EAAgB,KAAK,IAAS,EAG/B,CAHiC,CAGxB,cAAc,EAC7B,IAAK,EAJyD,WAK5D,GAAS,GACT,KACF,KAAK,MACH,GAAS,EACT,KACF,KAAK,cACH,GAAS,EACT,CADW,IAEb,KAAK,OACH,GAAS,EACT,KACF,KAAK,AALuC,WAM1C,GAAS,EAEb,CAEA,OAAO,KAAK,GAAG,CAAC,EAAG,KAAK,GAAG,CAAC,IAAK,GACnC,CAGQ,mBAAmB,CAAc,CAAU,OAUjD,AAAI,AATgB,CAAC,OAAQ,OAAQ,OAAQ,QAAS,OAAQ,OAAQ,MAAO,OAAQ,OAAQ,MAAO,OAAQ,KAAM,OAAQ,OAAQ,MAAO,MAAO,OAAQ,KAAM,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,MAAO,OAAQ,OAAQ,OAAO,CASpN,QAAQ,CAAC,GAAgB,MAAP,OAC9B,AATqB,CAAC,MAAO,MAAO,KAAM,OAAQ,IAAK,OAAQ,OAAQ,OAAQ,OAAO,CASrE,QAAQ,CAAC,GAAgB,MAAP,eARb,AAStB,CATuB,MAAO,OAAQ,MAAO,OAAO,CASlC,QAAQ,CAAC,GAAgB,MAAP,OACpC,AATsB,CAAC,KAAM,MAAO,KAAM,MAAO,MAAO,MAAO,OAAO,CASpD,QAAQ,CAAC,GAAgB,MAAP,OARf,AASrB,CATsB,MAAO,MAAO,OAAQ,MAAO,KAAM,KAAM,KAAK,CASnD,QAAQ,CAAC,GAAgB,MAAP,MARf,AASpB,CATqB,OAAQ,MAAO,OAAQ,KAAM,OAAQ,KAAM,OAAQ,OAAQ,OAAQ,MAAO,OAAQ,IAAI,CAS3F,QAAQ,CAAC,GAAgB,MAAP,KAClC,AATyB,CAAC,OAAQ,OAAQ,OAAO,CAS5B,QAAQ,CAAC,GAAgB,MAAP,mBACvC,AATkB,CAAC,MAAO,MAAO,MAAM,CASzB,QAAQ,CAAC,GAAgB,MAAP,GAE7B,OACT,CAGA,MAAM,UAAU,CAAyB,CAAyB,CAEhE,MAAO,CADS,MAAM,IAAI,CAAC,UAAU,CAAC,EAAiB,EAAA,EACxC,gBACjB,AADiC,CAEnC,yLC5OA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,CAAA,CAAA,OAAA,IAAA,EAAA,EAAA,CAAA,CAAA,+BCfA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MAEO,eAAe,EAAI,CAAoB,EAC5C,GAAI,CACF,GAAM,cAAE,CAAY,CAAE,CAAG,IAAI,IAAI,EAAQ,GAAG,EACtC,EAAQ,SAAS,EAAa,GAAG,CAAC,UAAY,MAEpD,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,EAAA,gBAAgB,CAAC,MAAM,CAAC,mBAAmB,CAAC,EAKlF,IAAM,EAAiB,CAHP,MAAM,EAAA,YAAY,CAAC,SAAS,CAAC,EAAA,iBAAgB,EAG9B,KAAK,CAAC,EAAG,GAExC,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,CACvB,aAAc,EAAA,gBAAgB,CAAC,MAAM,CACrC,QAAS,EACT,SAAU,IAAI,OAAO,WAAW,EAClC,EACF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,8BAA+B,GACtC,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,MAAO,oCAAqC,EAC9C,CAAE,OAAQ,GAAI,EAElB,CACF,CDZA,IAAA,EAAA,EAAA,CAAA,CAAA,OAIA,IAAM,EAAc,IAAI,EAAA,mBAAmB,CAAC,CACxC,WAAY,CACR,KAAM,EAAA,SAAS,CAAC,SAAS,CACzB,KAAM,2BACN,SAAU,qBACV,SAAU,QACV,WAAY,EAChB,EACA,QAAS,CAAA,OACT,IADiD,eACc,CAA3C,EACpB,iBAAkB,+DAClB,iBAZqB,GAarB,SAAA,CACJ,GAIM,kBAAE,CAAgB,sBAAE,CAAoB,aAAE,CAAW,CAAE,CAAG,EAChE,SAAS,IACL,MAAO,CAAA,EAAA,EAAA,UAAA,AAAW,EAAC,kBACf,uBACA,CACJ,EACJ,CAEO,eAAe,EAAQ,CAAG,CAAE,CAAG,CAAE,CAAG,EACvC,IAAI,EACJ,IAAI,EAAU,2BAKV,EAAU,EAAQ,OAAO,CAAC,WAAY,KAAO,IAMjD,IAAM,EAAgB,MAAM,EAAY,OAAO,CAAC,EAAK,EAAK,SACtD,EACA,mBAHE,CAAA,CAIN,GACA,GAAI,CAAC,EAID,OAHA,EAAI,IADY,MACF,CAAG,IACjB,EAAI,GAAG,CAAC,eACS,MAAjB,CAAwB,CAApB,IAAyB,KAAhB,EAAoB,EAAI,SAAS,CAAC,IAAI,CAAC,EAAK,QAAQ,OAAO,IACjE,KAEX,GAAM,SAAE,CAAO,QAAE,CAAM,YAAE,CAAU,CAAE,aAAW,mBAAE,CAAiB,qBAAE,CAAmB,sBAAE,CAAoB,yBAAE,CAAuB,kBAAE,CAAgB,CAAE,CAAG,EACxJ,EAAoB,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,GACvC,EAAQ,GAAQ,EAAkB,aAAa,CAAC,EAAkB,EAAI,EAAkB,MAAM,CAAC,EAAA,AAAiB,EACpH,GAAI,GAAS,CAAC,EAAa,CACvB,IAAM,GAAgB,CAAQ,EAAkB,MAAM,CAAC,EAAiB,CAClE,EAAgB,EAAkB,aAAa,CAAC,EAAkB,CACxE,GAAI,IAC+B,IAA3B,EAAc,KADH,GACW,EAAc,CAAC,EACrC,MAAM,IAAI,EAAA,CAD0C,cAC3B,AAGrC,CACA,IAAI,EAAW,MACX,GAAU,EAAY,IAAb,CAAkB,EAAK,EAAD,EAG/B,EAAW,AAAa,OAHqB,KAC7C,EAAW,CAAA,EAEwB,IAAM,CAAA,EAE7C,IAAM,EACgB,AAAtB,OAAY,CAAkB,IAAb,EAEjB,CAAC,EAKK,EAAe,GAAS,CAAC,EACzB,EAAS,EAAI,MAAM,EAAI,MACvB,EAAS,CAAA,EAAA,EAAA,SAAA,AAAS,IAClB,EAAa,EAAO,UAVyE,QAUvD,GACtC,EAAU,QACZ,oBACA,EACA,WAAY,CACR,aAAc,CACV,gBAAiB,EAAQ,EAAW,YAAY,CAAC,eAAe,CAChE,gBAAgB,CAAQ,EAAW,YAAY,CAAC,cAAc,AAClE,0BACA,EACA,iBAAkB,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,oBACtC,kBAA2E,AAAxD,OAAC,EAA2B,EAAW,YAAA,AAAY,EAAY,KAAK,EAAI,EAAyB,SAAS,cAC7H,EACA,UAAW,EAAI,SAAS,CACxB,QAAS,AAAC,IACN,EAAI,EAAE,CAAC,QAAS,EACpB,EACA,sBAAkB,EAClB,8BAA+B,CAAC,EAAO,EAAU,IAAe,EAAY,cAAc,CAAC,EAAK,EAAO,EAAc,EACzH,EACA,cAAe,CACX,SACJ,CACJ,EACM,EAAc,IAAI,EAAA,eAAe,CAAC,GAClC,EAAc,IAAI,EAAA,gBAAgB,CAAC,GACnC,EAAU,EAAA,kBAAkB,CAAC,mBAAmB,CAAC,EAAa,CAAA,EAAA,EAAA,sBAAA,AAAsB,EAAC,IAC3F,GAAI,CACA,IAAM,EAAoB,MAAO,GACtB,EAAY,MAAM,CAAC,EAAS,GAAS,OAAO,CAAC,KAChD,GAAI,CAAC,EAAM,OACX,EAAK,aAAa,CAAC,CACf,mBAAoB,EAAI,UAAU,CAClC,YAAY,CAChB,GACA,IAAM,EAAqB,EAAO,qBAAqB,GAEvD,GAAI,CAAC,EACD,OAEJ,GAAI,EAAmB,GAAG,CAAC,EAHF,kBAGwB,EAAA,cAAc,CAAC,aAAa,CAAE,YAC3E,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,EAAmB,GAAG,CAAC,kBAAkB,qEAAqE,CAAC,EAG9J,IAAM,EAAQ,EAAmB,GAAG,CAAC,cACrC,GAAI,EAAO,CACP,IAAM,EAAO,CAAA,EAAG,EAAO,CAAC,EAAE,EAAA,CAAO,CACjC,EAAK,aAAa,CAAC,CACf,aAAc,EACd,aAAc,EACd,iBAAkB,CACtB,GACA,EAAK,UAAU,CAAC,EACpB,MACI,CADG,CACE,UAAU,CAAC,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAE9C,GAEE,EAAiB,MAAO,QACtB,EA0FI,EAzFR,IAAM,EAAoB,MAAO,oBAAE,CAAkB,CAAE,IACnD,GAAI,CACA,GAAI,CAAC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GAAwB,GAA2B,CAAC,EAK3F,OAJA,EAAI,SAD2G,CACjG,CAAG,IAEjB,EAAI,SAAS,CAAC,iBAAkB,eAChC,EAAI,GAAG,CAAC,gCACD,KAEX,IAAM,EAAW,MAAM,EAAkB,GACzC,EAAI,YAAY,CAAG,EAAQ,UAAU,CAAC,YAAY,CAClD,IAAI,EAAmB,EAAQ,UAAU,CAAC,gBAAgB,CAGtD,GACI,EAAI,SAAS,EAAE,CACf,CAFc,CAEV,SAAS,CAAC,GACd,OAAmB,GAG3B,IAAM,EAAY,EAAQ,UAAU,CAAC,aAAa,CAGlD,IAAI,EA6BA,OADA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,EAAU,EAAQ,UAAU,CAAC,gBAAgB,EACnF,IA7BA,EACP,IAAM,EAAO,MAAM,EAAS,IAAI,GAE1B,EAAU,CAAA,EAAA,EAAA,yBAAyB,AAAzB,EAA0B,EAAS,OAAO,EACtD,IACA,CAAO,CAAC,EAAA,GADG,mBACmB,CAAC,CAAG,CAAA,EAElC,CAAC,CAAO,CAAC,eAAe,EAAI,EAAK,IAAI,EAAE,CACvC,CAAO,CAAC,eAAe,CAAG,EAAK,IAAA,AAAI,EAEvC,IAAM,EAAa,KAAkD,IAA3C,EAAQ,UAAU,CAAC,mBAAmB,IAAoB,EAAQ,UAAU,CAAC,mBAAmB,EAAI,EAAA,cAAA,AAAc,GAAW,AAAR,EAAgB,UAAU,CAAC,mBAAmB,CACvL,EAAS,KAA8C,IAAvC,EAAQ,UAAU,CAAC,eAAe,EAAoB,EAAQ,UAAU,CAAC,eAAe,EAAI,EAAA,cAAc,MAAG,EAAY,EAAQ,UAAU,CAAC,eAAe,CAcjL,MAZmB,CAYZ,AAXH,MAAO,CACH,KAAM,EAAA,eAAe,CAAC,SAAS,CAC/B,OAAQ,EAAS,MAAM,CACvB,KAAM,OAAO,IAAI,CAAC,MAAM,EAAK,WAAW,IACxC,SACJ,EACA,aAAc,YACV,SACA,CACJ,CACJ,CAEJ,CAKJ,CAAE,KALS,CAKF,EAAK,CAcV,MAX0B,MAAtB,EAA6B,KAAK,EAAI,EAAmB,OAAA,AAAO,EAAE,CAClE,MAAM,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,cAClC,uBACA,CACJ,EACJ,EAAG,GAED,CACV,CACJ,EACM,EAAa,MAAM,EAAY,cAAc,CAAC,KAChD,aACA,WACA,EACA,UAAW,EAAA,SAAS,CAAC,SAAS,CAC9B,YAAY,oBACZ,EACA,mBAAmB,uBACnB,EACA,0BACA,oBACA,UAAW,EAAI,SAAS,AAC5B,GAEA,GAAI,CAAC,EACD,KADQ,EACD,KAEX,GAAI,CAAe,MAAd,CAAqB,EAAS,AAA0C,GAA9C,IAAK,EAAoB,EAAW,KAAA,AAAK,EAAY,KAAK,EAAI,EAAkB,IAAI,IAAM,EAAA,eAAe,CAAC,SAAS,CAE9I,CAFgJ,KAE1I,OAAO,cAAc,CAAC,AAAI,MAAM,CAAC,kDAAkD,EAAgB,MAAd,CAAqB,EAAS,AAA2C,GAA/C,IAAK,EAAqB,EAAW,KAAK,AAAL,EAAiB,KAAK,EAAI,EAAmB,IAAI,CAAA,CAAE,EAAG,oBAAqB,CACjO,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,EAEA,CAAC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAgB,AACrC,EAAI,SAAS,CAAC,iBAAkB,EAAuB,cAAgB,EAAW,MAAM,CAAG,OAAS,EAAW,OAAO,CAAG,QAAU,OAGnI,GACA,EAAI,QADS,CACA,CAAC,gBAAiB,2DAEnC,IAAM,EAAU,CAAA,EAAA,EAAA,2BAAA,AAA2B,EAAC,EAAW,KAAK,CAAC,OAAO,EAapE,MAZM,AAAF,CAAE,AAAD,EAAC,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GACxC,EAD6C,AACrC,GADwC,GAClC,CAAC,EAAA,sBAAsB,GAIrC,EAAW,YAAY,EAAK,EAAI,AAAL,SAAc,CAAC,kBAAqB,EAAD,AAAS,GAAG,CAAC,kBAAkB,AAC7F,EAAQ,GAAG,CAAC,gBAAiB,CAAA,EAAA,EAAA,qBAAA,AAAqB,EAAC,EAAW,YAAY,GAE9E,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,EAAW,KAAK,CAAC,IAAI,CAAE,SAC7E,EACA,OAAQ,EAAW,KAAK,CAAC,MAAM,EAAI,GACvC,IACO,IACX,EAGI,EACA,MAAM,EAAe,EADT,CAGZ,MAAM,EAAO,qBAAqB,CAAC,EAAI,OAAO,CAAE,IAAI,EAAO,KAAK,CAAC,EAAA,cAAc,CAAC,aAAa,CAAE,CACvF,SAAU,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAChC,KAAM,EAAA,QAAQ,CAAC,MAAM,CACrB,WAAY,CACR,cAAe,EACf,cAAe,EAAI,GAAG,AAC1B,CACJ,EAAG,GAEf,CAAE,MAAO,EAAK,CAcV,GAbI,AAAE,CAAD,YAAgB,EAAA,eAAe,EAChC,CADmC,KAC7B,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,cAClC,uBACA,CACJ,EACJ,GAIA,EAAO,MAAM,EAKjB,OAHA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,KAAM,CAC5D,OAAQ,GACZ,IACO,IACX,CACJ,EAEA,qCAAqC", "ignoreList": [1]}