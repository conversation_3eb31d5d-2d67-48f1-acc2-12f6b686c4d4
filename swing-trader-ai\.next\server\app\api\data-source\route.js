var R=require("../../../chunks/[turbopack]_runtime.js")("server/app/api/data-source/route.js")
R.c("server/chunks/6bf44_next_4959be0a._.js")
R.c("server/chunks/6bf44_@stoqey_ib_dist_07ed8ba7._.js")
R.c("server/chunks/6bf44_rxjs_dist_cjs_5c50c2dc._.js")
R.c("server/chunks/6bf44_axios_lib_4f26ea03._.js")
R.c("server/chunks/6bf44_mime-db_6bff638e._.js")
R.c("server/chunks/6bf44_bd348202._.js")
R.c("server/chunks/[root-of-the-server]__8985979b._.js")
R.m("[project]/swing-trader-ai/.next-internal/server/app/api/data-source/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/swing-trader-ai/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/swing-trader-ai/src/app/api/data-source/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/swing-trader-ai/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/swing-trader-ai/src/app/api/data-source/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
