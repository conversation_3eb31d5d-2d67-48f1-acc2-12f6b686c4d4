var R=require("../../../../../chunks/[turbopack]_runtime.js")("server/app/api/scanner/sector/[sector]/route.js")
R.c("server/chunks/6bf44_next_b88fbc49._.js")
R.c("server/chunks/6bf44_tailwind-merge_dist_bundle-mjs_mjs_f1284d17._.js")
R.c("server/chunks/6bf44_axios_lib_4f26ea03._.js")
R.c("server/chunks/6bf44_mime-db_6bff638e._.js")
R.c("server/chunks/6bf44_cb9740ed._.js")
R.c("server/chunks/[root-of-the-server]__a6e3ed8f._.js")
R.m("[project]/swing-trader-ai/.next-internal/server/app/api/scanner/sector/[sector]/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/swing-trader-ai/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/swing-trader-ai/src/app/api/scanner/sector/[sector]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/swing-trader-ai/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/swing-trader-ai/src/app/api/scanner/sector/[sector]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
