/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/data-source/route";
exports.ids = ["app/api/data-source/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdata-source%2Froute&page=%2Fapi%2Fdata-source%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdata-source%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdata-source%2Froute&page=%2Fapi%2Fdata-source%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdata-source%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var C_Users_SJFit_Desktop_shittyidea_swing_trader_ai_src_app_api_data_source_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/data-source/route.ts */ \"(rsc)/./src/app/api/data-source/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/data-source/route\",\n        pathname: \"/api/data-source\",\n        filename: \"route\",\n        bundlePath: \"app/api/data-source/route\"\n    },\n    distDir: \".next\" || 0,\n    relativeProjectDir:  false || '',\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\shittyidea\\\\swing-trader-ai\\\\src\\\\app\\\\api\\\\data-source\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_SJFit_Desktop_shittyidea_swing_trader_ai_src_app_api_data_source_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/data-source/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = false;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        if (!(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdata-source%2Froute&page=%2Fapi%2Fdata-source%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdata-source%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/data-source/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/data-source/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_ibkr__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/ibkr */ \"(rsc)/./src/lib/ibkr.ts\");\n/* harmony import */ var _lib_polygon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/polygon */ \"(rsc)/./src/lib/polygon.ts\");\n\n\n\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const action = searchParams.get('action');\n        switch(action){\n            case 'check_ibkr':\n                return await checkIBKRConnection();\n            case 'check_polygon':\n                return await checkPolygonConnection();\n            case 'status':\n                return await getDataSourceStatus();\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: 'Invalid action'\n                }, {\n                    status: 400\n                });\n        }\n    } catch (error) {\n        console.error('Error in data source API:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to check data sources',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function checkIBKRConnection() {\n    try {\n        const ibkrAPI = new _lib_ibkr__WEBPACK_IMPORTED_MODULE_1__.IBKRAPI({\n            host: '127.0.0.1',\n            port: 4002,\n            clientId: 1,\n            paperTrading: true\n        });\n        const isConnected = await ibkrAPI.connect();\n        if (isConnected) {\n            // Test with a simple quote request\n            const testQuote = await ibkrAPI.getMarketData('SPY');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: {\n                    connected: true,\n                    source: 'IBKR',\n                    testQuote: testQuote ? 'Success' : 'Failed',\n                    message: 'IBKR connection successful'\n                }\n            });\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                data: {\n                    connected: false,\n                    source: 'IBKR',\n                    message: 'IBKR connection failed - ensure TWS/IB Gateway is running'\n                }\n            });\n        }\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            data: {\n                connected: false,\n                source: 'IBKR',\n                error: error instanceof Error ? error.message : 'Unknown error',\n                message: 'IBKR connection error'\n            }\n        });\n    }\n}\nasync function checkPolygonConnection() {\n    try {\n        const polygonAPI = new _lib_polygon__WEBPACK_IMPORTED_MODULE_2__.PolygonAPI(process.env.POLYGON_API_KEY);\n        // Test with a simple quote request\n        const testQuote = await polygonAPI.getStockQuote('SPY');\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                connected: true,\n                source: 'Polygon',\n                testQuote: testQuote ? 'Success' : 'Failed',\n                message: 'Polygon API connection successful'\n            }\n        });\n    } catch (error) {\n        const isRateLimit = error instanceof Error && error.message.includes('429');\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            data: {\n                connected: false,\n                source: 'Polygon',\n                error: error instanceof Error ? error.message : 'Unknown error',\n                isRateLimit,\n                message: isRateLimit ? 'Polygon API rate limit exceeded' : 'Polygon API connection error'\n            }\n        });\n    }\n}\nasync function getDataSourceStatus() {\n    try {\n        const [ibkrResult, polygonResult] = await Promise.all([\n            checkIBKRConnection(),\n            checkPolygonConnection()\n        ]);\n        const ibkrData = await ibkrResult.json();\n        const polygonData = await polygonResult.json();\n        const recommendedSource = ibkrData.success ? 'IBKR' : polygonData.success ? 'Polygon' : 'NONE';\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                ibkr: ibkrData.data,\n                polygon: polygonData.data,\n                recommended: recommendedSource,\n                timestamp: new Date().toISOString()\n            }\n        });\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to check data source status',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { action, config } = body;\n        switch(action){\n            case 'set_preferred_source':\n                // Store preferred data source in environment or database\n                // For now, just return success\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    data: {\n                        preferredSource: config.source,\n                        message: `Preferred data source set to ${config.source}`\n                    }\n                });\n            case 'test_connection':\n                if (config.source === 'IBKR') {\n                    return await checkIBKRConnection();\n                } else if (config.source === 'Polygon') {\n                    return await checkPolygonConnection();\n                } else {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: false,\n                        error: 'Invalid source specified'\n                    }, {\n                        status: 400\n                    });\n                }\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: 'Invalid action'\n                }, {\n                    status: 400\n                });\n        }\n    } catch (error) {\n        console.error('Error in data source POST API:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to process data source request',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/data-source/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/ibkr.ts":
/*!*************************!*\
  !*** ./src/lib/ibkr.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IBKRAPI: () => (/* binding */ IBKRAPI)\n/* harmony export */ });\n/* harmony import */ var _stoqey_ib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @stoqey/ib */ \"(rsc)/./node_modules/@stoqey/ib/dist/index.js\");\n/* harmony import */ var _stoqey_ib__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_stoqey_ib__WEBPACK_IMPORTED_MODULE_0__);\n\nclass IBKRAPI {\n    constructor(config){\n        this.connected = false;\n        this.nextOrderId = 1;\n        this.positions = new Map();\n        this.orders = new Map();\n        this.accountSummary = null;\n        this.config = config;\n        this.ib = new _stoqey_ib__WEBPACK_IMPORTED_MODULE_0__.IBApi({\n            host: config.host,\n            port: config.port,\n            clientId: config.clientId\n        });\n        this.setupEventHandlers();\n    }\n    setupEventHandlers() {\n        // Connection events\n        this.ib.on(_stoqey_ib__WEBPACK_IMPORTED_MODULE_0__.EventName.connected, ()=>{\n            console.log('✅ Connected to IBKR');\n            this.connected = true;\n            this.requestNextOrderId();\n            this.requestAccountSummary();\n            this.requestPositions();\n        });\n        this.ib.on(_stoqey_ib__WEBPACK_IMPORTED_MODULE_0__.EventName.disconnected, ()=>{\n            console.log('❌ Disconnected from IBKR');\n            this.connected = false;\n        });\n        this.ib.on(_stoqey_ib__WEBPACK_IMPORTED_MODULE_0__.EventName.error, (err, code, reqId)=>{\n            console.error(`IBKR Error ${code}:`, err);\n        });\n        // Order management\n        this.ib.on(_stoqey_ib__WEBPACK_IMPORTED_MODULE_0__.EventName.nextValidId, (orderId)=>{\n            this.nextOrderId = orderId;\n            console.log(`Next valid order ID: ${orderId}`);\n        });\n        this.ib.on(_stoqey_ib__WEBPACK_IMPORTED_MODULE_0__.EventName.orderStatus, (orderId, status, filled, remaining, avgFillPrice, permId, parentId, lastFillPrice, clientId, whyHeld, mktCapPrice)=>{\n            const order = this.orders.get(orderId);\n            if (order) {\n                order.status = status;\n                order.filled = filled;\n                order.remaining = remaining;\n                this.orders.set(orderId, order);\n            }\n        });\n        // Position updates\n        this.ib.on(_stoqey_ib__WEBPACK_IMPORTED_MODULE_0__.EventName.position, (account, contract, position, avgCost)=>{\n            const symbol = contract.symbol;\n            const existingPosition = this.positions.get(symbol) || {\n                symbol,\n                position: 0,\n                marketPrice: 0,\n                marketValue: 0,\n                averageCost: 0,\n                unrealizedPNL: 0,\n                realizedPNL: 0\n            };\n            existingPosition.position = position;\n            existingPosition.averageCost = avgCost;\n            this.positions.set(symbol, existingPosition);\n        });\n        // Account summary\n        this.ib.on(_stoqey_ib__WEBPACK_IMPORTED_MODULE_0__.EventName.accountSummary, (reqId, account, tag, value, currency)=>{\n            if (!this.accountSummary) {\n                this.accountSummary = {\n                    totalCashValue: 0,\n                    netLiquidation: 0,\n                    grossPositionValue: 0,\n                    availableFunds: 0,\n                    buyingPower: 0,\n                    unrealizedPnL: 0,\n                    realizedPnL: 0\n                };\n            }\n            switch(tag){\n                case 'TotalCashValue':\n                    this.accountSummary.totalCashValue = parseFloat(value);\n                    break;\n                case 'NetLiquidation':\n                    this.accountSummary.netLiquidation = parseFloat(value);\n                    break;\n                case 'GrossPositionValue':\n                    this.accountSummary.grossPositionValue = parseFloat(value);\n                    break;\n                case 'AvailableFunds':\n                    this.accountSummary.availableFunds = parseFloat(value);\n                    break;\n                case 'BuyingPower':\n                    this.accountSummary.buyingPower = parseFloat(value);\n                    break;\n                case 'UnrealizedPnL':\n                    this.accountSummary.unrealizedPnL = parseFloat(value);\n                    break;\n                case 'RealizedPnL':\n                    this.accountSummary.realizedPnL = parseFloat(value);\n                    break;\n            }\n        });\n    }\n    async connect() {\n        return new Promise((resolve, reject)=>{\n            if (this.connected) {\n                resolve();\n                return;\n            }\n            const timeout = setTimeout(()=>{\n                reject(new Error('Connection timeout'));\n            }, 10000);\n            this.ib.once(_stoqey_ib__WEBPACK_IMPORTED_MODULE_0__.EventName.connected, ()=>{\n                clearTimeout(timeout);\n                resolve();\n            });\n            this.ib.once(_stoqey_ib__WEBPACK_IMPORTED_MODULE_0__.EventName.error, (err)=>{\n                clearTimeout(timeout);\n                reject(err);\n            });\n            this.ib.connect();\n        });\n    }\n    disconnect() {\n        if (this.connected) {\n            this.ib.disconnect();\n        }\n    }\n    requestNextOrderId() {\n        this.ib.reqIds(1);\n    }\n    requestAccountSummary() {\n        this.ib.reqAccountSummary(1, 'All', 'TotalCashValue,NetLiquidation,GrossPositionValue,AvailableFunds,BuyingPower,UnrealizedPnL,RealizedPnL');\n    }\n    requestPositions() {\n        this.ib.reqPositions();\n    }\n    // Create a stock contract\n    createStockContract(symbol) {\n        return {\n            symbol: symbol.toUpperCase(),\n            secType: 'STK',\n            exchange: 'SMART',\n            currency: 'USD'\n        };\n    }\n    // Place a market order\n    async placeMarketOrder(symbol, action, quantity) {\n        if (!this.connected) {\n            throw new Error('Not connected to IBKR');\n        }\n        const contract = this.createStockContract(symbol);\n        const order = {\n            orderId: this.nextOrderId,\n            action,\n            totalQuantity: quantity,\n            orderType: 'MKT'\n        };\n        // Store order for tracking\n        this.orders.set(this.nextOrderId, {\n            orderId: this.nextOrderId,\n            symbol: symbol.toUpperCase(),\n            action,\n            quantity,\n            orderType: 'MKT',\n            status: 'Submitted',\n            filled: 0,\n            remaining: quantity\n        });\n        this.ib.placeOrder(this.nextOrderId, contract, order);\n        const orderId = this.nextOrderId;\n        this.nextOrderId++;\n        return orderId;\n    }\n    // Place a limit order\n    async placeLimitOrder(symbol, action, quantity, price) {\n        if (!this.connected) {\n            throw new Error('Not connected to IBKR');\n        }\n        const contract = this.createStockContract(symbol);\n        const order = {\n            orderId: this.nextOrderId,\n            action,\n            totalQuantity: quantity,\n            orderType: 'LMT',\n            lmtPrice: price\n        };\n        // Store order for tracking\n        this.orders.set(this.nextOrderId, {\n            orderId: this.nextOrderId,\n            symbol: symbol.toUpperCase(),\n            action,\n            quantity,\n            orderType: 'LMT',\n            price,\n            status: 'Submitted',\n            filled: 0,\n            remaining: quantity\n        });\n        this.ib.placeOrder(this.nextOrderId, contract, order);\n        const orderId = this.nextOrderId;\n        this.nextOrderId++;\n        return orderId;\n    }\n    // Place a stop-loss order\n    async placeStopOrder(symbol, action, quantity, stopPrice) {\n        if (!this.connected) {\n            throw new Error('Not connected to IBKR');\n        }\n        const contract = this.createStockContract(symbol);\n        const order = {\n            orderId: this.nextOrderId,\n            action,\n            totalQuantity: quantity,\n            orderType: 'STP',\n            auxPrice: stopPrice\n        };\n        // Store order for tracking\n        this.orders.set(this.nextOrderId, {\n            orderId: this.nextOrderId,\n            symbol: symbol.toUpperCase(),\n            action,\n            quantity,\n            orderType: 'STP',\n            price: stopPrice,\n            status: 'Submitted',\n            filled: 0,\n            remaining: quantity\n        });\n        this.ib.placeOrder(this.nextOrderId, contract, order);\n        const orderId = this.nextOrderId;\n        this.nextOrderId++;\n        return orderId;\n    }\n    // Cancel an order\n    async cancelOrder(orderId) {\n        if (!this.connected) {\n            throw new Error('Not connected to IBKR');\n        }\n        this.ib.cancelOrder(orderId);\n    }\n    // Get account summary\n    getAccountSummary() {\n        return this.accountSummary;\n    }\n    // Get all positions\n    getPositions() {\n        return Array.from(this.positions.values());\n    }\n    // Get position for specific symbol\n    getPosition(symbol) {\n        return this.positions.get(symbol.toUpperCase()) || null;\n    }\n    // Get all orders\n    getOrders() {\n        return Array.from(this.orders.values());\n    }\n    // Get specific order\n    getOrder(orderId) {\n        return this.orders.get(orderId) || null;\n    }\n    // Check if connected\n    isConnected() {\n        return this.connected;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/ibkr.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/polygon.ts":
/*!****************************!*\
  !*** ./src/lib/polygon.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PolygonAPI: () => (/* binding */ PolygonAPI),\n/* harmony export */   polygonAPI: () => (/* binding */ polygonAPI)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/lib/axios.js\");\n\nconst POLYGON_BASE_URL = 'https://api.polygon.io';\nconst API_KEY = process.env.POLYGON_API_KEY;\nclass PolygonAPI {\n    constructor(apiKey){\n        this.apiKey = apiKey || API_KEY || '';\n        if (!this.apiKey) {\n            throw new Error('Polygon API key is required');\n        }\n    }\n    // Get current stock quote using Polygon's snapshot endpoint (better for paid plans)\n    async getStockQuote(symbol) {\n        try {\n            // Use snapshot endpoint for real-time data (available on paid plans)\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${POLYGON_BASE_URL}/v2/snapshot/locale/us/markets/stocks/tickers/${symbol}`, {\n                params: {\n                    apikey: this.apiKey\n                }\n            });\n            if (!response.data || !response.data.ticker) {\n                throw new Error(`No data found for ${symbol}`);\n            }\n            const data = response.data.ticker;\n            // Extract data from Polygon snapshot response structure\n            const dayData = data.day || {};\n            const prevDayData = data.prevDay || {};\n            const minData = data.min || {};\n            // Use the most recent price available\n            const currentPrice = dayData.c || minData.c || prevDayData.c;\n            const prevClose = prevDayData.c;\n            const change = data.todaysChange || currentPrice - prevClose;\n            const changePercent = data.todaysChangePerc || change / prevClose * 100;\n            return {\n                symbol: symbol.toUpperCase(),\n                name: data.name || symbol.toUpperCase(),\n                price: currentPrice || 0,\n                change: change || 0,\n                changePercent: changePercent || 0,\n                volume: dayData.v || minData.v || 1000000,\n                marketCap: data.market_cap || this.estimateMarketCap(symbol, currentPrice || 0),\n                pe: undefined,\n                dividend: undefined\n            };\n        } catch (error) {\n            console.error('Error fetching stock quote from Polygon:', error);\n            // Fallback to previous day data if snapshot fails\n            try {\n                const fallbackResponse = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/prev`, {\n                    params: {\n                        adjusted: 'true',\n                        apikey: this.apiKey\n                    }\n                });\n                const data = fallbackResponse.data.results[0];\n                return {\n                    symbol: symbol.toUpperCase(),\n                    name: symbol.toUpperCase(),\n                    price: data.c || 0,\n                    change: data.c - data.o || 0,\n                    changePercent: data.o ? (data.c - data.o) / data.o * 100 : 0,\n                    volume: data.v || 1000000,\n                    marketCap: this.estimateMarketCap(symbol, data.c || 0),\n                    pe: undefined,\n                    dividend: undefined\n                };\n            } catch (fallbackError) {\n                console.error('Polygon fallback also failed:', fallbackError);\n                throw new Error(`Failed to fetch quote for ${symbol}`);\n            }\n        }\n    }\n    /**\n   * Estimate market cap based on symbol and price\n   * This is a fallback when Polygon doesn't provide market cap data\n   */ estimateMarketCap(symbol, price) {\n        // Import stock universe data for better estimates\n        const stockEstimates = {\n            // Large cap (>$200B)\n            'AAPL': 3000000000000,\n            'MSFT': 2800000000000,\n            'NVDA': 1800000000000,\n            'GOOGL': 1700000000000,\n            'GOOG': 1700000000000,\n            'AMZN': 1500000000000,\n            'TSLA': 800000000000,\n            'META': 800000000000,\n            'BRK.B': 900000000000,\n            // Mid-large cap ($50B-$200B)\n            'JPM': 500000000000,\n            'V': 500000000000,\n            'UNH': 500000000000,\n            'JNJ': 450000000000,\n            'XOM': 450000000000,\n            'WMT': 600000000000,\n            'PG': 400000000000,\n            'MA': 400000000000,\n            'HD': 350000000000,\n            'CVX': 300000000000,\n            'ABBV': 300000000000,\n            'BAC': 300000000000,\n            'COST': 350000000000,\n            'AVGO': 600000000000,\n            'TSM': 500000000000,\n            // Mid cap ($10B-$50B)\n            'NFLX': 200000000000,\n            'ORCL': 350000000000,\n            'CRM': 250000000000,\n            'ADBE': 220000000000,\n            'AMD': 220000000000,\n            'INTC': 200000000000,\n            'QCOM': 180000000000,\n            'TMO': 200000000000,\n            'DHR': 180000000000,\n            'CAT': 180000000000,\n            'GE': 180000000000,\n            'DIS': 180000000000,\n            'VZ': 170000000000,\n            'PFE': 160000000000,\n            'NKE': 150000000000,\n            'MS': 150000000000,\n            'UBER': 150000000000,\n            'C': 120000000000,\n            'GS': 120000000000,\n            'T': 120000000000,\n            'AMGN': 150000000000,\n            'HON': 140000000000,\n            'LOW': 150000000000,\n            'BMY': 120000000000,\n            'CMCSA': 150000000000,\n            'SBUX': 110000000000,\n            'MMM': 60000000000,\n            // Smaller cap but popular swing trading stocks\n            'PLTR': 60000000000,\n            'SHOP': 80000000000,\n            'GILD': 80000000000,\n            'TGT': 70000000000,\n            'COP': 150000000000,\n            'EOG': 70000000000,\n            'SLB': 60000000000,\n            'PYPL': 70000000000,\n            'SQ': 40000000000,\n            'COIN': 50000000000,\n            'DASH': 50000000000,\n            'MRNA': 30000000000,\n            'SNOW': 50000000000,\n            'ROKU': 5000000000,\n            'HOOD': 15000000000,\n            'LYFT': 6000000000,\n            'SPG': 50000000000,\n            'PLD': 120000000000,\n            'NEE': 150000000000\n        };\n        // Return estimated market cap if available, otherwise estimate based on price\n        if (stockEstimates[symbol]) {\n            return stockEstimates[symbol];\n        }\n        // Rough estimation based on price (very approximate)\n        if (price > 500) return 100000000000 // Assume large cap if high price\n        ;\n        if (price > 100) return 50000000000 // Assume mid-large cap\n        ;\n        if (price > 50) return 20000000000 // Assume mid cap\n        ;\n        if (price > 10) return 5000000000 // Assume small-mid cap\n        ;\n        return 1000000000 // Default to $1B minimum for scanning\n        ;\n    }\n    // Get historical candlestick data (optimized for paid plans)\n    async getHistoricalData(symbol, timespan = 'day', multiplier = 1, from, to) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/range/${multiplier}/${timespan}/${from}/${to}`, {\n                params: {\n                    adjusted: 'true',\n                    sort: 'asc',\n                    limit: 50000,\n                    apikey: this.apiKey\n                }\n            });\n            if (!response.data.results || response.data.results.length === 0) {\n                console.warn(`No historical data found for ${symbol}`);\n                return [];\n            }\n            return response.data.results.map((candle)=>({\n                    timestamp: candle.t,\n                    open: candle.o,\n                    high: candle.h,\n                    low: candle.l,\n                    close: candle.c,\n                    volume: candle.v\n                }));\n        } catch (error) {\n            console.error(`Error fetching historical data for ${symbol}:`, error);\n            // Log the specific error for debugging\n            if (error.response) {\n                console.error(`Polygon API Error: ${error.response.status} - ${error.response.statusText}`);\n                console.error('Response data:', error.response.data);\n            }\n            throw new Error(`Failed to fetch historical data for ${symbol}: ${error.message}`);\n        }\n    }\n    // Get company details\n    async getCompanyDetails(symbol) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${POLYGON_BASE_URL}/v3/reference/tickers/${symbol}`, {\n                params: {\n                    apikey: this.apiKey\n                }\n            });\n            return response.data.results;\n        } catch (error) {\n            console.error('Error fetching company details:', error);\n            return null;\n        }\n    }\n    // Get market status\n    async getMarketStatus() {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${POLYGON_BASE_URL}/v1/marketstatus/now`, {\n                params: {\n                    apikey: this.apiKey\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching market status:', error);\n            return null;\n        }\n    }\n    // Search for stocks\n    async searchStocks(query, limit = 10) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${POLYGON_BASE_URL}/v3/reference/tickers`, {\n                params: {\n                    search: query,\n                    market: 'stocks',\n                    active: 'true',\n                    limit,\n                    apikey: this.apiKey\n                }\n            });\n            return response.data.results || [];\n        } catch (error) {\n            console.error('Error searching stocks:', error);\n            return [];\n        }\n    }\n}\n// Create a singleton instance\nconst polygonAPI = new PolygonAPI();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3BvbHlnb24udHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXlCO0FBR3pCLE1BQU1DLG1CQUFtQjtBQUN6QixNQUFNQyxVQUFVQyxRQUFRQyxHQUFHLENBQUNDLGVBQWU7QUFFcEMsTUFBTUM7SUFHWCxZQUFZQyxNQUFlLENBQUU7UUFDM0IsSUFBSSxDQUFDQSxNQUFNLEdBQUdBLFVBQVVMLFdBQVc7UUFDbkMsSUFBSSxDQUFDLElBQUksQ0FBQ0ssTUFBTSxFQUFFO1lBQ2hCLE1BQU0sSUFBSUMsTUFBTTtRQUNsQjtJQUNGO0lBRUEsb0ZBQW9GO0lBQ3BGLE1BQU1DLGNBQWNDLE1BQWMsRUFBc0I7UUFDdEQsSUFBSTtZQUNGLHFFQUFxRTtZQUNyRSxNQUFNQyxXQUFXLE1BQU1YLDZDQUFLQSxDQUFDWSxHQUFHLENBQzlCLEdBQUdYLGlCQUFpQiw4Q0FBOEMsRUFBRVMsUUFBUSxFQUM1RTtnQkFDRUcsUUFBUTtvQkFDTkMsUUFBUSxJQUFJLENBQUNQLE1BQU07Z0JBQ3JCO1lBQ0Y7WUFHRixJQUFJLENBQUNJLFNBQVNJLElBQUksSUFBSSxDQUFDSixTQUFTSSxJQUFJLENBQUNDLE1BQU0sRUFBRTtnQkFDM0MsTUFBTSxJQUFJUixNQUFNLENBQUMsa0JBQWtCLEVBQUVFLFFBQVE7WUFDL0M7WUFFQSxNQUFNSyxPQUFPSixTQUFTSSxJQUFJLENBQUNDLE1BQU07WUFFakMsd0RBQXdEO1lBQ3hELE1BQU1DLFVBQVVGLEtBQUtHLEdBQUcsSUFBSSxDQUFDO1lBQzdCLE1BQU1DLGNBQWNKLEtBQUtLLE9BQU8sSUFBSSxDQUFDO1lBQ3JDLE1BQU1DLFVBQVVOLEtBQUtPLEdBQUcsSUFBSSxDQUFDO1lBRTdCLHNDQUFzQztZQUN0QyxNQUFNQyxlQUFlTixRQUFRTyxDQUFDLElBQUlILFFBQVFHLENBQUMsSUFBSUwsWUFBWUssQ0FBQztZQUM1RCxNQUFNQyxZQUFZTixZQUFZSyxDQUFDO1lBQy9CLE1BQU1FLFNBQVNYLEtBQUtZLFlBQVksSUFBS0osZUFBZUU7WUFDcEQsTUFBTUcsZ0JBQWdCYixLQUFLYyxnQkFBZ0IsSUFBSyxTQUFVSixZQUFhO1lBRXZFLE9BQU87Z0JBQ0xmLFFBQVFBLE9BQU9vQixXQUFXO2dCQUMxQkMsTUFBTWhCLEtBQUtnQixJQUFJLElBQUlyQixPQUFPb0IsV0FBVztnQkFDckNFLE9BQU9ULGdCQUFnQjtnQkFDdkJHLFFBQVFBLFVBQVU7Z0JBQ2xCRSxlQUFlQSxpQkFBaUI7Z0JBQ2hDSyxRQUFRaEIsUUFBUWlCLENBQUMsSUFBSWIsUUFBUWEsQ0FBQyxJQUFJO2dCQUNsQ0MsV0FBV3BCLEtBQUtxQixVQUFVLElBQUksSUFBSSxDQUFDQyxpQkFBaUIsQ0FBQzNCLFFBQVFhLGdCQUFnQjtnQkFDN0VlLElBQUlDO2dCQUNKQyxVQUFVRDtZQUNaO1FBQ0YsRUFBRSxPQUFPRSxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw0Q0FBNENBO1lBRTFELGtEQUFrRDtZQUNsRCxJQUFJO2dCQUNGLE1BQU1FLG1CQUFtQixNQUFNM0MsNkNBQUtBLENBQUNZLEdBQUcsQ0FDdEMsR0FBR1gsaUJBQWlCLGdCQUFnQixFQUFFUyxPQUFPLEtBQUssQ0FBQyxFQUNuRDtvQkFDRUcsUUFBUTt3QkFDTitCLFVBQVU7d0JBQ1Y5QixRQUFRLElBQUksQ0FBQ1AsTUFBTTtvQkFDckI7Z0JBQ0Y7Z0JBR0YsTUFBTVEsT0FBTzRCLGlCQUFpQjVCLElBQUksQ0FBQzhCLE9BQU8sQ0FBQyxFQUFFO2dCQUM3QyxPQUFPO29CQUNMbkMsUUFBUUEsT0FBT29CLFdBQVc7b0JBQzFCQyxNQUFNckIsT0FBT29CLFdBQVc7b0JBQ3hCRSxPQUFPakIsS0FBS1MsQ0FBQyxJQUFJO29CQUNqQkUsUUFBUSxLQUFNRixDQUFDLEdBQUdULEtBQUsrQixDQUFDLElBQUs7b0JBQzdCbEIsZUFBZWIsS0FBSytCLENBQUMsR0FBRyxDQUFFL0IsS0FBS1MsQ0FBQyxHQUFHVCxLQUFLK0IsQ0FBQyxJQUFJL0IsS0FBSytCLENBQUMsR0FBSSxNQUFNO29CQUM3RGIsUUFBUWxCLEtBQUttQixDQUFDLElBQUk7b0JBQ2xCQyxXQUFXLElBQUksQ0FBQ0UsaUJBQWlCLENBQUMzQixRQUFRSyxLQUFLUyxDQUFDLElBQUk7b0JBQ3BEYyxJQUFJQztvQkFDSkMsVUFBVUQ7Z0JBQ1o7WUFDRixFQUFFLE9BQU9RLGVBQWU7Z0JBQ3RCTCxRQUFRRCxLQUFLLENBQUMsaUNBQWlDTTtnQkFDL0MsTUFBTSxJQUFJdkMsTUFBTSxDQUFDLDBCQUEwQixFQUFFRSxRQUFRO1lBQ3ZEO1FBQ0Y7SUFDRjtJQUVBOzs7R0FHQyxHQUNELGtCQUEwQkEsTUFBYyxFQUFFc0IsS0FBYSxFQUFVO1FBQy9ELGtEQUFrRDtRQUNsRCxNQUFNZ0IsaUJBQTRDO1lBQ2hELHFCQUFxQjtZQUNyQixRQUFRO1lBQWUsUUFBUTtZQUFlLFFBQVE7WUFDdEQsU0FBUztZQUFlLFFBQVE7WUFBZSxRQUFRO1lBQ3ZELFFBQVE7WUFBYyxRQUFRO1lBQWMsU0FBUztZQUVyRCw2QkFBNkI7WUFDN0IsT0FBTztZQUFjLEtBQUs7WUFBYyxPQUFPO1lBQy9DLE9BQU87WUFBYyxPQUFPO1lBQWMsT0FBTztZQUNqRCxNQUFNO1lBQWMsTUFBTTtZQUFjLE1BQU07WUFDOUMsT0FBTztZQUFjLFFBQVE7WUFBYyxPQUFPO1lBQ2xELFFBQVE7WUFBYyxRQUFRO1lBQWMsT0FBTztZQUVuRCxzQkFBc0I7WUFDdEIsUUFBUTtZQUFjLFFBQVE7WUFBYyxPQUFPO1lBQ25ELFFBQVE7WUFBYyxPQUFPO1lBQWMsUUFBUTtZQUNuRCxRQUFRO1lBQWMsT0FBTztZQUFjLE9BQU87WUFDbEQsT0FBTztZQUFjLE1BQU07WUFBYyxPQUFPO1lBQ2hELE1BQU07WUFBYyxPQUFPO1lBQWMsT0FBTztZQUNoRCxNQUFNO1lBQWMsUUFBUTtZQUFjLEtBQUs7WUFDL0MsTUFBTTtZQUFjLEtBQUs7WUFBYyxRQUFRO1lBQy9DLE9BQU87WUFBYyxPQUFPO1lBQWMsT0FBTztZQUNqRCxTQUFTO1lBQWMsUUFBUTtZQUFjLE9BQU87WUFFcEQsK0NBQStDO1lBQy9DLFFBQVE7WUFBYSxRQUFRO1lBQWEsUUFBUTtZQUNsRCxPQUFPO1lBQWEsT0FBTztZQUFjLE9BQU87WUFDaEQsT0FBTztZQUFhLFFBQVE7WUFBYSxNQUFNO1lBQy9DLFFBQVE7WUFBYSxRQUFRO1lBQWEsUUFBUTtZQUNsRCxRQUFRO1lBQWEsUUFBUTtZQUFZLFFBQVE7WUFDakQsUUFBUTtZQUFZLE9BQU87WUFBYSxPQUFPO1lBQy9DLE9BQU87UUFDVDtRQUVBLDhFQUE4RTtRQUM5RSxJQUFJQSxjQUFjLENBQUN0QyxPQUFPLEVBQUU7WUFDMUIsT0FBT3NDLGNBQWMsQ0FBQ3RDLE9BQU87UUFDL0I7UUFFQSxxREFBcUQ7UUFDckQsSUFBSXNCLFFBQVEsS0FBSyxPQUFPLGFBQWEsaUNBQWlDOztRQUN0RSxJQUFJQSxRQUFRLEtBQUssT0FBTyxZQUFhLHVCQUF1Qjs7UUFDNUQsSUFBSUEsUUFBUSxJQUFJLE9BQU8sWUFBYyxpQkFBaUI7O1FBQ3RELElBQUlBLFFBQVEsSUFBSSxPQUFPLFdBQWMsdUJBQXVCOztRQUM1RCxPQUFPLFdBQVcsc0NBQXNDOztJQUMxRDtJQUVBLDZEQUE2RDtJQUM3RCxNQUFNaUIsa0JBQ0p2QyxNQUFjLEVBQ2R3QyxXQUF5RCxLQUFLLEVBQzlEQyxhQUFxQixDQUFDLEVBQ3RCQyxJQUFZLEVBQ1pDLEVBQVUsRUFDa0I7UUFDNUIsSUFBSTtZQUNGLE1BQU0xQyxXQUFXLE1BQU1YLDZDQUFLQSxDQUFDWSxHQUFHLENBQzlCLEdBQUdYLGlCQUFpQixnQkFBZ0IsRUFBRVMsT0FBTyxPQUFPLEVBQUV5QyxXQUFXLENBQUMsRUFBRUQsU0FBUyxDQUFDLEVBQUVFLEtBQUssQ0FBQyxFQUFFQyxJQUFJLEVBQzVGO2dCQUNFeEMsUUFBUTtvQkFDTitCLFVBQVU7b0JBQ1ZVLE1BQU07b0JBQ05DLE9BQU87b0JBQ1B6QyxRQUFRLElBQUksQ0FBQ1AsTUFBTTtnQkFDckI7WUFDRjtZQUdGLElBQUksQ0FBQ0ksU0FBU0ksSUFBSSxDQUFDOEIsT0FBTyxJQUFJbEMsU0FBU0ksSUFBSSxDQUFDOEIsT0FBTyxDQUFDVyxNQUFNLEtBQUssR0FBRztnQkFDaEVkLFFBQVFlLElBQUksQ0FBQyxDQUFDLDZCQUE2QixFQUFFL0MsUUFBUTtnQkFDckQsT0FBTyxFQUFFO1lBQ1g7WUFFQSxPQUFPQyxTQUFTSSxJQUFJLENBQUM4QixPQUFPLENBQUNhLEdBQUcsQ0FBQyxDQUFDQyxTQUFpQjtvQkFDakRDLFdBQVdELE9BQU9FLENBQUM7b0JBQ25CQyxNQUFNSCxPQUFPYixDQUFDO29CQUNkaUIsTUFBTUosT0FBT0ssQ0FBQztvQkFDZEMsS0FBS04sT0FBT08sQ0FBQztvQkFDYkMsT0FBT1IsT0FBT25DLENBQUM7b0JBQ2ZTLFFBQVEwQixPQUFPekIsQ0FBQztnQkFDbEI7UUFDRixFQUFFLE9BQU9PLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLENBQUMsbUNBQW1DLEVBQUUvQixPQUFPLENBQUMsQ0FBQyxFQUFFK0I7WUFFL0QsdUNBQXVDO1lBQ3ZDLElBQUlBLE1BQU05QixRQUFRLEVBQUU7Z0JBQ2xCK0IsUUFBUUQsS0FBSyxDQUFDLENBQUMsbUJBQW1CLEVBQUVBLE1BQU05QixRQUFRLENBQUN5RCxNQUFNLENBQUMsR0FBRyxFQUFFM0IsTUFBTTlCLFFBQVEsQ0FBQzBELFVBQVUsRUFBRTtnQkFDMUYzQixRQUFRRCxLQUFLLENBQUMsa0JBQWtCQSxNQUFNOUIsUUFBUSxDQUFDSSxJQUFJO1lBQ3JEO1lBRUEsTUFBTSxJQUFJUCxNQUFNLENBQUMsb0NBQW9DLEVBQUVFLE9BQU8sRUFBRSxFQUFFK0IsTUFBTTZCLE9BQU8sRUFBRTtRQUNuRjtJQUNGO0lBRUEsc0JBQXNCO0lBQ3RCLE1BQU1DLGtCQUFrQjdELE1BQWMsRUFBRTtRQUN0QyxJQUFJO1lBQ0YsTUFBTUMsV0FBVyxNQUFNWCw2Q0FBS0EsQ0FBQ1ksR0FBRyxDQUM5QixHQUFHWCxpQkFBaUIsc0JBQXNCLEVBQUVTLFFBQVEsRUFDcEQ7Z0JBQ0VHLFFBQVE7b0JBQ05DLFFBQVEsSUFBSSxDQUFDUCxNQUFNO2dCQUNyQjtZQUNGO1lBR0YsT0FBT0ksU0FBU0ksSUFBSSxDQUFDOEIsT0FBTztRQUM5QixFQUFFLE9BQU9KLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLG1DQUFtQ0E7WUFDakQsT0FBTztRQUNUO0lBQ0Y7SUFFQSxvQkFBb0I7SUFDcEIsTUFBTStCLGtCQUFrQjtRQUN0QixJQUFJO1lBQ0YsTUFBTTdELFdBQVcsTUFBTVgsNkNBQUtBLENBQUNZLEdBQUcsQ0FDOUIsR0FBR1gsaUJBQWlCLG9CQUFvQixDQUFDLEVBQ3pDO2dCQUNFWSxRQUFRO29CQUNOQyxRQUFRLElBQUksQ0FBQ1AsTUFBTTtnQkFDckI7WUFDRjtZQUdGLE9BQU9JLFNBQVNJLElBQUk7UUFDdEIsRUFBRSxPQUFPMEIsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsaUNBQWlDQTtZQUMvQyxPQUFPO1FBQ1Q7SUFDRjtJQUVBLG9CQUFvQjtJQUNwQixNQUFNZ0MsYUFBYUMsS0FBYSxFQUFFbkIsUUFBZ0IsRUFBRSxFQUFFO1FBQ3BELElBQUk7WUFDRixNQUFNNUMsV0FBVyxNQUFNWCw2Q0FBS0EsQ0FBQ1ksR0FBRyxDQUM5QixHQUFHWCxpQkFBaUIscUJBQXFCLENBQUMsRUFDMUM7Z0JBQ0VZLFFBQVE7b0JBQ044RCxRQUFRRDtvQkFDUkUsUUFBUTtvQkFDUkMsUUFBUTtvQkFDUnRCO29CQUNBekMsUUFBUSxJQUFJLENBQUNQLE1BQU07Z0JBQ3JCO1lBQ0Y7WUFHRixPQUFPSSxTQUFTSSxJQUFJLENBQUM4QixPQUFPLElBQUksRUFBRTtRQUNwQyxFQUFFLE9BQU9KLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDJCQUEyQkE7WUFDekMsT0FBTyxFQUFFO1FBQ1g7SUFDRjtBQUNGO0FBRUEsOEJBQThCO0FBQ3ZCLE1BQU1xQyxhQUFhLElBQUl4RSxhQUFZIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFNKRml0XFxEZXNrdG9wXFxzaGl0dHlpZGVhXFxzd2luZy10cmFkZXItYWlcXHNyY1xcbGliXFxwb2x5Z29uLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBheGlvcyBmcm9tICdheGlvcydcbmltcG9ydCB7IENhbmRsZXN0aWNrRGF0YSwgU3RvY2tEYXRhIH0gZnJvbSAnQC90eXBlcy90cmFkaW5nJ1xuXG5jb25zdCBQT0xZR09OX0JBU0VfVVJMID0gJ2h0dHBzOi8vYXBpLnBvbHlnb24uaW8nXG5jb25zdCBBUElfS0VZID0gcHJvY2Vzcy5lbnYuUE9MWUdPTl9BUElfS0VZXG5cbmV4cG9ydCBjbGFzcyBQb2x5Z29uQVBJIHtcbiAgcHJpdmF0ZSBhcGlLZXk6IHN0cmluZ1xuXG4gIGNvbnN0cnVjdG9yKGFwaUtleT86IHN0cmluZykge1xuICAgIHRoaXMuYXBpS2V5ID0gYXBpS2V5IHx8IEFQSV9LRVkgfHwgJydcbiAgICBpZiAoIXRoaXMuYXBpS2V5KSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1BvbHlnb24gQVBJIGtleSBpcyByZXF1aXJlZCcpXG4gICAgfVxuICB9XG5cbiAgLy8gR2V0IGN1cnJlbnQgc3RvY2sgcXVvdGUgdXNpbmcgUG9seWdvbidzIHNuYXBzaG90IGVuZHBvaW50IChiZXR0ZXIgZm9yIHBhaWQgcGxhbnMpXG4gIGFzeW5jIGdldFN0b2NrUXVvdGUoc3ltYm9sOiBzdHJpbmcpOiBQcm9taXNlPFN0b2NrRGF0YT4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBVc2Ugc25hcHNob3QgZW5kcG9pbnQgZm9yIHJlYWwtdGltZSBkYXRhIChhdmFpbGFibGUgb24gcGFpZCBwbGFucylcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MuZ2V0KFxuICAgICAgICBgJHtQT0xZR09OX0JBU0VfVVJMfS92Mi9zbmFwc2hvdC9sb2NhbGUvdXMvbWFya2V0cy9zdG9ja3MvdGlja2Vycy8ke3N5bWJvbH1gLFxuICAgICAgICB7XG4gICAgICAgICAgcGFyYW1zOiB7XG4gICAgICAgICAgICBhcGlrZXk6IHRoaXMuYXBpS2V5XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICApXG5cbiAgICAgIGlmICghcmVzcG9uc2UuZGF0YSB8fCAhcmVzcG9uc2UuZGF0YS50aWNrZXIpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBObyBkYXRhIGZvdW5kIGZvciAke3N5bWJvbH1gKVxuICAgICAgfVxuXG4gICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UuZGF0YS50aWNrZXJcblxuICAgICAgLy8gRXh0cmFjdCBkYXRhIGZyb20gUG9seWdvbiBzbmFwc2hvdCByZXNwb25zZSBzdHJ1Y3R1cmVcbiAgICAgIGNvbnN0IGRheURhdGEgPSBkYXRhLmRheSB8fCB7fVxuICAgICAgY29uc3QgcHJldkRheURhdGEgPSBkYXRhLnByZXZEYXkgfHwge31cbiAgICAgIGNvbnN0IG1pbkRhdGEgPSBkYXRhLm1pbiB8fCB7fVxuXG4gICAgICAvLyBVc2UgdGhlIG1vc3QgcmVjZW50IHByaWNlIGF2YWlsYWJsZVxuICAgICAgY29uc3QgY3VycmVudFByaWNlID0gZGF5RGF0YS5jIHx8IG1pbkRhdGEuYyB8fCBwcmV2RGF5RGF0YS5jXG4gICAgICBjb25zdCBwcmV2Q2xvc2UgPSBwcmV2RGF5RGF0YS5jXG4gICAgICBjb25zdCBjaGFuZ2UgPSBkYXRhLnRvZGF5c0NoYW5nZSB8fCAoY3VycmVudFByaWNlIC0gcHJldkNsb3NlKVxuICAgICAgY29uc3QgY2hhbmdlUGVyY2VudCA9IGRhdGEudG9kYXlzQ2hhbmdlUGVyYyB8fCAoKGNoYW5nZSAvIHByZXZDbG9zZSkgKiAxMDApXG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN5bWJvbDogc3ltYm9sLnRvVXBwZXJDYXNlKCksXG4gICAgICAgIG5hbWU6IGRhdGEubmFtZSB8fCBzeW1ib2wudG9VcHBlckNhc2UoKSxcbiAgICAgICAgcHJpY2U6IGN1cnJlbnRQcmljZSB8fCAwLFxuICAgICAgICBjaGFuZ2U6IGNoYW5nZSB8fCAwLFxuICAgICAgICBjaGFuZ2VQZXJjZW50OiBjaGFuZ2VQZXJjZW50IHx8IDAsXG4gICAgICAgIHZvbHVtZTogZGF5RGF0YS52IHx8IG1pbkRhdGEudiB8fCAxMDAwMDAwLCAvLyBEZWZhdWx0IHRvIDFNIHZvbHVtZSBpZiBtaXNzaW5nXG4gICAgICAgIG1hcmtldENhcDogZGF0YS5tYXJrZXRfY2FwIHx8IHRoaXMuZXN0aW1hdGVNYXJrZXRDYXAoc3ltYm9sLCBjdXJyZW50UHJpY2UgfHwgMCksXG4gICAgICAgIHBlOiB1bmRlZmluZWQsXG4gICAgICAgIGRpdmlkZW5kOiB1bmRlZmluZWRcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgc3RvY2sgcXVvdGUgZnJvbSBQb2x5Z29uOicsIGVycm9yKVxuXG4gICAgICAvLyBGYWxsYmFjayB0byBwcmV2aW91cyBkYXkgZGF0YSBpZiBzbmFwc2hvdCBmYWlsc1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgZmFsbGJhY2tSZXNwb25zZSA9IGF3YWl0IGF4aW9zLmdldChcbiAgICAgICAgICBgJHtQT0xZR09OX0JBU0VfVVJMfS92Mi9hZ2dzL3RpY2tlci8ke3N5bWJvbH0vcHJldmAsXG4gICAgICAgICAge1xuICAgICAgICAgICAgcGFyYW1zOiB7XG4gICAgICAgICAgICAgIGFkanVzdGVkOiAndHJ1ZScsXG4gICAgICAgICAgICAgIGFwaWtleTogdGhpcy5hcGlLZXlcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIClcblxuICAgICAgICBjb25zdCBkYXRhID0gZmFsbGJhY2tSZXNwb25zZS5kYXRhLnJlc3VsdHNbMF1cbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBzeW1ib2w6IHN5bWJvbC50b1VwcGVyQ2FzZSgpLFxuICAgICAgICAgIG5hbWU6IHN5bWJvbC50b1VwcGVyQ2FzZSgpLFxuICAgICAgICAgIHByaWNlOiBkYXRhLmMgfHwgMCxcbiAgICAgICAgICBjaGFuZ2U6IChkYXRhLmMgLSBkYXRhLm8pIHx8IDAsXG4gICAgICAgICAgY2hhbmdlUGVyY2VudDogZGF0YS5vID8gKChkYXRhLmMgLSBkYXRhLm8pIC8gZGF0YS5vKSAqIDEwMCA6IDAsXG4gICAgICAgICAgdm9sdW1lOiBkYXRhLnYgfHwgMTAwMDAwMCxcbiAgICAgICAgICBtYXJrZXRDYXA6IHRoaXMuZXN0aW1hdGVNYXJrZXRDYXAoc3ltYm9sLCBkYXRhLmMgfHwgMCksXG4gICAgICAgICAgcGU6IHVuZGVmaW5lZCxcbiAgICAgICAgICBkaXZpZGVuZDogdW5kZWZpbmVkXG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGZhbGxiYWNrRXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignUG9seWdvbiBmYWxsYmFjayBhbHNvIGZhaWxlZDonLCBmYWxsYmFja0Vycm9yKVxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBmZXRjaCBxdW90ZSBmb3IgJHtzeW1ib2x9YClcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogRXN0aW1hdGUgbWFya2V0IGNhcCBiYXNlZCBvbiBzeW1ib2wgYW5kIHByaWNlXG4gICAqIFRoaXMgaXMgYSBmYWxsYmFjayB3aGVuIFBvbHlnb24gZG9lc24ndCBwcm92aWRlIG1hcmtldCBjYXAgZGF0YVxuICAgKi9cbiAgcHJpdmF0ZSBlc3RpbWF0ZU1hcmtldENhcChzeW1ib2w6IHN0cmluZywgcHJpY2U6IG51bWJlcik6IG51bWJlciB7XG4gICAgLy8gSW1wb3J0IHN0b2NrIHVuaXZlcnNlIGRhdGEgZm9yIGJldHRlciBlc3RpbWF0ZXNcbiAgICBjb25zdCBzdG9ja0VzdGltYXRlczogeyBba2V5OiBzdHJpbmddOiBudW1iZXIgfSA9IHtcbiAgICAgIC8vIExhcmdlIGNhcCAoPiQyMDBCKVxuICAgICAgJ0FBUEwnOiAzMDAwMDAwMDAwMDAwLCAnTVNGVCc6IDI4MDAwMDAwMDAwMDAsICdOVkRBJzogMTgwMDAwMDAwMDAwMCxcbiAgICAgICdHT09HTCc6IDE3MDAwMDAwMDAwMDAsICdHT09HJzogMTcwMDAwMDAwMDAwMCwgJ0FNWk4nOiAxNTAwMDAwMDAwMDAwLFxuICAgICAgJ1RTTEEnOiA4MDAwMDAwMDAwMDAsICdNRVRBJzogODAwMDAwMDAwMDAwLCAnQlJLLkInOiA5MDAwMDAwMDAwMDAsXG5cbiAgICAgIC8vIE1pZC1sYXJnZSBjYXAgKCQ1MEItJDIwMEIpXG4gICAgICAnSlBNJzogNTAwMDAwMDAwMDAwLCAnVic6IDUwMDAwMDAwMDAwMCwgJ1VOSCc6IDUwMDAwMDAwMDAwMCxcbiAgICAgICdKTkonOiA0NTAwMDAwMDAwMDAsICdYT00nOiA0NTAwMDAwMDAwMDAsICdXTVQnOiA2MDAwMDAwMDAwMDAsXG4gICAgICAnUEcnOiA0MDAwMDAwMDAwMDAsICdNQSc6IDQwMDAwMDAwMDAwMCwgJ0hEJzogMzUwMDAwMDAwMDAwLFxuICAgICAgJ0NWWCc6IDMwMDAwMDAwMDAwMCwgJ0FCQlYnOiAzMDAwMDAwMDAwMDAsICdCQUMnOiAzMDAwMDAwMDAwMDAsXG4gICAgICAnQ09TVCc6IDM1MDAwMDAwMDAwMCwgJ0FWR08nOiA2MDAwMDAwMDAwMDAsICdUU00nOiA1MDAwMDAwMDAwMDAsXG5cbiAgICAgIC8vIE1pZCBjYXAgKCQxMEItJDUwQilcbiAgICAgICdORkxYJzogMjAwMDAwMDAwMDAwLCAnT1JDTCc6IDM1MDAwMDAwMDAwMCwgJ0NSTSc6IDI1MDAwMDAwMDAwMCxcbiAgICAgICdBREJFJzogMjIwMDAwMDAwMDAwLCAnQU1EJzogMjIwMDAwMDAwMDAwLCAnSU5UQyc6IDIwMDAwMDAwMDAwMCxcbiAgICAgICdRQ09NJzogMTgwMDAwMDAwMDAwLCAnVE1PJzogMjAwMDAwMDAwMDAwLCAnREhSJzogMTgwMDAwMDAwMDAwLFxuICAgICAgJ0NBVCc6IDE4MDAwMDAwMDAwMCwgJ0dFJzogMTgwMDAwMDAwMDAwLCAnRElTJzogMTgwMDAwMDAwMDAwLFxuICAgICAgJ1ZaJzogMTcwMDAwMDAwMDAwLCAnUEZFJzogMTYwMDAwMDAwMDAwLCAnTktFJzogMTUwMDAwMDAwMDAwLFxuICAgICAgJ01TJzogMTUwMDAwMDAwMDAwLCAnVUJFUic6IDE1MDAwMDAwMDAwMCwgJ0MnOiAxMjAwMDAwMDAwMDAsXG4gICAgICAnR1MnOiAxMjAwMDAwMDAwMDAsICdUJzogMTIwMDAwMDAwMDAwLCAnQU1HTic6IDE1MDAwMDAwMDAwMCxcbiAgICAgICdIT04nOiAxNDAwMDAwMDAwMDAsICdMT1cnOiAxNTAwMDAwMDAwMDAsICdCTVknOiAxMjAwMDAwMDAwMDAsXG4gICAgICAnQ01DU0EnOiAxNTAwMDAwMDAwMDAsICdTQlVYJzogMTEwMDAwMDAwMDAwLCAnTU1NJzogNjAwMDAwMDAwMDAsXG5cbiAgICAgIC8vIFNtYWxsZXIgY2FwIGJ1dCBwb3B1bGFyIHN3aW5nIHRyYWRpbmcgc3RvY2tzXG4gICAgICAnUExUUic6IDYwMDAwMDAwMDAwLCAnU0hPUCc6IDgwMDAwMDAwMDAwLCAnR0lMRCc6IDgwMDAwMDAwMDAwLFxuICAgICAgJ1RHVCc6IDcwMDAwMDAwMDAwLCAnQ09QJzogMTUwMDAwMDAwMDAwLCAnRU9HJzogNzAwMDAwMDAwMDAsXG4gICAgICAnU0xCJzogNjAwMDAwMDAwMDAsICdQWVBMJzogNzAwMDAwMDAwMDAsICdTUSc6IDQwMDAwMDAwMDAwLFxuICAgICAgJ0NPSU4nOiA1MDAwMDAwMDAwMCwgJ0RBU0gnOiA1MDAwMDAwMDAwMCwgJ01STkEnOiAzMDAwMDAwMDAwMCxcbiAgICAgICdTTk9XJzogNTAwMDAwMDAwMDAsICdST0tVJzogNTAwMDAwMDAwMCwgJ0hPT0QnOiAxNTAwMDAwMDAwMCxcbiAgICAgICdMWUZUJzogNjAwMDAwMDAwMCwgJ1NQRyc6IDUwMDAwMDAwMDAwLCAnUExEJzogMTIwMDAwMDAwMDAwLFxuICAgICAgJ05FRSc6IDE1MDAwMDAwMDAwMFxuICAgIH1cblxuICAgIC8vIFJldHVybiBlc3RpbWF0ZWQgbWFya2V0IGNhcCBpZiBhdmFpbGFibGUsIG90aGVyd2lzZSBlc3RpbWF0ZSBiYXNlZCBvbiBwcmljZVxuICAgIGlmIChzdG9ja0VzdGltYXRlc1tzeW1ib2xdKSB7XG4gICAgICByZXR1cm4gc3RvY2tFc3RpbWF0ZXNbc3ltYm9sXVxuICAgIH1cblxuICAgIC8vIFJvdWdoIGVzdGltYXRpb24gYmFzZWQgb24gcHJpY2UgKHZlcnkgYXBwcm94aW1hdGUpXG4gICAgaWYgKHByaWNlID4gNTAwKSByZXR1cm4gMTAwMDAwMDAwMDAwIC8vIEFzc3VtZSBsYXJnZSBjYXAgaWYgaGlnaCBwcmljZVxuICAgIGlmIChwcmljZSA+IDEwMCkgcmV0dXJuIDUwMDAwMDAwMDAwICAvLyBBc3N1bWUgbWlkLWxhcmdlIGNhcFxuICAgIGlmIChwcmljZSA+IDUwKSByZXR1cm4gMjAwMDAwMDAwMDAgICAvLyBBc3N1bWUgbWlkIGNhcFxuICAgIGlmIChwcmljZSA+IDEwKSByZXR1cm4gNTAwMDAwMDAwMCAgICAvLyBBc3N1bWUgc21hbGwtbWlkIGNhcFxuICAgIHJldHVybiAxMDAwMDAwMDAwIC8vIERlZmF1bHQgdG8gJDFCIG1pbmltdW0gZm9yIHNjYW5uaW5nXG4gIH1cblxuICAvLyBHZXQgaGlzdG9yaWNhbCBjYW5kbGVzdGljayBkYXRhIChvcHRpbWl6ZWQgZm9yIHBhaWQgcGxhbnMpXG4gIGFzeW5jIGdldEhpc3RvcmljYWxEYXRhKFxuICAgIHN5bWJvbDogc3RyaW5nLFxuICAgIHRpbWVzcGFuOiAnbWludXRlJyB8ICdob3VyJyB8ICdkYXknIHwgJ3dlZWsnIHwgJ21vbnRoJyA9ICdkYXknLFxuICAgIG11bHRpcGxpZXI6IG51bWJlciA9IDEsXG4gICAgZnJvbTogc3RyaW5nLFxuICAgIHRvOiBzdHJpbmdcbiAgKTogUHJvbWlzZTxDYW5kbGVzdGlja0RhdGFbXT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zLmdldChcbiAgICAgICAgYCR7UE9MWUdPTl9CQVNFX1VSTH0vdjIvYWdncy90aWNrZXIvJHtzeW1ib2x9L3JhbmdlLyR7bXVsdGlwbGllcn0vJHt0aW1lc3Bhbn0vJHtmcm9tfS8ke3RvfWAsXG4gICAgICAgIHtcbiAgICAgICAgICBwYXJhbXM6IHtcbiAgICAgICAgICAgIGFkanVzdGVkOiAndHJ1ZScsXG4gICAgICAgICAgICBzb3J0OiAnYXNjJyxcbiAgICAgICAgICAgIGxpbWl0OiA1MDAwMCwgLy8gSGlnaGVyIGxpbWl0IGZvciBwYWlkIHBsYW5zXG4gICAgICAgICAgICBhcGlrZXk6IHRoaXMuYXBpS2V5XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICApXG5cbiAgICAgIGlmICghcmVzcG9uc2UuZGF0YS5yZXN1bHRzIHx8IHJlc3BvbnNlLmRhdGEucmVzdWx0cy5sZW5ndGggPT09IDApIHtcbiAgICAgICAgY29uc29sZS53YXJuKGBObyBoaXN0b3JpY2FsIGRhdGEgZm91bmQgZm9yICR7c3ltYm9sfWApXG4gICAgICAgIHJldHVybiBbXVxuICAgICAgfVxuXG4gICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YS5yZXN1bHRzLm1hcCgoY2FuZGxlOiBhbnkpID0+ICh7XG4gICAgICAgIHRpbWVzdGFtcDogY2FuZGxlLnQsXG4gICAgICAgIG9wZW46IGNhbmRsZS5vLFxuICAgICAgICBoaWdoOiBjYW5kbGUuaCxcbiAgICAgICAgbG93OiBjYW5kbGUubCxcbiAgICAgICAgY2xvc2U6IGNhbmRsZS5jLFxuICAgICAgICB2b2x1bWU6IGNhbmRsZS52XG4gICAgICB9KSlcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihgRXJyb3IgZmV0Y2hpbmcgaGlzdG9yaWNhbCBkYXRhIGZvciAke3N5bWJvbH06YCwgZXJyb3IpXG5cbiAgICAgIC8vIExvZyB0aGUgc3BlY2lmaWMgZXJyb3IgZm9yIGRlYnVnZ2luZ1xuICAgICAgaWYgKGVycm9yLnJlc3BvbnNlKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoYFBvbHlnb24gQVBJIEVycm9yOiAke2Vycm9yLnJlc3BvbnNlLnN0YXR1c30gLSAke2Vycm9yLnJlc3BvbnNlLnN0YXR1c1RleHR9YClcbiAgICAgICAgY29uc29sZS5lcnJvcignUmVzcG9uc2UgZGF0YTonLCBlcnJvci5yZXNwb25zZS5kYXRhKVxuICAgICAgfVxuXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBmZXRjaCBoaXN0b3JpY2FsIGRhdGEgZm9yICR7c3ltYm9sfTogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgfVxuICB9XG5cbiAgLy8gR2V0IGNvbXBhbnkgZGV0YWlsc1xuICBhc3luYyBnZXRDb21wYW55RGV0YWlscyhzeW1ib2w6IHN0cmluZykge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zLmdldChcbiAgICAgICAgYCR7UE9MWUdPTl9CQVNFX1VSTH0vdjMvcmVmZXJlbmNlL3RpY2tlcnMvJHtzeW1ib2x9YCxcbiAgICAgICAge1xuICAgICAgICAgIHBhcmFtczoge1xuICAgICAgICAgICAgYXBpa2V5OiB0aGlzLmFwaUtleVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgKVxuXG4gICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YS5yZXN1bHRzXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIGNvbXBhbnkgZGV0YWlsczonLCBlcnJvcilcbiAgICAgIHJldHVybiBudWxsXG4gICAgfVxuICB9XG5cbiAgLy8gR2V0IG1hcmtldCBzdGF0dXNcbiAgYXN5bmMgZ2V0TWFya2V0U3RhdHVzKCkge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zLmdldChcbiAgICAgICAgYCR7UE9MWUdPTl9CQVNFX1VSTH0vdjEvbWFya2V0c3RhdHVzL25vd2AsXG4gICAgICAgIHtcbiAgICAgICAgICBwYXJhbXM6IHtcbiAgICAgICAgICAgIGFwaWtleTogdGhpcy5hcGlLZXlcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIClcblxuICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGFcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgbWFya2V0IHN0YXR1czonLCBlcnJvcilcbiAgICAgIHJldHVybiBudWxsXG4gICAgfVxuICB9XG5cbiAgLy8gU2VhcmNoIGZvciBzdG9ja3NcbiAgYXN5bmMgc2VhcmNoU3RvY2tzKHF1ZXJ5OiBzdHJpbmcsIGxpbWl0OiBudW1iZXIgPSAxMCkge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zLmdldChcbiAgICAgICAgYCR7UE9MWUdPTl9CQVNFX1VSTH0vdjMvcmVmZXJlbmNlL3RpY2tlcnNgLFxuICAgICAgICB7XG4gICAgICAgICAgcGFyYW1zOiB7XG4gICAgICAgICAgICBzZWFyY2g6IHF1ZXJ5LFxuICAgICAgICAgICAgbWFya2V0OiAnc3RvY2tzJyxcbiAgICAgICAgICAgIGFjdGl2ZTogJ3RydWUnLFxuICAgICAgICAgICAgbGltaXQsXG4gICAgICAgICAgICBhcGlrZXk6IHRoaXMuYXBpS2V5XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICApXG5cbiAgICAgIHJldHVybiByZXNwb25zZS5kYXRhLnJlc3VsdHMgfHwgW11cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igc2VhcmNoaW5nIHN0b2NrczonLCBlcnJvcilcbiAgICAgIHJldHVybiBbXVxuICAgIH1cbiAgfVxufVxuXG4vLyBDcmVhdGUgYSBzaW5nbGV0b24gaW5zdGFuY2VcbmV4cG9ydCBjb25zdCBwb2x5Z29uQVBJID0gbmV3IFBvbHlnb25BUEkoKVxuIl0sIm5hbWVzIjpbImF4aW9zIiwiUE9MWUdPTl9CQVNFX1VSTCIsIkFQSV9LRVkiLCJwcm9jZXNzIiwiZW52IiwiUE9MWUdPTl9BUElfS0VZIiwiUG9seWdvbkFQSSIsImFwaUtleSIsIkVycm9yIiwiZ2V0U3RvY2tRdW90ZSIsInN5bWJvbCIsInJlc3BvbnNlIiwiZ2V0IiwicGFyYW1zIiwiYXBpa2V5IiwiZGF0YSIsInRpY2tlciIsImRheURhdGEiLCJkYXkiLCJwcmV2RGF5RGF0YSIsInByZXZEYXkiLCJtaW5EYXRhIiwibWluIiwiY3VycmVudFByaWNlIiwiYyIsInByZXZDbG9zZSIsImNoYW5nZSIsInRvZGF5c0NoYW5nZSIsImNoYW5nZVBlcmNlbnQiLCJ0b2RheXNDaGFuZ2VQZXJjIiwidG9VcHBlckNhc2UiLCJuYW1lIiwicHJpY2UiLCJ2b2x1bWUiLCJ2IiwibWFya2V0Q2FwIiwibWFya2V0X2NhcCIsImVzdGltYXRlTWFya2V0Q2FwIiwicGUiLCJ1bmRlZmluZWQiLCJkaXZpZGVuZCIsImVycm9yIiwiY29uc29sZSIsImZhbGxiYWNrUmVzcG9uc2UiLCJhZGp1c3RlZCIsInJlc3VsdHMiLCJvIiwiZmFsbGJhY2tFcnJvciIsInN0b2NrRXN0aW1hdGVzIiwiZ2V0SGlzdG9yaWNhbERhdGEiLCJ0aW1lc3BhbiIsIm11bHRpcGxpZXIiLCJmcm9tIiwidG8iLCJzb3J0IiwibGltaXQiLCJsZW5ndGgiLCJ3YXJuIiwibWFwIiwiY2FuZGxlIiwidGltZXN0YW1wIiwidCIsIm9wZW4iLCJoaWdoIiwiaCIsImxvdyIsImwiLCJjbG9zZSIsInN0YXR1cyIsInN0YXR1c1RleHQiLCJtZXNzYWdlIiwiZ2V0Q29tcGFueURldGFpbHMiLCJnZXRNYXJrZXRTdGF0dXMiLCJzZWFyY2hTdG9ja3MiLCJxdWVyeSIsInNlYXJjaCIsIm1hcmtldCIsImFjdGl2ZSIsInBvbHlnb25BUEkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/polygon.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/@stoqey","vendor-chunks/rxjs","vendor-chunks/colors","vendor-chunks/eventemitter3","vendor-chunks/command-buffer","vendor-chunks/function-rate-limit"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdata-source%2Froute&page=%2Fapi%2Fdata-source%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdata-source%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();