{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/lib/indicators.ts"], "sourcesContent": ["import { CandlestickData, TechnicalIndicator } from '@/types/trading'\n\nexport class TechnicalIndicators {\n  // Simple Moving Average\n  static sma(data: number[], period: number): number[] {\n    const result: number[] = []\n    for (let i = period - 1; i < data.length; i++) {\n      const sum = data.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0)\n      result.push(sum / period)\n    }\n    return result\n  }\n\n  // Exponential Moving Average\n  static ema(data: number[], period: number): number[] {\n    const result: number[] = []\n    const multiplier = 2 / (period + 1)\n    \n    // Start with SMA for first value\n    let ema = data.slice(0, period).reduce((a, b) => a + b, 0) / period\n    result.push(ema)\n    \n    for (let i = period; i < data.length; i++) {\n      ema = (data[i] * multiplier) + (ema * (1 - multiplier))\n      result.push(ema)\n    }\n    \n    return result\n  }\n\n  // Relative Strength Index\n  static rsi(data: number[], period: number = 14): number[] {\n    const gains: number[] = []\n    const losses: number[] = []\n    \n    for (let i = 1; i < data.length; i++) {\n      const change = data[i] - data[i - 1]\n      gains.push(change > 0 ? change : 0)\n      losses.push(change < 0 ? Math.abs(change) : 0)\n    }\n    \n    const avgGains = this.sma(gains, period)\n    const avgLosses = this.sma(losses, period)\n    \n    return avgGains.map((gain, i) => {\n      const rs = gain / avgLosses[i]\n      return 100 - (100 / (1 + rs))\n    })\n  }\n\n  // MACD (Moving Average Convergence Divergence)\n  static macd(data: number[], fastPeriod: number = 12, slowPeriod: number = 26, signalPeriod: number = 9) {\n    const fastEMA = this.ema(data, fastPeriod)\n    const slowEMA = this.ema(data, slowPeriod)\n    \n    // Align arrays (slowEMA starts later)\n    const startIndex = slowPeriod - fastPeriod\n    const macdLine = fastEMA.slice(startIndex).map((fast, i) => fast - slowEMA[i])\n    \n    const signalLine = this.ema(macdLine, signalPeriod)\n    const histogram = macdLine.slice(signalPeriod - 1).map((macd, i) => macd - signalLine[i])\n    \n    return {\n      macd: macdLine,\n      signal: signalLine,\n      histogram\n    }\n  }\n\n  // Bollinger Bands\n  static bollingerBands(data: number[], period: number = 20, stdDev: number = 2) {\n    const sma = this.sma(data, period)\n    const bands = sma.map((avg, i) => {\n      const slice = data.slice(i, i + period)\n      const variance = slice.reduce((sum, val) => sum + Math.pow(val - avg, 2), 0) / period\n      const standardDeviation = Math.sqrt(variance)\n      \n      return {\n        upper: avg + (standardDeviation * stdDev),\n        middle: avg,\n        lower: avg - (standardDeviation * stdDev)\n      }\n    })\n    \n    return bands\n  }\n\n  // Support and Resistance Levels\n  static findSupportResistance(candles: CandlestickData[], lookback: number = 20): { support: number[], resistance: number[] } {\n    const highs = candles.map(c => c.high)\n    const lows = candles.map(c => c.low)\n    \n    const resistance: number[] = []\n    const support: number[] = []\n    \n    for (let i = lookback; i < candles.length - lookback; i++) {\n      const currentHigh = highs[i]\n      const currentLow = lows[i]\n      \n      // Check if current high is a local maximum\n      const isResistance = highs.slice(i - lookback, i).every(h => h <= currentHigh) &&\n                          highs.slice(i + 1, i + lookback + 1).every(h => h <= currentHigh)\n      \n      // Check if current low is a local minimum\n      const isSupport = lows.slice(i - lookback, i).every(l => l >= currentLow) &&\n                       lows.slice(i + 1, i + lookback + 1).every(l => l >= currentLow)\n      \n      if (isResistance) resistance.push(currentHigh)\n      if (isSupport) support.push(currentLow)\n    }\n    \n    return { support, resistance }\n  }\n\n  // Volume analysis\n  static volumeAnalysis(candles: CandlestickData[], period: number = 20) {\n    const volumes = candles.map(c => c.volume)\n    const avgVolume = this.sma(volumes, period)\n    const currentVolume = volumes[volumes.length - 1]\n    const currentAvgVolume = avgVolume[avgVolume.length - 1]\n\n    return {\n      currentVolume,\n      averageVolume: currentAvgVolume,\n      volumeRatio: currentVolume / currentAvgVolume,\n      isHighVolume: currentVolume > currentAvgVolume * 1.5,\n      isLowVolume: currentVolume < currentAvgVolume * 0.5\n    }\n  }\n\n  // Swing Trading Analysis\n  static analyzeSwingSetup(candles: CandlestickData[]): TechnicalIndicator[] {\n    const closes = candles.map(c => c.close)\n    const indicators: TechnicalIndicator[] = []\n\n    // RSI Analysis\n    const rsi = this.rsi(closes)\n    const currentRSI = rsi[rsi.length - 1]\n\n    let rsiSignal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'\n    let rsiDescription = `RSI: ${currentRSI.toFixed(2)}`\n\n    if (currentRSI < 30) {\n      rsiSignal = 'BUY'\n      rsiDescription += ' - Oversold condition, potential bounce'\n    } else if (currentRSI > 70) {\n      rsiSignal = 'SELL'\n      rsiDescription += ' - Overbought condition, potential pullback'\n    } else {\n      rsiDescription += ' - Neutral zone'\n    }\n\n    indicators.push({\n      name: 'RSI',\n      value: currentRSI,\n      signal: rsiSignal,\n      description: rsiDescription\n    })\n\n    // Moving Average Analysis\n    const sma20 = this.sma(closes, 20)\n    const sma50 = this.sma(closes, 50)\n    const currentPrice = closes[closes.length - 1]\n    const currentSMA20 = sma20[sma20.length - 1]\n    const currentSMA50 = sma50[sma50.length - 1]\n\n    let maSignal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'\n    let maDescription = `Price vs SMA20: ${((currentPrice / currentSMA20 - 1) * 100).toFixed(2)}%`\n\n    if (currentPrice > currentSMA20 && currentSMA20 > currentSMA50) {\n      maSignal = 'BUY'\n      maDescription += ' - Bullish trend'\n    } else if (currentPrice < currentSMA20 && currentSMA20 < currentSMA50) {\n      maSignal = 'SELL'\n      maDescription += ' - Bearish trend'\n    } else {\n      maDescription += ' - Mixed signals'\n    }\n\n    indicators.push({\n      name: 'Moving Averages',\n      value: (currentPrice / currentSMA20 - 1) * 100,\n      signal: maSignal,\n      description: maDescription\n    })\n\n    // MACD Analysis\n    const macdData = this.macd(closes)\n    const currentMACD = macdData.macd[macdData.macd.length - 1]\n    const currentSignal = macdData.signal[macdData.signal.length - 1]\n    const currentHistogram = macdData.histogram[macdData.histogram.length - 1]\n\n    let macdSignal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'\n    let macdDescription = `MACD: ${currentMACD.toFixed(4)}, Signal: ${currentSignal.toFixed(4)}`\n\n    if (currentMACD > currentSignal && currentHistogram > 0) {\n      macdSignal = 'BUY'\n      macdDescription += ' - Bullish momentum'\n    } else if (currentMACD < currentSignal && currentHistogram < 0) {\n      macdSignal = 'SELL'\n      macdDescription += ' - Bearish momentum'\n    } else {\n      macdDescription += ' - Momentum shifting'\n    }\n\n    indicators.push({\n      name: 'MACD',\n      value: currentHistogram,\n      signal: macdSignal,\n      description: macdDescription\n    })\n\n    // Volume Analysis\n    const volumeData = this.volumeAnalysis(candles)\n    let volumeSignal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'\n    let volumeDescription = `Volume: ${(volumeData.volumeRatio * 100).toFixed(0)}% of average`\n\n    if (volumeData.isHighVolume) {\n      volumeSignal = 'BUY'\n      volumeDescription += ' - High volume confirms move'\n    } else if (volumeData.isLowVolume) {\n      volumeSignal = 'SELL'\n      volumeDescription += ' - Low volume, weak conviction'\n    } else {\n      volumeDescription += ' - Normal volume'\n    }\n\n    indicators.push({\n      name: 'Volume',\n      value: volumeData.volumeRatio,\n      signal: volumeSignal,\n      description: volumeDescription\n    })\n\n    return indicators\n  }\n}\n"], "names": [], "mappings": ";;;;AAEO,MAAM;IACX,wBAAwB;IACxB,OAAO,IAAI,IAAc,EAAE,MAAc,EAAY;QACnD,MAAM,SAAmB,EAAE;QAC3B,IAAK,IAAI,IAAI,SAAS,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YAC7C,MAAM,MAAM,KAAK,KAAK,CAAC,IAAI,SAAS,GAAG,IAAI,GAAG,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG;YACtE,OAAO,IAAI,CAAC,MAAM;QACpB;QACA,OAAO;IACT;IAEA,6BAA6B;IAC7B,OAAO,IAAI,IAAc,EAAE,MAAc,EAAY;QACnD,MAAM,SAAmB,EAAE;QAC3B,MAAM,aAAa,IAAI,CAAC,SAAS,CAAC;QAElC,iCAAiC;QACjC,IAAI,MAAM,KAAK,KAAK,CAAC,GAAG,QAAQ,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG,KAAK;QAC7D,OAAO,IAAI,CAAC;QAEZ,IAAK,IAAI,IAAI,QAAQ,IAAI,KAAK,MAAM,EAAE,IAAK;YACzC,MAAM,AAAC,IAAI,CAAC,EAAE,GAAG,aAAe,MAAM,CAAC,IAAI,UAAU;YACrD,OAAO,IAAI,CAAC;QACd;QAEA,OAAO;IACT;IAEA,0BAA0B;IAC1B,OAAO,IAAI,IAAc,EAAE,SAAiB,EAAE,EAAY;QACxD,MAAM,QAAkB,EAAE;QAC1B,MAAM,SAAmB,EAAE;QAE3B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YACpC,MAAM,SAAS,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE;YACpC,MAAM,IAAI,CAAC,SAAS,IAAI,SAAS;YACjC,OAAO,IAAI,CAAC,SAAS,IAAI,KAAK,GAAG,CAAC,UAAU;QAC9C;QAEA,MAAM,WAAW,IAAI,CAAC,GAAG,CAAC,OAAO;QACjC,MAAM,YAAY,IAAI,CAAC,GAAG,CAAC,QAAQ;QAEnC,OAAO,SAAS,GAAG,CAAC,CAAC,MAAM;YACzB,MAAM,KAAK,OAAO,SAAS,CAAC,EAAE;YAC9B,OAAO,MAAO,MAAM,CAAC,IAAI,EAAE;QAC7B;IACF;IAEA,+CAA+C;IAC/C,OAAO,KAAK,IAAc,EAAE,aAAqB,EAAE,EAAE,aAAqB,EAAE,EAAE,eAAuB,CAAC,EAAE;QACtG,MAAM,UAAU,IAAI,CAAC,GAAG,CAAC,MAAM;QAC/B,MAAM,UAAU,IAAI,CAAC,GAAG,CAAC,MAAM;QAE/B,sCAAsC;QACtC,MAAM,aAAa,aAAa;QAChC,MAAM,WAAW,QAAQ,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC,MAAM,IAAM,OAAO,OAAO,CAAC,EAAE;QAE7E,MAAM,aAAa,IAAI,CAAC,GAAG,CAAC,UAAU;QACtC,MAAM,YAAY,SAAS,KAAK,CAAC,eAAe,GAAG,GAAG,CAAC,CAAC,MAAM,IAAM,OAAO,UAAU,CAAC,EAAE;QAExF,OAAO;YACL,MAAM;YACN,QAAQ;YACR;QACF;IACF;IAEA,kBAAkB;IAClB,OAAO,eAAe,IAAc,EAAE,SAAiB,EAAE,EAAE,SAAiB,CAAC,EAAE;QAC7E,MAAM,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM;QAC3B,MAAM,QAAQ,IAAI,GAAG,CAAC,CAAC,KAAK;YAC1B,MAAM,QAAQ,KAAK,KAAK,CAAC,GAAG,IAAI;YAChC,MAAM,WAAW,MAAM,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,KAAK,GAAG,CAAC,MAAM,KAAK,IAAI,KAAK;YAC/E,MAAM,oBAAoB,KAAK,IAAI,CAAC;YAEpC,OAAO;gBACL,OAAO,MAAO,oBAAoB;gBAClC,QAAQ;gBACR,OAAO,MAAO,oBAAoB;YACpC;QACF;QAEA,OAAO;IACT;IAEA,gCAAgC;IAChC,OAAO,sBAAsB,OAA0B,EAAE,WAAmB,EAAE,EAA+C;QAC3H,MAAM,QAAQ,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;QACrC,MAAM,OAAO,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG;QAEnC,MAAM,aAAuB,EAAE;QAC/B,MAAM,UAAoB,EAAE;QAE5B,IAAK,IAAI,IAAI,UAAU,IAAI,QAAQ,MAAM,GAAG,UAAU,IAAK;YACzD,MAAM,cAAc,KAAK,CAAC,EAAE;YAC5B,MAAM,aAAa,IAAI,CAAC,EAAE;YAE1B,2CAA2C;YAC3C,MAAM,eAAe,MAAM,KAAK,CAAC,IAAI,UAAU,GAAG,KAAK,CAAC,CAAA,IAAK,KAAK,gBAC9C,MAAM,KAAK,CAAC,IAAI,GAAG,IAAI,WAAW,GAAG,KAAK,CAAC,CAAA,IAAK,KAAK;YAEzE,0CAA0C;YAC1C,MAAM,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,GAAG,KAAK,CAAC,CAAA,IAAK,KAAK,eAC7C,KAAK,KAAK,CAAC,IAAI,GAAG,IAAI,WAAW,GAAG,KAAK,CAAC,CAAA,IAAK,KAAK;YAErE,IAAI,cAAc,WAAW,IAAI,CAAC;YAClC,IAAI,WAAW,QAAQ,IAAI,CAAC;QAC9B;QAEA,OAAO;YAAE;YAAS;QAAW;IAC/B;IAEA,kBAAkB;IAClB,OAAO,eAAe,OAA0B,EAAE,SAAiB,EAAE,EAAE;QACrE,MAAM,UAAU,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;QACzC,MAAM,YAAY,IAAI,CAAC,GAAG,CAAC,SAAS;QACpC,MAAM,gBAAgB,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE;QACjD,MAAM,mBAAmB,SAAS,CAAC,UAAU,MAAM,GAAG,EAAE;QAExD,OAAO;YACL;YACA,eAAe;YACf,aAAa,gBAAgB;YAC7B,cAAc,gBAAgB,mBAAmB;YACjD,aAAa,gBAAgB,mBAAmB;QAClD;IACF;IAEA,yBAAyB;IACzB,OAAO,kBAAkB,OAA0B,EAAwB;QACzE,MAAM,SAAS,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;QACvC,MAAM,aAAmC,EAAE;QAE3C,eAAe;QACf,MAAM,MAAM,IAAI,CAAC,GAAG,CAAC;QACrB,MAAM,aAAa,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;QAEtC,IAAI,YAAwC;QAC5C,IAAI,iBAAiB,CAAC,KAAK,EAAE,WAAW,OAAO,CAAC,IAAI;QAEpD,IAAI,aAAa,IAAI;YACnB,YAAY;YACZ,kBAAkB;QACpB,OAAO,IAAI,aAAa,IAAI;YAC1B,YAAY;YACZ,kBAAkB;QACpB,OAAO;YACL,kBAAkB;QACpB;QAEA,WAAW,IAAI,CAAC;YACd,MAAM;YACN,OAAO;YACP,QAAQ;YACR,aAAa;QACf;QAEA,0BAA0B;QAC1B,MAAM,QAAQ,IAAI,CAAC,GAAG,CAAC,QAAQ;QAC/B,MAAM,QAAQ,IAAI,CAAC,GAAG,CAAC,QAAQ;QAC/B,MAAM,eAAe,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;QAC9C,MAAM,eAAe,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;QAC5C,MAAM,eAAe,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;QAE5C,IAAI,WAAuC;QAC3C,IAAI,gBAAgB,CAAC,gBAAgB,EAAE,CAAC,CAAC,eAAe,eAAe,CAAC,IAAI,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAE9F,IAAI,eAAe,gBAAgB,eAAe,cAAc;YAC9D,WAAW;YACX,iBAAiB;QACnB,OAAO,IAAI,eAAe,gBAAgB,eAAe,cAAc;YACrE,WAAW;YACX,iBAAiB;QACnB,OAAO;YACL,iBAAiB;QACnB;QAEA,WAAW,IAAI,CAAC;YACd,MAAM;YACN,OAAO,CAAC,eAAe,eAAe,CAAC,IAAI;YAC3C,QAAQ;YACR,aAAa;QACf;QAEA,gBAAgB;QAChB,MAAM,WAAW,IAAI,CAAC,IAAI,CAAC;QAC3B,MAAM,cAAc,SAAS,IAAI,CAAC,SAAS,IAAI,CAAC,MAAM,GAAG,EAAE;QAC3D,MAAM,gBAAgB,SAAS,MAAM,CAAC,SAAS,MAAM,CAAC,MAAM,GAAG,EAAE;QACjE,MAAM,mBAAmB,SAAS,SAAS,CAAC,SAAS,SAAS,CAAC,MAAM,GAAG,EAAE;QAE1E,IAAI,aAAyC;QAC7C,IAAI,kBAAkB,CAAC,MAAM,EAAE,YAAY,OAAO,CAAC,GAAG,UAAU,EAAE,cAAc,OAAO,CAAC,IAAI;QAE5F,IAAI,cAAc,iBAAiB,mBAAmB,GAAG;YACvD,aAAa;YACb,mBAAmB;QACrB,OAAO,IAAI,cAAc,iBAAiB,mBAAmB,GAAG;YAC9D,aAAa;YACb,mBAAmB;QACrB,OAAO;YACL,mBAAmB;QACrB;QAEA,WAAW,IAAI,CAAC;YACd,MAAM;YACN,OAAO;YACP,QAAQ;YACR,aAAa;QACf;QAEA,kBAAkB;QAClB,MAAM,aAAa,IAAI,CAAC,cAAc,CAAC;QACvC,IAAI,eAA2C;QAC/C,IAAI,oBAAoB,CAAC,QAAQ,EAAE,CAAC,WAAW,WAAW,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,YAAY,CAAC;QAE1F,IAAI,WAAW,YAAY,EAAE;YAC3B,eAAe;YACf,qBAAqB;QACvB,OAAO,IAAI,WAAW,WAAW,EAAE;YACjC,eAAe;YACf,qBAAqB;QACvB,OAAO;YACL,qBAAqB;QACvB;QAEA,WAAW,IAAI,CAAC;YACd,MAAM;YACN,OAAO,WAAW,WAAW;YAC7B,QAAQ;YACR,aAAa;QACf;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 258, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/lib/swingStrategies.ts"], "sourcesContent": ["import { CandlestickData, StockData, SwingTradingAnalysis } from '@/types/trading'\nimport { TechnicalIndicators } from './indicators'\n\nexport interface StrategySetup {\n  strategy: 'overnight_momentum' | 'technical_breakout'\n  confidence: number\n  entryPrice: number\n  stopLoss: number\n  targets: number[]\n  positionSize: number\n  riskAmount: number\n  holdingPeriod: 'overnight' | 'days_to_weeks'\n  keyLevel: number\n  invalidation: string\n  notes: string[]\n  // Precise trading execution details\n  preciseEntry: {\n    price: number\n    orderType: 'market' | 'limit'\n    timing: string\n    conditions: string[]\n    urgency: 'immediate' | 'wait_for_pullback' | 'breakout_confirmation'\n  }\n  preciseExit: {\n    stopLoss: {\n      price: number\n      orderType: 'stop' | 'stop_limit'\n      reason: string\n      triggerConditions: string[]\n    }\n    takeProfits: Array<{\n      price: number\n      percentage: number // % of position to sell\n      target: string // \"R1\", \"R2\", \"Extension\", etc.\n      reasoning: string\n      orderType: 'limit' | 'market'\n    }>\n  }\n  riskManagement: {\n    maxRiskDollars: number\n    accountRiskPercent: number\n    sharesForRisk: number\n    invalidationPrice: number\n    timeStopHours: number\n    maxDrawdownPercent: number\n  }\n  executionPlan: {\n    entryInstructions: string[]\n    exitInstructions: string[]\n    monitoringPoints: string[]\n    contingencyPlans: string[]\n  }\n}\n\nexport interface SwingSetupCriteria {\n  // Basic filters\n  minPrice: number\n  minVolume: number\n  minMarketCap: number\n  minATRPercent: number\n  \n  // Technical requirements\n  above200SMA: boolean\n  maxDistanceFrom8EMA: number // in ATR units\n  minRoomToResistance: number // in ATR units\n  \n  // Timing\n  scanTimeStart: string // \"12:00\"\n  scanTimeEnd: string   // \"16:00\"\n  \n  // Risk management\n  maxRiskPerTrade: number // percentage of account\n  maxConcurrentPositions: number\n}\n\nexport class SwingTradingStrategies {\n  private static readonly DEFAULT_CRITERIA: SwingSetupCriteria = {\n    minPrice: 5.0,\n    minVolume: 500000,\n    minMarketCap: **********, // $1B\n    minATRPercent: 2.0,\n    above200SMA: true,\n    maxDistanceFrom8EMA: 2.0, // 2x ATR\n    minRoomToResistance: 1.0, // 1 ATR minimum\n    scanTimeStart: \"12:00\",\n    scanTimeEnd: \"16:00\",\n    maxRiskPerTrade: 1.0, // 1% max risk\n    maxConcurrentPositions: 3\n  }\n\n  // Strategy #1: Overnight Momentum Continuation\n  static analyzeOvernightMomentum(\n    symbol: string,\n    candles: CandlestickData[],\n    quote: StockData,\n    accountSize: number = 100000\n  ): StrategySetup | null {\n    if (candles.length < 50) return null\n\n    const closes = candles.map(c => c.close)\n    const highs = candles.map(c => c.high)\n    const lows = candles.map(c => c.low)\n    const volumes = candles.map(c => c.volume)\n    \n    const currentPrice = quote.price\n    const currentVolume = quote.volume\n    const changePercent = quote.changePercent\n\n    // Calculate technical indicators (adjusted for shorter history)\n    const sma50 = TechnicalIndicators.sma(closes, Math.min(50, closes.length - 1)) // Use 50-day instead of 200-day\n    const ema8 = TechnicalIndicators.ema(closes, Math.min(8, closes.length - 1))\n    const atr = this.calculateATR(candles, Math.min(14, candles.length - 1))\n\n    const current50SMA = sma50[sma50.length - 1]\n    const current8EMA = ema8[ema8.length - 1]\n    const currentATR = atr[atr.length - 1]\n\n    // Basic qualification filters (using 50-day SMA instead of 200-day)\n    if (!this.passesBasicFilters(quote, currentVolume, current50SMA, currentPrice)) {\n      console.log(`❌ ${symbol} failed basic filters`)\n      return null\n    }\n\n    console.log(`✅ ${symbol} passed basic filters, analyzing strategy...`)\n\n    // Check if it's a top intraday gainer (top decile movers)\n    console.log(`📊 ${symbol} momentum check: ${changePercent}% (need ≥2%)`)\n    if (changePercent < 2.0) {\n      console.log(`❌ ${symbol} failed momentum: ${changePercent}% < 2%`)\n      return null // Minimum 2% gain for momentum\n    }\n\n    // Check distance from 8-EMA (not wildly extended)\n    const distanceFrom8EMA = Math.abs(currentPrice - current8EMA) / currentATR\n    console.log(`📈 ${symbol} EMA distance: ${distanceFrom8EMA.toFixed(2)} ATR (max ${this.DEFAULT_CRITERIA.maxDistanceFrom8EMA})`)\n    if (distanceFrom8EMA > this.DEFAULT_CRITERIA.maxDistanceFrom8EMA) {\n      console.log(`❌ ${symbol} failed EMA distance check`)\n      return null\n    }\n\n    // Look for defended intraday level (simplified - using VWAP proxy)\n    const vwap = this.calculateVWAP(candles.slice(-1)[0])\n    const keyLevel = Math.max(vwap, currentPrice * 0.98) // Approximate defended level\n\n    // Check if holding gains (>50% of day's range)\n    const todayHigh = highs[highs.length - 1]\n    const todayLow = lows[lows.length - 1]\n    const dayRange = todayHigh - todayLow\n    const currentFromLow = currentPrice - todayLow\n    const holdingGainsPercent = currentFromLow / dayRange\n\n    console.log(`📊 ${symbol} holding gains: ${(holdingGainsPercent * 100).toFixed(1)}% (need ≥50%)`)\n    if (holdingGainsPercent < 0.5) {\n      console.log(`❌ ${symbol} failed holding gains check`)\n      return null // Must hold >50% of range\n    }\n\n    // Calculate room to next resistance\n    const roomToResistance = this.calculateRoomToResistance(candles, currentPrice, currentATR)\n    console.log(`📊 ${symbol} room to resistance: ${roomToResistance.toFixed(2)} ATR (need ≥${this.DEFAULT_CRITERIA.minRoomToResistance})`)\n    if (roomToResistance < this.DEFAULT_CRITERIA.minRoomToResistance) {\n      console.log(`❌ ${symbol} failed resistance room check`)\n      return null\n    }\n\n    // Position sizing (risk 0.5-1% of account)\n    const riskPercent = 0.75 // 0.75% risk for overnight holds\n    const stopDistance = currentPrice - keyLevel\n    const riskAmount = accountSize * (riskPercent / 100)\n    const positionSize = Math.floor(riskAmount / stopDistance)\n\n    // Targets: Pre-market scale at 3-5%, opening hour at 5-8%\n    const targets = [\n      currentPrice * 1.03, // 3% pre-market target\n      currentPrice * 1.05, // 5% opening hour target\n      currentPrice * 1.08  // 8% extended target\n    ]\n\n    const confidence = this.calculateOvernightConfidence(\n      changePercent, holdingGainsPercent, currentVolume, roomToResistance\n    )\n\n    return {\n      strategy: 'overnight_momentum',\n      confidence,\n      entryPrice: currentPrice,\n      stopLoss: keyLevel,\n      targets,\n      positionSize,\n      riskAmount,\n      holdingPeriod: 'overnight',\n      keyLevel,\n      invalidation: `Daily close below ${keyLevel.toFixed(2)} or gap down below level`,\n      notes: [\n        'Enter final 30-60 min before close',\n        'Exit pre-market on strength or first 45min',\n        'Hard stop if gaps below defended level',\n        'Scale out aggressively if gaps >1 ATR up'\n      ],\n      // Precise entry execution\n      preciseEntry: {\n        price: currentPrice * 0.999, // Slightly below current for better fill\n        orderType: 'limit',\n        timing: 'Final 30-60 minutes before market close',\n        conditions: [\n          `Stock holding above ${keyLevel.toFixed(2)} (defended level)`,\n          `Volume above ${(currentVolume * 0.8).toLocaleString()} shares`,\n          `Price above ${current8EMA.toFixed(2)} (8-EMA)`,\n          'No late-day selling pressure'\n        ],\n        urgency: 'wait_for_pullback'\n      },\n      // Precise exit execution\n      preciseExit: {\n        stopLoss: {\n          price: keyLevel * 0.995, // Slightly below key level\n          orderType: 'stop',\n          reason: 'Defended level broken - invalidates setup',\n          triggerConditions: [\n            'Any close below defended level',\n            'Gap down below key level',\n            'Heavy selling into close'\n          ]\n        },\n        takeProfits: [\n          {\n            price: targets[0],\n            percentage: 33,\n            target: 'T1 - Pre-market (3%)',\n            reasoning: 'Take profits on pre-market strength',\n            orderType: 'limit'\n          },\n          {\n            price: targets[1],\n            percentage: 33,\n            target: 'T2 - Opening hour (5%)',\n            reasoning: 'Scale out on opening momentum',\n            orderType: 'limit'\n          },\n          {\n            price: targets[2],\n            percentage: 34,\n            target: 'T3 - Extended (8%)',\n            reasoning: 'Final exit on extended move',\n            orderType: 'limit'\n          }\n        ]\n      },\n      // Risk management details\n      riskManagement: {\n        maxRiskDollars: riskAmount,\n        accountRiskPercent: riskPercent,\n        sharesForRisk: positionSize,\n        invalidationPrice: keyLevel,\n        timeStopHours: 18, // Exit by next day close if no movement\n        maxDrawdownPercent: 2.0\n      },\n      // Execution plan\n      executionPlan: {\n        entryInstructions: [\n          '1. Wait for final 30-60 minutes before close',\n          '2. Confirm stock is holding defended level',\n          '3. Place limit order slightly below current price',\n          '4. Cancel if not filled by close'\n        ],\n        exitInstructions: [\n          '1. Set stop-loss immediately after fill',\n          '2. Monitor pre-market for gap up',\n          '3. Scale out 1/3 at each target level',\n          '4. Exit all by 10:15 AM if no momentum'\n        ],\n        monitoringPoints: [\n          'Pre-market price action and volume',\n          'Opening gap and first 15-minute candle',\n          'Key level defense throughout session',\n          'Overall market sentiment'\n        ],\n        contingencyPlans: [\n          'If gaps down: Exit immediately at market open',\n          'If gaps up >2%: Scale out more aggressively',\n          'If sideways: Exit by 10:15 AM',\n          'If market weakness: Tighten stops'\n        ]\n      }\n    }\n  }\n\n  // Strategy #2: Technical Breakout Trend-Follow (8-EMA + 200-SMA)\n  static analyzeTechnicalBreakout(\n    symbol: string,\n    candles: CandlestickData[],\n    quote: StockData,\n    accountSize: number = 100000\n  ): StrategySetup | null {\n    if (candles.length < 50) return null\n\n    const closes = candles.map(c => c.close)\n    const volumes = candles.map(c => c.volume)\n    const currentPrice = quote.price\n\n    // Calculate technical indicators (adjusted for shorter history)\n    const sma50 = TechnicalIndicators.sma(closes, Math.min(50, closes.length - 1))\n    const ema8 = TechnicalIndicators.ema(closes, Math.min(8, closes.length - 1))\n    const atr = this.calculateATR(candles, Math.min(14, candles.length - 1))\n\n    const current50SMA = sma50[sma50.length - 1]\n    const current8EMA = ema8[ema8.length - 1]\n    const currentATR = atr[atr.length - 1]\n\n    // Basic qualification filters (using 50-day SMA)\n    if (!this.passesBasicFilters(quote, quote.volume, current50SMA, currentPrice)) {\n      return null\n    }\n\n    // Check if in clear uptrend above 50-SMA (adjusted from 200-SMA)\n    if (currentPrice <= current50SMA) return null\n\n    // Check 8-EMA behavior - should be \"hugging\" the 8-EMA\n    const distanceFrom8EMA = Math.abs(currentPrice - current8EMA)\n    const emaDistancePercent = (distanceFrom8EMA / currentPrice) * 100\n    \n    // Should be close to 8-EMA (within 2-3% for quality trend-follow)\n    if (emaDistancePercent > 3.0) return null\n\n    // Check for recent breakout or EMA reclaim\n    const recentEMAReclaim = this.checkEMAReclaim(closes, ema8, 5) // Last 5 days\n    if (!recentEMAReclaim) return null\n\n    // Volume expansion check\n    const avgVolume = TechnicalIndicators.sma(volumes, 20)\n    const currentAvgVolume = avgVolume[avgVolume.length - 1]\n    const volumeExpansion = quote.volume / currentAvgVolume\n    \n    if (volumeExpansion < 1.2) return null // Need some volume expansion\n\n    // Calculate room to next resistance\n    const roomToResistance = this.calculateRoomToResistance(candles, currentPrice, currentATR)\n    if (roomToResistance < 1.5) return null // Need more room for trend-follow\n\n    // Position sizing (risk 1% of account)\n    const riskPercent = 1.0\n    const stopDistance = currentPrice - current8EMA // Stop at 8-EMA break\n    const riskAmount = accountSize * (riskPercent / 100)\n    const positionSize = Math.floor(riskAmount / stopDistance)\n\n    // Targets: Scale at resistance levels\n    const targets = [\n      currentPrice * 1.05, // 5% first target\n      currentPrice * 1.10, // 10% second target\n      currentPrice * 1.15  // 15% extended target\n    ]\n\n    const confidence = this.calculateBreakoutConfidence(\n      emaDistancePercent, volumeExpansion, roomToResistance, quote.changePercent\n    )\n\n    return {\n      strategy: 'technical_breakout',\n      confidence,\n      entryPrice: currentPrice,\n      stopLoss: current8EMA,\n      targets,\n      positionSize,\n      riskAmount,\n      holdingPeriod: 'days_to_weeks',\n      keyLevel: current8EMA,\n      invalidation: `Daily close below 8-EMA (${current8EMA.toFixed(2)})`,\n      notes: [\n        'Enter on afternoon reclaim of 8-EMA',\n        'Add only on higher-low pullbacks to 8-EMA',\n        'Scale partials at resistance levels',\n        'Exit on daily close below 8-EMA'\n      ],\n      // Precise entry execution\n      preciseEntry: {\n        price: current8EMA * 1.002, // Slightly above 8-EMA for confirmation\n        orderType: 'limit',\n        timing: 'Afternoon reclaim or first pullback to 8-EMA',\n        conditions: [\n          `Price reclaiming ${current8EMA.toFixed(2)} (8-EMA) with volume`,\n          `Above ${current50SMA.toFixed(2)} (50-day SMA)`,\n          `Volume expansion above ${(quote.volume * 1.2).toLocaleString()}`,\n          'No major resistance overhead'\n        ],\n        urgency: 'breakout_confirmation'\n      },\n      // Precise exit execution\n      preciseExit: {\n        stopLoss: {\n          price: current8EMA * 0.998, // Slightly below 8-EMA\n          orderType: 'stop',\n          reason: '8-EMA breakdown invalidates trend-follow setup',\n          triggerConditions: [\n            'Daily close below 8-EMA',\n            'Intraday break with volume',\n            'Loss of 50-SMA support'\n          ]\n        },\n        takeProfits: [\n          {\n            price: targets[0],\n            percentage: 25,\n            target: 'R1 - First resistance (5%)',\n            reasoning: 'Take partial profits at first resistance',\n            orderType: 'limit'\n          },\n          {\n            price: targets[1],\n            percentage: 35,\n            target: 'R2 - Major resistance (10%)',\n            reasoning: 'Scale out at major resistance level',\n            orderType: 'limit'\n          },\n          {\n            price: targets[2],\n            percentage: 40,\n            target: 'R3 - Extension (15%)',\n            reasoning: 'Final exit on extended breakout',\n            orderType: 'limit'\n          }\n        ]\n      },\n      // Risk management details\n      riskManagement: {\n        maxRiskDollars: riskAmount,\n        accountRiskPercent: riskPercent,\n        sharesForRisk: positionSize,\n        invalidationPrice: current8EMA,\n        timeStopHours: 72, // 3 trading days max hold if no progress\n        maxDrawdownPercent: 3.0\n      },\n      // Execution plan\n      executionPlan: {\n        entryInstructions: [\n          '1. Wait for afternoon reclaim of 8-EMA',\n          '2. Confirm volume expansion on breakout',\n          '3. Place limit order above 8-EMA',\n          '4. Only enter on higher-low pullbacks'\n        ],\n        exitInstructions: [\n          '1. Set stop-loss below 8-EMA immediately',\n          '2. Scale out 25% at first resistance',\n          '3. Trail stop to breakeven after R1',\n          '4. Exit remaining on 8-EMA breakdown'\n        ],\n        monitoringPoints: [\n          '8-EMA as dynamic support/resistance',\n          'Volume confirmation on moves',\n          'Overall market trend alignment',\n          'Sector strength/weakness'\n        ],\n        contingencyPlans: [\n          'If fails at resistance: Tighten stops',\n          'If market weakness: Exit early',\n          'If sector rotation: Consider exit',\n          'If extended: Take more profits'\n        ]\n      }\n    }\n  }\n\n  // Helper methods\n  private static passesBasicFilters(\n    quote: StockData,\n    volume: number,\n    sma50: number,\n    price: number\n  ): boolean {\n    const priceCheck = price >= this.DEFAULT_CRITERIA.minPrice\n    const volumeCheck = volume >= this.DEFAULT_CRITERIA.minVolume\n    const marketCapCheck = (quote.marketCap || 0) >= this.DEFAULT_CRITERIA.minMarketCap\n    const smaCheck = price > sma50\n\n    console.log(`🔍 Basic filters for ${quote.symbol}:`)\n    console.log(`  💰 Price: ${price} >= ${this.DEFAULT_CRITERIA.minPrice} = ${priceCheck}`)\n    console.log(`  📊 Volume: ${volume} >= ${this.DEFAULT_CRITERIA.minVolume} = ${volumeCheck}`)\n    console.log(`  🏢 Market Cap: ${quote.marketCap || 0} >= ${this.DEFAULT_CRITERIA.minMarketCap} = ${marketCapCheck}`)\n    console.log(`  📈 Above SMA50: ${price} > ${sma50} = ${smaCheck}`)\n\n    const passes = priceCheck && volumeCheck && marketCapCheck && smaCheck\n    console.log(`  ✅ Overall: ${passes}`)\n\n    return passes\n  }\n\n  private static calculateATR(candles: CandlestickData[], period: number): number[] {\n    const trueRanges: number[] = []\n    \n    for (let i = 1; i < candles.length; i++) {\n      const high = candles[i].high\n      const low = candles[i].low\n      const prevClose = candles[i - 1].close\n      \n      const tr = Math.max(\n        high - low,\n        Math.abs(high - prevClose),\n        Math.abs(low - prevClose)\n      )\n      \n      trueRanges.push(tr)\n    }\n    \n    return TechnicalIndicators.sma(trueRanges, period)\n  }\n\n  private static calculateVWAP(candle: CandlestickData): number {\n    // Simplified VWAP calculation using typical price\n    return (candle.high + candle.low + candle.close) / 3\n  }\n\n  private static calculateRoomToResistance(\n    candles: CandlestickData[], \n    currentPrice: number, \n    atr: number\n  ): number {\n    // Find recent highs as resistance levels\n    const recentHighs = candles.slice(-20).map(c => c.high)\n    const maxHigh = Math.max(...recentHighs)\n    const roomToHigh = maxHigh - currentPrice\n    return roomToHigh / atr\n  }\n\n  private static checkEMAReclaim(closes: number[], ema8: number[], lookback: number): boolean {\n    // Check if price recently reclaimed 8-EMA\n    for (let i = Math.max(0, closes.length - lookback); i < closes.length - 1; i++) {\n      if (closes[i] < ema8[i] && closes[i + 1] > ema8[i + 1]) {\n        return true // Found a reclaim\n      }\n    }\n    return false\n  }\n\n  private static calculateOvernightConfidence(\n    changePercent: number,\n    holdingGains: number,\n    volume: number,\n    roomToResistance: number\n  ): number {\n    let confidence = 50\n\n    // Change percent bonus\n    if (changePercent > 5) confidence += 15\n    else if (changePercent > 3) confidence += 10\n    else if (changePercent > 2) confidence += 5\n\n    // Holding gains bonus\n    if (holdingGains > 0.8) confidence += 15\n    else if (holdingGains > 0.6) confidence += 10\n    else if (holdingGains > 0.5) confidence += 5\n\n    // Volume bonus\n    if (volume > 2000000) confidence += 10\n    else if (volume > 1000000) confidence += 5\n\n    // Room to resistance\n    if (roomToResistance > 2) confidence += 10\n    else if (roomToResistance > 1.5) confidence += 5\n\n    return Math.min(95, Math.max(30, confidence))\n  }\n\n  private static calculateBreakoutConfidence(\n    emaDistance: number,\n    volumeExpansion: number,\n    roomToResistance: number,\n    changePercent: number\n  ): number {\n    let confidence = 60\n\n    // EMA proximity bonus (closer is better for trend-follow)\n    if (emaDistance < 1) confidence += 15\n    else if (emaDistance < 2) confidence += 10\n    else if (emaDistance < 3) confidence += 5\n\n    // Volume expansion bonus\n    if (volumeExpansion > 2) confidence += 15\n    else if (volumeExpansion > 1.5) confidence += 10\n    else if (volumeExpansion > 1.2) confidence += 5\n\n    // Room to resistance\n    if (roomToResistance > 3) confidence += 15\n    else if (roomToResistance > 2) confidence += 10\n    else if (roomToResistance > 1.5) confidence += 5\n\n    // Positive momentum\n    if (changePercent > 2) confidence += 5\n\n    return Math.min(95, Math.max(40, confidence))\n  }\n}\n"], "names": [], "mappings": ";;;;AACA;;AA0EO,MAAM;IACX,OAAwB,mBAAuC;QAC7D,UAAU;QACV,WAAW;QACX,cAAc;QACd,eAAe;QACf,aAAa;QACb,qBAAqB;QACrB,qBAAqB;QACrB,eAAe;QACf,aAAa;QACb,iBAAiB;QACjB,wBAAwB;IAC1B,EAAC;IAED,+CAA+C;IAC/C,OAAO,yBACL,MAAc,EACd,OAA0B,EAC1B,KAAgB,EAChB,cAAsB,MAAM,EACN;QACtB,IAAI,QAAQ,MAAM,GAAG,IAAI,OAAO;QAEhC,MAAM,SAAS,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;QACvC,MAAM,QAAQ,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;QACrC,MAAM,OAAO,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG;QACnC,MAAM,UAAU,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;QAEzC,MAAM,eAAe,MAAM,KAAK;QAChC,MAAM,gBAAgB,MAAM,MAAM;QAClC,MAAM,gBAAgB,MAAM,aAAa;QAEzC,gEAAgE;QAChE,MAAM,QAAQ,0KAAmB,CAAC,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,IAAI,OAAO,MAAM,GAAG,IAAI,gCAAgC;;QAC/G,MAAM,OAAO,0KAAmB,CAAC,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,OAAO,MAAM,GAAG;QACzE,MAAM,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,KAAK,GAAG,CAAC,IAAI,QAAQ,MAAM,GAAG;QAErE,MAAM,eAAe,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;QAC5C,MAAM,cAAc,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QACzC,MAAM,aAAa,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;QAEtC,oEAAoE;QACpE,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,eAAe,cAAc,eAAe;YAC9E,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,OAAO,qBAAqB,CAAC;YAC9C,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,OAAO,4CAA4C,CAAC;QAErE,0DAA0D;QAC1D,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,OAAO,iBAAiB,EAAE,cAAc,YAAY,CAAC;QACvE,IAAI,gBAAgB,KAAK;YACvB,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,OAAO,kBAAkB,EAAE,cAAc,MAAM,CAAC;YACjE,OAAO,KAAK,+BAA+B;;QAC7C;QAEA,kDAAkD;QAClD,MAAM,mBAAmB,KAAK,GAAG,CAAC,eAAe,eAAe;QAChE,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,OAAO,eAAe,EAAE,iBAAiB,OAAO,CAAC,GAAG,UAAU,EAAE,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,CAAC;QAC9H,IAAI,mBAAmB,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,EAAE;YAChE,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,OAAO,0BAA0B,CAAC;YACnD,OAAO;QACT;QAEA,mEAAmE;QACnE,MAAM,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE;QACpD,MAAM,WAAW,KAAK,GAAG,CAAC,MAAM,eAAe,MAAM,6BAA6B;;QAElF,+CAA+C;QAC/C,MAAM,YAAY,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;QACzC,MAAM,WAAW,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QACtC,MAAM,WAAW,YAAY;QAC7B,MAAM,iBAAiB,eAAe;QACtC,MAAM,sBAAsB,iBAAiB;QAE7C,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,OAAO,gBAAgB,EAAE,CAAC,sBAAsB,GAAG,EAAE,OAAO,CAAC,GAAG,aAAa,CAAC;QAChG,IAAI,sBAAsB,KAAK;YAC7B,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,OAAO,2BAA2B,CAAC;YACpD,OAAO,KAAK,0BAA0B;;QACxC;QAEA,oCAAoC;QACpC,MAAM,mBAAmB,IAAI,CAAC,yBAAyB,CAAC,SAAS,cAAc;QAC/E,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,OAAO,qBAAqB,EAAE,iBAAiB,OAAO,CAAC,GAAG,YAAY,EAAE,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,CAAC;QACtI,IAAI,mBAAmB,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,EAAE;YAChE,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,OAAO,6BAA6B,CAAC;YACtD,OAAO;QACT;QAEA,2CAA2C;QAC3C,MAAM,cAAc,KAAK,iCAAiC;;QAC1D,MAAM,eAAe,eAAe;QACpC,MAAM,aAAa,cAAc,CAAC,cAAc,GAAG;QACnD,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa;QAE7C,0DAA0D;QAC1D,MAAM,UAAU;YACd,eAAe;YACf,eAAe;YACf,eAAe,KAAM,qBAAqB;SAC3C;QAED,MAAM,aAAa,IAAI,CAAC,4BAA4B,CAClD,eAAe,qBAAqB,eAAe;QAGrD,OAAO;YACL,UAAU;YACV;YACA,YAAY;YACZ,UAAU;YACV;YACA;YACA;YACA,eAAe;YACf;YACA,cAAc,CAAC,kBAAkB,EAAE,SAAS,OAAO,CAAC,GAAG,wBAAwB,CAAC;YAChF,OAAO;gBACL;gBACA;gBACA;gBACA;aACD;YACD,0BAA0B;YAC1B,cAAc;gBACZ,OAAO,eAAe;gBACtB,WAAW;gBACX,QAAQ;gBACR,YAAY;oBACV,CAAC,oBAAoB,EAAE,SAAS,OAAO,CAAC,GAAG,iBAAiB,CAAC;oBAC7D,CAAC,aAAa,EAAE,CAAC,gBAAgB,GAAG,EAAE,cAAc,GAAG,OAAO,CAAC;oBAC/D,CAAC,YAAY,EAAE,YAAY,OAAO,CAAC,GAAG,QAAQ,CAAC;oBAC/C;iBACD;gBACD,SAAS;YACX;YACA,yBAAyB;YACzB,aAAa;gBACX,UAAU;oBACR,OAAO,WAAW;oBAClB,WAAW;oBACX,QAAQ;oBACR,mBAAmB;wBACjB;wBACA;wBACA;qBACD;gBACH;gBACA,aAAa;oBACX;wBACE,OAAO,OAAO,CAAC,EAAE;wBACjB,YAAY;wBACZ,QAAQ;wBACR,WAAW;wBACX,WAAW;oBACb;oBACA;wBACE,OAAO,OAAO,CAAC,EAAE;wBACjB,YAAY;wBACZ,QAAQ;wBACR,WAAW;wBACX,WAAW;oBACb;oBACA;wBACE,OAAO,OAAO,CAAC,EAAE;wBACjB,YAAY;wBACZ,QAAQ;wBACR,WAAW;wBACX,WAAW;oBACb;iBACD;YACH;YACA,0BAA0B;YAC1B,gBAAgB;gBACd,gBAAgB;gBAChB,oBAAoB;gBACpB,eAAe;gBACf,mBAAmB;gBACnB,eAAe;gBACf,oBAAoB;YACtB;YACA,iBAAiB;YACjB,eAAe;gBACb,mBAAmB;oBACjB;oBACA;oBACA;oBACA;iBACD;gBACD,kBAAkB;oBAChB;oBACA;oBACA;oBACA;iBACD;gBACD,kBAAkB;oBAChB;oBACA;oBACA;oBACA;iBACD;gBACD,kBAAkB;oBAChB;oBACA;oBACA;oBACA;iBACD;YACH;QACF;IACF;IAEA,iEAAiE;IACjE,OAAO,yBACL,MAAc,EACd,OAA0B,EAC1B,KAAgB,EAChB,cAAsB,MAAM,EACN;QACtB,IAAI,QAAQ,MAAM,GAAG,IAAI,OAAO;QAEhC,MAAM,SAAS,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;QACvC,MAAM,UAAU,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;QACzC,MAAM,eAAe,MAAM,KAAK;QAEhC,gEAAgE;QAChE,MAAM,QAAQ,0KAAmB,CAAC,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,IAAI,OAAO,MAAM,GAAG;QAC3E,MAAM,OAAO,0KAAmB,CAAC,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,OAAO,MAAM,GAAG;QACzE,MAAM,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,KAAK,GAAG,CAAC,IAAI,QAAQ,MAAM,GAAG;QAErE,MAAM,eAAe,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;QAC5C,MAAM,cAAc,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QACzC,MAAM,aAAa,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;QAEtC,iDAAiD;QACjD,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,MAAM,MAAM,EAAE,cAAc,eAAe;YAC7E,OAAO;QACT;QAEA,iEAAiE;QACjE,IAAI,gBAAgB,cAAc,OAAO;QAEzC,uDAAuD;QACvD,MAAM,mBAAmB,KAAK,GAAG,CAAC,eAAe;QACjD,MAAM,qBAAqB,AAAC,mBAAmB,eAAgB;QAE/D,kEAAkE;QAClE,IAAI,qBAAqB,KAAK,OAAO;QAErC,2CAA2C;QAC3C,MAAM,mBAAmB,IAAI,CAAC,eAAe,CAAC,QAAQ,MAAM,GAAG,cAAc;;QAC7E,IAAI,CAAC,kBAAkB,OAAO;QAE9B,yBAAyB;QACzB,MAAM,YAAY,0KAAmB,CAAC,GAAG,CAAC,SAAS;QACnD,MAAM,mBAAmB,SAAS,CAAC,UAAU,MAAM,GAAG,EAAE;QACxD,MAAM,kBAAkB,MAAM,MAAM,GAAG;QAEvC,IAAI,kBAAkB,KAAK,OAAO,KAAK,6BAA6B;;QAEpE,oCAAoC;QACpC,MAAM,mBAAmB,IAAI,CAAC,yBAAyB,CAAC,SAAS,cAAc;QAC/E,IAAI,mBAAmB,KAAK,OAAO,KAAK,kCAAkC;;QAE1E,uCAAuC;QACvC,MAAM,cAAc;QACpB,MAAM,eAAe,eAAe,YAAY,sBAAsB;;QACtE,MAAM,aAAa,cAAc,CAAC,cAAc,GAAG;QACnD,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa;QAE7C,sCAAsC;QACtC,MAAM,UAAU;YACd,eAAe;YACf,eAAe;YACf,eAAe,KAAM,sBAAsB;SAC5C;QAED,MAAM,aAAa,IAAI,CAAC,2BAA2B,CACjD,oBAAoB,iBAAiB,kBAAkB,MAAM,aAAa;QAG5E,OAAO;YACL,UAAU;YACV;YACA,YAAY;YACZ,UAAU;YACV;YACA;YACA;YACA,eAAe;YACf,UAAU;YACV,cAAc,CAAC,yBAAyB,EAAE,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC;YACnE,OAAO;gBACL;gBACA;gBACA;gBACA;aACD;YACD,0BAA0B;YAC1B,cAAc;gBACZ,OAAO,cAAc;gBACrB,WAAW;gBACX,QAAQ;gBACR,YAAY;oBACV,CAAC,iBAAiB,EAAE,YAAY,OAAO,CAAC,GAAG,oBAAoB,CAAC;oBAChE,CAAC,MAAM,EAAE,aAAa,OAAO,CAAC,GAAG,aAAa,CAAC;oBAC/C,CAAC,uBAAuB,EAAE,CAAC,MAAM,MAAM,GAAG,GAAG,EAAE,cAAc,IAAI;oBACjE;iBACD;gBACD,SAAS;YACX;YACA,yBAAyB;YACzB,aAAa;gBACX,UAAU;oBACR,OAAO,cAAc;oBACrB,WAAW;oBACX,QAAQ;oBACR,mBAAmB;wBACjB;wBACA;wBACA;qBACD;gBACH;gBACA,aAAa;oBACX;wBACE,OAAO,OAAO,CAAC,EAAE;wBACjB,YAAY;wBACZ,QAAQ;wBACR,WAAW;wBACX,WAAW;oBACb;oBACA;wBACE,OAAO,OAAO,CAAC,EAAE;wBACjB,YAAY;wBACZ,QAAQ;wBACR,WAAW;wBACX,WAAW;oBACb;oBACA;wBACE,OAAO,OAAO,CAAC,EAAE;wBACjB,YAAY;wBACZ,QAAQ;wBACR,WAAW;wBACX,WAAW;oBACb;iBACD;YACH;YACA,0BAA0B;YAC1B,gBAAgB;gBACd,gBAAgB;gBAChB,oBAAoB;gBACpB,eAAe;gBACf,mBAAmB;gBACnB,eAAe;gBACf,oBAAoB;YACtB;YACA,iBAAiB;YACjB,eAAe;gBACb,mBAAmB;oBACjB;oBACA;oBACA;oBACA;iBACD;gBACD,kBAAkB;oBAChB;oBACA;oBACA;oBACA;iBACD;gBACD,kBAAkB;oBAChB;oBACA;oBACA;oBACA;iBACD;gBACD,kBAAkB;oBAChB;oBACA;oBACA;oBACA;iBACD;YACH;QACF;IACF;IAEA,iBAAiB;IACjB,OAAe,mBACb,KAAgB,EAChB,MAAc,EACd,KAAa,EACb,KAAa,EACJ;QACT,MAAM,aAAa,SAAS,IAAI,CAAC,gBAAgB,CAAC,QAAQ;QAC1D,MAAM,cAAc,UAAU,IAAI,CAAC,gBAAgB,CAAC,SAAS;QAC7D,MAAM,iBAAiB,CAAC,MAAM,SAAS,IAAI,CAAC,KAAK,IAAI,CAAC,gBAAgB,CAAC,YAAY;QACnF,MAAM,WAAW,QAAQ;QAEzB,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,MAAM,MAAM,CAAC,CAAC,CAAC;QACnD,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,MAAM,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,EAAE,YAAY;QACvF,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,OAAO,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,GAAG,EAAE,aAAa;QAC3F,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,MAAM,SAAS,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,GAAG,EAAE,gBAAgB;QACnH,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,MAAM,GAAG,EAAE,MAAM,GAAG,EAAE,UAAU;QAEjE,MAAM,SAAS,cAAc,eAAe,kBAAkB;QAC9D,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,QAAQ;QAEpC,OAAO;IACT;IAEA,OAAe,aAAa,OAA0B,EAAE,MAAc,EAAY;QAChF,MAAM,aAAuB,EAAE;QAE/B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,MAAM,OAAO,OAAO,CAAC,EAAE,CAAC,IAAI;YAC5B,MAAM,MAAM,OAAO,CAAC,EAAE,CAAC,GAAG;YAC1B,MAAM,YAAY,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK;YAEtC,MAAM,KAAK,KAAK,GAAG,CACjB,OAAO,KACP,KAAK,GAAG,CAAC,OAAO,YAChB,KAAK,GAAG,CAAC,MAAM;YAGjB,WAAW,IAAI,CAAC;QAClB;QAEA,OAAO,0KAAmB,CAAC,GAAG,CAAC,YAAY;IAC7C;IAEA,OAAe,cAAc,MAAuB,EAAU;QAC5D,kDAAkD;QAClD,OAAO,CAAC,OAAO,IAAI,GAAG,OAAO,GAAG,GAAG,OAAO,KAAK,IAAI;IACrD;IAEA,OAAe,0BACb,OAA0B,EAC1B,YAAoB,EACpB,GAAW,EACH;QACR,yCAAyC;QACzC,MAAM,cAAc,QAAQ,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;QACtD,MAAM,UAAU,KAAK,GAAG,IAAI;QAC5B,MAAM,aAAa,UAAU;QAC7B,OAAO,aAAa;IACtB;IAEA,OAAe,gBAAgB,MAAgB,EAAE,IAAc,EAAE,QAAgB,EAAW;QAC1F,0CAA0C;QAC1C,IAAK,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,OAAO,MAAM,GAAG,WAAW,IAAI,OAAO,MAAM,GAAG,GAAG,IAAK;YAC9E,IAAI,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,MAAM,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,EAAE;gBACtD,OAAO,KAAK,kBAAkB;;YAChC;QACF;QACA,OAAO;IACT;IAEA,OAAe,6BACb,aAAqB,EACrB,YAAoB,EACpB,MAAc,EACd,gBAAwB,EAChB;QACR,IAAI,aAAa;QAEjB,uBAAuB;QACvB,IAAI,gBAAgB,GAAG,cAAc;aAChC,IAAI,gBAAgB,GAAG,cAAc;aACrC,IAAI,gBAAgB,GAAG,cAAc;QAE1C,sBAAsB;QACtB,IAAI,eAAe,KAAK,cAAc;aACjC,IAAI,eAAe,KAAK,cAAc;aACtC,IAAI,eAAe,KAAK,cAAc;QAE3C,eAAe;QACf,IAAI,SAAS,SAAS,cAAc;aAC/B,IAAI,SAAS,SAAS,cAAc;QAEzC,qBAAqB;QACrB,IAAI,mBAAmB,GAAG,cAAc;aACnC,IAAI,mBAAmB,KAAK,cAAc;QAE/C,OAAO,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI;IACnC;IAEA,OAAe,4BACb,WAAmB,EACnB,eAAuB,EACvB,gBAAwB,EACxB,aAAqB,EACb;QACR,IAAI,aAAa;QAEjB,0DAA0D;QAC1D,IAAI,cAAc,GAAG,cAAc;aAC9B,IAAI,cAAc,GAAG,cAAc;aACnC,IAAI,cAAc,GAAG,cAAc;QAExC,yBAAyB;QACzB,IAAI,kBAAkB,GAAG,cAAc;aAClC,IAAI,kBAAkB,KAAK,cAAc;aACzC,IAAI,kBAAkB,KAAK,cAAc;QAE9C,qBAAqB;QACrB,IAAI,mBAAmB,GAAG,cAAc;aACnC,IAAI,mBAAmB,GAAG,cAAc;aACxC,IAAI,mBAAmB,KAAK,cAAc;QAE/C,oBAAoB;QACpB,IAAI,gBAAgB,GAAG,cAAc;QAErC,OAAO,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI;IACnC;AACF", "debugId": null}}, {"offset": {"line": 777, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/lib/polygon.ts"], "sourcesContent": ["import axios from 'axios'\nimport { CandlestickData, StockData } from '@/types/trading'\n\nconst POLYGON_BASE_URL = 'https://api.polygon.io'\nconst API_KEY = process.env.POLYGON_API_KEY\n\nexport class PolygonAPI {\n  private apiKey: string\n\n  constructor(apiKey?: string) {\n    this.apiKey = apiKey || API_KEY || ''\n    if (!this.apiKey) {\n      throw new Error('Polygon API key is required')\n    }\n  }\n\n  // Get current stock quote using Polygon's snapshot endpoint (better for paid plans)\n  async getStockQuote(symbol: string): Promise<StockData> {\n    try {\n      // Use snapshot endpoint for real-time data (available on paid plans)\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v2/snapshot/locale/us/markets/stocks/tickers/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      if (!response.data || !response.data.ticker) {\n        throw new Error(`No data found for ${symbol}`)\n      }\n\n      const data = response.data.ticker\n\n      // Extract data from Polygon snapshot response structure\n      const dayData = data.day || {}\n      const prevDayData = data.prevDay || {}\n      const minData = data.min || {}\n\n      // Use the most recent price available\n      const currentPrice = dayData.c || minData.c || prevDayData.c\n      const prevClose = prevDayData.c\n      const change = data.todaysChange || (currentPrice - prevClose)\n      const changePercent = data.todaysChangePerc || ((change / prevClose) * 100)\n\n      return {\n        symbol: symbol.toUpperCase(),\n        name: data.name || symbol.toUpperCase(),\n        price: currentPrice || 0,\n        change: change || 0,\n        changePercent: changePercent || 0,\n        volume: dayData.v || minData.v || 1000000, // Default to 1M volume if missing\n        marketCap: data.market_cap || this.estimateMarketCap(symbol, currentPrice || 0),\n        pe: undefined,\n        dividend: undefined\n      }\n    } catch (error) {\n      console.error('Error fetching stock quote from Polygon:', error)\n\n      // Fallback to previous day data if snapshot fails\n      try {\n        const fallbackResponse = await axios.get(\n          `${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/prev`,\n          {\n            params: {\n              adjusted: 'true',\n              apikey: this.apiKey\n            }\n          }\n        )\n\n        const data = fallbackResponse.data.results[0]\n        return {\n          symbol: symbol.toUpperCase(),\n          name: symbol.toUpperCase(),\n          price: data.c || 0,\n          change: (data.c - data.o) || 0,\n          changePercent: data.o ? ((data.c - data.o) / data.o) * 100 : 0,\n          volume: data.v || 1000000,\n          marketCap: this.estimateMarketCap(symbol, data.c || 0),\n          pe: undefined,\n          dividend: undefined\n        }\n      } catch (fallbackError) {\n        console.error('Polygon fallback also failed:', fallbackError)\n        throw new Error(`Failed to fetch quote for ${symbol}`)\n      }\n    }\n  }\n\n  /**\n   * Estimate market cap based on symbol and price\n   * This is a fallback when Polygon doesn't provide market cap data\n   */\n  private estimateMarketCap(symbol: string, price: number): number {\n    // Import stock universe data for better estimates\n    const stockEstimates: { [key: string]: number } = {\n      // Large cap (>$200B)\n      'AAPL': 3000000000000, 'MSFT': 2800000000000, 'NVDA': 1800000000000,\n      'GOOGL': 1700000000000, 'GOOG': 1700000000000, 'AMZN': ********00000,\n      'TSLA': 800000000000, 'META': 800000000000, 'BRK.B': 900000000000,\n\n      // Mid-large cap ($50B-$200B)\n      'JPM': 500000000000, 'V': 500000000000, 'UNH': 500000000000,\n      'JNJ': 450000000000, 'XOM': 450000000000, 'WMT': 600000000000,\n      'PG': ********0000, 'MA': ********0000, 'HD': ********0000,\n      'CVX': 300000000000, 'ABBV': 300000000000, 'BAC': 300000000000,\n      'COST': ********0000, 'AVGO': 600000000000, 'TSM': 500000000000,\n\n      // Mid cap ($10B-$50B)\n      'NFLX': ********0000, 'ORCL': ********0000, 'CRM': 250000000000,\n      'ADBE': 2********000, 'AMD': 2********000, 'INTC': ********0000,\n      'QCOM': 180000000000, 'TMO': ********0000, 'DHR': 180000000000,\n      'CAT': 180000000000, 'GE': 180000000000, 'DIS': 180000000000,\n      'VZ': 170000000000, 'PFE': 160000000000, 'NKE': ********0000,\n      'MS': ********0000, 'UBER': ********0000, 'C': 1********000,\n      'GS': 1********000, 'T': 1********000, 'AMGN': ********0000,\n      'HON': 1********000, 'LOW': ********0000, 'BMY': 1********000,\n      'CMCSA': ********0000, 'SBUX': 1**********0, 'MMM': 60000000000,\n\n      // Smaller cap but popular swing trading stocks\n      'PLTR': 60000000000, 'SHOP': 80000000000, 'GILD': 80000000000,\n      'TGT': 70000000000, 'COP': ********0000, 'EOG': 70000000000,\n      'SLB': 60000000000, 'PYPL': 70000000000, 'SQ': ********000,\n      'COIN': 50000000000, 'DASH': 50000000000, 'MRNA': 30000000000,\n      'SNOW': 50000000000, 'ROKU': 5000000000, 'HOOD': ********000,\n      'LYFT': 6000000000, 'SPG': 50000000000, 'PLD': 1********000,\n      'NEE': ********0000\n    }\n\n    // Return estimated market cap if available, otherwise estimate based on price\n    if (stockEstimates[symbol]) {\n      return stockEstimates[symbol]\n    }\n\n    // Rough estimation based on price (very approximate)\n    if (price > 500) return **********00 // Assume large cap if high price\n    if (price > 100) return 50000000000  // Assume mid-large cap\n    if (price > 50) return ********000   // Assume mid cap\n    if (price > 10) return 5000000000    // Assume small-mid cap\n    return ********** // Default to $1B minimum for scanning\n  }\n\n  // Get historical candlestick data (optimized for paid plans)\n  async getHistoricalData(\n    symbol: string,\n    timespan: 'minute' | 'hour' | 'day' | 'week' | 'month' = 'day',\n    multiplier: number = 1,\n    from: string,\n    to: string\n  ): Promise<CandlestickData[]> {\n    try {\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/range/${multiplier}/${timespan}/${from}/${to}`,\n        {\n          params: {\n            adjusted: 'true',\n            sort: 'asc',\n            limit: 50000, // Higher limit for paid plans\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      if (!response.data.results || response.data.results.length === 0) {\n        console.warn(`No historical data found for ${symbol}`)\n        return []\n      }\n\n      return response.data.results.map((candle: any) => ({\n        timestamp: candle.t,\n        open: candle.o,\n        high: candle.h,\n        low: candle.l,\n        close: candle.c,\n        volume: candle.v\n      }))\n    } catch (error) {\n      console.error(`Error fetching historical data for ${symbol}:`, error)\n\n      // Log the specific error for debugging\n      if (error.response) {\n        console.error(`Polygon API Error: ${error.response.status} - ${error.response.statusText}`)\n        console.error('Response data:', error.response.data)\n      }\n\n      throw new Error(`Failed to fetch historical data for ${symbol}: ${error.message}`)\n    }\n  }\n\n  // Get company details\n  async getCompanyDetails(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v3/reference/tickers/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data.results\n    } catch (error) {\n      console.error('Error fetching company details:', error)\n      return null\n    }\n  }\n\n  // Get market status\n  async getMarketStatus() {\n    try {\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v1/marketstatus/now`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error fetching market status:', error)\n      return null\n    }\n  }\n\n  // Search for stocks\n  async searchStocks(query: string, limit: number = 10) {\n    try {\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v3/reference/tickers`,\n        {\n          params: {\n            search: query,\n            market: 'stocks',\n            active: 'true',\n            limit,\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data.results || []\n    } catch (error) {\n      console.error('Error searching stocks:', error)\n      return []\n    }\n  }\n}\n\n// Create a singleton instance\nexport const polygonAPI = new PolygonAPI()\n"], "names": [], "mappings": ";;;;;;AAAA;;AAGA,MAAM,mBAAmB;AACzB,MAAM,UAAU,QAAQ,GAAG,CAAC,eAAe;AAEpC,MAAM;IACH,OAAc;IAEtB,YAAY,MAAe,CAAE;QAC3B,IAAI,CAAC,MAAM,GAAG,UAAU,WAAW;QACnC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,oFAAoF;IACpF,MAAM,cAAc,MAAc,EAAsB;QACtD,IAAI;YACF,qEAAqE;YACrE,MAAM,WAAW,MAAM,2KAAK,CAAC,GAAG,CAC9B,GAAG,iBAAiB,8CAA8C,EAAE,QAAQ,EAC5E;gBACE,QAAQ;oBACN,QAAQ,IAAI,CAAC,MAAM;gBACrB;YACF;YAGF,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,MAAM,EAAE;gBAC3C,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,QAAQ;YAC/C;YAEA,MAAM,OAAO,SAAS,IAAI,CAAC,MAAM;YAEjC,wDAAwD;YACxD,MAAM,UAAU,KAAK,GAAG,IAAI,CAAC;YAC7B,MAAM,cAAc,KAAK,OAAO,IAAI,CAAC;YACrC,MAAM,UAAU,KAAK,GAAG,IAAI,CAAC;YAE7B,sCAAsC;YACtC,MAAM,eAAe,QAAQ,CAAC,IAAI,QAAQ,CAAC,IAAI,YAAY,CAAC;YAC5D,MAAM,YAAY,YAAY,CAAC;YAC/B,MAAM,SAAS,KAAK,YAAY,IAAK,eAAe;YACpD,MAAM,gBAAgB,KAAK,gBAAgB,IAAK,AAAC,SAAS,YAAa;YAEvE,OAAO;gBACL,QAAQ,OAAO,WAAW;gBAC1B,MAAM,KAAK,IAAI,IAAI,OAAO,WAAW;gBACrC,OAAO,gBAAgB;gBACvB,QAAQ,UAAU;gBAClB,eAAe,iBAAiB;gBAChC,QAAQ,QAAQ,CAAC,IAAI,QAAQ,CAAC,IAAI;gBAClC,WAAW,KAAK,UAAU,IAAI,IAAI,CAAC,iBAAiB,CAAC,QAAQ,gBAAgB;gBAC7E,IAAI;gBACJ,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAE1D,kDAAkD;YAClD,IAAI;gBACF,MAAM,mBAAmB,MAAM,2KAAK,CAAC,GAAG,CACtC,GAAG,iBAAiB,gBAAgB,EAAE,OAAO,KAAK,CAAC,EACnD;oBACE,QAAQ;wBACN,UAAU;wBACV,QAAQ,IAAI,CAAC,MAAM;oBACrB;gBACF;gBAGF,MAAM,OAAO,iBAAiB,IAAI,CAAC,OAAO,CAAC,EAAE;gBAC7C,OAAO;oBACL,QAAQ,OAAO,WAAW;oBAC1B,MAAM,OAAO,WAAW;oBACxB,OAAO,KAAK,CAAC,IAAI;oBACjB,QAAQ,AAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAK;oBAC7B,eAAe,KAAK,CAAC,GAAG,AAAC,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,KAAK,CAAC,GAAI,MAAM;oBAC7D,QAAQ,KAAK,CAAC,IAAI;oBAClB,WAAW,IAAI,CAAC,iBAAiB,CAAC,QAAQ,KAAK,CAAC,IAAI;oBACpD,IAAI;oBACJ,UAAU;gBACZ;YACF,EAAE,OAAO,eAAe;gBACtB,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,QAAQ;YACvD;QACF;IACF;IAEA;;;GAGC,GACD,AAAQ,kBAAkB,MAAc,EAAE,KAAa,EAAU;QAC/D,kDAAkD;QAClD,MAAM,iBAA4C;YAChD,qBAAqB;YACrB,QAAQ;YAAe,QAAQ;YAAe,QAAQ;YACtD,SAAS;YAAe,QAAQ;YAAe,QAAQ;YACvD,QAAQ;YAAc,QAAQ;YAAc,SAAS;YAErD,6BAA6B;YAC7B,OAAO;YAAc,KAAK;YAAc,OAAO;YAC/C,OAAO;YAAc,OAAO;YAAc,OAAO;YACjD,MAAM;YAAc,MAAM;YAAc,MAAM;YAC9C,OAAO;YAAc,QAAQ;YAAc,OAAO;YAClD,QAAQ;YAAc,QAAQ;YAAc,OAAO;YAEnD,sBAAsB;YACtB,QAAQ;YAAc,QAAQ;YAAc,OAAO;YACnD,QAAQ;YAAc,OAAO;YAAc,QAAQ;YACnD,QAAQ;YAAc,OAAO;YAAc,OAAO;YAClD,OAAO;YAAc,MAAM;YAAc,OAAO;YAChD,MAAM;YAAc,OAAO;YAAc,OAAO;YAChD,MAAM;YAAc,QAAQ;YAAc,KAAK;YAC/C,MAAM;YAAc,KAAK;YAAc,QAAQ;YAC/C,OAAO;YAAc,OAAO;YAAc,OAAO;YACjD,SAAS;YAAc,QAAQ;YAAc,OAAO;YAEpD,+CAA+C;YAC/C,QAAQ;YAAa,QAAQ;YAAa,QAAQ;YAClD,OAAO;YAAa,OAAO;YAAc,OAAO;YAChD,OAAO;YAAa,QAAQ;YAAa,MAAM;YAC/C,QAAQ;YAAa,QAAQ;YAAa,QAAQ;YAClD,QAAQ;YAAa,QAAQ;YAAY,QAAQ;YACjD,QAAQ;YAAY,OAAO;YAAa,OAAO;YAC/C,OAAO;QACT;QAEA,8EAA8E;QAC9E,IAAI,cAAc,CAAC,OAAO,EAAE;YAC1B,OAAO,cAAc,CAAC,OAAO;QAC/B;QAEA,qDAAqD;QACrD,IAAI,QAAQ,KAAK,OAAO,aAAa,iCAAiC;;QACtE,IAAI,QAAQ,KAAK,OAAO,YAAa,uBAAuB;;QAC5D,IAAI,QAAQ,IAAI,OAAO,YAAc,iBAAiB;;QACtD,IAAI,QAAQ,IAAI,OAAO,WAAc,uBAAuB;;QAC5D,OAAO,WAAW,sCAAsC;;IAC1D;IAEA,6DAA6D;IAC7D,MAAM,kBACJ,MAAc,EACd,WAAyD,KAAK,EAC9D,aAAqB,CAAC,EACtB,IAAY,EACZ,EAAU,EACkB;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,2KAAK,CAAC,GAAG,CAC9B,GAAG,iBAAiB,gBAAgB,EAAE,OAAO,OAAO,EAAE,WAAW,CAAC,EAAE,SAAS,CAAC,EAAE,KAAK,CAAC,EAAE,IAAI,EAC5F;gBACE,QAAQ;oBACN,UAAU;oBACV,MAAM;oBACN,OAAO;oBACP,QAAQ,IAAI,CAAC,MAAM;gBACrB;YACF;YAGF,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,GAAG;gBAChE,QAAQ,IAAI,CAAC,CAAC,6BAA6B,EAAE,QAAQ;gBACrD,OAAO,EAAE;YACX;YAEA,OAAO,SAAS,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,SAAgB,CAAC;oBACjD,WAAW,OAAO,CAAC;oBACnB,MAAM,OAAO,CAAC;oBACd,MAAM,OAAO,CAAC;oBACd,KAAK,OAAO,CAAC;oBACb,OAAO,OAAO,CAAC;oBACf,QAAQ,OAAO,CAAC;gBAClB,CAAC;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,OAAO,CAAC,CAAC,EAAE;YAE/D,uCAAuC;YACvC,IAAI,MAAM,QAAQ,EAAE;gBAClB,QAAQ,KAAK,CAAC,CAAC,mBAAmB,EAAE,MAAM,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,QAAQ,CAAC,UAAU,EAAE;gBAC1F,QAAQ,KAAK,CAAC,kBAAkB,MAAM,QAAQ,CAAC,IAAI;YACrD;YAEA,MAAM,IAAI,MAAM,CAAC,oCAAoC,EAAE,OAAO,EAAE,EAAE,MAAM,OAAO,EAAE;QACnF;IACF;IAEA,sBAAsB;IACtB,MAAM,kBAAkB,MAAc,EAAE;QACtC,IAAI;YACF,MAAM,WAAW,MAAM,2KAAK,CAAC,GAAG,CAC9B,GAAG,iBAAiB,sBAAsB,EAAE,QAAQ,EACpD;gBACE,QAAQ;oBACN,QAAQ,IAAI,CAAC,MAAM;gBACrB;YACF;YAGF,OAAO,SAAS,IAAI,CAAC,OAAO;QAC9B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO;QACT;IACF;IAEA,oBAAoB;IACpB,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,2KAAK,CAAC,GAAG,CAC9B,GAAG,iBAAiB,oBAAoB,CAAC,EACzC;gBACE,QAAQ;oBACN,QAAQ,IAAI,CAAC,MAAM;gBACrB;YACF;YAGF,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;QACT;IACF;IAEA,oBAAoB;IACpB,MAAM,aAAa,KAAa,EAAE,QAAgB,EAAE,EAAE;QACpD,IAAI;YACF,MAAM,WAAW,MAAM,2KAAK,CAAC,GAAG,CAC9B,GAAG,iBAAiB,qBAAqB,CAAC,EAC1C;gBACE,QAAQ;oBACN,QAAQ;oBACR,QAAQ;oBACR,QAAQ;oBACR;oBACA,QAAQ,IAAI,CAAC,MAAM;gBACrB;YACF;YAGF,OAAO,SAAS,IAAI,CAAC,OAAO,IAAI,EAAE;QACpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,EAAE;QACX;IACF;AACF;AAGO,MAAM,aAAa,IAAI", "debugId": null}}, {"offset": {"line": 1038, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/data/stockUniverse.ts"], "sourcesContent": ["/**\n * Comprehensive stock universe for swing trading\n * Focus on liquid, volatile large-cap stocks with good technical patterns\n */\n\nexport interface StockInfo {\n  symbol: string\n  name: string\n  sector: string\n  marketCap: number // in billions\n  avgVolume: number // average daily volume\n  volatility: 'High' | 'Medium' | 'Low'\n  swingTradingRating: number // 1-10 scale\n}\n\nexport const SWING_TRADING_UNIVERSE: StockInfo[] = [\n  // Technology - High Growth & Volatility\n  { symbol: 'AAPL', name: 'Apple Inc.', sector: 'Technology', marketCap: 3000, avgVolume: 50000000, volatility: 'Medium', swingTradingRating: 9 },\n  { symbol: 'MSFT', name: 'Microsoft Corporation', sector: 'Technology', marketCap: 2800, avgVolume: 25000000, volatility: 'Medium', swingTradingRating: 8 },\n  { symbol: 'NVDA', name: 'NVIDIA Corporation', sector: 'Technology', marketCap: 1800, avgVolume: 45000000, volatility: 'High', swingTradingRating: 10 },\n  { symbol: 'GOOGL', name: 'Alphabet Inc. Class A', sector: 'Technology', marketCap: 1700, avgVolume: 25000000, volatility: 'Medium', swingTradingRating: 8 },\n  { symbol: 'GOOG', name: 'Alphabet Inc. Class C', sector: 'Technology', marketCap: 1700, avgVolume: ********, volatility: 'Medium', swingTradingRating: 8 },\n  { symbol: 'META', name: 'Meta Platforms Inc.', sector: 'Technology', marketCap: 800, avgVolume: ********, volatility: 'High', swingTradingRating: 9 },\n  { symbol: 'TSLA', name: 'Tesla Inc.', sector: 'Technology', marketCap: 800, avgVolume: 75000000, volatility: 'High', swingTradingRating: 10 },\n  { symbol: 'AMZN', name: 'Amazon.com Inc.', sector: 'Technology', marketCap: 1500, avgVolume: ********, volatility: 'Medium', swingTradingRating: 8 },\n  { symbol: 'NFLX', name: 'Netflix Inc.', sector: 'Technology', marketCap: 200, avgVolume: 8000000, volatility: 'High', swingTradingRating: 9 },\n  { symbol: 'ORCL', name: 'Oracle Corporation', sector: 'Technology', marketCap: 350, avgVolume: ********, volatility: 'Medium', swingTradingRating: 7 },\n  { symbol: 'CRM', name: 'Salesforce Inc.', sector: 'Technology', marketCap: 250, avgVolume: 6000000, volatility: 'High', swingTradingRating: 8 },\n  { symbol: 'ADBE', name: 'Adobe Inc.', sector: 'Technology', marketCap: 220, avgVolume: 3000000, volatility: 'Medium', swingTradingRating: 7 },\n  { symbol: 'AVGO', name: 'Broadcom Inc.', sector: 'Technology', marketCap: 600, avgVolume: 2000000, volatility: 'Medium', swingTradingRating: 8 },\n  { symbol: 'TSM', name: 'Taiwan Semiconductor', sector: 'Technology', marketCap: 500, avgVolume: ********, volatility: 'Medium', swingTradingRating: 8 },\n  { symbol: 'ASML', name: 'ASML Holding N.V.', sector: 'Technology', marketCap: 300, avgVolume: 1500000, volatility: 'High', swingTradingRating: 8 },\n  { symbol: 'AMD', name: 'Advanced Micro Devices', sector: 'Technology', marketCap: 220, avgVolume: 45000000, volatility: 'High', swingTradingRating: 9 },\n  { symbol: 'INTC', name: 'Intel Corporation', sector: 'Technology', marketCap: 200, avgVolume: 25000000, volatility: 'Medium', swingTradingRating: 7 },\n  { symbol: 'QCOM', name: 'QUALCOMM Incorporated', sector: 'Technology', marketCap: 180, avgVolume: 8000000, volatility: 'Medium', swingTradingRating: 8 },\n  { symbol: 'PLTR', name: 'Palantir Technologies', sector: 'Technology', marketCap: 60, avgVolume: ********, volatility: 'High', swingTradingRating: 9 },\n  { symbol: 'SNOW', name: 'Snowflake Inc.', sector: 'Technology', marketCap: 50, avgVolume: 4000000, volatility: 'High', swingTradingRating: 8 },\n\n  // Financial Services - Interest Rate Sensitive\n  { symbol: 'JPM', name: 'JPMorgan Chase & Co.', sector: 'Financial', marketCap: 500, avgVolume: ********, volatility: 'Medium', swingTradingRating: 8 },\n  { symbol: 'BAC', name: 'Bank of America Corp.', sector: 'Financial', marketCap: 300, avgVolume: ********, volatility: 'Medium', swingTradingRating: 8 },\n  { symbol: 'WFC', name: 'Wells Fargo & Company', sector: 'Financial', marketCap: 180, avgVolume: ********, volatility: 'Medium', swingTradingRating: 7 },\n  { symbol: 'GS', name: 'Goldman Sachs Group', sector: 'Financial', marketCap: 120, avgVolume: 2500000, volatility: 'High', swingTradingRating: 8 },\n  { symbol: 'MS', name: 'Morgan Stanley', sector: 'Financial', marketCap: 150, avgVolume: 8000000, volatility: 'High', swingTradingRating: 8 },\n  { symbol: 'C', name: 'Citigroup Inc.', sector: 'Financial', marketCap: 120, avgVolume: ********, volatility: 'High', swingTradingRating: 8 },\n  { symbol: 'BRK.B', name: 'Berkshire Hathaway B', sector: 'Financial', marketCap: 900, avgVolume: 4000000, volatility: 'Low', swingTradingRating: 6 },\n  { symbol: 'V', name: 'Visa Inc.', sector: 'Financial', marketCap: 500, avgVolume: 6000000, volatility: 'Medium', swingTradingRating: 7 },\n  { symbol: 'MA', name: 'Mastercard Inc.', sector: 'Financial', marketCap: 400, avgVolume: 3000000, volatility: 'Medium', swingTradingRating: 7 },\n  { symbol: 'PYPL', name: 'PayPal Holdings Inc.', sector: 'Financial', marketCap: 70, avgVolume: ********, volatility: 'High', swingTradingRating: 8 },\n\n  // Healthcare & Biotech - Defensive with Growth\n  { symbol: 'JNJ', name: 'Johnson & Johnson', sector: 'Healthcare', marketCap: 450, avgVolume: 7000000, volatility: 'Low', swingTradingRating: 6 },\n  { symbol: 'UNH', name: 'UnitedHealth Group', sector: 'Healthcare', marketCap: 500, avgVolume: 3000000, volatility: 'Medium', swingTradingRating: 7 },\n  { symbol: 'PFE', name: 'Pfizer Inc.', sector: 'Healthcare', marketCap: 160, avgVolume: 25000000, volatility: 'Medium', swingTradingRating: 7 },\n  { symbol: 'ABBV', name: 'AbbVie Inc.', sector: 'Healthcare', marketCap: 300, avgVolume: 6000000, volatility: 'Medium', swingTradingRating: 7 },\n  { symbol: 'TMO', name: 'Thermo Fisher Scientific', sector: 'Healthcare', marketCap: 200, avgVolume: 1500000, volatility: 'Medium', swingTradingRating: 7 },\n  { symbol: 'DHR', name: 'Danaher Corporation', sector: 'Healthcare', marketCap: 180, avgVolume: 2000000, volatility: 'Medium', swingTradingRating: 7 },\n  { symbol: 'BMY', name: 'Bristol Myers Squibb', sector: 'Healthcare', marketCap: 120, avgVolume: 10000000, volatility: 'Medium', swingTradingRating: 7 },\n  { symbol: 'AMGN', name: 'Amgen Inc.', sector: 'Healthcare', marketCap: 150, avgVolume: 2500000, volatility: 'Medium', swingTradingRating: 7 },\n  { symbol: 'GILD', name: 'Gilead Sciences Inc.', sector: 'Healthcare', marketCap: 80, avgVolume: 6000000, volatility: 'Medium', swingTradingRating: 7 },\n  { symbol: 'MRNA', name: 'Moderna Inc.', sector: 'Healthcare', marketCap: 30, avgVolume: 8000000, volatility: 'High', swingTradingRating: 9 },\n\n  // Consumer & Retail - Economic Sensitive\n  { symbol: 'WMT', name: 'Walmart Inc.', sector: 'Consumer', marketCap: 600, avgVolume: 8000000, volatility: 'Low', swingTradingRating: 6 },\n  { symbol: 'HD', name: 'Home Depot Inc.', sector: 'Consumer', marketCap: 350, avgVolume: 3000000, volatility: 'Medium', swingTradingRating: 7 },\n  { symbol: 'MCD', name: 'McDonald\\'s Corporation', sector: 'Consumer', marketCap: 200, avgVolume: 2500000, volatility: 'Low', swingTradingRating: 6 },\n  { symbol: 'NKE', name: 'Nike Inc.', sector: 'Consumer', marketCap: 150, avgVolume: 6000000, volatility: 'Medium', swingTradingRating: 7 },\n  { symbol: 'SBUX', name: 'Starbucks Corporation', sector: 'Consumer', marketCap: 110, avgVolume: 6000000, volatility: 'Medium', swingTradingRating: 7 },\n  { symbol: 'TGT', name: 'Target Corporation', sector: 'Consumer', marketCap: 70, avgVolume: 4000000, volatility: 'High', swingTradingRating: 8 },\n  { symbol: 'COST', name: 'Costco Wholesale Corp.', sector: 'Consumer', marketCap: 350, avgVolume: 2000000, volatility: 'Medium', swingTradingRating: 7 },\n  { symbol: 'LOW', name: 'Lowe\\'s Companies Inc.', sector: 'Consumer', marketCap: 150, avgVolume: 3000000, volatility: 'Medium', swingTradingRating: 7 },\n\n  // Energy - Commodity Driven\n  { symbol: 'XOM', name: 'Exxon Mobil Corporation', sector: 'Energy', marketCap: 450, avgVolume: ********, volatility: 'High', swingTradingRating: 8 },\n  { symbol: 'CVX', name: 'Chevron Corporation', sector: 'Energy', marketCap: 300, avgVolume: ********, volatility: 'Medium', swingTradingRating: 7 },\n  { symbol: 'COP', name: 'ConocoPhillips', sector: 'Energy', marketCap: 150, avgVolume: 8000000, volatility: 'High', swingTradingRating: 8 },\n  { symbol: 'SLB', name: 'Schlumberger Limited', sector: 'Energy', marketCap: 60, avgVolume: ********, volatility: 'High', swingTradingRating: 8 },\n  { symbol: 'EOG', name: 'EOG Resources Inc.', sector: 'Energy', marketCap: 70, avgVolume: 4000000, volatility: 'High', swingTradingRating: 8 },\n\n  // Industrial & Materials\n  { symbol: 'CAT', name: 'Caterpillar Inc.', sector: 'Industrial', marketCap: 180, avgVolume: 3000000, volatility: 'High', swingTradingRating: 8 },\n  { symbol: 'BA', name: 'Boeing Company', sector: 'Industrial', marketCap: 120, avgVolume: 8000000, volatility: 'High', swingTradingRating: 9 },\n  { symbol: 'GE', name: 'General Electric Co.', sector: 'Industrial', marketCap: 180, avgVolume: 45000000, volatility: 'High', swingTradingRating: 9 },\n  { symbol: 'MMM', name: '3M Company', sector: 'Industrial', marketCap: 60, avgVolume: 3000000, volatility: 'Medium', swingTradingRating: 7 },\n  { symbol: 'HON', name: 'Honeywell International', sector: 'Industrial', marketCap: 140, avgVolume: 2500000, volatility: 'Medium', swingTradingRating: 7 },\n\n  // Communication & Media\n  { symbol: 'DIS', name: 'Walt Disney Company', sector: 'Media', marketCap: 180, avgVolume: ********, volatility: 'High', swingTradingRating: 8 },\n  { symbol: 'CMCSA', name: 'Comcast Corporation', sector: 'Media', marketCap: 150, avgVolume: ********, volatility: 'Medium', swingTradingRating: 7 },\n  { symbol: 'VZ', name: 'Verizon Communications', sector: 'Telecom', marketCap: 170, avgVolume: ********, volatility: 'Low', swingTradingRating: 6 },\n  { symbol: 'T', name: 'AT&T Inc.', sector: 'Telecom', marketCap: 120, avgVolume: ********, volatility: 'Medium', swingTradingRating: 7 },\n\n  // High-Volatility Growth Stocks\n  { symbol: 'ROKU', name: 'Roku Inc.', sector: 'Technology', marketCap: 5, avgVolume: 8000000, volatility: 'High', swingTradingRating: 9 },\n  { symbol: 'SHOP', name: 'Shopify Inc.', sector: 'Technology', marketCap: 80, avgVolume: 3000000, volatility: 'High', swingTradingRating: 9 },\n  { symbol: 'BLOCK', name: 'Block Inc.', sector: 'Financial', marketCap: 40, avgVolume: 8000000, volatility: 'High', swingTradingRating: 9 },\n  { symbol: 'UBER', name: 'Uber Technologies', sector: 'Technology', marketCap: 150, avgVolume: ********, volatility: 'High', swingTradingRating: 9 },\n  { symbol: 'LYFT', name: 'Lyft Inc.', sector: 'Technology', marketCap: 6, avgVolume: 4000000, volatility: 'High', swingTradingRating: 8 },\n  { symbol: 'DASH', name: 'DoorDash Inc.', sector: 'Technology', marketCap: 50, avgVolume: 3000000, volatility: 'High', swingTradingRating: 8 },\n  { symbol: 'COIN', name: 'Coinbase Global Inc.', sector: 'Financial', marketCap: 50, avgVolume: 8000000, volatility: 'High', swingTradingRating: 9 },\n  { symbol: 'HOOD', name: 'Robinhood Markets Inc.', sector: 'Financial', marketCap: 15, avgVolume: 10000000, volatility: 'High', swingTradingRating: 9 },\n\n  // REITs & Utilities (Lower volatility but good for certain strategies)\n  { symbol: 'SPG', name: 'Simon Property Group', sector: 'REIT', marketCap: 50, avgVolume: 2000000, volatility: 'Medium', swingTradingRating: 6 },\n  { symbol: 'PLD', name: 'Prologis Inc.', sector: 'REIT', marketCap: 120, avgVolume: 2500000, volatility: 'Medium', swingTradingRating: 6 },\n  { symbol: 'NEE', name: 'NextEra Energy Inc.', sector: 'Utilities', marketCap: 150, avgVolume: 8000000, volatility: 'Low', swingTradingRating: 6 },\n]\n\n// Helper functions\nexport const getStocksByVolatility = (volatility: 'High' | 'Medium' | 'Low'): StockInfo[] => {\n  return SWING_TRADING_UNIVERSE.filter(stock => stock.volatility === volatility)\n}\n\nexport const getStocksBySector = (sector: string): StockInfo[] => {\n  return SWING_TRADING_UNIVERSE.filter(stock => stock.sector === sector)\n}\n\nexport const getTopSwingTradingStocks = (limit: number = 50): StockInfo[] => {\n  return SWING_TRADING_UNIVERSE\n    .sort((a, b) => b.swingTradingRating - a.swingTradingRating)\n    .slice(0, limit)\n}\n\nexport const getHighVolumeStocks = (minVolume: number = 10000000): StockInfo[] => {\n  return SWING_TRADING_UNIVERSE.filter(stock => stock.avgVolume >= minVolume)\n}\n\n// Default stock symbols for quick access\nexport const DEFAULT_SWING_SYMBOLS = SWING_TRADING_UNIVERSE.map(stock => stock.symbol)\n\n// High-priority stocks for scanning (top swing trading candidates)\nexport const PRIORITY_SWING_SYMBOLS = getTopSwingTradingStocks(75).map(stock => stock.symbol)\n\n// Sector-based symbol lists\nexport const TECH_SYMBOLS = getStocksBySector('Technology').map(stock => stock.symbol)\nexport const FINANCIAL_SYMBOLS = getStocksBySector('Financial').map(stock => stock.symbol)\nexport const HEALTHCARE_SYMBOLS = getStocksBySector('Healthcare').map(stock => stock.symbol)\nexport const ENERGY_SYMBOLS = getStocksBySector('Energy').map(stock => stock.symbol)\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;;;;;;;;AAYM,MAAM,yBAAsC;IACjD,wCAAwC;IACxC;QAAE,QAAQ;QAAQ,MAAM;QAAc,QAAQ;QAAc,WAAW;QAAM,WAAW;QAAU,YAAY;QAAU,oBAAoB;IAAE;IAC9I;QAAE,QAAQ;QAAQ,MAAM;QAAyB,QAAQ;QAAc,WAAW;QAAM,WAAW;QAAU,YAAY;QAAU,oBAAoB;IAAE;IACzJ;QAAE,QAAQ;QAAQ,MAAM;QAAsB,QAAQ;QAAc,WAAW;QAAM,WAAW;QAAU,YAAY;QAAQ,oBAAoB;IAAG;IACrJ;QAAE,QAAQ;QAAS,MAAM;QAAyB,QAAQ;QAAc,WAAW;QAAM,WAAW;QAAU,YAAY;QAAU,oBAAoB;IAAE;IAC1J;QAAE,QAAQ;QAAQ,MAAM;QAAyB,QAAQ;QAAc,WAAW;QAAM,WAAW;QAAU,YAAY;QAAU,oBAAoB;IAAE;IACzJ;QAAE,QAAQ;QAAQ,MAAM;QAAuB,QAAQ;QAAc,WAAW;QAAK,WAAW;QAAU,YAAY;QAAQ,oBAAoB;IAAE;IACpJ;QAAE,QAAQ;QAAQ,MAAM;QAAc,QAAQ;QAAc,WAAW;QAAK,WAAW;QAAU,YAAY;QAAQ,oBAAoB;IAAG;IAC5I;QAAE,QAAQ;QAAQ,MAAM;QAAmB,QAAQ;QAAc,WAAW;QAAM,WAAW;QAAU,YAAY;QAAU,oBAAoB;IAAE;IACnJ;QAAE,QAAQ;QAAQ,MAAM;QAAgB,QAAQ;QAAc,WAAW;QAAK,WAAW;QAAS,YAAY;QAAQ,oBAAoB;IAAE;IAC5I;QAAE,QAAQ;QAAQ,MAAM;QAAsB,QAAQ;QAAc,WAAW;QAAK,WAAW;QAAU,YAAY;QAAU,oBAAoB;IAAE;IACrJ;QAAE,QAAQ;QAAO,MAAM;QAAmB,QAAQ;QAAc,WAAW;QAAK,WAAW;QAAS,YAAY;QAAQ,oBAAoB;IAAE;IAC9I;QAAE,QAAQ;QAAQ,MAAM;QAAc,QAAQ;QAAc,WAAW;QAAK,WAAW;QAAS,YAAY;QAAU,oBAAoB;IAAE;IAC5I;QAAE,QAAQ;QAAQ,MAAM;QAAiB,QAAQ;QAAc,WAAW;QAAK,WAAW;QAAS,YAAY;QAAU,oBAAoB;IAAE;IAC/I;QAAE,QAAQ;QAAO,MAAM;QAAwB,QAAQ;QAAc,WAAW;QAAK,WAAW;QAAU,YAAY;QAAU,oBAAoB;IAAE;IACtJ;QAAE,QAAQ;QAAQ,MAAM;QAAqB,QAAQ;QAAc,WAAW;QAAK,WAAW;QAAS,YAAY;QAAQ,oBAAoB;IAAE;IACjJ;QAAE,QAAQ;QAAO,MAAM;QAA0B,QAAQ;QAAc,WAAW;QAAK,WAAW;QAAU,YAAY;QAAQ,oBAAoB;IAAE;IACtJ;QAAE,QAAQ;QAAQ,MAAM;QAAqB,QAAQ;QAAc,WAAW;QAAK,WAAW;QAAU,YAAY;QAAU,oBAAoB;IAAE;IACpJ;QAAE,QAAQ;QAAQ,MAAM;QAAyB,QAAQ;QAAc,WAAW;QAAK,WAAW;QAAS,YAAY;QAAU,oBAAoB;IAAE;IACvJ;QAAE,QAAQ;QAAQ,MAAM;QAAyB,QAAQ;QAAc,WAAW;QAAI,WAAW;QAAU,YAAY;QAAQ,oBAAoB;IAAE;IACrJ;QAAE,QAAQ;QAAQ,MAAM;QAAkB,QAAQ;QAAc,WAAW;QAAI,WAAW;QAAS,YAAY;QAAQ,oBAAoB;IAAE;IAE7I,+CAA+C;IAC/C;QAAE,QAAQ;QAAO,MAAM;QAAwB,QAAQ;QAAa,WAAW;QAAK,WAAW;QAAU,YAAY;QAAU,oBAAoB;IAAE;IACrJ;QAAE,QAAQ;QAAO,MAAM;QAAyB,QAAQ;QAAa,WAAW;QAAK,WAAW;QAAU,YAAY;QAAU,oBAAoB;IAAE;IACtJ;QAAE,QAAQ;QAAO,MAAM;QAAyB,QAAQ;QAAa,WAAW;QAAK,WAAW;QAAU,YAAY;QAAU,oBAAoB;IAAE;IACtJ;QAAE,QAAQ;QAAM,MAAM;QAAuB,QAAQ;QAAa,WAAW;QAAK,WAAW;QAAS,YAAY;QAAQ,oBAAoB;IAAE;IAChJ;QAAE,QAAQ;QAAM,MAAM;QAAkB,QAAQ;QAAa,WAAW;QAAK,WAAW;QAAS,YAAY;QAAQ,oBAAoB;IAAE;IAC3I;QAAE,QAAQ;QAAK,MAAM;QAAkB,QAAQ;QAAa,WAAW;QAAK,WAAW;QAAU,YAAY;QAAQ,oBAAoB;IAAE;IAC3I;QAAE,QAAQ;QAAS,MAAM;QAAwB,QAAQ;QAAa,WAAW;QAAK,WAAW;QAAS,YAAY;QAAO,oBAAoB;IAAE;IACnJ;QAAE,QAAQ;QAAK,MAAM;QAAa,QAAQ;QAAa,WAAW;QAAK,WAAW;QAAS,YAAY;QAAU,oBAAoB;IAAE;IACvI;QAAE,QAAQ;QAAM,MAAM;QAAmB,QAAQ;QAAa,WAAW;QAAK,WAAW;QAAS,YAAY;QAAU,oBAAoB;IAAE;IAC9I;QAAE,QAAQ;QAAQ,MAAM;QAAwB,QAAQ;QAAa,WAAW;QAAI,WAAW;QAAU,YAAY;QAAQ,oBAAoB;IAAE;IAEnJ,+CAA+C;IAC/C;QAAE,QAAQ;QAAO,MAAM;QAAqB,QAAQ;QAAc,WAAW;QAAK,WAAW;QAAS,YAAY;QAAO,oBAAoB;IAAE;IAC/I;QAAE,QAAQ;QAAO,MAAM;QAAsB,QAAQ;QAAc,WAAW;QAAK,WAAW;QAAS,YAAY;QAAU,oBAAoB;IAAE;IACnJ;QAAE,QAAQ;QAAO,MAAM;QAAe,QAAQ;QAAc,WAAW;QAAK,WAAW;QAAU,YAAY;QAAU,oBAAoB;IAAE;IAC7I;QAAE,QAAQ;QAAQ,MAAM;QAAe,QAAQ;QAAc,WAAW;QAAK,WAAW;QAAS,YAAY;QAAU,oBAAoB;IAAE;IAC7I;QAAE,QAAQ;QAAO,MAAM;QAA4B,QAAQ;QAAc,WAAW;QAAK,WAAW;QAAS,YAAY;QAAU,oBAAoB;IAAE;IACzJ;QAAE,QAAQ;QAAO,MAAM;QAAuB,QAAQ;QAAc,WAAW;QAAK,WAAW;QAAS,YAAY;QAAU,oBAAoB;IAAE;IACpJ;QAAE,QAAQ;QAAO,MAAM;QAAwB,QAAQ;QAAc,WAAW;QAAK,WAAW;QAAU,YAAY;QAAU,oBAAoB;IAAE;IACtJ;QAAE,QAAQ;QAAQ,MAAM;QAAc,QAAQ;QAAc,WAAW;QAAK,WAAW;QAAS,YAAY;QAAU,oBAAoB;IAAE;IAC5I;QAAE,QAAQ;QAAQ,MAAM;QAAwB,QAAQ;QAAc,WAAW;QAAI,WAAW;QAAS,YAAY;QAAU,oBAAoB;IAAE;IACrJ;QAAE,QAAQ;QAAQ,MAAM;QAAgB,QAAQ;QAAc,WAAW;QAAI,WAAW;QAAS,YAAY;QAAQ,oBAAoB;IAAE;IAE3I,yCAAyC;IACzC;QAAE,QAAQ;QAAO,MAAM;QAAgB,QAAQ;QAAY,WAAW;QAAK,WAAW;QAAS,YAAY;QAAO,oBAAoB;IAAE;IACxI;QAAE,QAAQ;QAAM,MAAM;QAAmB,QAAQ;QAAY,WAAW;QAAK,WAAW;QAAS,YAAY;QAAU,oBAAoB;IAAE;IAC7I;QAAE,QAAQ;QAAO,MAAM;QAA2B,QAAQ;QAAY,WAAW;QAAK,WAAW;QAAS,YAAY;QAAO,oBAAoB;IAAE;IACnJ;QAAE,QAAQ;QAAO,MAAM;QAAa,QAAQ;QAAY,WAAW;QAAK,WAAW;QAAS,YAAY;QAAU,oBAAoB;IAAE;IACxI;QAAE,QAAQ;QAAQ,MAAM;QAAyB,QAAQ;QAAY,WAAW;QAAK,WAAW;QAAS,YAAY;QAAU,oBAAoB;IAAE;IACrJ;QAAE,QAAQ;QAAO,MAAM;QAAsB,QAAQ;QAAY,WAAW;QAAI,WAAW;QAAS,YAAY;QAAQ,oBAAoB;IAAE;IAC9I;QAAE,QAAQ;QAAQ,MAAM;QAA0B,QAAQ;QAAY,WAAW;QAAK,WAAW;QAAS,YAAY;QAAU,oBAAoB;IAAE;IACtJ;QAAE,QAAQ;QAAO,MAAM;QAA0B,QAAQ;QAAY,WAAW;QAAK,WAAW;QAAS,YAAY;QAAU,oBAAoB;IAAE;IAErJ,4BAA4B;IAC5B;QAAE,QAAQ;QAAO,MAAM;QAA2B,QAAQ;QAAU,WAAW;QAAK,WAAW;QAAU,YAAY;QAAQ,oBAAoB;IAAE;IACnJ;QAAE,QAAQ;QAAO,MAAM;QAAuB,QAAQ;QAAU,WAAW;QAAK,WAAW;QAAU,YAAY;QAAU,oBAAoB;IAAE;IACjJ;QAAE,QAAQ;QAAO,MAAM;QAAkB,QAAQ;QAAU,WAAW;QAAK,WAAW;QAAS,YAAY;QAAQ,oBAAoB;IAAE;IACzI;QAAE,QAAQ;QAAO,MAAM;QAAwB,QAAQ;QAAU,WAAW;QAAI,WAAW;QAAU,YAAY;QAAQ,oBAAoB;IAAE;IAC/I;QAAE,QAAQ;QAAO,MAAM;QAAsB,QAAQ;QAAU,WAAW;QAAI,WAAW;QAAS,YAAY;QAAQ,oBAAoB;IAAE;IAE5I,yBAAyB;IACzB;QAAE,QAAQ;QAAO,MAAM;QAAoB,QAAQ;QAAc,WAAW;QAAK,WAAW;QAAS,YAAY;QAAQ,oBAAoB;IAAE;IAC/I;QAAE,QAAQ;QAAM,MAAM;QAAkB,QAAQ;QAAc,WAAW;QAAK,WAAW;QAAS,YAAY;QAAQ,oBAAoB;IAAE;IAC5I;QAAE,QAAQ;QAAM,MAAM;QAAwB,QAAQ;QAAc,WAAW;QAAK,WAAW;QAAU,YAAY;QAAQ,oBAAoB;IAAE;IACnJ;QAAE,QAAQ;QAAO,MAAM;QAAc,QAAQ;QAAc,WAAW;QAAI,WAAW;QAAS,YAAY;QAAU,oBAAoB;IAAE;IAC1I;QAAE,QAAQ;QAAO,MAAM;QAA2B,QAAQ;QAAc,WAAW;QAAK,WAAW;QAAS,YAAY;QAAU,oBAAoB;IAAE;IAExJ,wBAAwB;IACxB;QAAE,QAAQ;QAAO,MAAM;QAAuB,QAAQ;QAAS,WAAW;QAAK,WAAW;QAAU,YAAY;QAAQ,oBAAoB;IAAE;IAC9I;QAAE,QAAQ;QAAS,MAAM;QAAuB,QAAQ;QAAS,WAAW;QAAK,WAAW;QAAU,YAAY;QAAU,oBAAoB;IAAE;IAClJ;QAAE,QAAQ;QAAM,MAAM;QAA0B,QAAQ;QAAW,WAAW;QAAK,WAAW;QAAU,YAAY;QAAO,oBAAoB;IAAE;IACjJ;QAAE,QAAQ;QAAK,MAAM;QAAa,QAAQ;QAAW,WAAW;QAAK,WAAW;QAAU,YAAY;QAAU,oBAAoB;IAAE;IAEtI,gCAAgC;IAChC;QAAE,QAAQ;QAAQ,MAAM;QAAa,QAAQ;QAAc,WAAW;QAAG,WAAW;QAAS,YAAY;QAAQ,oBAAoB;IAAE;IACvI;QAAE,QAAQ;QAAQ,MAAM;QAAgB,QAAQ;QAAc,WAAW;QAAI,WAAW;QAAS,YAAY;QAAQ,oBAAoB;IAAE;IAC3I;QAAE,QAAQ;QAAS,MAAM;QAAc,QAAQ;QAAa,WAAW;QAAI,WAAW;QAAS,YAAY;QAAQ,oBAAoB;IAAE;IACzI;QAAE,QAAQ;QAAQ,MAAM;QAAqB,QAAQ;QAAc,WAAW;QAAK,WAAW;QAAU,YAAY;QAAQ,oBAAoB;IAAE;IAClJ;QAAE,QAAQ;QAAQ,MAAM;QAAa,QAAQ;QAAc,WAAW;QAAG,WAAW;QAAS,YAAY;QAAQ,oBAAoB;IAAE;IACvI;QAAE,QAAQ;QAAQ,MAAM;QAAiB,QAAQ;QAAc,WAAW;QAAI,WAAW;QAAS,YAAY;QAAQ,oBAAoB;IAAE;IAC5I;QAAE,QAAQ;QAAQ,MAAM;QAAwB,QAAQ;QAAa,WAAW;QAAI,WAAW;QAAS,YAAY;QAAQ,oBAAoB;IAAE;IAClJ;QAAE,QAAQ;QAAQ,MAAM;QAA0B,QAAQ;QAAa,WAAW;QAAI,WAAW;QAAU,YAAY;QAAQ,oBAAoB;IAAE;IAErJ,uEAAuE;IACvE;QAAE,QAAQ;QAAO,MAAM;QAAwB,QAAQ;QAAQ,WAAW;QAAI,WAAW;QAAS,YAAY;QAAU,oBAAoB;IAAE;IAC9I;QAAE,QAAQ;QAAO,MAAM;QAAiB,QAAQ;QAAQ,WAAW;QAAK,WAAW;QAAS,YAAY;QAAU,oBAAoB;IAAE;IACxI;QAAE,QAAQ;QAAO,MAAM;QAAuB,QAAQ;QAAa,WAAW;QAAK,WAAW;QAAS,YAAY;QAAO,oBAAoB;IAAE;CACjJ;AAGM,MAAM,wBAAwB,CAAC;IACpC,OAAO,uBAAuB,MAAM,CAAC,CAAA,QAAS,MAAM,UAAU,KAAK;AACrE;AAEO,MAAM,oBAAoB,CAAC;IAChC,OAAO,uBAAuB,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;AACjE;AAEO,MAAM,2BAA2B,CAAC,QAAgB,EAAE;IACzD,OAAO,uBACJ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,kBAAkB,GAAG,EAAE,kBAAkB,EAC1D,KAAK,CAAC,GAAG;AACd;AAEO,MAAM,sBAAsB,CAAC,YAAoB,QAAQ;IAC9D,OAAO,uBAAuB,MAAM,CAAC,CAAA,QAAS,MAAM,SAAS,IAAI;AACnE;AAGO,MAAM,wBAAwB,uBAAuB,GAAG,CAAC,CAAA,QAAS,MAAM,MAAM;AAG9E,MAAM,yBAAyB,yBAAyB,IAAI,GAAG,CAAC,CAAA,QAAS,MAAM,MAAM;AAGrF,MAAM,eAAe,kBAAkB,cAAc,GAAG,CAAC,CAAA,QAAS,MAAM,MAAM;AAC9E,MAAM,oBAAoB,kBAAkB,aAAa,GAAG,CAAC,CAAA,QAAS,MAAM,MAAM;AAClF,MAAM,qBAAqB,kBAAkB,cAAc,GAAG,CAAC,CAAA,QAAS,MAAM,MAAM;AACpF,MAAM,iBAAiB,kBAAkB,UAAU,GAAG,CAAC,CAAA,QAAS,MAAM,MAAM", "debugId": null}}, {"offset": {"line": 1755, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/lib/enhancedSwingScanner.ts"], "sourcesContent": ["import { SwingTradingStrategies, StrategySetup } from './swingStrategies'\nimport { PolygonAPI } from './polygon'\nimport { StockData, CandlestickData } from '@/types/trading'\nimport { SWING_TRADING_UNIVERSE } from '@/data/stockUniverse'\nimport { format, subDays } from 'date-fns'\n\nexport interface EnhancedScanResult {\n  symbol: string\n  name: string\n  sector: string\n  quote: StockData\n  overnightSetup?: StrategySetup\n  breakoutSetup?: StrategySetup\n  bestStrategy?: 'overnight_momentum' | 'technical_breakout'\n  overallScore: number\n  rank: number\n  scanTime: string\n  alerts: string[]\n  riskWarnings: string[]\n}\n\nexport interface StrategyScanSummary {\n  totalScanned: number\n  overnightSetups: number\n  breakoutSetups: number\n  bothStrategies: number\n  topSetups: EnhancedScanResult[]\n  scanDuration: number\n  marketConditions: {\n    timeOfDay: string\n    isOptimalScanTime: boolean\n    marketHours: boolean\n  }\n}\n\nexport class EnhancedSwingScanner {\n  private polygonAPI: PolygonAPI\n  private accountSize: number\n\n  constructor(accountSize: number = 100000) {\n    this.polygonAPI = new PolygonAPI(process.env.POLYGON_API_KEY)\n    this.accountSize = accountSize\n  }\n\n  // Main enhanced scanning function\n  async scanWithStrategies(\n    symbols: string[], \n    maxConcurrent: number = 5\n  ): Promise<StrategyScanSummary> {\n    const startTime = Date.now()\n    const results: EnhancedScanResult[] = []\n    const failed: string[] = []\n\n    console.log(`Starting enhanced strategy scan of ${symbols.length} stocks...`)\n\n    // Check if we're in optimal scan time (12:00-16:00 ET)\n    const marketConditions = this.getMarketConditions()\n\n    // Process stocks in batches\n    for (let i = 0; i < symbols.length; i += maxConcurrent) {\n      const batch = symbols.slice(i, i + maxConcurrent)\n      const batchPromises = batch.map(symbol => this.scanSingleStockStrategies(symbol))\n      \n      const batchResults = await Promise.allSettled(batchPromises)\n      \n      batchResults.forEach((result, index) => {\n        const symbol = batch[index]\n        if (result.status === 'fulfilled' && result.value) {\n          results.push(result.value)\n        } else {\n          failed.push(symbol)\n          console.warn(`Failed to scan ${symbol}:`, result.status === 'rejected' ? result.reason : 'Unknown error')\n        }\n      })\n\n      // Rate limiting delay\n      if (i + maxConcurrent < symbols.length) {\n        await new Promise(resolve => setTimeout(resolve, 1000))\n      }\n    }\n\n    // Sort by overall score and assign ranks\n    results.sort((a, b) => b.overallScore - a.overallScore)\n    results.forEach((result, index) => {\n      result.rank = index + 1\n    })\n\n    // Calculate summary statistics\n    const overnightSetups = results.filter(r => r.overnightSetup).length\n    const breakoutSetups = results.filter(r => r.breakoutSetup).length\n    const bothStrategies = results.filter(r => r.overnightSetup && r.breakoutSetup).length\n\n    const scanDuration = Date.now() - startTime\n\n    return {\n      totalScanned: symbols.length,\n      overnightSetups,\n      breakoutSetups,\n      bothStrategies,\n      topSetups: results.slice(0, 25), // Top 25 setups\n      scanDuration,\n      marketConditions\n    }\n  }\n\n  // Scan individual stock for both strategies\n  private async scanSingleStockStrategies(symbol: string): Promise<EnhancedScanResult | null> {\n    try {\n      console.log(`Starting scan for ${symbol}...`)\n\n      // Get stock quote and historical data\n      const [quote, historicalData] = await Promise.all([\n        this.polygonAPI.getStockQuote(symbol),\n        this.getHistoricalData(symbol)\n      ])\n\n      console.log(`Quote for ${symbol}:`, quote)\n      console.log(`Historical data length for ${symbol}:`, historicalData?.length)\n\n      if (!quote) {\n        throw new Error(`No quote data available for ${symbol}`)\n      }\n\n      if (!historicalData || historicalData.length < 30) {\n        throw new Error(`Insufficient historical data for ${symbol} - need at least 30 days, got ${historicalData?.length || 0}`)\n      }\n\n      // Analyze both strategies\n      const overnightSetup = SwingTradingStrategies.analyzeOvernightMomentum(\n        symbol, historicalData, quote, this.accountSize\n      )\n      \n      const breakoutSetup = SwingTradingStrategies.analyzeTechnicalBreakout(\n        symbol, historicalData, quote, this.accountSize\n      )\n\n      // Skip if no valid setups\n      if (!overnightSetup && !breakoutSetup) {\n        return null\n      }\n\n      // Determine best strategy and overall score\n      const { bestStrategy, overallScore } = this.calculateBestStrategy(overnightSetup, breakoutSetup)\n\n      // Generate alerts and warnings\n      const alerts = this.generateAlerts(overnightSetup, breakoutSetup, quote)\n      const riskWarnings = this.generateRiskWarnings(overnightSetup, breakoutSetup, quote)\n\n      const result = {\n        symbol,\n        name: quote.name || symbol,\n        sector: this.getSectorForSymbol(symbol),\n        quote,\n        overnightSetup: overnightSetup || undefined,\n        breakoutSetup: breakoutSetup || undefined,\n        bestStrategy,\n        overallScore,\n        rank: 0, // Will be set after sorting\n        scanTime: new Date().toISOString(),\n        alerts,\n        riskWarnings\n      }\n\n      console.log(`Successfully scanned ${symbol} with score ${overallScore}`)\n      return result\n    } catch (error) {\n      console.error(`❌ Error scanning ${symbol}:`, error)\n      console.error('📊 Quote data:', quote)\n      console.error('📈 Historical data length:', historicalData?.length)\n      console.error('🔍 Error message:', error instanceof Error ? error.message : 'Unknown error')\n      console.error('📋 Error stack:', error instanceof Error ? error.stack : 'No stack trace')\n\n      // Try to identify the specific failure point\n      if (!quote) {\n        console.error('❌ Failure: No quote data')\n      } else if (!historicalData || historicalData.length < 30) {\n        console.error('❌ Failure: Insufficient historical data')\n      } else {\n        console.error('❌ Failure: Strategy analysis error')\n      }\n\n      return null\n    }\n  }\n\n  // Get historical data with optimized API usage\n  private async getHistoricalData(symbol: string): Promise<CandlestickData[]> {\n    const to = format(new Date(), 'yyyy-MM-dd')\n    const from = format(subDays(new Date(), 100), 'yyyy-MM-dd') // 100 days should be sufficient\n\n    try {\n      console.log(`Fetching historical data for ${symbol} from ${from} to ${to}`)\n      const data = await this.polygonAPI.getHistoricalData(symbol, 'day', 1, from, to)\n\n      if (data.length === 0) {\n        console.warn(`No historical data returned for ${symbol}`)\n        throw new Error('No historical data available')\n      }\n\n      console.log(`Successfully fetched ${data.length} days of data for ${symbol}`)\n      return data\n    } catch (error) {\n      console.error(`Failed to fetch historical data for ${symbol}:`, error)\n      throw error\n    }\n  }\n\n  // Calculate best strategy and overall score\n  private calculateBestStrategy(\n    overnight?: StrategySetup | null, \n    breakout?: StrategySetup | null\n  ): { bestStrategy?: 'overnight_momentum' | 'technical_breakout', overallScore: number } {\n    if (!overnight && !breakout) {\n      return { overallScore: 0 }\n    }\n\n    if (overnight && !breakout) {\n      return { bestStrategy: 'overnight_momentum', overallScore: overnight.confidence }\n    }\n\n    if (breakout && !overnight) {\n      return { bestStrategy: 'technical_breakout', overallScore: breakout.confidence }\n    }\n\n    if (overnight && breakout) {\n      // Both strategies valid - choose higher confidence\n      if (overnight.confidence > breakout.confidence) {\n        return { bestStrategy: 'overnight_momentum', overallScore: overnight.confidence + 5 } // Bonus for multiple setups\n      } else {\n        return { bestStrategy: 'technical_breakout', overallScore: breakout.confidence + 5 }\n      }\n    }\n\n    return { overallScore: 0 }\n  }\n\n  // Generate trading alerts\n  private generateAlerts(\n    overnight?: StrategySetup | null,\n    breakout?: StrategySetup | null,\n    quote?: StockData\n  ): string[] {\n    const alerts: string[] = []\n\n    if (overnight) {\n      alerts.push(`🚀 OVERNIGHT MOMENTUM: Entry ${overnight.entryPrice.toFixed(2)}, Target ${overnight.targets[0].toFixed(2)}`)\n      alerts.push(`⏰ Execute in final 30-60 min before close`)\n      alerts.push(`🛑 Stop: ${overnight.stopLoss.toFixed(2)} (${((overnight.entryPrice - overnight.stopLoss) / overnight.entryPrice * 100).toFixed(1)}% risk)`)\n    }\n\n    if (breakout) {\n      alerts.push(`📈 BREAKOUT SETUP: Entry ${breakout.entryPrice.toFixed(2)}, riding 8-EMA`)\n      alerts.push(`🎯 Targets: ${breakout.targets.map(t => t.toFixed(2)).join(', ')}`)\n      alerts.push(`🛑 Stop: Daily close below ${breakout.stopLoss.toFixed(2)}`)\n    }\n\n    if (quote && quote.changePercent > 5) {\n      alerts.push(`🔥 Strong momentum: +${quote.changePercent.toFixed(1)}% today`)\n    }\n\n    return alerts\n  }\n\n  // Generate risk warnings\n  private generateRiskWarnings(\n    overnight?: StrategySetup | null,\n    breakout?: StrategySetup | null,\n    quote?: StockData\n  ): string[] {\n    const warnings: string[] = []\n\n    if (overnight) {\n      warnings.push(`⚠️ Overnight gap risk - size down vs intraday trades`)\n      if (quote && quote.changePercent > 8) {\n        warnings.push(`⚠️ Extended move (+${quote.changePercent.toFixed(1)}%) - consider smaller size`)\n      }\n    }\n\n    if (quote && (quote.marketCap || 0) < **********) {\n      warnings.push(`⚠️ Small cap overnight risk - volatile gaps possible`)\n    }\n\n    if (quote && quote.volume < 1000000) {\n      warnings.push(`⚠️ Lower volume - may have liquidity issues`)\n    }\n\n    return warnings\n  }\n\n  // Get market conditions with proper timezone handling\n  private getMarketConditions() {\n    const now = new Date()\n\n    // Get current time in Eastern Time (market timezone)\n    const etNow = new Date(now.toLocaleString(\"en-US\", {timeZone: \"America/New_York\"}))\n    const etHour = etNow.getHours()\n    const etMinute = etNow.getMinutes()\n    const etTimeDecimal = etHour + etMinute / 60\n\n    // Get local time for display\n    const localHour = now.getHours()\n    const localMinute = now.getMinutes()\n\n    // Check if it's a weekday (Monday = 1, Friday = 5)\n    const dayOfWeek = etNow.getDay()\n    const isWeekday = dayOfWeek >= 1 && dayOfWeek <= 5\n\n    return {\n      timeOfDay: `${localHour.toString().padStart(2, '0')}:${localMinute.toString().padStart(2, '0')} Local (${etHour.toString().padStart(2, '0')}:${etMinute.toString().padStart(2, '0')} ET)`,\n      isOptimalScanTime: isWeekday && etTimeDecimal >= 12 && etTimeDecimal <= 16, // 12:00-16:00 ET on weekdays\n      marketHours: isWeekday && etTimeDecimal >= 9.5 && etTimeDecimal <= 16, // 9:30-16:00 ET on weekdays\n      etTime: `${etHour.toString().padStart(2, '0')}:${etMinute.toString().padStart(2, '0')} ET`,\n      isWeekday\n    }\n  }\n\n  // Get sector for symbol using stock universe data\n  private getSectorForSymbol(symbol: string): string {\n    const stockInfo = SWING_TRADING_UNIVERSE.find(stock => stock.symbol === symbol)\n    return stockInfo?.sector || 'Other'\n  }\n\n  // Quick scan with strategies\n  async quickStrategyScan(prioritySymbols: string[]): Promise<EnhancedScanResult[]> {\n    const summary = await this.scanWithStrategies(prioritySymbols, 8)\n    return summary.topSetups\n  }\n}\n\n// Create singleton instance\nexport const enhancedSwingScanner = new EnhancedSwingScanner()\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;AACA;AAAA;;;;;AA+BO,MAAM;IACH,WAAsB;IACtB,YAAmB;IAE3B,YAAY,cAAsB,MAAM,CAAE;QACxC,IAAI,CAAC,UAAU,GAAG,IAAI,8JAAU,CAAC,QAAQ,GAAG,CAAC,eAAe;QAC5D,IAAI,CAAC,WAAW,GAAG;IACrB;IAEA,kCAAkC;IAClC,MAAM,mBACJ,OAAiB,EACjB,gBAAwB,CAAC,EACK;QAC9B,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,UAAgC,EAAE;QACxC,MAAM,SAAmB,EAAE;QAE3B,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,QAAQ,MAAM,CAAC,UAAU,CAAC;QAE5E,uDAAuD;QACvD,MAAM,mBAAmB,IAAI,CAAC,mBAAmB;QAEjD,4BAA4B;QAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,KAAK,cAAe;YACtD,MAAM,QAAQ,QAAQ,KAAK,CAAC,GAAG,IAAI;YACnC,MAAM,gBAAgB,MAAM,GAAG,CAAC,CAAA,SAAU,IAAI,CAAC,yBAAyB,CAAC;YAEzE,MAAM,eAAe,MAAM,QAAQ,UAAU,CAAC;YAE9C,aAAa,OAAO,CAAC,CAAC,QAAQ;gBAC5B,MAAM,SAAS,KAAK,CAAC,MAAM;gBAC3B,IAAI,OAAO,MAAM,KAAK,eAAe,OAAO,KAAK,EAAE;oBACjD,QAAQ,IAAI,CAAC,OAAO,KAAK;gBAC3B,OAAO;oBACL,OAAO,IAAI,CAAC;oBACZ,QAAQ,IAAI,CAAC,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,MAAM,KAAK,aAAa,OAAO,MAAM,GAAG;gBAC3F;YACF;YAEA,sBAAsB;YACtB,IAAI,IAAI,gBAAgB,QAAQ,MAAM,EAAE;gBACtC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD;QACF;QAEA,yCAAyC;QACzC,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,YAAY,GAAG,EAAE,YAAY;QACtD,QAAQ,OAAO,CAAC,CAAC,QAAQ;YACvB,OAAO,IAAI,GAAG,QAAQ;QACxB;QAEA,+BAA+B;QAC/B,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,cAAc,EAAE,MAAM;QACpE,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,aAAa,EAAE,MAAM;QAClE,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,cAAc,IAAI,EAAE,aAAa,EAAE,MAAM;QAEtF,MAAM,eAAe,KAAK,GAAG,KAAK;QAElC,OAAO;YACL,cAAc,QAAQ,MAAM;YAC5B;YACA;YACA;YACA,WAAW,QAAQ,KAAK,CAAC,GAAG;YAC5B;YACA;QACF;IACF;IAEA,4CAA4C;IAC5C,MAAc,0BAA0B,MAAc,EAAsC;QAC1F,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,OAAO,GAAG,CAAC;YAE5C,sCAAsC;YACtC,MAAM,CAAC,QAAO,gBAAe,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAChD,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC;gBAC9B,IAAI,CAAC,iBAAiB,CAAC;aACxB;YAED,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,EAAE;YACpC,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,OAAO,CAAC,CAAC,EAAE,iBAAgB;YAErE,IAAI,CAAC,QAAO;gBACV,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,QAAQ;YACzD;YAEA,IAAI,CAAC,mBAAkB,gBAAe,MAAM,GAAG,IAAI;gBACjD,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,OAAO,8BAA8B,EAAE,iBAAgB,UAAU,GAAG;YAC1H;YAEA,0BAA0B;YAC1B,MAAM,iBAAiB,kLAAsB,CAAC,wBAAwB,CACpE,QAAQ,iBAAgB,QAAO,IAAI,CAAC,WAAW;YAGjD,MAAM,gBAAgB,kLAAsB,CAAC,wBAAwB,CACnE,QAAQ,iBAAgB,QAAO,IAAI,CAAC,WAAW;YAGjD,0BAA0B;YAC1B,IAAI,CAAC,kBAAkB,CAAC,eAAe;gBACrC,OAAO;YACT;YAEA,4CAA4C;YAC5C,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,qBAAqB,CAAC,gBAAgB;YAElF,+BAA+B;YAC/B,MAAM,SAAS,IAAI,CAAC,cAAc,CAAC,gBAAgB,eAAe;YAClE,MAAM,eAAe,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,eAAe;YAE9E,MAAM,SAAS;gBACb;gBACA,MAAM,OAAM,IAAI,IAAI;gBACpB,QAAQ,IAAI,CAAC,kBAAkB,CAAC;gBAChC,OAAA;gBACA,gBAAgB,kBAAkB;gBAClC,eAAe,iBAAiB;gBAChC;gBACA;gBACA,MAAM;gBACN,UAAU,IAAI,OAAO,WAAW;gBAChC;gBACA;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,OAAO,YAAY,EAAE,cAAc;YACvE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC,EAAE;YAC7C,QAAQ,KAAK,CAAC,kBAAkB;YAChC,QAAQ,KAAK,CAAC,8BAA8B,gBAAgB;YAC5D,QAAQ,KAAK,CAAC,qBAAqB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC5E,QAAQ,KAAK,CAAC,mBAAmB,iBAAiB,QAAQ,MAAM,KAAK,GAAG;YAExE,6CAA6C;YAC7C,IAAI,CAAC,OAAO;gBACV,QAAQ,KAAK,CAAC;YAChB,OAAO,IAAI,CAAC,kBAAkB,eAAe,MAAM,GAAG,IAAI;gBACxD,QAAQ,KAAK,CAAC;YAChB,OAAO;gBACL,QAAQ,KAAK,CAAC;YAChB;YAEA,OAAO;QACT;IACF;IAEA,+CAA+C;IAC/C,MAAc,kBAAkB,MAAc,EAA8B;QAC1E,MAAM,KAAK,IAAA,0LAAM,EAAC,IAAI,QAAQ;QAC9B,MAAM,OAAO,IAAA,0LAAM,EAAC,IAAA,4KAAO,EAAC,IAAI,QAAQ,MAAM,cAAc,gCAAgC;;QAE5F,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,OAAO,MAAM,EAAE,KAAK,IAAI,EAAE,IAAI;YAC1E,MAAM,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,QAAQ,OAAO,GAAG,MAAM;YAE7E,IAAI,KAAK,MAAM,KAAK,GAAG;gBACrB,QAAQ,IAAI,CAAC,CAAC,gCAAgC,EAAE,QAAQ;gBACxD,MAAM,IAAI,MAAM;YAClB;YAEA,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,KAAK,MAAM,CAAC,kBAAkB,EAAE,QAAQ;YAC5E,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,OAAO,CAAC,CAAC,EAAE;YAChE,MAAM;QACR;IACF;IAEA,4CAA4C;IACpC,sBACN,SAAgC,EAChC,QAA+B,EACuD;QACtF,IAAI,CAAC,aAAa,CAAC,UAAU;YAC3B,OAAO;gBAAE,cAAc;YAAE;QAC3B;QAEA,IAAI,aAAa,CAAC,UAAU;YAC1B,OAAO;gBAAE,cAAc;gBAAsB,cAAc,UAAU,UAAU;YAAC;QAClF;QAEA,IAAI,YAAY,CAAC,WAAW;YAC1B,OAAO;gBAAE,cAAc;gBAAsB,cAAc,SAAS,UAAU;YAAC;QACjF;QAEA,IAAI,aAAa,UAAU;YACzB,mDAAmD;YACnD,IAAI,UAAU,UAAU,GAAG,SAAS,UAAU,EAAE;gBAC9C,OAAO;oBAAE,cAAc;oBAAsB,cAAc,UAAU,UAAU,GAAG;gBAAE,EAAE,4BAA4B;;YACpH,OAAO;gBACL,OAAO;oBAAE,cAAc;oBAAsB,cAAc,SAAS,UAAU,GAAG;gBAAE;YACrF;QACF;QAEA,OAAO;YAAE,cAAc;QAAE;IAC3B;IAEA,0BAA0B;IAClB,eACN,SAAgC,EAChC,QAA+B,EAC/B,MAAiB,EACP;QACV,MAAM,SAAmB,EAAE;QAE3B,IAAI,WAAW;YACb,OAAO,IAAI,CAAC,CAAC,6BAA6B,EAAE,UAAU,UAAU,CAAC,OAAO,CAAC,GAAG,SAAS,EAAE,UAAU,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI;YACxH,OAAO,IAAI,CAAC,CAAC,yCAAyC,CAAC;YACvD,OAAO,IAAI,CAAC,CAAC,SAAS,EAAE,UAAU,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,UAAU,UAAU,GAAG,UAAU,QAAQ,IAAI,UAAU,UAAU,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC;QAC1J;QAEA,IAAI,UAAU;YACZ,OAAO,IAAI,CAAC,CAAC,yBAAyB,EAAE,SAAS,UAAU,CAAC,OAAO,CAAC,GAAG,cAAc,CAAC;YACtF,OAAO,IAAI,CAAC,CAAC,YAAY,EAAE,SAAS,OAAO,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO;YAC/E,OAAO,IAAI,CAAC,CAAC,2BAA2B,EAAE,SAAS,QAAQ,CAAC,OAAO,CAAC,IAAI;QAC1E;QAEA,IAAI,UAAS,OAAM,aAAa,GAAG,GAAG;YACpC,OAAO,IAAI,CAAC,CAAC,qBAAqB,EAAE,OAAM,aAAa,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC;QAC7E;QAEA,OAAO;IACT;IAEA,yBAAyB;IACjB,qBACN,SAAgC,EAChC,QAA+B,EAC/B,MAAiB,EACP;QACV,MAAM,WAAqB,EAAE;QAE7B,IAAI,WAAW;YACb,SAAS,IAAI,CAAC,CAAC,oDAAoD,CAAC;YACpE,IAAI,UAAS,OAAM,aAAa,GAAG,GAAG;gBACpC,SAAS,IAAI,CAAC,CAAC,mBAAmB,EAAE,OAAM,aAAa,CAAC,OAAO,CAAC,GAAG,0BAA0B,CAAC;YAChG;QACF;QAEA,IAAI,UAAS,CAAC,OAAM,SAAS,IAAI,CAAC,IAAI,YAAY;YAChD,SAAS,IAAI,CAAC,CAAC,oDAAoD,CAAC;QACtE;QAEA,IAAI,UAAS,OAAM,MAAM,GAAG,SAAS;YACnC,SAAS,IAAI,CAAC,CAAC,2CAA2C,CAAC;QAC7D;QAEA,OAAO;IACT;IAEA,sDAAsD;IAC9C,sBAAsB;QAC5B,MAAM,MAAM,IAAI;QAEhB,qDAAqD;QACrD,MAAM,QAAQ,IAAI,KAAK,IAAI,cAAc,CAAC,SAAS;YAAC,UAAU;QAAkB;QAChF,MAAM,SAAS,MAAM,QAAQ;QAC7B,MAAM,WAAW,MAAM,UAAU;QACjC,MAAM,gBAAgB,SAAS,WAAW;QAE1C,6BAA6B;QAC7B,MAAM,YAAY,IAAI,QAAQ;QAC9B,MAAM,cAAc,IAAI,UAAU;QAElC,mDAAmD;QACnD,MAAM,YAAY,MAAM,MAAM;QAC9B,MAAM,YAAY,aAAa,KAAK,aAAa;QAEjD,OAAO;YACL,WAAW,GAAG,UAAU,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,YAAY,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,QAAQ,EAAE,OAAO,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,SAAS,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,IAAI,CAAC;YACzL,mBAAmB,aAAa,iBAAiB,MAAM,iBAAiB;YACxE,aAAa,aAAa,iBAAiB,OAAO,iBAAiB;YACnE,QAAQ,GAAG,OAAO,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,SAAS,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,GAAG,CAAC;YAC1F;QACF;IACF;IAEA,kDAAkD;IAC1C,mBAAmB,MAAc,EAAU;QACjD,MAAM,YAAY,iLAAsB,CAAC,IAAI,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;QACxE,OAAO,WAAW,UAAU;IAC9B;IAEA,6BAA6B;IAC7B,MAAM,kBAAkB,eAAyB,EAAiC;QAChF,MAAM,UAAU,MAAM,IAAI,CAAC,kBAAkB,CAAC,iBAAiB;QAC/D,OAAO,QAAQ,SAAS;IAC1B;AACF;AAGO,MAAM,uBAAuB,IAAI", "debugId": null}}, {"offset": {"line": 2018, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/app/api/scanner/strategies/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { EnhancedSwingScanner } from '@/lib/enhancedSwingScanner'\nimport { PRIORITY_SWING_SYMBOLS, DEFAULT_SWING_SYMBOLS, getTopSwingTradingStocks } from '@/data/stockUniverse'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const scanType = searchParams.get('type') || 'quick' // quick, full\n    const accountSize = parseInt(searchParams.get('accountSize') || '100000')\n    const limit = parseInt(searchParams.get('limit') || '20')\n    \n    console.log(`Starting ${scanType} strategy scan...`)\n\n    // Set account size for position sizing\n    const scanner = new EnhancedSwingScanner(accountSize)\n\n    let symbols: string[]\n    let maxConcurrent: number\n\n    if (scanType === 'full') {\n      // Full scan: All 70+ stocks with slower processing\n      symbols = DEFAULT_SWING_SYMBOLS\n      maxConcurrent = 3\n      console.log(`Full scan: ${symbols.length} stocks`)\n    } else {\n      // Quick scan: Top 30 swing trading candidates with faster processing\n      symbols = PRIORITY_SWING_SYMBOLS\n      maxConcurrent = 6\n      console.log(`Quick scan: ${symbols.length} priority stocks`)\n    }\n\n    const summary = await scanner.scanWithStrategies(symbols, maxConcurrent)\n    \n    // Limit results if requested\n    const limitedSummary = {\n      ...summary,\n      topSetups: summary.topSetups.slice(0, limit)\n    }\n    \n    return NextResponse.json(limitedSummary)\n  } catch (error) {\n    console.error('Error in strategy scanner API:', error)\n    return NextResponse.json(\n      { error: 'Failed to perform strategy scan' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,WAAW,aAAa,GAAG,CAAC,WAAW,QAAQ,cAAc;;QACnE,MAAM,cAAc,SAAS,aAAa,GAAG,CAAC,kBAAkB;QAChE,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QAEpD,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,iBAAiB,CAAC;QAEnD,uCAAuC;QACvC,MAAM,UAAU,IAAI,qLAAoB,CAAC;QAEzC,IAAI;QACJ,IAAI;QAEJ,IAAI,aAAa,QAAQ;YACvB,mDAAmD;YACnD,UAAU,gLAAqB;YAC/B,gBAAgB;YAChB,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,MAAM,CAAC,OAAO,CAAC;QACnD,OAAO;YACL,qEAAqE;YACrE,UAAU,iLAAsB;YAChC,gBAAgB;YAChB,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,QAAQ,MAAM,CAAC,gBAAgB,CAAC;QAC7D;QAEA,MAAM,UAAU,MAAM,QAAQ,kBAAkB,CAAC,SAAS;QAE1D,6BAA6B;QAC7B,MAAM,iBAAiB;YACrB,GAAG,OAAO;YACV,WAAW,QAAQ,SAAS,CAAC,KAAK,CAAC,GAAG;QACxC;QAEA,OAAO,yKAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO,yKAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAkC,GAC3C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}