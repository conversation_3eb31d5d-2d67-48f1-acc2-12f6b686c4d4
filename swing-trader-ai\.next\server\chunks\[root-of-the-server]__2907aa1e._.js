module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},14747,(e,t,r)=>{t.exports=e.x("path",()=>require("path"))},78006,e=>{"use strict";e.s(["PolygonAPI",()=>s]);var t=e.i(55362);let r="https://api.polygon.io",a=process.env.POLYGON_API_KEY;class s{apiKey;constructor(e){if(this.apiKey=e||a||"",!this.apiKey)throw Error("Polygon API key is required")}async getStockQuote(e){try{let a=(await t.default.get(`${r}/v2/snapshot/locale/us/markets/stocks/tickers/${e}`,{params:{apikey:this.apiKey}})).data.results[0];if(!a)throw Error(`No data found for ${e}`);let s=a.value||a,i=s.day||{},n=s.prevDay||{};s.lastQuote;let o=(s.lastTrade||{}).p||i.c||n.c,c=n.c||i.o,l=o-c;return{symbol:e.toUpperCase(),name:s.name||e.toUpperCase(),price:o,change:l,changePercent:l/c*100,volume:i.v||0,marketCap:s.market_cap,pe:void 0,dividend:void 0}}catch(a){console.error("Error fetching stock quote from Polygon:",a);try{let a=(await t.default.get(`${r}/v2/aggs/ticker/${e}/prev`,{params:{adjusted:"true",apikey:this.apiKey}})).data.results[0];return{symbol:e.toUpperCase(),name:e.toUpperCase(),price:a.c,change:a.c-a.o,changePercent:(a.c-a.o)/a.o*100,volume:a.v,marketCap:void 0,pe:void 0,dividend:void 0}}catch(t){throw console.error("Polygon fallback also failed:",t),Error(`Failed to fetch quote for ${e}`)}}}async getHistoricalData(e,a="day",s=1,i,n){try{let o=await t.default.get(`${r}/v2/aggs/ticker/${e}/range/${s}/${a}/${i}/${n}`,{params:{adjusted:"true",sort:"asc",limit:5e4,apikey:this.apiKey}});if(!o.data.results||0===o.data.results.length)return console.warn(`No historical data found for ${e}`),[];return o.data.results.map(e=>({timestamp:e.t,open:e.o,high:e.h,low:e.l,close:e.c,volume:e.v}))}catch(t){throw console.error(`Error fetching historical data for ${e}:`,t),t.response&&(console.error(`Polygon API Error: ${t.response.status} - ${t.response.statusText}`),console.error("Response data:",t.response.data)),Error(`Failed to fetch historical data for ${e}: ${t.message}`)}}async getCompanyDetails(e){try{return(await t.default.get(`${r}/v3/reference/tickers/${e}`,{params:{apikey:this.apiKey}})).data.results}catch(e){return console.error("Error fetching company details:",e),null}}async getMarketStatus(){try{return(await t.default.get(`${r}/v1/marketstatus/now`,{params:{apikey:this.apiKey}})).data}catch(e){return console.error("Error fetching market status:",e),null}}async searchStocks(e,a=10){try{return(await t.default.get(`${r}/v3/reference/tickers`,{params:{search:e,market:"stocks",active:"true",limit:a,apikey:this.apiKey}})).data.results||[]}catch(e){return console.error("Error searching stocks:",e),[]}}}new s},29547,e=>{"use strict";e.s(["FMPAPI",()=>s]);var t=e.i(55362);let r="https://financialmodelingprep.com/api",a=process.env.FMP_API_KEY;class s{apiKey;constructor(e){if(this.apiKey=e||a||"",!this.apiKey)throw Error("FMP API key is required")}async getStockQuote(e){try{let a=(await t.default.get(`${r}/v3/quote/${e}`,{params:{apikey:this.apiKey}})).data[0];if(!a)throw Error(`No data found for symbol ${e}`);return{symbol:a.symbol,name:a.name||a.symbol,price:a.price,change:a.change,changePercent:a.changesPercentage,volume:a.volume,marketCap:a.marketCap,pe:a.pe,dividend:void 0}}catch(t){throw console.error("Error fetching FMP stock quote:",t),Error(`Failed to fetch quote for ${e}`)}}async getCompanyProfile(e){try{return(await t.default.get(`${r}/v3/profile/${e}`,{params:{apikey:this.apiKey}})).data[0]}catch(e){return console.error("Error fetching company profile:",e),null}}async getFinancialRatios(e){try{return(await t.default.get(`${r}/v3/ratios/${e}`,{params:{apikey:this.apiKey}})).data[0]}catch(e){return console.error("Error fetching financial ratios:",e),null}}async getKeyMetrics(e){try{return(await t.default.get(`${r}/v3/key-metrics/${e}`,{params:{apikey:this.apiKey}})).data[0]}catch(e){return console.error("Error fetching key metrics:",e),null}}async getAnalystRecommendations(e){try{return(await t.default.get(`${r}/v3/analyst-stock-recommendations/${e}`,{params:{apikey:this.apiKey}})).data}catch(e){return console.error("Error fetching analyst recommendations:",e),[]}}async getEarningsCalendar(e,a){try{let s={apikey:this.apiKey};return e&&(s.from=e),a&&(s.to=a),(await t.default.get(`${r}/v3/earning_calendar`,{params:s})).data}catch(e){return console.error("Error fetching earnings calendar:",e),[]}}async getEconomicCalendar(e,a){try{let s={apikey:this.apiKey};return e&&(s.from=e),a&&(s.to=a),(await t.default.get(`${r}/v3/economic_calendar`,{params:s})).data}catch(e){return console.error("Error fetching economic calendar:",e),[]}}async searchStocks(e,a=10){try{return(await t.default.get(`${r}/v3/search`,{params:{query:e,limit:a,apikey:this.apiKey}})).data}catch(e){return console.error("Error searching stocks:",e),[]}}async getSectorPerformance(){try{return(await t.default.get(`${r}/v3/sector-performance`,{params:{apikey:this.apiKey}})).data}catch(e){return console.error("Error fetching sector performance:",e),[]}}async getMarketMovers(e){try{return(await t.default.get(`${r}/v3/stock_market/${e}`,{params:{apikey:this.apiKey}})).data}catch(t){return console.error(`Error fetching market ${e}:`,t),[]}}async getEarningsCalendar(e,a=30){try{let s=new Date;s.setDate(s.getDate()-a);let i=new Date;return(await t.default.get(`${r}/v3/earning_calendar`,{params:{apikey:this.apiKey,from:s.toISOString().split("T")[0],to:i.toISOString().split("T")[0],...e&&{symbol:e.toUpperCase()}}})).data||[]}catch(e){return console.error("Error fetching earnings calendar:",e),[]}}async getStockNews(e,a=50){try{return(await t.default.get(`${r}/v3/stock_news`,{params:{apikey:this.apiKey,tickers:e.toUpperCase(),limit:a}})).data||[]}catch(e){return console.error("Error fetching stock news:",e),[]}}async getAnalystRecommendations(e,a=30){try{return(await t.default.get(`${r}/v3/analyst-stock-recommendations/${e.toUpperCase()}`,{params:{apikey:this.apiKey,limit:a}})).data||[]}catch(e){return console.error("Error fetching analyst recommendations:",e),[]}}async getInsiderTrading(e,a=30){try{let s=await t.default.get(`${r}/v4/insider-trading`,{params:{apikey:this.apiKey,symbol:e.toUpperCase(),limit:5*a}}),i=new Date;return i.setDate(i.getDate()-a),(s.data||[]).filter(e=>new Date(e.filingDate)>=i)}catch(e){return console.error("Error fetching insider trading:",e),[]}}async getSECFilings(e,a=30){try{let s=await t.default.get(`${r}/v3/sec_filings/${e.toUpperCase()}`,{params:{apikey:this.apiKey,limit:2*a}}),i=new Date;return i.setDate(i.getDate()-a),(s.data||[]).filter(e=>new Date(e.filedDate)>=i)}catch(e){return console.error("Error fetching SEC filings:",e),[]}}async getPreMarketQuote(e){try{let a=(await t.default.get(`${r}/v3/quote/${e.toUpperCase()}`,{params:{apikey:this.apiKey}})).data[0];if(!a)return null;return{symbol:a.symbol,price:a.price,previousClose:a.previousClose,change:a.change,changePercent:a.changesPercentage,volume:a.volume,marketCap:a.marketCap,avgVolume:a.avgVolume,preMarketPrice:a.preMarketPrice||a.price,preMarketChange:a.preMarketChange||a.change,preMarketChangePercent:a.preMarketChangePercent||a.changesPercentage}}catch(e){return console.error("Error fetching pre-market quote:",e),null}}async getMultiplePreMarketQuotes(e){try{let a=e.map(e=>e.toUpperCase()).join(",");return((await t.default.get(`${r}/v3/quote/${a}`,{params:{apikey:this.apiKey}})).data||[]).map(e=>({symbol:e.symbol,price:e.price,previousClose:e.previousClose,change:e.change,changePercent:e.changesPercentage,volume:e.volume,marketCap:e.marketCap,avgVolume:e.avgVolume,preMarketPrice:e.preMarketPrice||e.price,preMarketChange:e.preMarketChange||e.change,preMarketChangePercent:e.preMarketChangePercent||e.changesPercentage}))}catch(e){return console.error("Error fetching multiple pre-market quotes:",e),[]}}async getCompanyProfile(e){try{return(await t.default.get(`${r}/v3/profile/${e.toUpperCase()}`,{params:{apikey:this.apiKey}})).data[0]||null}catch(e){return console.error("Error fetching company profile:",e),null}}}new s},27478,e=>{"use strict";e.s(["CatalystDetectionEngine",()=>a]);var t=e.i(29547),r=e.i(78006);class a{fmpAPI;polygonAPI;catalystCache=new Map;impactMeasurements=new Map;constructor(e,a){this.fmpAPI=new t.FMPAPI(e),this.polygonAPI=new r.PolygonAPI(a)}async detectCatalysts(e){let t=[];try{let r=`${e}_${Math.floor(Date.now()/3e5)}`;if(this.catalystCache.has(r))return this.catalystCache.get(r);let[a,s,i,n,o]=await Promise.all([this.detectEarningsCatalysts(e),this.detectNewsCatalysts(e),this.detectAnalystCatalysts(e),this.detectInsiderCatalysts(e),this.detectSECFilingCatalysts(e)]);return t.push(...a,...s,...i,...n,...o),t.sort((e,t)=>{let r=this.getFreshnessWeight(e.freshness)-this.getFreshnessWeight(t.freshness);return 0!==r?r:t.qualityScore-e.qualityScore}),this.catalystCache.set(r,t),t}catch(t){return console.error(`Error detecting catalysts for ${e}:`,t),[]}}async detectEarningsCatalysts(e){let t=[];try{for(let r of(await this.fmpAPI.getEarningsCalendar(e,30)))if(this.isEarningsBeat(r)){let a={id:`earnings_${e}_${r.date}`,symbol:e,type:"earnings_beat_guidance",tier:"tier_1",impact:"bullish",title:`${e} Beats Earnings Expectations`,description:`Q${r.quarter} earnings beat: EPS ${r.actualEPS} vs ${r.estimatedEPS} expected`,source:"FMP Earnings Data",announcementTime:r.date,discoveredTime:new Date().toISOString(),qualityScore:this.calculateEarningsQualityScore(r),freshness:this.calculateFreshness(r.date),estimatedDuration:"short_term",verified:!0,tags:["earnings","beat","guidance"],metadata:{actualEPS:r.actualEPS,estimatedEPS:r.estimatedEPS,beatPercent:(r.actualEPS-r.estimatedEPS)/r.estimatedEPS*100,guidanceRaised:r.guidanceRaised||!1}};t.push(a)}}catch(t){console.error(`Error detecting earnings catalysts for ${e}:`,t)}return t}async detectNewsCatalysts(e){let t=[];try{for(let r of(await this.fmpAPI.getStockNews(e,50))){let a=this.classifyNewsAsCatalyst(r);if(a){let s={id:`news_${e}_${r.publishedDate}_${r.title.slice(0,20).replace(/\s+/g,"_")}`,symbol:e,type:a.type,tier:a.tier,impact:a.impact,title:r.title,description:r.text?.slice(0,200)+"...",source:r.site,sourceUrl:r.url,announcementTime:r.publishedDate,discoveredTime:new Date().toISOString(),qualityScore:this.calculateNewsQualityScore(r,a.type),freshness:this.calculateFreshness(r.publishedDate),estimatedDuration:this.estimateNewsDuration(a.type),verified:this.isReliableNewsSource(r.site),tags:this.extractNewsKeywords(r.title+" "+(r.text||"")),metadata:{site:r.site,sentiment:r.sentiment||"neutral"}};t.push(s)}}}catch(t){console.error(`Error detecting news catalysts for ${e}:`,t)}return t}async detectAnalystCatalysts(e){let t=[];try{for(let r of(await this.fmpAPI.getAnalystRecommendations(e,30)))if(this.isSignificantAnalystChange(r)){let a=r.newGrade>r.previousGrade,s={id:`analyst_${e}_${r.date}_${r.analystCompany}`,symbol:e,type:a?"analyst_upgrade":"analyst_downgrade",tier:"tier_2",impact:a?"bullish":"bearish",title:`${r.analystCompany} ${a?"Upgrades":"Downgrades"} ${e}`,description:`${r.analystName} at ${r.analystCompany} ${a?"upgraded":"downgraded"} to ${r.newGrade}`,source:"FMP Analyst Data",announcementTime:r.date,discoveredTime:new Date().toISOString(),qualityScore:this.calculateAnalystQualityScore(r),freshness:this.calculateFreshness(r.date),estimatedDuration:"medium_term",verified:!0,tags:["analyst",a?"upgrade":"downgrade",r.analystCompany.toLowerCase()],metadata:{analystCompany:r.analystCompany,analystName:r.analystName,previousGrade:r.previousGrade,newGrade:r.newGrade,priceTarget:r.priceTarget}};t.push(s)}}catch(t){console.error(`Error detecting analyst catalysts for ${e}:`,t)}return t}async detectInsiderCatalysts(e){let t=[];try{for(let r of(await this.fmpAPI.getInsiderTrading(e,30)))if(this.isSignificantInsiderTrade(r)){let a=r.transactionType.toLowerCase().includes("buy")||r.transactionType.toLowerCase().includes("purchase"),s={id:`insider_${e}_${r.filingDate}_${r.reportingName}`,symbol:e,type:a?"insider_buying":"insider_selling",tier:"tier_2",impact:a?"bullish":"bearish",title:`${r.reportingName} ${a?"Buys":"Sells"} ${e} Shares`,description:`${r.reportingName} (${r.typeOfOwner}) ${r.transactionType} ${r.securitiesTransacted} shares at $${r.price}`,source:"SEC Insider Trading Filings",announcementTime:r.filingDate,discoveredTime:new Date().toISOString(),qualityScore:this.calculateInsiderQualityScore(r),freshness:this.calculateFreshness(r.filingDate),estimatedDuration:"medium_term",verified:!0,tags:["insider",a?"buying":"selling",r.typeOfOwner.toLowerCase()],metadata:{reportingName:r.reportingName,typeOfOwner:r.typeOfOwner,transactionType:r.transactionType,securitiesTransacted:r.securitiesTransacted,price:r.price,dollarValue:r.securitiesTransacted*r.price}};t.push(s)}}catch(t){console.error(`Error detecting insider catalysts for ${e}:`,t)}return t}async detectSECFilingCatalysts(e){let t=[];try{for(let r of(await this.fmpAPI.getSECFilings(e,30)))if(this.isSignificantSECFiling(r)){let a={id:`sec_${e}_${r.filedDate}_${r.type}`,symbol:e,type:"sec_filing",tier:this.getSECFilingTier(r.type),impact:this.getSECFilingImpact(r.type),title:`${e} Files ${r.type}`,description:`${r.type} filing: ${r.description||"SEC regulatory filing"}`,source:"SEC EDGAR Database",sourceUrl:r.link,announcementTime:r.filedDate,discoveredTime:new Date().toISOString(),qualityScore:this.calculateSECFilingQualityScore(r),freshness:this.calculateFreshness(r.filedDate),estimatedDuration:this.estimateSECFilingDuration(r.type),verified:!0,tags:["sec","filing",r.type.toLowerCase()],metadata:{filingType:r.type,cik:r.cik,acceptedDate:r.acceptedDate}};t.push(a)}}catch(t){console.error(`Error detecting SEC filing catalysts for ${e}:`,t)}return t}getFreshnessWeight(e){switch(e){case"fresh":return 3;case"moderate":return 2;case"stale":return 1;default:return 0}}calculateFreshness(e){let t=new Date(e),r=(new Date().getTime()-t.getTime())/36e5;return r<24?"fresh":r<72?"moderate":"stale"}isEarningsBeat(e){return e.actualEPS>e.estimatedEPS&&(e.guidanceRaised||e.actualEPS>1.05*e.estimatedEPS)}calculateEarningsQualityScore(e){let t=5,r=(e.actualEPS-e.estimatedEPS)/e.estimatedEPS*100;return r>20?t+=3:r>10?t+=2:r>5&&(t+=1),e.guidanceRaised&&(t+=2),e.actualRevenue>e.estimatedRevenue&&(t+=1),Math.min(10,t)}classifyNewsAsCatalyst(e){let t=e.title.toLowerCase()+" "+(e.text||"").toLowerCase();return t.includes("fda")&&(t.includes("approval")||t.includes("approved"))?{type:"fda_approval",tier:"tier_1",impact:"bullish"}:t.includes("trial")&&(t.includes("positive")||t.includes("successful"))?{type:"drug_trial_results",tier:"tier_1",impact:"bullish"}:t.includes("contract")&&(t.includes("win")||t.includes("awarded"))?{type:"contract_win",tier:"tier_1",impact:"bullish"}:t.includes("partnership")||t.includes("collaboration")?{type:"partnership",tier:"tier_1",impact:"bullish"}:t.includes("merger")||t.includes("acquisition")||t.includes("buyout")?{type:"merger_acquisition",tier:"tier_1",impact:"bullish"}:t.includes("stock split")||t.includes("share split")?{type:"stock_split",tier:"tier_2",impact:"bullish"}:null}calculateNewsQualityScore(e,t){let r=5;return this.isReliableNewsSource(e.site)&&(r+=2),["fda_approval","merger_acquisition","earnings_beat_guidance"].includes(t)&&(r+=2),"positive"===e.sentiment?r+=1:"negative"===e.sentiment&&(r-=1),Math.max(1,Math.min(10,r))}isReliableNewsSource(e){return["reuters.com","bloomberg.com","wsj.com","cnbc.com","marketwatch.com","yahoo.com","sec.gov","fda.gov"].some(t=>e.toLowerCase().includes(t))}extractNewsKeywords(e){let t=[],r=e.toLowerCase();for(let[e,a]of Object.entries({earnings:["earnings","eps","revenue","profit"],fda:["fda","approval","drug","trial"],merger:["merger","acquisition","buyout","takeover"],partnership:["partnership","collaboration","alliance"],contract:["contract","deal","agreement"],upgrade:["upgrade","raised","increased"],downgrade:["downgrade","lowered","reduced"]}))a.some(e=>r.includes(e))&&t.push(e);return t}estimateNewsDuration(e){switch(e){case"earnings_beat_guidance":case"fda_approval":case"merger_acquisition":default:return"short_term";case"analyst_upgrade":case"analyst_downgrade":case"partnership":return"medium_term";case"stock_split":return"long_term"}}isSignificantAnalystChange(e){return Math.abs(e.newGrade-e.previousGrade)>=1&&e.priceTarget>0}calculateAnalystQualityScore(e){let t=5;["goldman sachs","morgan stanley","jp morgan","bank of america"].some(t=>e.analystCompany.toLowerCase().includes(t))&&(t+=2);let r=Math.abs(e.newGrade-e.previousGrade);return r>=2?t+=2:r>=1&&(t+=1),e.priceTargetChange>10&&(t+=1),Math.min(10,t)}isSignificantInsiderTrade(e){return e.securitiesTransacted*e.price>=1e6&&"Other"!==e.typeOfOwner}calculateInsiderQualityScore(e){let t=5,r=e.securitiesTransacted*e.price;return r>=1e7?t+=3:r>=5e6?t+=2:r>=1e6&&(t+=1),e.typeOfOwner.toLowerCase().includes("ceo")||e.typeOfOwner.toLowerCase().includes("cfo")?t+=2:e.typeOfOwner.toLowerCase().includes("director")&&(t+=1),Math.min(10,t)}isSignificantSECFiling(e){return["8-K","10-K","10-Q","13D","13G","S-1","S-4"].includes(e.type)}getSECFilingTier(e){return["8-K","13D","S-4"].includes(e)?"tier_1":["10-K","10-Q","13G"].includes(e)?"tier_2":"tier_3"}getSECFilingImpact(e){return"neutral"}calculateSECFilingQualityScore(e){let t=5;return["8-K","13D"].includes(e.type)?t+=2:["10-K","10-Q"].includes(e.type)&&(t+=1),Math.min(10,t)}estimateSECFilingDuration(e){switch(e){case"8-K":return"short_term";case"13D":default:return"medium_term";case"S-4":return"long_term"}}}},77382,(e,t,r)=>{},11345,e=>{"use strict";e.s(["handler",()=>x,"patchFetch",()=>_,"routeModule",()=>k,"serverHooks",()=>$,"workAsyncStorage",()=>S,"workUnitAsyncStorage",()=>P],11345);var t=e.i(11971),r=e.i(6780),a=e.i(51842),s=e.i(62950),i=e.i(21346),n=e.i(30506),o=e.i(63077),c=e.i(34765),l=e.i(64182),u=e.i(85062),d=e.i(51548),p=e.i(95133),y=e.i(8819),g=e.i(41050),h=e.i(93695);e.i(96641);var m=e.i(3893);e.s(["GET",()=>v,"POST",()=>E],15922);var f=e.i(59169),w=e.i(27478);async function v(e){try{let{searchParams:t}=new URL(e.url),r=t.get("symbol"),a=t.get("symbols")?.split(",").filter(Boolean),s=t.get("types")?.split(",").filter(Boolean),i=parseInt(t.get("minQuality")||"5"),n="true"===t.get("freshOnly"),o=parseInt(t.get("limit")||"20");if(!r&&!a)return f.NextResponse.json({success:!1,error:"Symbol or symbols parameter required"},{status:400});console.log("🔍 Catalyst Detection API called with params:",{symbol:r,symbols:a?.length||0,catalystTypes:s,minQuality:i,freshOnly:n,limit:o});let c=new w.CatalystDetectionEngine(process.env.FMP_API_KEY,process.env.POLYGON_API_KEY),l=[];if(r){let e=await c.detectCatalysts(r);l.push(...e)}else if(a){let e=a.map(e=>c.detectCatalysts(e));l=(await Promise.all(e)).flat()}let u=l;s&&s.length>0&&(u=u.filter(e=>s.includes(e.type))),i>1&&(u=u.filter(e=>e.qualityScore>=i)),n&&(u=u.filter(e=>"fresh"===e.freshness)),u.sort((e,t)=>{let r=e=>{switch(e){case"fresh":return 3;case"moderate":return 2;case"stale":return 1;default:return 0}},a=10*r(e.freshness)+e.qualityScore;return 10*r(t.freshness)+t.qualityScore-a});let d=u.slice(0,o),p={totalFound:l.length,afterFilters:u.length,returned:d.length,byTier:d.reduce((e,t)=>(e[t.tier]=(e[t.tier]||0)+1,e),{}),byType:d.reduce((e,t)=>(e[t.type]=(e[t.type]||0)+1,e),{}),byFreshness:d.reduce((e,t)=>(e[t.freshness]=(e[t.freshness]||0)+1,e),{}),avgQualityScore:d.length>0?Math.round(d.reduce((e,t)=>e+t.qualityScore,0)/d.length*100)/100:0},y={success:!0,data:{catalysts:d,summary:p,filters:{symbol:r,symbols:a?.length||0,catalystTypes:s,minQuality:i,freshOnly:n,limit:o},timestamp:new Date().toISOString()}};return f.NextResponse.json(y)}catch(e){return console.error("Error in Catalyst Detection API:",e),f.NextResponse.json({success:!1,error:"Failed to detect catalysts",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function E(e){try{let{action:t,data:r}=await e.json(),a=new w.CatalystDetectionEngine(process.env.FMP_API_KEY,process.env.POLYGON_API_KEY);if("batch_detect"!==t)return f.NextResponse.json({success:!1,error:"Invalid action"},{status:400});{let e=r.symbols||[];if(!Array.isArray(e)||0===e.length)return f.NextResponse.json({success:!1,error:"Symbols array required"},{status:400});let t=[];for(let r of e)try{let e=await a.detectCatalysts(r);t.push({symbol:r,catalysts:e,success:!0})}catch(e){t.push({symbol:r,catalysts:[],success:!1,error:e instanceof Error?e.message:"Unknown error"})}return f.NextResponse.json({success:!0,data:{results:t}})}}catch(e){return console.error("Error in Catalyst Detection POST API:",e),f.NextResponse.json({success:!1,error:"Failed to process catalyst detection request",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}var C=e.i(15922);let k=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/catalyst/detect/route",pathname:"/api/catalyst/detect",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/swing-trader-ai/src/app/api/catalyst/detect/route.ts",nextConfigOutput:"",userland:C}),{workAsyncStorage:S,workUnitAsyncStorage:P,serverHooks:$}=k;function _(){return(0,a.patchFetch)({workAsyncStorage:S,workUnitAsyncStorage:P})}async function x(e,t,a){var f;let w="/api/catalyst/detect/route";w=w.replace(/\/index$/,"")||"/";let v=await k.prepare(e,t,{srcPage:w,multiZoneDraftMode:!1});if(!v)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:E,params:C,nextConfig:S,isDraftMode:P,prerenderManifest:$,routerServerContext:_,isOnDemandRevalidate:x,revalidateOnlyGenerated:b,resolvedPathname:R}=v,D=(0,n.normalizeAppPath)(w),A=!!($.dynamicRoutes[D]||$.routes[R]);if(A&&!P){let e=!!$.routes[R],t=$.dynamicRoutes[D];if(t&&!1===t.fallback&&!e)throw new h.NoFallbackError}let T=null;!A||k.isDev||P||(T="/index"===(T=R)?"/":T);let I=!0===k.isDev||!A,N=A&&!I,O=e.method||"GET",M=(0,i.getTracer)(),q=M.getActiveScopeSpan(),K={params:C,prerenderManifest:$,renderOpts:{experimental:{cacheComponents:!!S.experimental.cacheComponents,authInterrupts:!!S.experimental.authInterrupts},supportsDynamicResponse:I,incrementalCache:(0,s.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(f=S.experimental)?void 0:f.cacheLife,isRevalidate:N,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>k.onRequestError(e,t,a,_)},sharedContext:{buildId:E}},F=new o.NodeNextRequest(e),U=new o.NodeNextResponse(t),j=c.NextRequestAdapter.fromNodeNextRequest(F,(0,c.signalFromNodeResponse)(t));try{let n=async r=>k.handle(j,K).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=M.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==l.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let s=a.get("next.route");if(s){let e=`${O} ${s}`;r.setAttributes({"next.route":s,"http.route":s,"next.span_name":e}),r.updateName(e)}else r.updateName(`${O} ${e.url}`)}),o=async i=>{var o,c;let l=async({previousCacheEntry:r})=>{try{if(!(0,s.getRequestMeta)(e,"minimalMode")&&x&&b&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await n(i);e.fetchMetrics=K.renderOpts.fetchMetrics;let c=K.renderOpts.pendingWaitUntil;c&&a.waitUntil&&(a.waitUntil(c),c=void 0);let l=K.renderOpts.collectedTags;if(!A)return await (0,d.sendResponse)(F,U,o,K.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,p.toNodeOutgoingHttpHeaders)(o.headers);l&&(t[g.NEXT_CACHE_TAGS_HEADER]=l),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==K.renderOpts.collectedRevalidate&&!(K.renderOpts.collectedRevalidate>=g.INFINITE_CACHE)&&K.renderOpts.collectedRevalidate,a=void 0===K.renderOpts.collectedExpire||K.renderOpts.collectedExpire>=g.INFINITE_CACHE?void 0:K.renderOpts.collectedExpire;return{value:{kind:m.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await k.onRequestError(e,t,{routerKind:"App Router",routePath:w,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:N,isOnDemandRevalidate:x})},_),t}},h=await k.handleResponse({req:e,nextConfig:S,cacheKey:T,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:$,isRoutePPREnabled:!1,isOnDemandRevalidate:x,revalidateOnlyGenerated:b,responseGenerator:l,waitUntil:a.waitUntil});if(!A)return null;if((null==h||null==(o=h.value)?void 0:o.kind)!==m.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==h||null==(c=h.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,s.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",x?"REVALIDATED":h.isMiss?"MISS":h.isStale?"STALE":"HIT"),P&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let f=(0,p.fromNodeOutgoingHttpHeaders)(h.value.headers);return(0,s.getRequestMeta)(e,"minimalMode")&&A||f.delete(g.NEXT_CACHE_TAGS_HEADER),!h.cacheControl||t.getHeader("Cache-Control")||f.get("Cache-Control")||f.set("Cache-Control",(0,y.getCacheControlHeader)(h.cacheControl)),await (0,d.sendResponse)(F,U,new Response(h.value.body,{headers:f,status:h.value.status||200})),null};q?await o(q):await M.withPropagatedContext(e.headers,()=>M.trace(l.BaseServerSpan.handleRequest,{spanName:`${O} ${e.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":O,"http.target":e.url}},o))}catch(t){if(t instanceof h.NoFallbackError||await k.onRequestError(e,t,{routerKind:"App Router",routePath:D,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:N,isOnDemandRevalidate:x})}),A)throw t;return await (0,d.sendResponse)(F,U,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__2907aa1e._.js.map