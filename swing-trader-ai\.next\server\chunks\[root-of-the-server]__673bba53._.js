module.exports = [
"[project]/swing-trader-ai/.next-internal/server/app/api/ai/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[project]/swing-trader-ai/src/lib/openai.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__,
    "generateMarketCommentary",
    ()=>generateMarketCommentary,
    "generateRiskAssessment",
    ()=>generateRiskAssessment,
    "generateTradingRecommendations",
    ()=>generateTradingRecommendations,
    "getLatestModel",
    ()=>getLatestModel,
    "isOpenAIEnabled",
    ()=>isOpenAIEnabled
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/swing-trader-ai/node_modules/openai/index.mjs [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/swing-trader-ai/node_modules/openai/client.mjs [app-route] (ecmascript) <export OpenAI as default>");
;
// Rate limiting configuration
const RATE_LIMIT_DELAY = 1000; // 1 second between requests
let lastRequestTime = 0;
// Initialize OpenAI client
const openai = new __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
    apiKey: process.env.OPENAI_API_KEY
});
const isOpenAIEnabled = ()=>{
    return process.env.OPENAI_ENABLED === 'true' && !!process.env.OPENAI_API_KEY;
};
// Rate limiting helper
const enforceRateLimit = async ()=>{
    const now = Date.now();
    const timeSinceLastRequest = now - lastRequestTime;
    if (timeSinceLastRequest < RATE_LIMIT_DELAY) {
        const delay = RATE_LIMIT_DELAY - timeSinceLastRequest;
        await new Promise((resolve)=>setTimeout(resolve, delay));
    }
    lastRequestTime = Date.now();
};
const getLatestModel = async ()=>{
    try {
        await enforceRateLimit();
        const models = await openai.models.list();
        const gptModels = models.data.filter((model)=>model.id.startsWith('gpt-')).sort((a, b)=>b.created - a.created);
        // Prefer GPT-4 models, then GPT-3.5
        const preferredModels = [
            'gpt-4o',
            'gpt-4o-mini',
            'gpt-4-turbo',
            'gpt-4',
            'gpt-3.5-turbo'
        ];
        for (const preferred of preferredModels){
            const found = gptModels.find((model)=>model.id === preferred);
            if (found) {
                console.log(`Using OpenAI model: ${found.id}`);
                return found.id;
            }
        }
        // Fallback to the latest GPT model
        if (gptModels.length > 0) {
            console.log(`Using fallback OpenAI model: ${gptModels[0].id}`);
            return gptModels[0].id;
        }
        throw new Error('No GPT models available');
    } catch (error) {
        console.error('Error getting OpenAI models:', error);
        return 'gpt-4o'; // Default fallback
    }
};
const generateMarketCommentary = async (scanResults, marketConditions)=>{
    if (!isOpenAIEnabled()) {
        return "AI analysis disabled. Enable in configuration to get intelligent market insights.";
    }
    try {
        await enforceRateLimit();
        const model = await getLatestModel();
        const topResults = scanResults.slice(0, 5);
        const prompt = `As a professional swing trading analyst, provide a concise market commentary based on the following scan results and market conditions:

Market Conditions:
- Time: ${marketConditions.timeOfDay}
- Market Hours: ${marketConditions.marketHours ? 'Open' : 'Closed'}
- Optimal Scan Time: ${marketConditions.isOptimalScanTime ? 'Yes' : 'No'}

Top Trading Opportunities:
${topResults.map((result, i)=>`
${i + 1}. ${result.symbol} (Score: ${result.overallScore.toFixed(1)}/100)
   - Best Strategy: ${result.bestStrategy || 'None'}
   - Price Action: Recent momentum and volume patterns
   - Key Levels: Support/resistance analysis
`).join('')}

Provide a 2-3 paragraph market commentary focusing on:
1. Overall market sentiment and trading conditions
2. Key themes and sectors showing strength/weakness
3. Risk considerations and trading recommendations

Keep it professional, actionable, and under 200 words.`;
        const completion = await openai.chat.completions.create({
            model,
            messages: [
                {
                    role: 'system',
                    content: 'You are a professional swing trading analyst with expertise in technical analysis and market psychology. Provide clear, actionable insights.'
                },
                {
                    role: 'user',
                    content: prompt
                }
            ],
            max_tokens: 300,
            temperature: 0.7
        });
        return completion.choices[0]?.message?.content || 'Unable to generate market commentary at this time.';
    } catch (error) {
        console.error('Error generating market commentary:', error);
        return 'Market commentary temporarily unavailable. Technical analysis remains fully functional.';
    }
};
const generateRiskAssessment = async (setup, marketData)=>{
    if (!isOpenAIEnabled()) {
        return {
            riskScore: 5,
            riskFactors: [
                'AI analysis disabled'
            ],
            recommendations: [
                'Enable AI features for enhanced risk assessment'
            ],
            sentiment: 'neutral'
        };
    }
    try {
        await enforceRateLimit();
        const model = await getLatestModel();
        const prompt = `Analyze this swing trading setup for risk assessment:

Symbol: ${setup.symbol}
Strategy: ${setup.strategy}
Confidence: ${setup.confidence}%
Entry: $${setup.entryPrice}
Stop Loss: $${setup.stopLoss}
Targets: ${setup.targets.map((t)=>`$${t}`).join(', ')}
Position Size: ${setup.positionSize} shares
Risk Amount: $${setup.riskAmount}

Provide a JSON response with:
{
  "riskScore": 1-10 (1=low risk, 10=high risk),
  "riskFactors": ["factor1", "factor2", ...],
  "recommendations": ["rec1", "rec2", ...],
  "sentiment": "bullish|bearish|neutral"
}

Consider: market conditions, position sizing, risk/reward ratio, strategy type, and current market volatility.`;
        const completion = await openai.chat.completions.create({
            model,
            messages: [
                {
                    role: 'system',
                    content: 'You are a risk management expert specializing in swing trading. Provide objective risk assessments in valid JSON format.'
                },
                {
                    role: 'user',
                    content: prompt
                }
            ],
            max_tokens: 400,
            temperature: 0.3
        });
        const response = completion.choices[0]?.message?.content;
        if (response) {
            try {
                return JSON.parse(response);
            } catch (parseError) {
                console.error('Error parsing AI risk assessment:', parseError);
            }
        }
        // Fallback response
        return {
            riskScore: 5,
            riskFactors: [
                'Unable to complete AI risk analysis'
            ],
            recommendations: [
                'Review setup manually',
                'Consider current market conditions'
            ],
            sentiment: 'neutral'
        };
    } catch (error) {
        console.error('Error generating risk assessment:', error);
        return {
            riskScore: 5,
            riskFactors: [
                'AI risk assessment temporarily unavailable'
            ],
            recommendations: [
                'Proceed with standard risk management'
            ],
            sentiment: 'neutral'
        };
    }
};
const generateTradingRecommendations = async (scanResults, userPreferences)=>{
    if (!isOpenAIEnabled()) {
        return {
            topPicks: [
                'AI recommendations disabled'
            ],
            avoidList: [],
            marketOutlook: 'Enable AI features for personalized recommendations',
            actionItems: [
                'Configure OpenAI integration'
            ]
        };
    }
    try {
        await enforceRateLimit();
        const model = await getLatestModel();
        const topResults = scanResults.slice(0, 10);
        const prompt = `As a professional trading advisor, analyze these swing trading opportunities and provide personalized recommendations:

User Profile:
- Risk Tolerance: ${userPreferences?.riskTolerance || 'medium'}
- Trading Style: ${userPreferences?.tradingStyle || 'moderate'}
- Account Size: $${userPreferences?.accountSize?.toLocaleString() || '100,000'}

Available Opportunities:
${topResults.map((result, i)=>`
${i + 1}. ${result.symbol} (Score: ${result.overallScore.toFixed(1)}/100)
   - Strategy: ${result.bestStrategy || 'None'}
   - Confidence: High/Medium/Low based on score
`).join('')}

Provide a JSON response with:
{
  "topPicks": ["symbol1", "symbol2", "symbol3"],
  "avoidList": ["symbol1", "symbol2"],
  "marketOutlook": "brief market outlook",
  "actionItems": ["action1", "action2", "action3"]
}

Focus on risk-appropriate recommendations for the user's profile.`;
        const completion = await openai.chat.completions.create({
            model,
            messages: [
                {
                    role: 'system',
                    content: 'You are a professional trading advisor. Provide personalized, risk-appropriate recommendations in valid JSON format.'
                },
                {
                    role: 'user',
                    content: prompt
                }
            ],
            max_tokens: 500,
            temperature: 0.4
        });
        const response = completion.choices[0]?.message?.content;
        if (response) {
            try {
                return JSON.parse(response);
            } catch (parseError) {
                console.error('Error parsing AI recommendations:', parseError);
            }
        }
        // Fallback response
        return {
            topPicks: topResults.slice(0, 3).map((r)=>r.symbol),
            avoidList: [],
            marketOutlook: 'Mixed market conditions - proceed with caution',
            actionItems: [
                'Review top-scoring setups',
                'Monitor market conditions',
                'Manage position sizes'
            ]
        };
    } catch (error) {
        console.error('Error generating trading recommendations:', error);
        return {
            topPicks: [],
            avoidList: [],
            marketOutlook: 'AI recommendations temporarily unavailable',
            actionItems: [
                'Use technical analysis for decision making'
            ]
        };
    }
};
const __TURBOPACK__default__export__ = {
    isOpenAIEnabled,
    getLatestModel,
    generateMarketCommentary,
    generateRiskAssessment,
    generateTradingRecommendations
};
}),
"[project]/swing-trader-ai/src/app/api/ai/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "GET",
    ()=>GET,
    "POST",
    ()=>POST
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/src/lib/openai.ts [app-route] (ecmascript)");
;
;
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const action = searchParams.get('action');
        switch(action){
            case 'status':
                return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    enabled: (0, __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isOpenAIEnabled"])(),
                    model: (0, __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isOpenAIEnabled"])() ? await (0, __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLatestModel"])() : null,
                    features: {
                        marketCommentary: true,
                        riskAssessment: true,
                        tradingRecommendations: true
                    }
                });
            case 'model':
                if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isOpenAIEnabled"])()) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                        error: 'OpenAI not enabled'
                    }, {
                        status: 400
                    });
                }
                const model = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLatestModel"])();
                return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    model
                });
            default:
                return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Invalid action'
                }, {
                    status: 400
                });
        }
    } catch (error) {
        console.error('AI API error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isOpenAIEnabled"])()) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'OpenAI not enabled'
            }, {
                status: 400
            });
        }
        const body = await request.json();
        const { action, data } = body;
        switch(action){
            case 'market-commentary':
                const { scanResults, marketConditions } = data;
                const commentary = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateMarketCommentary"])(scanResults, marketConditions);
                return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    commentary
                });
            case 'risk-assessment':
                const { setup } = data;
                const riskAssessment = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateRiskAssessment"])(setup);
                return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    riskAssessment
                });
            case 'trading-recommendations':
                const { scanResults: results, userPreferences } = data;
                const recommendations = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateTradingRecommendations"])(results, userPreferences);
                return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    recommendations
                });
            default:
                return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Invalid action'
                }, {
                    status: 400
                });
        }
    } catch (error) {
        console.error('AI API POST error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__673bba53._.js.map