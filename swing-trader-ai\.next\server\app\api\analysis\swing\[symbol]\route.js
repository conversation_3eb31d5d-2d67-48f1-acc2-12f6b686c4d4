var R=require("../../../../../chunks/[turbopack]_runtime.js")("server/app/api/analysis/swing/[symbol]/route.js")
R.c("server/chunks/6bf44_next_f8906dbb._.js")
R.c("server/chunks/6bf44_axios_lib_4f26ea03._.js")
R.c("server/chunks/6bf44_mime-db_6bff638e._.js")
R.c("server/chunks/6bf44_tailwind-merge_dist_bundle-mjs_mjs_f1284d17._.js")
R.c("server/chunks/6bf44_9a6f9a60._.js")
R.c("server/chunks/[root-of-the-server]__e9214c1a._.js")
R.m("[project]/swing-trader-ai/.next-internal/server/app/api/analysis/swing/[symbol]/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/swing-trader-ai/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/swing-trader-ai/src/app/api/analysis/swing/[symbol]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/swing-trader-ai/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/swing-trader-ai/src/app/api/analysis/swing/[symbol]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
