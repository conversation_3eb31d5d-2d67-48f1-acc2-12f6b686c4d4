module.exports = [
"[project]/swing-trader-ai/.next-internal/server/app/api/scanner/perfect-pick/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/util [external] (util, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}),
"[externals]/stream [external] (stream, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}),
"[externals]/path [external] (path, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}),
"[externals]/http [external] (http, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}),
"[externals]/https [external] (https, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}),
"[externals]/url [external] (url, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}),
"[externals]/fs [external] (fs, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}),
"[externals]/crypto [external] (crypto, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}),
"[externals]/assert [external] (assert, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}),
"[externals]/tty [external] (tty, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}),
"[externals]/os [external] (os, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}),
"[externals]/zlib [external] (zlib, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}),
"[externals]/events [external] (events, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}),
"[project]/swing-trader-ai/src/lib/polygon.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "PolygonAPI",
    ()=>PolygonAPI,
    "polygonAPI",
    ()=>polygonAPI
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/node_modules/axios/lib/axios.js [app-route] (ecmascript)");
;
const POLYGON_BASE_URL = 'https://api.polygon.io';
const API_KEY = process.env.POLYGON_API_KEY;
class PolygonAPI {
    apiKey;
    constructor(apiKey){
        this.apiKey = apiKey || API_KEY || '';
        if (!this.apiKey) {
            throw new Error('Polygon API key is required');
        }
    }
    // Get current stock quote using Polygon's snapshot endpoint (better for paid plans)
    async getStockQuote(symbol) {
        try {
            // Use snapshot endpoint for real-time data (available on paid plans)
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${POLYGON_BASE_URL}/v2/snapshot/locale/us/markets/stocks/tickers/${symbol}`, {
                params: {
                    apikey: this.apiKey
                }
            });
            if (!response.data || !response.data.ticker) {
                throw new Error(`No data found for ${symbol}`);
            }
            const data = response.data.ticker;
            // Extract data from Polygon snapshot response structure
            const dayData = data.day || {};
            const prevDayData = data.prevDay || {};
            const minData = data.min || {};
            // Use the most recent price available
            const currentPrice = dayData.c || minData.c || prevDayData.c;
            const prevClose = prevDayData.c;
            const change = data.todaysChange || currentPrice - prevClose;
            const changePercent = data.todaysChangePerc || change / prevClose * 100;
            return {
                symbol: symbol.toUpperCase(),
                name: data.name || symbol.toUpperCase(),
                price: currentPrice || 0,
                change: change || 0,
                changePercent: changePercent || 0,
                volume: dayData.v || minData.v || 1000000,
                marketCap: data.market_cap || this.estimateMarketCap(symbol, currentPrice || 0),
                pe: undefined,
                dividend: undefined
            };
        } catch (error) {
            console.error('Error fetching stock quote from Polygon:', error);
            // Fallback to previous day data if snapshot fails
            try {
                const fallbackResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/prev`, {
                    params: {
                        adjusted: 'true',
                        apikey: this.apiKey
                    }
                });
                const data = fallbackResponse.data.results[0];
                return {
                    symbol: symbol.toUpperCase(),
                    name: symbol.toUpperCase(),
                    price: data.c || 0,
                    change: data.c - data.o || 0,
                    changePercent: data.o ? (data.c - data.o) / data.o * 100 : 0,
                    volume: data.v || 1000000,
                    marketCap: this.estimateMarketCap(symbol, data.c || 0),
                    pe: undefined,
                    dividend: undefined
                };
            } catch (fallbackError) {
                console.error('Polygon fallback also failed:', fallbackError);
                throw new Error(`Failed to fetch quote for ${symbol}`);
            }
        }
    }
    /**
   * Estimate market cap based on symbol and price
   * This is a fallback when Polygon doesn't provide market cap data
   */ estimateMarketCap(symbol, price) {
        // Import stock universe data for better estimates
        const stockEstimates = {
            // Large cap (>$200B)
            'AAPL': 3000000000000,
            'MSFT': 2*********000,
            'NVDA': 1*********000,
            'GOOGL': 1700000000000,
            'GOOG': 1700000000000,
            'AMZN': 1500000000000,
            'TSLA': *********000,
            'META': *********000,
            'BRK.B': 900000000000,
            // Mid-large cap ($50B-$200B)
            'JPM': 500000000000,
            'V': 500000000000,
            'UNH': 500000000000,
            'JNJ': 450000000000,
            'XOM': 450000000000,
            'WMT': 600000000000,
            'PG': 400000000000,
            'MA': 400000000000,
            'HD': 350000000000,
            'CVX': 300000000000,
            'ABBV': 300000000000,
            'BAC': 300000000000,
            'COST': 350000000000,
            'AVGO': 600000000000,
            'TSM': 500000000000,
            // Mid cap ($10B-$50B)
            'NFLX': 200000000000,
            'ORCL': 350000000000,
            'CRM': 250000000000,
            'ADBE': 220000000000,
            'AMD': 220000000000,
            'INTC': 200000000000,
            'QCOM': 1*********00,
            'TMO': 200000000000,
            'DHR': 1*********00,
            'CAT': 1*********00,
            'GE': 1*********00,
            'DIS': 1*********00,
            'VZ': 170000000000,
            'PFE': 160000000000,
            'NKE': 150000000000,
            'MS': 150000000000,
            'UBER': 150000000000,
            'C': 120000000000,
            'GS': 120000000000,
            'T': 120000000000,
            'AMGN': 150000000000,
            'HON': 140000000000,
            'LOW': 150000000000,
            'BMY': 120000000000,
            'CMCSA': 150000000000,
            'SBUX': 1********000,
            'MMM': 60000000000,
            // Smaller cap but popular swing trading stocks
            'PLTR': 60000000000,
            'SHOP': *********00,
            'GILD': *********00,
            'TGT': 70000000000,
            'COP': 150000000000,
            'EOG': 70000000000,
            'SLB': 60000000000,
            'PYPL': 70000000000,
            'SQ': 40000000000,
            'COIN': 50000000000,
            'DASH': 50000000000,
            'MRNA': 30000000000,
            'SNOW': 50000000000,
            'ROKU': 5000000000,
            'HOOD': 15000000000,
            'LYFT': 6000000000,
            'SPG': 50000000000,
            'PLD': 120000000000,
            'NEE': 150000000000
        };
        // Return estimated market cap if available, otherwise estimate based on price
        if (stockEstimates[symbol]) {
            return stockEstimates[symbol];
        }
        // Rough estimation based on price (very approximate)
        if (price > 500) return ********0000 // Assume large cap if high price
        ;
        if (price > 100) return 50000000000 // Assume mid-large cap
        ;
        if (price > 50) return 20000000000 // Assume mid cap
        ;
        if (price > 10) return 5000000000 // Assume small-mid cap
        ;
        return ********00 // Default to $1B minimum for scanning
        ;
    }
    // Get historical candlestick data (optimized for paid plans)
    async getHistoricalData(symbol, timespan = 'day', multiplier = 1, from, to) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/range/${multiplier}/${timespan}/${from}/${to}`, {
                params: {
                    adjusted: 'true',
                    sort: 'asc',
                    limit: 50000,
                    apikey: this.apiKey
                }
            });
            if (!response.data.results || response.data.results.length === 0) {
                console.warn(`No historical data found for ${symbol}`);
                return [];
            }
            return response.data.results.map((candle)=>({
                    timestamp: candle.t,
                    open: candle.o,
                    high: candle.h,
                    low: candle.l,
                    close: candle.c,
                    volume: candle.v
                }));
        } catch (error) {
            console.error(`Error fetching historical data for ${symbol}:`, error);
            // Log the specific error for debugging
            if (error.response) {
                console.error(`Polygon API Error: ${error.response.status} - ${error.response.statusText}`);
                console.error('Response data:', error.response.data);
            }
            throw new Error(`Failed to fetch historical data for ${symbol}: ${error.message}`);
        }
    }
    // Get company details
    async getCompanyDetails(symbol) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${POLYGON_BASE_URL}/v3/reference/tickers/${symbol}`, {
                params: {
                    apikey: this.apiKey
                }
            });
            return response.data.results;
        } catch (error) {
            console.error('Error fetching company details:', error);
            return null;
        }
    }
    // Get market status
    async getMarketStatus() {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${POLYGON_BASE_URL}/v1/marketstatus/now`, {
                params: {
                    apikey: this.apiKey
                }
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching market status:', error);
            return null;
        }
    }
    // Search for stocks
    async searchStocks(query, limit = 10) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${POLYGON_BASE_URL}/v3/reference/tickers`, {
                params: {
                    search: query,
                    market: 'stocks',
                    active: 'true',
                    limit,
                    apikey: this.apiKey
                }
            });
            return response.data.results || [];
        } catch (error) {
            console.error('Error searching stocks:', error);
            return [];
        }
    }
}
const polygonAPI = new PolygonAPI();
}),
"[externals]/net [external] (net, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}),
"[project]/swing-trader-ai/src/lib/ibkr.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "IBKRAPI",
    ()=>IBKRAPI
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/node_modules/@stoqey/ib/dist/index.js [app-route] (ecmascript)");
;
class IBKRAPI {
    ib;
    config;
    connected = false;
    nextOrderId = 1;
    positions = new Map();
    orders = new Map();
    accountSummary = null;
    constructor(config){
        this.config = config;
        this.ib = new __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["IBApi"]({
            host: config.host,
            port: config.port,
            clientId: config.clientId
        });
        this.setupEventHandlers();
    }
    setupEventHandlers() {
        // Connection events
        this.ib.on(__TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].connected, ()=>{
            console.log('✅ Connected to IBKR');
            this.connected = true;
            this.requestNextOrderId();
            this.requestAccountSummary();
            this.requestPositions();
        });
        this.ib.on(__TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].disconnected, ()=>{
            console.log('❌ Disconnected from IBKR');
            this.connected = false;
        });
        this.ib.on(__TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].error, (err, code, reqId)=>{
            console.error(`IBKR Error ${code}:`, err);
        });
        // Order management
        this.ib.on(__TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].nextValidId, (orderId)=>{
            this.nextOrderId = orderId;
            console.log(`Next valid order ID: ${orderId}`);
        });
        this.ib.on(__TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].orderStatus, (orderId, status, filled, remaining, avgFillPrice, permId, parentId, lastFillPrice, clientId, whyHeld, mktCapPrice)=>{
            const order = this.orders.get(orderId);
            if (order) {
                order.status = status;
                order.filled = filled;
                order.remaining = remaining;
                this.orders.set(orderId, order);
            }
        });
        // Position updates
        this.ib.on(__TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].position, (account, contract, position, avgCost)=>{
            const symbol = contract.symbol;
            const existingPosition = this.positions.get(symbol) || {
                symbol,
                position: 0,
                marketPrice: 0,
                marketValue: 0,
                averageCost: 0,
                unrealizedPNL: 0,
                realizedPNL: 0
            };
            existingPosition.position = position;
            existingPosition.averageCost = avgCost;
            this.positions.set(symbol, existingPosition);
        });
        // Account summary
        this.ib.on(__TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].accountSummary, (reqId, account, tag, value, currency)=>{
            if (!this.accountSummary) {
                this.accountSummary = {
                    totalCashValue: 0,
                    netLiquidation: 0,
                    grossPositionValue: 0,
                    availableFunds: 0,
                    buyingPower: 0,
                    unrealizedPnL: 0,
                    realizedPnL: 0
                };
            }
            switch(tag){
                case 'TotalCashValue':
                    this.accountSummary.totalCashValue = parseFloat(value);
                    break;
                case 'NetLiquidation':
                    this.accountSummary.netLiquidation = parseFloat(value);
                    break;
                case 'GrossPositionValue':
                    this.accountSummary.grossPositionValue = parseFloat(value);
                    break;
                case 'AvailableFunds':
                    this.accountSummary.availableFunds = parseFloat(value);
                    break;
                case 'BuyingPower':
                    this.accountSummary.buyingPower = parseFloat(value);
                    break;
                case 'UnrealizedPnL':
                    this.accountSummary.unrealizedPnL = parseFloat(value);
                    break;
                case 'RealizedPnL':
                    this.accountSummary.realizedPnL = parseFloat(value);
                    break;
            }
        });
    }
    async connect() {
        return new Promise((resolve, reject)=>{
            if (this.connected) {
                resolve();
                return;
            }
            const timeout = setTimeout(()=>{
                reject(new Error('Connection timeout'));
            }, 10000);
            this.ib.once(__TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].connected, ()=>{
                clearTimeout(timeout);
                resolve();
            });
            this.ib.once(__TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].error, (err)=>{
                clearTimeout(timeout);
                reject(err);
            });
            this.ib.connect();
        });
    }
    disconnect() {
        if (this.connected) {
            this.ib.disconnect();
        }
    }
    requestNextOrderId() {
        this.ib.reqIds(1);
    }
    requestAccountSummary() {
        this.ib.reqAccountSummary(1, 'All', 'TotalCashValue,NetLiquidation,GrossPositionValue,AvailableFunds,BuyingPower,UnrealizedPnL,RealizedPnL');
    }
    requestPositions() {
        this.ib.reqPositions();
    }
    // Create a stock contract
    createStockContract(symbol) {
        return {
            symbol: symbol.toUpperCase(),
            secType: 'STK',
            exchange: 'SMART',
            currency: 'USD'
        };
    }
    // Place a market order
    async placeMarketOrder(symbol, action, quantity) {
        if (!this.connected) {
            throw new Error('Not connected to IBKR');
        }
        const contract = this.createStockContract(symbol);
        const order = {
            orderId: this.nextOrderId,
            action,
            totalQuantity: quantity,
            orderType: 'MKT'
        };
        // Store order for tracking
        this.orders.set(this.nextOrderId, {
            orderId: this.nextOrderId,
            symbol: symbol.toUpperCase(),
            action,
            quantity,
            orderType: 'MKT',
            status: 'Submitted',
            filled: 0,
            remaining: quantity
        });
        this.ib.placeOrder(this.nextOrderId, contract, order);
        const orderId = this.nextOrderId;
        this.nextOrderId++;
        return orderId;
    }
    // Place a limit order
    async placeLimitOrder(symbol, action, quantity, price) {
        if (!this.connected) {
            throw new Error('Not connected to IBKR');
        }
        const contract = this.createStockContract(symbol);
        const order = {
            orderId: this.nextOrderId,
            action,
            totalQuantity: quantity,
            orderType: 'LMT',
            lmtPrice: price
        };
        // Store order for tracking
        this.orders.set(this.nextOrderId, {
            orderId: this.nextOrderId,
            symbol: symbol.toUpperCase(),
            action,
            quantity,
            orderType: 'LMT',
            price,
            status: 'Submitted',
            filled: 0,
            remaining: quantity
        });
        this.ib.placeOrder(this.nextOrderId, contract, order);
        const orderId = this.nextOrderId;
        this.nextOrderId++;
        return orderId;
    }
    // Place a stop-loss order
    async placeStopOrder(symbol, action, quantity, stopPrice) {
        if (!this.connected) {
            throw new Error('Not connected to IBKR');
        }
        const contract = this.createStockContract(symbol);
        const order = {
            orderId: this.nextOrderId,
            action,
            totalQuantity: quantity,
            orderType: 'STP',
            auxPrice: stopPrice
        };
        // Store order for tracking
        this.orders.set(this.nextOrderId, {
            orderId: this.nextOrderId,
            symbol: symbol.toUpperCase(),
            action,
            quantity,
            orderType: 'STP',
            price: stopPrice,
            status: 'Submitted',
            filled: 0,
            remaining: quantity
        });
        this.ib.placeOrder(this.nextOrderId, contract, order);
        const orderId = this.nextOrderId;
        this.nextOrderId++;
        return orderId;
    }
    // Cancel an order
    async cancelOrder(orderId) {
        if (!this.connected) {
            throw new Error('Not connected to IBKR');
        }
        this.ib.cancelOrder(orderId);
    }
    // Get account summary
    getAccountSummary() {
        return this.accountSummary;
    }
    // Get all positions
    getPositions() {
        return Array.from(this.positions.values());
    }
    // Get position for specific symbol
    getPosition(symbol) {
        return this.positions.get(symbol.toUpperCase()) || null;
    }
    // Get all orders
    getOrders() {
        return Array.from(this.orders.values());
    }
    // Get specific order
    getOrder(orderId) {
        return this.orders.get(orderId) || null;
    }
    // Check if connected
    isConnected() {
        return this.connected;
    }
}
}),
"[project]/swing-trader-ai/src/lib/catalystDetection.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "CatalystDetectionEngine",
    ()=>CatalystDetectionEngine
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$polygon$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/src/lib/polygon.ts [app-route] (ecmascript)");
;
class CatalystDetectionEngine {
    polygonAPI;
    catalystCache = new Map();
    impactMeasurements = new Map();
    constructor(polygonApiKey){
        this.polygonAPI = new __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$polygon$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PolygonAPI"](polygonApiKey);
    }
    /**
   * Detect catalysts for a specific symbol
   */ async detectCatalysts(symbol) {
        const catalysts = [];
        try {
            // Check cache first (5-minute cache)
            const cacheKey = `${symbol}_${Math.floor(Date.now() / (5 * 60 * 1000))}`;
            if (this.catalystCache.has(cacheKey)) {
                return this.catalystCache.get(cacheKey);
            }
            // Detect different types of catalysts in parallel
            const [earningsCatalysts, newsCatalysts, analystCatalysts, insiderCatalysts, secFilingCatalysts] = await Promise.all([
                this.detectEarningsCatalysts(symbol),
                this.detectNewsCatalysts(symbol),
                this.detectAnalystCatalysts(symbol),
                this.detectInsiderCatalysts(symbol),
                this.detectSECFilingCatalysts(symbol)
            ]);
            catalysts.push(...earningsCatalysts, ...newsCatalysts, ...analystCatalysts, ...insiderCatalysts, ...secFilingCatalysts);
            // Sort by quality score and freshness
            catalysts.sort((a, b)=>{
                const freshnessWeight = this.getFreshnessWeight(a.freshness) - this.getFreshnessWeight(b.freshness);
                if (freshnessWeight !== 0) return freshnessWeight;
                return b.qualityScore - a.qualityScore;
            });
            // Cache results
            this.catalystCache.set(cacheKey, catalysts);
            return catalysts;
        } catch (error) {
            console.error(`Error detecting catalysts for ${symbol}:`, error);
            return [];
        }
    }
    /**
   * Detect earnings-related catalysts
   */ async detectEarningsCatalysts(symbol) {
        const catalysts = [];
        try {
            // Get recent earnings data from FMP
            const earningsData = await this.fmpAPI.getEarningsCalendar(symbol, 30) // Last 30 days
            ;
            for (const earnings of earningsData){
                if (this.isEarningsBeat(earnings)) {
                    const catalyst = {
                        id: `earnings_${symbol}_${earnings.date}`,
                        symbol,
                        type: 'earnings_beat_guidance',
                        tier: 'tier_1',
                        impact: 'bullish',
                        title: `${symbol} Beats Earnings Expectations`,
                        description: `Q${earnings.quarter} earnings beat: EPS ${earnings.actualEPS} vs ${earnings.estimatedEPS} expected`,
                        source: 'FMP Earnings Data',
                        announcementTime: earnings.date,
                        discoveredTime: new Date().toISOString(),
                        qualityScore: this.calculateEarningsQualityScore(earnings),
                        freshness: this.calculateFreshness(earnings.date),
                        estimatedDuration: 'short_term',
                        verified: true,
                        tags: [
                            'earnings',
                            'beat',
                            'guidance'
                        ],
                        metadata: {
                            actualEPS: earnings.actualEPS,
                            estimatedEPS: earnings.estimatedEPS,
                            beatPercent: (earnings.actualEPS - earnings.estimatedEPS) / earnings.estimatedEPS * 100,
                            guidanceRaised: earnings.guidanceRaised || false
                        }
                    };
                    catalysts.push(catalyst);
                }
            }
        } catch (error) {
            console.error(`Error detecting earnings catalysts for ${symbol}:`, error);
        }
        return catalysts;
    }
    /**
   * Detect news-related catalysts
   */ async detectNewsCatalysts(symbol) {
        const catalysts = [];
        try {
            // Get recent news from FMP
            const newsData = await this.fmpAPI.getStockNews(symbol, 50) // Last 50 articles
            ;
            for (const news of newsData){
                const catalystType = this.classifyNewsAsCatalyst(news);
                if (catalystType) {
                    const catalyst = {
                        id: `news_${symbol}_${news.publishedDate}_${news.title.slice(0, 20).replace(/\s+/g, '_')}`,
                        symbol,
                        type: catalystType.type,
                        tier: catalystType.tier,
                        impact: catalystType.impact,
                        title: news.title,
                        description: news.text?.slice(0, 200) + '...' || news.title,
                        source: news.site,
                        sourceUrl: news.url,
                        announcementTime: news.publishedDate,
                        discoveredTime: new Date().toISOString(),
                        qualityScore: this.calculateNewsQualityScore(news, catalystType.type),
                        freshness: this.calculateFreshness(news.publishedDate),
                        estimatedDuration: this.estimateNewsDuration(catalystType.type),
                        verified: this.isReliableNewsSource(news.site),
                        tags: this.extractNewsKeywords(news.title + ' ' + (news.text || '')),
                        metadata: {
                            site: news.site,
                            sentiment: news.sentiment || 'neutral'
                        }
                    };
                    catalysts.push(catalyst);
                }
            }
        } catch (error) {
            console.error(`Error detecting news catalysts for ${symbol}:`, error);
        }
        return catalysts;
    }
    /**
   * Detect analyst upgrade/downgrade catalysts
   */ async detectAnalystCatalysts(symbol) {
        const catalysts = [];
        try {
            // Get analyst recommendations from FMP
            const analystData = await this.fmpAPI.getAnalystRecommendations(symbol, 30);
            for (const recommendation of analystData){
                if (this.isSignificantAnalystChange(recommendation)) {
                    const isUpgrade = recommendation.newGrade > recommendation.previousGrade;
                    const catalyst = {
                        id: `analyst_${symbol}_${recommendation.date}_${recommendation.analystCompany}`,
                        symbol,
                        type: isUpgrade ? 'analyst_upgrade' : 'analyst_downgrade',
                        tier: 'tier_2',
                        impact: isUpgrade ? 'bullish' : 'bearish',
                        title: `${recommendation.analystCompany} ${isUpgrade ? 'Upgrades' : 'Downgrades'} ${symbol}`,
                        description: `${recommendation.analystName} at ${recommendation.analystCompany} ${isUpgrade ? 'upgraded' : 'downgraded'} to ${recommendation.newGrade}`,
                        source: 'FMP Analyst Data',
                        announcementTime: recommendation.date,
                        discoveredTime: new Date().toISOString(),
                        qualityScore: this.calculateAnalystQualityScore(recommendation),
                        freshness: this.calculateFreshness(recommendation.date),
                        estimatedDuration: 'medium_term',
                        verified: true,
                        tags: [
                            'analyst',
                            isUpgrade ? 'upgrade' : 'downgrade',
                            recommendation.analystCompany.toLowerCase()
                        ],
                        metadata: {
                            analystCompany: recommendation.analystCompany,
                            analystName: recommendation.analystName,
                            previousGrade: recommendation.previousGrade,
                            newGrade: recommendation.newGrade,
                            priceTarget: recommendation.priceTarget
                        }
                    };
                    catalysts.push(catalyst);
                }
            }
        } catch (error) {
            console.error(`Error detecting analyst catalysts for ${symbol}:`, error);
        }
        return catalysts;
    }
    /**
   * Detect insider trading catalysts
   */ async detectInsiderCatalysts(symbol) {
        const catalysts = [];
        try {
            // Get insider trading data from FMP
            const insiderData = await this.fmpAPI.getInsiderTrading(symbol, 30);
            for (const trade of insiderData){
                if (this.isSignificantInsiderTrade(trade)) {
                    const isBuying = trade.transactionType.toLowerCase().includes('buy') || trade.transactionType.toLowerCase().includes('purchase');
                    const catalyst = {
                        id: `insider_${symbol}_${trade.filingDate}_${trade.reportingName}`,
                        symbol,
                        type: isBuying ? 'insider_buying' : 'insider_selling',
                        tier: 'tier_2',
                        impact: isBuying ? 'bullish' : 'bearish',
                        title: `${trade.reportingName} ${isBuying ? 'Buys' : 'Sells'} ${symbol} Shares`,
                        description: `${trade.reportingName} (${trade.typeOfOwner}) ${trade.transactionType} ${trade.securitiesTransacted} shares at $${trade.price}`,
                        source: 'SEC Insider Trading Filings',
                        announcementTime: trade.filingDate,
                        discoveredTime: new Date().toISOString(),
                        qualityScore: this.calculateInsiderQualityScore(trade),
                        freshness: this.calculateFreshness(trade.filingDate),
                        estimatedDuration: 'medium_term',
                        verified: true,
                        tags: [
                            'insider',
                            isBuying ? 'buying' : 'selling',
                            trade.typeOfOwner.toLowerCase()
                        ],
                        metadata: {
                            reportingName: trade.reportingName,
                            typeOfOwner: trade.typeOfOwner,
                            transactionType: trade.transactionType,
                            securitiesTransacted: trade.securitiesTransacted,
                            price: trade.price,
                            dollarValue: trade.securitiesTransacted * trade.price
                        }
                    };
                    catalysts.push(catalyst);
                }
            }
        } catch (error) {
            console.error(`Error detecting insider catalysts for ${symbol}:`, error);
        }
        return catalysts;
    }
    /**
   * Detect SEC filing catalysts
   */ async detectSECFilingCatalysts(symbol) {
        const catalysts = [];
        try {
            // Get recent SEC filings from FMP
            const filings = await this.fmpAPI.getSECFilings(symbol, 30);
            for (const filing of filings){
                if (this.isSignificantSECFiling(filing)) {
                    const catalyst = {
                        id: `sec_${symbol}_${filing.filedDate}_${filing.type}`,
                        symbol,
                        type: 'sec_filing',
                        tier: this.getSECFilingTier(filing.type),
                        impact: this.getSECFilingImpact(filing.type),
                        title: `${symbol} Files ${filing.type}`,
                        description: `${filing.type} filing: ${filing.description || 'SEC regulatory filing'}`,
                        source: 'SEC EDGAR Database',
                        sourceUrl: filing.link,
                        announcementTime: filing.filedDate,
                        discoveredTime: new Date().toISOString(),
                        qualityScore: this.calculateSECFilingQualityScore(filing),
                        freshness: this.calculateFreshness(filing.filedDate),
                        estimatedDuration: this.estimateSECFilingDuration(filing.type),
                        verified: true,
                        tags: [
                            'sec',
                            'filing',
                            filing.type.toLowerCase()
                        ],
                        metadata: {
                            filingType: filing.type,
                            cik: filing.cik,
                            acceptedDate: filing.acceptedDate
                        }
                    };
                    catalysts.push(catalyst);
                }
            }
        } catch (error) {
            console.error(`Error detecting SEC filing catalysts for ${symbol}:`, error);
        }
        return catalysts;
    }
    // Helper methods for catalyst classification and scoring
    getFreshnessWeight(freshness) {
        switch(freshness){
            case 'fresh':
                return 3;
            case 'moderate':
                return 2;
            case 'stale':
                return 1;
            default:
                return 0;
        }
    }
    calculateFreshness(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const hoursAgo = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
        if (hoursAgo < 24) return 'fresh';
        if (hoursAgo < 72) return 'moderate';
        return 'stale';
    }
    isEarningsBeat(earnings) {
        return earnings.actualEPS > earnings.estimatedEPS && (earnings.guidanceRaised || earnings.actualEPS > earnings.estimatedEPS * 1.05);
    }
    calculateEarningsQualityScore(earnings) {
        let score = 5 // Base score
        ;
        // Beat percentage
        const beatPercent = (earnings.actualEPS - earnings.estimatedEPS) / earnings.estimatedEPS * 100;
        if (beatPercent > 20) score += 3;
        else if (beatPercent > 10) score += 2;
        else if (beatPercent > 5) score += 1;
        // Guidance raised
        if (earnings.guidanceRaised) score += 2;
        // Revenue beat
        if (earnings.actualRevenue > earnings.estimatedRevenue) score += 1;
        return Math.min(10, score);
    }
    classifyNewsAsCatalyst(news) {
        const title = news.title.toLowerCase();
        const text = (news.text || '').toLowerCase();
        const content = title + ' ' + text;
        // FDA/Drug related
        if (content.includes('fda') && (content.includes('approval') || content.includes('approved'))) {
            return {
                type: 'fda_approval',
                tier: 'tier_1',
                impact: 'bullish'
            };
        }
        if (content.includes('trial') && (content.includes('positive') || content.includes('successful'))) {
            return {
                type: 'drug_trial_results',
                tier: 'tier_1',
                impact: 'bullish'
            };
        }
        // Contract/Partnership
        if (content.includes('contract') && (content.includes('win') || content.includes('awarded'))) {
            return {
                type: 'contract_win',
                tier: 'tier_1',
                impact: 'bullish'
            };
        }
        if (content.includes('partnership') || content.includes('collaboration')) {
            return {
                type: 'partnership',
                tier: 'tier_1',
                impact: 'bullish'
            };
        }
        // M&A
        if (content.includes('merger') || content.includes('acquisition') || content.includes('buyout')) {
            return {
                type: 'merger_acquisition',
                tier: 'tier_1',
                impact: 'bullish'
            };
        }
        // Stock split
        if (content.includes('stock split') || content.includes('share split')) {
            return {
                type: 'stock_split',
                tier: 'tier_2',
                impact: 'bullish'
            };
        }
        return null;
    }
    calculateNewsQualityScore(news, catalystType) {
        let score = 5 // Base score
        ;
        // Source reliability
        if (this.isReliableNewsSource(news.site)) score += 2;
        // Catalyst type importance
        if ([
            'fda_approval',
            'merger_acquisition',
            'earnings_beat_guidance'
        ].includes(catalystType)) {
            score += 2;
        }
        // Sentiment
        if (news.sentiment === 'positive') score += 1;
        else if (news.sentiment === 'negative') score -= 1;
        return Math.max(1, Math.min(10, score));
    }
    isReliableNewsSource(site) {
        const reliableSources = [
            'reuters.com',
            'bloomberg.com',
            'wsj.com',
            'cnbc.com',
            'marketwatch.com',
            'yahoo.com',
            'sec.gov',
            'fda.gov'
        ];
        return reliableSources.some((source)=>site.toLowerCase().includes(source));
    }
    extractNewsKeywords(text) {
        const keywords = [];
        const content = text.toLowerCase();
        const keywordMap = {
            'earnings': [
                'earnings',
                'eps',
                'revenue',
                'profit'
            ],
            'fda': [
                'fda',
                'approval',
                'drug',
                'trial'
            ],
            'merger': [
                'merger',
                'acquisition',
                'buyout',
                'takeover'
            ],
            'partnership': [
                'partnership',
                'collaboration',
                'alliance'
            ],
            'contract': [
                'contract',
                'deal',
                'agreement'
            ],
            'upgrade': [
                'upgrade',
                'raised',
                'increased'
            ],
            'downgrade': [
                'downgrade',
                'lowered',
                'reduced'
            ]
        };
        for (const [category, terms] of Object.entries(keywordMap)){
            if (terms.some((term)=>content.includes(term))) {
                keywords.push(category);
            }
        }
        return keywords;
    }
    estimateNewsDuration(catalystType) {
        switch(catalystType){
            case 'earnings_beat_guidance':
            case 'fda_approval':
            case 'merger_acquisition':
                return 'short_term';
            case 'analyst_upgrade':
            case 'analyst_downgrade':
            case 'partnership':
                return 'medium_term';
            case 'stock_split':
                return 'long_term';
            default:
                return 'short_term';
        }
    }
    isSignificantAnalystChange(recommendation) {
        // Check if it's a meaningful grade change
        const gradeChange = Math.abs(recommendation.newGrade - recommendation.previousGrade);
        return gradeChange >= 1 && recommendation.priceTarget > 0;
    }
    calculateAnalystQualityScore(recommendation) {
        let score = 5 // Base score
        ;
        // Analyst firm reputation (simplified)
        const topFirms = [
            'goldman sachs',
            'morgan stanley',
            'jp morgan',
            'bank of america'
        ];
        if (topFirms.some((firm)=>recommendation.analystCompany.toLowerCase().includes(firm))) {
            score += 2;
        }
        // Grade change magnitude
        const gradeChange = Math.abs(recommendation.newGrade - recommendation.previousGrade);
        if (gradeChange >= 2) score += 2;
        else if (gradeChange >= 1) score += 1;
        // Price target change
        if (recommendation.priceTargetChange > 10) score += 1;
        return Math.min(10, score);
    }
    isSignificantInsiderTrade(trade) {
        const dollarValue = trade.securitiesTransacted * trade.price;
        return dollarValue >= 1000000 && // $1M+ transactions
        trade.typeOfOwner !== 'Other' // Exclude generic "Other" category
        ;
    }
    calculateInsiderQualityScore(trade) {
        let score = 5 // Base score
        ;
        const dollarValue = trade.securitiesTransacted * trade.price;
        // Transaction size
        if (dollarValue >= ********) score += 3; // $10M+
        else if (dollarValue >= 5000000) score += 2; // $5M+
        else if (dollarValue >= 1000000) score += 1; // $1M+
        // Insider type
        if (trade.typeOfOwner.toLowerCase().includes('ceo') || trade.typeOfOwner.toLowerCase().includes('cfo')) {
            score += 2;
        } else if (trade.typeOfOwner.toLowerCase().includes('director')) {
            score += 1;
        }
        return Math.min(10, score);
    }
    isSignificantSECFiling(filing) {
        const significantFilings = [
            '8-K',
            '10-K',
            '10-Q',
            '13D',
            '13G',
            'S-1',
            'S-4'
        ];
        return significantFilings.includes(filing.type);
    }
    getSECFilingTier(filingType) {
        const tier1Filings = [
            '8-K',
            '13D',
            'S-4'
        ] // Material events, activist investors, M&A
        ;
        const tier2Filings = [
            '10-K',
            '10-Q',
            '13G'
        ] // Regular reports, passive investors
        ;
        if (tier1Filings.includes(filingType)) return 'tier_1';
        if (tier2Filings.includes(filingType)) return 'tier_2';
        return 'tier_3';
    }
    getSECFilingImpact(filingType) {
        // Most SEC filings are neutral until analyzed
        return 'neutral';
    }
    calculateSECFilingQualityScore(filing) {
        let score = 5 // Base score
        ;
        // Filing type importance
        if ([
            '8-K',
            '13D'
        ].includes(filing.type)) score += 2;
        else if ([
            '10-K',
            '10-Q'
        ].includes(filing.type)) score += 1;
        return Math.min(10, score);
    }
    estimateSECFilingDuration(filingType) {
        switch(filingType){
            case '8-K':
                return 'short_term' // Material events
                ;
            case '13D':
                return 'medium_term' // Activist investors
                ;
            case 'S-4':
                return 'long_term' // M&A registration
                ;
            default:
                return 'medium_term';
        }
    }
}
}),
"[project]/swing-trader-ai/src/lib/preMarketGapScanner.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "PreMarketGapScanner",
    ()=>PreMarketGapScanner
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$polygon$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/src/lib/polygon.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$ibkr$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/src/lib/ibkr.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$catalystDetection$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/src/lib/catalystDetection.ts [app-route] (ecmascript)");
;
;
;
class PreMarketGapScanner {
    polygonAPI;
    ibkrAPI;
    catalystEngine;
    useIBKR;
    // Default universe of 65+ stocks to scan
    SCAN_UNIVERSE = [
        // Mega caps
        'AAPL',
        'MSFT',
        'GOOGL',
        'GOOG',
        'AMZN',
        'NVDA',
        'META',
        'TSLA',
        'BRK.B',
        'UNH',
        'JNJ',
        'XOM',
        'JPM',
        'V',
        'PG',
        'HD',
        'CVX',
        'MA',
        'BAC',
        'ABBV',
        'PFE',
        'AVGO',
        'KO',
        'MRK',
        'PEP',
        'TMO',
        'COST',
        'DIS',
        'ABT',
        'ACN',
        'MCD',
        'CSCO',
        'LIN',
        'VZ',
        'ADBE',
        'WMT',
        'CRM',
        'NFLX',
        'DHR',
        'NKE',
        'TXN',
        'NEE',
        'BMY',
        'ORCL',
        'PM',
        'RTX',
        'UPS',
        'QCOM',
        'T',
        'LOW',
        // High-beta growth stocks
        'AMD',
        'CRM',
        'SNOW',
        'PLTR',
        'ROKU',
        'ZM',
        'DOCU',
        'PTON',
        'SHOP',
        'SQ',
        'PYPL',
        'UBER',
        'LYFT',
        'ABNB',
        'COIN',
        'RBLX',
        'U',
        'DKNG',
        'CRWD',
        'ZS',
        // Biotech/Pharma (catalyst-heavy)
        'GILD',
        'BIIB',
        'REGN',
        'VRTX',
        'ILMN',
        'MRNA',
        'BNTX',
        'AMGN',
        'CELG',
        'ISRG'
    ];
    constructor(polygonApiKey, useIBKR = true){
        this.polygonAPI = new __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$polygon$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PolygonAPI"](polygonApiKey);
        this.ibkrAPI = new __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$ibkr$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["IBKRAPI"]({
            host: '127.0.0.1',
            port: 4002,
            clientId: 1,
            paperTrading: true
        });
        this.catalystEngine = new __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$catalystDetection$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CatalystDetectionEngine"](polygonApiKey);
        this.useIBKR = useIBKR;
    }
    /**
   * Run comprehensive pre-market gap scan
   */ async runGapScan(customUniverse) {
        const universe = customUniverse || this.SCAN_UNIVERSE;
        const results = [];
        console.log(`🔍 Starting pre-market gap scan on ${universe.length} symbols...`);
        try {
            let quotes = [];
            if (this.useIBKR) {
                console.log('📊 Attempting to use IBKR for market data...');
                try {
                    // Try to connect to IBKR first
                    await this.ibkrAPI.connect();
                    // Get quotes from IBKR
                    quotes = await this.getIBKRQuotes(universe);
                    if (quotes.length > 0) {
                        console.log(`✅ Retrieved ${quotes.length} quotes from IBKR`);
                    } else {
                        throw new Error('No quotes received from IBKR');
                    }
                } catch (ibkrError) {
                    console.warn('⚠️ IBKR connection failed, falling back to FMP:', ibkrError);
                    this.useIBKR = false;
                }
            }
            // Fallback to Polygon if IBKR failed or not enabled
            if (!this.useIBKR || quotes.length === 0) {
                console.log('📊 Using Polygon API for market data...');
                quotes = await this.getPolygonQuotes(universe);
            }
            // Process each quote in parallel
            const scanPromises = quotes.map((quote)=>this.processSingleStock(quote));
            const scanResults = await Promise.all(scanPromises);
            // Filter out null results and sort by gap percentage
            const validResults = scanResults.filter((result)=>result !== null).sort((a, b)=>b.gapPercent - a.gapPercent);
            console.log(`✅ Gap scan complete. Found ${validResults.length} results.`);
            return validResults;
        } catch (error) {
            console.error('Error running gap scan:', error);
            return [];
        }
    }
    /**
   * Get quotes from IBKR
   */ async getIBKRQuotes(symbols) {
        try {
            const quotes = [];
            // Process symbols in batches to avoid overwhelming IBKR
            const batchSize = 10;
            for(let i = 0; i < symbols.length; i += batchSize){
                const batch = symbols.slice(i, i + batchSize);
                const batchPromises = batch.map(async (symbol)=>{
                    try {
                        const quote = await this.ibkrAPI.getMarketData(symbol);
                        if (quote) {
                            return {
                                symbol: quote.symbol,
                                price: quote.last || quote.close,
                                previousClose: quote.previousClose,
                                change: quote.change,
                                changesPercentage: quote.changePercent,
                                volume: quote.volume,
                                marketCap: quote.marketCap || 0,
                                avgVolume: quote.avgVolume || 0,
                                preMarketPrice: quote.last || quote.close,
                                preMarketChange: quote.change,
                                preMarketChangePercent: quote.changePercent
                            };
                        }
                        return null;
                    } catch (error) {
                        console.error(`Error getting IBKR quote for ${symbol}:`, error);
                        return null;
                    }
                });
                const batchResults = await Promise.all(batchPromises);
                quotes.push(...batchResults.filter((q)=>q !== null));
                // Small delay between batches
                if (i + batchSize < symbols.length) {
                    await new Promise((resolve)=>setTimeout(resolve, 100));
                }
            }
            return quotes;
        } catch (error) {
            console.error('Error getting IBKR quotes:', error);
            return [];
        }
    }
    /**
   * Get quotes from Polygon API
   */ async getPolygonQuotes(symbols) {
        try {
            const quotes = [];
            // Process symbols in batches to avoid overwhelming Polygon
            const batchSize = 10;
            for(let i = 0; i < symbols.length; i += batchSize){
                const batch = symbols.slice(i, i + batchSize);
                const batchPromises = batch.map(async (symbol)=>{
                    try {
                        // Get current quote from Polygon
                        const quote = await this.polygonAPI.getStockQuote(symbol);
                        if (quote) {
                            return {
                                symbol: quote.symbol,
                                price: quote.price,
                                previousClose: quote.previousClose,
                                change: quote.change,
                                changesPercentage: quote.changePercent,
                                volume: quote.volume,
                                marketCap: quote.marketCap || 0,
                                avgVolume: quote.avgVolume || 0,
                                preMarketPrice: quote.price,
                                preMarketChange: quote.change,
                                preMarketChangePercent: quote.changePercent
                            };
                        }
                        return null;
                    } catch (error) {
                        console.error(`Error getting Polygon quote for ${symbol}:`, error);
                        return null;
                    }
                });
                const batchResults = await Promise.all(batchPromises);
                quotes.push(...batchResults.filter((q)=>q !== null));
                // Small delay between batches to respect rate limits
                if (i + batchSize < symbols.length) {
                    await new Promise((resolve)=>setTimeout(resolve, 100));
                }
            }
            return quotes;
        } catch (error) {
            console.error('Error getting Polygon quotes:', error);
            return [];
        }
    }
    /**
   * Process a single stock for gap scan criteria
   */ async processSingleStock(quote) {
        try {
            const symbol = quote.symbol;
            const currentPrice = quote.preMarketPrice || quote.price;
            const previousClose = quote.previousClose;
            if (!currentPrice || !previousClose || previousClose <= 0) {
                return null;
            }
            const gapPercent = (currentPrice - previousClose) / previousClose * 100;
            // Quick filter: only process stocks with 3%+ gaps
            if (gapPercent < 3.0) {
                return null;
            }
            // Get additional data
            const [companyProfile, catalysts] = await Promise.all([
                this.fmpAPI.getCompanyProfile(symbol),
                this.catalystEngine.detectCatalysts(symbol)
            ]);
            if (!companyProfile) {
                return null;
            }
            // Calculate pre-market metrics
            const preMarketVolume = quote.volume || 0;
            const avgDailyVolume = quote.avgVolume || 1;
            const preMarketDollarVolume = preMarketVolume * currentPrice;
            const marketCap = quote.marketCap || companyProfile.mktCap || 0;
            // Check all criteria
            const criteriaChecks = {
                priceAbove1Dollar: currentPrice > 1.0,
                gapAbove3Percent: gapPercent >= 3.0,
                marketCapAbove800M: marketCap >= *********,
                preMarketVolumeAbove20K: preMarketVolume >= 20000,
                preMarketDollarVolumeAbove1M: preMarketDollarVolume >= 1000000,
                excludesPennyStocks: currentPrice > 1.0 && marketCap >= *********,
                hasCatalyst: catalysts.length > 0
            };
            const meetsAllCriteria = Object.values(criteriaChecks).every((check)=>check);
            // Get the best catalyst (highest quality score)
            const bestCatalyst = catalysts.length > 0 ? catalysts.reduce((best, current)=>current.qualityScore > best.qualityScore ? current : best) : undefined;
            const result = {
                symbol,
                name: companyProfile.companyName || symbol,
                sector: companyProfile.sector || 'Unknown',
                price: currentPrice,
                previousClose,
                gapPercent,
                preMarketHigh: currentPrice,
                preMarketLow: currentPrice * 0.98,
                preMarketVolume,
                preMarketDollarVolume,
                marketCap,
                averageDailyVolume: avgDailyVolume,
                catalyst: bestCatalyst,
                scanTime: new Date().toISOString(),
                meetsAllCriteria,
                criteriaChecks
            };
            return result;
        } catch (error) {
            console.error(`Error processing ${quote.symbol}:`, error);
            return null;
        }
    }
    /**
   * Get filtered results that meet all Perfect-Pick criteria
   */ async getPerfectPickCandidates(customUniverse) {
        const allResults = await this.runGapScan(customUniverse);
        return allResults.filter((result)=>result.meetsAllCriteria && result.catalyst && result.catalyst.tier === 'tier_1' // Only highest tier catalysts
        );
    }
    /**
   * Get results by gap percentage ranges
   */ async getGapRangeResults(minGap = 3, maxGap = 15, customUniverse) {
        const allResults = await this.runGapScan(customUniverse);
        return allResults.filter((result)=>result.gapPercent >= minGap && result.gapPercent <= maxGap);
    }
    /**
   * Get results by catalyst type
   */ async getCatalystTypeResults(catalystTypes, customUniverse) {
        const allResults = await this.runGapScan(customUniverse);
        return allResults.filter((result)=>result.catalyst && catalystTypes.includes(result.catalyst.type));
    }
    /**
   * Get scheduled scan times (4 AM, 6 AM, 8 AM, 9 AM EST)
   */ getScheduledScanTimes() {
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const scanTimes = [
            new Date(today.getTime() + 4 * 60 * 60 * 1000),
            new Date(today.getTime() + 6 * 60 * 60 * 1000),
            new Date(today.getTime() + 8 * 60 * 60 * 1000),
            new Date(today.getTime() + 9 * 60 * 60 * 1000) // 9 AM EST
        ];
        // Adjust for EST (UTC-5) or EDT (UTC-4)
        const isEDT = this.isDaylightSavingTime(now);
        const offsetHours = isEDT ? 4 : 5;
        return scanTimes.map((time)=>new Date(time.getTime() + offsetHours * 60 * 60 * 1000));
    }
    /**
   * Check if current time is during daylight saving time
   */ isDaylightSavingTime(date) {
        const year = date.getFullYear();
        // DST starts second Sunday in March
        const dstStart = new Date(year, 2, 1) // March 1st
        ;
        dstStart.setDate(dstStart.getDate() + (7 - dstStart.getDay()) + 7); // Second Sunday
        // DST ends first Sunday in November
        const dstEnd = new Date(year, 10, 1) // November 1st
        ;
        dstEnd.setDate(dstEnd.getDate() + (7 - dstEnd.getDay())); // First Sunday
        return date >= dstStart && date < dstEnd;
    }
    /**
   * Get real-time updates for existing scan results
   */ async updateScanResults(existingResults) {
        const symbols = existingResults.map((result)=>result.symbol);
        const updatedQuotes = await this.fmpAPI.getMultiplePreMarketQuotes(symbols);
        const updatedResults = [];
        for (const quote of updatedQuotes){
            const existingResult = existingResults.find((r)=>r.symbol === quote.symbol);
            if (!existingResult) continue;
            const currentPrice = quote.preMarketPrice || quote.price;
            const gapPercent = (currentPrice - quote.previousClose) / quote.previousClose * 100;
            const updatedResult = {
                ...existingResult,
                price: currentPrice,
                gapPercent,
                preMarketVolume: quote.volume || 0,
                preMarketDollarVolume: (quote.volume || 0) * currentPrice,
                scanTime: new Date().toISOString()
            };
            // Re-check criteria with updated data
            updatedResult.criteriaChecks.gapAbove3Percent = gapPercent >= 3.0;
            updatedResult.criteriaChecks.preMarketVolumeAbove20K = updatedResult.preMarketVolume >= 20000;
            updatedResult.criteriaChecks.preMarketDollarVolumeAbove1M = updatedResult.preMarketDollarVolume >= 1000000;
            updatedResult.meetsAllCriteria = Object.values(updatedResult.criteriaChecks).every((check)=>check);
            updatedResults.push(updatedResult);
        }
        return updatedResults.sort((a, b)=>b.gapPercent - a.gapPercent);
    }
    /**
   * Get summary statistics for scan results
   */ getScanSummary(results) {
        const totalScanned = this.SCAN_UNIVERSE.length;
        const gapsFound = results.length;
        const perfectPicks = results.filter((r)=>r.meetsAllCriteria).length;
        const withCatalysts = results.filter((r)=>r.catalyst).length;
        const avgGap = results.length > 0 ? results.reduce((sum, r)=>sum + r.gapPercent, 0) / results.length : 0;
        const sectorBreakdown = results.reduce((acc, result)=>{
            acc[result.sector] = (acc[result.sector] || 0) + 1;
            return acc;
        }, {});
        const catalystTypeBreakdown = results.filter((r)=>r.catalyst).reduce((acc, result)=>{
            const type = result.catalyst.type;
            acc[type] = (acc[type] || 0) + 1;
            return acc;
        }, {});
        return {
            totalScanned,
            gapsFound,
            perfectPicks,
            withCatalysts,
            avgGap: Math.round(avgGap * 100) / 100,
            sectorBreakdown,
            catalystTypeBreakdown,
            scanTime: new Date().toISOString()
        };
    }
}
}),
"[project]/swing-trader-ai/src/lib/indicators.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "TechnicalIndicators",
    ()=>TechnicalIndicators
]);
class TechnicalIndicators {
    // Simple Moving Average
    static sma(data, period) {
        const result = [];
        for(let i = period - 1; i < data.length; i++){
            const sum = data.slice(i - period + 1, i + 1).reduce((a, b)=>a + b, 0);
            result.push(sum / period);
        }
        return result;
    }
    // Exponential Moving Average
    static ema(data, period) {
        const result = [];
        const multiplier = 2 / (period + 1);
        // Start with SMA for first value
        let ema = data.slice(0, period).reduce((a, b)=>a + b, 0) / period;
        result.push(ema);
        for(let i = period; i < data.length; i++){
            ema = data[i] * multiplier + ema * (1 - multiplier);
            result.push(ema);
        }
        return result;
    }
    // Relative Strength Index
    static rsi(data, period = 14) {
        const gains = [];
        const losses = [];
        for(let i = 1; i < data.length; i++){
            const change = data[i] - data[i - 1];
            gains.push(change > 0 ? change : 0);
            losses.push(change < 0 ? Math.abs(change) : 0);
        }
        const avgGains = this.sma(gains, period);
        const avgLosses = this.sma(losses, period);
        return avgGains.map((gain, i)=>{
            const rs = gain / avgLosses[i];
            return 100 - 100 / (1 + rs);
        });
    }
    // MACD (Moving Average Convergence Divergence)
    static macd(data, fastPeriod = 12, slowPeriod = 26, signalPeriod = 9) {
        const fastEMA = this.ema(data, fastPeriod);
        const slowEMA = this.ema(data, slowPeriod);
        // Align arrays (slowEMA starts later)
        const startIndex = slowPeriod - fastPeriod;
        const macdLine = fastEMA.slice(startIndex).map((fast, i)=>fast - slowEMA[i]);
        const signalLine = this.ema(macdLine, signalPeriod);
        const histogram = macdLine.slice(signalPeriod - 1).map((macd, i)=>macd - signalLine[i]);
        return {
            macd: macdLine,
            signal: signalLine,
            histogram
        };
    }
    // Bollinger Bands
    static bollingerBands(data, period = 20, stdDev = 2) {
        const sma = this.sma(data, period);
        const bands = sma.map((avg, i)=>{
            const slice = data.slice(i, i + period);
            const variance = slice.reduce((sum, val)=>sum + Math.pow(val - avg, 2), 0) / period;
            const standardDeviation = Math.sqrt(variance);
            return {
                upper: avg + standardDeviation * stdDev,
                middle: avg,
                lower: avg - standardDeviation * stdDev
            };
        });
        return bands;
    }
    // Support and Resistance Levels
    static findSupportResistance(candles, lookback = 20) {
        const highs = candles.map((c)=>c.high);
        const lows = candles.map((c)=>c.low);
        const resistance = [];
        const support = [];
        for(let i = lookback; i < candles.length - lookback; i++){
            const currentHigh = highs[i];
            const currentLow = lows[i];
            // Check if current high is a local maximum
            const isResistance = highs.slice(i - lookback, i).every((h)=>h <= currentHigh) && highs.slice(i + 1, i + lookback + 1).every((h)=>h <= currentHigh);
            // Check if current low is a local minimum
            const isSupport = lows.slice(i - lookback, i).every((l)=>l >= currentLow) && lows.slice(i + 1, i + lookback + 1).every((l)=>l >= currentLow);
            if (isResistance) resistance.push(currentHigh);
            if (isSupport) support.push(currentLow);
        }
        return {
            support,
            resistance
        };
    }
    // Volume analysis
    static volumeAnalysis(candles, period = 20) {
        const volumes = candles.map((c)=>c.volume);
        const avgVolume = this.sma(volumes, period);
        const currentVolume = volumes[volumes.length - 1];
        const currentAvgVolume = avgVolume[avgVolume.length - 1];
        return {
            currentVolume,
            averageVolume: currentAvgVolume,
            volumeRatio: currentVolume / currentAvgVolume,
            isHighVolume: currentVolume > currentAvgVolume * 1.5,
            isLowVolume: currentVolume < currentAvgVolume * 0.5
        };
    }
    // Swing Trading Analysis
    static analyzeSwingSetup(candles) {
        const closes = candles.map((c)=>c.close);
        const indicators = [];
        // RSI Analysis
        const rsi = this.rsi(closes);
        const currentRSI = rsi[rsi.length - 1];
        let rsiSignal = 'NEUTRAL';
        let rsiDescription = `RSI: ${currentRSI.toFixed(2)}`;
        if (currentRSI < 30) {
            rsiSignal = 'BUY';
            rsiDescription += ' - Oversold condition, potential bounce';
        } else if (currentRSI > 70) {
            rsiSignal = 'SELL';
            rsiDescription += ' - Overbought condition, potential pullback';
        } else {
            rsiDescription += ' - Neutral zone';
        }
        indicators.push({
            name: 'RSI',
            value: currentRSI,
            signal: rsiSignal,
            description: rsiDescription
        });
        // Moving Average Analysis
        const sma20 = this.sma(closes, 20);
        const sma50 = this.sma(closes, 50);
        const currentPrice = closes[closes.length - 1];
        const currentSMA20 = sma20[sma20.length - 1];
        const currentSMA50 = sma50[sma50.length - 1];
        let maSignal = 'NEUTRAL';
        let maDescription = `Price vs SMA20: ${((currentPrice / currentSMA20 - 1) * 100).toFixed(2)}%`;
        if (currentPrice > currentSMA20 && currentSMA20 > currentSMA50) {
            maSignal = 'BUY';
            maDescription += ' - Bullish trend';
        } else if (currentPrice < currentSMA20 && currentSMA20 < currentSMA50) {
            maSignal = 'SELL';
            maDescription += ' - Bearish trend';
        } else {
            maDescription += ' - Mixed signals';
        }
        indicators.push({
            name: 'Moving Averages',
            value: (currentPrice / currentSMA20 - 1) * 100,
            signal: maSignal,
            description: maDescription
        });
        // MACD Analysis
        const macdData = this.macd(closes);
        const currentMACD = macdData.macd[macdData.macd.length - 1];
        const currentSignal = macdData.signal[macdData.signal.length - 1];
        const currentHistogram = macdData.histogram[macdData.histogram.length - 1];
        let macdSignal = 'NEUTRAL';
        let macdDescription = `MACD: ${currentMACD.toFixed(4)}, Signal: ${currentSignal.toFixed(4)}`;
        if (currentMACD > currentSignal && currentHistogram > 0) {
            macdSignal = 'BUY';
            macdDescription += ' - Bullish momentum';
        } else if (currentMACD < currentSignal && currentHistogram < 0) {
            macdSignal = 'SELL';
            macdDescription += ' - Bearish momentum';
        } else {
            macdDescription += ' - Momentum shifting';
        }
        indicators.push({
            name: 'MACD',
            value: currentHistogram,
            signal: macdSignal,
            description: macdDescription
        });
        // Volume Analysis
        const volumeData = this.volumeAnalysis(candles);
        let volumeSignal = 'NEUTRAL';
        let volumeDescription = `Volume: ${(volumeData.volumeRatio * 100).toFixed(0)}% of average`;
        if (volumeData.isHighVolume) {
            volumeSignal = 'BUY';
            volumeDescription += ' - High volume confirms move';
        } else if (volumeData.isLowVolume) {
            volumeSignal = 'SELL';
            volumeDescription += ' - Low volume, weak conviction';
        } else {
            volumeDescription += ' - Normal volume';
        }
        indicators.push({
            name: 'Volume',
            value: volumeData.volumeRatio,
            signal: volumeSignal,
            description: volumeDescription
        });
        return indicators;
    }
}
}),
"[project]/swing-trader-ai/src/lib/technicalGateAnalysis.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "TechnicalGateAnalysis",
    ()=>TechnicalGateAnalysis
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$indicators$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/src/lib/indicators.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$polygon$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/src/lib/polygon.ts [app-route] (ecmascript)");
;
;
class TechnicalGateAnalysis {
    polygonAPI;
    constructor(polygonApiKey){
        this.polygonAPI = new __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$polygon$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PolygonAPI"](polygonApiKey);
    }
    /**
   * Perform comprehensive technical gate analysis
   */ async analyzeTechnicalGate(symbol) {
        try {
            // Get historical data (need at least 200 days for SMA200)
            const historicalData = await this.getHistoricalData(symbol, 250);
            if (!historicalData || historicalData.length < 200) {
                console.error(`Insufficient data for ${symbol} - need at least 200 days`);
                return null;
            }
            const currentPrice = historicalData[historicalData.length - 1].close;
            // Calculate technical indicators
            const sma200 = __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$indicators$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TechnicalIndicators"].calculateSMA(historicalData, 200);
            const ema8 = __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$indicators$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TechnicalIndicators"].calculateEMA(historicalData, 8);
            const vwap = this.calculateVWAP(historicalData.slice(-20)) // 20-day VWAP
            ;
            // Analyze trend confirmation
            const dailyTrendConfirmed = this.analyzeDailyTrend(historicalData);
            // Check moving average conditions
            const aboveSMA200 = currentPrice > sma200[sma200.length - 1];
            const aboveEMA8 = currentPrice > ema8[ema8.length - 1];
            const respectsEMA8 = this.checkEMA8Respect(historicalData, ema8);
            // Check for all-time high
            const isAtAllTimeHigh = this.checkAllTimeHigh(historicalData);
            // Check for clean breakout
            const hasCleanBreakout = this.checkCleanBreakout(historicalData);
            // Check volume expansion
            const volumeExpansion = this.checkVolumeExpansion(historicalData);
            // Calculate resistance and support levels
            const resistanceLevels = this.calculateResistanceLevels(historicalData);
            const supportLevels = this.calculateSupportLevels(historicalData);
            // Calculate overall grade and score
            const gateScore = this.calculateGateScore({
                dailyTrendConfirmed,
                aboveSMA200,
                aboveEMA8,
                respectsEMA8,
                isAtAllTimeHigh,
                hasCleanBreakout,
                volumeExpansion
            });
            const overallGrade = this.calculateOverallGrade(gateScore);
            const analysis = {
                symbol,
                dailyTrendConfirmed,
                aboveSMA200,
                aboveEMA8,
                respectsEMA8,
                isAtAllTimeHigh,
                hasCleanBreakout,
                volumeExpansion,
                overallGrade,
                gateScore,
                resistanceLevels,
                supportLevels,
                keyTechnicalLevels: {
                    sma200: sma200[sma200.length - 1],
                    ema8: ema8[ema8.length - 1],
                    vwap,
                    previousHigh: Math.max(...historicalData.slice(-20).map((d)=>d.high)),
                    previousLow: Math.min(...historicalData.slice(-20).map((d)=>d.low))
                }
            };
            return analysis;
        } catch (error) {
            console.error(`Error analyzing technical gate for ${symbol}:`, error);
            return null;
        }
    }
    /**
   * Get historical candlestick data
   */ async getHistoricalData(symbol, days) {
        try {
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - days);
            return await this.polygonAPI.getHistoricalData(symbol, startDate.toISOString().split('T')[0], endDate.toISOString().split('T')[0], '1', 'day');
        } catch (error) {
            console.error(`Error fetching historical data for ${symbol}:`, error);
            return [];
        }
    }
    /**
   * Analyze daily trend confirmation (higher highs, higher lows over 20+ days)
   */ analyzeDailyTrend(data) {
        if (data.length < 20) return false;
        const recent20Days = data.slice(-20);
        const first10Days = recent20Days.slice(0, 10);
        const last10Days = recent20Days.slice(10);
        const firstPeriodHigh = Math.max(...first10Days.map((d)=>d.high));
        const firstPeriodLow = Math.min(...first10Days.map((d)=>d.low));
        const lastPeriodHigh = Math.max(...last10Days.map((d)=>d.high));
        const lastPeriodLow = Math.min(...last10Days.map((d)=>d.low));
        // Check for higher highs and higher lows
        return lastPeriodHigh > firstPeriodHigh && lastPeriodLow > firstPeriodLow;
    }
    /**
   * Check if stock consistently respects/reclaims 8-EMA
   */ checkEMA8Respect(data, ema8) {
        if (data.length < 20 || ema8.length < 20) return false;
        const recent20Days = data.slice(-20);
        const recent20EMA = ema8.slice(-20);
        let respectCount = 0;
        for(let i = 0; i < recent20Days.length; i++){
            const candle = recent20Days[i];
            const emaValue = recent20EMA[i];
            // Check if low didn't break significantly below EMA8 (allow 2% cushion)
            if (candle.low >= emaValue * 0.98) {
                respectCount++;
            }
        }
        // Stock respects EMA8 if it holds above it 70% of the time
        return respectCount / recent20Days.length >= 0.7;
    }
    /**
   * Check if stock is at or near all-time high
   */ checkAllTimeHigh(data) {
        const currentPrice = data[data.length - 1].close;
        const allTimeHigh = Math.max(...data.map((d)=>d.high));
        // Consider "at ATH" if within 5% of all-time high
        return currentPrice >= allTimeHigh * 0.95;
    }
    /**
   * Check for clean breakout from consolidation patterns
   */ checkCleanBreakout(data) {
        if (data.length < 30) return false;
        const recent30Days = data.slice(-30);
        const last5Days = recent30Days.slice(-5);
        const consolidationPeriod = recent30Days.slice(-30, -5);
        // Calculate consolidation range
        const consolidationHigh = Math.max(...consolidationPeriod.map((d)=>d.high));
        const consolidationLow = Math.min(...consolidationPeriod.map((d)=>d.low));
        const consolidationRange = (consolidationHigh - consolidationLow) / consolidationLow;
        // Check if recent price broke above consolidation with volume
        const recentHigh = Math.max(...last5Days.map((d)=>d.high));
        const recentVolume = last5Days.reduce((sum, d)=>sum + d.volume, 0) / last5Days.length;
        const avgVolume = consolidationPeriod.reduce((sum, d)=>sum + d.volume, 0) / consolidationPeriod.length;
        // Clean breakout criteria:
        // 1. Consolidation range < 20% (tight consolidation)
        // 2. Recent high > consolidation high
        // 3. Volume expansion on breakout
        return consolidationRange < 0.20 && recentHigh > consolidationHigh && recentVolume > avgVolume * 1.5;
    }
    /**
   * Check for volume expansion on breakout days
   */ checkVolumeExpansion(data) {
        if (data.length < 20) return false;
        const recent5Days = data.slice(-5);
        const previous20Days = data.slice(-25, -5);
        const recentAvgVolume = recent5Days.reduce((sum, d)=>sum + d.volume, 0) / recent5Days.length;
        const historicalAvgVolume = previous20Days.reduce((sum, d)=>sum + d.volume, 0) / previous20Days.length;
        // Volume expansion if recent volume is 150%+ of historical average
        return recentAvgVolume > historicalAvgVolume * 1.5;
    }
    /**
   * Calculate VWAP (Volume Weighted Average Price)
   */ calculateVWAP(data) {
        let totalVolume = 0;
        let totalVolumePrice = 0;
        for (const candle of data){
            const typicalPrice = (candle.high + candle.low + candle.close) / 3;
            totalVolumePrice += typicalPrice * candle.volume;
            totalVolume += candle.volume;
        }
        return totalVolume > 0 ? totalVolumePrice / totalVolume : 0;
    }
    /**
   * Calculate resistance levels using pivot highs
   */ calculateResistanceLevels(data) {
        const resistanceLevels = [];
        const lookback = 5 // Look for pivots with 5 days on each side
        ;
        for(let i = lookback; i < data.length - lookback; i++){
            const current = data[i];
            let isPivotHigh = true;
            // Check if current high is higher than surrounding highs
            for(let j = i - lookback; j <= i + lookback; j++){
                if (j !== i && data[j].high >= current.high) {
                    isPivotHigh = false;
                    break;
                }
            }
            if (isPivotHigh) {
                resistanceLevels.push(current.high);
            }
        }
        // Return top 5 most recent resistance levels
        return resistanceLevels.slice(-5).sort((a, b)=>b - a);
    }
    /**
   * Calculate support levels using pivot lows
   */ calculateSupportLevels(data) {
        const supportLevels = [];
        const lookback = 5;
        for(let i = lookback; i < data.length - lookback; i++){
            const current = data[i];
            let isPivotLow = true;
            // Check if current low is lower than surrounding lows
            for(let j = i - lookback; j <= i + lookback; j++){
                if (j !== i && data[j].low <= current.low) {
                    isPivotLow = false;
                    break;
                }
            }
            if (isPivotLow) {
                supportLevels.push(current.low);
            }
        }
        // Return top 5 most recent support levels
        return supportLevels.slice(-5).sort((a, b)=>b - a);
    }
    /**
   * Calculate overall gate score (0-100)
   */ calculateGateScore(conditions) {
        let score = 0;
        // Required conditions (higher weight)
        if (conditions.dailyTrendConfirmed) score += 20;
        if (conditions.aboveSMA200) score += 20;
        if (conditions.aboveEMA8) score += 15;
        // Premium conditions (bonus points)
        if (conditions.respectsEMA8) score += 15;
        if (conditions.isAtAllTimeHigh) score += 15;
        if (conditions.hasCleanBreakout) score += 10;
        if (conditions.volumeExpansion) score += 5;
        return Math.min(100, score);
    }
    /**
   * Calculate overall grade based on score
   */ calculateOverallGrade(score) {
        if (score >= 90) return 'A';
        if (score >= 80) return 'B';
        if (score >= 70) return 'C';
        if (score >= 60) return 'D';
        return 'F';
    }
    /**
   * Batch analyze multiple symbols
   */ async batchAnalyzeTechnicalGates(symbols) {
        const results = [];
        // Process in chunks to avoid API rate limits
        const chunkSize = 5;
        for(let i = 0; i < symbols.length; i += chunkSize){
            const chunk = symbols.slice(i, i + chunkSize);
            const chunkPromises = chunk.map((symbol)=>this.analyzeTechnicalGate(symbol));
            const chunkResults = await Promise.all(chunkPromises);
            results.push(...chunkResults.filter((result)=>result !== null));
            // Small delay between chunks
            if (i + chunkSize < symbols.length) {
                await new Promise((resolve)=>setTimeout(resolve, 1000));
            }
        }
        return results.sort((a, b)=>b.gateScore - a.gateScore);
    }
}
}),
"[project]/swing-trader-ai/src/lib/perfectPickTradingSystem.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "PerfectPickTradingSystem",
    ()=>PerfectPickTradingSystem
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$preMarketGapScanner$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/src/lib/preMarketGapScanner.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$technicalGateAnalysis$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/src/lib/technicalGateAnalysis.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$catalystDetection$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/src/lib/catalystDetection.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$polygon$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/src/lib/polygon.ts [app-route] (ecmascript)");
;
;
;
;
class PerfectPickTradingSystem {
    gapScanner;
    technicalAnalyzer;
    catalystEngine;
    polygonAPI;
    constructor(polygonApiKey, useIBKR = true){
        this.gapScanner = new __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$preMarketGapScanner$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PreMarketGapScanner"](polygonApiKey, useIBKR);
        this.technicalAnalyzer = new __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$technicalGateAnalysis$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TechnicalGateAnalysis"](polygonApiKey);
        this.catalystEngine = new __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$catalystDetection$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CatalystDetectionEngine"](polygonApiKey);
        this.polygonAPI = new __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$polygon$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PolygonAPI"](polygonApiKey);
    }
    /**
   * Run complete Perfect-Pick analysis pipeline
   */ async runPerfectPickScan(accountSize = 100000, riskPercent = 2, customUniverse) {
        console.log('🎯 Starting Perfect-Pick Trading System scan...');
        try {
            // Step 1: Pre-Market Gap Scan
            console.log('📊 Running pre-market gap scan...');
            const gapResults = await this.gapScanner.runGapScan(customUniverse);
            // Filter for basic criteria first
            const qualifiedGaps = gapResults.filter((gap)=>gap.gapPercent >= 3.0 && gap.gapPercent <= 15.0 && // Exclude over-extended gaps
                gap.marketCap >= ********* && gap.price > 1.0);
            console.log(`✅ Found ${qualifiedGaps.length} qualified gap candidates`);
            if (qualifiedGaps.length === 0) {
                return [];
            }
            // Step 2: Technical Gate Analysis
            console.log('🔍 Running technical gate analysis...');
            const symbols = qualifiedGaps.map((gap)=>gap.symbol);
            const technicalAnalyses = await this.technicalAnalyzer.batchAnalyzeTechnicalGates(symbols);
            // Filter for passing technical gates (Grade B or better)
            const passingTechnical = technicalAnalyses.filter((analysis)=>[
                    'A',
                    'B'
                ].includes(analysis.overallGrade) && analysis.aboveSMA200 && analysis.aboveEMA8);
            console.log(`✅ ${passingTechnical.length} stocks passed technical gate`);
            // Step 3: Combine and create Perfect-Pick setups
            const perfectPickSetups = [];
            for (const gapResult of qualifiedGaps){
                const technicalAnalysis = passingTechnical.find((t)=>t.symbol === gapResult.symbol);
                if (!technicalAnalysis) continue;
                const catalyst = gapResult.catalyst;
                if (!catalyst || !this.isValidCatalyst(catalyst)) continue;
                // Create Perfect-Pick setup
                const setup = await this.createPerfectPickSetup(gapResult, technicalAnalysis, catalyst, accountSize, riskPercent);
                if (setup && this.validatePerfectPickSetup(setup)) {
                    perfectPickSetups.push(setup);
                }
            }
            // Sort by overall score
            perfectPickSetups.sort((a, b)=>b.overallScore - a.overallScore);
            console.log(`🎯 Generated ${perfectPickSetups.length} Perfect-Pick setups`);
            return perfectPickSetups;
        } catch (error) {
            console.error('Error running Perfect-Pick scan:', error);
            return [];
        }
    }
    /**
   * Create a complete Perfect-Pick setup
   */ async createPerfectPickSetup(gapScan, technicalGate, catalyst, accountSize, riskPercent) {
        try {
            const symbol = gapScan.symbol;
            const currentPrice = gapScan.price;
            // Calculate risk management
            const preMarketLow = gapScan.preMarketLow;
            const stopLoss = preMarketLow * 0.99 // Slightly below PML for safety
            ;
            const riskPerShare = currentPrice - stopLoss;
            if (riskPerShare <= 0) {
                return null // Invalid risk setup
                ;
            }
            const maxRiskAmount = accountSize * (riskPercent / 100);
            const positionSize = Math.floor(maxRiskAmount / riskPerShare);
            const maxPositionValue = accountSize * 0.05 // 5% max position size
            ;
            const maxShares = Math.floor(maxPositionValue / currentPrice);
            const finalPositionSize = Math.min(positionSize, maxShares);
            const actualRiskAmount = finalPositionSize * riskPerShare;
            // Calculate reward targets (minimum 3:1 R/R required)
            const target3R = currentPrice + riskPerShare * 3;
            const target4R = currentPrice + riskPerShare * 4;
            const target5R = currentPrice + riskPerShare * 5;
            const riskRewardRatio = 3 // Minimum required
            ;
            // Check for exclusion reasons
            const exclusionReasons = this.checkExclusionCriteria(gapScan, technicalGate, catalyst);
            // Validation checks
            const validationChecks = {
                hasValidCatalyst: this.isValidCatalyst(catalyst),
                meetsGapCriteria: gapScan.meetsAllCriteria,
                passesTechnicalGate: [
                    'A',
                    'B'
                ].includes(technicalGate.overallGrade),
                hasEntryTrigger: true,
                meetsRiskReward: riskRewardRatio >= 3,
                noExclusionFlags: exclusionReasons.length === 0
            };
            // Calculate overall score
            const overallScore = this.calculateOverallScore(gapScan, technicalGate, catalyst, validationChecks);
            const setupGrade = this.calculateSetupGrade(overallScore);
            const setup = {
                symbol,
                name: gapScan.name,
                catalyst,
                gapScan,
                technicalGate,
                riskManagement: {
                    entryPrice: currentPrice,
                    stopLoss,
                    stopLossType: 'pre_market_low',
                    riskPerShare,
                    positionSize: finalPositionSize,
                    accountRiskPercent: riskPercent,
                    maxPositionPercent: 5
                },
                rewardPlanning: {
                    riskRewardRatio,
                    target3R,
                    target4R,
                    target5R,
                    scaleOutPlan: [
                        {
                            level: 3,
                            percentage: 25
                        },
                        {
                            level: 4,
                            percentage: 25
                        },
                        {
                            level: 5,
                            percentage: 25
                        } // Take 25% at 5R, hold 25% for trend
                    ]
                },
                overallScore,
                setupGrade,
                exclusionReasons,
                validationChecks,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            return setup;
        } catch (error) {
            console.error(`Error creating Perfect-Pick setup for ${gapScan.symbol}:`, error);
            return null;
        }
    }
    /**
   * Generate intraday entry trigger
   */ async generateEntryTrigger(symbol, preMarketHigh) {
        try {
            // Get current intraday data
            const currentQuote = await this.fmpAPI.getStockQuote(symbol);
            const currentPrice = currentQuote.price;
            // Calculate VWAP (simplified - would need intraday data for accurate VWAP)
            const vwap = currentPrice * 0.995 // Approximation
            ;
            // Determine entry signal type
            let entrySignalType;
            let urgency;
            let conditions = [];
            if (currentPrice > preMarketHigh) {
                entrySignalType = 'pmh_break';
                urgency = 'immediate';
                conditions.push('Clean break above pre-market high');
            } else if (currentPrice <= vwap && currentPrice > vwap * 0.98) {
                entrySignalType = 'vwap_pullback';
                urgency = 'wait_for_pullback';
                conditions.push('Pullback to VWAP support');
            } else {
                entrySignalType = 'first_candle_close';
                urgency = 'breakout_confirmation';
                conditions.push('Wait for first 5-min candle close above PMH');
            }
            // Check volume confirmation (simplified)
            const volumeConfirmation = currentQuote.volume > (currentQuote.volume || 0) * 1.5;
            const vwapRising = true // Would need historical VWAP data to determine
            ;
            const noMajorResistance = true // Would need to check against resistance levels
            ;
            const trigger = {
                symbol,
                preMarketHigh,
                preMarketLow: preMarketHigh * 0.95,
                vwap,
                entrySignalType,
                entryPrice: currentPrice,
                entryTime: new Date().toISOString(),
                volumeConfirmation,
                vwapRising,
                noMajorResistance,
                triggerValid: volumeConfirmation && vwapRising && noMajorResistance,
                urgency,
                conditions
            };
            return trigger;
        } catch (error) {
            console.error(`Error generating entry trigger for ${symbol}:`, error);
            return null;
        }
    }
    /**
   * Validate catalyst quality and tier
   */ isValidCatalyst(catalyst) {
        // Tier 1 catalysts (highest priority)
        const tier1Types = [
            'earnings_beat_guidance',
            'fda_approval',
            'drug_trial_results',
            'contract_win',
            'partnership',
            'merger_acquisition'
        ];
        // Tier 2 catalysts (secondary)
        const tier2Types = [
            'analyst_upgrade',
            'stock_split',
            'sector_rotation'
        ];
        const isValidType = tier1Types.includes(catalyst.type) || tier2Types.includes(catalyst.type);
        const isFresh = catalyst.freshness === 'fresh' || catalyst.freshness === 'moderate';
        const hasQuality = catalyst.qualityScore >= 6;
        const isVerified = catalyst.verified;
        return isValidType && isFresh && hasQuality && isVerified;
    }
    /**
   * Check for exclusion criteria
   */ checkExclusionCriteria(gapScan, technicalGate, catalyst) {
        const exclusions = [];
        // Anti-pattern filters
        if (!technicalGate.dailyTrendConfirmed) {
            exclusions.push('Stock not in confirmed daily uptrend');
        }
        if (!technicalGate.aboveSMA200) {
            exclusions.push('Stock below 200-day SMA');
        }
        if (gapScan.gapPercent > 15) {
            exclusions.push('Gap too extended (>15%)');
        }
        if (gapScan.averageDailyVolume < 500000) {
            exclusions.push('Low liquidity (avg daily volume <500K)');
        }
        if (catalyst.impact === 'bearish') {
            exclusions.push('Negative catalyst detected');
        }
        if (catalyst.freshness === 'stale') {
            exclusions.push('Catalyst is stale (>72 hours old)');
        }
        return exclusions;
    }
    /**
   * Calculate overall setup score (0-100)
   */ calculateOverallScore(gapScan, technicalGate, catalyst, validationChecks) {
        let score = 0;
        // Gap quality (25 points max)
        score += Math.min(25, gapScan.gapPercent * 2); // 3% gap = 6 points, 10% gap = 20 points
        // Technical gate score (35 points max)
        score += technicalGate.gateScore / 100 * 35;
        // Catalyst quality (25 points max)
        score += catalyst.qualityScore / 10 * 25;
        // Validation bonus (15 points max)
        const validationCount = Object.values(validationChecks).filter(Boolean).length;
        score += validationCount / Object.keys(validationChecks).length * 15;
        return Math.min(100, Math.round(score));
    }
    /**
   * Calculate setup grade
   */ calculateSetupGrade(score) {
        if (score >= 95) return 'A+';
        if (score >= 90) return 'A';
        if (score >= 85) return 'B+';
        if (score >= 80) return 'B';
        if (score >= 75) return 'C+';
        if (score >= 70) return 'C';
        if (score >= 60) return 'D';
        return 'F';
    }
    /**
   * Validate complete Perfect-Pick setup
   */ validatePerfectPickSetup(setup) {
        const checks = setup.validationChecks;
        // Must pass all critical checks
        const criticalChecks = [
            checks.hasValidCatalyst,
            checks.meetsGapCriteria,
            checks.passesTechnicalGate,
            checks.meetsRiskReward,
            checks.noExclusionFlags
        ];
        return criticalChecks.every((check)=>check) && setup.overallScore >= 70;
    }
    /**
   * Get setup summary statistics
   */ getSetupSummary(setups) {
        const totalSetups = setups.length;
        const gradeBreakdown = setups.reduce((acc, setup)=>{
            acc[setup.setupGrade] = (acc[setup.setupGrade] || 0) + 1;
            return acc;
        }, {});
        const catalystBreakdown = setups.reduce((acc, setup)=>{
            acc[setup.catalyst.type] = (acc[setup.catalyst.type] || 0) + 1;
            return acc;
        }, {});
        const avgScore = totalSetups > 0 ? setups.reduce((sum, setup)=>sum + setup.overallScore, 0) / totalSetups : 0;
        const avgGap = totalSetups > 0 ? setups.reduce((sum, setup)=>sum + setup.gapScan.gapPercent, 0) / totalSetups : 0;
        return {
            totalSetups,
            avgScore: Math.round(avgScore * 100) / 100,
            avgGap: Math.round(avgGap * 100) / 100,
            gradeBreakdown,
            catalystBreakdown,
            generatedAt: new Date().toISOString()
        };
    }
    /**
   * Update existing setups with current market data
   */ async updatePerfectPickSetups(setups) {
        const updatedSetups = [];
        for (const setup of setups){
            try {
                // Get current quote
                const currentQuote = await this.fmpAPI.getStockQuote(setup.symbol);
                const currentPrice = currentQuote.price;
                // Update entry trigger if needed
                const entryTrigger = await this.generateEntryTrigger(setup.symbol, setup.gapScan.preMarketHigh);
                // Update the setup
                const updatedSetup = {
                    ...setup,
                    entryTrigger,
                    riskManagement: {
                        ...setup.riskManagement,
                        entryPrice: currentPrice
                    },
                    updatedAt: new Date().toISOString()
                };
                updatedSetups.push(updatedSetup);
            } catch (error) {
                console.error(`Error updating setup for ${setup.symbol}:`, error);
                updatedSetups.push(setup); // Keep original if update fails
            }
        }
        return updatedSetups;
    }
}
}),
"[project]/swing-trader-ai/src/app/api/scanner/perfect-pick/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "GET",
    ()=>GET,
    "POST",
    ()=>POST
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$perfectPickTradingSystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/src/lib/perfectPickTradingSystem.ts [app-route] (ecmascript)");
;
;
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const accountSize = parseInt(searchParams.get('accountSize') || '100000');
        const riskPercent = parseFloat(searchParams.get('riskPercent') || '2');
        const customUniverse = searchParams.get('universe')?.split(',').filter(Boolean);
        const limit = parseInt(searchParams.get('limit') || '20');
        const dataSource = searchParams.get('dataSource') || 'IBKR';
        const useIBKR = dataSource === 'IBKR';
        console.log('🎯 Perfect-Pick API called with params:', {
            accountSize,
            riskPercent,
            customUniverse: customUniverse?.length || 'default',
            limit,
            dataSource,
            useIBKR
        });
        // Initialize Perfect-Pick Trading System with data source preference
        const perfectPickSystem = new __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$perfectPickTradingSystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PerfectPickTradingSystem"](process.env.POLYGON_API_KEY, useIBKR);
        // Run the complete Perfect-Pick scan
        const setups = await perfectPickSystem.runPerfectPickScan(accountSize, riskPercent, customUniverse);
        // Limit results
        const limitedSetups = setups.slice(0, limit);
        // Get summary statistics
        const summary = perfectPickSystem.getSetupSummary(limitedSetups);
        const response = {
            success: true,
            data: {
                setups: limitedSetups,
                summary,
                scanParams: {
                    accountSize,
                    riskPercent,
                    universeSize: customUniverse?.length || 'default',
                    limit
                },
                timestamp: new Date().toISOString()
            }
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(response);
    } catch (error) {
        console.error('Error in Perfect-Pick API:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to run Perfect-Pick scan',
            details: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const body = await request.json();
        const { action, data } = body;
        const perfectPickSystem = new __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$perfectPickTradingSystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PerfectPickTradingSystem"](process.env.FMP_API_KEY, process.env.POLYGON_API_KEY);
        switch(action){
            case 'update_setups':
                const updatedSetups = await perfectPickSystem.updatePerfectPickSetups(data.setups);
                return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    data: {
                        setups: updatedSetups
                    }
                });
            case 'generate_entry_trigger':
                const entryTrigger = await perfectPickSystem.generateEntryTrigger(data.symbol, data.preMarketHigh);
                return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    data: {
                        entryTrigger
                    }
                });
            default:
                return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: false,
                    error: 'Invalid action'
                }, {
                    status: 400
                });
        }
    } catch (error) {
        console.error('Error in Perfect-Pick POST API:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to process Perfect-Pick request',
            details: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__cad37136._.js.map