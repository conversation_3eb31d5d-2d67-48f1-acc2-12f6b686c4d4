module.exports=[18230,e=>{"use strict";e.s(["swingScanner",()=>o]);var t=e.i(17673),a=e.i(78006),n=e.i(29547),r=e.i(86678),i=e.i(97669);let o=new class{fmpAPI;polygonAPI;constructor(){this.fmpAPI=new n.FMPAPI(process.env.FMP_API_KEY),this.polygonAPI=new a.PolygonAPI(process.env.POLYGON_API_KEY)}async scanStocks(e,t=5){let a=Date.now(),n=[],r=[],i={};console.log(`Starting scan of ${e.length} stocks...`);for(let a=0;a<e.length;a+=t){let o=e.slice(a,a+t),s=o.map(e=>this.scanSingleStock(e));(await Promise.allSettled(s)).forEach((e,t)=>{let a=o[t];if("fulfilled"===e.status&&e.value){n.push(e.value);let t=e.value.sector;i[t]=(i[t]||0)+1}else r.push(a),console.warn(`Failed to scan ${a}:`,"rejected"===e.status?e.reason:"Unknown error")}),a+t<e.length&&await new Promise(e=>setTimeout(e,1e3))}n.sort((e,t)=>t.score-e.score),n.forEach((e,t)=>{e.rank=t+1});let o=Date.now()-a;return{totalScanned:e.length,successfulScans:n.length,failedScans:r.length,topOpportunities:n.slice(0,20),sectorBreakdown:i,scanDuration:o}}async scanSingleStock(e){try{let[a,n]=await Promise.all([this.fmpAPI.getStockQuote(e),this.getHistoricalData(e)]);if(!n||n.length<50)throw Error("Insufficient historical data");let r=t.SwingTradingAnalyzer.analyzeSwingTrade(e,n),i=this.calculateSwingScore(a,r);return{symbol:e,name:a.name,sector:this.getSectorForSymbol(e),quote:a,analysis:r,score:i,rank:0,scanTime:new Date().toISOString()}}catch(t){return console.error(`Error scanning ${e}:`,t),null}}async getHistoricalData(e){let t=(0,r.format)(new Date,"yyyy-MM-dd"),a=(0,r.format)((0,i.subDays)(new Date,100),"yyyy-MM-dd");try{return await this.polygonAPI.getHistoricalData(e,"day",1,a,t)}catch(t){throw console.warn(`Polygon failed for ${e}, trying alternative...`),t}}calculateSwingScore(e,t){let a;a=0+.4*t.confidence;let n=t.riskRewardRatio;n>=3?a+=20:n>=2?a+=15:n>=1.5?a+=10:n>=1&&(a+=5);let r=t.indicators.find(e=>"Volume"===e.name);r&&("BUY"===r.signal&&r.value>1.5?a+=15:"BUY"===r.signal?a+=10:"NEUTRAL"===r.signal&&(a+=5)),"BULLISH"===t.trend?a+=15:"BEARISH"===t.trend?a+=10:a+=5;let i=t.indicators.filter(e=>"BUY"===e.signal).length,o=t.indicators.filter(e=>"SELL"===e.signal).length,s=t.indicators.length;i>o?a+=i/s*10:o>i&&(a+=o/s*8);let l=Math.abs(e.changePercent);switch(l>5?a+=10:l>2?a+=7:l>1?a+=5:l<.5&&(a-=5),t.recommendation){case"STRONG_BUY":a+=10;break;case"BUY":a+=7;break;case"STRONG_SELL":a+=8;break;case"SELL":a+=5;break;case"NO_TRADE":a-=10}return Math.max(0,Math.min(100,a))}getSectorForSymbol(e){return["MSFT","NVDA","GOOG","GOOGL","META","AVGO","TSM","ORCL","CSCO","AMD","ASML","MU","LRCX","PLTR","APP","NET","DDOG","ZS","SHOP","SOUN","IONQ","RGTI","RIOT","HUT","IREN","ASTS","NBIS"].includes(e)?"Technology":["JPM","BAC","MS","SCHW","C","HOOD","SOFI","TIGR","FUTU"].includes(e)?"Financial Services":["JNJ","ABBV","MRK","GILD"].includes(e)?"Healthcare":["GE","CAT","BA","GEV","UAL","VRT","RKLB"].includes(e)?"Industrial":["AEM","NEM","PAAS","BTG","HL","MP","AG"].includes(e)?"Materials":["AMZN","DIS","SBUX","MO","DASH","GM","NCLH","CELH","LEVI","ELF","ETSY","W"].includes(e)?"Consumer":["NFLX","RBLX","BILI"].includes(e)?"Communication Services":["CEG","VST","CCJ"].includes(e)?"Energy":"Other"}async quickScan(e){return(await this.scanStocks(e,8)).topOpportunities}}},5149,(e,t,a)=>{},67753,e=>{"use strict";e.s(["handler",()=>C,"patchFetch",()=>T,"routeModule",()=>y,"serverHooks",()=>P,"workAsyncStorage",()=>v,"workUnitAsyncStorage",()=>O],67753);var t=e.i(11971),a=e.i(6780),n=e.i(51842),r=e.i(62950),i=e.i(21346),o=e.i(30506),s=e.i(63077),l=e.i(34765),c=e.i(64182),u=e.i(85062),d=e.i(51548),p=e.i(95133),h=e.i(8819),g=e.i(41050),R=e.i(93695);e.i(96641);var S=e.i(3893);e.s(["GET",()=>f],14449);var E=e.i(59169),w=e.i(18230),m=e.i(5744);async function f(e){try{let{searchParams:t}=new URL(e.url),a=parseInt(t.get("limit")||"10");console.log(`Starting quick scan of ${m.PRIORITY_SYMBOLS.length} priority stocks...`);let n=(await w.swingScanner.quickScan(m.PRIORITY_SYMBOLS)).slice(0,a);return E.NextResponse.json({totalScanned:m.PRIORITY_SYMBOLS.length,results:n,scanTime:new Date().toISOString()})}catch(e){return console.error("Error in quick scanner API:",e),E.NextResponse.json({error:"Failed to perform quick stock scan"},{status:500})}}var A=e.i(14449);let y=new t.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/scanner/quick/route",pathname:"/api/scanner/quick",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/swing-trader-ai/src/app/api/scanner/quick/route.ts",nextConfigOutput:"",userland:A}),{workAsyncStorage:v,workUnitAsyncStorage:O,serverHooks:P}=y;function T(){return(0,n.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:O})}async function C(e,t,n){var E;let w="/api/scanner/quick/route";w=w.replace(/\/index$/,"")||"/";let m=await y.prepare(e,t,{srcPage:w,multiZoneDraftMode:!1});if(!m)return t.statusCode=400,t.end("Bad Request"),null==n.waitUntil||n.waitUntil.call(n,Promise.resolve()),null;let{buildId:f,params:A,nextConfig:v,isDraftMode:O,prerenderManifest:P,routerServerContext:T,isOnDemandRevalidate:C,revalidateOnlyGenerated:I,resolvedPathname:M}=m,N=(0,o.normalizeAppPath)(w),k=!!(P.dynamicRoutes[N]||P.routes[M]);if(k&&!O){let e=!!P.routes[M],t=P.dynamicRoutes[N];if(t&&!1===t.fallback&&!e)throw new R.NoFallbackError}let x=null;!k||y.isDev||O||(x="/index"===(x=M)?"/":x);let L=!0===y.isDev||!k,U=k&&!L,D=e.method||"GET",H=(0,i.getTracer)(),_=H.getActiveScopeSpan(),b={params:A,prerenderManifest:P,renderOpts:{experimental:{cacheComponents:!!v.experimental.cacheComponents,authInterrupts:!!v.experimental.authInterrupts},supportsDynamicResponse:L,incrementalCache:(0,r.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(E=v.experimental)?void 0:E.cacheLife,isRevalidate:U,waitUntil:n.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,a,n)=>y.onRequestError(e,t,n,T)},sharedContext:{buildId:f}},B=new s.NodeNextRequest(e),q=new s.NodeNextResponse(t),G=l.NextRequestAdapter.fromNodeNextRequest(B,(0,l.signalFromNodeResponse)(t));try{let o=async a=>y.handle(G,b).finally(()=>{if(!a)return;a.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let n=H.getRootSpanAttributes();if(!n)return;if(n.get("next.span_type")!==c.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${n.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let r=n.get("next.route");if(r){let e=`${D} ${r}`;a.setAttributes({"next.route":r,"http.route":r,"next.span_name":e}),a.updateName(e)}else a.updateName(`${D} ${e.url}`)}),s=async i=>{var s,l;let c=async({previousCacheEntry:a})=>{try{if(!(0,r.getRequestMeta)(e,"minimalMode")&&C&&I&&!a)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let s=await o(i);e.fetchMetrics=b.renderOpts.fetchMetrics;let l=b.renderOpts.pendingWaitUntil;l&&n.waitUntil&&(n.waitUntil(l),l=void 0);let c=b.renderOpts.collectedTags;if(!k)return await (0,d.sendResponse)(B,q,s,b.renderOpts.pendingWaitUntil),null;{let e=await s.blob(),t=(0,p.toNodeOutgoingHttpHeaders)(s.headers);c&&(t[g.NEXT_CACHE_TAGS_HEADER]=c),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let a=void 0!==b.renderOpts.collectedRevalidate&&!(b.renderOpts.collectedRevalidate>=g.INFINITE_CACHE)&&b.renderOpts.collectedRevalidate,n=void 0===b.renderOpts.collectedExpire||b.renderOpts.collectedExpire>=g.INFINITE_CACHE?void 0:b.renderOpts.collectedExpire;return{value:{kind:S.CachedRouteKind.APP_ROUTE,status:s.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:a,expire:n}}}}catch(t){throw(null==a?void 0:a.isStale)&&await y.onRequestError(e,t,{routerKind:"App Router",routePath:w,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:U,isOnDemandRevalidate:C})},T),t}},R=await y.handleResponse({req:e,nextConfig:v,cacheKey:x,routeKind:a.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:P,isRoutePPREnabled:!1,isOnDemandRevalidate:C,revalidateOnlyGenerated:I,responseGenerator:c,waitUntil:n.waitUntil});if(!k)return null;if((null==R||null==(s=R.value)?void 0:s.kind)!==S.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==R||null==(l=R.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,r.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",C?"REVALIDATED":R.isMiss?"MISS":R.isStale?"STALE":"HIT"),O&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let E=(0,p.fromNodeOutgoingHttpHeaders)(R.value.headers);return(0,r.getRequestMeta)(e,"minimalMode")&&k||E.delete(g.NEXT_CACHE_TAGS_HEADER),!R.cacheControl||t.getHeader("Cache-Control")||E.get("Cache-Control")||E.set("Cache-Control",(0,h.getCacheControlHeader)(R.cacheControl)),await (0,d.sendResponse)(B,q,new Response(R.value.body,{headers:E,status:R.value.status||200})),null};_?await s(_):await H.withPropagatedContext(e.headers,()=>H.trace(c.BaseServerSpan.handleRequest,{spanName:`${D} ${e.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":D,"http.target":e.url}},s))}catch(t){if(t instanceof R.NoFallbackError||await y.onRequestError(e,t,{routerKind:"App Router",routePath:N,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:U,isOnDemandRevalidate:C})}),k)throw t;return await (0,d.sendResponse)(B,q,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=swing-trader-ai_a2ddbfa8._.js.map