# SwingTrader AI - Data Architecture

## 🎯 **New Optimized Data Sources**

The system has been completely updated to use only **premium, paid data sources** for maximum reliability and performance:

### **Primary Data Sources:**
1. **Interactive Brokers (IBKR)** - Real-time trading data
2. **Polygon.io** - Historical data and technical analysis

### **Removed:**
- ❌ **Financial Modeling Prep (FMP)** - Eliminated due to rate limiting issues

---

## 📊 **Data Source Responsibilities**

### **IBKR (Interactive Brokers)**
**Purpose:** Real-time market data and trading execution

**Provides:**
- ✅ **Real-time quotes** - Live bid/ask, last price, volume
- ✅ **Pre-market data** - Gap scanning and early opportunity detection
- ✅ **Extended hours data** - After-hours trading information
- ✅ **Direct trade execution** - Live order placement and management
- ✅ **Portfolio data** - Real positions, P&L, account balances
- ✅ **No rate limits** - Professional-grade API access

**Used By:**
- Pre-market gap scanner (primary data source)
- Real-time quote display
- Trade execution system
- Portfolio management

### **Polygon.io**
**Purpose:** Historical data and technical analysis

**Provides:**
- ✅ **Historical OHLCV data** - Daily, hourly, minute-level candles
- ✅ **Technical indicators** - SMA, EMA, RSI, MACD calculations
- ✅ **Fundamental data** - Company information, financials
- ✅ **News and events** - Catalyst detection data
- ✅ **High-quality data** - Professional-grade accuracy
- ✅ **Generous rate limits** - Suitable for batch processing

**Used By:**
- Technical gate analysis
- Historical backtesting
- Catalyst detection engine
- Strategy pattern recognition
- Fallback for real-time quotes (when IBKR unavailable)

---

## 🔄 **Data Flow Architecture**

### **Event-Driven Scanner Pipeline:**

```
1. IBKR Connection Check
   ├── ✅ Connected → Use IBKR for real-time data
   └── ❌ Disconnected → Fallback to Polygon.io

2. Pre-Market Gap Scanning
   ├── IBKR: Real-time pre-market quotes
   └── Polygon: Fallback quotes + historical context

3. Technical Analysis
   └── Polygon: Historical data + indicator calculations

4. Catalyst Detection
   └── Polygon: News, events, fundamental changes

5. Perfect-Pick Generation
   ├── IBKR: Current market conditions
   └── Polygon: Historical patterns + technical setup
```

### **Data Source Priority:**
1. **IBKR** (Primary) - Real-time market data
2. **Polygon** (Secondary) - Historical data + fallback quotes
3. **Graceful degradation** - System continues with available sources

---

## 🛠 **Technical Implementation**

### **Updated Components:**

#### **PreMarketGapScanner**
```typescript
constructor(polygonApiKey?: string, useIBKR: boolean = true)
// Removed: fmpApiKey parameter
// Added: Polygon fallback for quotes
```

#### **CatalystDetectionEngine**
```typescript
constructor(polygonApiKey?: string)
// Removed: fmpApiKey parameter
// Uses: Polygon for news and fundamental data
```

#### **TechnicalGateAnalysis**
```typescript
constructor(polygonApiKey?: string)
// Removed: fmpApiKey parameter
// Uses: Polygon for all historical data
```

#### **PerfectPickTradingSystem**
```typescript
constructor(polygonApiKey?: string, useIBKR: boolean = true)
// Removed: fmpApiKey parameter
// Simplified: Two data sources only
```

### **API Endpoints Updated:**
- `/api/data-source` - Now checks IBKR + Polygon (removed FMP)
- `/api/scanner/perfect-pick` - Uses new data architecture
- All scanner endpoints - Updated to use Polygon fallback

### **UI Components Updated:**
- `DataSourceSelector` - Shows IBKR + Polygon status
- `EventDrivenScanner` - Uses new data source selection
- All trading cards - Display data from optimal sources

---

## 🎯 **Benefits of New Architecture**

### **Reliability:**
- ✅ **No more rate limiting** - Both IBKR and Polygon have generous limits
- ✅ **Professional data quality** - Institutional-grade accuracy
- ✅ **Redundancy** - Fallback system ensures continuous operation

### **Performance:**
- ✅ **Real-time data** - IBKR provides live market feeds
- ✅ **Fast historical queries** - Polygon optimized for bulk data
- ✅ **Reduced API calls** - Efficient batching and caching

### **Features:**
- ✅ **Pre-market scanning** - IBKR provides extended hours data
- ✅ **Direct trading** - IBKR enables live order execution
- ✅ **Advanced analytics** - Polygon supports complex technical analysis

### **Cost Efficiency:**
- ✅ **Paid subscriptions utilized** - Maximizes value from existing services
- ✅ **No wasted API calls** - Eliminates FMP rate limit issues
- ✅ **Scalable architecture** - Supports increased usage

---

## 🔧 **Configuration**

### **Environment Variables:**
```bash
# Required
POLYGON_API_KEY=your_polygon_api_key

# IBKR Configuration (automatic detection)
# Requires TWS/IB Gateway running locally
# Default ports: 4002 (paper), 4001 (live)
```

### **IBKR Setup:**
1. Install TWS or IB Gateway
2. Enable API connections
3. Set socket port (4002 for paper trading)
4. Add 127.0.0.1 to trusted IPs
5. System auto-detects connection

### **Polygon Setup:**
- API key configured in environment
- Automatic rate limit handling
- Batch processing optimization

---

## 📈 **Usage Examples**

### **Check Data Source Status:**
```typescript
// GET /api/data-source?action=status
{
  "ibkr": { "connected": true, "source": "IBKR" },
  "polygon": { "connected": true, "source": "Polygon" },
  "recommended": "IBKR"
}
```

### **Run Event-Driven Scan:**
```typescript
// Uses IBKR for real-time data, Polygon for historical analysis
const results = await fetch('/api/scanner/perfect-pick?dataSource=IBKR')
```

### **Fallback Behavior:**
```typescript
// If IBKR disconnected, automatically uses Polygon
// Graceful degradation with user notification
```

---

## 🚀 **Next Steps**

1. **Test IBKR Connection** - Verify TWS/Gateway setup
2. **Validate Polygon Data** - Confirm API key and limits
3. **Run Complete Scan** - Test end-to-end pipeline
4. **Monitor Performance** - Track data quality and speed
5. **Enable Live Trading** - Connect IBKR for order execution

The new architecture provides a robust, scalable foundation for professional swing trading operations! 🎯
