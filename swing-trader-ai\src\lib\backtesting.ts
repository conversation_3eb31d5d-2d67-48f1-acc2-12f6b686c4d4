import { PerfectPickSetup, CandlestickData, Catalyst } from '@/types/trading'

export interface BacktestResult {
  symbol: string
  entryDate: string
  entryPrice: number
  exitDate?: string
  exitPrice?: number
  stopLoss: number
  targets: number[]
  outcome: 'win' | 'loss' | 'breakeven' | 'open'
  rMultiple: number // Actual R achieved
  holdingDays: number
  maxDrawdown: number
  maxRunup: number
  exitReason: 'stop_loss' | 'target_hit' | 'time_exit' | 'manual' | 'still_open'
  catalystType: string
  catalystQuality: number
  setupGrade: string
  technicalScore: number
}

export interface BacktestSummary {
  totalTrades: number
  winRate: number
  avgRMultiple: number
  avgHoldingDays: number
  maxDrawdown: number
  totalReturn: number
  sharpeRatio: number
  profitFactor: number
  largestWin: number
  largestLoss: number
  consecutiveWins: number
  consecutiveLosses: number
  bySetupGrade: Record<string, BacktestSummary>
  byCatalystType: Record<string, BacktestSummary>
  monthlyReturns: { month: string; return: number }[]
}

export class PerfectPickBacktester {
  private results: BacktestResult[] = []

  /**
   * Backtest a Perfect-Pick setup using historical data
   */
  async backtestSetup(
    setup: PerfectPickSetup,
    historicalData: CandlestickData[],
    maxHoldingDays: number = 30
  ): Promise<BacktestResult> {
    const entryPrice = setup.riskManagement.entryPrice
    const stopLoss = setup.riskManagement.stopLoss
    const targets = [
      setup.rewardPlanning.target3R,
      setup.rewardPlanning.target4R,
      setup.rewardPlanning.target5R
    ]

    // Find entry date in historical data
    const entryIndex = historicalData.findIndex(candle => 
      Math.abs(candle.close - entryPrice) / entryPrice < 0.02 // Within 2% of entry price
    )

    if (entryIndex === -1) {
      throw new Error('Entry price not found in historical data')
    }

    const entryCandle = historicalData[entryIndex]
    let exitPrice: number | undefined
    let exitDate: string | undefined
    let exitReason: BacktestResult['exitReason'] = 'still_open'
    let outcome: BacktestResult['outcome'] = 'open'
    let rMultiple = 0
    let maxDrawdown = 0
    let maxRunup = 0
    let holdingDays = 0

    // Simulate trade execution from entry point
    for (let i = entryIndex + 1; i < Math.min(entryIndex + maxHoldingDays, historicalData.length); i++) {
      const candle = historicalData[i]
      holdingDays = i - entryIndex

      // Calculate current P&L
      const currentPL = (candle.close - entryPrice) / (entryPrice - stopLoss)
      maxRunup = Math.max(maxRunup, currentPL)
      maxDrawdown = Math.min(maxDrawdown, currentPL)

      // Check for stop loss hit
      if (candle.low <= stopLoss) {
        exitPrice = stopLoss
        exitDate = new Date(candle.timestamp).toISOString()
        exitReason = 'stop_loss'
        outcome = 'loss'
        rMultiple = -1
        break
      }

      // Check for target hits (in order)
      for (let j = 0; j < targets.length; j++) {
        if (candle.high >= targets[j]) {
          exitPrice = targets[j]
          exitDate = new Date(candle.timestamp).toISOString()
          exitReason = 'target_hit'
          outcome = 'win'
          rMultiple = j + 3 // 3R, 4R, or 5R
          break
        }
      }

      if (exitPrice) break
    }

    // If no exit found, use time-based exit
    if (!exitPrice && holdingDays >= maxHoldingDays) {
      const lastCandle = historicalData[Math.min(entryIndex + maxHoldingDays, historicalData.length - 1)]
      exitPrice = lastCandle.close
      exitDate = new Date(lastCandle.timestamp).toISOString()
      exitReason = 'time_exit'
      rMultiple = (exitPrice - entryPrice) / (entryPrice - stopLoss)
      outcome = rMultiple > 0 ? 'win' : rMultiple < 0 ? 'loss' : 'breakeven'
    }

    const result: BacktestResult = {
      symbol: setup.symbol,
      entryDate: new Date(entryCandle.timestamp).toISOString(),
      entryPrice,
      exitDate,
      exitPrice,
      stopLoss,
      targets,
      outcome,
      rMultiple,
      holdingDays,
      maxDrawdown,
      maxRunup,
      exitReason,
      catalystType: setup.catalyst.type,
      catalystQuality: setup.catalyst.qualityScore,
      setupGrade: setup.setupGrade,
      technicalScore: setup.technicalGate.gateScore
    }

    this.results.push(result)
    return result
  }

  /**
   * Batch backtest multiple setups
   */
  async backtestMultipleSetups(
    setups: PerfectPickSetup[],
    getHistoricalData: (symbol: string) => Promise<CandlestickData[]>,
    maxHoldingDays: number = 30
  ): Promise<BacktestResult[]> {
    const results: BacktestResult[] = []

    for (const setup of setups) {
      try {
        const historicalData = await getHistoricalData(setup.symbol)
        const result = await this.backtestSetup(setup, historicalData, maxHoldingDays)
        results.push(result)
      } catch (error) {
        console.error(`Error backtesting ${setup.symbol}:`, error)
      }
    }

    return results
  }

  /**
   * Generate comprehensive backtest summary
   */
  generateSummary(results: BacktestResult[]): BacktestSummary {
    if (results.length === 0) {
      return this.getEmptySummary()
    }

    const completedTrades = results.filter(r => r.outcome !== 'open')
    const wins = completedTrades.filter(r => r.outcome === 'win')
    const losses = completedTrades.filter(r => r.outcome === 'loss')

    const winRate = completedTrades.length > 0 ? wins.length / completedTrades.length : 0
    const avgRMultiple = completedTrades.length > 0 
      ? completedTrades.reduce((sum, r) => sum + r.rMultiple, 0) / completedTrades.length 
      : 0

    const avgHoldingDays = completedTrades.length > 0
      ? completedTrades.reduce((sum, r) => sum + r.holdingDays, 0) / completedTrades.length
      : 0

    const totalReturn = completedTrades.reduce((sum, r) => sum + r.rMultiple, 0)
    const maxDrawdown = Math.min(...results.map(r => r.maxDrawdown))

    // Calculate Profit Factor
    const grossProfit = wins.reduce((sum, r) => sum + r.rMultiple, 0)
    const grossLoss = Math.abs(losses.reduce((sum, r) => sum + r.rMultiple, 0))
    const profitFactor = grossLoss > 0 ? grossProfit / grossLoss : grossProfit > 0 ? Infinity : 0

    // Calculate Sharpe Ratio (simplified)
    const returns = completedTrades.map(r => r.rMultiple)
    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length
    const stdDev = Math.sqrt(returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length)
    const sharpeRatio = stdDev > 0 ? avgReturn / stdDev : 0

    // Find largest win/loss
    const largestWin = Math.max(...completedTrades.map(r => r.rMultiple))
    const largestLoss = Math.min(...completedTrades.map(r => r.rMultiple))

    // Calculate consecutive wins/losses
    const { consecutiveWins, consecutiveLosses } = this.calculateConsecutiveWinsLosses(completedTrades)

    // Group by setup grade
    const bySetupGrade = this.groupResultsByField(completedTrades, 'setupGrade')

    // Group by catalyst type
    const byCatalystType = this.groupResultsByField(completedTrades, 'catalystType')

    // Generate monthly returns (simplified)
    const monthlyReturns = this.calculateMonthlyReturns(completedTrades)

    return {
      totalTrades: completedTrades.length,
      winRate,
      avgRMultiple,
      avgHoldingDays,
      maxDrawdown,
      totalReturn,
      sharpeRatio,
      profitFactor,
      largestWin,
      largestLoss,
      consecutiveWins,
      consecutiveLosses,
      bySetupGrade,
      byCatalystType,
      monthlyReturns
    }
  }

  /**
   * Get performance by catalyst tier
   */
  getPerformanceByCatalystTier(results: BacktestResult[]): Record<string, BacktestSummary> {
    const tier1Types = ['earnings_beat_guidance', 'fda_approval', 'merger_acquisition']
    const tier2Types = ['analyst_upgrade', 'stock_split', 'partnership']
    
    const tier1Results = results.filter(r => tier1Types.includes(r.catalystType))
    const tier2Results = results.filter(r => tier2Types.includes(r.catalystType))
    const tier3Results = results.filter(r => !tier1Types.includes(r.catalystType) && !tier2Types.includes(r.catalystType))

    return {
      'Tier 1': this.generateSummary(tier1Results),
      'Tier 2': this.generateSummary(tier2Results),
      'Tier 3': this.generateSummary(tier3Results)
    }
  }

  /**
   * Get performance by technical grade
   */
  getPerformanceByTechnicalGrade(results: BacktestResult[]): Record<string, BacktestSummary> {
    const gradeA = results.filter(r => r.setupGrade.startsWith('A'))
    const gradeB = results.filter(r => r.setupGrade.startsWith('B'))
    const gradeC = results.filter(r => r.setupGrade.startsWith('C'))

    return {
      'Grade A': this.generateSummary(gradeA),
      'Grade B': this.generateSummary(gradeB),
      'Grade C': this.generateSummary(gradeC)
    }
  }

  private getEmptySummary(): BacktestSummary {
    return {
      totalTrades: 0,
      winRate: 0,
      avgRMultiple: 0,
      avgHoldingDays: 0,
      maxDrawdown: 0,
      totalReturn: 0,
      sharpeRatio: 0,
      profitFactor: 0,
      largestWin: 0,
      largestLoss: 0,
      consecutiveWins: 0,
      consecutiveLosses: 0,
      bySetupGrade: {},
      byCatalystType: {},
      monthlyReturns: []
    }
  }

  private calculateConsecutiveWinsLosses(results: BacktestResult[]): { consecutiveWins: number; consecutiveLosses: number } {
    let maxConsecutiveWins = 0
    let maxConsecutiveLosses = 0
    let currentWinStreak = 0
    let currentLossStreak = 0

    for (const result of results) {
      if (result.outcome === 'win') {
        currentWinStreak++
        currentLossStreak = 0
        maxConsecutiveWins = Math.max(maxConsecutiveWins, currentWinStreak)
      } else if (result.outcome === 'loss') {
        currentLossStreak++
        currentWinStreak = 0
        maxConsecutiveLosses = Math.max(maxConsecutiveLosses, currentLossStreak)
      }
    }

    return {
      consecutiveWins: maxConsecutiveWins,
      consecutiveLosses: maxConsecutiveLosses
    }
  }

  private groupResultsByField(results: BacktestResult[], field: keyof BacktestResult): Record<string, BacktestSummary> {
    const grouped = results.reduce((acc, result) => {
      const key = String(result[field])
      if (!acc[key]) acc[key] = []
      acc[key].push(result)
      return acc
    }, {} as Record<string, BacktestResult[]>)

    const summaries: Record<string, BacktestSummary> = {}
    for (const [key, groupResults] of Object.entries(grouped)) {
      summaries[key] = this.generateSummary(groupResults)
    }

    return summaries
  }

  private calculateMonthlyReturns(results: BacktestResult[]): { month: string; return: number }[] {
    const monthlyData = results.reduce((acc, result) => {
      const month = new Date(result.entryDate).toISOString().slice(0, 7) // YYYY-MM
      if (!acc[month]) acc[month] = 0
      acc[month] += result.rMultiple
      return acc
    }, {} as Record<string, number>)

    return Object.entries(monthlyData)
      .map(([month, returnValue]) => ({ month, return: returnValue }))
      .sort((a, b) => a.month.localeCompare(b.month))
  }

  /**
   * Export results to CSV format
   */
  exportToCSV(results: BacktestResult[]): string {
    const headers = [
      'Symbol', 'Entry Date', 'Entry Price', 'Exit Date', 'Exit Price',
      'Stop Loss', 'Outcome', 'R Multiple', 'Holding Days', 'Exit Reason',
      'Catalyst Type', 'Catalyst Quality', 'Setup Grade', 'Technical Score'
    ]

    const rows = results.map(result => [
      result.symbol,
      result.entryDate,
      result.entryPrice.toFixed(2),
      result.exitDate || '',
      result.exitPrice?.toFixed(2) || '',
      result.stopLoss.toFixed(2),
      result.outcome,
      result.rMultiple.toFixed(2),
      result.holdingDays.toString(),
      result.exitReason,
      result.catalystType,
      result.catalystQuality.toString(),
      result.setupGrade,
      result.technicalScore.toString()
    ])

    return [headers, ...rows].map(row => row.join(',')).join('\n')
  }
}
