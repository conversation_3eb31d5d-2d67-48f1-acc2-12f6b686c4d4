module.exports=[18622,(e,r,t)=>{r.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,r,t)=>{r.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},70406,(e,r,t)=>{r.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,r,t)=>{r.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},24361,(e,r,t)=>{r.exports=e.x("util",()=>require("util"))},32319,(e,r,t)=>{r.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},24725,(e,r,t)=>{r.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},20635,(e,r,t)=>{r.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},14747,(e,r,t)=>{r.exports=e.x("path",()=>require("path"))},78006,e=>{"use strict";e.s(["PolygonAPI",()=>s]);var r=e.i(55362);let t="https://api.polygon.io",a=process.env.POLYGON_API_KEY;class s{apiKey;constructor(e){if(this.apiKey=e||a||"",!this.apiKey)throw Error("Polygon API key is required")}async getStockQuote(e){try{let a=(await r.default.get(`${t}/v2/snapshot/locale/us/markets/stocks/tickers/${e}`,{params:{apikey:this.apiKey}})).data.results[0];if(!a)throw Error(`No data found for ${e}`);let s=a.value||a,o=s.day||{},n=s.prevDay||{};s.lastQuote;let i=(s.lastTrade||{}).p||o.c||n.c,p=n.c||o.o,c=i-p;return{symbol:e.toUpperCase(),name:s.name||e.toUpperCase(),price:i,change:c,changePercent:c/p*100,volume:o.v||0,marketCap:s.market_cap,pe:void 0,dividend:void 0}}catch(a){console.error("Error fetching stock quote from Polygon:",a);try{let a=(await r.default.get(`${t}/v2/aggs/ticker/${e}/prev`,{params:{adjusted:"true",apikey:this.apiKey}})).data.results[0];return{symbol:e.toUpperCase(),name:e.toUpperCase(),price:a.c,change:a.c-a.o,changePercent:(a.c-a.o)/a.o*100,volume:a.v,marketCap:void 0,pe:void 0,dividend:void 0}}catch(r){throw console.error("Polygon fallback also failed:",r),Error(`Failed to fetch quote for ${e}`)}}}async getHistoricalData(e,a="day",s=1,o,n){try{let i=await r.default.get(`${t}/v2/aggs/ticker/${e}/range/${s}/${a}/${o}/${n}`,{params:{adjusted:"true",sort:"asc",limit:5e4,apikey:this.apiKey}});if(!i.data.results||0===i.data.results.length)return console.warn(`No historical data found for ${e}`),[];return i.data.results.map(e=>({timestamp:e.t,open:e.o,high:e.h,low:e.l,close:e.c,volume:e.v}))}catch(r){throw console.error(`Error fetching historical data for ${e}:`,r),r.response&&(console.error(`Polygon API Error: ${r.response.status} - ${r.response.statusText}`),console.error("Response data:",r.response.data)),Error(`Failed to fetch historical data for ${e}: ${r.message}`)}}async getCompanyDetails(e){try{return(await r.default.get(`${t}/v3/reference/tickers/${e}`,{params:{apikey:this.apiKey}})).data.results}catch(e){return console.error("Error fetching company details:",e),null}}async getMarketStatus(){try{return(await r.default.get(`${t}/v1/marketstatus/now`,{params:{apikey:this.apiKey}})).data}catch(e){return console.error("Error fetching market status:",e),null}}async searchStocks(e,a=10){try{return(await r.default.get(`${t}/v3/reference/tickers`,{params:{search:e,market:"stocks",active:"true",limit:a,apikey:this.apiKey}})).data.results||[]}catch(e){return console.error("Error searching stocks:",e),[]}}}new s},36894,(e,r,t)=>{}];

//# sourceMappingURL=%5Broot-of-the-server%5D__860a08df._.js.map