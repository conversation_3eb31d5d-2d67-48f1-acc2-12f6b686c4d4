module.exports=[18230,e=>{"use strict";e.s(["swingScanner",()=>i]);var t=e.i(17673),n=e.i(78006),a=e.i(29547),r=e.i(86678),o=e.i(97669);let i=new class{fmpAPI;polygonAPI;constructor(){this.fmpAPI=new a.FMPAPI(process.env.FMP_API_KEY),this.polygonAPI=new n.PolygonAPI(process.env.POLYGON_API_KEY)}async scanStocks(e,t=5){let n=Date.now(),a=[],r=[],o={};console.log(`Starting scan of ${e.length} stocks...`);for(let n=0;n<e.length;n+=t){let i=e.slice(n,n+t),s=i.map(e=>this.scanSingleStock(e));(await Promise.allSettled(s)).forEach((e,t)=>{let n=i[t];if("fulfilled"===e.status&&e.value){a.push(e.value);let t=e.value.sector;o[t]=(o[t]||0)+1}else r.push(n),console.warn(`Failed to scan ${n}:`,"rejected"===e.status?e.reason:"Unknown error")}),n+t<e.length&&await new Promise(e=>setTimeout(e,1e3))}a.sort((e,t)=>t.score-e.score),a.forEach((e,t)=>{e.rank=t+1});let i=Date.now()-n;return{totalScanned:e.length,successfulScans:a.length,failedScans:r.length,topOpportunities:a.slice(0,20),sectorBreakdown:o,scanDuration:i}}async scanSingleStock(e){try{let[n,a]=await Promise.all([this.fmpAPI.getStockQuote(e),this.getHistoricalData(e)]);if(!a||a.length<50)throw Error("Insufficient historical data");let r=t.SwingTradingAnalyzer.analyzeSwingTrade(e,a),o=this.calculateSwingScore(n,r);return{symbol:e,name:n.name,sector:this.getSectorForSymbol(e),quote:n,analysis:r,score:o,rank:0,scanTime:new Date().toISOString()}}catch(t){return console.error(`Error scanning ${e}:`,t),null}}async getHistoricalData(e){let t=(0,r.format)(new Date,"yyyy-MM-dd"),n=(0,r.format)((0,o.subDays)(new Date,100),"yyyy-MM-dd");try{return await this.polygonAPI.getHistoricalData(e,"day",1,n,t)}catch(t){throw console.warn(`Polygon failed for ${e}, trying alternative...`),t}}calculateSwingScore(e,t){let n;n=0+.4*t.confidence;let a=t.riskRewardRatio;a>=3?n+=20:a>=2?n+=15:a>=1.5?n+=10:a>=1&&(n+=5);let r=t.indicators.find(e=>"Volume"===e.name);r&&("BUY"===r.signal&&r.value>1.5?n+=15:"BUY"===r.signal?n+=10:"NEUTRAL"===r.signal&&(n+=5)),"BULLISH"===t.trend?n+=15:"BEARISH"===t.trend?n+=10:n+=5;let o=t.indicators.filter(e=>"BUY"===e.signal).length,i=t.indicators.filter(e=>"SELL"===e.signal).length,s=t.indicators.length;o>i?n+=o/s*10:i>o&&(n+=i/s*8);let l=Math.abs(e.changePercent);switch(l>5?n+=10:l>2?n+=7:l>1?n+=5:l<.5&&(n-=5),t.recommendation){case"STRONG_BUY":n+=10;break;case"BUY":n+=7;break;case"STRONG_SELL":n+=8;break;case"SELL":n+=5;break;case"NO_TRADE":n-=10}return Math.max(0,Math.min(100,n))}getSectorForSymbol(e){return["MSFT","NVDA","GOOG","GOOGL","META","AVGO","TSM","ORCL","CSCO","AMD","ASML","MU","LRCX","PLTR","APP","NET","DDOG","ZS","SHOP","SOUN","IONQ","RGTI","RIOT","HUT","IREN","ASTS","NBIS"].includes(e)?"Technology":["JPM","BAC","MS","SCHW","C","HOOD","SOFI","TIGR","FUTU"].includes(e)?"Financial Services":["JNJ","ABBV","MRK","GILD"].includes(e)?"Healthcare":["GE","CAT","BA","GEV","UAL","VRT","RKLB"].includes(e)?"Industrial":["AEM","NEM","PAAS","BTG","HL","MP","AG"].includes(e)?"Materials":["AMZN","DIS","SBUX","MO","DASH","GM","NCLH","CELH","LEVI","ELF","ETSY","W"].includes(e)?"Consumer":["NFLX","RBLX","BILI"].includes(e)?"Communication Services":["CEG","VST","CCJ"].includes(e)?"Energy":"Other"}async quickScan(e){return(await this.scanStocks(e,8)).topOpportunities}}},99740,(e,t,n)=>{},9920,e=>{"use strict";e.s(["handler",()=>T,"patchFetch",()=>P,"routeModule",()=>v,"serverHooks",()=>O,"workAsyncStorage",()=>y,"workUnitAsyncStorage",()=>C],9920);var t=e.i(11971),n=e.i(6780),a=e.i(51842),r=e.i(62950),o=e.i(21346),i=e.i(30506),s=e.i(63077),l=e.i(34765),c=e.i(64182),u=e.i(85062),d=e.i(51548),p=e.i(95133),h=e.i(8819),g=e.i(41050),S=e.i(93695);e.i(96641);var R=e.i(3893);e.s(["GET",()=>A],52584);var f=e.i(59169),E=e.i(18230),w=e.i(5744);async function A(e){try{let{searchParams:t}=new URL(e.url),n=parseInt(t.get("limit")||"20"),a=parseInt(t.get("concurrent")||"5");console.log(`Starting full scan of ${w.ALL_SYMBOLS.length} stocks...`);let r=await E.swingScanner.scanStocks(w.ALL_SYMBOLS,a),o={...r,topOpportunities:r.topOpportunities.slice(0,n)};return f.NextResponse.json(o)}catch(e){return console.error("Error in full scanner API:",e),f.NextResponse.json({error:"Failed to perform full stock scan"},{status:500})}}var m=e.i(52584);let v=new t.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/scanner/full/route",pathname:"/api/scanner/full",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/swing-trader-ai/src/app/api/scanner/full/route.ts",nextConfigOutput:"",userland:m}),{workAsyncStorage:y,workUnitAsyncStorage:C,serverHooks:O}=v;function P(){return(0,a.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:C})}async function T(e,t,a){var f;let E="/api/scanner/full/route";E=E.replace(/\/index$/,"")||"/";let w=await v.prepare(e,t,{srcPage:E,multiZoneDraftMode:!1});if(!w)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:A,params:m,nextConfig:y,isDraftMode:C,prerenderManifest:O,routerServerContext:P,isOnDemandRevalidate:T,revalidateOnlyGenerated:I,resolvedPathname:M}=w,N=(0,i.normalizeAppPath)(E),L=!!(O.dynamicRoutes[N]||O.routes[M]);if(L&&!C){let e=!!O.routes[M],t=O.dynamicRoutes[N];if(t&&!1===t.fallback&&!e)throw new S.NoFallbackError}let x=null;!L||v.isDev||C||(x="/index"===(x=M)?"/":x);let U=!0===v.isDev||!L,k=L&&!U,H=e.method||"GET",D=(0,o.getTracer)(),_=D.getActiveScopeSpan(),b={params:m,prerenderManifest:O,renderOpts:{experimental:{cacheComponents:!!y.experimental.cacheComponents,authInterrupts:!!y.experimental.authInterrupts},supportsDynamicResponse:U,incrementalCache:(0,r.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(f=y.experimental)?void 0:f.cacheLife,isRevalidate:k,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,n,a)=>v.onRequestError(e,t,a,P)},sharedContext:{buildId:A}},B=new s.NodeNextRequest(e),G=new s.NodeNextResponse(t),F=l.NextRequestAdapter.fromNodeNextRequest(B,(0,l.signalFromNodeResponse)(t));try{let i=async n=>v.handle(F,b).finally(()=>{if(!n)return;n.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=D.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==c.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let r=a.get("next.route");if(r){let e=`${H} ${r}`;n.setAttributes({"next.route":r,"http.route":r,"next.span_name":e}),n.updateName(e)}else n.updateName(`${H} ${e.url}`)}),s=async o=>{var s,l;let c=async({previousCacheEntry:n})=>{try{if(!(0,r.getRequestMeta)(e,"minimalMode")&&T&&I&&!n)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let s=await i(o);e.fetchMetrics=b.renderOpts.fetchMetrics;let l=b.renderOpts.pendingWaitUntil;l&&a.waitUntil&&(a.waitUntil(l),l=void 0);let c=b.renderOpts.collectedTags;if(!L)return await (0,d.sendResponse)(B,G,s,b.renderOpts.pendingWaitUntil),null;{let e=await s.blob(),t=(0,p.toNodeOutgoingHttpHeaders)(s.headers);c&&(t[g.NEXT_CACHE_TAGS_HEADER]=c),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let n=void 0!==b.renderOpts.collectedRevalidate&&!(b.renderOpts.collectedRevalidate>=g.INFINITE_CACHE)&&b.renderOpts.collectedRevalidate,a=void 0===b.renderOpts.collectedExpire||b.renderOpts.collectedExpire>=g.INFINITE_CACHE?void 0:b.renderOpts.collectedExpire;return{value:{kind:R.CachedRouteKind.APP_ROUTE,status:s.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:n,expire:a}}}}catch(t){throw(null==n?void 0:n.isStale)&&await v.onRequestError(e,t,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:k,isOnDemandRevalidate:T})},P),t}},S=await v.handleResponse({req:e,nextConfig:y,cacheKey:x,routeKind:n.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:O,isRoutePPREnabled:!1,isOnDemandRevalidate:T,revalidateOnlyGenerated:I,responseGenerator:c,waitUntil:a.waitUntil});if(!L)return null;if((null==S||null==(s=S.value)?void 0:s.kind)!==R.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==S||null==(l=S.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,r.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",T?"REVALIDATED":S.isMiss?"MISS":S.isStale?"STALE":"HIT"),C&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let f=(0,p.fromNodeOutgoingHttpHeaders)(S.value.headers);return(0,r.getRequestMeta)(e,"minimalMode")&&L||f.delete(g.NEXT_CACHE_TAGS_HEADER),!S.cacheControl||t.getHeader("Cache-Control")||f.get("Cache-Control")||f.set("Cache-Control",(0,h.getCacheControlHeader)(S.cacheControl)),await (0,d.sendResponse)(B,G,new Response(S.value.body,{headers:f,status:S.value.status||200})),null};_?await s(_):await D.withPropagatedContext(e.headers,()=>D.trace(c.BaseServerSpan.handleRequest,{spanName:`${H} ${e.url}`,kind:o.SpanKind.SERVER,attributes:{"http.method":H,"http.target":e.url}},s))}catch(t){if(t instanceof S.NoFallbackError||await v.onRequestError(e,t,{routerKind:"App Router",routePath:N,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:k,isOnDemandRevalidate:T})}),L)throw t;return await (0,d.sendResponse)(B,G,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=swing-trader-ai_d63825e1._.js.map