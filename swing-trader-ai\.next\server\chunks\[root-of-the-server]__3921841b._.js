module.exports = [
"[project]/swing-trader-ai/.next-internal/server/app/api/data-source/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/fs [external] (fs, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}),
"[externals]/path [external] (path, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}),
"[externals]/os [external] (os, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}),
"[externals]/crypto [external] (crypto, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}),
"[externals]/net [external] (net, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}),
"[externals]/util [external] (util, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}),
"[project]/swing-trader-ai/src/lib/ibkr.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "IBKRAPI",
    ()=>IBKRAPI
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/node_modules/@stoqey/ib/dist/index.js [app-route] (ecmascript)");
;
class IBKRAPI {
    ib;
    config;
    connected = false;
    nextOrderId = 1;
    positions = new Map();
    orders = new Map();
    accountSummary = null;
    constructor(config){
        this.config = config;
        this.ib = new __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["IBApi"]({
            host: config.host,
            port: config.port,
            clientId: config.clientId
        });
        this.setupEventHandlers();
    }
    setupEventHandlers() {
        // Connection events
        this.ib.on(__TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].connected, ()=>{
            console.log('✅ Connected to IBKR');
            this.connected = true;
            this.requestNextOrderId();
            this.requestAccountSummary();
            this.requestPositions();
        });
        this.ib.on(__TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].disconnected, ()=>{
            console.log('❌ Disconnected from IBKR');
            this.connected = false;
        });
        this.ib.on(__TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].error, (err, code, reqId)=>{
            console.error(`IBKR Error ${code}:`, err);
        });
        // Order management
        this.ib.on(__TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].nextValidId, (orderId)=>{
            this.nextOrderId = orderId;
            console.log(`Next valid order ID: ${orderId}`);
        });
        this.ib.on(__TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].orderStatus, (orderId, status, filled, remaining, avgFillPrice, permId, parentId, lastFillPrice, clientId, whyHeld, mktCapPrice)=>{
            const order = this.orders.get(orderId);
            if (order) {
                order.status = status;
                order.filled = filled;
                order.remaining = remaining;
                this.orders.set(orderId, order);
            }
        });
        // Position updates
        this.ib.on(__TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].position, (account, contract, position, avgCost)=>{
            const symbol = contract.symbol;
            const existingPosition = this.positions.get(symbol) || {
                symbol,
                position: 0,
                marketPrice: 0,
                marketValue: 0,
                averageCost: 0,
                unrealizedPNL: 0,
                realizedPNL: 0
            };
            existingPosition.position = position;
            existingPosition.averageCost = avgCost;
            this.positions.set(symbol, existingPosition);
        });
        // Account summary
        this.ib.on(__TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].accountSummary, (reqId, account, tag, value, currency)=>{
            if (!this.accountSummary) {
                this.accountSummary = {
                    totalCashValue: 0,
                    netLiquidation: 0,
                    grossPositionValue: 0,
                    availableFunds: 0,
                    buyingPower: 0,
                    unrealizedPnL: 0,
                    realizedPnL: 0
                };
            }
            switch(tag){
                case 'TotalCashValue':
                    this.accountSummary.totalCashValue = parseFloat(value);
                    break;
                case 'NetLiquidation':
                    this.accountSummary.netLiquidation = parseFloat(value);
                    break;
                case 'GrossPositionValue':
                    this.accountSummary.grossPositionValue = parseFloat(value);
                    break;
                case 'AvailableFunds':
                    this.accountSummary.availableFunds = parseFloat(value);
                    break;
                case 'BuyingPower':
                    this.accountSummary.buyingPower = parseFloat(value);
                    break;
                case 'UnrealizedPnL':
                    this.accountSummary.unrealizedPnL = parseFloat(value);
                    break;
                case 'RealizedPnL':
                    this.accountSummary.realizedPnL = parseFloat(value);
                    break;
            }
        });
    }
    async connect() {
        return new Promise((resolve, reject)=>{
            if (this.connected) {
                resolve();
                return;
            }
            const timeout = setTimeout(()=>{
                reject(new Error('Connection timeout'));
            }, 10000);
            this.ib.once(__TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].connected, ()=>{
                clearTimeout(timeout);
                resolve();
            });
            this.ib.once(__TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f40$stoqey$2f$ib$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EventName"].error, (err)=>{
                clearTimeout(timeout);
                reject(err);
            });
            this.ib.connect();
        });
    }
    disconnect() {
        if (this.connected) {
            this.ib.disconnect();
        }
    }
    requestNextOrderId() {
        this.ib.reqIds(1);
    }
    requestAccountSummary() {
        this.ib.reqAccountSummary(1, 'All', 'TotalCashValue,NetLiquidation,GrossPositionValue,AvailableFunds,BuyingPower,UnrealizedPnL,RealizedPnL');
    }
    requestPositions() {
        this.ib.reqPositions();
    }
    // Create a stock contract
    createStockContract(symbol) {
        return {
            symbol: symbol.toUpperCase(),
            secType: 'STK',
            exchange: 'SMART',
            currency: 'USD'
        };
    }
    // Place a market order
    async placeMarketOrder(symbol, action, quantity) {
        if (!this.connected) {
            throw new Error('Not connected to IBKR');
        }
        const contract = this.createStockContract(symbol);
        const order = {
            orderId: this.nextOrderId,
            action,
            totalQuantity: quantity,
            orderType: 'MKT'
        };
        // Store order for tracking
        this.orders.set(this.nextOrderId, {
            orderId: this.nextOrderId,
            symbol: symbol.toUpperCase(),
            action,
            quantity,
            orderType: 'MKT',
            status: 'Submitted',
            filled: 0,
            remaining: quantity
        });
        this.ib.placeOrder(this.nextOrderId, contract, order);
        const orderId = this.nextOrderId;
        this.nextOrderId++;
        return orderId;
    }
    // Place a limit order
    async placeLimitOrder(symbol, action, quantity, price) {
        if (!this.connected) {
            throw new Error('Not connected to IBKR');
        }
        const contract = this.createStockContract(symbol);
        const order = {
            orderId: this.nextOrderId,
            action,
            totalQuantity: quantity,
            orderType: 'LMT',
            lmtPrice: price
        };
        // Store order for tracking
        this.orders.set(this.nextOrderId, {
            orderId: this.nextOrderId,
            symbol: symbol.toUpperCase(),
            action,
            quantity,
            orderType: 'LMT',
            price,
            status: 'Submitted',
            filled: 0,
            remaining: quantity
        });
        this.ib.placeOrder(this.nextOrderId, contract, order);
        const orderId = this.nextOrderId;
        this.nextOrderId++;
        return orderId;
    }
    // Place a stop-loss order
    async placeStopOrder(symbol, action, quantity, stopPrice) {
        if (!this.connected) {
            throw new Error('Not connected to IBKR');
        }
        const contract = this.createStockContract(symbol);
        const order = {
            orderId: this.nextOrderId,
            action,
            totalQuantity: quantity,
            orderType: 'STP',
            auxPrice: stopPrice
        };
        // Store order for tracking
        this.orders.set(this.nextOrderId, {
            orderId: this.nextOrderId,
            symbol: symbol.toUpperCase(),
            action,
            quantity,
            orderType: 'STP',
            price: stopPrice,
            status: 'Submitted',
            filled: 0,
            remaining: quantity
        });
        this.ib.placeOrder(this.nextOrderId, contract, order);
        const orderId = this.nextOrderId;
        this.nextOrderId++;
        return orderId;
    }
    // Cancel an order
    async cancelOrder(orderId) {
        if (!this.connected) {
            throw new Error('Not connected to IBKR');
        }
        this.ib.cancelOrder(orderId);
    }
    // Get account summary
    getAccountSummary() {
        return this.accountSummary;
    }
    // Get all positions
    getPositions() {
        return Array.from(this.positions.values());
    }
    // Get position for specific symbol
    getPosition(symbol) {
        return this.positions.get(symbol.toUpperCase()) || null;
    }
    // Get all orders
    getOrders() {
        return Array.from(this.orders.values());
    }
    // Get specific order
    getOrder(orderId) {
        return this.orders.get(orderId) || null;
    }
    // Check if connected
    isConnected() {
        return this.connected;
    }
}
}),
"[externals]/stream [external] (stream, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}),
"[externals]/http [external] (http, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}),
"[externals]/https [external] (https, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}),
"[externals]/url [external] (url, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}),
"[externals]/assert [external] (assert, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}),
"[externals]/tty [external] (tty, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}),
"[externals]/zlib [external] (zlib, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}),
"[externals]/events [external] (events, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}),
"[project]/swing-trader-ai/src/lib/fmp.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "FMPAPI",
    ()=>FMPAPI,
    "fmpAPI",
    ()=>fmpAPI
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/node_modules/axios/lib/axios.js [app-route] (ecmascript)");
;
const FMP_BASE_URL = 'https://financialmodelingprep.com/api';
const API_KEY = process.env.FMP_API_KEY;
class FMPAPI {
    apiKey;
    constructor(apiKey){
        this.apiKey = apiKey || API_KEY || '';
        if (!this.apiKey) {
            throw new Error('FMP API key is required');
        }
    }
    // Get real-time stock quote
    async getStockQuote(symbol) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/quote/${symbol}`, {
                params: {
                    apikey: this.apiKey
                }
            });
            const data = response.data[0];
            if (!data) {
                throw new Error(`No data found for symbol ${symbol}`);
            }
            return {
                symbol: data.symbol,
                name: data.name || data.symbol,
                price: data.price,
                change: data.change,
                changePercent: data.changesPercentage,
                volume: data.volume,
                marketCap: data.marketCap,
                pe: data.pe,
                dividend: undefined // Will be fetched separately if needed
            };
        } catch (error) {
            console.error('Error fetching FMP stock quote:', error);
            throw new Error(`Failed to fetch quote for ${symbol}`);
        }
    }
    // Get company profile
    async getCompanyProfile(symbol) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/profile/${symbol}`, {
                params: {
                    apikey: this.apiKey
                }
            });
            return response.data[0];
        } catch (error) {
            console.error('Error fetching company profile:', error);
            return null;
        }
    }
    // Get financial ratios
    async getFinancialRatios(symbol) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/ratios/${symbol}`, {
                params: {
                    apikey: this.apiKey
                }
            });
            return response.data[0] // Most recent ratios
            ;
        } catch (error) {
            console.error('Error fetching financial ratios:', error);
            return null;
        }
    }
    // Get key metrics
    async getKeyMetrics(symbol) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/key-metrics/${symbol}`, {
                params: {
                    apikey: this.apiKey
                }
            });
            return response.data[0] // Most recent metrics
            ;
        } catch (error) {
            console.error('Error fetching key metrics:', error);
            return null;
        }
    }
    // Get analyst recommendations
    async getAnalystRecommendations(symbol) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/analyst-stock-recommendations/${symbol}`, {
                params: {
                    apikey: this.apiKey
                }
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching analyst recommendations:', error);
            return [];
        }
    }
    // Get earnings calendar
    async getEarningsCalendar(from, to) {
        try {
            const params = {
                apikey: this.apiKey
            };
            if (from) params.from = from;
            if (to) params.to = to;
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/earning_calendar`, {
                params
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching earnings calendar:', error);
            return [];
        }
    }
    // Get economic calendar
    async getEconomicCalendar(from, to) {
        try {
            const params = {
                apikey: this.apiKey
            };
            if (from) params.from = from;
            if (to) params.to = to;
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/economic_calendar`, {
                params
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching economic calendar:', error);
            return [];
        }
    }
    // Search for stocks
    async searchStocks(query, limit = 10) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/search`, {
                params: {
                    query,
                    limit,
                    apikey: this.apiKey
                }
            });
            return response.data;
        } catch (error) {
            console.error('Error searching stocks:', error);
            return [];
        }
    }
    // Get sector performance
    async getSectorPerformance() {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/sector-performance`, {
                params: {
                    apikey: this.apiKey
                }
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching sector performance:', error);
            return [];
        }
    }
    // Get market gainers/losers
    async getMarketMovers(type) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/stock_market/${type}`, {
                params: {
                    apikey: this.apiKey
                }
            });
            return response.data;
        } catch (error) {
            console.error(`Error fetching market ${type}:`, error);
            return [];
        }
    }
    // ===== CATALYST DETECTION ENDPOINTS =====
    // Get earnings calendar for catalyst detection
    async getEarningsCalendar(symbol, days = 30) {
        try {
            const fromDate = new Date();
            fromDate.setDate(fromDate.getDate() - days);
            const toDate = new Date();
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/earning_calendar`, {
                params: {
                    apikey: this.apiKey,
                    from: fromDate.toISOString().split('T')[0],
                    to: toDate.toISOString().split('T')[0],
                    ...symbol && {
                        symbol: symbol.toUpperCase()
                    }
                }
            });
            return response.data || [];
        } catch (error) {
            console.error('Error fetching earnings calendar:', error);
            return [];
        }
    }
    // Get stock news for catalyst detection
    async getStockNews(symbol, limit = 50) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/stock_news`, {
                params: {
                    apikey: this.apiKey,
                    tickers: symbol.toUpperCase(),
                    limit
                }
            });
            return response.data || [];
        } catch (error) {
            console.error('Error fetching stock news:', error);
            return [];
        }
    }
    // Get analyst recommendations
    async getAnalystRecommendations(symbol, days = 30) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/analyst-stock-recommendations/${symbol.toUpperCase()}`, {
                params: {
                    apikey: this.apiKey,
                    limit: days
                }
            });
            return response.data || [];
        } catch (error) {
            console.error('Error fetching analyst recommendations:', error);
            return [];
        }
    }
    // Get insider trading data
    async getInsiderTrading(symbol, days = 30) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v4/insider-trading`, {
                params: {
                    apikey: this.apiKey,
                    symbol: symbol.toUpperCase(),
                    limit: days * 5 // Approximate multiple to get enough data
                }
            });
            // Filter to last N days
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - days);
            return (response.data || []).filter((trade)=>new Date(trade.filingDate) >= cutoffDate);
        } catch (error) {
            console.error('Error fetching insider trading:', error);
            return [];
        }
    }
    // Get SEC filings
    async getSECFilings(symbol, days = 30) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/sec_filings/${symbol.toUpperCase()}`, {
                params: {
                    apikey: this.apiKey,
                    limit: days * 2 // Get more filings to filter by date
                }
            });
            // Filter to last N days
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - days);
            return (response.data || []).filter((filing)=>new Date(filing.filedDate) >= cutoffDate);
        } catch (error) {
            console.error('Error fetching SEC filings:', error);
            return [];
        }
    }
    // Get pre-market quotes for gap scanning
    async getPreMarketQuote(symbol) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/quote/${symbol.toUpperCase()}`, {
                params: {
                    apikey: this.apiKey
                }
            });
            const data = response.data[0];
            if (!data) return null;
            return {
                symbol: data.symbol,
                price: data.price,
                previousClose: data.previousClose,
                change: data.change,
                changePercent: data.changesPercentage,
                volume: data.volume,
                marketCap: data.marketCap,
                avgVolume: data.avgVolume,
                // Pre-market specific data (if available)
                preMarketPrice: data.preMarketPrice || data.price,
                preMarketChange: data.preMarketChange || data.change,
                preMarketChangePercent: data.preMarketChangePercent || data.changesPercentage
            };
        } catch (error) {
            console.error('Error fetching pre-market quote:', error);
            return null;
        }
    }
    // Get multiple pre-market quotes efficiently
    async getMultiplePreMarketQuotes(symbols) {
        try {
            const symbolsString = symbols.map((s)=>s.toUpperCase()).join(',');
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/quote/${symbolsString}`, {
                params: {
                    apikey: this.apiKey
                }
            });
            return (response.data || []).map((data)=>({
                    symbol: data.symbol,
                    price: data.price,
                    previousClose: data.previousClose,
                    change: data.change,
                    changePercent: data.changesPercentage,
                    volume: data.volume,
                    marketCap: data.marketCap,
                    avgVolume: data.avgVolume,
                    preMarketPrice: data.preMarketPrice || data.price,
                    preMarketChange: data.preMarketChange || data.change,
                    preMarketChangePercent: data.preMarketChangePercent || data.changesPercentage
                }));
        } catch (error) {
            console.error('Error fetching multiple pre-market quotes:', error);
            return [];
        }
    }
    // Get company profile for additional context
    async getCompanyProfile(symbol) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${FMP_BASE_URL}/v3/profile/${symbol.toUpperCase()}`, {
                params: {
                    apikey: this.apiKey
                }
            });
            return response.data[0] || null;
        } catch (error) {
            console.error('Error fetching company profile:', error);
            return null;
        }
    }
}
const fmpAPI = new FMPAPI();
}),
"[project]/swing-trader-ai/src/app/api/data-source/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "GET",
    ()=>GET,
    "POST",
    ()=>POST
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$ibkr$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/src/lib/ibkr.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$fmp$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/src/lib/fmp.ts [app-route] (ecmascript)");
;
;
;
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const action = searchParams.get('action');
        switch(action){
            case 'check_ibkr':
                return await checkIBKRConnection();
            case 'check_fmp':
                return await checkFMPConnection();
            case 'status':
                return await getDataSourceStatus();
            default:
                return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: false,
                    error: 'Invalid action'
                }, {
                    status: 400
                });
        }
    } catch (error) {
        console.error('Error in data source API:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to check data sources',
            details: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
async function checkIBKRConnection() {
    try {
        const ibkrAPI = new __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$ibkr$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["IBKRAPI"]({
            host: '127.0.0.1',
            port: 7497,
            clientId: 1,
            paperTrading: true
        });
        const isConnected = await ibkrAPI.connect();
        if (isConnected) {
            // Test with a simple quote request
            const testQuote = await ibkrAPI.getMarketData('SPY');
            return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                data: {
                    connected: true,
                    source: 'IBKR',
                    testQuote: testQuote ? 'Success' : 'Failed',
                    message: 'IBKR connection successful'
                }
            });
        } else {
            return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                data: {
                    connected: false,
                    source: 'IBKR',
                    message: 'IBKR connection failed - ensure TWS/IB Gateway is running'
                }
            });
        }
    } catch (error) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            data: {
                connected: false,
                source: 'IBKR',
                error: error instanceof Error ? error.message : 'Unknown error',
                message: 'IBKR connection error'
            }
        });
    }
}
async function checkFMPConnection() {
    try {
        const fmpAPI = new __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$fmp$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["FMPAPI"](process.env.FMP_API_KEY);
        // Test with a simple quote request
        const testQuote = await fmpAPI.getStockQuote('SPY');
        return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                connected: true,
                source: 'FMP',
                testQuote: testQuote ? 'Success' : 'Failed',
                message: 'FMP API connection successful'
            }
        });
    } catch (error) {
        const isRateLimit = error instanceof Error && error.message.includes('429');
        return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            data: {
                connected: false,
                source: 'FMP',
                error: error instanceof Error ? error.message : 'Unknown error',
                isRateLimit,
                message: isRateLimit ? 'FMP API rate limit exceeded' : 'FMP API connection error'
            }
        });
    }
}
async function getDataSourceStatus() {
    try {
        const [ibkrResult, fmpResult] = await Promise.all([
            checkIBKRConnection(),
            checkFMPConnection()
        ]);
        const ibkrData = await ibkrResult.json();
        const fmpData = await fmpResult.json();
        const recommendedSource = ibkrData.success ? 'IBKR' : fmpData.success ? 'FMP' : 'NONE';
        return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                ibkr: ibkrData.data,
                fmp: fmpData.data,
                recommended: recommendedSource,
                timestamp: new Date().toISOString()
            }
        });
    } catch (error) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to check data source status',
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
}
async function POST(request) {
    try {
        const body = await request.json();
        const { action, config } = body;
        switch(action){
            case 'set_preferred_source':
                // Store preferred data source in environment or database
                // For now, just return success
                return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    data: {
                        preferredSource: config.source,
                        message: `Preferred data source set to ${config.source}`
                    }
                });
            case 'test_connection':
                if (config.source === 'IBKR') {
                    return await checkIBKRConnection();
                } else if (config.source === 'FMP') {
                    return await checkFMPConnection();
                } else {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                        success: false,
                        error: 'Invalid source specified'
                    }, {
                        status: 400
                    });
                }
            default:
                return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: false,
                    error: 'Invalid action'
                }, {
                    status: 400
                });
        }
    } catch (error) {
        console.error('Error in data source POST API:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to process data source request',
            details: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__3921841b._.js.map