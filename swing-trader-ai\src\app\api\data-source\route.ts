import { NextRequest, NextResponse } from 'next/server'
import { IBKRAPI } from '@/lib/ibkr'
import { FMPAPI } from '@/lib/fmp'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    switch (action) {
      case 'check_ibkr':
        return await checkIBKRConnection()
      
      case 'check_fmp':
        return await checkFMPConnection()
      
      case 'status':
        return await getDataSourceStatus()
      
      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Error in data source API:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to check data sources',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

async function checkIBKRConnection() {
  try {
    const ibkrAPI = new IBKRAPI({
      host: '127.0.0.1',
      port: 4002, // IB Gateway paper trading port
      clientId: 1,
      paperTrading: true
    })

    const isConnected = await ibkrAPI.connect()
    
    if (isConnected) {
      // Test with a simple quote request
      const testQuote = await ibkrAPI.getMarketData('SPY')
      
      return NextResponse.json({
        success: true,
        data: {
          connected: true,
          source: 'IBKR',
          testQuote: testQuote ? 'Success' : 'Failed',
          message: 'IBKR connection successful'
        }
      })
    } else {
      return NextResponse.json({
        success: false,
        data: {
          connected: false,
          source: 'IBKR',
          message: 'IBKR connection failed - ensure TWS/IB Gateway is running'
        }
      })
    }
  } catch (error) {
    return NextResponse.json({
      success: false,
      data: {
        connected: false,
        source: 'IBKR',
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'IBKR connection error'
      }
    })
  }
}

async function checkFMPConnection() {
  try {
    const fmpAPI = new FMPAPI(process.env.FMP_API_KEY)
    
    // Test with a simple quote request
    const testQuote = await fmpAPI.getStockQuote('SPY')
    
    return NextResponse.json({
      success: true,
      data: {
        connected: true,
        source: 'FMP',
        testQuote: testQuote ? 'Success' : 'Failed',
        message: 'FMP API connection successful'
      }
    })
  } catch (error) {
    const isRateLimit = error instanceof Error && error.message.includes('429')
    
    return NextResponse.json({
      success: false,
      data: {
        connected: false,
        source: 'FMP',
        error: error instanceof Error ? error.message : 'Unknown error',
        isRateLimit,
        message: isRateLimit ? 'FMP API rate limit exceeded' : 'FMP API connection error'
      }
    })
  }
}

async function getDataSourceStatus() {
  try {
    const [ibkrResult, fmpResult] = await Promise.all([
      checkIBKRConnection(),
      checkFMPConnection()
    ])

    const ibkrData = await ibkrResult.json()
    const fmpData = await fmpResult.json()

    const recommendedSource = ibkrData.success ? 'IBKR' : 
                             fmpData.success ? 'FMP' : 'NONE'

    return NextResponse.json({
      success: true,
      data: {
        ibkr: ibkrData.data,
        fmp: fmpData.data,
        recommended: recommendedSource,
        timestamp: new Date().toISOString()
      }
    })
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Failed to check data source status',
      details: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, config } = body

    switch (action) {
      case 'set_preferred_source':
        // Store preferred data source in environment or database
        // For now, just return success
        return NextResponse.json({
          success: true,
          data: {
            preferredSource: config.source,
            message: `Preferred data source set to ${config.source}`
          }
        })

      case 'test_connection':
        if (config.source === 'IBKR') {
          return await checkIBKRConnection()
        } else if (config.source === 'FMP') {
          return await checkFMPConnection()
        } else {
          return NextResponse.json(
            { success: false, error: 'Invalid source specified' },
            { status: 400 }
          )
        }

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Error in data source POST API:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process data source request',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
