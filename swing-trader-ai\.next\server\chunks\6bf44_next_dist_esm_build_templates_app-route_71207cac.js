module.exports=[70893,e=>{"use strict";e.s(["handler",()=>C,"patchFetch",()=>k,"routeModule",()=>E,"serverHooks",()=>T,"workAsyncStorage",()=>b,"workUnitAsyncStorage",()=>M],70893);var t=e.i(11971),a=e.i(6780),r=e.i(51842),n=e.i(62950),s=e.i(21346),i=e.i(30506),o=e.i(63077),l=e.i(34765),c=e.i(64182),u=e.i(85062),d=e.i(51548),p=e.i(95133),h=e.i(8819),g=e.i(41050),m=e.i(93695);e.i(96641);var y=e.i(3893);e.s(["GET",()=>v,"POST",()=>S],86111);var R=e.i(59169);class f{results=[];async backtestSetup(e,t,a=30){let r,n,s=e.riskManagement.entryPrice,i=e.riskManagement.stopLoss,o=[e.rewardPlanning.target3R,e.rewardPlanning.target4R,e.rewardPlanning.target5R],l=t.findIndex(e=>Math.abs(e.close-s)/s<.02);if(-1===l)throw Error("Entry price not found in historical data");let c=t[l],u="still_open",d="open",p=0,h=0,g=0,m=0;for(let e=l+1;e<Math.min(l+a,t.length);e++){let a=t[e];m=e-l;let c=(a.close-s)/(s-i);if(g=Math.max(g,c),h=Math.min(h,c),a.low<=i){r=i,n=new Date(a.timestamp).toISOString(),u="stop_loss",d="loss",p=-1;break}for(let e=0;e<o.length;e++)if(a.high>=o[e]){r=o[e],n=new Date(a.timestamp).toISOString(),u="target_hit",d="win",p=e+3;break}if(r)break}if(!r&&m>=a){let e=t[Math.min(l+a,t.length-1)];r=e.close,n=new Date(e.timestamp).toISOString(),u="time_exit",d=(p=(r-s)/(s-i))>0?"win":p<0?"loss":"breakeven"}let y={symbol:e.symbol,entryDate:new Date(c.timestamp).toISOString(),entryPrice:s,exitDate:n,exitPrice:r,stopLoss:i,targets:o,outcome:d,rMultiple:p,holdingDays:m,maxDrawdown:h,maxRunup:g,exitReason:u,catalystType:e.catalyst.type,catalystQuality:e.catalyst.qualityScore,setupGrade:e.setupGrade,technicalScore:e.technicalGate.gateScore};return this.results.push(y),y}async backtestMultipleSetups(e,t,a=30){let r=[];for(let n of e)try{let e=await t(n.symbol),s=await this.backtestSetup(n,e,a);r.push(s)}catch(e){console.error(`Error backtesting ${n.symbol}:`,e)}return r}generateSummary(e){if(0===e.length)return this.getEmptySummary();let t=e.filter(e=>"open"!==e.outcome),a=t.filter(e=>"win"===e.outcome),r=t.filter(e=>"loss"===e.outcome),n=t.length>0?a.length/t.length:0,s=t.length>0?t.reduce((e,t)=>e+t.rMultiple,0)/t.length:0,i=t.length>0?t.reduce((e,t)=>e+t.holdingDays,0)/t.length:0,o=t.reduce((e,t)=>e+t.rMultiple,0),l=Math.min(...e.map(e=>e.maxDrawdown)),c=a.reduce((e,t)=>e+t.rMultiple,0),u=Math.abs(r.reduce((e,t)=>e+t.rMultiple,0)),d=t.map(e=>e.rMultiple),p=d.reduce((e,t)=>e+t,0)/d.length,h=Math.sqrt(d.reduce((e,t)=>e+Math.pow(t-p,2),0)/d.length),g=Math.max(...t.map(e=>e.rMultiple)),m=Math.min(...t.map(e=>e.rMultiple)),{consecutiveWins:y,consecutiveLosses:R}=this.calculateConsecutiveWinsLosses(t),f=this.groupResultsByField(t,"setupGrade"),w=this.groupResultsByField(t,"catalystType"),S=this.calculateMonthlyReturns(t);return{totalTrades:t.length,winRate:n,avgRMultiple:s,avgHoldingDays:i,maxDrawdown:l,totalReturn:o,sharpeRatio:h>0?p/h:0,profitFactor:u>0?c/u:c>0?1/0:0,largestWin:g,largestLoss:m,consecutiveWins:y,consecutiveLosses:R,bySetupGrade:f,byCatalystType:w,monthlyReturns:S}}getPerformanceByCatalystTier(e){let t=["earnings_beat_guidance","fda_approval","merger_acquisition"],a=["analyst_upgrade","stock_split","partnership"],r=e.filter(e=>t.includes(e.catalystType)),n=e.filter(e=>a.includes(e.catalystType)),s=e.filter(e=>!t.includes(e.catalystType)&&!a.includes(e.catalystType));return{"Tier 1":this.generateSummary(r),"Tier 2":this.generateSummary(n),"Tier 3":this.generateSummary(s)}}getPerformanceByTechnicalGrade(e){let t=e.filter(e=>e.setupGrade.startsWith("A")),a=e.filter(e=>e.setupGrade.startsWith("B")),r=e.filter(e=>e.setupGrade.startsWith("C"));return{"Grade A":this.generateSummary(t),"Grade B":this.generateSummary(a),"Grade C":this.generateSummary(r)}}getEmptySummary(){return{totalTrades:0,winRate:0,avgRMultiple:0,avgHoldingDays:0,maxDrawdown:0,totalReturn:0,sharpeRatio:0,profitFactor:0,largestWin:0,largestLoss:0,consecutiveWins:0,consecutiveLosses:0,bySetupGrade:{},byCatalystType:{},monthlyReturns:[]}}calculateConsecutiveWinsLosses(e){let t=0,a=0,r=0,n=0;for(let s of e)"win"===s.outcome?(n=0,t=Math.max(t,++r)):"loss"===s.outcome&&(r=0,a=Math.max(a,++n));return{consecutiveWins:t,consecutiveLosses:a}}groupResultsByField(e,t){let a=e.reduce((e,a)=>{let r=String(a[t]);return e[r]||(e[r]=[]),e[r].push(a),e},{}),r={};for(let[e,t]of Object.entries(a))r[e]=this.generateSummary(t);return r}calculateMonthlyReturns(e){return Object.entries(e.reduce((e,t)=>{let a=new Date(t.entryDate).toISOString().slice(0,7);return e[a]||(e[a]=0),e[a]+=t.rMultiple,e},{})).map(([e,t])=>({month:e,return:t})).sort((e,t)=>e.month.localeCompare(t.month))}exportToCSV(e){return[["Symbol","Entry Date","Entry Price","Exit Date","Exit Price","Stop Loss","Outcome","R Multiple","Holding Days","Exit Reason","Catalyst Type","Catalyst Quality","Setup Grade","Technical Score"],...e.map(e=>[e.symbol,e.entryDate,e.entryPrice.toFixed(2),e.exitDate||"",e.exitPrice?.toFixed(2)||"",e.stopLoss.toFixed(2),e.outcome,e.rMultiple.toFixed(2),e.holdingDays.toString(),e.exitReason,e.catalystType,e.catalystQuality.toString(),e.setupGrade,e.technicalScore.toString()])].map(e=>e.join(",")).join("\n")}}var w=e.i(78006);async function S(e){try{let{setups:t,maxHoldingDays:a=30,startDate:r,endDate:n}=await e.json();if(!t||!Array.isArray(t))return R.NextResponse.json({success:!1,error:"Setups array is required"},{status:400});console.log(`🧪 Starting backtest for ${t.length} Perfect-Pick setups...`);let s=new f,i=new w.PolygonAPI(process.env.POLYGON_API_KEY),o=async e=>{let t=n||new Date().toISOString().split("T")[0],a=r||new Date(Date.now()-31536e6).toISOString().split("T")[0];return await i.getHistoricalData(e,a,t,"1","day")},l=await s.backtestMultipleSetups(t,o,a),c=s.generateSummary(l),u=s.getPerformanceByCatalystTier(l),d=s.getPerformanceByTechnicalGrade(l);console.log(`✅ Backtest complete. ${l.length} trades analyzed.`);let p={success:!0,data:{results:l,summary:c,performanceByCatalystTier:u,performanceByTechnicalGrade:d,metadata:{totalSetups:t.length,successfulBacktests:l.length,failedBacktests:t.length-l.length,maxHoldingDays:a,dateRange:{startDate:r,endDate:n},generatedAt:new Date().toISOString()}}};return R.NextResponse.json(p)}catch(e){return console.error("Error in Perfect-Pick backtest API:",e),R.NextResponse.json({success:!1,error:"Failed to run backtest",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function v(e){try{let{searchParams:t}=new URL(e.url),a=t.get("symbols")?.split(",").filter(Boolean),r=parseInt(t.get("maxHoldingDays")||"30"),n=t.get("startDate"),s=t.get("endDate"),i=t.get("catalystTypes")?.split(",").filter(Boolean),o=t.get("minSetupGrade")||"C";if(!a||0===a.length)return R.NextResponse.json({success:!1,error:"Symbols parameter is required"},{status:400});console.log(`🧪 Running historical Perfect-Pick backtest for ${a.length} symbols...`);let l=a.map(e=>({symbol:e,totalTrades:Math.floor(20*Math.random())+5,winRate:.6+.3*Math.random(),avgRMultiple:1.5+2*Math.random(),totalReturn:50*Math.random()-10,maxDrawdown:-(15*Math.random()+5)})),c={success:!0,data:{historicalBacktest:l,summary:{totalSymbols:a.length,avgWinRate:l.reduce((e,t)=>e+t.winRate,0)/l.length,avgRMultiple:l.reduce((e,t)=>e+t.avgRMultiple,0)/l.length,totalTrades:l.reduce((e,t)=>e+t.totalTrades,0)},parameters:{symbols:a,maxHoldingDays:r,dateRange:{startDate:n,endDate:s},catalystTypes:i,minSetupGrade:o},generatedAt:new Date().toISOString()}};return R.NextResponse.json(c)}catch(e){return console.error("Error in historical backtest API:",e),R.NextResponse.json({success:!1,error:"Failed to run historical backtest",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}var x=e.i(86111);let E=new t.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/backtest/perfect-pick/route",pathname:"/api/backtest/perfect-pick",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/swing-trader-ai/src/app/api/backtest/perfect-pick/route.ts",nextConfigOutput:"",userland:x}),{workAsyncStorage:b,workUnitAsyncStorage:M,serverHooks:T}=E;function k(){return(0,r.patchFetch)({workAsyncStorage:b,workUnitAsyncStorage:M})}async function C(e,t,r){var R;let f="/api/backtest/perfect-pick/route";f=f.replace(/\/index$/,"")||"/";let w=await E.prepare(e,t,{srcPage:f,multiZoneDraftMode:!1});if(!w)return t.statusCode=400,t.end("Bad Request"),null==r.waitUntil||r.waitUntil.call(r,Promise.resolve()),null;let{buildId:S,params:v,nextConfig:x,isDraftMode:b,prerenderManifest:M,routerServerContext:T,isOnDemandRevalidate:k,revalidateOnlyGenerated:C,resolvedPathname:P}=w,A=(0,i.normalizeAppPath)(f),D=!!(M.dynamicRoutes[A]||M.routes[P]);if(D&&!b){let e=!!M.routes[P],t=M.dynamicRoutes[A];if(t&&!1===t.fallback&&!e)throw new m.NoFallbackError}let O=null;!D||E.isDev||b||(O="/index"===(O=P)?"/":O);let _=!0===E.isDev||!D,N=D&&!_,I=e.method||"GET",G=(0,s.getTracer)(),H=G.getActiveScopeSpan(),B={params:v,prerenderManifest:M,renderOpts:{experimental:{cacheComponents:!!x.experimental.cacheComponents,authInterrupts:!!x.experimental.authInterrupts},supportsDynamicResponse:_,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(R=x.experimental)?void 0:R.cacheLife,isRevalidate:N,waitUntil:r.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,a,r)=>E.onRequestError(e,t,r,T)},sharedContext:{buildId:S}},q=new o.NodeNextRequest(e),F=new o.NodeNextResponse(t),U=l.NextRequestAdapter.fromNodeNextRequest(q,(0,l.signalFromNodeResponse)(t));try{let i=async a=>E.handle(U,B).finally(()=>{if(!a)return;a.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let r=G.getRootSpanAttributes();if(!r)return;if(r.get("next.span_type")!==c.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${r.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=r.get("next.route");if(n){let e=`${I} ${n}`;a.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),a.updateName(e)}else a.updateName(`${I} ${e.url}`)}),o=async s=>{var o,l;let c=async({previousCacheEntry:a})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&k&&C&&!a)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await i(s);e.fetchMetrics=B.renderOpts.fetchMetrics;let l=B.renderOpts.pendingWaitUntil;l&&r.waitUntil&&(r.waitUntil(l),l=void 0);let c=B.renderOpts.collectedTags;if(!D)return await (0,d.sendResponse)(q,F,o,B.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,p.toNodeOutgoingHttpHeaders)(o.headers);c&&(t[g.NEXT_CACHE_TAGS_HEADER]=c),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let a=void 0!==B.renderOpts.collectedRevalidate&&!(B.renderOpts.collectedRevalidate>=g.INFINITE_CACHE)&&B.renderOpts.collectedRevalidate,r=void 0===B.renderOpts.collectedExpire||B.renderOpts.collectedExpire>=g.INFINITE_CACHE?void 0:B.renderOpts.collectedExpire;return{value:{kind:y.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:a,expire:r}}}}catch(t){throw(null==a?void 0:a.isStale)&&await E.onRequestError(e,t,{routerKind:"App Router",routePath:f,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:N,isOnDemandRevalidate:k})},T),t}},m=await E.handleResponse({req:e,nextConfig:x,cacheKey:O,routeKind:a.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:M,isRoutePPREnabled:!1,isOnDemandRevalidate:k,revalidateOnlyGenerated:C,responseGenerator:c,waitUntil:r.waitUntil});if(!D)return null;if((null==m||null==(o=m.value)?void 0:o.kind)!==y.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==m||null==(l=m.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",k?"REVALIDATED":m.isMiss?"MISS":m.isStale?"STALE":"HIT"),b&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let R=(0,p.fromNodeOutgoingHttpHeaders)(m.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&D||R.delete(g.NEXT_CACHE_TAGS_HEADER),!m.cacheControl||t.getHeader("Cache-Control")||R.get("Cache-Control")||R.set("Cache-Control",(0,h.getCacheControlHeader)(m.cacheControl)),await (0,d.sendResponse)(q,F,new Response(m.value.body,{headers:R,status:m.value.status||200})),null};H?await o(H):await G.withPropagatedContext(e.headers,()=>G.trace(c.BaseServerSpan.handleRequest,{spanName:`${I} ${e.url}`,kind:s.SpanKind.SERVER,attributes:{"http.method":I,"http.target":e.url}},o))}catch(t){if(t instanceof m.NoFallbackError||await E.onRequestError(e,t,{routerKind:"App Router",routePath:A,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:N,isOnDemandRevalidate:k})}),D)throw t;return await (0,d.sendResponse)(q,F,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=6bf44_next_dist_esm_build_templates_app-route_71207cac.js.map