import { PreMarketGapScan, Catalyst } from '@/types/trading'
import { FMPAPI } from './fmp'
import { PolygonAPI } from './polygon'
import { IBKRAPI } from './ibkr'
import { CatalystDetectionEngine } from './catalystDetection'

export class PreMarketGapScanner {
  private fmpAPI: FMPAPI
  private polygonAPI: PolygonAPI
  private ibkrAPI: IBKRAPI
  private catalystEngine: CatalystDetectionEngine
  private useIBKR: boolean
  
  // Default universe of 65+ stocks to scan
  private readonly SCAN_UNIVERSE = [
    // Mega caps
    'AAPL', 'MSFT', 'GOOGL', 'GOOG', 'AMZN', 'NVDA', 'META', 'TSLA', 'BRK.B', 'UNH',
    'JNJ', 'XOM', 'JPM', 'V', 'PG', 'HD', 'CVX', 'MA', 'BAC', 'ABBV',
    'PFE', 'AVGO', 'KO', 'MRK', 'PEP', 'TMO', 'COST', 'DIS', 'ABT', 'ACN',
    'MCD', 'CSCO', 'LIN', 'VZ', 'ADBE', 'WMT', 'CRM', 'NFLX', 'DHR', 'NKE',
    'TXN', 'NEE', 'BMY', 'ORCL', 'PM', 'RTX', 'UPS', 'QCOM', 'T', 'LOW',
    
    // High-beta growth stocks
    'AMD', 'CRM', 'SNOW', 'PLTR', 'ROKU', 'ZM', 'DOCU', 'PTON', 'SHOP', 'SQ',
    'PYPL', 'UBER', 'LYFT', 'ABNB', 'COIN', 'RBLX', 'U', 'DKNG', 'CRWD', 'ZS',
    
    // Biotech/Pharma (catalyst-heavy)
    'GILD', 'BIIB', 'REGN', 'VRTX', 'ILMN', 'MRNA', 'BNTX', 'AMGN', 'CELG', 'ISRG'
  ]

  constructor(fmpApiKey?: string, polygonApiKey?: string, useIBKR: boolean = true) {
    this.fmpAPI = new FMPAPI(fmpApiKey)
    this.polygonAPI = new PolygonAPI(polygonApiKey)
    this.ibkrAPI = new IBKRAPI({
      host: '127.0.0.1',
      port: 4002, // IB Gateway port (4002 for paper, 4001 for live)
      clientId: 1,
      paperTrading: true
    })
    this.catalystEngine = new CatalystDetectionEngine(fmpApiKey, polygonApiKey)
    this.useIBKR = useIBKR
  }

  /**
   * Run comprehensive pre-market gap scan
   */
  async runGapScan(customUniverse?: string[]): Promise<PreMarketGapScan[]> {
    const universe = customUniverse || this.SCAN_UNIVERSE
    const results: PreMarketGapScan[] = []

    console.log(`🔍 Starting pre-market gap scan on ${universe.length} symbols...`)

    try {
      let quotes: any[] = []

      if (this.useIBKR) {
        console.log('📊 Attempting to use IBKR for market data...')
        try {
          // Try to connect to IBKR first
          await this.ibkrAPI.connect()

          // Get quotes from IBKR
          quotes = await this.getIBKRQuotes(universe)

          if (quotes.length > 0) {
            console.log(`✅ Retrieved ${quotes.length} quotes from IBKR`)
          } else {
            throw new Error('No quotes received from IBKR')
          }
        } catch (ibkrError) {
          console.warn('⚠️ IBKR connection failed, falling back to FMP:', ibkrError)
          this.useIBKR = false
        }
      }

      // Fallback to FMP if IBKR failed or not enabled
      if (!this.useIBKR || quotes.length === 0) {
        console.log('📊 Using FMP API for market data...')
        quotes = await this.fmpAPI.getMultiplePreMarketQuotes(universe)
      }

      // Process each quote in parallel
      const scanPromises = quotes.map(quote => this.processSingleStock(quote))
      const scanResults = await Promise.all(scanPromises)

      // Filter out null results and sort by gap percentage
      const validResults = scanResults
        .filter((result): result is PreMarketGapScan => result !== null)
        .sort((a, b) => b.gapPercent - a.gapPercent)

      console.log(`✅ Gap scan complete. Found ${validResults.length} results.`)

      return validResults
    } catch (error) {
      console.error('Error running gap scan:', error)
      return []
    }
  }

  /**
   * Get quotes from IBKR
   */
  private async getIBKRQuotes(symbols: string[]): Promise<any[]> {
    try {
      const quotes = []

      // Process symbols in batches to avoid overwhelming IBKR
      const batchSize = 10
      for (let i = 0; i < symbols.length; i += batchSize) {
        const batch = symbols.slice(i, i + batchSize)

        const batchPromises = batch.map(async (symbol) => {
          try {
            const quote = await this.ibkrAPI.getMarketData(symbol)
            if (quote) {
              return {
                symbol: quote.symbol,
                price: quote.last || quote.close,
                previousClose: quote.previousClose,
                change: quote.change,
                changesPercentage: quote.changePercent,
                volume: quote.volume,
                marketCap: quote.marketCap || 0,
                avgVolume: quote.avgVolume || 0,
                preMarketPrice: quote.last || quote.close,
                preMarketChange: quote.change,
                preMarketChangePercent: quote.changePercent
              }
            }
            return null
          } catch (error) {
            console.error(`Error getting IBKR quote for ${symbol}:`, error)
            return null
          }
        })

        const batchResults = await Promise.all(batchPromises)
        quotes.push(...batchResults.filter(q => q !== null))

        // Small delay between batches
        if (i + batchSize < symbols.length) {
          await new Promise(resolve => setTimeout(resolve, 100))
        }
      }

      return quotes
    } catch (error) {
      console.error('Error getting IBKR quotes:', error)
      return []
    }
  }

  /**
   * Process a single stock for gap scan criteria
   */
  private async processSingleStock(quote: any): Promise<PreMarketGapScan | null> {
    try {
      const symbol = quote.symbol
      const currentPrice = quote.preMarketPrice || quote.price
      const previousClose = quote.previousClose
      
      if (!currentPrice || !previousClose || previousClose <= 0) {
        return null
      }

      const gapPercent = ((currentPrice - previousClose) / previousClose) * 100
      
      // Quick filter: only process stocks with 3%+ gaps
      if (gapPercent < 3.0) {
        return null
      }

      // Get additional data
      const [companyProfile, catalysts] = await Promise.all([
        this.fmpAPI.getCompanyProfile(symbol),
        this.catalystEngine.detectCatalysts(symbol)
      ])

      if (!companyProfile) {
        return null
      }

      // Calculate pre-market metrics
      const preMarketVolume = quote.volume || 0
      const avgDailyVolume = quote.avgVolume || 1
      const preMarketDollarVolume = preMarketVolume * currentPrice
      const marketCap = quote.marketCap || companyProfile.mktCap || 0

      // Check all criteria
      const criteriaChecks = {
        priceAbove1Dollar: currentPrice > 1.0,
        gapAbove3Percent: gapPercent >= 3.0,
        marketCapAbove800M: marketCap >= *********,
        preMarketVolumeAbove20K: preMarketVolume >= 20000,
        preMarketDollarVolumeAbove1M: preMarketDollarVolume >= 1000000,
        excludesPennyStocks: currentPrice > 1.0 && marketCap >= *********,
        hasCatalyst: catalysts.length > 0
      }

      const meetsAllCriteria = Object.values(criteriaChecks).every(check => check)

      // Get the best catalyst (highest quality score)
      const bestCatalyst = catalysts.length > 0 
        ? catalysts.reduce((best, current) => 
            current.qualityScore > best.qualityScore ? current : best
          )
        : undefined

      const result: PreMarketGapScan = {
        symbol,
        name: companyProfile.companyName || symbol,
        sector: companyProfile.sector || 'Unknown',
        price: currentPrice,
        previousClose,
        gapPercent,
        preMarketHigh: currentPrice, // Simplified - would need intraday data for actual PMH
        preMarketLow: currentPrice * 0.98, // Estimated based on current price
        preMarketVolume,
        preMarketDollarVolume,
        marketCap,
        averageDailyVolume: avgDailyVolume,
        catalyst: bestCatalyst,
        scanTime: new Date().toISOString(),
        meetsAllCriteria,
        criteriaChecks
      }

      return result
    } catch (error) {
      console.error(`Error processing ${quote.symbol}:`, error)
      return null
    }
  }

  /**
   * Get filtered results that meet all Perfect-Pick criteria
   */
  async getPerfectPickCandidates(customUniverse?: string[]): Promise<PreMarketGapScan[]> {
    const allResults = await this.runGapScan(customUniverse)
    
    return allResults.filter(result => 
      result.meetsAllCriteria && 
      result.catalyst && 
      result.catalyst.tier === 'tier_1' // Only highest tier catalysts
    )
  }

  /**
   * Get results by gap percentage ranges
   */
  async getGapRangeResults(
    minGap: number = 3, 
    maxGap: number = 15, 
    customUniverse?: string[]
  ): Promise<PreMarketGapScan[]> {
    const allResults = await this.runGapScan(customUniverse)
    
    return allResults.filter(result => 
      result.gapPercent >= minGap && 
      result.gapPercent <= maxGap
    )
  }

  /**
   * Get results by catalyst type
   */
  async getCatalystTypeResults(
    catalystTypes: string[], 
    customUniverse?: string[]
  ): Promise<PreMarketGapScan[]> {
    const allResults = await this.runGapScan(customUniverse)
    
    return allResults.filter(result => 
      result.catalyst && 
      catalystTypes.includes(result.catalyst.type)
    )
  }

  /**
   * Get scheduled scan times (4 AM, 6 AM, 8 AM, 9 AM EST)
   */
  getScheduledScanTimes(): Date[] {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    
    const scanTimes = [
      new Date(today.getTime() + 4 * 60 * 60 * 1000), // 4 AM EST
      new Date(today.getTime() + 6 * 60 * 60 * 1000), // 6 AM EST
      new Date(today.getTime() + 8 * 60 * 60 * 1000), // 8 AM EST
      new Date(today.getTime() + 9 * 60 * 60 * 1000)  // 9 AM EST
    ]
    
    // Adjust for EST (UTC-5) or EDT (UTC-4)
    const isEDT = this.isDaylightSavingTime(now)
    const offsetHours = isEDT ? 4 : 5
    
    return scanTimes.map(time => 
      new Date(time.getTime() + offsetHours * 60 * 60 * 1000)
    )
  }

  /**
   * Check if current time is during daylight saving time
   */
  private isDaylightSavingTime(date: Date): boolean {
    const year = date.getFullYear()
    
    // DST starts second Sunday in March
    const dstStart = new Date(year, 2, 1) // March 1st
    dstStart.setDate(dstStart.getDate() + (7 - dstStart.getDay()) + 7) // Second Sunday
    
    // DST ends first Sunday in November
    const dstEnd = new Date(year, 10, 1) // November 1st
    dstEnd.setDate(dstEnd.getDate() + (7 - dstEnd.getDay())) // First Sunday
    
    return date >= dstStart && date < dstEnd
  }

  /**
   * Get real-time updates for existing scan results
   */
  async updateScanResults(existingResults: PreMarketGapScan[]): Promise<PreMarketGapScan[]> {
    const symbols = existingResults.map(result => result.symbol)
    const updatedQuotes = await this.fmpAPI.getMultiplePreMarketQuotes(symbols)
    
    const updatedResults: PreMarketGapScan[] = []
    
    for (const quote of updatedQuotes) {
      const existingResult = existingResults.find(r => r.symbol === quote.symbol)
      if (!existingResult) continue
      
      const currentPrice = quote.preMarketPrice || quote.price
      const gapPercent = ((currentPrice - quote.previousClose) / quote.previousClose) * 100
      
      const updatedResult: PreMarketGapScan = {
        ...existingResult,
        price: currentPrice,
        gapPercent,
        preMarketVolume: quote.volume || 0,
        preMarketDollarVolume: (quote.volume || 0) * currentPrice,
        scanTime: new Date().toISOString()
      }
      
      // Re-check criteria with updated data
      updatedResult.criteriaChecks.gapAbove3Percent = gapPercent >= 3.0
      updatedResult.criteriaChecks.preMarketVolumeAbove20K = updatedResult.preMarketVolume >= 20000
      updatedResult.criteriaChecks.preMarketDollarVolumeAbove1M = updatedResult.preMarketDollarVolume >= 1000000
      updatedResult.meetsAllCriteria = Object.values(updatedResult.criteriaChecks).every(check => check)
      
      updatedResults.push(updatedResult)
    }
    
    return updatedResults.sort((a, b) => b.gapPercent - a.gapPercent)
  }

  /**
   * Get summary statistics for scan results
   */
  getScanSummary(results: PreMarketGapScan[]) {
    const totalScanned = this.SCAN_UNIVERSE.length
    const gapsFound = results.length
    const perfectPicks = results.filter(r => r.meetsAllCriteria).length
    const withCatalysts = results.filter(r => r.catalyst).length
    
    const avgGap = results.length > 0 
      ? results.reduce((sum, r) => sum + r.gapPercent, 0) / results.length 
      : 0
    
    const sectorBreakdown = results.reduce((acc, result) => {
      acc[result.sector] = (acc[result.sector] || 0) + 1
      return acc
    }, {} as Record<string, number>)
    
    const catalystTypeBreakdown = results
      .filter(r => r.catalyst)
      .reduce((acc, result) => {
        const type = result.catalyst!.type
        acc[type] = (acc[type] || 0) + 1
        return acc
      }, {} as Record<string, number>)
    
    return {
      totalScanned,
      gapsFound,
      perfectPicks,
      withCatalysts,
      avgGap: Math.round(avgGap * 100) / 100,
      sectorBreakdown,
      catalystTypeBreakdown,
      scanTime: new Date().toISOString()
    }
  }
}
