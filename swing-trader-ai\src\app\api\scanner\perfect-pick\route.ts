import { NextRequest, NextResponse } from 'next/server'
import { PerfectPickTradingSystem } from '@/lib/perfectPickTradingSystem'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    const accountSize = parseInt(searchParams.get('accountSize') || '100000')
    const riskPercent = parseFloat(searchParams.get('riskPercent') || '2')
    const customUniverse = searchParams.get('universe')?.split(',').filter(Boolean)
    const limit = parseInt(searchParams.get('limit') || '20')

    console.log('🎯 Perfect-Pick API called with params:', {
      accountSize,
      riskPercent,
      customUniverse: customUniverse?.length || 'default',
      limit
    })

    // Initialize Perfect-Pick Trading System
    const perfectPickSystem = new PerfectPickTradingSystem(
      process.env.FMP_API_KEY,
      process.env.POLYGON_API_KEY
    )

    // Run the complete Perfect-Pick scan
    const setups = await perfectPickSystem.runPerfectPickScan(
      accountSize,
      riskPercent,
      customUniverse
    )

    // Limit results
    const limitedSetups = setups.slice(0, limit)

    // Get summary statistics
    const summary = perfectPickSystem.getSetupSummary(limitedSetups)

    const response = {
      success: true,
      data: {
        setups: limitedSetups,
        summary,
        scanParams: {
          accountSize,
          riskPercent,
          universeSize: customUniverse?.length || 'default',
          limit
        },
        timestamp: new Date().toISOString()
      }
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error in Perfect-Pick API:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to run Perfect-Pick scan',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, data } = body

    const perfectPickSystem = new PerfectPickTradingSystem(
      process.env.FMP_API_KEY,
      process.env.POLYGON_API_KEY
    )

    switch (action) {
      case 'update_setups':
        const updatedSetups = await perfectPickSystem.updatePerfectPickSetups(data.setups)
        return NextResponse.json({
          success: true,
          data: { setups: updatedSetups }
        })

      case 'generate_entry_trigger':
        const entryTrigger = await perfectPickSystem.generateEntryTrigger(
          data.symbol,
          data.preMarketHigh
        )
        return NextResponse.json({
          success: true,
          data: { entryTrigger }
        })

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Error in Perfect-Pick POST API:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process Perfect-Pick request',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
