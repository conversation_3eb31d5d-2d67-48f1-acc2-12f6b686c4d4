module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},14747,(e,t,r)=>{t.exports=e.x("path",()=>require("path"))},78006,e=>{"use strict";e.s(["PolygonAPI",()=>s]);var t=e.i(55362);let r="https://api.polygon.io",a=process.env.POLYGON_API_KEY;class s{apiKey;constructor(e){if(this.apiKey=e||a||"",!this.apiKey)throw Error("Polygon API key is required")}async getStockQuote(e){try{let a=(await t.default.get(`${r}/v2/snapshot/locale/us/markets/stocks/tickers/${e}`,{params:{apikey:this.apiKey}})).data.results[0];if(!a)throw Error(`No data found for ${e}`);let s=a.value||a,n=s.day||{},o=s.prevDay||{};s.lastQuote;let i=(s.lastTrade||{}).p||n.c||o.c,l=o.c||n.o,u=i-l;return{symbol:e.toUpperCase(),name:s.name||e.toUpperCase(),price:i,change:u,changePercent:u/l*100,volume:n.v||0,marketCap:s.market_cap,pe:void 0,dividend:void 0}}catch(a){console.error("Error fetching stock quote from Polygon:",a);try{let a=(await t.default.get(`${r}/v2/aggs/ticker/${e}/prev`,{params:{adjusted:"true",apikey:this.apiKey}})).data.results[0];return{symbol:e.toUpperCase(),name:e.toUpperCase(),price:a.c,change:a.c-a.o,changePercent:(a.c-a.o)/a.o*100,volume:a.v,marketCap:void 0,pe:void 0,dividend:void 0}}catch(t){throw console.error("Polygon fallback also failed:",t),Error(`Failed to fetch quote for ${e}`)}}}async getHistoricalData(e,a="day",s=1,n,o){try{let i=await t.default.get(`${r}/v2/aggs/ticker/${e}/range/${s}/${a}/${n}/${o}`,{params:{adjusted:"true",sort:"asc",limit:5e4,apikey:this.apiKey}});if(!i.data.results||0===i.data.results.length)return console.warn(`No historical data found for ${e}`),[];return i.data.results.map(e=>({timestamp:e.t,open:e.o,high:e.h,low:e.l,close:e.c,volume:e.v}))}catch(t){throw console.error(`Error fetching historical data for ${e}:`,t),t.response&&(console.error(`Polygon API Error: ${t.response.status} - ${t.response.statusText}`),console.error("Response data:",t.response.data)),Error(`Failed to fetch historical data for ${e}: ${t.message}`)}}async getCompanyDetails(e){try{return(await t.default.get(`${r}/v3/reference/tickers/${e}`,{params:{apikey:this.apiKey}})).data.results}catch(e){return console.error("Error fetching company details:",e),null}}async getMarketStatus(){try{return(await t.default.get(`${r}/v1/marketstatus/now`,{params:{apikey:this.apiKey}})).data}catch(e){return console.error("Error fetching market status:",e),null}}async searchStocks(e,a=10){try{return(await t.default.get(`${r}/v3/reference/tickers`,{params:{search:e,market:"stocks",active:"true",limit:a,apikey:this.apiKey}})).data.results||[]}catch(e){return console.error("Error searching stocks:",e),[]}}}new s},58445,e=>{"use strict";e.s(["TechnicalIndicators",()=>t]);class t{static sma(e,t){let r=[];for(let a=t-1;a<e.length;a++){let s=e.slice(a-t+1,a+1).reduce((e,t)=>e+t,0);r.push(s/t)}return r}static ema(e,t){let r=[],a=2/(t+1),s=e.slice(0,t).reduce((e,t)=>e+t,0)/t;r.push(s);for(let n=t;n<e.length;n++)r.push(s=e[n]*a+s*(1-a));return r}static rsi(e,t=14){let r=[],a=[];for(let t=1;t<e.length;t++){let s=e[t]-e[t-1];r.push(s>0?s:0),a.push(s<0?Math.abs(s):0)}let s=this.sma(r,t),n=this.sma(a,t);return s.map((e,t)=>100-100/(1+e/n[t]))}static macd(e,t=12,r=26,a=9){let s=this.ema(e,t),n=this.ema(e,r),o=s.slice(r-t).map((e,t)=>e-n[t]),i=this.ema(o,a),l=o.slice(a-1).map((e,t)=>e-i[t]);return{macd:o,signal:i,histogram:l}}static bollingerBands(e,t=20,r=2){return this.sma(e,t).map((a,s)=>{let n=Math.sqrt(e.slice(s,s+t).reduce((e,t)=>e+Math.pow(t-a,2),0)/t);return{upper:a+n*r,middle:a,lower:a-n*r}})}static findSupportResistance(e,t=20){let r=e.map(e=>e.high),a=e.map(e=>e.low),s=[],n=[];for(let o=t;o<e.length-t;o++){let e=r[o],i=a[o],l=r.slice(o-t,o).every(t=>t<=e)&&r.slice(o+1,o+t+1).every(t=>t<=e),u=a.slice(o-t,o).every(e=>e>=i)&&a.slice(o+1,o+t+1).every(e=>e>=i);l&&s.push(e),u&&n.push(i)}return{support:n,resistance:s}}static volumeAnalysis(e,t=20){let r=e.map(e=>e.volume),a=this.sma(r,t),s=r[r.length-1],n=a[a.length-1];return{currentVolume:s,averageVolume:n,volumeRatio:s/n,isHighVolume:s>1.5*n,isLowVolume:s<.5*n}}static analyzeSwingSetup(e){let t=e.map(e=>e.close),r=[],a=this.rsi(t),s=a[a.length-1],n="NEUTRAL",o=`RSI: ${s.toFixed(2)}`;s<30?(n="BUY",o+=" - Oversold condition, potential bounce"):s>70?(n="SELL",o+=" - Overbought condition, potential pullback"):o+=" - Neutral zone",r.push({name:"RSI",value:s,signal:n,description:o});let i=this.sma(t,20),l=this.sma(t,50),u=t[t.length-1],c=i[i.length-1],p=l[l.length-1],d="NEUTRAL",h=`Price vs SMA20: ${((u/c-1)*100).toFixed(2)}%`;u>c&&c>p?(d="BUY",h+=" - Bullish trend"):u<c&&c<p?(d="SELL",h+=" - Bearish trend"):h+=" - Mixed signals",r.push({name:"Moving Averages",value:(u/c-1)*100,signal:d,description:h});let m=this.macd(t),g=m.macd[m.macd.length-1],y=m.signal[m.signal.length-1],v=m.histogram[m.histogram.length-1],f="NEUTRAL",x=`MACD: ${g.toFixed(4)}, Signal: ${y.toFixed(4)}`;g>y&&v>0?(f="BUY",x+=" - Bullish momentum"):g<y&&v<0?(f="SELL",x+=" - Bearish momentum"):x+=" - Momentum shifting",r.push({name:"MACD",value:v,signal:f,description:x});let w=this.volumeAnalysis(e),R="NEUTRAL",E=`Volume: ${(100*w.volumeRatio).toFixed(0)}% of average`;return w.isHighVolume?(R="BUY",E+=" - High volume confirms move"):w.isLowVolume?(R="SELL",E+=" - Low volume, weak conviction"):E+=" - Normal volume",r.push({name:"Volume",value:w.volumeRatio,signal:R,description:E}),r}}},92724,(e,t,r)=>{},94725,e=>{"use strict";e.s(["handler",()=>$,"patchFetch",()=>N,"routeModule",()=>A,"serverHooks",()=>P,"workAsyncStorage",()=>C,"workUnitAsyncStorage",()=>b],94725);var t=e.i(11971),r=e.i(6780),a=e.i(51842),s=e.i(62950),n=e.i(21346),o=e.i(30506),i=e.i(63077),l=e.i(34765),u=e.i(64182),c=e.i(85062),p=e.i(51548),d=e.i(95133),h=e.i(8819),m=e.i(41050),g=e.i(93695);e.i(96641);var y=e.i(3893);e.s(["GET",()=>E],98446);var v=e.i(59169),f=e.i(78006),x=e.i(17673),w=e.i(86678),R=e.i(97669);async function E(e,{params:t}){try{let{symbol:r}=await t,{searchParams:a}=new URL(e.url),s=a.get("timeframe")||"1D",n=parseInt(a.get("days")||"100");if(!r)return v.NextResponse.json({error:"Symbol parameter is required"},{status:400});let o=new f.PolygonAPI(process.env.POLYGON_API_KEY),i=(0,w.format)(new Date,"yyyy-MM-dd"),l=(0,w.format)((0,R.subDays)(new Date,n),"yyyy-MM-dd"),u=await o.getHistoricalData(r.toUpperCase(),"day",1,l,i);if(u.length<50)return v.NextResponse.json({error:"Insufficient historical data for analysis"},{status:400});let c=x.SwingTradingAnalyzer.analyzeSwingTrade(r.toUpperCase(),u,s);return v.NextResponse.json(c)}catch(e){return console.error("Error in swing analysis API:",e),v.NextResponse.json({error:"Failed to perform swing trading analysis"},{status:500})}}var k=e.i(98446);let A=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/analysis/swing/[symbol]/route",pathname:"/api/analysis/swing/[symbol]",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/swing-trader-ai/src/app/api/analysis/swing/[symbol]/route.ts",nextConfigOutput:"",userland:k}),{workAsyncStorage:C,workUnitAsyncStorage:b,serverHooks:P}=A;function N(){return(0,a.patchFetch)({workAsyncStorage:C,workUnitAsyncStorage:b})}async function $(e,t,a){var v;let f="/api/analysis/swing/[symbol]/route";f=f.replace(/\/index$/,"")||"/";let x=await A.prepare(e,t,{srcPage:f,multiZoneDraftMode:!1});if(!x)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:w,params:R,nextConfig:E,isDraftMode:k,prerenderManifest:C,routerServerContext:b,isOnDemandRevalidate:P,revalidateOnlyGenerated:N,resolvedPathname:$}=x,S=(0,o.normalizeAppPath)(f),T=!!(C.dynamicRoutes[S]||C.routes[$]);if(T&&!k){let e=!!C.routes[$],t=C.dynamicRoutes[S];if(t&&!1===t.fallback&&!e)throw new g.NoFallbackError}let U=null;!T||A.isDev||k||(U="/index"===(U=$)?"/":U);let q=!0===A.isDev||!T,M=T&&!q,O=e.method||"GET",I=(0,n.getTracer)(),j=I.getActiveScopeSpan(),_={params:R,prerenderManifest:C,renderOpts:{experimental:{cacheComponents:!!E.experimental.cacheComponents,authInterrupts:!!E.experimental.authInterrupts},supportsDynamicResponse:q,incrementalCache:(0,s.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(v=E.experimental)?void 0:v.cacheLife,isRevalidate:M,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>A.onRequestError(e,t,a,b)},sharedContext:{buildId:w}},L=new i.NodeNextRequest(e),D=new i.NodeNextResponse(t),H=l.NextRequestAdapter.fromNodeNextRequest(L,(0,l.signalFromNodeResponse)(t));try{let o=async r=>A.handle(H,_).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=I.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==u.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let s=a.get("next.route");if(s){let e=`${O} ${s}`;r.setAttributes({"next.route":s,"http.route":s,"next.span_name":e}),r.updateName(e)}else r.updateName(`${O} ${e.url}`)}),i=async n=>{var i,l;let u=async({previousCacheEntry:r})=>{try{if(!(0,s.getRequestMeta)(e,"minimalMode")&&P&&N&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let i=await o(n);e.fetchMetrics=_.renderOpts.fetchMetrics;let l=_.renderOpts.pendingWaitUntil;l&&a.waitUntil&&(a.waitUntil(l),l=void 0);let u=_.renderOpts.collectedTags;if(!T)return await (0,p.sendResponse)(L,D,i,_.renderOpts.pendingWaitUntil),null;{let e=await i.blob(),t=(0,d.toNodeOutgoingHttpHeaders)(i.headers);u&&(t[m.NEXT_CACHE_TAGS_HEADER]=u),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==_.renderOpts.collectedRevalidate&&!(_.renderOpts.collectedRevalidate>=m.INFINITE_CACHE)&&_.renderOpts.collectedRevalidate,a=void 0===_.renderOpts.collectedExpire||_.renderOpts.collectedExpire>=m.INFINITE_CACHE?void 0:_.renderOpts.collectedExpire;return{value:{kind:y.CachedRouteKind.APP_ROUTE,status:i.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await A.onRequestError(e,t,{routerKind:"App Router",routePath:f,routeType:"route",revalidateReason:(0,c.getRevalidateReason)({isRevalidate:M,isOnDemandRevalidate:P})},b),t}},g=await A.handleResponse({req:e,nextConfig:E,cacheKey:U,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:C,isRoutePPREnabled:!1,isOnDemandRevalidate:P,revalidateOnlyGenerated:N,responseGenerator:u,waitUntil:a.waitUntil});if(!T)return null;if((null==g||null==(i=g.value)?void 0:i.kind)!==y.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==g||null==(l=g.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,s.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",P?"REVALIDATED":g.isMiss?"MISS":g.isStale?"STALE":"HIT"),k&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let v=(0,d.fromNodeOutgoingHttpHeaders)(g.value.headers);return(0,s.getRequestMeta)(e,"minimalMode")&&T||v.delete(m.NEXT_CACHE_TAGS_HEADER),!g.cacheControl||t.getHeader("Cache-Control")||v.get("Cache-Control")||v.set("Cache-Control",(0,h.getCacheControlHeader)(g.cacheControl)),await (0,p.sendResponse)(L,D,new Response(g.value.body,{headers:v,status:g.value.status||200})),null};j?await i(j):await I.withPropagatedContext(e.headers,()=>I.trace(u.BaseServerSpan.handleRequest,{spanName:`${O} ${e.url}`,kind:n.SpanKind.SERVER,attributes:{"http.method":O,"http.target":e.url}},i))}catch(t){if(t instanceof g.NoFallbackError||await A.onRequestError(e,t,{routerKind:"App Router",routePath:S,routeType:"route",revalidateReason:(0,c.getRevalidateReason)({isRevalidate:M,isOnDemandRevalidate:P})}),T)throw t;return await (0,p.sendResponse)(L,D,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__248750bc._.js.map