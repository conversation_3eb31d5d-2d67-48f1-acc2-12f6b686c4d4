{"version": 3, "sources": ["turbopack:///[project]/swing-trader-ai/node_modules/date-fns/locale/en-US/_lib/formatDistance.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/locale/_lib/buildFormatLongFn.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/locale/en-US/_lib/formatLong.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/locale/en-US/_lib/formatRelative.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/locale/_lib/buildLocalizeFn.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/locale/en-US/_lib/localize.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/locale/_lib/buildMatchFn.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/locale/_lib/buildMatchPatternFn.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/locale/en-US/_lib/match.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/locale/en-US.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/_lib/defaultOptions.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/constants.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/constructFrom.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/toDate.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/_lib/getTimezoneOffsetInMilliseconds.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/_lib/normalizeDates.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/startOfDay.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/differenceInCalendarDays.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/startOfYear.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/getDayOfYear.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/startOfWeek.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/startOfISOWeek.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/getISOWeekYear.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/startOfISOWeekYear.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/getISOWeek.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/getWeekYear.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/startOfWeekYear.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/getWeek.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/_lib/addLeadingZeros.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/_lib/format/lightFormatters.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/_lib/format/formatters.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/_lib/format/longFormatters.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/_lib/protectedTokens.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/isDate.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/isValid.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/format.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/addDays.js", "turbopack:///[project]/swing-trader-ai/node_modules/date-fns/subDays.js"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"less than a second\",\n    other: \"less than {{count}} seconds\",\n  },\n\n  xSeconds: {\n    one: \"1 second\",\n    other: \"{{count}} seconds\",\n  },\n\n  halfAMinute: \"half a minute\",\n\n  lessThanXMinutes: {\n    one: \"less than a minute\",\n    other: \"less than {{count}} minutes\",\n  },\n\n  xMinutes: {\n    one: \"1 minute\",\n    other: \"{{count}} minutes\",\n  },\n\n  aboutXHours: {\n    one: \"about 1 hour\",\n    other: \"about {{count}} hours\",\n  },\n\n  xHours: {\n    one: \"1 hour\",\n    other: \"{{count}} hours\",\n  },\n\n  xDays: {\n    one: \"1 day\",\n    other: \"{{count}} days\",\n  },\n\n  aboutXWeeks: {\n    one: \"about 1 week\",\n    other: \"about {{count}} weeks\",\n  },\n\n  xWeeks: {\n    one: \"1 week\",\n    other: \"{{count}} weeks\",\n  },\n\n  aboutXMonths: {\n    one: \"about 1 month\",\n    other: \"about {{count}} months\",\n  },\n\n  xMonths: {\n    one: \"1 month\",\n    other: \"{{count}} months\",\n  },\n\n  aboutXYears: {\n    one: \"about 1 year\",\n    other: \"about {{count}} years\",\n  },\n\n  xYears: {\n    one: \"1 year\",\n    other: \"{{count}} years\",\n  },\n\n  overXYears: {\n    one: \"over 1 year\",\n    other: \"over {{count}} years\",\n  },\n\n  almostXYears: {\n    one: \"almost 1 year\",\n    other: \"almost {{count}} years\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", count.toString());\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"in \" + result;\n    } else {\n      return result + \" ago\";\n    }\n  }\n\n  return result;\n};\n", "export function buildFormatLongFn(args) {\n  return (options = {}) => {\n    // TODO: Remove String()\n    const width = options.width ? String(options.width) : args.defaultWidth;\n    const format = args.formats[width] || args.formats[args.defaultWidth];\n    return format;\n  };\n}\n", "import { buildFormatLongFn } from \"../../_lib/buildFormatLongFn.js\";\n\nconst dateFormats = {\n  full: \"EEEE, MMMM do, y\",\n  long: \"MMMM do, y\",\n  medium: \"MMM d, y\",\n  short: \"MM/dd/yyyy\",\n};\n\nconst timeFormats = {\n  full: \"h:mm:ss a zzzz\",\n  long: \"h:mm:ss a z\",\n  medium: \"h:mm:ss a\",\n  short: \"h:mm a\",\n};\n\nconst dateTimeFormats = {\n  full: \"{{date}} 'at' {{time}}\",\n  long: \"{{date}} 'at' {{time}}\",\n  medium: \"{{date}}, {{time}}\",\n  short: \"{{date}}, {{time}}\",\n};\n\nexport const formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\",\n  }),\n\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\",\n  }),\n\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"full\",\n  }),\n};\n", "const formatRelativeLocale = {\n  lastWeek: \"'last' eeee 'at' p\",\n  yesterday: \"'yesterday at' p\",\n  today: \"'today at' p\",\n  tomorrow: \"'tomorrow at' p\",\n  nextWeek: \"eeee 'at' p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n", "/**\n * The localize function argument callback which allows to convert raw value to\n * the actual type.\n *\n * @param value - The value to convert\n *\n * @returns The converted value\n */\n\n/**\n * The map of localized values for each width.\n */\n\n/**\n * The index type of the locale unit value. It types conversion of units of\n * values that don't start at 0 (i.e. quarters).\n */\n\n/**\n * Converts the unit value to the tuple of values.\n */\n\n/**\n * The tuple of localized era values. The first element represents BC,\n * the second element represents AD.\n */\n\n/**\n * The tuple of localized quarter values. The first element represents Q1.\n */\n\n/**\n * The tuple of localized day values. The first element represents Sunday.\n */\n\n/**\n * The tuple of localized month values. The first element represents January.\n */\n\nexport function buildLocalizeFn(args) {\n  return (value, options) => {\n    const context = options?.context ? String(options.context) : \"standalone\";\n\n    let valuesArray;\n    if (context === \"formatting\" && args.formattingValues) {\n      const defaultWidth = args.defaultFormattingWidth || args.defaultWidth;\n      const width = options?.width ? String(options.width) : defaultWidth;\n\n      valuesArray =\n        args.formattingValues[width] || args.formattingValues[defaultWidth];\n    } else {\n      const defaultWidth = args.defaultWidth;\n      const width = options?.width ? String(options.width) : args.defaultWidth;\n\n      valuesArray = args.values[width] || args.values[defaultWidth];\n    }\n    const index = args.argumentCallback ? args.argumentCallback(value) : value;\n\n    // @ts-expect-error - For some reason TypeScript just don't want to match it, no matter how hard we try. I challenge you to try to remove it!\n    return valuesArray[index];\n  };\n}\n", "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\nconst eraValues = {\n  narrow: [\"B\", \"A\"],\n  abbreviated: [\"BC\", \"AD\"],\n  wide: [\"Before Christ\", \"Ann<PERSON> Domini\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"1st quarter\", \"2nd quarter\", \"3rd quarter\", \"4th quarter\"],\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nconst monthValues = {\n  narrow: [\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"],\n  abbreviated: [\n    \"Jan\",\n    \"Feb\",\n    \"Mar\",\n    \"Apr\",\n    \"May\",\n    \"Jun\",\n    \"Jul\",\n    \"Aug\",\n    \"Sep\",\n    \"Oct\",\n    \"Nov\",\n    \"Dec\",\n  ],\n\n  wide: [\n    \"January\",\n    \"February\",\n    \"March\",\n    \"April\",\n    \"May\",\n    \"June\",\n    \"July\",\n    \"August\",\n    \"September\",\n    \"October\",\n    \"November\",\n    \"December\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"S\", \"M\", \"T\", \"W\", \"T\", \"F\", \"S\"],\n  short: [\"Su\", \"Mo\", \"Tu\", \"We\", \"Th\", \"Fr\", \"Sa\"],\n  abbreviated: [\"Sun\", \"Mon\", \"Tue\", \"Wed\", \"Thu\", \"Fri\", \"Sat\"],\n  wide: [\n    \"Sunday\",\n    \"Monday\",\n    \"Tuesday\",\n    \"Wednesday\",\n    \"Thursday\",\n    \"Friday\",\n    \"Saturday\",\n  ],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"mi\",\n    noon: \"n\",\n    morning: \"morning\",\n    afternoon: \"afternoon\",\n    evening: \"evening\",\n    night: \"night\",\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"midnight\",\n    noon: \"noon\",\n    morning: \"morning\",\n    afternoon: \"afternoon\",\n    evening: \"evening\",\n    night: \"night\",\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"midnight\",\n    noon: \"noon\",\n    morning: \"morning\",\n    afternoon: \"afternoon\",\n    evening: \"evening\",\n    night: \"night\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"mi\",\n    noon: \"n\",\n    morning: \"in the morning\",\n    afternoon: \"in the afternoon\",\n    evening: \"in the evening\",\n    night: \"at night\",\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"midnight\",\n    noon: \"noon\",\n    morning: \"in the morning\",\n    afternoon: \"in the afternoon\",\n    evening: \"in the evening\",\n    night: \"at night\",\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"midnight\",\n    noon: \"noon\",\n    morning: \"in the morning\",\n    afternoon: \"in the afternoon\",\n    evening: \"in the evening\",\n    night: \"at night\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n\n  // If ordinal numbers depend on context, for example,\n  // if they are different for different grammatical genders,\n  // use `options.unit`.\n  //\n  // `unit` can be 'year', 'quarter', 'month', 'week', 'date', 'dayOfYear',\n  // 'day', 'hour', 'minute', 'second'.\n\n  const rem100 = number % 100;\n  if (rem100 > 20 || rem100 < 10) {\n    switch (rem100 % 10) {\n      case 1:\n        return number + \"st\";\n      case 2:\n        return number + \"nd\";\n      case 3:\n        return number + \"rd\";\n    }\n  }\n  return number + \"th\";\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n", "export function buildMatchFn(args) {\n  return (string, options = {}) => {\n    const width = options.width;\n\n    const matchPattern =\n      (width && args.matchPatterns[width]) ||\n      args.matchPatterns[args.defaultMatchWidth];\n    const matchResult = string.match(matchPattern);\n\n    if (!matchResult) {\n      return null;\n    }\n    const matchedString = matchResult[0];\n\n    const parsePatterns =\n      (width && args.parsePatterns[width]) ||\n      args.parsePatterns[args.defaultParseWidth];\n\n    const key = Array.isArray(parsePatterns)\n      ? findIndex(parsePatterns, (pattern) => pattern.test(matchedString))\n      : // [TODO] -- I challenge you to fix the type\n        findKey(parsePatterns, (pattern) => pattern.test(matchedString));\n\n    let value;\n\n    value = args.valueCallback ? args.valueCallback(key) : key;\n    value = options.valueCallback\n      ? // [TODO] -- I challenge you to fix the type\n        options.valueCallback(value)\n      : value;\n\n    const rest = string.slice(matchedString.length);\n\n    return { value, rest };\n  };\n}\n\nfunction findKey(object, predicate) {\n  for (const key in object) {\n    if (\n      Object.prototype.hasOwnProperty.call(object, key) &&\n      predicate(object[key])\n    ) {\n      return key;\n    }\n  }\n  return undefined;\n}\n\nfunction findIndex(array, predicate) {\n  for (let key = 0; key < array.length; key++) {\n    if (predicate(array[key])) {\n      return key;\n    }\n  }\n  return undefined;\n}\n", "export function buildMatchPatternFn(args) {\n  return (string, options = {}) => {\n    const matchResult = string.match(args.matchPattern);\n    if (!matchResult) return null;\n    const matchedString = matchResult[0];\n\n    const parseResult = string.match(args.parsePattern);\n    if (!parseResult) return null;\n    let value = args.valueCallback\n      ? args.valueCallback(parseResult[0])\n      : parseResult[0];\n\n    // [TODO] I challenge you to fix the type\n    value = options.valueCallback ? options.valueCallback(value) : value;\n\n    const rest = string.slice(matchedString.length);\n\n    return { value, rest };\n  };\n}\n", "import { buildMatchFn } from \"../../_lib/buildMatchFn.js\";\nimport { buildMatchPatternFn } from \"../../_lib/buildMatchPatternFn.js\";\n\nconst matchOrdinalNumberPattern = /^(\\d+)(th|st|nd|rd)?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\n\nconst matchEraPatterns = {\n  narrow: /^(b|a)/i,\n  abbreviated: /^(b\\.?\\s?c\\.?|b\\.?\\s?c\\.?\\s?e\\.?|a\\.?\\s?d\\.?|c\\.?\\s?e\\.?)/i,\n  wide: /^(before christ|before common era|anno domini|common era)/i,\n};\nconst parseEraPatterns = {\n  any: [/^b/i, /^(a|c)/i],\n};\n\nconst matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^q[1234]/i,\n  wide: /^[1234](th|st|nd|rd)? quarter/i,\n};\nconst parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i],\n};\n\nconst matchMonthPatterns = {\n  narrow: /^[jfmasond]/i,\n  abbreviated: /^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,\n  wide: /^(january|february|march|april|may|june|july|august|september|october|november|december)/i,\n};\nconst parseMonthPatterns = {\n  narrow: [\n    /^j/i,\n    /^f/i,\n    /^m/i,\n    /^a/i,\n    /^m/i,\n    /^j/i,\n    /^j/i,\n    /^a/i,\n    /^s/i,\n    /^o/i,\n    /^n/i,\n    /^d/i,\n  ],\n\n  any: [\n    /^ja/i,\n    /^f/i,\n    /^mar/i,\n    /^ap/i,\n    /^may/i,\n    /^jun/i,\n    /^jul/i,\n    /^au/i,\n    /^s/i,\n    /^o/i,\n    /^n/i,\n    /^d/i,\n  ],\n};\n\nconst matchDayPatterns = {\n  narrow: /^[smtwf]/i,\n  short: /^(su|mo|tu|we|th|fr|sa)/i,\n  abbreviated: /^(sun|mon|tue|wed|thu|fri|sat)/i,\n  wide: /^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i,\n};\nconst parseDayPatterns = {\n  narrow: [/^s/i, /^m/i, /^t/i, /^w/i, /^t/i, /^f/i, /^s/i],\n  any: [/^su/i, /^m/i, /^tu/i, /^w/i, /^th/i, /^f/i, /^sa/i],\n};\n\nconst matchDayPeriodPatterns = {\n  narrow: /^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,\n  any: /^([ap]\\.?\\s?m\\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i,\n};\nconst parseDayPeriodPatterns = {\n  any: {\n    am: /^a/i,\n    pm: /^p/i,\n    midnight: /^mi/i,\n    noon: /^no/i,\n    morning: /morning/i,\n    afternoon: /afternoon/i,\n    evening: /evening/i,\n    night: /night/i,\n  },\n};\n\nexport const match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: (value) => parseInt(value, 10),\n  }),\n\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: (index) => index + 1,\n  }),\n\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"any\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\",\n  }),\n};\n", "import { formatDistance } from \"./en-US/_lib/formatDistance.js\";\nimport { formatLong } from \"./en-US/_lib/formatLong.js\";\nimport { formatRelative } from \"./en-US/_lib/formatRelative.js\";\nimport { localize } from \"./en-US/_lib/localize.js\";\nimport { match } from \"./en-US/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary English locale (United States).\n * @language English\n * @iso-639-2 eng\n * <AUTHOR> [@kossnocorp](https://github.com/kossnocorp)\n * <AUTHOR> [@leshakoss](https://github.com/leshakoss)\n */\nexport const enUS = {\n  code: \"en-US\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default enUS;\n", "let defaultOptions = {};\n\nexport function getDefaultOptions() {\n  return defaultOptions;\n}\n\nexport function setDefaultOptions(newOptions) {\n  defaultOptions = newOptions;\n}\n", "/**\n * @module constants\n * @summary Useful constants\n * @description\n * Collection of useful date constants.\n *\n * The constants could be imported from `date-fns/constants`:\n *\n * ```ts\n * import { maxTime, minTime } from \"./constants/date-fns/constants\";\n *\n * function isAllowedTime(time) {\n *   return time <= maxTime && time >= minTime;\n * }\n * ```\n */\n\n/**\n * @constant\n * @name daysInWeek\n * @summary Days in 1 week.\n */\nexport const daysInWeek = 7;\n\n/**\n * @constant\n * @name daysInYear\n * @summary Days in 1 year.\n *\n * @description\n * How many days in a year.\n *\n * One years equals 365.2425 days according to the formula:\n *\n * > Leap year occurs every 4 years, except for years that are divisible by 100 and not divisible by 400.\n * > 1 mean year = (365+1/4-1/100+1/400) days = 365.2425 days\n */\nexport const daysInYear = 365.2425;\n\n/**\n * @constant\n * @name maxTime\n * @summary Maximum allowed time.\n *\n * @example\n * import { maxTime } from \"./constants/date-fns/constants\";\n *\n * const isValid = 8640000000000001 <= maxTime;\n * //=> false\n *\n * new Date(8640000000000001);\n * //=> Invalid Date\n */\nexport const maxTime = Math.pow(10, 8) * 24 * 60 * 60 * 1000;\n\n/**\n * @constant\n * @name minTime\n * @summary Minimum allowed time.\n *\n * @example\n * import { minTime } from \"./constants/date-fns/constants\";\n *\n * const isValid = -8640000000000001 >= minTime;\n * //=> false\n *\n * new Date(-8640000000000001)\n * //=> Invalid Date\n */\nexport const minTime = -maxTime;\n\n/**\n * @constant\n * @name millisecondsInWeek\n * @summary Milliseconds in 1 week.\n */\nexport const millisecondsInWeek = 604800000;\n\n/**\n * @constant\n * @name millisecondsInDay\n * @summary Milliseconds in 1 day.\n */\nexport const millisecondsInDay = 86400000;\n\n/**\n * @constant\n * @name millisecondsInMinute\n * @summary Milliseconds in 1 minute\n */\nexport const millisecondsInMinute = 60000;\n\n/**\n * @constant\n * @name millisecondsInHour\n * @summary Milliseconds in 1 hour\n */\nexport const millisecondsInHour = 3600000;\n\n/**\n * @constant\n * @name millisecondsInSecond\n * @summary Milliseconds in 1 second\n */\nexport const millisecondsInSecond = 1000;\n\n/**\n * @constant\n * @name minutesInYear\n * @summary Minutes in 1 year.\n */\nexport const minutesInYear = 525600;\n\n/**\n * @constant\n * @name minutesInMonth\n * @summary Minutes in 1 month.\n */\nexport const minutesInMonth = 43200;\n\n/**\n * @constant\n * @name minutesInDay\n * @summary Minutes in 1 day.\n */\nexport const minutesInDay = 1440;\n\n/**\n * @constant\n * @name minutesInHour\n * @summary Minutes in 1 hour.\n */\nexport const minutesInHour = 60;\n\n/**\n * @constant\n * @name monthsInQuarter\n * @summary Months in 1 quarter.\n */\nexport const monthsInQuarter = 3;\n\n/**\n * @constant\n * @name monthsInYear\n * @summary Months in 1 year.\n */\nexport const monthsInYear = 12;\n\n/**\n * @constant\n * @name quartersInYear\n * @summary Quarters in 1 year\n */\nexport const quartersInYear = 4;\n\n/**\n * @constant\n * @name secondsInHour\n * @summary Seconds in 1 hour.\n */\nexport const secondsInHour = 3600;\n\n/**\n * @constant\n * @name secondsInMinute\n * @summary Seconds in 1 minute.\n */\nexport const secondsInMinute = 60;\n\n/**\n * @constant\n * @name secondsInDay\n * @summary Seconds in 1 day.\n */\nexport const secondsInDay = secondsInHour * 24;\n\n/**\n * @constant\n * @name secondsInWeek\n * @summary Seconds in 1 week.\n */\nexport const secondsInWeek = secondsInDay * 7;\n\n/**\n * @constant\n * @name secondsInYear\n * @summary Seconds in 1 year.\n */\nexport const secondsInYear = secondsInDay * daysInYear;\n\n/**\n * @constant\n * @name secondsInMonth\n * @summary Seconds in 1 month\n */\nexport const secondsInMonth = secondsInYear / 12;\n\n/**\n * @constant\n * @name secondsInQuarter\n * @summary Seconds in 1 quarter.\n */\nexport const secondsInQuarter = secondsInMonth * 3;\n\n/**\n * @constant\n * @name constructFromSymbol\n * @summary Symbol enabling Date extensions to inherit properties from the reference date.\n *\n * The symbol is used to enable the `constructFrom` function to construct a date\n * using a reference date and a value. It allows to transfer extra properties\n * from the reference date to the new date. It's useful for extensions like\n * [`TZDate`](https://github.com/date-fns/tz) that accept a time zone as\n * a constructor argument.\n */\nexport const constructFromSymbol = Symbol.for(\"constructDateFrom\");\n", "import { constructFromSymbol } from \"./constants.js\";\n\n/**\n * @name constructFrom\n * @category Generic Helpers\n * @summary Constructs a date using the reference date and the value\n *\n * @description\n * The function constructs a new date using the constructor from the reference\n * date and the given value. It helps to build generic functions that accept\n * date extensions.\n *\n * It defaults to `Date` if the passed reference date is a number or a string.\n *\n * Starting from v3.7.0, it allows to construct a date using `[Symbol.for(\"constructDateFrom\")]`\n * enabling to transfer extra properties from the reference date to the new date.\n * It's useful for extensions like [`TZDate`](https://github.com/date-fns/tz)\n * that accept a time zone as a constructor argument.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The reference date to take constructor from\n * @param value - The value to create the date\n *\n * @returns Date initialized using the given date and value\n *\n * @example\n * import { constructFrom } from \"./constructFrom/date-fns\";\n *\n * // A function that clones a date preserving the original type\n * function cloneDate<DateType extends Date>(date: DateType): DateType {\n *   return constructFrom(\n *     date, // Use constructor from the given date\n *     date.getTime() // Use the date value to create a new date\n *   );\n * }\n */\nexport function constructFrom(date, value) {\n  if (typeof date === \"function\") return date(value);\n\n  if (date && typeof date === \"object\" && constructFromSymbol in date)\n    return date[constructFromSymbol](value);\n\n  if (date instanceof Date) return new date.constructor(value);\n\n  return new Date(value);\n}\n\n// Fallback for modularized imports:\nexport default constructFrom;\n", "import { constructFrom } from \"./constructFrom.js\";\n\n/**\n * @name toDate\n * @category Common Helpers\n * @summary Convert the given argument to an instance of Date.\n *\n * @description\n * Convert the given argument to an instance of Date.\n *\n * If the argument is an instance of Date, the function returns its clone.\n *\n * If the argument is a number, it is treated as a timestamp.\n *\n * If the argument is none of the above, the function returns Invalid Date.\n *\n * Starting from v3.7.0, it clones a date using `[Symbol.for(\"constructDateFrom\")]`\n * enabling to transfer extra properties from the reference date to the new date.\n * It's useful for extensions like [`TZDate`](https://github.com/date-fns/tz)\n * that accept a time zone as a constructor argument.\n *\n * **Note**: *all* Date arguments passed to any *date-fns* function is processed by `toDate`.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param argument - The value to convert\n *\n * @returns The parsed date in the local time zone\n *\n * @example\n * // Clone the date:\n * const result = toDate(new Date(2014, 1, 11, 11, 30, 30))\n * //=> Tue Feb 11 2014 11:30:30\n *\n * @example\n * // Convert the timestamp to date:\n * const result = toDate(1392098430000)\n * //=> Tue Feb 11 2014 11:30:30\n */\nexport function toDate(argument, context) {\n  // [TODO] Get rid of `toDate` or `constructFrom`?\n  return constructFrom(context || argument, argument);\n}\n\n// Fallback for modularized imports:\nexport default toDate;\n", "import { toDate } from \"../toDate.js\";\n\n/**\n * Google Chrome as of 67.0.3396.87 introduced timezones with offset that includes seconds.\n * They usually appear for dates that denote time before the timezones were introduced\n * (e.g. for 'Europe/Prague' timezone the offset is GMT+00:57:44 before 1 October 1891\n * and GMT+01:00:00 after that date)\n *\n * Date#getTimezoneOffset returns the offset in minutes and would return 57 for the example above,\n * which would lead to incorrect calculations.\n *\n * This function returns the timezone offset in milliseconds that takes seconds in account.\n */\nexport function getTimezoneOffsetInMilliseconds(date) {\n  const _date = toDate(date);\n  const utcDate = new Date(\n    Date.UTC(\n      _date.getFullYear(),\n      _date.getMonth(),\n      _date.getDate(),\n      _date.getHours(),\n      _date.getMinutes(),\n      _date.getSeconds(),\n      _date.getMilliseconds(),\n    ),\n  );\n  utcDate.setUTCFullYear(_date.getFullYear());\n  return +date - +utcDate;\n}\n", "import { constructFrom } from \"../constructFrom.js\";\n\nexport function normalizeDates(context, ...dates) {\n  const normalize = constructFrom.bind(\n    null,\n    context || dates.find((date) => typeof date === \"object\"),\n  );\n  return dates.map(normalize);\n}\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link startOfDay} function options.\n */\n\n/**\n * @name startOfDay\n * @category Day Helpers\n * @summary Return the start of a day for the given date.\n *\n * @description\n * Return the start of a day for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - The options\n *\n * @returns The start of a day\n *\n * @example\n * // The start of a day for 2 September 2014 11:55:00:\n * const result = startOfDay(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 02 2014 00:00:00\n */\nexport function startOfDay(date, options) {\n  const _date = toDate(date, options?.in);\n  _date.setHours(0, 0, 0, 0);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default startOfDay;\n", "import { getTimezoneOffsetInMilliseconds } from \"./_lib/getTimezoneOffsetInMilliseconds.js\";\nimport { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { millisecondsInDay } from \"./constants.js\";\nimport { startOfDay } from \"./startOfDay.js\";\n\n/**\n * The {@link differenceInCalendarDays} function options.\n */\n\n/**\n * @name differenceInCalendarDays\n * @category Day Helpers\n * @summary Get the number of calendar days between the given dates.\n *\n * @description\n * Get the number of calendar days between the given dates. This means that the times are removed\n * from the dates and then the difference in days is calculated.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - The options object\n *\n * @returns The number of calendar days\n *\n * @example\n * // How many calendar days are between\n * // 2 July 2011 23:00:00 and 2 July 2012 00:00:00?\n * const result = differenceInCalendarDays(\n *   new Date(2012, 6, 2, 0, 0),\n *   new Date(2011, 6, 2, 23, 0)\n * )\n * //=> 366\n * // How many calendar days are between\n * // 2 July 2011 23:59:00 and 3 July 2011 00:01:00?\n * const result = differenceInCalendarDays(\n *   new Date(2011, 6, 3, 0, 1),\n *   new Date(2011, 6, 2, 23, 59)\n * )\n * //=> 1\n */\nexport function differenceInCalendarDays(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n\n  const laterStartOfDay = startOfDay(laterDate_);\n  const earlierStartOfDay = startOfDay(earlierDate_);\n\n  const laterTimestamp =\n    +laterStartOfDay - getTimezoneOffsetInMilliseconds(laterStartOfDay);\n  const earlierTimestamp =\n    +earlierStartOfDay - getTimezoneOffsetInMilliseconds(earlierStartOfDay);\n\n  // Round the number of days to the nearest integer because the number of\n  // milliseconds in a day is not constant (e.g. it's different in the week of\n  // the daylight saving time clock shift).\n  return Math.round((laterTimestamp - earlierTimestamp) / millisecondsInDay);\n}\n\n// Fallback for modularized imports:\nexport default differenceInCalendarDays;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link startOfYear} function options.\n */\n\n/**\n * @name startOfYear\n * @category Year Helpers\n * @summary Return the start of a year for the given date.\n *\n * @description\n * Return the start of a year for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - The options\n *\n * @returns The start of a year\n *\n * @example\n * // The start of a year for 2 September 2014 11:55:00:\n * const result = startOfYear(new Date(2014, 8, 2, 11, 55, 00))\n * //=> Wed Jan 01 2014 00:00:00\n */\nexport function startOfYear(date, options) {\n  const date_ = toDate(date, options?.in);\n  date_.setFullYear(date_.getFullYear(), 0, 1);\n  date_.setHours(0, 0, 0, 0);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default startOfYear;\n", "import { differenceInCalendarDays } from \"./differenceInCalendarDays.js\";\nimport { startOfYear } from \"./startOfYear.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getDayOfYear} function options.\n */\n\n/**\n * @name getDayOfYear\n * @category Day Helpers\n * @summary Get the day of the year of the given date.\n *\n * @description\n * Get the day of the year of the given date.\n *\n * @param date - The given date\n * @param options - The options\n *\n * @returns The day of year\n *\n * @example\n * // Which day of the year is 2 July 2014?\n * const result = getDayOfYear(new Date(2014, 6, 2))\n * //=> 183\n */\nexport function getDayOfYear(date, options) {\n  const _date = toDate(date, options?.in);\n  const diff = differenceInCalendarDays(_date, startOfYear(_date));\n  const dayOfYear = diff + 1;\n  return dayOfYear;\n}\n\n// Fallback for modularized imports:\nexport default getDayOfYear;\n", "import { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link startOfWeek} function options.\n */\n\n/**\n * @name startOfWeek\n * @category Week Helpers\n * @summary Return the start of a week for the given date.\n *\n * @description\n * Return the start of a week for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The start of a week\n *\n * @example\n * // The start of a week for 2 September 2014 11:55:00:\n * const result = startOfWeek(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Sun Aug 31 2014 00:00:00\n *\n * @example\n * // If the week starts on Monday, the start of the week for 2 September 2014 11:55:00:\n * const result = startOfWeek(new Date(2014, 8, 2, 11, 55, 0), { weekStartsOn: 1 })\n * //=> Mon Sep 01 2014 00:00:00\n */\nexport function startOfWeek(date, options) {\n  const defaultOptions = getDefaultOptions();\n  const weekStartsOn =\n    options?.weekStartsOn ??\n    options?.locale?.options?.weekStartsOn ??\n    defaultOptions.weekStartsOn ??\n    defaultOptions.locale?.options?.weekStartsOn ??\n    0;\n\n  const _date = toDate(date, options?.in);\n  const day = _date.getDay();\n  const diff = (day < weekStartsOn ? 7 : 0) + day - weekStartsOn;\n\n  _date.setDate(_date.getDate() - diff);\n  _date.setHours(0, 0, 0, 0);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default startOfWeek;\n", "import { startOfWeek } from \"./startOfWeek.js\";\n\n/**\n * The {@link startOfISOWeek} function options.\n */\n\n/**\n * @name startOfISOWeek\n * @category ISO Week Helpers\n * @summary Return the start of an ISO week for the given date.\n *\n * @description\n * Return the start of an ISO week for the given date.\n * The result will be in the local timezone.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The start of an ISO week\n *\n * @example\n * // The start of an ISO week for 2 September 2014 11:55:00:\n * const result = startOfISOWeek(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Mon Sep 01 2014 00:00:00\n */\nexport function startOfISOWeek(date, options) {\n  return startOfWeek(date, { ...options, weekStartsOn: 1 });\n}\n\n// Fallback for modularized imports:\nexport default startOfISOWeek;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { startOfISOWeek } from \"./startOfISOWeek.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getISOWeekYear} function options.\n */\n\n/**\n * @name getISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Get the ISO week-numbering year of the given date.\n *\n * @description\n * Get the ISO week-numbering year of the given date,\n * which always starts 3 days before the year's first Thursday.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param date - The given date\n *\n * @returns The ISO week-numbering year\n *\n * @example\n * // Which ISO-week numbering year is 2 January 2005?\n * const result = getISOWeekYear(new Date(2005, 0, 2))\n * //=> 2004\n */\nexport function getISOWeekYear(date, options) {\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n\n  const fourthOfJanuaryOfNextYear = constructFrom(_date, 0);\n  fourthOfJanuaryOfNextYear.setFullYear(year + 1, 0, 4);\n  fourthOfJanuaryOfNextYear.setHours(0, 0, 0, 0);\n  const startOfNextYear = startOfISOWeek(fourthOfJanuaryOfNextYear);\n\n  const fourthOfJanuaryOfThisYear = constructFrom(_date, 0);\n  fourthOfJanuaryOfThisYear.setFullYear(year, 0, 4);\n  fourthOfJanuaryOfThisYear.setHours(0, 0, 0, 0);\n  const startOfThisYear = startOfISOWeek(fourthOfJanuaryOfThisYear);\n\n  if (_date.getTime() >= startOfNextYear.getTime()) {\n    return year + 1;\n  } else if (_date.getTime() >= startOfThisYear.getTime()) {\n    return year;\n  } else {\n    return year - 1;\n  }\n}\n\n// Fallback for modularized imports:\nexport default getISOWeekYear;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { getISOWeekYear } from \"./getISOWeekYear.js\";\nimport { startOfISOWeek } from \"./startOfISOWeek.js\";\n\n/**\n * The {@link startOfISOWeekYear} function options.\n */\n\n/**\n * @name startOfISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Return the start of an ISO week-numbering year for the given date.\n *\n * @description\n * Return the start of an ISO week-numbering year,\n * which always starts 3 days before the year's first Thursday.\n * The result will be in the local timezone.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The start of an ISO week-numbering year\n *\n * @example\n * // The start of an ISO week-numbering year for 2 July 2005:\n * const result = startOfISOWeekYear(new Date(2005, 6, 2))\n * //=> Mon Jan 03 2005 00:00:00\n */\nexport function startOfISOWeekYear(date, options) {\n  const year = getISOWeekYear(date, options);\n  const fourthOfJanuary = constructFrom(options?.in || date, 0);\n  fourthOfJanuary.setFullYear(year, 0, 4);\n  fourthOfJanuary.setHours(0, 0, 0, 0);\n  return startOfISOWeek(fourthOfJanuary);\n}\n\n// Fallback for modularized imports:\nexport default startOfISOWeekYear;\n", "import { millisecondsInWeek } from \"./constants.js\";\nimport { startOfISOWeek } from \"./startOfISOWeek.js\";\nimport { startOfISOWeekYear } from \"./startOfISOWeekYear.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getISOWeek} function options.\n */\n\n/**\n * @name getISOWeek\n * @category ISO Week Helpers\n * @summary Get the ISO week of the given date.\n *\n * @description\n * Get the ISO week of the given date.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param date - The given date\n * @param options - The options\n *\n * @returns The ISO week\n *\n * @example\n * // Which week of the ISO-week numbering year is 2 January 2005?\n * const result = getISOWeek(new Date(2005, 0, 2))\n * //=> 53\n */\nexport function getISOWeek(date, options) {\n  const _date = toDate(date, options?.in);\n  const diff = +startOfISOWeek(_date) - +startOfISOWeekYear(_date);\n\n  // Round the number of weeks to the nearest integer because the number of\n  // milliseconds in a week is not constant (e.g. it's different in the week of\n  // the daylight saving time clock shift).\n  return Math.round(diff / millisecondsInWeek) + 1;\n}\n\n// Fallback for modularized imports:\nexport default getISOWeek;\n", "import { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { startOfWeek } from \"./startOfWeek.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getWeekYear} function options.\n */\n\n/**\n * @name getWeekYear\n * @category Week-Numbering Year Helpers\n * @summary Get the local week-numbering year of the given date.\n *\n * @description\n * Get the local week-numbering year of the given date.\n * The exact calculation depends on the values of\n * `options.weekStartsOn` (which is the index of the first day of the week)\n * and `options.firstWeekContainsDate` (which is the day of January, which is always in\n * the first week of the week-numbering year)\n *\n * Week numbering: https://en.wikipedia.org/wiki/Week#The_ISO_week_date_system\n *\n * @param date - The given date\n * @param options - An object with options.\n *\n * @returns The local week-numbering year\n *\n * @example\n * // Which week numbering year is 26 December 2004 with the default settings?\n * const result = getWeekYear(new Date(2004, 11, 26))\n * //=> 2005\n *\n * @example\n * // Which week numbering year is 26 December 2004 if week starts on Saturday?\n * const result = getWeekYear(new Date(2004, 11, 26), { weekStartsOn: 6 })\n * //=> 2004\n *\n * @example\n * // Which week numbering year is 26 December 2004 if the first week contains 4 January?\n * const result = getWeekYear(new Date(2004, 11, 26), { firstWeekContainsDate: 4 })\n * //=> 2004\n */\nexport function getWeekYear(date, options) {\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n\n  const defaultOptions = getDefaultOptions();\n  const firstWeekContainsDate =\n    options?.firstWeekContainsDate ??\n    options?.locale?.options?.firstWeekContainsDate ??\n    defaultOptions.firstWeekContainsDate ??\n    defaultOptions.locale?.options?.firstWeekContainsDate ??\n    1;\n\n  const firstWeekOfNextYear = constructFrom(options?.in || date, 0);\n  firstWeekOfNextYear.setFullYear(year + 1, 0, firstWeekContainsDate);\n  firstWeekOfNextYear.setHours(0, 0, 0, 0);\n  const startOfNextYear = startOfWeek(firstWeekOfNextYear, options);\n\n  const firstWeekOfThisYear = constructFrom(options?.in || date, 0);\n  firstWeekOfThisYear.setFullYear(year, 0, firstWeekContainsDate);\n  firstWeekOfThisYear.setHours(0, 0, 0, 0);\n  const startOfThisYear = startOfWeek(firstWeekOfThisYear, options);\n\n  if (+_date >= +startOfNextYear) {\n    return year + 1;\n  } else if (+_date >= +startOfThisYear) {\n    return year;\n  } else {\n    return year - 1;\n  }\n}\n\n// Fallback for modularized imports:\nexport default getWeekYear;\n", "import { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { getWeekYear } from \"./getWeekYear.js\";\nimport { startOfWeek } from \"./startOfWeek.js\";\n\n/**\n * The {@link startOfWeekYear} function options.\n */\n\n/**\n * @name startOfWeekYear\n * @category Week-Numbering Year Helpers\n * @summary Return the start of a local week-numbering year for the given date.\n *\n * @description\n * Return the start of a local week-numbering year.\n * The exact calculation depends on the values of\n * `options.weekStartsOn` (which is the index of the first day of the week)\n * and `options.firstWeekContainsDate` (which is the day of January, which is always in\n * the first week of the week-numbering year)\n *\n * Week numbering: https://en.wikipedia.org/wiki/Week#The_ISO_week_date_system\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The start of a week-numbering year\n *\n * @example\n * // The start of an a week-numbering year for 2 July 2005 with default settings:\n * const result = startOfWeekYear(new Date(2005, 6, 2))\n * //=> Sun Dec 26 2004 00:00:00\n *\n * @example\n * // The start of a week-numbering year for 2 July 2005\n * // if Monday is the first day of week\n * // and 4 January is always in the first week of the year:\n * const result = startOfWeekYear(new Date(2005, 6, 2), {\n *   weekStartsOn: 1,\n *   firstWeekContainsDate: 4\n * })\n * //=> Mon Jan 03 2005 00:00:00\n */\nexport function startOfWeekYear(date, options) {\n  const defaultOptions = getDefaultOptions();\n  const firstWeekContainsDate =\n    options?.firstWeekContainsDate ??\n    options?.locale?.options?.firstWeekContainsDate ??\n    defaultOptions.firstWeekContainsDate ??\n    defaultOptions.locale?.options?.firstWeekContainsDate ??\n    1;\n\n  const year = getWeekYear(date, options);\n  const firstWeek = constructFrom(options?.in || date, 0);\n  firstWeek.setFullYear(year, 0, firstWeekContainsDate);\n  firstWeek.setHours(0, 0, 0, 0);\n  const _date = startOfWeek(firstWeek, options);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default startOfWeekYear;\n", "import { millisecondsInWeek } from \"./constants.js\";\nimport { startOfWeek } from \"./startOfWeek.js\";\nimport { startOfWeekYear } from \"./startOfWeekYear.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getWeek} function options.\n */\n\n/**\n * @name getWeek\n * @category Week Helpers\n * @summary Get the local week index of the given date.\n *\n * @description\n * Get the local week index of the given date.\n * The exact calculation depends on the values of\n * `options.weekStartsOn` (which is the index of the first day of the week)\n * and `options.firstWeekContainsDate` (which is the day of January, which is always in\n * the first week of the week-numbering year)\n *\n * Week numbering: https://en.wikipedia.org/wiki/Week#The_ISO_week_date_system\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The week\n *\n * @example\n * // Which week of the local week numbering year is 2 January 2005 with default options?\n * const result = getWeek(new Date(2005, 0, 2))\n * //=> 2\n *\n * @example\n * // Which week of the local week numbering year is 2 January 2005,\n * // if Monday is the first day of the week,\n * // and the first week of the year always contains 4 January?\n * const result = getWeek(new Date(2005, 0, 2), {\n *   weekStartsOn: 1,\n *   firstWeekContainsDate: 4\n * })\n * //=> 53\n */\nexport function getWeek(date, options) {\n  const _date = toDate(date, options?.in);\n  const diff = +startOfWeek(_date, options) - +startOfWeekYear(_date, options);\n\n  // Round the number of weeks to the nearest integer because the number of\n  // milliseconds in a week is not constant (e.g. it's different in the week of\n  // the daylight saving time clock shift).\n  return Math.round(diff / millisecondsInWeek) + 1;\n}\n\n// Fallback for modularized imports:\nexport default getWeek;\n", "export function addLeadingZeros(number, targetLength) {\n  const sign = number < 0 ? \"-\" : \"\";\n  const output = Math.abs(number).toString().padStart(targetLength, \"0\");\n  return sign + output;\n}\n", "import { addLeadingZeros } from \"../addLeadingZeros.js\";\n\n/*\n * |     | Unit                           |     | Unit                           |\n * |-----|--------------------------------|-----|--------------------------------|\n * |  a  | AM, PM                         |  A* |                                |\n * |  d  | Day of month                   |  D  |                                |\n * |  h  | Hour [1-12]                    |  H  | Hour [0-23]                    |\n * |  m  | Minute                         |  M  | Month                          |\n * |  s  | Second                         |  S  | Fraction of second             |\n * |  y  | Year (abs)                     |  Y  |                                |\n *\n * Letters marked by * are not implemented but reserved by Unicode standard.\n */\n\nexport const lightFormatters = {\n  // Year\n  y(date, token) {\n    // From http://www.unicode.org/reports/tr35/tr35-31/tr35-dates.html#Date_Format_tokens\n    // | Year     |     y | yy |   yyy |  yyyy | yyyyy |\n    // |----------|-------|----|-------|-------|-------|\n    // | AD 1     |     1 | 01 |   001 |  0001 | 00001 |\n    // | AD 12    |    12 | 12 |   012 |  0012 | 00012 |\n    // | AD 123   |   123 | 23 |   123 |  0123 | 00123 |\n    // | AD 1234  |  1234 | 34 |  1234 |  1234 | 01234 |\n    // | AD 12345 | 12345 | 45 | 12345 | 12345 | 12345 |\n\n    const signedYear = date.getFullYear();\n    // Returns 1 for 1 BC (which is year 0 in JavaScript)\n    const year = signedYear > 0 ? signedYear : 1 - signedYear;\n    return addLeadingZeros(token === \"yy\" ? year % 100 : year, token.length);\n  },\n\n  // Month\n  M(date, token) {\n    const month = date.getMonth();\n    return token === \"M\" ? String(month + 1) : addLeadingZeros(month + 1, 2);\n  },\n\n  // Day of the month\n  d(date, token) {\n    return addLeadingZeros(date.getDate(), token.length);\n  },\n\n  // AM or PM\n  a(date, token) {\n    const dayPeriodEnumValue = date.getHours() / 12 >= 1 ? \"pm\" : \"am\";\n\n    switch (token) {\n      case \"a\":\n      case \"aa\":\n        return dayPeriodEnumValue.toUpperCase();\n      case \"aaa\":\n        return dayPeriodEnumValue;\n      case \"aaaaa\":\n        return dayPeriodEnumValue[0];\n      case \"aaaa\":\n      default:\n        return dayPeriodEnumValue === \"am\" ? \"a.m.\" : \"p.m.\";\n    }\n  },\n\n  // Hour [1-12]\n  h(date, token) {\n    return addLeadingZeros(date.getHours() % 12 || 12, token.length);\n  },\n\n  // Hour [0-23]\n  H(date, token) {\n    return addLeadingZeros(date.getHours(), token.length);\n  },\n\n  // Minute\n  m(date, token) {\n    return addLeadingZeros(date.getMinutes(), token.length);\n  },\n\n  // Second\n  s(date, token) {\n    return addLeadingZeros(date.getSeconds(), token.length);\n  },\n\n  // Fraction of second\n  S(date, token) {\n    const numberOfDigits = token.length;\n    const milliseconds = date.getMilliseconds();\n    const fractionalSeconds = Math.trunc(\n      milliseconds * Math.pow(10, numberOfDigits - 3),\n    );\n    return addLeadingZeros(fractionalSeconds, token.length);\n  },\n};\n", "import { getDayOfYear } from \"../../getDayOfYear.js\";\nimport { getISOWeek } from \"../../getISOWeek.js\";\nimport { getISOWeekYear } from \"../../getISOWeekYear.js\";\nimport { getWeek } from \"../../getWeek.js\";\nimport { getWeekYear } from \"../../getWeekYear.js\";\n\nimport { addLeadingZeros } from \"../addLeadingZeros.js\";\nimport { lightFormatters } from \"./lightFormatters.js\";\n\nconst dayPeriodEnum = {\n  am: \"am\",\n  pm: \"pm\",\n  midnight: \"midnight\",\n  noon: \"noon\",\n  morning: \"morning\",\n  afternoon: \"afternoon\",\n  evening: \"evening\",\n  night: \"night\",\n};\n\n/*\n * |     | Unit                           |     | Unit                           |\n * |-----|--------------------------------|-----|--------------------------------|\n * |  a  | AM, PM                         |  A* | Milliseconds in day            |\n * |  b  | AM, PM, noon, midnight         |  B  | Flexible day period            |\n * |  c  | Stand-alone local day of week  |  C* | Localized hour w/ day period   |\n * |  d  | Day of month                   |  D  | Day of year                    |\n * |  e  | Local day of week              |  E  | Day of week                    |\n * |  f  |                                |  F* | Day of week in month           |\n * |  g* | Modified Julian day            |  G  | Era                            |\n * |  h  | Hour [1-12]                    |  H  | Hour [0-23]                    |\n * |  i! | ISO day of week                |  I! | ISO week of year               |\n * |  j* | Localized hour w/ day period   |  J* | Localized hour w/o day period  |\n * |  k  | Hour [1-24]                    |  K  | Hour [0-11]                    |\n * |  l* | (deprecated)                   |  L  | Stand-alone month              |\n * |  m  | Minute                         |  M  | Month                          |\n * |  n  |                                |  N  |                                |\n * |  o! | Ordinal number modifier        |  O  | Timezone (GMT)                 |\n * |  p! | Long localized time            |  P! | Long localized date            |\n * |  q  | Stand-alone quarter            |  Q  | Quarter                        |\n * |  r* | Related Gregorian year         |  R! | ISO week-numbering year        |\n * |  s  | Second                         |  S  | Fraction of second             |\n * |  t! | Seconds timestamp              |  T! | Milliseconds timestamp         |\n * |  u  | Extended year                  |  U* | Cyclic year                    |\n * |  v* | Timezone (generic non-locat.)  |  V* | Timezone (location)            |\n * |  w  | Local week of year             |  W* | Week of month                  |\n * |  x  | Timezone (ISO-8601 w/o Z)      |  X  | Timezone (ISO-8601)            |\n * |  y  | Year (abs)                     |  Y  | Local week-numbering year      |\n * |  z  | Timezone (specific non-locat.) |  Z* | Timezone (aliases)             |\n *\n * Letters marked by * are not implemented but reserved by Unicode standard.\n *\n * Letters marked by ! are non-standard, but implemented by date-fns:\n * - `o` modifies the previous token to turn it into an ordinal (see `format` docs)\n * - `i` is ISO day of week. For `i` and `ii` is returns numeric ISO week days,\n *   i.e. 7 for Sunday, 1 for Monday, etc.\n * - `I` is ISO week of year, as opposed to `w` which is local week of year.\n * - `R` is ISO week-numbering year, as opposed to `Y` which is local week-numbering year.\n *   `R` is supposed to be used in conjunction with `I` and `i`\n *   for universal ISO week-numbering date, whereas\n *   `Y` is supposed to be used in conjunction with `w` and `e`\n *   for week-numbering date specific to the locale.\n * - `P` is long localized date format\n * - `p` is long localized time format\n */\n\nexport const formatters = {\n  // Era\n  G: function (date, token, localize) {\n    const era = date.getFullYear() > 0 ? 1 : 0;\n    switch (token) {\n      // AD, BC\n      case \"G\":\n      case \"GG\":\n      case \"GGG\":\n        return localize.era(era, { width: \"abbreviated\" });\n      // A, B\n      case \"GGGGG\":\n        return localize.era(era, { width: \"narrow\" });\n      // Anno Domini, Before Christ\n      case \"GGGG\":\n      default:\n        return localize.era(era, { width: \"wide\" });\n    }\n  },\n\n  // Year\n  y: function (date, token, localize) {\n    // Ordinal number\n    if (token === \"yo\") {\n      const signedYear = date.getFullYear();\n      // Returns 1 for 1 BC (which is year 0 in JavaScript)\n      const year = signedYear > 0 ? signedYear : 1 - signedYear;\n      return localize.ordinalNumber(year, { unit: \"year\" });\n    }\n\n    return lightFormatters.y(date, token);\n  },\n\n  // Local week-numbering year\n  Y: function (date, token, localize, options) {\n    const signedWeekYear = getWeekYear(date, options);\n    // Returns 1 for 1 BC (which is year 0 in JavaScript)\n    const weekYear = signedWeekYear > 0 ? signedWeekYear : 1 - signedWeekYear;\n\n    // Two digit year\n    if (token === \"YY\") {\n      const twoDigitYear = weekYear % 100;\n      return addLeadingZeros(twoDigitYear, 2);\n    }\n\n    // Ordinal number\n    if (token === \"Yo\") {\n      return localize.ordinalNumber(weekYear, { unit: \"year\" });\n    }\n\n    // Padding\n    return addLeadingZeros(weekYear, token.length);\n  },\n\n  // ISO week-numbering year\n  R: function (date, token) {\n    const isoWeekYear = getISOWeekYear(date);\n\n    // Padding\n    return addLeadingZeros(isoWeekYear, token.length);\n  },\n\n  // Extended year. This is a single number designating the year of this calendar system.\n  // The main difference between `y` and `u` localizers are B.C. years:\n  // | Year | `y` | `u` |\n  // |------|-----|-----|\n  // | AC 1 |   1 |   1 |\n  // | BC 1 |   1 |   0 |\n  // | BC 2 |   2 |  -1 |\n  // Also `yy` always returns the last two digits of a year,\n  // while `uu` pads single digit years to 2 characters and returns other years unchanged.\n  u: function (date, token) {\n    const year = date.getFullYear();\n    return addLeadingZeros(year, token.length);\n  },\n\n  // Quarter\n  Q: function (date, token, localize) {\n    const quarter = Math.ceil((date.getMonth() + 1) / 3);\n    switch (token) {\n      // 1, 2, 3, 4\n      case \"Q\":\n        return String(quarter);\n      // 01, 02, 03, 04\n      case \"QQ\":\n        return addLeadingZeros(quarter, 2);\n      // 1st, 2nd, 3rd, 4th\n      case \"Qo\":\n        return localize.ordinalNumber(quarter, { unit: \"quarter\" });\n      // Q1, Q2, Q3, Q4\n      case \"QQQ\":\n        return localize.quarter(quarter, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      // 1, 2, 3, 4 (narrow quarter; could be not numerical)\n      case \"QQQQQ\":\n        return localize.quarter(quarter, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // 1st quarter, 2nd quarter, ...\n      case \"QQQQ\":\n      default:\n        return localize.quarter(quarter, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // Stand-alone quarter\n  q: function (date, token, localize) {\n    const quarter = Math.ceil((date.getMonth() + 1) / 3);\n    switch (token) {\n      // 1, 2, 3, 4\n      case \"q\":\n        return String(quarter);\n      // 01, 02, 03, 04\n      case \"qq\":\n        return addLeadingZeros(quarter, 2);\n      // 1st, 2nd, 3rd, 4th\n      case \"qo\":\n        return localize.ordinalNumber(quarter, { unit: \"quarter\" });\n      // Q1, Q2, Q3, Q4\n      case \"qqq\":\n        return localize.quarter(quarter, {\n          width: \"abbreviated\",\n          context: \"standalone\",\n        });\n      // 1, 2, 3, 4 (narrow quarter; could be not numerical)\n      case \"qqqqq\":\n        return localize.quarter(quarter, {\n          width: \"narrow\",\n          context: \"standalone\",\n        });\n      // 1st quarter, 2nd quarter, ...\n      case \"qqqq\":\n      default:\n        return localize.quarter(quarter, {\n          width: \"wide\",\n          context: \"standalone\",\n        });\n    }\n  },\n\n  // Month\n  M: function (date, token, localize) {\n    const month = date.getMonth();\n    switch (token) {\n      case \"M\":\n      case \"MM\":\n        return lightFormatters.M(date, token);\n      // 1st, 2nd, ..., 12th\n      case \"Mo\":\n        return localize.ordinalNumber(month + 1, { unit: \"month\" });\n      // Jan, Feb, ..., Dec\n      case \"MMM\":\n        return localize.month(month, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      // J, F, ..., D\n      case \"MMMMM\":\n        return localize.month(month, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // January, February, ..., December\n      case \"MMMM\":\n      default:\n        return localize.month(month, { width: \"wide\", context: \"formatting\" });\n    }\n  },\n\n  // Stand-alone month\n  L: function (date, token, localize) {\n    const month = date.getMonth();\n    switch (token) {\n      // 1, 2, ..., 12\n      case \"L\":\n        return String(month + 1);\n      // 01, 02, ..., 12\n      case \"LL\":\n        return addLeadingZeros(month + 1, 2);\n      // 1st, 2nd, ..., 12th\n      case \"Lo\":\n        return localize.ordinalNumber(month + 1, { unit: \"month\" });\n      // Jan, Feb, ..., Dec\n      case \"LLL\":\n        return localize.month(month, {\n          width: \"abbreviated\",\n          context: \"standalone\",\n        });\n      // J, F, ..., D\n      case \"LLLLL\":\n        return localize.month(month, {\n          width: \"narrow\",\n          context: \"standalone\",\n        });\n      // January, February, ..., December\n      case \"LLLL\":\n      default:\n        return localize.month(month, { width: \"wide\", context: \"standalone\" });\n    }\n  },\n\n  // Local week of year\n  w: function (date, token, localize, options) {\n    const week = getWeek(date, options);\n\n    if (token === \"wo\") {\n      return localize.ordinalNumber(week, { unit: \"week\" });\n    }\n\n    return addLeadingZeros(week, token.length);\n  },\n\n  // ISO week of year\n  I: function (date, token, localize) {\n    const isoWeek = getISOWeek(date);\n\n    if (token === \"Io\") {\n      return localize.ordinalNumber(isoWeek, { unit: \"week\" });\n    }\n\n    return addLeadingZeros(isoWeek, token.length);\n  },\n\n  // Day of the month\n  d: function (date, token, localize) {\n    if (token === \"do\") {\n      return localize.ordinalNumber(date.getDate(), { unit: \"date\" });\n    }\n\n    return lightFormatters.d(date, token);\n  },\n\n  // Day of year\n  D: function (date, token, localize) {\n    const dayOfYear = getDayOfYear(date);\n\n    if (token === \"Do\") {\n      return localize.ordinalNumber(dayOfYear, { unit: \"dayOfYear\" });\n    }\n\n    return addLeadingZeros(dayOfYear, token.length);\n  },\n\n  // Day of week\n  E: function (date, token, localize) {\n    const dayOfWeek = date.getDay();\n    switch (token) {\n      // Tue\n      case \"E\":\n      case \"EE\":\n      case \"EEE\":\n        return localize.day(dayOfWeek, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      // T\n      case \"EEEEE\":\n        return localize.day(dayOfWeek, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // Tu\n      case \"EEEEEE\":\n        return localize.day(dayOfWeek, {\n          width: \"short\",\n          context: \"formatting\",\n        });\n      // Tuesday\n      case \"EEEE\":\n      default:\n        return localize.day(dayOfWeek, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // Local day of week\n  e: function (date, token, localize, options) {\n    const dayOfWeek = date.getDay();\n    const localDayOfWeek = (dayOfWeek - options.weekStartsOn + 8) % 7 || 7;\n    switch (token) {\n      // Numerical value (Nth day of week with current locale or weekStartsOn)\n      case \"e\":\n        return String(localDayOfWeek);\n      // Padded numerical value\n      case \"ee\":\n        return addLeadingZeros(localDayOfWeek, 2);\n      // 1st, 2nd, ..., 7th\n      case \"eo\":\n        return localize.ordinalNumber(localDayOfWeek, { unit: \"day\" });\n      case \"eee\":\n        return localize.day(dayOfWeek, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      // T\n      case \"eeeee\":\n        return localize.day(dayOfWeek, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // Tu\n      case \"eeeeee\":\n        return localize.day(dayOfWeek, {\n          width: \"short\",\n          context: \"formatting\",\n        });\n      // Tuesday\n      case \"eeee\":\n      default:\n        return localize.day(dayOfWeek, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // Stand-alone local day of week\n  c: function (date, token, localize, options) {\n    const dayOfWeek = date.getDay();\n    const localDayOfWeek = (dayOfWeek - options.weekStartsOn + 8) % 7 || 7;\n    switch (token) {\n      // Numerical value (same as in `e`)\n      case \"c\":\n        return String(localDayOfWeek);\n      // Padded numerical value\n      case \"cc\":\n        return addLeadingZeros(localDayOfWeek, token.length);\n      // 1st, 2nd, ..., 7th\n      case \"co\":\n        return localize.ordinalNumber(localDayOfWeek, { unit: \"day\" });\n      case \"ccc\":\n        return localize.day(dayOfWeek, {\n          width: \"abbreviated\",\n          context: \"standalone\",\n        });\n      // T\n      case \"ccccc\":\n        return localize.day(dayOfWeek, {\n          width: \"narrow\",\n          context: \"standalone\",\n        });\n      // Tu\n      case \"cccccc\":\n        return localize.day(dayOfWeek, {\n          width: \"short\",\n          context: \"standalone\",\n        });\n      // Tuesday\n      case \"cccc\":\n      default:\n        return localize.day(dayOfWeek, {\n          width: \"wide\",\n          context: \"standalone\",\n        });\n    }\n  },\n\n  // ISO day of week\n  i: function (date, token, localize) {\n    const dayOfWeek = date.getDay();\n    const isoDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;\n    switch (token) {\n      // 2\n      case \"i\":\n        return String(isoDayOfWeek);\n      // 02\n      case \"ii\":\n        return addLeadingZeros(isoDayOfWeek, token.length);\n      // 2nd\n      case \"io\":\n        return localize.ordinalNumber(isoDayOfWeek, { unit: \"day\" });\n      // Tue\n      case \"iii\":\n        return localize.day(dayOfWeek, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      // T\n      case \"iiiii\":\n        return localize.day(dayOfWeek, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // Tu\n      case \"iiiiii\":\n        return localize.day(dayOfWeek, {\n          width: \"short\",\n          context: \"formatting\",\n        });\n      // Tuesday\n      case \"iiii\":\n      default:\n        return localize.day(dayOfWeek, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // AM or PM\n  a: function (date, token, localize) {\n    const hours = date.getHours();\n    const dayPeriodEnumValue = hours / 12 >= 1 ? \"pm\" : \"am\";\n\n    switch (token) {\n      case \"a\":\n      case \"aa\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      case \"aaa\":\n        return localize\n          .dayPeriod(dayPeriodEnumValue, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          })\n          .toLowerCase();\n      case \"aaaaa\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      case \"aaaa\":\n      default:\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // AM, PM, midnight, noon\n  b: function (date, token, localize) {\n    const hours = date.getHours();\n    let dayPeriodEnumValue;\n    if (hours === 12) {\n      dayPeriodEnumValue = dayPeriodEnum.noon;\n    } else if (hours === 0) {\n      dayPeriodEnumValue = dayPeriodEnum.midnight;\n    } else {\n      dayPeriodEnumValue = hours / 12 >= 1 ? \"pm\" : \"am\";\n    }\n\n    switch (token) {\n      case \"b\":\n      case \"bb\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      case \"bbb\":\n        return localize\n          .dayPeriod(dayPeriodEnumValue, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          })\n          .toLowerCase();\n      case \"bbbbb\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      case \"bbbb\":\n      default:\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // in the morning, in the afternoon, in the evening, at night\n  B: function (date, token, localize) {\n    const hours = date.getHours();\n    let dayPeriodEnumValue;\n    if (hours >= 17) {\n      dayPeriodEnumValue = dayPeriodEnum.evening;\n    } else if (hours >= 12) {\n      dayPeriodEnumValue = dayPeriodEnum.afternoon;\n    } else if (hours >= 4) {\n      dayPeriodEnumValue = dayPeriodEnum.morning;\n    } else {\n      dayPeriodEnumValue = dayPeriodEnum.night;\n    }\n\n    switch (token) {\n      case \"B\":\n      case \"BB\":\n      case \"BBB\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      case \"BBBBB\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      case \"BBBB\":\n      default:\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // Hour [1-12]\n  h: function (date, token, localize) {\n    if (token === \"ho\") {\n      let hours = date.getHours() % 12;\n      if (hours === 0) hours = 12;\n      return localize.ordinalNumber(hours, { unit: \"hour\" });\n    }\n\n    return lightFormatters.h(date, token);\n  },\n\n  // Hour [0-23]\n  H: function (date, token, localize) {\n    if (token === \"Ho\") {\n      return localize.ordinalNumber(date.getHours(), { unit: \"hour\" });\n    }\n\n    return lightFormatters.H(date, token);\n  },\n\n  // Hour [0-11]\n  K: function (date, token, localize) {\n    const hours = date.getHours() % 12;\n\n    if (token === \"Ko\") {\n      return localize.ordinalNumber(hours, { unit: \"hour\" });\n    }\n\n    return addLeadingZeros(hours, token.length);\n  },\n\n  // Hour [1-24]\n  k: function (date, token, localize) {\n    let hours = date.getHours();\n    if (hours === 0) hours = 24;\n\n    if (token === \"ko\") {\n      return localize.ordinalNumber(hours, { unit: \"hour\" });\n    }\n\n    return addLeadingZeros(hours, token.length);\n  },\n\n  // Minute\n  m: function (date, token, localize) {\n    if (token === \"mo\") {\n      return localize.ordinalNumber(date.getMinutes(), { unit: \"minute\" });\n    }\n\n    return lightFormatters.m(date, token);\n  },\n\n  // Second\n  s: function (date, token, localize) {\n    if (token === \"so\") {\n      return localize.ordinalNumber(date.getSeconds(), { unit: \"second\" });\n    }\n\n    return lightFormatters.s(date, token);\n  },\n\n  // Fraction of second\n  S: function (date, token) {\n    return lightFormatters.S(date, token);\n  },\n\n  // Timezone (ISO-8601. If offset is 0, output is always `'Z'`)\n  X: function (date, token, _localize) {\n    const timezoneOffset = date.getTimezoneOffset();\n\n    if (timezoneOffset === 0) {\n      return \"Z\";\n    }\n\n    switch (token) {\n      // Hours and optional minutes\n      case \"X\":\n        return formatTimezoneWithOptionalMinutes(timezoneOffset);\n\n      // Hours, minutes and optional seconds without `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `XX`\n      case \"XXXX\":\n      case \"XX\": // Hours and minutes without `:` delimiter\n        return formatTimezone(timezoneOffset);\n\n      // Hours, minutes and optional seconds with `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `XXX`\n      case \"XXXXX\":\n      case \"XXX\": // Hours and minutes with `:` delimiter\n      default:\n        return formatTimezone(timezoneOffset, \":\");\n    }\n  },\n\n  // Timezone (ISO-8601. If offset is 0, output is `'+00:00'` or equivalent)\n  x: function (date, token, _localize) {\n    const timezoneOffset = date.getTimezoneOffset();\n\n    switch (token) {\n      // Hours and optional minutes\n      case \"x\":\n        return formatTimezoneWithOptionalMinutes(timezoneOffset);\n\n      // Hours, minutes and optional seconds without `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `xx`\n      case \"xxxx\":\n      case \"xx\": // Hours and minutes without `:` delimiter\n        return formatTimezone(timezoneOffset);\n\n      // Hours, minutes and optional seconds with `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `xxx`\n      case \"xxxxx\":\n      case \"xxx\": // Hours and minutes with `:` delimiter\n      default:\n        return formatTimezone(timezoneOffset, \":\");\n    }\n  },\n\n  // Timezone (GMT)\n  O: function (date, token, _localize) {\n    const timezoneOffset = date.getTimezoneOffset();\n\n    switch (token) {\n      // Short\n      case \"O\":\n      case \"OO\":\n      case \"OOO\":\n        return \"GMT\" + formatTimezoneShort(timezoneOffset, \":\");\n      // Long\n      case \"OOOO\":\n      default:\n        return \"GMT\" + formatTimezone(timezoneOffset, \":\");\n    }\n  },\n\n  // Timezone (specific non-location)\n  z: function (date, token, _localize) {\n    const timezoneOffset = date.getTimezoneOffset();\n\n    switch (token) {\n      // Short\n      case \"z\":\n      case \"zz\":\n      case \"zzz\":\n        return \"GMT\" + formatTimezoneShort(timezoneOffset, \":\");\n      // Long\n      case \"zzzz\":\n      default:\n        return \"GMT\" + formatTimezone(timezoneOffset, \":\");\n    }\n  },\n\n  // Seconds timestamp\n  t: function (date, token, _localize) {\n    const timestamp = Math.trunc(+date / 1000);\n    return addLeadingZeros(timestamp, token.length);\n  },\n\n  // Milliseconds timestamp\n  T: function (date, token, _localize) {\n    return addLeadingZeros(+date, token.length);\n  },\n};\n\nfunction formatTimezoneShort(offset, delimiter = \"\") {\n  const sign = offset > 0 ? \"-\" : \"+\";\n  const absOffset = Math.abs(offset);\n  const hours = Math.trunc(absOffset / 60);\n  const minutes = absOffset % 60;\n  if (minutes === 0) {\n    return sign + String(hours);\n  }\n  return sign + String(hours) + delimiter + addLeadingZeros(minutes, 2);\n}\n\nfunction formatTimezoneWithOptionalMinutes(offset, delimiter) {\n  if (offset % 60 === 0) {\n    const sign = offset > 0 ? \"-\" : \"+\";\n    return sign + addLeadingZeros(Math.abs(offset) / 60, 2);\n  }\n  return formatTimezone(offset, delimiter);\n}\n\nfunction formatTimezone(offset, delimiter = \"\") {\n  const sign = offset > 0 ? \"-\" : \"+\";\n  const absOffset = Math.abs(offset);\n  const hours = addLeadingZeros(Math.trunc(absOffset / 60), 2);\n  const minutes = addLeadingZeros(absOffset % 60, 2);\n  return sign + hours + delimiter + minutes;\n}\n", "const dateLongFormatter = (pattern, formatLong) => {\n  switch (pattern) {\n    case \"P\":\n      return formatLong.date({ width: \"short\" });\n    case \"PP\":\n      return formatLong.date({ width: \"medium\" });\n    case \"PPP\":\n      return formatLong.date({ width: \"long\" });\n    case \"PPPP\":\n    default:\n      return formatLong.date({ width: \"full\" });\n  }\n};\n\nconst timeLongFormatter = (pattern, formatLong) => {\n  switch (pattern) {\n    case \"p\":\n      return formatLong.time({ width: \"short\" });\n    case \"pp\":\n      return formatLong.time({ width: \"medium\" });\n    case \"ppp\":\n      return formatLong.time({ width: \"long\" });\n    case \"pppp\":\n    default:\n      return formatLong.time({ width: \"full\" });\n  }\n};\n\nconst dateTimeLongFormatter = (pattern, formatLong) => {\n  const matchResult = pattern.match(/(P+)(p+)?/) || [];\n  const datePattern = matchResult[1];\n  const timePattern = matchResult[2];\n\n  if (!timePattern) {\n    return dateLongFormatter(pattern, formatLong);\n  }\n\n  let dateTimeFormat;\n\n  switch (datePattern) {\n    case \"P\":\n      dateTimeFormat = formatLong.dateTime({ width: \"short\" });\n      break;\n    case \"PP\":\n      dateTimeFormat = formatLong.dateTime({ width: \"medium\" });\n      break;\n    case \"PPP\":\n      dateTimeFormat = formatLong.dateTime({ width: \"long\" });\n      break;\n    case \"PPPP\":\n    default:\n      dateTimeFormat = formatLong.dateTime({ width: \"full\" });\n      break;\n  }\n\n  return dateTimeFormat\n    .replace(\"{{date}}\", dateLongFormatter(datePattern, formatLong))\n    .replace(\"{{time}}\", timeLongFormatter(timePattern, formatLong));\n};\n\nexport const longFormatters = {\n  p: timeLongFormatter,\n  P: dateTimeLongFormatter,\n};\n", "const dayOfYearTokenRE = /^D+$/;\nconst weekYearTokenRE = /^Y+$/;\n\nconst throwTokens = [\"D\", \"DD\", \"YY\", \"YYYY\"];\n\nexport function isProtectedDayOfYearToken(token) {\n  return dayOfYearTokenRE.test(token);\n}\n\nexport function isProtectedWeekYearToken(token) {\n  return weekYearTokenRE.test(token);\n}\n\nexport function warnOrThrowProtectedError(token, format, input) {\n  const _message = message(token, format, input);\n  console.warn(_message);\n  if (throwTokens.includes(token)) throw new RangeError(_message);\n}\n\nfunction message(token, format, input) {\n  const subject = token[0] === \"Y\" ? \"years\" : \"days of the month\";\n  return `Use \\`${token.toLowerCase()}\\` instead of \\`${token}\\` (in \\`${format}\\`) for formatting ${subject} to the input \\`${input}\\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`;\n}\n", "/**\n * @name isDate\n * @category Common Helpers\n * @summary Is the given value a date?\n *\n * @description\n * Returns true if the given value is an instance of Date. The function works for dates transferred across iframes.\n *\n * @param value - The value to check\n *\n * @returns True if the given value is a date\n *\n * @example\n * // For a valid date:\n * const result = isDate(new Date())\n * //=> true\n *\n * @example\n * // For an invalid date:\n * const result = isDate(new Date(NaN))\n * //=> true\n *\n * @example\n * // For some value:\n * const result = isDate('2014-02-31')\n * //=> false\n *\n * @example\n * // For an object:\n * const result = isDate({})\n * //=> false\n */\nexport function isDate(value) {\n  return (\n    value instanceof Date ||\n    (typeof value === \"object\" &&\n      Object.prototype.toString.call(value) === \"[object Date]\")\n  );\n}\n\n// Fallback for modularized imports:\nexport default isDate;\n", "import { isDate } from \"./isDate.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * @name isValid\n * @category Common Helpers\n * @summary Is the given date valid?\n *\n * @description\n * Returns false if argument is Invalid Date and true otherwise.\n * Argument is converted to Date using `toDate`. See [toDate](https://date-fns.org/docs/toDate)\n * Invalid Date is a Date, whose time value is NaN.\n *\n * Time value of Date: http://es5.github.io/#x15.9.1.1\n *\n * @param date - The date to check\n *\n * @returns The date is valid\n *\n * @example\n * // For the valid date:\n * const result = isValid(new Date(2014, 1, 31))\n * //=> true\n *\n * @example\n * // For the value, convertible into a date:\n * const result = isValid(1393804800000)\n * //=> true\n *\n * @example\n * // For the invalid date:\n * const result = isValid(new Date(''))\n * //=> false\n */\nexport function isValid(date) {\n  return !((!isDate(date) && typeof date !== \"number\") || isNaN(+toDate(date)));\n}\n\n// Fallback for modularized imports:\nexport default isValid;\n", "import { defaultLocale } from \"./_lib/defaultLocale.js\";\nimport { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { formatters } from \"./_lib/format/formatters.js\";\nimport { longFormatters } from \"./_lib/format/longFormatters.js\";\nimport {\n  isProtectedDayOfYearToken,\n  isProtectedWeekYearToken,\n  warnOrThrowProtectedError,\n} from \"./_lib/protectedTokens.js\";\nimport { isValid } from \"./isValid.js\";\nimport { toDate } from \"./toDate.js\";\n\n// Rexports of internal for libraries to use.\n// See: https://github.com/date-fns/date-fns/issues/3638#issuecomment-1877082874\nexport { formatters, longFormatters };\n\n// This RegExp consists of three parts separated by `|`:\n// - [yYQqMLwIdDecihHKkms]o matches any available ordinal number token\n//   (one of the certain letters followed by `o`)\n// - (\\w)\\1* matches any sequences of the same letter\n// - '' matches two quote characters in a row\n// - '(''|[^'])+('|$) matches anything surrounded by two quote characters ('),\n//   except a single quote symbol, which ends the sequence.\n//   Two quote characters do not end the sequence.\n//   If there is no matching single quote\n//   then the sequence will continue until the end of the string.\n// - . matches any single character unmatched by previous parts of the RegExps\nconst formattingTokensRegExp =\n  /[yYQqMLwIdDecihHKkms]o|(\\w)\\1*|''|'(''|[^'])+('|$)|./g;\n\n// This RegExp catches symbols escaped by quotes, and also\n// sequences of symbols P, p, and the combinations like `PPPPPPPppppp`\nconst longFormattingTokensRegExp = /P+p+|P+|p+|''|'(''|[^'])+('|$)|./g;\n\nconst escapedStringRegExp = /^'([^]*?)'?$/;\nconst doubleQuoteRegExp = /''/g;\nconst unescapedLatinCharacterRegExp = /[a-zA-Z]/;\n\nexport { format as formatDate };\n\n/**\n * The {@link format} function options.\n */\n\n/**\n * @name format\n * @alias formatDate\n * @category Common Helpers\n * @summary Format the date.\n *\n * @description\n * Return the formatted date string in the given format. The result may vary by locale.\n *\n * > ⚠️ Please note that the `format` tokens differ from Moment.js and other libraries.\n * > See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * The characters wrapped between two single quotes characters (') are escaped.\n * Two single quotes in a row, whether inside or outside a quoted sequence, represent a 'real' single quote.\n * (see the last example)\n *\n * Format of the string is based on Unicode Technical Standard #35:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * with a few additions (see note 7 below the table).\n *\n * Accepted patterns:\n * | Unit                            | Pattern | Result examples                   | Notes |\n * |---------------------------------|---------|-----------------------------------|-------|\n * | Era                             | G..GGG  | AD, BC                            |       |\n * |                                 | GGGG    | Anno Domini, Before Christ        | 2     |\n * |                                 | GGGGG   | A, B                              |       |\n * | Calendar year                   | y       | 44, 1, 1900, 2017                 | 5     |\n * |                                 | yo      | 44th, 1st, 0th, 17th              | 5,7   |\n * |                                 | yy      | 44, 01, 00, 17                    | 5     |\n * |                                 | yyy     | 044, 001, 1900, 2017              | 5     |\n * |                                 | yyyy    | 0044, 0001, 1900, 2017            | 5     |\n * |                                 | yyyyy   | ...                               | 3,5   |\n * | Local week-numbering year       | Y       | 44, 1, 1900, 2017                 | 5     |\n * |                                 | Yo      | 44th, 1st, 1900th, 2017th         | 5,7   |\n * |                                 | YY      | 44, 01, 00, 17                    | 5,8   |\n * |                                 | YYY     | 044, 001, 1900, 2017              | 5     |\n * |                                 | YYYY    | 0044, 0001, 1900, 2017            | 5,8   |\n * |                                 | YYYYY   | ...                               | 3,5   |\n * | ISO week-numbering year         | R       | -43, 0, 1, 1900, 2017             | 5,7   |\n * |                                 | RR      | -43, 00, 01, 1900, 2017           | 5,7   |\n * |                                 | RRR     | -043, 000, 001, 1900, 2017        | 5,7   |\n * |                                 | RRRR    | -0043, 0000, 0001, 1900, 2017     | 5,7   |\n * |                                 | RRRRR   | ...                               | 3,5,7 |\n * | Extended year                   | u       | -43, 0, 1, 1900, 2017             | 5     |\n * |                                 | uu      | -43, 01, 1900, 2017               | 5     |\n * |                                 | uuu     | -043, 001, 1900, 2017             | 5     |\n * |                                 | uuuu    | -0043, 0001, 1900, 2017           | 5     |\n * |                                 | uuuuu   | ...                               | 3,5   |\n * | Quarter (formatting)            | Q       | 1, 2, 3, 4                        |       |\n * |                                 | Qo      | 1st, 2nd, 3rd, 4th                | 7     |\n * |                                 | QQ      | 01, 02, 03, 04                    |       |\n * |                                 | QQQ     | Q1, Q2, Q3, Q4                    |       |\n * |                                 | QQQQ    | 1st quarter, 2nd quarter, ...     | 2     |\n * |                                 | QQQQQ   | 1, 2, 3, 4                        | 4     |\n * | Quarter (stand-alone)           | q       | 1, 2, 3, 4                        |       |\n * |                                 | qo      | 1st, 2nd, 3rd, 4th                | 7     |\n * |                                 | qq      | 01, 02, 03, 04                    |       |\n * |                                 | qqq     | Q1, Q2, Q3, Q4                    |       |\n * |                                 | qqqq    | 1st quarter, 2nd quarter, ...     | 2     |\n * |                                 | qqqqq   | 1, 2, 3, 4                        | 4     |\n * | Month (formatting)              | M       | 1, 2, ..., 12                     |       |\n * |                                 | Mo      | 1st, 2nd, ..., 12th               | 7     |\n * |                                 | MM      | 01, 02, ..., 12                   |       |\n * |                                 | MMM     | Jan, Feb, ..., Dec                |       |\n * |                                 | MMMM    | January, February, ..., December  | 2     |\n * |                                 | MMMMM   | J, F, ..., D                      |       |\n * | Month (stand-alone)             | L       | 1, 2, ..., 12                     |       |\n * |                                 | Lo      | 1st, 2nd, ..., 12th               | 7     |\n * |                                 | LL      | 01, 02, ..., 12                   |       |\n * |                                 | LLL     | Jan, Feb, ..., Dec                |       |\n * |                                 | LLLL    | January, February, ..., December  | 2     |\n * |                                 | LLLLL   | J, F, ..., D                      |       |\n * | Local week of year              | w       | 1, 2, ..., 53                     |       |\n * |                                 | wo      | 1st, 2nd, ..., 53th               | 7     |\n * |                                 | ww      | 01, 02, ..., 53                   |       |\n * | ISO week of year                | I       | 1, 2, ..., 53                     | 7     |\n * |                                 | Io      | 1st, 2nd, ..., 53th               | 7     |\n * |                                 | II      | 01, 02, ..., 53                   | 7     |\n * | Day of month                    | d       | 1, 2, ..., 31                     |       |\n * |                                 | do      | 1st, 2nd, ..., 31st               | 7     |\n * |                                 | dd      | 01, 02, ..., 31                   |       |\n * | Day of year                     | D       | 1, 2, ..., 365, 366               | 9     |\n * |                                 | Do      | 1st, 2nd, ..., 365th, 366th       | 7     |\n * |                                 | DD      | 01, 02, ..., 365, 366             | 9     |\n * |                                 | DDD     | 001, 002, ..., 365, 366           |       |\n * |                                 | DDDD    | ...                               | 3     |\n * | Day of week (formatting)        | E..EEE  | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 | EEEE    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 | EEEEE   | M, T, W, T, F, S, S               |       |\n * |                                 | EEEEEE  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | ISO day of week (formatting)    | i       | 1, 2, 3, ..., 7                   | 7     |\n * |                                 | io      | 1st, 2nd, ..., 7th                | 7     |\n * |                                 | ii      | 01, 02, ..., 07                   | 7     |\n * |                                 | iii     | Mon, Tue, Wed, ..., Sun           | 7     |\n * |                                 | iiii    | Monday, Tuesday, ..., Sunday      | 2,7   |\n * |                                 | iiiii   | M, T, W, T, F, S, S               | 7     |\n * |                                 | iiiiii  | Mo, Tu, We, Th, Fr, Sa, Su        | 7     |\n * | Local day of week (formatting)  | e       | 2, 3, 4, ..., 1                   |       |\n * |                                 | eo      | 2nd, 3rd, ..., 1st                | 7     |\n * |                                 | ee      | 02, 03, ..., 01                   |       |\n * |                                 | eee     | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 | eeee    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 | eeeee   | M, T, W, T, F, S, S               |       |\n * |                                 | eeeeee  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | Local day of week (stand-alone) | c       | 2, 3, 4, ..., 1                   |       |\n * |                                 | co      | 2nd, 3rd, ..., 1st                | 7     |\n * |                                 | cc      | 02, 03, ..., 01                   |       |\n * |                                 | ccc     | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 | cccc    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 | ccccc   | M, T, W, T, F, S, S               |       |\n * |                                 | cccccc  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | AM, PM                          | a..aa   | AM, PM                            |       |\n * |                                 | aaa     | am, pm                            |       |\n * |                                 | aaaa    | a.m., p.m.                        | 2     |\n * |                                 | aaaaa   | a, p                              |       |\n * | AM, PM, noon, midnight          | b..bb   | AM, PM, noon, midnight            |       |\n * |                                 | bbb     | am, pm, noon, midnight            |       |\n * |                                 | bbbb    | a.m., p.m., noon, midnight        | 2     |\n * |                                 | bbbbb   | a, p, n, mi                       |       |\n * | Flexible day period             | B..BBB  | at night, in the morning, ...     |       |\n * |                                 | BBBB    | at night, in the morning, ...     | 2     |\n * |                                 | BBBBB   | at night, in the morning, ...     |       |\n * | Hour [1-12]                     | h       | 1, 2, ..., 11, 12                 |       |\n * |                                 | ho      | 1st, 2nd, ..., 11th, 12th         | 7     |\n * |                                 | hh      | 01, 02, ..., 11, 12               |       |\n * | Hour [0-23]                     | H       | 0, 1, 2, ..., 23                  |       |\n * |                                 | Ho      | 0th, 1st, 2nd, ..., 23rd          | 7     |\n * |                                 | HH      | 00, 01, 02, ..., 23               |       |\n * | Hour [0-11]                     | K       | 1, 2, ..., 11, 0                  |       |\n * |                                 | Ko      | 1st, 2nd, ..., 11th, 0th          | 7     |\n * |                                 | KK      | 01, 02, ..., 11, 00               |       |\n * | Hour [1-24]                     | k       | 24, 1, 2, ..., 23                 |       |\n * |                                 | ko      | 24th, 1st, 2nd, ..., 23rd         | 7     |\n * |                                 | kk      | 24, 01, 02, ..., 23               |       |\n * | Minute                          | m       | 0, 1, ..., 59                     |       |\n * |                                 | mo      | 0th, 1st, ..., 59th               | 7     |\n * |                                 | mm      | 00, 01, ..., 59                   |       |\n * | Second                          | s       | 0, 1, ..., 59                     |       |\n * |                                 | so      | 0th, 1st, ..., 59th               | 7     |\n * |                                 | ss      | 00, 01, ..., 59                   |       |\n * | Fraction of second              | S       | 0, 1, ..., 9                      |       |\n * |                                 | SS      | 00, 01, ..., 99                   |       |\n * |                                 | SSS     | 000, 001, ..., 999                |       |\n * |                                 | SSSS    | ...                               | 3     |\n * | Timezone (ISO-8601 w/ Z)        | X       | -08, +0530, Z                     |       |\n * |                                 | XX      | -0800, +0530, Z                   |       |\n * |                                 | XXX     | -08:00, +05:30, Z                 |       |\n * |                                 | XXXX    | -0800, +0530, Z, +123456          | 2     |\n * |                                 | XXXXX   | -08:00, +05:30, Z, +12:34:56      |       |\n * | Timezone (ISO-8601 w/o Z)       | x       | -08, +0530, +00                   |       |\n * |                                 | xx      | -0800, +0530, +0000               |       |\n * |                                 | xxx     | -08:00, +05:30, +00:00            | 2     |\n * |                                 | xxxx    | -0800, +0530, +0000, +123456      |       |\n * |                                 | xxxxx   | -08:00, +05:30, +00:00, +12:34:56 |       |\n * | Timezone (GMT)                  | O...OOO | GMT-8, GMT+5:30, GMT+0            |       |\n * |                                 | OOOO    | GMT-08:00, GMT+05:30, GMT+00:00   | 2     |\n * | Timezone (specific non-locat.)  | z...zzz | GMT-8, GMT+5:30, GMT+0            | 6     |\n * |                                 | zzzz    | GMT-08:00, GMT+05:30, GMT+00:00   | 2,6   |\n * | Seconds timestamp               | t       | 512969520                         | 7     |\n * |                                 | tt      | ...                               | 3,7   |\n * | Milliseconds timestamp          | T       | 512969520900                      | 7     |\n * |                                 | TT      | ...                               | 3,7   |\n * | Long localized date             | P       | 04/29/1453                        | 7     |\n * |                                 | PP      | Apr 29, 1453                      | 7     |\n * |                                 | PPP     | April 29th, 1453                  | 7     |\n * |                                 | PPPP    | Friday, April 29th, 1453          | 2,7   |\n * | Long localized time             | p       | 12:00 AM                          | 7     |\n * |                                 | pp      | 12:00:00 AM                       | 7     |\n * |                                 | ppp     | 12:00:00 AM GMT+2                 | 7     |\n * |                                 | pppp    | 12:00:00 AM GMT+02:00             | 2,7   |\n * | Combination of date and time    | Pp      | 04/29/1453, 12:00 AM              | 7     |\n * |                                 | PPpp    | Apr 29, 1453, 12:00:00 AM         | 7     |\n * |                                 | PPPppp  | April 29th, 1453 at ...           | 7     |\n * |                                 | PPPPpppp| Friday, April 29th, 1453 at ...   | 2,7   |\n * Notes:\n * 1. \"Formatting\" units (e.g. formatting quarter) in the default en-US locale\n *    are the same as \"stand-alone\" units, but are different in some languages.\n *    \"Formatting\" units are declined according to the rules of the language\n *    in the context of a date. \"Stand-alone\" units are always nominative singular:\n *\n *    `format(new Date(2017, 10, 6), 'do LLLL', {locale: cs}) //=> '6. listopad'`\n *\n *    `format(new Date(2017, 10, 6), 'do MMMM', {locale: cs}) //=> '6. listopadu'`\n *\n * 2. Any sequence of the identical letters is a pattern, unless it is escaped by\n *    the single quote characters (see below).\n *    If the sequence is longer than listed in table (e.g. `EEEEEEEEEEE`)\n *    the output will be the same as default pattern for this unit, usually\n *    the longest one (in case of ISO weekdays, `EEEE`). Default patterns for units\n *    are marked with \"2\" in the last column of the table.\n *\n *    `format(new Date(2017, 10, 6), 'MMM') //=> 'Nov'`\n *\n *    `format(new Date(2017, 10, 6), 'MMMM') //=> 'November'`\n *\n *    `format(new Date(2017, 10, 6), 'MMMMM') //=> 'N'`\n *\n *    `format(new Date(2017, 10, 6), 'MMMMMM') //=> 'November'`\n *\n *    `format(new Date(2017, 10, 6), 'MMMMMMM') //=> 'November'`\n *\n * 3. Some patterns could be unlimited length (such as `yyyyyyyy`).\n *    The output will be padded with zeros to match the length of the pattern.\n *\n *    `format(new Date(2017, 10, 6), 'yyyyyyyy') //=> '00002017'`\n *\n * 4. `QQQQQ` and `qqqqq` could be not strictly numerical in some locales.\n *    These tokens represent the shortest form of the quarter.\n *\n * 5. The main difference between `y` and `u` patterns are B.C. years:\n *\n *    | Year | `y` | `u` |\n *    |------|-----|-----|\n *    | AC 1 |   1 |   1 |\n *    | BC 1 |   1 |   0 |\n *    | BC 2 |   2 |  -1 |\n *\n *    Also `yy` always returns the last two digits of a year,\n *    while `uu` pads single digit years to 2 characters and returns other years unchanged:\n *\n *    | Year | `yy` | `uu` |\n *    |------|------|------|\n *    | 1    |   01 |   01 |\n *    | 14   |   14 |   14 |\n *    | 376  |   76 |  376 |\n *    | 1453 |   53 | 1453 |\n *\n *    The same difference is true for local and ISO week-numbering years (`Y` and `R`),\n *    except local week-numbering years are dependent on `options.weekStartsOn`\n *    and `options.firstWeekContainsDate` (compare [getISOWeekYear](https://date-fns.org/docs/getISOWeekYear)\n *    and [getWeekYear](https://date-fns.org/docs/getWeekYear)).\n *\n * 6. Specific non-location timezones are currently unavailable in `date-fns`,\n *    so right now these tokens fall back to GMT timezones.\n *\n * 7. These patterns are not in the Unicode Technical Standard #35:\n *    - `i`: ISO day of week\n *    - `I`: ISO week of year\n *    - `R`: ISO week-numbering year\n *    - `t`: seconds timestamp\n *    - `T`: milliseconds timestamp\n *    - `o`: ordinal number modifier\n *    - `P`: long localized date\n *    - `p`: long localized time\n *\n * 8. `YY` and `YYYY` tokens represent week-numbering years but they are often confused with years.\n *    You should enable `options.useAdditionalWeekYearTokens` to use them. See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * 9. `D` and `DD` tokens represent days of the year but they are often confused with days of the month.\n *    You should enable `options.useAdditionalDayOfYearTokens` to use them. See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * @param date - The original date\n * @param format - The string of tokens\n * @param options - An object with options\n *\n * @returns The formatted date string\n *\n * @throws `date` must not be Invalid Date\n * @throws `options.locale` must contain `localize` property\n * @throws `options.locale` must contain `formatLong` property\n * @throws use `yyyy` instead of `YYYY` for formatting years using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws use `yy` instead of `YY` for formatting years using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws use `d` instead of `D` for formatting days of the month using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws use `dd` instead of `DD` for formatting days of the month using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws format string contains an unescaped latin alphabet character\n *\n * @example\n * // Represent 11 February 2014 in middle-endian format:\n * const result = format(new Date(2014, 1, 11), 'MM/dd/yyyy')\n * //=> '02/11/2014'\n *\n * @example\n * // Represent 2 July 2014 in Esperanto:\n * import { eoLocale } from 'date-fns/locale/eo'\n * const result = format(new Date(2014, 6, 2), \"do 'de' MMMM yyyy\", {\n *   locale: eoLocale\n * })\n * //=> '2-a de julio 2014'\n *\n * @example\n * // Escape string by single quote characters:\n * const result = format(new Date(2014, 6, 2, 15), \"h 'o''clock'\")\n * //=> \"3 o'clock\"\n */\nexport function format(date, formatStr, options) {\n  const defaultOptions = getDefaultOptions();\n  const locale = options?.locale ?? defaultOptions.locale ?? defaultLocale;\n\n  const firstWeekContainsDate =\n    options?.firstWeekContainsDate ??\n    options?.locale?.options?.firstWeekContainsDate ??\n    defaultOptions.firstWeekContainsDate ??\n    defaultOptions.locale?.options?.firstWeekContainsDate ??\n    1;\n\n  const weekStartsOn =\n    options?.weekStartsOn ??\n    options?.locale?.options?.weekStartsOn ??\n    defaultOptions.weekStartsOn ??\n    defaultOptions.locale?.options?.weekStartsOn ??\n    0;\n\n  const originalDate = toDate(date, options?.in);\n\n  if (!isValid(originalDate)) {\n    throw new RangeError(\"Invalid time value\");\n  }\n\n  let parts = formatStr\n    .match(longFormattingTokensRegExp)\n    .map((substring) => {\n      const firstCharacter = substring[0];\n      if (firstCharacter === \"p\" || firstCharacter === \"P\") {\n        const longFormatter = longFormatters[firstCharacter];\n        return longFormatter(substring, locale.formatLong);\n      }\n      return substring;\n    })\n    .join(\"\")\n    .match(formattingTokensRegExp)\n    .map((substring) => {\n      // Replace two single quote characters with one single quote character\n      if (substring === \"''\") {\n        return { isToken: false, value: \"'\" };\n      }\n\n      const firstCharacter = substring[0];\n      if (firstCharacter === \"'\") {\n        return { isToken: false, value: cleanEscapedString(substring) };\n      }\n\n      if (formatters[firstCharacter]) {\n        return { isToken: true, value: substring };\n      }\n\n      if (firstCharacter.match(unescapedLatinCharacterRegExp)) {\n        throw new RangeError(\n          \"Format string contains an unescaped latin alphabet character `\" +\n            firstCharacter +\n            \"`\",\n        );\n      }\n\n      return { isToken: false, value: substring };\n    });\n\n  // invoke localize preprocessor (only for french locales at the moment)\n  if (locale.localize.preprocessor) {\n    parts = locale.localize.preprocessor(originalDate, parts);\n  }\n\n  const formatterOptions = {\n    firstWeekContainsDate,\n    weekStartsOn,\n    locale,\n  };\n\n  return parts\n    .map((part) => {\n      if (!part.isToken) return part.value;\n\n      const token = part.value;\n\n      if (\n        (!options?.useAdditionalWeekYearTokens &&\n          isProtectedWeekYearToken(token)) ||\n        (!options?.useAdditionalDayOfYearTokens &&\n          isProtectedDayOfYearToken(token))\n      ) {\n        warnOrThrowProtectedError(token, formatStr, String(date));\n      }\n\n      const formatter = formatters[token[0]];\n      return formatter(originalDate, token, locale.localize, formatterOptions);\n    })\n    .join(\"\");\n}\n\nfunction cleanEscapedString(input) {\n  const matched = input.match(escapedStringRegExp);\n\n  if (!matched) {\n    return input;\n  }\n\n  return matched[1].replace(doubleQuoteRegExp, \"'\");\n}\n\n// Fallback for modularized imports:\nexport default format;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link addDays} function options.\n */\n\n/**\n * @name addDays\n * @category Day Helpers\n * @summary Add the specified number of days to the given date.\n *\n * @description\n * Add the specified number of days to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of days to be added.\n * @param options - An object with options\n *\n * @returns The new date with the days added\n *\n * @example\n * // Add 10 days to 1 September 2014:\n * const result = addDays(new Date(2014, 8, 1), 10)\n * //=> Thu Sep 11 2014 00:00:00\n */\nexport function addDays(date, amount, options) {\n  const _date = toDate(date, options?.in);\n  if (isNaN(amount)) return constructFrom(options?.in || date, NaN);\n\n  // If 0 days, no-op to avoid changing times in the hour before end of DST\n  if (!amount) return _date;\n\n  _date.setDate(_date.getDate() + amount);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default addDays;\n", "import { addDays } from \"./addDays.js\";\n\n/**\n * The {@link subDays} function options.\n */\n\n/**\n * @name subDays\n * @category Day Helpers\n * @summary Subtract the specified number of days from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of days to be subtracted.\n * @param options - An object with options\n *\n * @returns The new date with the days subtracted\n *\n * @example\n * // Subtract 10 days from 1 September 2014:\n * const result = subDays(new Date(2014, 8, 1), 10)\n * //=> Fri Aug 22 2014 00:00:00\n */\nexport function subDays(date, amount, options) {\n  return addDays(date, -amount, options);\n}\n\n// Fallback for modularized imports:\nexport default subDays;\n"], "names": [], "mappings": "yEAAA,IAAM,EAAuB,CAC3B,iBAAkB,CAChB,IAAK,qBACL,MAAO,6BACT,EAEA,SAAU,CACR,IAAK,WACL,MAAO,mBACT,EAEA,YAAa,gBAEb,iBAAkB,CAChB,IAAK,qBACL,MAAO,6BACT,EAEA,SAAU,CACR,IAAK,WACL,MAAO,mBACT,EAEA,YAAa,CACX,IAAK,eACL,MAAO,uBACT,EAEA,OAAQ,CACN,IAAK,SACL,MAAO,iBACT,EAEA,MAAO,CACL,IAAK,QACL,MAAO,gBACT,EAEA,YAAa,CACX,IAAK,eACL,MAAO,uBACT,EAEA,OAAQ,CACN,IAAK,SACL,MAAO,iBACT,EAEA,aAAc,CACZ,IAAK,gBACL,MAAO,wBACT,EAEA,QAAS,CACP,IAAK,UACL,MAAO,kBACT,EAEA,YAAa,CACX,IAAK,eACL,MAAO,uBACT,EAEA,OAAQ,CACN,IAAK,SACL,MAAO,iBACT,EAEA,WAAY,CACV,IAAK,cACL,MAAO,sBACT,EAEA,aAAc,CACZ,IAAK,gBACL,MAAO,wBACT,CACF,EC7EO,SAAS,EAAkB,CAAI,EACpC,OAAO,CAAC,EAAU,CAAC,CAAC,IAElB,IAAM,EAAQ,EAAQ,KAAK,CAAG,OAAO,EAAQ,KAAK,EAAI,EAAK,YAAY,CAEvE,OAAO,AADQ,EAAK,OAAO,CAAC,EAAM,EAAI,EAAK,OAAO,CAAC,EAAK,YAAY,CAAC,AAEvE,CACF,CCgBO,IAAM,EAAa,CACxB,KAAM,EAAkB,CACtB,QAvBgB,CAuBP,AAtBX,KAAM,mBACN,KAAM,aACN,OAAQ,WACR,MAAO,YACT,EAmBI,aAAc,MAChB,GAEA,KAAM,EAAkB,CACtB,QArBgB,CAClB,AAoBW,KApBL,iBACN,KAAM,cACN,OAAQ,YACR,MAAO,QACT,EAiBI,aAAc,MAChB,GAEA,SAAU,EAAkB,CAC1B,QAnBoB,CAmBX,AAlBX,KAAM,yBACN,KAAM,yBACN,OAAQ,qBACR,MAAO,oBACT,EAeI,aAAc,MAChB,EACF,ECtCM,EAAuB,CAC3B,SAAU,qBACV,UAAW,mBACX,MAAO,eACP,SAAU,kBACV,SAAU,cACV,MAAO,GACT,ECgCO,SAAS,EAAgB,CAAI,EAClC,MAAO,CAAC,EAAO,SAGT,EACJ,GAAI,AAAY,gBAHA,GAAS,QAAU,OAAO,EAAQ,OAAO,EAAI,YAAA,GAG7B,EAAK,gBAAgB,CAAE,CACrD,IAAM,EAAe,EAAK,sBAAsB,EAAI,EAAK,YAAY,CAC/D,EAAQ,GAAS,MAAQ,OAAO,EAAQ,KAAK,EAAI,EAEvD,EACE,EAAK,gBAAgB,CAAC,EAAM,EAAI,EAAK,gBAAgB,CAAC,EAAa,AACvE,KAAO,CACL,IAAM,EAAe,EAAK,YAAY,CAChC,EAAQ,GAAS,MAAQ,OAAO,EAAQ,KAAK,EAAI,EAAK,YAAY,CAExE,EAAc,EAAK,MAAM,CAAC,EAAM,EAAI,EAAK,MAAM,CAAC,EAAa,AAC/D,CAIA,OAAO,CAAW,CAHJ,AAGK,EAHA,gBAAgB,CAAG,EAAK,gBAAgB,CAAC,GAAS,EAG5C,AAC3B,CACF,CE7DO,SAAS,EAAa,CAAI,EAC/B,MAAO,CAAC,EAAQ,EAAU,CAAC,CAAC,IAC1B,IAqBI,EArBE,EAAQ,EAAQ,KAAK,CAErB,EACH,GAAS,EAAK,aAAa,CAAC,EAAM,EACnC,EAAK,aAAa,CAAC,EAAK,iBAAiB,CAAC,CACtC,EAAc,EAAO,KAAK,CAAC,GAEjC,GAAI,CAAC,EACH,OAAO,IADS,CAGlB,IAAM,EAAgB,CAAW,CAAC,EAAE,CAE9B,EACH,GAAS,EAAK,aAAa,CAAC,EAAM,EACnC,EAAK,aAAa,CAAC,EAAK,iBAAiB,CAAC,CAEtC,EAAM,MAAM,OAAO,CAAC,GA+B9B,AA9BQ,SA8BC,AAAU,CAAK,CAAE,CAAS,EACjC,IAAK,IAAI,EAAM,EAAG,EAAM,EAAM,MAAM,CAAE,IACpC,EAD2C,CACvC,EAAU,CAAK,CAAC,EAAI,EACtB,CADyB,MAClB,CAIb,EArCkB,EAAe,AAAC,GAAY,EAAQ,IAAI,CAAC,IAEnD,AAgBR,SAAS,AAAQ,CAAM,CAAE,CAAS,EAChC,IAAK,IAAM,KAAO,EAChB,GACE,EAFsB,KAEf,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,EAAQ,IAC7C,EAAU,CAAM,CAAC,EAAI,EAErB,CADA,MACO,CAIb,EA1BgB,EAAe,AAAC,GAAY,EAAQ,IAAI,CAAC,IAYrD,OARA,EAAQ,EAAK,aAAa,CAAG,EAAK,aAAa,CAAC,GAAO,EAQhD,CAAE,MAPT,EAAQ,EAAQ,aAAa,CAEzB,EAAQ,aAAa,CAAC,GACtB,EAIY,KAFH,EAAO,KAAK,CAAC,EAAc,MAAM,CAEzB,CACvB,CACF,CGrBO,IAAM,EAAO,CAClB,KAAM,QACN,eT+D4B,CS/DZ,AT+Da,EAAO,EAAO,KAG3C,IAFI,EAEE,EAAa,CAAoB,CAAC,EAAM,CAS9C,GAPE,EADwB,UAAU,AAAhC,OAAO,EACA,EACA,AAAU,GAAG,GACb,EAAW,GAAG,CAEd,EAAW,KAAK,CAAC,OAAO,CAAC,YAAa,EAAM,QAAQ,IAG3D,GAAS,UACX,CADsB,EAClB,EAAQ,UAAU,EAAI,EAAQ,UAAU,CAAG,EAC7C,CADgD,KACzC,MAAQ,OAEf,OAAO,EAAS,OAIpB,OAAO,CACT,ESnFE,WAAY,EACZ,eNT4B,CAAC,AMSb,ENToB,EAAO,EAAW,IACtD,CAAoB,CAAC,EAAM,CMS3B,SJyIsB,CACtB,AI1IU,cJiHU,CAAC,EAAa,KAClC,IAAM,EAAS,OAAO,GAShB,EAAS,EAAS,IACxB,GAAI,EAAS,IAAM,EAAS,GAC1B,CAD8B,MACtB,EAAS,IACf,KAAK,EACH,OAAO,EAAS,IAClB,MAAK,EACH,OAAO,EAAS,IAClB,MAAK,EACH,OAAO,EAAS,IACpB,CAEF,OAAO,EAAS,IAClB,EAKE,IAAK,EAAgB,CACnB,OA9Jc,CA8JN,AA7JV,OAAQ,CAAC,IAAK,IAAI,CAClB,YAAa,CAAC,KAAM,KAAK,CACzB,KAAM,CAAC,gBAAiB,cAAc,AACxC,EA2JI,aAAc,MAChB,GAEA,QAAS,EAAgB,CACvB,OA7JkB,CA6JV,AA5JV,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAI,CAC5B,YAAa,CAAC,KAAM,KAAM,KAAM,KAAK,CACrC,KAAM,CAAC,cAAe,cAAe,cAAe,cAAc,AACpE,EA0JI,aAAc,OACd,iBAAkB,AAAC,GAAY,EAAU,CAC3C,GAEA,MAAO,EAAgB,CACrB,OAzJgB,CAyJR,AAxJV,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAI,CACpE,YAAa,CACX,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACD,CAED,KAAM,CACJ,UACA,WACA,QACA,QACA,MACA,OACA,OACA,SACA,YACA,UACA,WACA,WACD,AACH,EA2HI,aAAc,MAChB,GAEA,IAAK,EAAgB,CACnB,OA7Hc,CA6HN,AA5HV,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAI,CAC3C,MAAO,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAK,CACjD,YAAa,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAM,CAC9D,KAAM,CACJ,SACA,SACA,UACA,YACA,WACA,SACA,WACD,AACH,EAiHI,aAAc,MAChB,GAEA,UAAW,EAAgB,CACzB,OAnHoB,CACtB,AAkHU,OAlHF,CACN,GAAI,IACJ,GAAI,IACJ,SAAU,KACV,KAAM,IACN,QAAS,UACT,UAAW,YACX,QAAS,UACT,MAAO,OACT,EACA,YAAa,CACX,GAAI,KACJ,GAAI,KACJ,SAAU,WACV,KAAM,OACN,QAAS,UACT,UAAW,YACX,QAAS,UACT,MAAO,OACT,EACA,KAAM,CACJ,GAAI,OACJ,GAAI,OACJ,SAAU,WACV,KAAM,OACN,QAAS,UACT,UAAW,YACX,QAAS,UACT,MAAO,OACT,CACF,EAqFI,aAAc,OACd,iBApF8B,CAChC,AAmFoB,OAnFZ,CACN,GAAI,IACJ,GAAI,IACJ,SAAU,KACV,KAAM,IACN,QAAS,iBACT,UAAW,mBACX,QAAS,iBACT,MAAO,UACT,EACA,YAAa,CACX,GAAI,KACJ,GAAI,KACJ,SAAU,WACV,KAAM,OACN,QAAS,iBACT,UAAW,mBACX,QAAS,iBACT,MAAO,UACT,EACA,KAAM,CACJ,GAAI,OACJ,GAAI,OACJ,SAAU,WACV,KAAM,OACN,QAAS,iBACT,UAAW,mBACX,QAAS,iBACT,MAAO,UACT,CACF,EAsDI,uBAAwB,MAC1B,EACF,EItKE,MDqEmB,CCrEZ,ADsEP,cAAe,AD1FV,SAAS,AAAoB,CAAI,EACtC,MAAO,CAAC,EAAQ,EAAU,CAAC,CAAC,IAC1B,IAAM,EAAc,EAAO,KAAK,CAAC,EAAK,YAAY,EAClD,GAAI,CAAC,EAAa,OAAO,KACzB,IAAM,EAAgB,CAAW,CAAC,EAAE,CAE9B,EAAc,EAAO,KAAK,CAAC,EAAK,YAAY,EAClD,GAAI,CAAC,EAAa,OAAO,KACzB,IAAI,EAAQ,EAAK,aAAa,CAC1B,EAAK,aAAa,CAAC,CAAW,CAAC,EAAE,EACjC,CAAW,CAAC,EAAE,CAOlB,OAAO,AAAE,MAJT,EAAQ,EAAQ,aAAa,CAAG,EAAQ,aAAa,CAAC,GAAS,EAI/C,KAFH,EAAO,KAAK,CAAC,EAAc,MAAM,EAGhD,CACF,ECuEqC,CACjC,aAxF8B,CAwFhB,uBACd,aAxF8B,CAwFhB,MACd,cAAe,AAAC,GAAU,SAAS,EAAO,GAC5C,GAEA,IAAK,EAAa,CAChB,cA3FqB,CA2FN,AA1FjB,OAAQ,UACR,YAAa,6DACb,KAAM,4DACR,EAwFI,kBAAmB,OACnB,cAxFqB,CAwFN,AAvFjB,IAAK,CAAC,MAAO,UAAU,AACzB,EAuFI,kBAAmB,KACrB,GAEA,QAAS,EAAa,CACpB,cAzFyB,CAyFV,AAxFjB,OAAQ,WACR,YAAa,YACb,KAAM,gCACR,EAsFI,kBAAmB,OACnB,cAtFyB,CAC3B,AAqFiB,IArFZ,CAAC,KAAM,KAAM,KAAM,KAAK,AAC/B,EAqFI,kBAAmB,MACnB,cAAe,AAAC,GAAU,EAAQ,CACpC,GAEA,MAAO,EAAa,CAClB,cAxFuB,CACzB,AAuFiB,OAvFT,eACR,YAAa,sDACb,KAAM,2FACR,EAqFI,kBAAmB,OACnB,cArFuB,CACzB,AAoFiB,OApFT,CACN,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACD,CAED,IAAK,CACH,OACA,MACA,QACA,OACA,QACA,QACA,QACA,OACA,MACA,MACA,MACA,MACD,AACH,EAwDI,kBAAmB,KACrB,GAEA,IAAK,EAAa,CAChB,cA1DqB,CA0DN,AAzDjB,OAAQ,YACR,MAAO,2BACP,YAAa,kCACb,KAAM,8DACR,EAsDI,kBAAmB,OACnB,cAtDqB,CACvB,AAqDiB,OArDT,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAM,CACzD,IAAK,CAAC,OAAQ,MAAO,OAAQ,MAAO,OAAQ,MAAO,OAAO,AAC5D,EAoDI,kBAAmB,KACrB,GAEA,UAAW,EAAa,CACtB,cAtD2B,CAsDZ,AArDjB,OAAQ,6DACR,IAAK,gFACP,EAoDI,kBAAmB,MACnB,cApD2B,CAoDZ,AAnDjB,IAAK,CACH,GAAI,MACJ,GAAI,MACJ,SAAU,OACV,KAAM,OACN,QAAS,WACT,UAAW,aACX,QAAS,WACT,MAAO,QACT,CACF,EA0CI,kBAAmB,KACrB,EACF,EC9GE,QAAS,CACP,aAAc,EACd,AADgB,UAAU,YACH,CACzB,CACF,ECzBI,EAAiB,CAAC,ECuNT,EAAsB,OAAO,GAAG,CAAC,qBClLvC,SAAS,EAAc,CAAI,CAAE,CAAK,QACvC,AAAoB,YAAY,AAA5B,OAAO,EAA4B,EAAK,GAExC,GAAwB,UAAhB,OAAO,GAAqB,KAAuB,EACtD,CAAI,CAAC,EAAZ,AAAgC,CAAC,GAE/B,aAAgB,KAAa,CAAP,GAAW,EAAK,WAAW,CAAC,GAE/C,IAAI,KAAK,EAClB,CCNO,SAAS,EAAO,CAAQ,CAAE,CAAO,EAEtC,OAAO,EAAc,GAAW,EAAU,EAC5C,CC9BO,SAAS,EAAgC,CAAI,EAClD,IAAM,EAAQ,EAAO,GACf,EAAU,IAAI,KAClB,KAAK,GAAG,CACN,EAAM,WAAW,GACjB,EAAM,QAAQ,GACd,EAAM,OAAO,GACb,EAAM,QAAQ,GACd,EAAM,UAAU,GAChB,EAAM,UAAU,GAChB,EAAM,eAAe,KAIzB,OADA,EAAQ,cAAc,CAAC,EAAM,WAAW,IACjC,AAAC,EAAQ,CAClB,CEAO,GFDU,MECD,EAAW,CAAI,CAAE,CAAO,EACtC,IAAM,EAAQ,EAAO,EAAM,GAAS,IAEpC,OADA,EAAM,QAAQ,CAAC,EAAG,EAAG,EAAG,GACjB,CACT,CIEO,SAAS,EAAY,CAAI,CAAE,CAAO,EAEvC,IAAM,EACJ,GAAS,cACT,GAAS,QAAQ,SAAS,cAC1B,EAAe,YAAY,EAC3B,EAAe,MAAM,EAAE,SAAS,cAChC,EAEI,EAAQ,EAAO,EAAM,GAAS,IAC9B,EAAM,EAAM,MAAM,GAKxB,OAFA,EAAM,OAAO,CAAC,EAAM,OAAO,IAFd,AAAsB,CAEH,EAFlB,CAAyB,CAAnB,AAAoB,CAApB,EAAwB,EAAM,CAAA,GAGlD,EAAM,QAAQ,CAAC,EAAG,EAAG,EAAG,GACjB,CACT,CCpBO,SAAS,EAAe,CAAI,CAAE,CAAO,EAC1C,OAAO,EAAY,EAAM,CAAE,GAAG,CAAO,CAAE,aAAc,CAAE,EACzD,CCJO,SAAS,EAAe,CAAI,CAAE,CAAO,EAC1C,IAAM,EAAQ,EAAO,EAAM,GAAS,IAC9B,EAAO,EAAM,WAAW,GAExB,EAA4B,EAAc,EAAO,GACvD,EAA0B,WAAW,CAAC,EAAO,EAAG,EAAG,GACnD,EAA0B,QAAQ,CAAC,EAAG,EAAG,EAAG,GAC5C,IAAM,EAAkB,EAAe,GAEjC,EAA4B,EAAc,EAAO,GACvD,EAA0B,WAAW,CAAC,EAAM,EAAG,GAC/C,EAA0B,QAAQ,CAAC,EAAG,EAAG,EAAG,GAC5C,IAAM,EAAkB,EAAe,UAEvC,AAAI,EAAM,OAAO,IAAM,EAAgB,OAAO,GACrC,CADyC,CAClC,EACL,EAAM,OAAO,IAAM,EAAgB,OAAO,GAC5C,CADgD,CAGhD,EAAO,CAElB,CGNO,SAAS,EAAY,CAAI,CAAE,CAAO,EACvC,IAAM,EAAQ,EAAO,EAAM,GAAS,IAC9B,EAAO,EAAM,WAAW,GAGxB,EACJ,GAAS,uBACT,GAAS,QAAQ,SAAS,uBAC1B,EAAe,qBAAqB,EACpC,EAAe,MAAM,EAAE,SAAS,uBAChC,EAEI,EAAsB,EAAc,GAAS,IAAM,EAAM,GAC/D,EAAoB,WAAW,CAAC,EAAO,EAAG,EAAG,GAC7C,EAAoB,QAAQ,CAAC,EAAG,EAAG,EAAG,GACtC,IAAM,EAAkB,EAAY,EAAqB,GAEnD,EAAsB,EAAc,GAAS,IAAM,EAAM,GAC/D,EAAoB,WAAW,CAAC,EAAM,EAAG,GACzC,EAAoB,QAAQ,CAAC,EAAG,EAAG,EAAG,GACtC,IAAM,EAAkB,EAAY,EAAqB,SAEzD,AAAI,CAAC,GAAS,CAAC,EACN,EAAO,EACL,CAAC,GAAS,CAAC,EACb,EAEA,EAAO,AALgB,CAOlC,CGxEO,SHmEkC,AGnEzB,EAAgB,CAAM,CAAE,CAAY,EAElD,IAAM,EAAS,KAAK,GAAG,CAAC,GAAQ,QAAQ,GAAG,QAAQ,CAAC,EAAc,KAClE,MAAO,CAFM,EAAS,EAAI,IAAM,EAAA,EAElB,CAChB,CCWO,IAAM,EAAkB,CAE7B,EAAE,CAAI,CAAE,CAAK,EAUX,IAAM,EAAa,EAAK,WAAW,GAE7B,EAAO,EAAa,EAAI,EAAa,EAAI,EAC/C,OAAO,EAA0B,OAAV,EAAiB,EAAO,IAAM,EAAM,EAAM,MAAM,CACzE,EAGA,EAAE,CAAI,CAAE,CAAK,EACX,IAAM,EAAQ,EAAK,QAAQ,GAC3B,MAAiB,MAAV,EAAgB,OAAO,EAAQ,GAAK,EAAgB,EAAQ,EAAG,EACxE,IAGA,CAAE,EAAM,EAAF,EACG,CADI,CACY,EAAK,OAAO,GAAI,EAAM,MAAM,EAIrD,EAAE,CAAI,CAAE,CAAK,EACX,IAAM,EAAqB,EAAK,QAAQ,GAAK,IAAM,EAAI,KAAO,KAE9D,OAAQ,GACN,IAAK,IACL,IAAK,KACH,OAAO,EAAmB,WAAW,EACvC,KAAK,MACH,OAAO,CACT,KAAK,QACH,OAAO,CAAkB,CAAC,EAC5B,AAD8B,KACzB,IAEH,MAAO,AAAuB,SAAO,OAAS,MAClD,CACF,IAGA,CAAE,EAAM,EAAF,EACG,CADI,CACY,EAAK,QAAQ,GAAK,IAAM,GAAI,EAAM,MAAM,IAIjE,CAAE,EAAM,EAAF,EACG,CADI,CACY,EAAK,QAAQ,GAAI,EAAM,MAAM,IAItD,CAAE,EAAM,EAAF,EACG,CADI,CACY,EAAK,UAAU,GAAI,EAAM,MAAM,IAIxD,CAAE,EAAM,EAAF,EACG,CADI,CACY,EAAK,UAAU,GAAI,EAAM,MAAM,EAIxD,EAAE,CAAI,CAAE,CAAK,EACX,IAAM,EAAiB,EAAM,MAAM,CAKnC,OAAO,EAHmB,KAAK,KAAK,CADf,AAEnB,EAFwB,CAIH,cAJkB,GAExB,KAAK,GAAG,CAAC,GAAI,EAAiB,IAEL,EAAM,MAAM,CACxD,CACF,EClFM,EAAgB,CAGpB,SAAU,WACV,KAAM,OACN,QAAS,UACT,UAAW,YACX,QAAS,UACT,MAAO,OACT,EAgDa,EAAa,CAExB,EAAG,SAAU,CAAI,CAAE,CAAK,CAAE,CAAQ,EAChC,IAAM,IAAM,EAAK,WAAW,IAAK,EACjC,EADqC,IAAI,CACjC,GAEN,IAAK,IACL,IAAK,KACL,IAAK,MACH,OAAO,EAAS,GAAG,CAAC,EAAK,CAAE,MAAO,aAAc,EAElD,KAAK,QACH,OAAO,EAAS,GAAG,CAAC,EAAK,CAAE,MAAO,QAAS,EAE7C,KAAK,IAEH,OAAO,EAAS,GAAG,CAAC,EAAK,CAAE,MAAO,MAAO,EAC7C,CACF,EAGA,EAAG,SAAU,CAAI,CAAE,CAAK,CAAE,CAAQ,EAEhC,GAAc,OAAV,EAAgB,CAClB,IAAM,EAAa,EAAK,WAAW,GAGnC,OAAO,EAAS,aAAa,CADhB,AACiB,EADJ,EAAI,EAAa,EAAI,EACX,CAAE,KAAM,MAAO,EACrD,CAEA,OAAO,EAAgB,CAAC,CAAC,EAAM,EACjC,EAGA,EAAG,SAAU,CAAI,CAAE,CAAK,CAAE,CAAQ,CAAE,CAAO,EACzC,IAAM,EAAiB,EAAY,EAAM,GAEnC,EAAW,EAAiB,EAAI,EAAiB,EAAI,QAG3D,AAAc,MAAM,CAAhB,EAEK,EADc,EAAW,IACK,GAIzB,KAJW,CAIL,CAAhB,EACK,EAAS,aAAa,CAAC,EAAU,CAAE,KAAM,MAAO,GAIlD,EAAgB,EAAU,EAAM,MAAM,CAC/C,EAGA,EAAG,SAAU,CAAI,CAAE,CAAK,EAItB,OAAO,EAHa,EAAe,GAGC,EAAM,MAAM,CAAzB,AACzB,EAWA,EAAG,SAAU,CAAI,CAAE,CAAK,EAEtB,OAAO,EADM,EAAK,WAAW,CACN,EAAM,EAAM,MAAM,CAC3C,EAGA,EAAG,SAAU,CAAI,CAAE,CAAK,CAAE,CAAQ,EAChC,IAAM,EAAU,KAAK,IAAI,CAAC,CAAC,EAAK,QAAQ,IAAK,CAAC,CAAI,GAClD,OAAQ,GAEN,IAAK,IACH,OAAO,OAAO,EAEhB,KAAK,KACH,OAAO,EAAgB,EAAS,EAElC,KAAK,KACH,OAAO,EAAS,aAAa,CAAC,EAAS,CAAE,KAAM,SAAU,EAE3D,KAAK,MACH,OAAO,EAAS,OAAO,CAAC,EAAS,CAC/B,MAAO,cACP,QAAS,YACX,EAEF,KAAK,QACH,OAAO,EAAS,OAAO,CAAC,EAAS,CAC/B,MAAO,SACP,QAAS,YACX,EAEF,KAAK,IAEH,OAAO,EAAS,OAAO,CAAC,EAAS,CAC/B,MAAO,OACP,QAAS,YACX,EACJ,CACF,EAGA,EAAG,SAAU,CAAI,CAAE,CAAK,CAAE,CAAQ,EAChC,IAAM,EAAU,KAAK,IAAI,CAAC,CAAC,EAAK,QAAQ,IAAK,CAAC,CAAI,GAClD,OAAQ,GAEN,IAAK,IACH,OAAO,OAAO,EAEhB,KAAK,KACH,OAAO,EAAgB,EAAS,EAElC,KAAK,KACH,OAAO,EAAS,aAAa,CAAC,EAAS,CAAE,KAAM,SAAU,EAE3D,KAAK,MACH,OAAO,EAAS,OAAO,CAAC,EAAS,CAC/B,MAAO,cACP,QAAS,YACX,EAEF,KAAK,QACH,OAAO,EAAS,OAAO,CAAC,EAAS,CAC/B,MAAO,SACP,QAAS,YACX,EAEF,KAAK,IAEH,OAAO,EAAS,OAAO,CAAC,EAAS,CAC/B,MAAO,OACP,QAAS,YACX,EACJ,CACF,EAGA,EAAG,SAAU,CAAI,CAAE,CAAK,CAAE,CAAQ,EAChC,IAAM,EAAQ,EAAK,QAAQ,GAC3B,OAAQ,GACN,IAAK,IACL,IAAK,KACH,OAAO,EAAgB,CAAC,CAAC,EAAM,EAEjC,KAAK,KACH,OAAO,EAAS,aAAa,CAAC,EAAQ,EAAG,CAAE,KAAM,OAAQ,EAE3D,KAAK,MACH,OAAO,EAAS,KAAK,CAAC,EAAO,CAC3B,MAAO,cACP,QAAS,YACX,EAEF,KAAK,QACH,OAAO,EAAS,KAAK,CAAC,EAAO,CAC3B,MAAO,SACP,QAAS,YACX,EAEF,KAAK,IAEH,OAAO,EAAS,KAAK,CAAC,EAAO,CAAE,MAAO,OAAQ,QAAS,YAAa,EACxE,CACF,EAGA,EAAG,SAAU,CAAI,CAAE,CAAK,CAAE,CAAQ,EAChC,IAAM,EAAQ,EAAK,QAAQ,GAC3B,OAAQ,GAEN,IAAK,IACH,OAAO,OAAO,EAAQ,EAExB,KAAK,KACH,OAAO,EAAgB,EAAQ,EAAG,EAEpC,KAAK,KACH,OAAO,EAAS,aAAa,CAAC,EAAQ,EAAG,CAAE,KAAM,OAAQ,EAE3D,KAAK,MACH,OAAO,EAAS,KAAK,CAAC,EAAO,CAC3B,MAAO,cACP,QAAS,YACX,EAEF,KAAK,QACH,OAAO,EAAS,KAAK,CAAC,EAAO,CAC3B,MAAO,SACP,QAAS,YACX,EAEF,KAAK,IAEH,OAAO,EAAS,KAAK,CAAC,EAAO,CAAE,MAAO,OAAQ,QAAS,YAAa,EACxE,CACF,EAGA,EAAG,SAAU,CAAI,CAAE,CAAK,CAAE,CAAQ,CAAE,CAAO,EACzC,IAAM,EAAO,AHxOV,SAAS,AAAQ,CAAI,CAAE,CAAO,EACnC,IAAM,EAAQ,EAAO,EAAM,GAAS,IAMpC,OAAO,KAAK,KAAK,CAAC,CALL,AAAC,EAAY,EAAO,EAKR,CDJpB,ACDwC,QAAD,CDC9B,AAAgB,CAAI,CAAE,CAAO,EAE3C,IAAM,EACJ,GAAS,uBACT,GAAS,QAAQ,SAAS,uBAC1B,EAAe,qBAAqB,EACpC,EAAe,MAAM,EAAE,SAAS,uBAChC,EAEI,EAAO,EAAY,EAAM,GACzB,EAAY,EAAc,GAAS,IAAM,EAAM,GAIrD,OAHA,AAGO,EAHG,WAAW,CAAC,EAAM,EAAG,GAC/B,EAAU,QAAQ,CAAC,EAAG,EAAG,EAAG,GACd,EAAY,EAAW,EAEvC,EChB+D,EAAO,EAAA,UAKrB,CACjD,EGgOyB,EAAM,SAE3B,AAAc,MAAM,CAAhB,EACK,EAAS,aAAa,CAAC,EAAM,CAAE,KAAM,MAAO,GAG9C,EAAgB,EAAM,EAAM,MAAM,CAC3C,EAGA,EAAG,SAAU,CAAI,CAAE,CAAK,CAAE,CAAQ,EAChC,IAAM,ENjQH,AMiQa,SNjQO,AAAX,CAAe,CAAE,CAAO,EACtC,IAAM,EAAQ,EAAO,OAAM,GAM3B,MANoC,CAM7B,KAAK,KAAK,CAAC,CALJ,AAAD,EAAgB,GAAU,ADElC,CCGoB,KALa,GDExB,AAAmB,CAAI,EAAS,EAC9C,IAAM,EAAO,EAAe,MAAM,CADK,GAEjC,EAAkB,EAA6B,EAAM,GAG3D,OAHsC,AACtC,EAAgB,OAD+B,IACpB,CAAC,EAAM,EAAG,GACrC,EAAgB,QAAQ,CAAC,EAAG,EAAG,EAAG,GAC3B,EAAe,EACxB,ECR4D,EAAA,Eb6C1B,QaxCe,CACjD,EMyP+B,SAE3B,AAAc,MAAM,CAAhB,EACK,EAAS,aAAa,CAAC,EAAS,CAAE,KAAM,MAAO,GAGjD,EAAgB,EAAS,EAAM,MAAM,CAC9C,EAGA,EAAG,SAAU,CAAI,CAAE,CAAK,CAAE,CAAQ,QAChC,AAAI,AAAU,MAAM,GACX,EAAS,aAAa,CAAC,EAAK,OAAO,GAAI,CAAE,KAAM,MAAO,GAGxD,EAAgB,CAAC,CAAC,EAAM,EACjC,EAGA,EAAG,SAAU,CAAI,CAAE,CAAK,CAAE,CAAQ,EAChC,IAAM,EXxRH,AWwRe,SXxRN,AAAa,CAAI,CAAE,CAAO,EACxC,IAAM,EAAQ,EAAO,EAAM,QAG3B,CAHoC,MACvB,AACK,AACX,AFUF,SAAS,AAAyB,CAAS,CAAE,CAAW,CAAE,CAAO,EACtE,GAAM,CAAC,EAAY,EAAa,CAAG,AFvC9B,SAAwB,AAAf,CAAsB,CAAE,GAAG,CAAK,EAC9C,IAAM,EAAY,EAAc,IAAI,CAClC,KACA,GAAW,EAAM,IAAI,CAAC,AAAC,GAAyB,UAAhB,OAAO,IAEzC,OAAO,EAAM,GAAG,CAAC,EACnB,OEkCI,EACA,EACA,GAGI,EAAkB,AALb,EAKwB,GAC7B,EAAoB,EAAW,GAUrC,OAAO,KAAK,KAAK,CAAC,CAPf,AAOgB,EAPE,EAAgC,IAEnD,AAAC,EAAoB,EAAgC,EAAA,CAKnB,CAAgB,CNyBrB,AMzBK,GAAoB,GAC1D,EE/BwC,EAAO,ADAxC,SAAS,AAAY,CAAI,CAAE,CAAO,EACvC,IAAM,EAAQ,EAAO,OAAM,GAG3B,MAHoC,CACpC,EAAM,WAAW,CAAC,EAAM,WAAW,GAAI,EAAG,GAC1C,EAAM,QAAQ,CAAC,EAAG,EAAG,EAAG,GACjB,CACT,ECL2D,IAChC,CAE3B,EWmRmC,SAE/B,AAAI,AAAU,MAAM,GACX,EAAS,aAAa,CAAC,EAAW,CAAE,KAAM,WAAY,GAGxD,EAAgB,EAAW,EAAM,MAAM,CAChD,EAGA,EAAG,SAAU,CAAI,CAAE,CAAK,CAAE,CAAQ,EAChC,IAAM,EAAY,EAAK,MAAM,GAC7B,OAAQ,GAEN,IAAK,IACL,IAAK,KACL,IAAK,MACH,OAAO,EAAS,GAAG,CAAC,EAAW,CAC7B,MAAO,cACP,QAAS,YACX,EAEF,KAAK,QACH,OAAO,EAAS,GAAG,CAAC,EAAW,CAC7B,MAAO,SACP,QAAS,YACX,EAEF,KAAK,SACH,OAAO,EAAS,GAAG,CAAC,EAAW,CAC7B,MAAO,QACP,QAAS,YACX,EAEF,KAAK,IAEH,OAAO,EAAS,GAAG,CAAC,EAAW,CAC7B,MAAO,OACP,QAAS,YACX,EACJ,CACF,EAGA,EAAG,SAAU,CAAI,CAAE,CAAK,CAAE,CAAQ,CAAE,CAAO,EACzC,IAAM,EAAY,EAAK,MAAM,GACvB,EAAiB,CAAC,EAAY,EAAQ,YAAY,EAAG,CAAC,CAAI,GAAK,EACrE,OAAQ,GAEN,IAAK,IACH,OAAO,OAAO,EAEhB,KAAK,KACH,OAAO,EAAgB,EAAgB,EAEzC,KAAK,KACH,OAAO,EAAS,aAAa,CAAC,EAAgB,CAAE,KAAM,KAAM,EAC9D,KAAK,MACH,OAAO,EAAS,GAAG,CAAC,EAAW,CAC7B,MAAO,cACP,QAAS,YACX,EAEF,KAAK,QACH,OAAO,EAAS,GAAG,CAAC,EAAW,CAC7B,MAAO,SACP,QAAS,YACX,EAEF,KAAK,SACH,OAAO,EAAS,GAAG,CAAC,EAAW,CAC7B,MAAO,QACP,QAAS,YACX,EAEF,KAAK,IAEH,OAAO,EAAS,GAAG,CAAC,EAAW,CAC7B,MAAO,OACP,QAAS,YACX,EACJ,CACF,EAGA,EAAG,SAAU,CAAI,CAAE,CAAK,CAAE,CAAQ,CAAE,CAAO,EACzC,IAAM,EAAY,EAAK,MAAM,GACvB,EAAiB,CAAC,EAAY,EAAQ,YAAY,EAAG,CAAC,CAAI,GAAK,EACrE,OAAQ,GAEN,IAAK,IACH,OAAO,OAAO,EAEhB,KAAK,KACH,OAAO,EAAgB,EAAgB,EAAM,MAAM,CAErD,KAAK,KACH,OAAO,EAAS,aAAa,CAAC,EAAgB,CAAE,KAAM,KAAM,EAC9D,KAAK,MACH,OAAO,EAAS,GAAG,CAAC,EAAW,CAC7B,MAAO,cACP,QAAS,YACX,EAEF,KAAK,QACH,OAAO,EAAS,GAAG,CAAC,EAAW,CAC7B,MAAO,SACP,QAAS,YACX,EAEF,KAAK,SACH,OAAO,EAAS,GAAG,CAAC,EAAW,CAC7B,MAAO,QACP,QAAS,YACX,EAEF,KAAK,IAEH,OAAO,EAAS,GAAG,CAAC,EAAW,CAC7B,MAAO,OACP,QAAS,YACX,EACJ,CACF,EAGA,EAAG,SAAU,CAAI,CAAE,CAAK,CAAE,CAAQ,EAChC,IAAM,EAAY,EAAK,MAAM,GACvB,EAAe,AAAc,MAAI,EAAI,EAC3C,OAAQ,GAEN,IAAK,IACH,OAAO,OAAO,EAEhB,KAAK,KACH,OAAO,EAAgB,EAAc,EAAM,MAAM,CAEnD,KAAK,KACH,OAAO,EAAS,aAAa,CAAC,EAAc,CAAE,KAAM,KAAM,EAE5D,KAAK,MACH,OAAO,EAAS,GAAG,CAAC,EAAW,CAC7B,MAAO,cACP,QAAS,YACX,EAEF,KAAK,QACH,OAAO,EAAS,GAAG,CAAC,EAAW,CAC7B,MAAO,SACP,QAAS,YACX,EAEF,KAAK,SACH,OAAO,EAAS,GAAG,CAAC,EAAW,CAC7B,MAAO,QACP,QAAS,YACX,EAEF,KAAK,IAEH,OAAO,EAAS,GAAG,CAAC,EAAW,CAC7B,MAAO,OACP,QAAS,YACX,EACJ,CACF,EAGA,EAAG,SAAU,CAAI,CAAE,CAAK,CAAE,CAAQ,EAEhC,IAAM,EADQ,AACa,EADR,QAAQ,GACQ,IAAM,EAAI,KAAO,KAEpD,OAAQ,GACN,IAAK,IACL,IAAK,KACH,OAAO,EAAS,SAAS,CAAC,EAAoB,CAC5C,MAAO,cACP,QAAS,YACX,EACF,KAAK,MACH,OAAO,EACJ,SAAS,CAAC,EAAoB,CAC7B,MAAO,cACP,QAAS,YACX,GACC,WAAW,EAChB,KAAK,QACH,OAAO,EAAS,SAAS,CAAC,EAAoB,CAC5C,MAAO,SACP,QAAS,YACX,EACF,KAAK,IAEH,OAAO,EAAS,SAAS,CAAC,EAAoB,CAC5C,MAAO,OACP,QAAS,YACX,EACJ,CACF,EAGA,EAAG,SAAU,CAAI,CAAE,CAAK,CAAE,CAAQ,EAChC,IACI,EADE,EAAQ,EAAK,QAAQ,GAU3B,OAPE,EADY,IAAI,CAAd,EACmB,EAAc,IAAI,CACpB,GAAG,CAAb,EACY,EAAc,QAAQ,CAEtB,EAAQ,IAAM,EAAI,KAAO,KAGxC,GACN,IAAK,IACL,IAAK,KACH,OAAO,EAAS,SAAS,CAAC,EAAoB,CAC5C,MAAO,cACP,QAAS,YACX,EACF,KAAK,MACH,OAAO,EACJ,SAAS,CAAC,EAAoB,CAC7B,MAAO,cACP,QAAS,YACX,GACC,WAAW,EAChB,KAAK,QACH,OAAO,EAAS,SAAS,CAAC,EAAoB,CAC5C,MAAO,SACP,QAAS,YACX,EACF,KAAK,IAEH,OAAO,EAAS,SAAS,CAAC,EAAoB,CAC5C,MAAO,OACP,QAAS,YACX,EACJ,CACF,EAGA,EAAG,SAAU,CAAI,CAAE,CAAK,CAAE,CAAQ,EAChC,IACI,EADE,EAAQ,EAAK,QAAQ,GAY3B,OATE,EADE,GAAS,GACU,CADN,CACoB,OAAO,CACjC,GAAS,GACG,CADC,CACa,SAAS,CACnC,GAAS,EACG,CADA,CACc,OAAO,CAErB,EAAc,KAAK,CAGlC,GACN,IAAK,IACL,IAAK,KACL,IAAK,MACH,OAAO,EAAS,SAAS,CAAC,EAAoB,CAC5C,MAAO,cACP,QAAS,YACX,EACF,KAAK,QACH,OAAO,EAAS,SAAS,CAAC,EAAoB,CAC5C,MAAO,SACP,QAAS,YACX,EACF,KAAK,IAEH,OAAO,EAAS,SAAS,CAAC,EAAoB,CAC5C,MAAO,OACP,QAAS,YACX,EACJ,CACF,EAGA,EAAG,SAAU,CAAI,CAAE,CAAK,CAAE,CAAQ,EAChC,GAAc,OAAV,EAAgB,CAClB,IAAI,EAAQ,EAAK,QAAQ,GAAK,GAE9B,OADc,IAAV,IAAa,EAAQ,EAAA,EAClB,EAAS,aAAa,CAAC,EAAO,CAAE,KAAM,MAAO,EACtD,CAEA,OAAO,EAAgB,CAAC,CAAC,EAAM,EACjC,EAGA,EAAG,SAAU,CAAI,CAAE,CAAK,CAAE,CAAQ,QAChC,AAAc,MAAM,CAAhB,EACK,EAAS,aAAa,CAAC,EAAK,QAAQ,GAAI,CAAE,KAAM,MAAO,GAGzD,EAAgB,CAAC,CAAC,EAAM,EACjC,EAGA,EAAG,SAAU,CAAI,CAAE,CAAK,CAAE,CAAQ,EAChC,IAAM,EAAQ,EAAK,QAAQ,GAAK,SAEhC,AAAI,AAAU,MAAM,GACX,EAAS,aAAa,CAAC,EAAO,CAAE,KAAM,MAAO,GAG/C,EAAgB,EAAO,EAAM,MAAM,CAC5C,EAGA,EAAG,SAAU,CAAI,CAAE,CAAK,CAAE,CAAQ,EAChC,IAAI,EAAQ,EAAK,QAAQ,SAGzB,CAFc,IAAV,IAAa,EAAQ,EAAA,EAEX,MAAM,CAAhB,GACK,EAAS,aAAa,CAAC,EAAO,CAAE,KAAM,MAAO,GAG/C,EAAgB,EAAO,EAAM,MAAM,CAC5C,EAGA,EAAG,SAAU,CAAI,CAAE,CAAK,CAAE,CAAQ,QAChC,AAAc,MAAM,CAAhB,EACK,EAAS,aAAa,CAAC,EAAK,UAAU,GAAI,CAAE,KAAM,QAAS,GAG7D,EAAgB,CAAC,CAAC,EAAM,EACjC,EAGA,EAAG,SAAU,CAAI,CAAE,CAAK,CAAE,CAAQ,QAChC,AAAI,AAAU,MAAM,GACX,EAAS,aAAa,CAAC,EAAK,UAAU,GAAI,CAAE,KAAM,QAAS,GAG7D,EAAgB,CAAC,CAAC,EAAM,EACjC,EAGA,EAAG,SAAU,CAAI,CAAE,CAAK,EACtB,OAAO,EAAgB,CAAC,CAAC,EAAM,EACjC,EAGA,EAAG,SAAU,CAAI,CAAE,CAAK,CAAE,CAAS,EACjC,IAAM,EAAiB,EAAK,iBAAiB,GAE7C,GAAuB,GAAG,CAAtB,EACF,MAAO,IAGT,OAAQ,GAEN,IAAK,IACH,OAAO,EAAkC,EAK3C,KAAK,OACL,IAAK,KACH,OAAO,EAAe,EAKxB,KAAK,IAGH,OAAO,EAAe,EAAgB,IAC1C,CACF,EAGA,EAAG,SAAU,CAAI,CAAE,CAAK,CAAE,CAAS,EACjC,IAAM,EAAiB,EAAK,iBAAiB,GAE7C,OAAQ,GAEN,IAAK,IACH,OAAO,EAAkC,EAK3C,KAAK,OACL,IAAK,KACH,OAAO,EAAe,EAKxB,KAAK,IAGH,OAAO,EAAe,EAAgB,IAC1C,CACF,EAGA,EAAG,SAAU,CAAI,CAAE,CAAK,CAAE,CAAS,EACjC,IAAM,EAAiB,EAAK,iBAAiB,GAE7C,OAAQ,GAEN,IAAK,IACL,IAAK,KACL,IAAK,MACH,MAAO,MAAQ,EAAoB,EAAgB,IAErD,KAAK,IAEH,MAAO,MAAQ,EAAe,EAAgB,IAClD,CACF,EAGA,EAAG,SAAU,CAAI,CAAE,CAAK,CAAE,CAAS,EACjC,IAAM,EAAiB,EAAK,iBAAiB,GAE7C,OAAQ,GAEN,IAAK,IACL,IAAK,KACL,IAAK,MACH,MAAO,MAAQ,EAAoB,EAAgB,IAErD,KAAK,IAEH,MAAO,MAAQ,EAAe,EAAgB,IAClD,CACF,EAGA,EAAG,SAAU,CAAI,CAAE,CAAK,CAAE,CAAS,EAEjC,OAAO,EADW,KAAK,KAAK,CAAC,AAAC,EAAO,CACd,IAAW,EAAM,MAAM,CAChD,EAGA,EAAG,SAAU,CAAI,CAAE,CAAK,CAAE,CAAS,EACjC,OAAO,EAAgB,CAAC,EAAM,EAAM,MAAM,CAC5C,CACF,EAEA,SAAS,EAAoB,CAAM,CAAE,EAAY,EAAE,EACjD,IAAM,EAAO,EAAS,EAAI,IAAM,IAC1B,EAAY,KAAK,GAAG,CAAC,GACrB,EAAQ,KAAK,KAAK,CAAC,EAAY,IAC/B,EAAU,EAAY,UAC5B,AAAI,AAAY,GAAG,GACV,EAAO,OAAO,GAEhB,EAAO,OAAO,GAAS,EAAY,EAAgB,EAAS,EACrE,CAEA,SAAS,EAAkC,CAAM,CAAE,CAAS,SAC1D,AAAI,EAAS,IAAO,EAEX,CAFc,AACR,EAAS,EAAI,IAAM,GAAA,EAClB,EAAgB,KAAK,GAAG,CAAC,GAAU,GAAI,GAEhD,EAAe,EAAQ,EAChC,CAEA,SAAS,EAAe,CAAM,CAAE,EAAY,EAAE,EAE5C,IAAM,EAAY,KAAK,GAAG,CAAC,GAG3B,MAAO,CAJM,EAAS,EAAI,EAIZ,EAJkB,GAAA,EAElB,EAAgB,KAAK,KAAK,CAAC,EAAY,IAAK,GAEpC,EADN,EAAgB,EAAY,GAAI,EAElD,CCvwBA,ADswBoC,ICtwB9B,EAAoB,CAAC,EAAS,KAClC,OAAQ,GACN,IAAK,IACH,OAAO,EAAW,IAAI,CAAC,CAAE,MAAO,OAAQ,EAC1C,KAAK,KACH,OAAO,EAAW,IAAI,CAAC,CAAE,MAAO,QAAS,EAC3C,KAAK,MACH,OAAO,EAAW,IAAI,CAAC,CAAE,MAAO,MAAO,EACzC,KAAK,IAEH,OAAO,EAAW,IAAI,CAAC,CAAE,MAAO,MAAO,EAC3C,CACF,EAEM,EAAoB,CAAC,EAAS,KAClC,OAAQ,GACN,IAAK,IACH,OAAO,EAAW,IAAI,CAAC,CAAE,MAAO,OAAQ,EAC1C,KAAK,KACH,OAAO,EAAW,IAAI,CAAC,CAAE,MAAO,QAAS,EAC3C,KAAK,MACH,OAAO,EAAW,IAAI,CAAC,CAAE,MAAO,MAAO,EACzC,KAAK,IAEH,OAAO,EAAW,IAAI,CAAC,CAAE,MAAO,MAAO,EAC3C,CACF,EAkCa,EAAiB,CAC5B,EAAG,EACH,EAlC4B,CAkCzB,AAlC0B,EAAS,KACtC,IAQI,EARE,EAAc,EAAQ,KAAK,CAAC,cAAgB,EAAE,CAC9C,EAAc,CAAW,CAAC,EAAE,CAC5B,EAAc,CAAW,CAAC,EAAE,CAElC,GAAI,CAAC,EACH,OAAO,EAAkB,EADT,AACkB,GAKpC,OAAQ,GACN,IAAK,IACH,EAAiB,EAAW,QAAQ,CAAC,CAAE,MAAO,OAAQ,GACtD,KACF,KAAK,KACH,EAAiB,EAAW,QAAQ,CAAC,CAAE,MAAO,QAAS,GACvD,KACF,KAAK,MACH,EAAiB,EAAW,QAAQ,CAAC,CAAE,MAAO,MAAO,GACrD,KACF,KAAK,IAEH,EAAiB,EAAW,QAAQ,CAAC,CAAE,MAAO,MAAO,EAEzD,CAEA,OAAO,EACJ,OAAO,CAAC,WAAY,EAAkB,EAAa,IACnD,OAAO,CAAC,WAAY,EAAkB,EAAa,GACxD,CAKA,EC/DM,EAAmB,OACnB,EAAkB,OAElB,EAAc,CAAC,IAAK,KAAM,KAAM,OAAO,CGwBvC,EACJ,wDAII,EAA6B,oCAE7B,EAAsB,eACtB,EAAoB,MACpB,EAAgC,WAoS/B,SAAS,EAAO,CAAI,CAAE,CAAS,CAAE,CAAO,EAE7C,IAAM,EAAS,GAAS,QAAU,EAAe,MAAM,EAAI,EAErD,EACJ,GAAS,uBACT,GAAS,QAAQ,SAAS,uBAC1B,EAAe,qBAAqB,EACpC,EAAe,MAAM,EAAE,SAAS,uBAChC,EAEI,EACJ,GAAS,cACT,GAAS,QAAQ,SAAS,cAC1B,EAAe,YAAY,EAC3B,AzBpVK,EyBoVU,MAAM,EAAE,SAAS,cAChC,EAEI,EAAe,EAAO,EAAM,GAAS,IAE3C,GDzTU,CCyTN,ADzTO,CDDT,AE0TG,MDzTa,ODDC,MAChB,AAAiB,OAAV,aACoC,kBAA1C,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAW,GCDH,UAAhB,OAAO,ACyTrB,GDzT2C,MAAM,CAAC,KCyTnC,CAC1B,CD1ToE,KC0T9D,AAAI,CD1TgE,UC0TrD,sBAGvB,IAAI,EAAQ,EACT,KAAK,CAAC,GACN,GAAG,CAAC,AAAC,IACJ,IAAM,EAAiB,CAAS,CAAC,EAAE,OACZ,AAAvB,MAAI,GAA6C,KAAK,CAAxB,EAErB,GADe,CAAc,CAAC,EAAA,AAAe,EAC/B,EAAW,EAAO,UAAU,EAE5C,CACT,GACC,IAAI,CAAC,IACL,KAAK,CAAC,GACN,GAAG,CAAE,AAAD,IAEH,GAAkB,MAAM,CAApB,EACF,MAAO,CAAE,SAAS,EAAO,MAAO,GAAI,EAGtC,IAAM,EAAiB,CAAS,CAAC,EAAE,CACnC,GAAuB,KAAK,CAAxB,EACF,MAAO,CAAE,QAAS,GAAO,MAAO,AAkDxC,SAAS,AAAmB,CAAK,EAC/B,IAAM,EAAU,EAAM,KAAK,CAAC,UAE5B,AAAK,EAIE,CAAO,CAJV,AAIW,EAAE,CAAC,EAJJ,KAIW,CAAC,EAAmB,KAHpC,CAIX,EA1D2D,EAAW,EAGhE,GAAI,CAAU,CAAC,EAAe,CAC5B,CAD8B,KACvB,CAAE,SAAS,EAAM,MAAO,CAAU,EAG3C,GAAI,EAAe,KAAK,CAAC,GACvB,MAAU,AAAJ,WACJ,YAFqD,qDAGnD,EACA,KAIN,MAAO,CAAE,SAAS,EAAO,MAAO,CAAU,CAC5C,GAGE,EAAO,QAAQ,CAAC,YAAY,EAAE,CAChC,EAAQ,EAAO,QAAQ,CAAC,YAAY,CAAC,EAAc,EAAA,EAGrD,IAAM,EAAmB,CACvB,wBACA,sBACA,CACF,EAEA,OAAO,EACJ,GAAG,CAAC,AAAC,IACJ,GAAI,CAAC,EAAK,OAAO,CAAE,OAAO,EAAK,KAAK,CAEpC,IAAM,EAAQ,EAAK,KAAK,CAYxB,OATG,CAAC,GAAS,6BH9YV,EAAgB,AG+Yf,IH/YmB,CAAC,AG+YK,IAC1B,CAAC,GAAS,8BHpZV,EAAiB,AGqZhB,IHrZoB,CAAC,AGqZK,EAAA,GAE5B,AADA,AH/YD,SAAS,AAA0B,CAAK,CAAE,CAAM,CAAE,CAAK,EAC5D,IAAM,EAKR,AALmB,SAKV,AAAQ,CAAK,CAAE,CAAM,CAAE,CAAK,EACnC,IAAM,EAAuB,MAAb,CAAK,CAAC,EAAE,CAAW,QAAU,oBAC7C,MAAO,CAAC,MAAM,EAAE,EAAM,WAAW,GAAG,gBAAgB,EAAE,EAAM,SAAS,EAAE,EAAO,mBAAmB,EAAE,EAAQ,gBAAgB,EAAE,EAAM,+EAA+E,CAAC,AACrN,EAR2B,EAAO,EAAQ,GAExC,GADA,QAAQ,IAAI,CAAC,GACT,EAAY,QAAQ,CAAC,GAAQ,MAAU,AAAJ,WAAe,EACxD,EG4YkC,EAAO,EAAW,OAAO,IAI9C,GADW,CAAU,CAAC,CAAK,CAAC,EAAE,CAAC,EACrB,EAAc,EAAO,EAAO,QAAQ,CAAE,EACzD,GACC,IAAI,CAAC,GACV,CE3YO,SAAS,EAAQ,CAAI,CAAE,CAAM,CAAE,CAAO,MDIf,ECHP,CAAC,EDItB,CADkC,EAAE,CAC9B,EAAQ,ECJC,EDG4B,GACtB,AAAe,MAAT,KAC3B,AAAI,MAAM,GAAgB,EAAc,ACLV,GDKmB,CAA9B,KAA0C,AAAN,MAGlD,GAEL,EAAM,GAFO,IAEA,CAAC,EAAM,OAAO,GAAK,GAFZ,ECPtB", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37]}