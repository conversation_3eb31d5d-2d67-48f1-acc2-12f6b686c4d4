module.exports=[68872,a=>{"use strict";a.s(["default",()=>b1],68872);var b=a.i(90975),c=a.i(13184);let d=a=>{let b=a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase());return b.charAt(0).toUpperCase()+b.slice(1)},e=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim();var f={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let g=(0,c.forwardRef)(({color:a="currentColor",size:b=24,strokeWidth:d=2,absoluteStrokeWidth:g,className:h="",children:i,iconNode:j,...k},l)=>(0,c.createElement)("svg",{ref:l,...f,width:b,height:b,stroke:a,strokeWidth:g?24*Number(d)/Number(b):d,className:e("lucide",h),...!i&&!(a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0})(k)&&{"aria-hidden":"true"},...k},[...j.map(([a,b])=>(0,c.createElement)(a,b)),...Array.isArray(i)?i:[i]])),h=(a,b)=>{let f=(0,c.forwardRef)(({className:f,...h},i)=>(0,c.createElement)(g,{ref:i,iconNode:b,className:e(`lucide-${d(a).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${a}`,f),...h}));return f.displayName=d(a),f},i=h("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]),j=h("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),k=h("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),l=h("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),m=h("brain",[["path",{d:"M12 18V5",key:"adv99a"}],["path",{d:"M15 13a4.17 4.17 0 0 1-3-4 4.17 4.17 0 0 1-3 4",key:"1e3is1"}],["path",{d:"M17.598 6.5A3 3 0 1 0 12 5a3 3 0 1 0-5.598 1.5",key:"1gqd8o"}],["path",{d:"M17.997 5.125a4 4 0 0 1 2.526 5.77",key:"iwvgf7"}],["path",{d:"M18 18a4 4 0 0 0 2-7.464",key:"efp6ie"}],["path",{d:"M19.967 17.483A4 4 0 1 1 12 18a4 4 0 1 1-7.967-.517",key:"1gq6am"}],["path",{d:"M6 18a4 4 0 0 1-2-7.464",key:"k1g0md"}],["path",{d:"M6.003 5.125a4 4 0 0 0-2.526 5.77",key:"q97ue3"}]]),n=h("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),o=h("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]),p=h("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),q=h("scan",[["path",{d:"M3 7V5a2 2 0 0 1 2-2h2",key:"aa7l1z"}],["path",{d:"M17 3h2a2 2 0 0 1 2 2v2",key:"4qcy5o"}],["path",{d:"M21 17v2a2 2 0 0 1-2 2h-2",key:"6vwrx8"}],["path",{d:"M7 21H5a2 2 0 0 1-2-2v-2",key:"ioqczr"}]]);function r(a,b){if("function"==typeof a)return a(b);null!=a&&(a.current=b)}function s(...a){return b=>{let c=!1,d=a.map(a=>{let d=r(a,b);return c||"function"!=typeof d||(c=!0),d});if(c)return()=>{for(let b=0;b<d.length;b++){let c=d[b];"function"==typeof c?c():r(a[b],null)}}}}function t(...a){return c.useCallback(s(...a),a)}function u(a){let d=function(a){let b=c.forwardRef((a,b)=>{let{children:d,...e}=a;if(c.isValidElement(d)){var f;let a,g,h=(f=d,(g=(a=Object.getOwnPropertyDescriptor(f.props,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?f.ref:(g=(a=Object.getOwnPropertyDescriptor(f,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?f.props.ref:f.props.ref||f.ref),i=function(a,b){let c={...b};for(let d in b){let e=a[d],f=b[d];/^on[A-Z]/.test(d)?e&&f?c[d]=(...a)=>{let b=f(...a);return e(...a),b}:e&&(c[d]=e):"style"===d?c[d]={...e,...f}:"className"===d&&(c[d]=[e,f].filter(Boolean).join(" "))}return{...a,...c}}(e,d.props);return d.type!==c.Fragment&&(i.ref=b?s(b,h):h),c.cloneElement(d,i)}return c.Children.count(d)>1?c.Children.only(null):null});return b.displayName=`${a}.SlotClone`,b}(a),e=c.forwardRef((a,e)=>{let{children:f,...g}=a,h=c.Children.toArray(f),i=h.find(x);if(i){let a=i.props.children,f=h.map(b=>b!==i?b:c.Children.count(a)>1?c.Children.only(null):c.isValidElement(a)?a.props.children:null);return(0,b.jsx)(d,{...g,ref:e,children:c.isValidElement(a)?c.cloneElement(a,void 0,f):null})}return(0,b.jsx)(d,{...g,ref:e,children:f})});return e.displayName=`${a}.Slot`,e}var v=u("Slot"),w=Symbol("radix.slottable");function x(a){return c.isValidElement(a)&&"function"==typeof a.type&&"__radixId"in a.type&&a.type.__radixId===w}function y(){for(var a,b,c=0,d="",e=arguments.length;c<e;c++)(a=arguments[c])&&(b=function a(b){var c,d,e="";if("string"==typeof b||"number"==typeof b)e+=b;else if("object"==typeof b)if(Array.isArray(b)){var f=b.length;for(c=0;c<f;c++)b[c]&&(d=a(b[c]))&&(e&&(e+=" "),e+=d)}else for(d in b)b[d]&&(e&&(e+=" "),e+=d);return e}(a))&&(d&&(d+=" "),d+=b);return d}let z=a=>"boolean"==typeof a?`${a}`:0===a?"0":a,A=(a,b)=>c=>{var d;if((null==b?void 0:b.variants)==null)return y(a,null==c?void 0:c.class,null==c?void 0:c.className);let{variants:e,defaultVariants:f}=b,g=Object.keys(e).map(a=>{let b=null==c?void 0:c[a],d=null==f?void 0:f[a];if(null===b)return null;let g=z(b)||z(d);return e[a][g]}),h=c&&Object.entries(c).reduce((a,b)=>{let[c,d]=b;return void 0===d||(a[c]=d),a},{});return y(a,g,null==b||null==(d=b.compoundVariants)?void 0:d.reduce((a,b)=>{let{class:c,className:d,...e}=b;return Object.entries(e).every(a=>{let[b,c]=a;return Array.isArray(c)?c.includes({...f,...h}[b]):({...f,...h})[b]===c})?[...a,c,d]:a},[]),null==c?void 0:c.class,null==c?void 0:c.className)},B=(a,b)=>{if(0===a.length)return b.classGroupId;let c=a[0],d=b.nextPart.get(c),e=d?B(a.slice(1),d):void 0;if(e)return e;if(0===b.validators.length)return;let f=a.join("-");return b.validators.find(({validator:a})=>a(f))?.classGroupId},C=/^\[(.+)\]$/,D=(a,b,c,d)=>{a.forEach(a=>{if("string"==typeof a){(""===a?b:E(b,a)).classGroupId=c;return}if("function"==typeof a)return F(a)?void D(a(d),b,c,d):void b.validators.push({validator:a,classGroupId:c});Object.entries(a).forEach(([a,e])=>{D(e,E(b,a),c,d)})})},E=(a,b)=>{let c=a;return b.split("-").forEach(a=>{c.nextPart.has(a)||c.nextPart.set(a,{nextPart:new Map,validators:[]}),c=c.nextPart.get(a)}),c},F=a=>a.isThemeGetter,G=/\s+/;function H(){let a,b,c=0,d="";for(;c<arguments.length;)(a=arguments[c++])&&(b=I(a))&&(d&&(d+=" "),d+=b);return d}let I=a=>{let b;if("string"==typeof a)return a;let c="";for(let d=0;d<a.length;d++)a[d]&&(b=I(a[d]))&&(c&&(c+=" "),c+=b);return c},J=a=>{let b=b=>b[a]||[];return b.isThemeGetter=!0,b},K=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,L=/^\((?:(\w[\w-]*):)?(.+)\)$/i,M=/^\d+\/\d+$/,N=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,O=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,P=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,Q=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,R=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,S=a=>M.test(a),T=a=>!!a&&!Number.isNaN(Number(a)),U=a=>!!a&&Number.isInteger(Number(a)),V=a=>a.endsWith("%")&&T(a.slice(0,-1)),W=a=>N.test(a),X=()=>!0,Y=a=>O.test(a)&&!P.test(a),Z=()=>!1,$=a=>Q.test(a),_=a=>R.test(a),aa=a=>!ac(a)&&!ai(a),ab=a=>ap(a,at,Z),ac=a=>K.test(a),ad=a=>ap(a,au,Y),ae=a=>ap(a,av,T),af=a=>ap(a,ar,Z),ag=a=>ap(a,as,_),ah=a=>ap(a,ax,$),ai=a=>L.test(a),aj=a=>aq(a,au),ak=a=>aq(a,aw),al=a=>aq(a,ar),am=a=>aq(a,at),an=a=>aq(a,as),ao=a=>aq(a,ax,!0),ap=(a,b,c)=>{let d=K.exec(a);return!!d&&(d[1]?b(d[1]):c(d[2]))},aq=(a,b,c=!1)=>{let d=L.exec(a);return!!d&&(d[1]?b(d[1]):c)},ar=a=>"position"===a||"percentage"===a,as=a=>"image"===a||"url"===a,at=a=>"length"===a||"size"===a||"bg-size"===a,au=a=>"length"===a,av=a=>"number"===a,aw=a=>"family-name"===a,ax=a=>"shadow"===a;Symbol.toStringTag;let ay=function(a,...b){let c,d,e,f=function(h){let i;return d=(c={cache:(a=>{if(a<1)return{get:()=>void 0,set:()=>{}};let b=0,c=new Map,d=new Map,e=(e,f)=>{c.set(e,f),++b>a&&(b=0,d=c,c=new Map)};return{get(a){let b=c.get(a);return void 0!==b?b:void 0!==(b=d.get(a))?(e(a,b),b):void 0},set(a,b){c.has(a)?c.set(a,b):e(a,b)}}})((i=b.reduce((a,b)=>b(a),a())).cacheSize),parseClassName:(a=>{let{prefix:b,experimentalParseClassName:c}=a,d=a=>{let b,c,d=[],e=0,f=0,g=0;for(let c=0;c<a.length;c++){let h=a[c];if(0===e&&0===f){if(":"===h){d.push(a.slice(g,c)),g=c+1;continue}if("/"===h){b=c;continue}}"["===h?e++:"]"===h?e--:"("===h?f++:")"===h&&f--}let h=0===d.length?a:a.substring(g),i=(c=h).endsWith("!")?c.substring(0,c.length-1):c.startsWith("!")?c.substring(1):c;return{modifiers:d,hasImportantModifier:i!==h,baseClassName:i,maybePostfixModifierPosition:b&&b>g?b-g:void 0}};if(b){let a=b+":",c=d;d=b=>b.startsWith(a)?c(b.substring(a.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:b,maybePostfixModifierPosition:void 0}}if(c){let a=d;d=b=>c({className:b,parseClassName:a})}return d})(i),sortModifiers:(a=>{let b=Object.fromEntries(a.orderSensitiveModifiers.map(a=>[a,!0]));return a=>{if(a.length<=1)return a;let c=[],d=[];return a.forEach(a=>{"["===a[0]||b[a]?(c.push(...d.sort(),a),d=[]):d.push(a)}),c.push(...d.sort()),c}})(i),...(a=>{let b=(a=>{let{theme:b,classGroups:c}=a,d={nextPart:new Map,validators:[]};for(let a in c)D(c[a],d,a,b);return d})(a),{conflictingClassGroups:c,conflictingClassGroupModifiers:d}=a;return{getClassGroupId:a=>{let c=a.split("-");return""===c[0]&&1!==c.length&&c.shift(),B(c,b)||(a=>{if(C.test(a)){let b=C.exec(a)[1],c=b?.substring(0,b.indexOf(":"));if(c)return"arbitrary.."+c}})(a)},getConflictingClassGroupIds:(a,b)=>{let e=c[a]||[];return b&&d[a]?[...e,...d[a]]:e}}})(i)}).cache.get,e=c.cache.set,f=g,g(h)};function g(a){let b=d(a);if(b)return b;let f=((a,b)=>{let{parseClassName:c,getClassGroupId:d,getConflictingClassGroupIds:e,sortModifiers:f}=b,g=[],h=a.trim().split(G),i="";for(let a=h.length-1;a>=0;a-=1){let b=h[a],{isExternal:j,modifiers:k,hasImportantModifier:l,baseClassName:m,maybePostfixModifierPosition:n}=c(b);if(j){i=b+(i.length>0?" "+i:i);continue}let o=!!n,p=d(o?m.substring(0,n):m);if(!p){if(!o||!(p=d(m))){i=b+(i.length>0?" "+i:i);continue}o=!1}let q=f(k).join(":"),r=l?q+"!":q,s=r+p;if(g.includes(s))continue;g.push(s);let t=e(p,o);for(let a=0;a<t.length;++a){let b=t[a];g.push(r+b)}i=b+(i.length>0?" "+i:i)}return i})(a,c);return e(a,f),f}return function(){return f(H.apply(null,arguments))}}(()=>{let a=J("color"),b=J("font"),c=J("text"),d=J("font-weight"),e=J("tracking"),f=J("leading"),g=J("breakpoint"),h=J("container"),i=J("spacing"),j=J("radius"),k=J("shadow"),l=J("inset-shadow"),m=J("text-shadow"),n=J("drop-shadow"),o=J("blur"),p=J("perspective"),q=J("aspect"),r=J("ease"),s=J("animate"),t=()=>["auto","avoid","all","avoid-page","page","left","right","column"],u=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],v=()=>[...u(),ai,ac],w=()=>["auto","hidden","clip","visible","scroll"],x=()=>["auto","contain","none"],y=()=>[ai,ac,i],z=()=>[S,"full","auto",...y()],A=()=>[U,"none","subgrid",ai,ac],B=()=>["auto",{span:["full",U,ai,ac]},U,ai,ac],C=()=>[U,"auto",ai,ac],D=()=>["auto","min","max","fr",ai,ac],E=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],F=()=>["start","end","center","stretch","center-safe","end-safe"],G=()=>["auto",...y()],H=()=>[S,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...y()],I=()=>[a,ai,ac],K=()=>[...u(),al,af,{position:[ai,ac]}],L=()=>["no-repeat",{repeat:["","x","y","space","round"]}],M=()=>["auto","cover","contain",am,ab,{size:[ai,ac]}],N=()=>[V,aj,ad],O=()=>["","none","full",j,ai,ac],P=()=>["",T,aj,ad],Q=()=>["solid","dashed","dotted","double"],R=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],Y=()=>[T,V,al,af],Z=()=>["","none",o,ai,ac],$=()=>["none",T,ai,ac],_=()=>["none",T,ai,ac],ap=()=>[T,ai,ac],aq=()=>[S,"full",...y()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[W],breakpoint:[W],color:[X],container:[W],"drop-shadow":[W],ease:["in","out","in-out"],font:[aa],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[W],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[W],shadow:[W],spacing:["px",T],text:[W],"text-shadow":[W],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",S,ac,ai,q]}],container:["container"],columns:[{columns:[T,ac,ai,h]}],"break-after":[{"break-after":t()}],"break-before":[{"break-before":t()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:v()}],overflow:[{overflow:w()}],"overflow-x":[{"overflow-x":w()}],"overflow-y":[{"overflow-y":w()}],overscroll:[{overscroll:x()}],"overscroll-x":[{"overscroll-x":x()}],"overscroll-y":[{"overscroll-y":x()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:z()}],"inset-x":[{"inset-x":z()}],"inset-y":[{"inset-y":z()}],start:[{start:z()}],end:[{end:z()}],top:[{top:z()}],right:[{right:z()}],bottom:[{bottom:z()}],left:[{left:z()}],visibility:["visible","invisible","collapse"],z:[{z:[U,"auto",ai,ac]}],basis:[{basis:[S,"full","auto",h,...y()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[T,S,"auto","initial","none",ac]}],grow:[{grow:["",T,ai,ac]}],shrink:[{shrink:["",T,ai,ac]}],order:[{order:[U,"first","last","none",ai,ac]}],"grid-cols":[{"grid-cols":A()}],"col-start-end":[{col:B()}],"col-start":[{"col-start":C()}],"col-end":[{"col-end":C()}],"grid-rows":[{"grid-rows":A()}],"row-start-end":[{row:B()}],"row-start":[{"row-start":C()}],"row-end":[{"row-end":C()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":D()}],"auto-rows":[{"auto-rows":D()}],gap:[{gap:y()}],"gap-x":[{"gap-x":y()}],"gap-y":[{"gap-y":y()}],"justify-content":[{justify:[...E(),"normal"]}],"justify-items":[{"justify-items":[...F(),"normal"]}],"justify-self":[{"justify-self":["auto",...F()]}],"align-content":[{content:["normal",...E()]}],"align-items":[{items:[...F(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...F(),{baseline:["","last"]}]}],"place-content":[{"place-content":E()}],"place-items":[{"place-items":[...F(),"baseline"]}],"place-self":[{"place-self":["auto",...F()]}],p:[{p:y()}],px:[{px:y()}],py:[{py:y()}],ps:[{ps:y()}],pe:[{pe:y()}],pt:[{pt:y()}],pr:[{pr:y()}],pb:[{pb:y()}],pl:[{pl:y()}],m:[{m:G()}],mx:[{mx:G()}],my:[{my:G()}],ms:[{ms:G()}],me:[{me:G()}],mt:[{mt:G()}],mr:[{mr:G()}],mb:[{mb:G()}],ml:[{ml:G()}],"space-x":[{"space-x":y()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":y()}],"space-y-reverse":["space-y-reverse"],size:[{size:H()}],w:[{w:[h,"screen",...H()]}],"min-w":[{"min-w":[h,"screen","none",...H()]}],"max-w":[{"max-w":[h,"screen","none","prose",{screen:[g]},...H()]}],h:[{h:["screen","lh",...H()]}],"min-h":[{"min-h":["screen","lh","none",...H()]}],"max-h":[{"max-h":["screen","lh",...H()]}],"font-size":[{text:["base",c,aj,ad]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[d,ai,ae]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",V,ac]}],"font-family":[{font:[ak,ac,b]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[e,ai,ac]}],"line-clamp":[{"line-clamp":[T,"none",ai,ae]}],leading:[{leading:[f,...y()]}],"list-image":[{"list-image":["none",ai,ac]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",ai,ac]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:I()}],"text-color":[{text:I()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...Q(),"wavy"]}],"text-decoration-thickness":[{decoration:[T,"from-font","auto",ai,ad]}],"text-decoration-color":[{decoration:I()}],"underline-offset":[{"underline-offset":[T,"auto",ai,ac]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:y()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",ai,ac]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",ai,ac]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:K()}],"bg-repeat":[{bg:L()}],"bg-size":[{bg:M()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},U,ai,ac],radial:["",ai,ac],conic:[U,ai,ac]},an,ag]}],"bg-color":[{bg:I()}],"gradient-from-pos":[{from:N()}],"gradient-via-pos":[{via:N()}],"gradient-to-pos":[{to:N()}],"gradient-from":[{from:I()}],"gradient-via":[{via:I()}],"gradient-to":[{to:I()}],rounded:[{rounded:O()}],"rounded-s":[{"rounded-s":O()}],"rounded-e":[{"rounded-e":O()}],"rounded-t":[{"rounded-t":O()}],"rounded-r":[{"rounded-r":O()}],"rounded-b":[{"rounded-b":O()}],"rounded-l":[{"rounded-l":O()}],"rounded-ss":[{"rounded-ss":O()}],"rounded-se":[{"rounded-se":O()}],"rounded-ee":[{"rounded-ee":O()}],"rounded-es":[{"rounded-es":O()}],"rounded-tl":[{"rounded-tl":O()}],"rounded-tr":[{"rounded-tr":O()}],"rounded-br":[{"rounded-br":O()}],"rounded-bl":[{"rounded-bl":O()}],"border-w":[{border:P()}],"border-w-x":[{"border-x":P()}],"border-w-y":[{"border-y":P()}],"border-w-s":[{"border-s":P()}],"border-w-e":[{"border-e":P()}],"border-w-t":[{"border-t":P()}],"border-w-r":[{"border-r":P()}],"border-w-b":[{"border-b":P()}],"border-w-l":[{"border-l":P()}],"divide-x":[{"divide-x":P()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":P()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...Q(),"hidden","none"]}],"divide-style":[{divide:[...Q(),"hidden","none"]}],"border-color":[{border:I()}],"border-color-x":[{"border-x":I()}],"border-color-y":[{"border-y":I()}],"border-color-s":[{"border-s":I()}],"border-color-e":[{"border-e":I()}],"border-color-t":[{"border-t":I()}],"border-color-r":[{"border-r":I()}],"border-color-b":[{"border-b":I()}],"border-color-l":[{"border-l":I()}],"divide-color":[{divide:I()}],"outline-style":[{outline:[...Q(),"none","hidden"]}],"outline-offset":[{"outline-offset":[T,ai,ac]}],"outline-w":[{outline:["",T,aj,ad]}],"outline-color":[{outline:I()}],shadow:[{shadow:["","none",k,ao,ah]}],"shadow-color":[{shadow:I()}],"inset-shadow":[{"inset-shadow":["none",l,ao,ah]}],"inset-shadow-color":[{"inset-shadow":I()}],"ring-w":[{ring:P()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:I()}],"ring-offset-w":[{"ring-offset":[T,ad]}],"ring-offset-color":[{"ring-offset":I()}],"inset-ring-w":[{"inset-ring":P()}],"inset-ring-color":[{"inset-ring":I()}],"text-shadow":[{"text-shadow":["none",m,ao,ah]}],"text-shadow-color":[{"text-shadow":I()}],opacity:[{opacity:[T,ai,ac]}],"mix-blend":[{"mix-blend":[...R(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":R()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[T]}],"mask-image-linear-from-pos":[{"mask-linear-from":Y()}],"mask-image-linear-to-pos":[{"mask-linear-to":Y()}],"mask-image-linear-from-color":[{"mask-linear-from":I()}],"mask-image-linear-to-color":[{"mask-linear-to":I()}],"mask-image-t-from-pos":[{"mask-t-from":Y()}],"mask-image-t-to-pos":[{"mask-t-to":Y()}],"mask-image-t-from-color":[{"mask-t-from":I()}],"mask-image-t-to-color":[{"mask-t-to":I()}],"mask-image-r-from-pos":[{"mask-r-from":Y()}],"mask-image-r-to-pos":[{"mask-r-to":Y()}],"mask-image-r-from-color":[{"mask-r-from":I()}],"mask-image-r-to-color":[{"mask-r-to":I()}],"mask-image-b-from-pos":[{"mask-b-from":Y()}],"mask-image-b-to-pos":[{"mask-b-to":Y()}],"mask-image-b-from-color":[{"mask-b-from":I()}],"mask-image-b-to-color":[{"mask-b-to":I()}],"mask-image-l-from-pos":[{"mask-l-from":Y()}],"mask-image-l-to-pos":[{"mask-l-to":Y()}],"mask-image-l-from-color":[{"mask-l-from":I()}],"mask-image-l-to-color":[{"mask-l-to":I()}],"mask-image-x-from-pos":[{"mask-x-from":Y()}],"mask-image-x-to-pos":[{"mask-x-to":Y()}],"mask-image-x-from-color":[{"mask-x-from":I()}],"mask-image-x-to-color":[{"mask-x-to":I()}],"mask-image-y-from-pos":[{"mask-y-from":Y()}],"mask-image-y-to-pos":[{"mask-y-to":Y()}],"mask-image-y-from-color":[{"mask-y-from":I()}],"mask-image-y-to-color":[{"mask-y-to":I()}],"mask-image-radial":[{"mask-radial":[ai,ac]}],"mask-image-radial-from-pos":[{"mask-radial-from":Y()}],"mask-image-radial-to-pos":[{"mask-radial-to":Y()}],"mask-image-radial-from-color":[{"mask-radial-from":I()}],"mask-image-radial-to-color":[{"mask-radial-to":I()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":u()}],"mask-image-conic-pos":[{"mask-conic":[T]}],"mask-image-conic-from-pos":[{"mask-conic-from":Y()}],"mask-image-conic-to-pos":[{"mask-conic-to":Y()}],"mask-image-conic-from-color":[{"mask-conic-from":I()}],"mask-image-conic-to-color":[{"mask-conic-to":I()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:K()}],"mask-repeat":[{mask:L()}],"mask-size":[{mask:M()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",ai,ac]}],filter:[{filter:["","none",ai,ac]}],blur:[{blur:Z()}],brightness:[{brightness:[T,ai,ac]}],contrast:[{contrast:[T,ai,ac]}],"drop-shadow":[{"drop-shadow":["","none",n,ao,ah]}],"drop-shadow-color":[{"drop-shadow":I()}],grayscale:[{grayscale:["",T,ai,ac]}],"hue-rotate":[{"hue-rotate":[T,ai,ac]}],invert:[{invert:["",T,ai,ac]}],saturate:[{saturate:[T,ai,ac]}],sepia:[{sepia:["",T,ai,ac]}],"backdrop-filter":[{"backdrop-filter":["","none",ai,ac]}],"backdrop-blur":[{"backdrop-blur":Z()}],"backdrop-brightness":[{"backdrop-brightness":[T,ai,ac]}],"backdrop-contrast":[{"backdrop-contrast":[T,ai,ac]}],"backdrop-grayscale":[{"backdrop-grayscale":["",T,ai,ac]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[T,ai,ac]}],"backdrop-invert":[{"backdrop-invert":["",T,ai,ac]}],"backdrop-opacity":[{"backdrop-opacity":[T,ai,ac]}],"backdrop-saturate":[{"backdrop-saturate":[T,ai,ac]}],"backdrop-sepia":[{"backdrop-sepia":["",T,ai,ac]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":y()}],"border-spacing-x":[{"border-spacing-x":y()}],"border-spacing-y":[{"border-spacing-y":y()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",ai,ac]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[T,"initial",ai,ac]}],ease:[{ease:["linear","initial",r,ai,ac]}],delay:[{delay:[T,ai,ac]}],animate:[{animate:["none",s,ai,ac]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[p,ai,ac]}],"perspective-origin":[{"perspective-origin":v()}],rotate:[{rotate:$()}],"rotate-x":[{"rotate-x":$()}],"rotate-y":[{"rotate-y":$()}],"rotate-z":[{"rotate-z":$()}],scale:[{scale:_()}],"scale-x":[{"scale-x":_()}],"scale-y":[{"scale-y":_()}],"scale-z":[{"scale-z":_()}],"scale-3d":["scale-3d"],skew:[{skew:ap()}],"skew-x":[{"skew-x":ap()}],"skew-y":[{"skew-y":ap()}],transform:[{transform:[ai,ac,"","none","gpu","cpu"]}],"transform-origin":[{origin:v()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:aq()}],"translate-x":[{"translate-x":aq()}],"translate-y":[{"translate-y":aq()}],"translate-z":[{"translate-z":aq()}],"translate-none":["translate-none"],accent:[{accent:I()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:I()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",ai,ac]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":y()}],"scroll-mx":[{"scroll-mx":y()}],"scroll-my":[{"scroll-my":y()}],"scroll-ms":[{"scroll-ms":y()}],"scroll-me":[{"scroll-me":y()}],"scroll-mt":[{"scroll-mt":y()}],"scroll-mr":[{"scroll-mr":y()}],"scroll-mb":[{"scroll-mb":y()}],"scroll-ml":[{"scroll-ml":y()}],"scroll-p":[{"scroll-p":y()}],"scroll-px":[{"scroll-px":y()}],"scroll-py":[{"scroll-py":y()}],"scroll-ps":[{"scroll-ps":y()}],"scroll-pe":[{"scroll-pe":y()}],"scroll-pt":[{"scroll-pt":y()}],"scroll-pr":[{"scroll-pr":y()}],"scroll-pb":[{"scroll-pb":y()}],"scroll-pl":[{"scroll-pl":y()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",ai,ac]}],fill:[{fill:["none",...I()]}],"stroke-w":[{stroke:[T,aj,ad,ae]}],stroke:[{stroke:["none",...I()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function az(...a){return ay(y(a))}function aA(a){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(a)}function aB(a){return new Intl.NumberFormat("en-US",{style:"percent",minimumFractionDigits:2,maximumFractionDigits:2}).format(a/100)}let aC=A("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),aD=c.forwardRef(({className:a,variant:c,size:d,asChild:e=!1,...f},g)=>(0,b.jsx)(e?v:"button",{className:az(aC({variant:c,size:d,className:a})),ref:g,...f}));aD.displayName="Button";let aE=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)("div",{ref:d,className:az("rounded-lg border bg-card text-card-foreground shadow-sm",a),...c}));aE.displayName="Card";let aF=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)("div",{ref:d,className:az("flex flex-col space-y-1.5 p-6",a),...c}));aF.displayName="CardHeader";let aG=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)("h3",{ref:d,className:az("text-2xl font-semibold leading-none tracking-tight",a),...c}));aG.displayName="CardTitle";let aH=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)("p",{ref:d,className:az("text-sm text-muted-foreground",a),...c}));aH.displayName="CardDescription";let aI=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)("div",{ref:d,className:az("p-6 pt-0",a),...c}));aI.displayName="CardContent",c.forwardRef(({className:a,...c},d)=>(0,b.jsx)("div",{ref:d,className:az("flex items-center p-6 pt-0",a),...c})).displayName="CardFooter";let aJ=A("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function aK({className:a,variant:c,...d}){return(0,b.jsx)("div",{className:az(aJ({variant:c}),a),...d})}let aL=h("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]]),aM=h("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);function aN({autoScan:a=!1}){let[d,e]=(0,c.useState)(!1),[f,g]=(0,c.useState)(null),[h,j]=(0,c.useState)("quick"),[k,l]=(0,c.useState)("Technology"),[m,n]=(0,c.useState)(null);(0,c.useEffect)(()=>{a&&q()},[a]);let q=async()=>{e(!0),n(null),g(null);try{let a=await fetch("/api/scanner/quick?limit=15");if(!a.ok)throw Error("Failed to fetch scan results");let b=await a.json(),c={totalScanned:b.totalScanned,successfulScans:b.results.length,failedScans:b.totalScanned-b.results.length,topOpportunities:b.results,sectorBreakdown:{},scanDuration:0};g(c)}catch(a){n("Failed to perform quick scan. Please try again."),console.error("Quick scan error:",a)}finally{e(!1)}},r=async()=>{e(!0),n(null),g(null);try{let a=await fetch("/api/scanner/full?limit=25&concurrent=3");if(!a.ok)throw Error("Failed to fetch scan results");let b=await a.json();g(b)}catch(a){n("Failed to perform full scan. Please try again."),console.error("Full scan error:",a)}finally{e(!1)}},s=async()=>{e(!0),n(null),g(null);try{let a=await fetch(`/api/scanner/sector/${encodeURIComponent(k)}?limit=15`);if(!a.ok)throw Error("Failed to fetch scan results");let b=await a.json();g(b)}catch(a){n("Failed to perform sector scan. Please try again."),console.error("Sector scan error:",a)}finally{e(!1)}};return(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)(aE,{className:"bg-slate-800/50 border-slate-700",children:[(0,b.jsxs)(aF,{children:[(0,b.jsxs)(aG,{className:"text-white flex items-center",children:[(0,b.jsx)(o,{className:"mr-2 h-5 w-5 text-blue-400"}),"Swing Trading Scanner"]}),(0,b.jsx)(aH,{className:"text-slate-300",children:"Automatically scan stocks for the best swing trading opportunities"})]}),(0,b.jsxs)(aI,{children:[(0,b.jsxs)("div",{className:"flex flex-wrap gap-4 mb-4",children:[(0,b.jsxs)(aD,{onClick:q,disabled:d,className:"bg-blue-600 hover:bg-blue-700",children:[d&&"quick"===h?(0,b.jsx)(p,{className:"mr-2 h-4 w-4 animate-spin"}):null,"Quick Scan (Top 16)"]}),(0,b.jsxs)(aD,{onClick:r,disabled:d,variant:"outline",className:"border-slate-600 text-slate-300 hover:bg-slate-800",children:[d&&"full"===h?(0,b.jsx)(p,{className:"mr-2 h-4 w-4 animate-spin"}):null,"Full Scan (All 70+ Stocks)"]}),(0,b.jsxs)("div",{className:"flex gap-2",children:[(0,b.jsx)("select",{value:k,onChange:a=>l(a.target.value),className:"px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm",disabled:d,children:["Technology","Financial Services","Healthcare","Industrial","Materials","Consumer","Communication Services","Energy"].map(a=>(0,b.jsx)("option",{value:a,children:a},a))}),(0,b.jsxs)(aD,{onClick:s,disabled:d,variant:"outline",className:"border-slate-600 text-slate-300 hover:bg-slate-800",children:[d&&"sector"===h?(0,b.jsx)(p,{className:"mr-2 h-4 w-4 animate-spin"}):null,"Scan Sector"]})]})]}),d&&(0,b.jsxs)("div",{className:"text-center py-4",children:[(0,b.jsx)(p,{className:"mx-auto h-8 w-8 animate-spin text-blue-400"}),(0,b.jsx)("p",{className:"text-slate-300 mt-2",children:"Scanning stocks for swing trading opportunities..."})]})]})]}),m&&(0,b.jsx)(aE,{className:"bg-red-900/20 border-red-500/50",children:(0,b.jsx)(aI,{className:"p-6",children:(0,b.jsx)("p",{className:"text-red-300 text-center",children:m})})}),f&&(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)(aE,{className:"bg-slate-800/50 border-slate-700",children:[(0,b.jsx)(aF,{children:(0,b.jsx)(aG,{className:"text-white",children:"Scan Summary"})}),(0,b.jsx)(aI,{children:(0,b.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("div",{className:"text-2xl font-bold text-white",children:f.totalScanned}),(0,b.jsx)("div",{className:"text-sm text-slate-300",children:"Total Scanned"})]}),(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("div",{className:"text-2xl font-bold text-green-400",children:f.successfulScans}),(0,b.jsx)("div",{className:"text-sm text-slate-300",children:"Successful"})]}),(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("div",{className:"text-2xl font-bold text-red-400",children:f.failedScans}),(0,b.jsx)("div",{className:"text-sm text-slate-300",children:"Failed"})]}),(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("div",{className:"text-2xl font-bold text-blue-400",children:f.scanDuration?`${(f.scanDuration/1e3).toFixed(1)}s`:"N/A"}),(0,b.jsx)("div",{className:"text-sm text-slate-300",children:"Duration"})]})]})})]}),(0,b.jsxs)(aE,{className:"bg-slate-800/50 border-slate-700",children:[(0,b.jsx)(aF,{children:(0,b.jsxs)(aG,{className:"text-white flex items-center",children:[(0,b.jsx)(i,{className:"mr-2 h-5 w-5 text-green-400"}),"Top Swing Trading Opportunities"]})}),(0,b.jsx)(aI,{children:(0,b.jsx)("div",{className:"space-y-4",children:f.topOpportunities.map((a,c)=>{var d;return(0,b.jsxs)("div",{className:"p-4 bg-slate-700/50 rounded-lg",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,b.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,b.jsxs)("div",{className:"text-lg font-bold text-white",children:["#",a.rank]}),(0,b.jsxs)("div",{children:[(0,b.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,b.jsx)("span",{className:"text-lg font-semibold text-white",children:a.symbol}),(a=>{switch(a){case"BULLISH":return(0,b.jsx)(i,{className:"h-4 w-4 text-green-400"});case"BEARISH":return(0,b.jsx)(aL,{className:"h-4 w-4 text-red-400"});default:return(0,b.jsx)(aM,{className:"h-4 w-4 text-yellow-400"})}})(a.analysis.trend),(0,b.jsx)(aK,{className:(d=a.analysis.recommendation).includes("BUY")?"bg-green-500/20 text-green-400":d.includes("SELL")?"bg-red-500/20 text-red-400":"bg-yellow-500/20 text-yellow-400",children:a.analysis.recommendation.replace("_"," ")})]}),(0,b.jsx)("div",{className:"text-sm text-slate-300",children:a.name})]})]}),(0,b.jsxs)("div",{className:"text-right",children:[(0,b.jsxs)("div",{className:"text-xl font-bold text-white",children:[a.score.toFixed(1),"/100"]}),(0,b.jsx)("div",{className:"text-sm text-slate-300",children:"Score"})]})]}),(0,b.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("div",{className:"text-slate-300",children:"Price"}),(0,b.jsx)("div",{className:"text-white font-semibold",children:aA(a.quote.price)}),(0,b.jsx)("div",{className:a.quote.change>=0?"text-green-400":"text-red-400",children:aB(a.quote.changePercent)})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("div",{className:"text-slate-300",children:"Entry"}),(0,b.jsx)("div",{className:"text-blue-400 font-semibold",children:aA(a.analysis.entryPrice)})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("div",{className:"text-slate-300",children:"R/R Ratio"}),(0,b.jsxs)("div",{className:"text-green-400 font-semibold",children:[a.analysis.riskRewardRatio.toFixed(2),":1"]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("div",{className:"text-slate-300",children:"Confidence"}),(0,b.jsxs)("div",{className:"text-white font-semibold",children:[a.analysis.confidence.toFixed(1),"%"]})]})]})]},a.symbol)})})})]})]})]})}let aO=h("moon",[["path",{d:"M20.985 12.486a9 9 0 1 1-9.473-9.472c.405-.022.617.46.402.803a6 6 0 0 0 8.268 8.268c.344-.215.825-.004.803.401",key:"kfwtm"}]]),aP=h("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]),aQ=h("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]),aR=h("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]),aS=h("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),aT=h("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),aU=h("play",[["path",{d:"M5 5a2 2 0 0 1 3.008-1.728l11.997 6.998a2 2 0 0 1 .003 3.458l-12 7A2 2 0 0 1 5 19z",key:"10ikf1"}]]),aV=h("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),aW=h("sparkles",[["path",{d:"M11.017 2.814a1 1 0 0 1 1.966 0l1.051 5.558a2 2 0 0 0 1.594 1.594l5.558 1.051a1 1 0 0 1 0 1.966l-5.558 1.051a2 2 0 0 0-1.594 1.594l-1.051 5.558a1 1 0 0 1-1.966 0l-1.051-5.558a2 2 0 0 0-1.594-1.594l-5.558-1.051a1 1 0 0 1 0-1.966l5.558-1.051a2 2 0 0 0 1.594-1.594z",key:"1s2grr"}],["path",{d:"M20 2v4",key:"1rf3ol"}],["path",{d:"M22 4h-4",key:"gwowj6"}],["circle",{cx:"4",cy:"20",r:"2",key:"6kqj1y"}]]);function aX({setup:a,onExecuteTrade:d,onViewChart:e}){var f,g;let[h,j]=(0,c.useState)(!1),[n,o]=(0,c.useState)(null),[q,r]=(0,c.useState)(!1),[s,t]=(0,c.useState)(!1);(0,c.useEffect)(()=>{u()},[]);let u=async()=>{try{let a=await fetch("/api/ai?action=status"),b=await a.json();t(b.enabled),b.enabled&&v()}catch(a){console.error("Error checking AI status:",a)}},v=async()=>{r(!0);try{let b=await fetch("/api/ai",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"risk-assessment",data:{setup:a}})});if(b.ok){let{riskAssessment:a}=await b.json();o(a)}}catch(a){console.error("Error loading AI risk assessment:",a)}finally{r(!1)}},w=((a.targets[0]-a.entryPrice)/(a.entryPrice-a.stopLoss)).toFixed(1);return(0,b.jsxs)(aE,{className:"bg-slate-800/50 border-slate-700",children:[(0,b.jsx)(aF,{className:"pb-3",children:(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{className:"flex items-center gap-3",children:[(0,b.jsx)(aG,{className:"text-xl text-white",children:a.symbol}),(0,b.jsx)(aK,{variant:"outline",className:"text-blue-400 border-blue-400",children:"overnight_momentum"===a.strategy?"Overnight Momentum":"Technical Breakout"}),(0,b.jsxs)(aK,{className:(f=a.confidence)>=80?"bg-green-500/20 text-green-400":f>=60?"bg-yellow-500/20 text-yellow-400":"bg-red-500/20 text-red-400",children:[a.confidence,"% Confidence"]})]}),(0,b.jsxs)("div",{className:"flex gap-2",children:[(0,b.jsxs)(aD,{size:"sm",variant:"outline",onClick:()=>e?.(a.symbol),className:"text-slate-300 hover:text-white",children:[(0,b.jsx)(aV,{className:"h-4 w-4 mr-1"}),"Chart"]}),(0,b.jsxs)(aD,{size:"sm",onClick:()=>d?.(a),className:"bg-green-600 hover:bg-green-700 text-white",children:[(0,b.jsx)(aU,{className:"h-4 w-4 mr-1"}),"Execute Trade"]})]})]})}),(0,b.jsxs)(aI,{className:"space-y-4",children:[(0,b.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,b.jsxs)("div",{className:"text-center p-3 bg-slate-700/30 rounded-lg",children:[(0,b.jsxs)("div",{className:"flex items-center justify-center mb-1",children:[(0,b.jsx)(i,{className:"h-4 w-4 text-green-400 mr-1"}),(0,b.jsx)("span",{className:"text-xs text-slate-400",children:"Entry"})]}),(0,b.jsxs)("div",{className:"text-lg font-bold text-white",children:["$",a.preciseEntry.price.toFixed(2)]}),(0,b.jsx)("div",{className:"text-xs text-slate-400",children:a.preciseEntry.orderType})]}),(0,b.jsxs)("div",{className:"text-center p-3 bg-slate-700/30 rounded-lg",children:[(0,b.jsxs)("div",{className:"flex items-center justify-center mb-1",children:[(0,b.jsx)(l,{className:"h-4 w-4 text-red-400 mr-1"}),(0,b.jsx)("span",{className:"text-xs text-slate-400",children:"Stop Loss"})]}),(0,b.jsxs)("div",{className:"text-lg font-bold text-white",children:["$",a.preciseExit.stopLoss.price.toFixed(2)]}),(0,b.jsx)("div",{className:"text-xs text-slate-400",children:a.preciseExit.stopLoss.orderType})]}),(0,b.jsxs)("div",{className:"text-center p-3 bg-slate-700/30 rounded-lg",children:[(0,b.jsxs)("div",{className:"flex items-center justify-center mb-1",children:[(0,b.jsx)(k,{className:"h-4 w-4 text-blue-400 mr-1"}),(0,b.jsx)("span",{className:"text-xs text-slate-400",children:"First Target"})]}),(0,b.jsxs)("div",{className:"text-lg font-bold text-white",children:["$",a.targets[0].toFixed(2)]}),(0,b.jsxs)("div",{className:"text-xs text-slate-400",children:["R/R: ",w,":1"]})]}),(0,b.jsxs)("div",{className:"text-center p-3 bg-slate-700/30 rounded-lg",children:[(0,b.jsxs)("div",{className:"flex items-center justify-center mb-1",children:[(0,b.jsx)(aR,{className:"h-4 w-4 text-yellow-400 mr-1"}),(0,b.jsx)("span",{className:"text-xs text-slate-400",children:"Position Size"})]}),(0,b.jsx)("div",{className:"text-lg font-bold text-white",children:a.positionSize}),(0,b.jsx)("div",{className:"text-xs text-slate-400",children:"shares"})]})]}),(0,b.jsxs)("div",{className:"p-3 bg-slate-700/20 rounded-lg",children:[(0,b.jsxs)("h4",{className:"text-sm font-semibold text-white mb-2 flex items-center",children:[(0,b.jsx)(aT,{className:"h-4 w-4 text-green-400 mr-2"}),"Entry Conditions"]}),(0,b.jsx)("div",{className:"space-y-1",children:a.preciseEntry.conditions.map((a,c)=>(0,b.jsxs)("div",{className:"text-xs text-slate-300 flex items-center",children:[(0,b.jsx)("div",{className:"w-1 h-1 bg-green-400 rounded-full mr-2"}),a]},c))}),(0,b.jsxs)("div",{className:"mt-2 text-xs text-blue-400",children:[(0,b.jsx)(aQ,{className:"h-3 w-3 inline mr-1"}),a.preciseEntry.timing]})]}),(0,b.jsxs)("div",{className:"p-3 bg-slate-700/20 rounded-lg",children:[(0,b.jsxs)("h4",{className:"text-sm font-semibold text-white mb-2 flex items-center",children:[(0,b.jsx)(k,{className:"h-4 w-4 text-blue-400 mr-2"}),"Take Profit Plan"]}),(0,b.jsx)("div",{className:"space-y-2",children:a.preciseExit.takeProfits.map((a,c)=>(0,b.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,b.jsxs)("div",{className:"text-slate-300",children:[(0,b.jsxs)("span",{className:"font-medium",children:[a.target,":"]})," $",a.price.toFixed(2)]}),(0,b.jsxs)("div",{className:"text-slate-400",children:[a.percentage,"% of position"]})]},c))})]}),(0,b.jsxs)("div",{className:"p-3 bg-slate-700/20 rounded-lg",children:[(0,b.jsxs)("h4",{className:"text-sm font-semibold text-white mb-2 flex items-center",children:[(0,b.jsx)(l,{className:"h-4 w-4 text-red-400 mr-2"}),"Risk Management"]}),(0,b.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-xs",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"text-slate-400",children:"Max Risk:"}),(0,b.jsxs)("span",{className:"text-white ml-2",children:["$",a.riskManagement.maxRiskDollars.toFixed(0)]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"text-slate-400",children:"Account Risk:"}),(0,b.jsxs)("span",{className:"text-white ml-2",children:[a.riskManagement.accountRiskPercent,"%"]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"text-slate-400",children:"Time Stop:"}),(0,b.jsxs)("span",{className:"text-white ml-2",children:[a.riskManagement.timeStopHours,"h"]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"text-slate-400",children:"Max Drawdown:"}),(0,b.jsxs)("span",{className:"text-white ml-2",children:[a.riskManagement.maxDrawdownPercent,"%"]})]})]})]}),s&&(0,b.jsxs)("div",{className:"p-3 bg-gradient-to-r from-blue-900/20 to-purple-900/20 rounded-lg border border-blue-500/20",children:[(0,b.jsxs)("h4",{className:"text-sm font-semibold text-white mb-2 flex items-center",children:[(0,b.jsx)(m,{className:"h-4 w-4 text-blue-400 mr-2"}),"AI Risk Assessment",(0,b.jsx)(aW,{className:"h-3 w-3 text-blue-400 ml-1"})]}),q?(0,b.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,b.jsx)(p,{className:"h-4 w-4 animate-spin text-blue-400 mr-2"}),(0,b.jsx)("span",{className:"text-xs text-slate-300",children:"Analyzing risk..."})]}):n?(0,b.jsxs)("div",{className:"space-y-3",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{className:"flex items-center",children:[(0,b.jsx)("span",{className:"text-xs text-slate-400",children:"Risk Score:"}),(0,b.jsxs)("span",{className:`ml-2 text-sm font-bold ${(g=n.riskScore)<=3?"text-green-400":g<=6?"text-yellow-400":"text-red-400"}`,children:[n.riskScore,"/10"]})]}),(0,b.jsxs)("div",{className:"flex items-center",children:[(0,b.jsx)("span",{className:"text-xs text-slate-400",children:"Sentiment:"}),(0,b.jsx)("span",{className:`ml-2 text-sm font-semibold capitalize ${(a=>{switch(a){case"bullish":return"text-green-400";case"bearish":return"text-red-400";default:return"text-yellow-400"}})(n.sentiment)}`,children:n.sentiment})]})]}),n.riskFactors.length>0&&(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"text-xs text-slate-400 mb-1 block",children:"Key Risk Factors:"}),(0,b.jsx)("div",{className:"space-y-1",children:n.riskFactors.slice(0,2).map((a,c)=>(0,b.jsxs)("div",{className:"text-xs text-slate-300 flex items-start",children:[(0,b.jsx)(aS,{className:"h-3 w-3 text-yellow-400 mr-1 mt-0.5 flex-shrink-0"}),a]},c))})]}),n.recommendations.length>0&&(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"text-xs text-slate-400 mb-1 block",children:"AI Recommendations:"}),(0,b.jsx)("div",{className:"space-y-1",children:n.recommendations.slice(0,2).map((a,c)=>(0,b.jsxs)("div",{className:"text-xs text-slate-300 flex items-start",children:[(0,b.jsx)(aT,{className:"h-3 w-3 text-blue-400 mr-1 mt-0.5 flex-shrink-0"}),a]},c))})]})]}):(0,b.jsx)("div",{className:"text-xs text-slate-400 text-center py-2",children:"AI risk assessment unavailable"})]}),(0,b.jsxs)(aD,{variant:"outline",size:"sm",onClick:()=>j(!h),className:"w-full text-slate-300 hover:text-white",children:[h?"Hide":"Show"," Execution Details"]}),h&&(0,b.jsxs)("div",{className:"space-y-4 pt-4 border-t border-slate-700",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h5",{className:"text-sm font-semibold text-green-400 mb-2",children:"Entry Instructions:"}),(0,b.jsx)("div",{className:"space-y-1",children:a.executionPlan.entryInstructions.map((a,c)=>(0,b.jsxs)("div",{className:"text-xs text-slate-300 flex items-start",children:[(0,b.jsx)("div",{className:"w-4 h-4 rounded-full bg-green-500/20 text-green-400 flex items-center justify-center text-[10px] mr-2 mt-0.5 flex-shrink-0",children:c+1}),a]},c))})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h5",{className:"text-sm font-semibold text-red-400 mb-2",children:"Exit Instructions:"}),(0,b.jsx)("div",{className:"space-y-1",children:a.executionPlan.exitInstructions.map((a,c)=>(0,b.jsxs)("div",{className:"text-xs text-slate-300 flex items-start",children:[(0,b.jsx)("div",{className:"w-4 h-4 rounded-full bg-red-500/20 text-red-400 flex items-center justify-center text-[10px] mr-2 mt-0.5 flex-shrink-0",children:c+1}),a]},c))})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h5",{className:"text-sm font-semibold text-blue-400 mb-2",children:"Monitor These:"}),(0,b.jsx)("div",{className:"space-y-1",children:a.executionPlan.monitoringPoints.map((a,c)=>(0,b.jsxs)("div",{className:"text-xs text-slate-300 flex items-center",children:[(0,b.jsx)(aV,{className:"h-3 w-3 text-blue-400 mr-2"}),a]},c))})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h5",{className:"text-sm font-semibold text-yellow-400 mb-2",children:"Contingency Plans:"}),(0,b.jsx)("div",{className:"space-y-1",children:a.executionPlan.contingencyPlans.map((a,c)=>(0,b.jsxs)("div",{className:"text-xs text-slate-300 flex items-start",children:[(0,b.jsx)(aS,{className:"h-3 w-3 text-yellow-400 mr-2 mt-0.5 flex-shrink-0"}),a]},c))})]}),(0,b.jsxs)("div",{className:"p-3 bg-red-500/10 border border-red-500/20 rounded-lg",children:[(0,b.jsx)("h5",{className:"text-sm font-semibold text-red-400 mb-1",children:"Setup Invalidation:"}),(0,b.jsx)("div",{className:"text-xs text-slate-300",children:a.invalidation})]})]})]})]})}let aY=h("lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]);function aZ({scanResults:a,marketConditions:d,onRefresh:e}){let[f,g]=(0,c.useState)(null),[h,l]=(0,c.useState)(""),[n,o]=(0,c.useState)(null),[q,r]=(0,c.useState)(!1),[s,t]=(0,c.useState)(null);(0,c.useEffect)(()=>{u()},[]),(0,c.useEffect)(()=>{a.length>0&&f?.enabled&&v()},[a,f]);let u=async()=>{try{let a=await fetch("/api/ai?action=status"),b=await a.json();g(b)}catch(a){console.error("Error checking AI status:",a),t("Unable to connect to AI service")}},v=async()=>{if(f?.enabled&&0!==a.length){r(!0),t(null);try{let b=await fetch("/api/ai",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"market-commentary",data:{scanResults:a,marketConditions:d}})});if(b.ok){let{commentary:a}=await b.json();l(a)}let c=await fetch("/api/ai",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"trading-recommendations",data:{scanResults:a,userPreferences:{riskTolerance:"medium",tradingStyle:"moderate",accountSize:1e5}}})});if(c.ok){let{recommendations:a}=await c.json();o(a)}}catch(a){console.error("Error generating AI insights:",a),t("Failed to generate AI insights")}finally{r(!1)}}};return f?f.enabled?(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsx)(aE,{className:"bg-slate-800/50 border-slate-700",children:(0,b.jsx)(aF,{className:"pb-3",children:(0,b.jsxs)(aG,{className:"text-white flex items-center justify-between",children:[(0,b.jsxs)("div",{className:"flex items-center",children:[(0,b.jsx)(aW,{className:"mr-2 h-5 w-5 text-blue-400"}),"AI-Powered Insights",(0,b.jsx)(aK,{className:"ml-2 bg-blue-500/20 text-blue-400",children:f.model||"GPT-4o"})]}),(0,b.jsxs)(aD,{size:"sm",variant:"outline",onClick:v,disabled:q,className:"text-slate-300 hover:text-white",children:[q?(0,b.jsx)(p,{className:"h-4 w-4 animate-spin mr-1"}):(0,b.jsx)(m,{className:"h-4 w-4 mr-1"}),q?"Analyzing...":"Refresh Insights"]})]})})}),s&&(0,b.jsx)(aE,{className:"bg-red-900/20 border-red-500/30",children:(0,b.jsx)(aI,{className:"p-4",children:(0,b.jsxs)("div",{className:"flex items-center text-red-400",children:[(0,b.jsx)(aS,{className:"h-4 w-4 mr-2"}),s]})})}),h&&(0,b.jsxs)(aE,{className:"bg-slate-800/50 border-slate-700",children:[(0,b.jsx)(aF,{children:(0,b.jsxs)(aG,{className:"text-white flex items-center",children:[(0,b.jsx)(j,{className:"mr-2 h-5 w-5 text-green-400"}),"Market Commentary"]})}),(0,b.jsx)(aI,{children:(0,b.jsx)("div",{className:"prose prose-invert max-w-none",children:(0,b.jsx)("p",{className:"text-slate-300 leading-relaxed whitespace-pre-line",children:h})})})]}),n&&(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,b.jsxs)(aE,{className:"bg-slate-800/50 border-slate-700",children:[(0,b.jsx)(aF,{children:(0,b.jsxs)(aG,{className:"text-white flex items-center",children:[(0,b.jsx)(i,{className:"mr-2 h-5 w-5 text-green-400"}),"AI Top Picks"]})}),(0,b.jsx)(aI,{children:(0,b.jsx)("div",{className:"space-y-2",children:n.topPicks.map((a,c)=>(0,b.jsxs)("div",{className:"flex items-center justify-between p-2 bg-green-900/20 rounded",children:[(0,b.jsxs)("div",{className:"flex items-center",children:[(0,b.jsx)("div",{className:"w-6 h-6 rounded-full bg-green-500/20 text-green-400 flex items-center justify-center text-sm font-bold mr-3",children:c+1}),(0,b.jsx)("span",{className:"text-white font-semibold",children:a})]}),(0,b.jsx)(aK,{className:"bg-green-500/20 text-green-400",children:"Recommended"})]},a))})})]}),(0,b.jsxs)(aE,{className:"bg-slate-800/50 border-slate-700",children:[(0,b.jsx)(aF,{children:(0,b.jsxs)(aG,{className:"text-white flex items-center",children:[(0,b.jsx)(aY,{className:"mr-2 h-5 w-5 text-yellow-400"}),"Action Items"]})}),(0,b.jsx)(aI,{children:(0,b.jsx)("div",{className:"space-y-2",children:n.actionItems.map((a,c)=>(0,b.jsxs)("div",{className:"flex items-start p-2 bg-yellow-900/20 rounded",children:[(0,b.jsx)(k,{className:"h-4 w-4 text-yellow-400 mr-2 mt-0.5 flex-shrink-0"}),(0,b.jsx)("span",{className:"text-slate-300 text-sm",children:a})]},c))})})]})]}),n?.marketOutlook&&(0,b.jsxs)(aE,{className:"bg-slate-800/50 border-slate-700",children:[(0,b.jsx)(aF,{children:(0,b.jsxs)(aG,{className:"text-white flex items-center",children:[(0,b.jsx)(i,{className:"mr-2 h-5 w-5 text-blue-400"}),"Market Outlook"]})}),(0,b.jsx)(aI,{children:(0,b.jsx)("p",{className:"text-slate-300 leading-relaxed",children:n.marketOutlook})})]}),n?.avoidList&&n.avoidList.length>0&&(0,b.jsxs)(aE,{className:"bg-slate-800/50 border-slate-700",children:[(0,b.jsx)(aF,{children:(0,b.jsxs)(aG,{className:"text-white flex items-center",children:[(0,b.jsx)(aL,{className:"mr-2 h-5 w-5 text-red-400"}),"Caution List"]})}),(0,b.jsx)(aI,{children:(0,b.jsx)("div",{className:"space-y-2",children:n.avoidList.map((a,c)=>(0,b.jsxs)("div",{className:"flex items-center justify-between p-2 bg-red-900/20 rounded",children:[(0,b.jsx)("span",{className:"text-white font-semibold",children:a}),(0,b.jsx)(aK,{className:"bg-red-500/20 text-red-400",children:"Avoid"})]},a))})})]}),q&&(0,b.jsx)(aE,{className:"bg-slate-800/50 border-slate-700",children:(0,b.jsx)(aI,{className:"p-6",children:(0,b.jsxs)("div",{className:"flex items-center justify-center",children:[(0,b.jsx)(p,{className:"h-6 w-6 animate-spin text-blue-400 mr-2"}),(0,b.jsx)("span",{className:"text-slate-300",children:"Generating AI insights..."})]})})})]}):(0,b.jsxs)(aE,{className:"bg-slate-800/50 border-slate-700",children:[(0,b.jsx)(aF,{children:(0,b.jsxs)(aG,{className:"text-white flex items-center",children:[(0,b.jsx)(m,{className:"mr-2 h-5 w-5 text-gray-400"}),"AI Insights",(0,b.jsx)(aK,{variant:"outline",className:"ml-2 text-gray-400 border-gray-400",children:"Disabled"})]})}),(0,b.jsx)(aI,{children:(0,b.jsxs)("div",{className:"text-center py-6",children:[(0,b.jsx)(m,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,b.jsx)("p",{className:"text-slate-300 mb-4",children:"AI-powered insights are currently disabled."}),(0,b.jsx)("p",{className:"text-sm text-slate-400 mb-4",children:"Enable OpenAI integration to get intelligent market analysis, risk assessments, and personalized trading recommendations."}),(0,b.jsx)(aD,{variant:"outline",onClick:u,children:"Check Configuration"})]})})]}):(0,b.jsx)(aE,{className:"bg-slate-800/50 border-slate-700",children:(0,b.jsx)(aI,{className:"p-6",children:(0,b.jsxs)("div",{className:"flex items-center justify-center",children:[(0,b.jsx)(p,{className:"h-6 w-6 animate-spin text-blue-400 mr-2"}),(0,b.jsx)("span",{className:"text-slate-300",children:"Checking AI status..."})]})})})}function a$({autoScan:a=!1,accountSize:d=1e5}){let[e,f]=(0,c.useState)(!1),[g,h]=(0,c.useState)(null),[j,l]=(0,c.useState)("both"),[m,o]=(0,c.useState)(null),[q,r]=(0,c.useState)(d),s=async a=>{try{alert(`Execute Trade for ${a.symbol}:

Entry: $${a.preciseEntry.price.toFixed(2)} (${a.preciseEntry.orderType})
Stop: $${a.preciseExit.stopLoss.price.toFixed(2)}
Target: $${a.targets[0].toFixed(2)}
Shares: ${a.positionSize}

This would normally open your trading platform or paper trading interface.`)}catch(a){console.error("Error executing trade:",a)}},t=a=>{let b=`https://www.tradingview.com/chart/?symbol=${a}`;window.open(b,"_blank")};(0,c.useEffect)(()=>{a&&u("quick")},[a]);let u=async a=>{f(!0),o(null),h(null);try{let b=await fetch(`/api/scanner/strategies?type=${a}&accountSize=${q}&limit=20`);if(!b.ok)throw Error("Failed to fetch strategy scan results");let c=await b.json();h(c)}catch(a){o("Failed to perform strategy scan. Please try again."),console.error("Strategy scan error:",a)}finally{f(!1)}},v=g?.topSetups.filter(a=>"both"===j||("overnight"===j?a.overnightSetup:"breakout"!==j||a.breakoutSetup))||[];return(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)(aE,{className:"bg-slate-800/50 border-slate-700",children:[(0,b.jsxs)(aF,{children:[(0,b.jsxs)(aG,{className:"text-white flex items-center",children:[(0,b.jsx)(n,{className:"mr-2 h-5 w-5 text-yellow-400"}),"Professional Swing Trading Strategies"]}),(0,b.jsx)(aH,{className:"text-slate-300",children:"Automated scanner implementing proven swing trading methodologies with precise entry/exit rules"})]}),(0,b.jsxs)(aI,{children:[(0,b.jsxs)("div",{className:"mb-4",children:[(0,b.jsx)("label",{className:"block text-sm text-slate-300 mb-2",children:"Account Size (for position sizing)"}),(0,b.jsx)("input",{type:"number",value:q,onChange:a=>r(parseInt(a.target.value)||1e5),className:"w-32 px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm",min:"10000",step:"10000",disabled:e})]}),(0,b.jsxs)("div",{className:"mb-4",children:[(0,b.jsx)("label",{className:"block text-sm text-slate-300 mb-2",children:"Strategy Filter"}),(0,b.jsxs)("div",{className:"flex gap-2",children:[(0,b.jsx)(aD,{variant:"both"===j?"default":"outline",size:"sm",onClick:()=>l("both"),className:"both"===j?"bg-blue-600":"border-slate-600 text-slate-300",children:"All Strategies"}),(0,b.jsxs)(aD,{variant:"overnight"===j?"default":"outline",size:"sm",onClick:()=>l("overnight"),className:"overnight"===j?"bg-purple-600":"border-slate-600 text-slate-300",children:[(0,b.jsx)(aO,{className:"mr-1 h-3 w-3"}),"Overnight"]}),(0,b.jsxs)(aD,{variant:"breakout"===j?"default":"outline",size:"sm",onClick:()=>l("breakout"),className:"breakout"===j?"bg-green-600":"border-slate-600 text-slate-300",children:[(0,b.jsx)(i,{className:"mr-1 h-3 w-3"}),"Breakout"]})]})]}),(0,b.jsxs)("div",{className:"flex gap-4 mb-4",children:[(0,b.jsxs)(aD,{onClick:()=>u("quick"),disabled:e,className:"bg-blue-600 hover:bg-blue-700",children:[e?(0,b.jsx)(p,{className:"mr-2 h-4 w-4 animate-spin"}):(0,b.jsx)(n,{className:"mr-2 h-4 w-4"}),"Quick Strategy Scan"]}),(0,b.jsxs)(aD,{onClick:()=>u("full"),disabled:e,variant:"outline",className:"border-slate-600 text-slate-300 hover:bg-slate-800",children:[e?(0,b.jsx)(p,{className:"mr-2 h-4 w-4 animate-spin"}):(0,b.jsx)(k,{className:"mr-2 h-4 w-4"}),"Full Strategy Scan"]})]}),g?.marketConditions&&(0,b.jsx)("div",{className:"p-3 bg-slate-700/50 rounded-lg",children:(0,b.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,b.jsx)("span",{className:"text-slate-300",children:"Market Status:"}),(0,b.jsxs)("div",{className:"flex items-center gap-4",children:[(0,b.jsx)("span",{className:"text-white",children:g.marketConditions.timeOfDay}),(0,b.jsxs)("div",{className:"flex gap-2",children:[(0,b.jsx)(aK,{className:g.marketConditions.marketHours?"bg-green-500/20 text-green-400":"bg-red-500/20 text-red-400",children:g.marketConditions.marketHours?"Market Open":"Market Closed"}),(0,b.jsx)(aK,{className:g.marketConditions.isOptimalScanTime?"bg-blue-500/20 text-blue-400":"bg-yellow-500/20 text-yellow-400",children:g.marketConditions.isOptimalScanTime?"Optimal Scan Time":"Outside Optimal Hours"})]})]})]})}),e&&(0,b.jsxs)("div",{className:"text-center py-4",children:[(0,b.jsx)(p,{className:"mx-auto h-8 w-8 animate-spin text-blue-400"}),(0,b.jsx)("p",{className:"text-slate-300 mt-2",children:"Analyzing stocks for professional swing trading setups..."})]})]})]}),m&&(0,b.jsx)(aE,{className:"bg-red-900/20 border-red-500/50",children:(0,b.jsx)(aI,{className:"p-6",children:(0,b.jsx)("p",{className:"text-red-300 text-center",children:m})})}),g&&(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)(aE,{className:"bg-slate-800/50 border-slate-700",children:[(0,b.jsx)(aF,{children:(0,b.jsx)(aG,{className:"text-white",children:"Strategy Scan Summary"})}),(0,b.jsx)(aI,{children:(0,b.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("div",{className:"text-2xl font-bold text-white",children:g.totalScanned}),(0,b.jsx)("div",{className:"text-sm text-slate-300",children:"Stocks Scanned"})]}),(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("div",{className:"text-2xl font-bold text-purple-400",children:g.overnightSetups}),(0,b.jsx)("div",{className:"text-sm text-slate-300",children:"Overnight Setups"})]}),(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("div",{className:"text-2xl font-bold text-green-400",children:g.breakoutSetups}),(0,b.jsx)("div",{className:"text-sm text-slate-300",children:"Breakout Setups"})]}),(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("div",{className:"text-2xl font-bold text-blue-400",children:g.bothStrategies}),(0,b.jsx)("div",{className:"text-sm text-slate-300",children:"Both Strategies"})]})]})})]}),(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("h2",{className:"text-2xl font-bold text-white flex items-center",children:[(0,b.jsx)(k,{className:"mr-3 h-6 w-6 text-green-400"}),"Professional Trading Setups (",v.length,")"]}),(0,b.jsxs)(aD,{variant:"outline",size:"sm",onClick:()=>window.open("https://www.tradingview.com","_blank"),className:"text-slate-300 hover:text-white",children:[(0,b.jsx)(aP,{className:"h-4 w-4 mr-2"}),"Open TradingView"]})]}),v.map((a,c)=>{let d="overnight_momentum"===a.bestStrategy?a.overnightSetup:a.breakoutSetup;if(!d)return null;let e={...d,symbol:a.symbol};return(0,b.jsx)(aX,{setup:e,onExecuteTrade:s,onViewChart:t},a.symbol)})]}),v.length>0&&(0,b.jsx)("div",{className:"mt-8",children:(0,b.jsx)(aZ,{scanResults:v,marketConditions:g.marketConditions,onRefresh:()=>runScan()})})]})]})}function a_(a,b,{checkForDefaultPrevented:c=!0}={}){return function(d){if(a?.(d),!1===c||!d.defaultPrevented)return b?.(d)}}function a0(a,d=[]){let e=[],f=()=>{let b=e.map(a=>c.createContext(a));return function(d){let e=d?.[a]||b;return c.useMemo(()=>({[`__scope${a}`]:{...d,[a]:e}}),[d,e])}};return f.scopeName=a,[function(d,f){let g=c.createContext(f),h=e.length;e=[...e,f];let i=d=>{let{scope:e,children:f,...i}=d,j=e?.[a]?.[h]||g,k=c.useMemo(()=>i,Object.values(i));return(0,b.jsx)(j.Provider,{value:k,children:f})};return i.displayName=d+"Provider",[i,function(b,e){let i=e?.[a]?.[h]||g,j=c.useContext(i);if(j)return j;if(void 0!==f)return f;throw Error(`\`${b}\` must be used within \`${d}\``)}]},function(...a){let b=a[0];if(1===a.length)return b;let d=()=>{let d=a.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(a){let e=d.reduce((b,{useScope:c,scopeName:d})=>{let e=c(a)[`__scope${d}`];return{...b,...e}},{});return c.useMemo(()=>({[`__scope${b.scopeName}`]:e}),[e])}};return d.scopeName=b.scopeName,d}(f,...d)]}var a1=new WeakMap;function a2(a,b){if("at"in Array.prototype)return Array.prototype.at.call(a,b);let c=function(a,b){let c=a.length,d=a3(b),e=d>=0?d:c+d;return e<0||e>=c?-1:e}(a,b);return -1===c?void 0:a[c]}function a3(a){return a!=a||0===a?0:Math.trunc(a)}(class a extends Map{#a;constructor(a){super(a),this.#a=[...super.keys()],a1.set(this,!0)}set(a,b){return a1.get(this)&&(this.has(a)?this.#a[this.#a.indexOf(a)]=a:this.#a.push(a)),super.set(a,b),this}insert(a,b,c){let d,e=this.has(b),f=this.#a.length,g=a3(a),h=g>=0?g:f+g,i=h<0||h>=f?-1:h;if(i===this.size||e&&i===this.size-1||-1===i)return this.set(b,c),this;let j=this.size+ +!e;g<0&&h++;let k=[...this.#a],l=!1;for(let a=h;a<j;a++)if(h===a){let f=k[a];k[a]===b&&(f=k[a+1]),e&&this.delete(b),d=this.get(f),this.set(b,c)}else{l||k[a-1]!==b||(l=!0);let c=k[l?a:a-1],e=d;d=this.get(c),this.delete(c),this.set(c,e)}return this}with(b,c,d){let e=new a(this);return e.insert(b,c,d),e}before(a){let b=this.#a.indexOf(a)-1;if(!(b<0))return this.entryAt(b)}setBefore(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d,b,c)}after(a){let b=this.#a.indexOf(a);if(-1!==(b=-1===b||b===this.size-1?-1:b+1))return this.entryAt(b)}setAfter(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d+1,b,c)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return this.#a=[],super.clear()}delete(a){let b=super.delete(a);return b&&this.#a.splice(this.#a.indexOf(a),1),b}deleteAt(a){let b=this.keyAt(a);return void 0!==b&&this.delete(b)}at(a){let b=a2(this.#a,a);if(void 0!==b)return this.get(b)}entryAt(a){let b=a2(this.#a,a);if(void 0!==b)return[b,this.get(b)]}indexOf(a){return this.#a.indexOf(a)}keyAt(a){return a2(this.#a,a)}from(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.at(d)}keyFrom(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.keyAt(d)}find(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return d;c++}}findIndex(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return c;c++}return -1}filter(b,c){let d=[],e=0;for(let a of this)Reflect.apply(b,c,[a,e,this])&&d.push(a),e++;return new a(d)}map(b,c){let d=[],e=0;for(let a of this)d.push([a[0],Reflect.apply(b,c,[a,e,this])]),e++;return new a(d)}reduce(...a){let[b,c]=a,d=0,e=c??this.at(0);for(let c of this)e=0===d&&1===a.length?c:Reflect.apply(b,this,[e,c,d,this]),d++;return e}reduceRight(...a){let[b,c]=a,d=c??this.at(-1);for(let c=this.size-1;c>=0;c--){let e=this.at(c);d=c===this.size-1&&1===a.length?e:Reflect.apply(b,this,[d,e,c,this])}return d}toSorted(b){return new a([...this.entries()].sort(b))}toReversed(){let b=new a;for(let a=this.size-1;a>=0;a--){let c=this.keyAt(a),d=this.get(c);b.set(c,d)}return b}toSpliced(...b){let c=[...this.entries()];return c.splice(...b),new a(c)}slice(b,c){let d=new a,e=this.size-1;if(void 0===b)return d;b<0&&(b+=this.size),void 0!==c&&c>0&&(e=c-1);for(let a=b;a<=e;a++){let b=this.keyAt(a),c=this.get(b);d.set(b,c)}return d}every(a,b){let c=0;for(let d of this){if(!Reflect.apply(a,b,[d,c,this]))return!1;c++}return!0}some(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return!0;c++}return!1}});var a4=globalThis?.document?c.useLayoutEffect:()=>{},a5=c[" useId ".trim().toString()]||(()=>void 0),a6=0;function a7(a){let[b,d]=c.useState(a5());return a4(()=>{a||d(a=>a??String(a6++))},[a]),a||(b?`radix-${b}`:"")}a.i(42402);var a8=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((a,d)=>{let e=u(`Primitive.${d}`),f=c.forwardRef((a,c)=>{let{asChild:f,...g}=a;return(0,b.jsx)(f?e:d,{...g,ref:c})});return f.displayName=`Primitive.${d}`,{...a,[d]:f}},{});c[" useEffectEvent ".trim().toString()],c[" useInsertionEffect ".trim().toString()];var a9=c[" useInsertionEffect ".trim().toString()]||a4;function ba({prop:a,defaultProp:b,onChange:d=()=>{},caller:e}){let[f,g,h]=function({defaultProp:a,onChange:b}){let[d,e]=c.useState(a),f=c.useRef(d),g=c.useRef(b);return a9(()=>{g.current=b},[b]),c.useEffect(()=>{f.current!==d&&(g.current?.(d),f.current=d)},[d,f]),[d,e,g]}({defaultProp:b,onChange:d}),i=void 0!==a,j=i?a:f;{let b=c.useRef(void 0!==a);c.useEffect(()=>{let a=b.current;if(a!==i){let b=i?"controlled":"uncontrolled";console.warn(`${e} is changing from ${a?"controlled":"uncontrolled"} to ${b}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}b.current=i},[i,e])}return[j,c.useCallback(b=>{if(i){let c="function"==typeof b?b(a):b;c!==a&&h.current?.(c)}else g(b)},[i,a,g,h])]}Symbol("RADIX:SYNC_STATE");var bb=c.createContext(void 0);function bc(a){let b=c.useContext(bb);return a||b||"ltr"}var bd="rovingFocusGroup.onEntryFocus",be={bubbles:!1,cancelable:!0},bf="RovingFocusGroup",[bg,bh,bi]=function(a){let d=a+"CollectionProvider",[e,f]=a0(d),[g,h]=e(d,{collectionRef:{current:null},itemMap:new Map}),i=a=>{let{scope:d,children:e}=a,f=c.default.useRef(null),h=c.default.useRef(new Map).current;return(0,b.jsx)(g,{scope:d,itemMap:h,collectionRef:f,children:e})};i.displayName=d;let j=a+"CollectionSlot",k=u(j),l=c.default.forwardRef((a,c)=>{let{scope:d,children:e}=a,f=t(c,h(j,d).collectionRef);return(0,b.jsx)(k,{ref:f,children:e})});l.displayName=j;let m=a+"CollectionItemSlot",n="data-radix-collection-item",o=u(m),p=c.default.forwardRef((a,d)=>{let{scope:e,children:f,...g}=a,i=c.default.useRef(null),j=t(d,i),k=h(m,e);return c.default.useEffect(()=>(k.itemMap.set(i,{ref:i,...g}),()=>void k.itemMap.delete(i))),(0,b.jsx)(o,{...{[n]:""},ref:j,children:f})});return p.displayName=m,[{Provider:i,Slot:l,ItemSlot:p},function(b){let d=h(a+"CollectionConsumer",b);return c.default.useCallback(()=>{let a=d.collectionRef.current;if(!a)return[];let b=Array.from(a.querySelectorAll(`[${n}]`));return Array.from(d.itemMap.values()).sort((a,c)=>b.indexOf(a.ref.current)-b.indexOf(c.ref.current))},[d.collectionRef,d.itemMap])},f]}(bf),[bj,bk]=a0(bf,[bi]),[bl,bm]=bj(bf),bn=c.forwardRef((a,c)=>(0,b.jsx)(bg.Provider,{scope:a.__scopeRovingFocusGroup,children:(0,b.jsx)(bg.Slot,{scope:a.__scopeRovingFocusGroup,children:(0,b.jsx)(bo,{...a,ref:c})})}));bn.displayName=bf;var bo=c.forwardRef((a,d)=>{let{__scopeRovingFocusGroup:e,orientation:f,loop:g=!1,dir:h,currentTabStopId:i,defaultCurrentTabStopId:j,onCurrentTabStopIdChange:k,onEntryFocus:l,preventScrollOnEntryFocus:m=!1,...n}=a,o=c.useRef(null),p=t(d,o),q=bc(h),[r,s]=ba({prop:i,defaultProp:j??null,onChange:k,caller:bf}),[u,v]=c.useState(!1),w=function(a){let b=c.useRef(a);return c.useEffect(()=>{b.current=a}),c.useMemo(()=>(...a)=>b.current?.(...a),[])}(l),x=bh(e),y=c.useRef(!1),[z,A]=c.useState(0);return c.useEffect(()=>{let a=o.current;if(a)return a.addEventListener(bd,w),()=>a.removeEventListener(bd,w)},[w]),(0,b.jsx)(bl,{scope:e,orientation:f,dir:q,loop:g,currentTabStopId:r,onItemFocus:c.useCallback(a=>s(a),[s]),onItemShiftTab:c.useCallback(()=>v(!0),[]),onFocusableItemAdd:c.useCallback(()=>A(a=>a+1),[]),onFocusableItemRemove:c.useCallback(()=>A(a=>a-1),[]),children:(0,b.jsx)(a8.div,{tabIndex:u||0===z?-1:0,"data-orientation":f,...n,ref:p,style:{outline:"none",...a.style},onMouseDown:a_(a.onMouseDown,()=>{y.current=!0}),onFocus:a_(a.onFocus,a=>{let b=!y.current;if(a.target===a.currentTarget&&b&&!u){let b=new CustomEvent(bd,be);if(a.currentTarget.dispatchEvent(b),!b.defaultPrevented){let a=x().filter(a=>a.focusable);bs([a.find(a=>a.active),a.find(a=>a.id===r),...a].filter(Boolean).map(a=>a.ref.current),m)}}y.current=!1}),onBlur:a_(a.onBlur,()=>v(!1))})})}),bp="RovingFocusGroupItem",bq=c.forwardRef((a,d)=>{let{__scopeRovingFocusGroup:e,focusable:f=!0,active:g=!1,tabStopId:h,children:i,...j}=a,k=a7(),l=h||k,m=bm(bp,e),n=m.currentTabStopId===l,o=bh(e),{onFocusableItemAdd:p,onFocusableItemRemove:q,currentTabStopId:r}=m;return c.useEffect(()=>{if(f)return p(),()=>q()},[f,p,q]),(0,b.jsx)(bg.ItemSlot,{scope:e,id:l,focusable:f,active:g,children:(0,b.jsx)(a8.span,{tabIndex:n?0:-1,"data-orientation":m.orientation,...j,ref:d,onMouseDown:a_(a.onMouseDown,a=>{f?m.onItemFocus(l):a.preventDefault()}),onFocus:a_(a.onFocus,()=>m.onItemFocus(l)),onKeyDown:a_(a.onKeyDown,a=>{if("Tab"===a.key&&a.shiftKey)return void m.onItemShiftTab();if(a.target!==a.currentTarget)return;let b=function(a,b,c){var d;let e=(d=a.key,"rtl"!==c?d:"ArrowLeft"===d?"ArrowRight":"ArrowRight"===d?"ArrowLeft":d);if(!("vertical"===b&&["ArrowLeft","ArrowRight"].includes(e))&&!("horizontal"===b&&["ArrowUp","ArrowDown"].includes(e)))return br[e]}(a,m.orientation,m.dir);if(void 0!==b){if(a.metaKey||a.ctrlKey||a.altKey||a.shiftKey)return;a.preventDefault();let c=o().filter(a=>a.focusable).map(a=>a.ref.current);if("last"===b)c.reverse();else if("prev"===b||"next"===b){"prev"===b&&c.reverse();let d=c.indexOf(a.currentTarget);c=m.loop?function(a,b){return a.map((c,d)=>a[(b+d)%a.length])}(c,d+1):c.slice(d+1)}setTimeout(()=>bs(c))}}),children:"function"==typeof i?i({isCurrentTabStop:n,hasTabStop:null!=r}):i})})});bq.displayName=bp;var br={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function bs(a,b=!1){let c=document.activeElement;for(let d of a)if(d===c||(d.focus({preventScroll:b}),document.activeElement!==c))return}var bt=a=>{let{present:b,children:d}=a,e=function(a){var b,d;let[e,f]=c.useState(),g=c.useRef(null),h=c.useRef(a),i=c.useRef("none"),[j,k]=(b=a?"mounted":"unmounted",d={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},c.useReducer((a,b)=>d[a][b]??a,b));return c.useEffect(()=>{let a=bu(g.current);i.current="mounted"===j?a:"none"},[j]),a4(()=>{let b=g.current,c=h.current;if(c!==a){let d=i.current,e=bu(b);a?k("MOUNT"):"none"===e||b?.display==="none"?k("UNMOUNT"):c&&d!==e?k("ANIMATION_OUT"):k("UNMOUNT"),h.current=a}},[a,k]),a4(()=>{if(e){let a,b=e.ownerDocument.defaultView??window,c=c=>{let d=bu(g.current).includes(CSS.escape(c.animationName));if(c.target===e&&d&&(k("ANIMATION_END"),!h.current)){let c=e.style.animationFillMode;e.style.animationFillMode="forwards",a=b.setTimeout(()=>{"forwards"===e.style.animationFillMode&&(e.style.animationFillMode=c)})}},d=a=>{a.target===e&&(i.current=bu(g.current))};return e.addEventListener("animationstart",d),e.addEventListener("animationcancel",c),e.addEventListener("animationend",c),()=>{b.clearTimeout(a),e.removeEventListener("animationstart",d),e.removeEventListener("animationcancel",c),e.removeEventListener("animationend",c)}}k("ANIMATION_END")},[e,k]),{isPresent:["mounted","unmountSuspended"].includes(j),ref:c.useCallback(a=>{g.current=a?getComputedStyle(a):null,f(a)},[])}}(b),f="function"==typeof d?d({present:e.isPresent}):c.Children.only(d),g=t(e.ref,function(a){let b=Object.getOwnPropertyDescriptor(a.props,"ref")?.get,c=b&&"isReactWarning"in b&&b.isReactWarning;return c?a.ref:(c=(b=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in b&&b.isReactWarning)?a.props.ref:a.props.ref||a.ref}(f));return"function"==typeof d||e.isPresent?c.cloneElement(f,{ref:g}):null};function bu(a){return a?.animationName||"none"}bt.displayName="Presence";var bv="Tabs",[bw,bx]=a0(bv,[bk]),by=bk(),[bz,bA]=bw(bv),bB=c.forwardRef((a,c)=>{let{__scopeTabs:d,value:e,onValueChange:f,defaultValue:g,orientation:h="horizontal",dir:i,activationMode:j="automatic",...k}=a,l=bc(i),[m,n]=ba({prop:e,onChange:f,defaultProp:g??"",caller:bv});return(0,b.jsx)(bz,{scope:d,baseId:a7(),value:m,onValueChange:n,orientation:h,dir:l,activationMode:j,children:(0,b.jsx)(a8.div,{dir:l,"data-orientation":h,...k,ref:c})})});bB.displayName=bv;var bC="TabsList",bD=c.forwardRef((a,c)=>{let{__scopeTabs:d,loop:e=!0,...f}=a,g=bA(bC,d),h=by(d);return(0,b.jsx)(bn,{asChild:!0,...h,orientation:g.orientation,dir:g.dir,loop:e,children:(0,b.jsx)(a8.div,{role:"tablist","aria-orientation":g.orientation,...f,ref:c})})});bD.displayName=bC;var bE="TabsTrigger",bF=c.forwardRef((a,c)=>{let{__scopeTabs:d,value:e,disabled:f=!1,...g}=a,h=bA(bE,d),i=by(d),j=bI(h.baseId,e),k=bJ(h.baseId,e),l=e===h.value;return(0,b.jsx)(bq,{asChild:!0,...i,focusable:!f,active:l,children:(0,b.jsx)(a8.button,{type:"button",role:"tab","aria-selected":l,"aria-controls":k,"data-state":l?"active":"inactive","data-disabled":f?"":void 0,disabled:f,id:j,...g,ref:c,onMouseDown:a_(a.onMouseDown,a=>{f||0!==a.button||!1!==a.ctrlKey?a.preventDefault():h.onValueChange(e)}),onKeyDown:a_(a.onKeyDown,a=>{[" ","Enter"].includes(a.key)&&h.onValueChange(e)}),onFocus:a_(a.onFocus,()=>{let a="manual"!==h.activationMode;l||f||!a||h.onValueChange(e)})})})});bF.displayName=bE;var bG="TabsContent",bH=c.forwardRef((a,d)=>{let{__scopeTabs:e,value:f,forceMount:g,children:h,...i}=a,j=bA(bG,e),k=bI(j.baseId,f),l=bJ(j.baseId,f),m=f===j.value,n=c.useRef(m);return c.useEffect(()=>{let a=requestAnimationFrame(()=>n.current=!1);return()=>cancelAnimationFrame(a)},[]),(0,b.jsx)(bt,{present:g||m,children:({present:c})=>(0,b.jsx)(a8.div,{"data-state":m?"active":"inactive","data-orientation":j.orientation,role:"tabpanel","aria-labelledby":k,hidden:!c,id:l,tabIndex:0,...i,ref:d,style:{...a.style,animationDuration:n.current?"0s":void 0},children:c&&h})})});function bI(a,b){return`${a}-trigger-${b}`}function bJ(a,b){return`${a}-content-${b}`}bH.displayName=bG;let bK=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(bD,{ref:d,className:az("inline-flex h-10 items-center justify-center rounded-md bg-slate-100 p-1 text-slate-500 dark:bg-slate-800 dark:text-slate-400",a),...c}));bK.displayName=bD.displayName;let bL=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(bF,{ref:d,className:az("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-white transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-white data-[state=active]:text-slate-950 data-[state=active]:shadow-sm dark:ring-offset-slate-950 dark:focus-visible:ring-slate-300 dark:data-[state=active]:bg-slate-950 dark:data-[state=active]:text-slate-50",a),...c}));bL.displayName=bF.displayName;let bM=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(bH,{ref:d,className:az("mt-2 ring-offset-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2 dark:ring-offset-slate-950 dark:focus-visible:ring-slate-300",a),...c}));bM.displayName=bH.displayName;let bN=h("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]),bO=h("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),bP=h("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]),bQ=h("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),bR=h("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);function bS({setup:a,onExecuteTrade:d,onViewChart:e,onGenerateEntryTrigger:f}){var g;let[h,j]=(0,c.useState)(!1),l=a.rewardPlanning.riskRewardRatio,m=a.riskManagement.positionSize*a.riskManagement.riskPerShare*l,o=a.riskManagement.positionSize*a.riskManagement.riskPerShare;return(0,b.jsxs)(aE,{className:"hover:shadow-lg transition-all duration-200 border-l-4 border-l-blue-500",children:[(0,b.jsx)(aF,{className:"pb-3",children:(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{className:"flex items-center gap-3",children:[(0,b.jsxs)("div",{children:[(0,b.jsxs)(aG,{className:"text-xl font-bold flex items-center gap-2",children:[a.symbol,(0,b.jsx)(bP,{className:"h-5 w-5 text-yellow-500"})]}),(0,b.jsx)("p",{className:"text-sm text-muted-foreground",children:a.name})]}),(0,b.jsxs)("div",{className:"flex items-center gap-2",children:[(0,b.jsxs)(aK,{className:`${(g=a.setupGrade).startsWith("A")?"text-green-600 bg-green-50 border-green-200":g.startsWith("B")?"text-blue-600 bg-blue-50 border-blue-200":g.startsWith("C")?"text-yellow-600 bg-yellow-50 border-yellow-200":"text-red-600 bg-red-50 border-red-200"} border`,children:["Grade ",a.setupGrade]}),(0,b.jsxs)(aK,{variant:"outline",className:"font-semibold",children:["Score: ",a.overallScore,"/100"]})]})]}),(0,b.jsxs)("div",{className:"text-right",children:[(0,b.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:aA(a.gapScan.price)}),(0,b.jsxs)("div",{className:"text-sm font-semibold text-green-600",children:["Gap: +",aB(a.gapScan.gapPercent)]})]})]})}),(0,b.jsxs)(aI,{className:"space-y-4",children:[(0,b.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border",children:[(0,b.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,b.jsx)(n,{className:"h-5 w-5 text-purple-600"}),(0,b.jsx)("span",{className:"font-semibold text-purple-800",children:"Catalyst"}),(a=>{switch(a){case"bullish":return(0,b.jsx)(i,{className:"h-4 w-4 text-green-500"});case"bearish":return(0,b.jsx)(aL,{className:"h-4 w-4 text-red-500"});default:return(0,b.jsx)(bN,{className:"h-4 w-4 text-gray-500"})}})(a.catalyst.impact),(0,b.jsx)(aK,{className:(a=>{switch(a){case"tier_1":return"bg-green-500 text-white";case"tier_2":return"bg-yellow-500 text-white";case"tier_3":return"bg-gray-500 text-white";default:return"bg-gray-400 text-white"}})(a.catalyst.tier),children:a.catalyst.tier.replace("_"," ").toUpperCase()}),(0,b.jsx)(aK,{className:(a=>{switch(a){case"fresh":return"text-green-600 bg-green-50";case"moderate":return"text-yellow-600 bg-yellow-50";case"stale":return"text-red-600 bg-red-50";default:return"text-gray-600 bg-gray-50"}})(a.catalyst.freshness),children:a.catalyst.freshness})]}),(0,b.jsx)("h4",{className:"font-medium text-gray-800 mb-1",children:a.catalyst.title}),(0,b.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:a.catalyst.description}),(0,b.jsxs)("div",{className:"flex items-center gap-4 text-xs text-gray-500",children:[(0,b.jsxs)("span",{children:["Quality: ",a.catalyst.qualityScore,"/10"]}),(0,b.jsxs)("span",{children:["Source: ",a.catalyst.source]}),(0,b.jsxs)("span",{className:"flex items-center gap-1",children:[(0,b.jsx)(aQ,{className:"h-3 w-3"}),new Date(a.catalyst.announcementTime).toLocaleString()]})]})]}),(0,b.jsxs)("div",{className:"grid grid-cols-4 gap-4",children:[(0,b.jsxs)("div",{className:"text-center p-3 bg-blue-50 rounded-lg",children:[(0,b.jsx)("div",{className:"text-sm text-blue-600 font-medium",children:"Entry"}),(0,b.jsx)("div",{className:"text-lg font-bold text-blue-800",children:aA(a.riskManagement.entryPrice)})]}),(0,b.jsxs)("div",{className:"text-center p-3 bg-red-50 rounded-lg",children:[(0,b.jsx)("div",{className:"text-sm text-red-600 font-medium",children:"Stop Loss"}),(0,b.jsx)("div",{className:"text-lg font-bold text-red-800",children:aA(a.riskManagement.stopLoss)})]}),(0,b.jsxs)("div",{className:"text-center p-3 bg-green-50 rounded-lg",children:[(0,b.jsx)("div",{className:"text-sm text-green-600 font-medium",children:"Target (3R)"}),(0,b.jsx)("div",{className:"text-lg font-bold text-green-800",children:aA(a.rewardPlanning.target3R)})]}),(0,b.jsxs)("div",{className:"text-center p-3 bg-purple-50 rounded-lg",children:[(0,b.jsx)("div",{className:"text-sm text-purple-600 font-medium",children:"R/R Ratio"}),(0,b.jsxs)("div",{className:"text-lg font-bold text-purple-800",children:[l,":1"]})]})]}),(0,b.jsx)("div",{className:"bg-gray-50 p-4 rounded-lg",children:(0,b.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-sm",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"text-gray-600",children:"Position Size:"}),(0,b.jsxs)("div",{className:"font-semibold",children:[a.riskManagement.positionSize," shares"]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"text-gray-600",children:"Risk Amount:"}),(0,b.jsx)("div",{className:"font-semibold text-red-600",children:aA(o)})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"text-gray-600",children:"Potential Profit (3R):"}),(0,b.jsx)("div",{className:"font-semibold text-green-600",children:aA(m)})]})]})}),(0,b.jsxs)("div",{className:"flex items-center justify-between p-3 bg-slate-50 rounded-lg",children:[(0,b.jsxs)("div",{className:"flex items-center gap-2",children:[(0,b.jsx)(k,{className:"h-4 w-4 text-slate-600"}),(0,b.jsx)("span",{className:"text-sm font-medium",children:"Technical Gate:"}),(0,b.jsxs)(aK,{variant:"A"===a.technicalGate.overallGrade?"default":"secondary",children:["Grade ",a.technicalGate.overallGrade]}),(0,b.jsxs)("span",{className:"text-xs text-muted-foreground",children:["(",a.technicalGate.gateScore,"/100)"]})]}),(0,b.jsxs)("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[a.technicalGate.aboveSMA200&&(0,b.jsx)(aT,{className:"h-3 w-3 text-green-500"}),a.technicalGate.aboveEMA8&&(0,b.jsx)(aT,{className:"h-3 w-3 text-green-500"}),a.technicalGate.dailyTrendConfirmed&&(0,b.jsx)(aT,{className:"h-3 w-3 text-green-500"})]})]}),a.entryTrigger&&(0,b.jsxs)("div",{className:"p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:[(0,b.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,b.jsx)(aU,{className:"h-4 w-4 text-yellow-600"}),(0,b.jsx)("span",{className:"font-medium text-yellow-800",children:"Entry Trigger"}),(0,b.jsx)(aK,{variant:"outline",className:"text-xs",children:a.entryTrigger.urgency.replace("_"," ")})]}),(0,b.jsxs)("p",{className:"text-sm text-yellow-700",children:[a.entryTrigger.entrySignalType.replace("_"," ").toUpperCase()," at ",aA(a.entryTrigger.entryPrice)]})]}),(0,b.jsx)("div",{className:"grid grid-cols-2 gap-2 text-xs",children:Object.entries(a.validationChecks).map(([a,c])=>(0,b.jsxs)("div",{className:"flex items-center gap-1",children:[c?(0,b.jsx)(aT,{className:"h-3 w-3 text-green-500"}):(0,b.jsx)(aS,{className:"h-3 w-3 text-red-500"}),(0,b.jsx)("span",{className:c?"text-green-700":"text-red-700",children:a.replace(/([A-Z])/g," $1").toLowerCase()})]},a))}),(0,b.jsxs)("div",{className:"flex items-center gap-2 pt-2",children:[(0,b.jsxs)(aD,{onClick:()=>d?.(a),className:"flex-1 bg-blue-600 hover:bg-blue-700",disabled:!a.validationChecks.noExclusionFlags,children:[(0,b.jsx)(aU,{className:"h-4 w-4 mr-2"}),"Execute Trade"]}),(0,b.jsxs)(aD,{variant:"outline",onClick:()=>e?.(a.symbol),className:"flex-1",children:[(0,b.jsx)(aV,{className:"h-4 w-4 mr-2"}),"View Chart"]}),!a.entryTrigger&&(0,b.jsxs)(aD,{variant:"outline",onClick:()=>f?.(a.symbol,a.gapScan.preMarketHigh),className:"flex-1",children:[(0,b.jsx)(k,{className:"h-4 w-4 mr-2"}),"Entry Trigger"]})]}),(0,b.jsx)(aD,{variant:"ghost",onClick:()=>j(!h),className:"w-full text-sm",children:h?(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(bR,{className:"h-4 w-4 mr-2"}),"Hide Details"]}):(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(bQ,{className:"h-4 w-4 mr-2"}),"Show Details"]})}),h&&(0,b.jsxs)("div",{className:"space-y-3 pt-3 border-t",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h4",{className:"font-medium mb-2",children:"Scale-out Plan"}),(0,b.jsx)("div",{className:"space-y-1 text-sm",children:a.rewardPlanning.scaleOutPlan.map((c,d)=>(0,b.jsxs)("div",{className:"flex justify-between",children:[(0,b.jsxs)("span",{children:["At ",c.level,"R (",aA(a.riskManagement.entryPrice+a.riskManagement.riskPerShare*c.level),"):"]}),(0,b.jsxs)("span",{className:"font-medium",children:[c.percentage,"%"]})]},d))})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h4",{className:"font-medium mb-2",children:"Key Technical Levels"}),(0,b.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[(0,b.jsxs)("div",{children:["SMA200: ",aA(a.technicalGate.keyTechnicalLevels.sma200)]}),(0,b.jsxs)("div",{children:["EMA8: ",aA(a.technicalGate.keyTechnicalLevels.ema8)]}),(0,b.jsxs)("div",{children:["VWAP: ",aA(a.technicalGate.keyTechnicalLevels.vwap)]}),(0,b.jsxs)("div",{children:["Prev High: ",aA(a.technicalGate.keyTechnicalLevels.previousHigh)]})]})]}),a.exclusionReasons.length>0&&(0,b.jsxs)("div",{children:[(0,b.jsx)("h4",{className:"font-medium mb-2 text-red-600",children:"Exclusion Reasons"}),(0,b.jsx)("ul",{className:"text-sm text-red-600 space-y-1",children:a.exclusionReasons.map((a,c)=>(0,b.jsxs)("li",{className:"flex items-center gap-1",children:[(0,b.jsx)(aS,{className:"h-3 w-3"}),a]},c))})]})]})]})]})}let bT=new class{alerts=[];subscribers=[];alertSound=null;constructor(){}subscribe(a){return this.subscribers.push(a),()=>{let b=this.subscribers.indexOf(a);b>-1&&this.subscribers.splice(b,1)}}addAlert(a){let b={...a,id:this.generateAlertId(),timestamp:new Date().toISOString(),read:!1};this.alerts.unshift(b),this.alerts.length>100&&(this.alerts=this.alerts.slice(0,100)),("high"===b.priority||"critical"===b.priority)&&this.playAlertSound(),"critical"===b.priority&&this.showBrowserNotification(b),this.notifySubscribers()}markAsRead(a){let b=this.alerts.find(b=>b.id===a);b&&(b.read=!0,this.notifySubscribers())}markAllAsRead(){this.alerts.forEach(a=>a.read=!0),this.notifySubscribers()}removeAlert(a){this.alerts=this.alerts.filter(b=>b.id!==a),this.notifySubscribers()}clearAllAlerts(){this.alerts=[],this.notifySubscribers()}getAlerts(){return[...this.alerts]}getUnreadCount(){return this.alerts.filter(a=>!a.read).length}createCatalystAlert(a){let b="tier_1"===a.tier?"high":"tier_2"===a.tier?"medium":"low";this.addAlert({type:"new_catalyst",symbol:a.symbol,title:`New ${a.tier.replace("_"," ").toUpperCase()} Catalyst: ${a.symbol}`,message:a.title,priority:b,data:{catalyst:a},actionable:!0,actions:[{id:"view_details",label:"View Details",type:"view_chart",data:{symbol:a.symbol}}]})}createPerfectPickAlert(a){this.addAlert({type:"perfect_pick_found",symbol:a.symbol,title:`Perfect-Pick Setup Found: ${a.symbol}`,message:`Grade ${a.setupGrade} setup with ${a.catalyst.type.replace(/_/g," ")} catalyst`,priority:a.setupGrade.startsWith("A")?"high":"medium",data:{setup:a},actionable:!0,actions:[{id:"execute_trade",label:"Execute Trade",type:"execute_trade",data:{setup:a}},{id:"view_chart",label:"View Chart",type:"view_chart",data:{symbol:a.symbol}}]})}createGapAlert(a){let b=a.gapPercent>10?"high":a.gapPercent>5?"medium":"low";this.addAlert({type:"pre_market_gap",symbol:a.symbol,title:`Pre-Market Gap: ${a.symbol}`,message:`${a.gapPercent.toFixed(1)}% gap with ${a.catalyst?"catalyst":"no catalyst"}`,priority:b,data:{gapScan:a},actionable:a.meetsAllCriteria,actions:a.meetsAllCriteria?[{id:"analyze_setup",label:"Analyze Setup",type:"view_chart",data:{symbol:a.symbol}}]:void 0})}createPMHBreakAlert(a,b,c){this.addAlert({type:"pmh_break",symbol:a,title:`PMH Break: ${a}`,message:`Price ${b.toFixed(2)} broke above PMH ${c.toFixed(2)}`,priority:"high",data:{currentPrice:b,pmh:c},actionable:!0,actions:[{id:"execute_entry",label:"Execute Entry",type:"execute_trade",data:{symbol:a,entryPrice:b}}]})}createStopLossAlert(a,b,c){this.addAlert({type:"stop_loss_hit",symbol:a,title:`Stop Loss Hit: ${a}`,message:`Price ${b.toFixed(2)} hit stop loss ${c.toFixed(2)}`,priority:"critical",data:{currentPrice:b,stopLoss:c},actionable:!0,actions:[{id:"exit_position",label:"Exit Position",type:"execute_trade",data:{symbol:a,action:"sell",price:b}}]})}createProfitTargetAlert(a,b,c,d){this.addAlert({type:"profit_target_hit",symbol:a,title:`Profit Target Hit: ${a}`,message:`Price ${b.toFixed(2)} hit ${d}R target ${c.toFixed(2)}`,priority:"high",data:{currentPrice:b,target:c,rLevel:d},actionable:!0,actions:[{id:"take_profit",label:"Take Profit",type:"take_profit",data:{symbol:a,price:b,rLevel:d}}]})}createVolumeSpikeAlert(a,b,c){let d=b/c;this.addAlert({type:"volume_spike",symbol:a,title:`Volume Spike: ${a}`,message:`Volume ${d.toFixed(1)}x above average`,priority:d>5?"high":"medium",data:{currentVolume:b,avgVolume:c,volumeRatio:d},actionable:!1})}monitorPerfectPickSetups(a){a.forEach(a=>{!this.alerts.find(b=>"perfect_pick_found"===b.type&&b.symbol===a.symbol&&b.data?.setup?.createdAt===a.createdAt)&&a.overallScore>=80&&this.createPerfectPickAlert(a)})}monitorGapScans(a){a.forEach(a=>{!this.alerts.find(b=>"pre_market_gap"===b.type&&b.symbol===a.symbol&&6e4>Math.abs(new Date(b.timestamp).getTime()-new Date(a.scanTime).getTime()))&&a.gapPercent>=3&&this.createGapAlert(a)})}generateAlertId(){return`alert_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}notifySubscribers(){this.subscribers.forEach(a=>a([...this.alerts]))}playAlertSound(){this.alertSound&&this.alertSound.play().catch(a=>{console.log("Could not play alert sound:",a)})}showBrowserNotification(a){"Notification"in window&&"granted"===Notification.permission?new Notification(a.title,{body:a.message,icon:"/favicon.ico",tag:a.symbol}):"Notification"in window&&"denied"!==Notification.permission&&Notification.requestPermission().then(b=>{"granted"===b&&new Notification(a.title,{body:a.message,icon:"/favicon.ico",tag:a.symbol})})}};function bU({accountSize:a=1e5,riskPercent:d=2}){let[e,f]=(0,c.useState)([]),[g,h]=(0,c.useState)([]),[j,l]=(0,c.useState)([]),[m,o]=(0,c.useState)(!1),[q,r]=(0,c.useState)("perfect-pick"),[s,t]=(0,c.useState)(""),[u,v]=(0,c.useState)(null);(0,c.useEffect)(()=>{let a=setInterval(()=>{let a=new Date().getHours();a>=4&&a<21&&z()},9e5);return()=>clearInterval(a)},[]);let w=async()=>{o(!0);try{let b=await fetch(`/api/scanner/perfect-pick?accountSize=${a}&riskPercent=${d}&limit=20`),c=await b.json();c.success&&(f(c.data.setups),v(c.data.summary),t(new Date().toLocaleTimeString()),bT.monitorPerfectPickSetups(c.data.setups))}catch(a){console.error("Error running Perfect-Pick scan:",a)}finally{o(!1)}},x=async()=>{o(!0);try{let a=await fetch("/api/scanner/gap-scan?minGap=3&maxGap=15&limit=30"),b=await a.json();b.success&&(h(b.data.results),t(new Date().toLocaleTimeString()),bT.monitorGapScans(b.data.results))}catch(a){console.error("Error running gap scan:",a)}finally{o(!1)}},y=async()=>{o(!0);try{let a=g.slice(0,10).map(a=>a.symbol).join(","),b=await fetch(`/api/catalyst/detect?symbols=${a}&minQuality=6&limit=50`),c=await b.json();c.success&&(l(c.data.catalysts),t(new Date().toLocaleTimeString()),c.data.catalysts.filter(a=>a.qualityScore>=7).forEach(a=>bT.createCatalystAlert(a)))}catch(a){console.error("Error running catalyst scan:",a)}finally{o(!1)}},z=()=>{switch(q){case"perfect-pick":w();break;case"gap-scan":x();break;case"catalysts":y()}},A=a=>"tier_1"===a.tier?"bg-green-500":"tier_2"===a.tier?"bg-yellow-500":"bg-gray-500";return(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h2",{className:"text-2xl font-bold",children:"Event-Driven Scanner"}),(0,b.jsx)("p",{className:"text-muted-foreground",children:"Perfect-Pick Trading System with Catalyst Detection"})]}),(0,b.jsxs)("div",{className:"flex items-center gap-2",children:[s&&(0,b.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Last scan: ",s]}),(0,b.jsxs)(aD,{onClick:z,disabled:m,variant:"outline",size:"sm",children:[m?(0,b.jsx)(p,{className:"h-4 w-4 animate-spin"}):(0,b.jsx)(bO,{className:"h-4 w-4"}),"Refresh"]})]})]}),u&&(0,b.jsxs)(aE,{children:[(0,b.jsx)(aF,{children:(0,b.jsxs)(aG,{className:"flex items-center gap-2",children:[(0,b.jsx)(bN,{className:"h-5 w-5"}),"Scan Summary"]})}),(0,b.jsx)(aI,{children:(0,b.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:u.totalSetups||0}),(0,b.jsx)("div",{className:"text-sm text-muted-foreground",children:"Total Setups"})]}),(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("div",{className:"text-2xl font-bold text-green-600",children:u.avgScore||0}),(0,b.jsx)("div",{className:"text-sm text-muted-foreground",children:"Avg Score"})]}),(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:aB(u.avgGap||0)}),(0,b.jsx)("div",{className:"text-sm text-muted-foreground",children:"Avg Gap"})]}),(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:Object.keys(u.catalystBreakdown||{}).length}),(0,b.jsx)("div",{className:"text-sm text-muted-foreground",children:"Catalyst Types"})]})]})})]}),(0,b.jsxs)(bB,{value:q,onValueChange:r,children:[(0,b.jsxs)(bK,{className:"grid w-full grid-cols-3",children:[(0,b.jsxs)(bL,{value:"perfect-pick",className:"flex items-center gap-2",children:[(0,b.jsx)(bP,{className:"h-4 w-4"}),"Perfect-Pick"]}),(0,b.jsxs)(bL,{value:"gap-scan",className:"flex items-center gap-2",children:[(0,b.jsx)(i,{className:"h-4 w-4"}),"Gap Scanner"]}),(0,b.jsxs)(bL,{value:"catalysts",className:"flex items-center gap-2",children:[(0,b.jsx)(n,{className:"h-4 w-4"}),"Catalysts"]})]}),(0,b.jsxs)(bM,{value:"perfect-pick",className:"space-y-4",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsx)("h3",{className:"text-lg font-semibold",children:"Perfect-Pick Setups"}),(0,b.jsxs)(aD,{onClick:w,disabled:m,children:[m?(0,b.jsx)(p,{className:"h-4 w-4 animate-spin mr-2"}):(0,b.jsx)(k,{className:"h-4 w-4 mr-2"}),"Scan Perfect-Picks"]})]}),(0,b.jsx)("div",{className:"space-y-6",children:e.map(a=>(0,b.jsx)(bS,{setup:a,onExecuteTrade:a=>{console.log("Execute trade for:",a.symbol)},onViewChart:a=>{console.log("View chart for:",a)},onGenerateEntryTrigger:async(a,b)=>{console.log("Generate entry trigger for:",a);try{let c=await fetch("/api/scanner/perfect-pick",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"generate_entry_trigger",data:{symbol:a,preMarketHigh:b}})}),d=await c.json();d.success&&console.log("Entry trigger generated:",d.data.entryTrigger)}catch(a){console.error("Error generating entry trigger:",a)}}},a.symbol))}),0===e.length&&!m&&(0,b.jsx)(aE,{children:(0,b.jsxs)(aI,{className:"text-center py-8",children:[(0,b.jsx)(k,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),(0,b.jsx)("p",{className:"text-muted-foreground",children:'No Perfect-Pick setups found. Click "Scan Perfect-Picks" to search for opportunities.'})]})})]}),(0,b.jsxs)(bM,{value:"gap-scan",className:"space-y-4",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsx)("h3",{className:"text-lg font-semibold",children:"Pre-Market Gap Scanner"}),(0,b.jsxs)(aD,{onClick:x,disabled:m,children:[m?(0,b.jsx)(p,{className:"h-4 w-4 animate-spin mr-2"}):(0,b.jsx)(i,{className:"h-4 w-4 mr-2"}),"Scan Gaps"]})]}),(0,b.jsx)("div",{className:"grid gap-3",children:g.map(a=>(0,b.jsx)(aE,{className:"hover:shadow-md transition-shadow",children:(0,b.jsxs)(aI,{className:"p-4",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{className:"flex items-center gap-3",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("div",{className:"font-semibold",children:a.symbol}),(0,b.jsx)("div",{className:"text-sm text-muted-foreground",children:a.name})]}),a.catalyst&&(0,b.jsx)(aK,{className:A(a.catalyst),children:a.catalyst.type.replace(/_/g," ")}),a.meetsAllCriteria&&(0,b.jsx)(aK,{className:"bg-green-500",children:"Perfect-Pick"})]}),(0,b.jsxs)("div",{className:"text-right",children:[(0,b.jsx)("div",{className:"font-bold",children:aA(a.price)}),(0,b.jsxs)("div",{className:"text-sm text-green-600",children:["+",aB(a.gapPercent)]})]})]}),(0,b.jsxs)("div",{className:"mt-3 grid grid-cols-3 gap-4 text-xs text-muted-foreground",children:[(0,b.jsxs)("div",{children:["Vol: ",(a.preMarketVolume/1e3).toFixed(0),"K"]}),(0,b.jsxs)("div",{children:["MCap: $",(a.marketCap/1e9).toFixed(1),"B"]}),(0,b.jsxs)("div",{children:["Sector: ",a.sector]})]})]})},a.symbol))}),0===g.length&&!m&&(0,b.jsx)(aE,{children:(0,b.jsxs)(aI,{className:"text-center py-8",children:[(0,b.jsx)(i,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),(0,b.jsx)("p",{className:"text-muted-foreground",children:'No gap opportunities found. Click "Scan Gaps" to search for pre-market movers.'})]})})]}),(0,b.jsxs)(bM,{value:"catalysts",className:"space-y-4",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsx)("h3",{className:"text-lg font-semibold",children:"Real-Time Catalysts"}),(0,b.jsxs)(aD,{onClick:y,disabled:m,children:[m?(0,b.jsx)(p,{className:"h-4 w-4 animate-spin mr-2"}):(0,b.jsx)(n,{className:"h-4 w-4 mr-2"}),"Scan Catalysts"]})]}),(0,b.jsx)("div",{className:"grid gap-3",children:j.map(a=>(0,b.jsx)(aE,{className:"hover:shadow-md transition-shadow",children:(0,b.jsx)(aI,{className:"p-4",children:(0,b.jsx)("div",{className:"flex items-start justify-between",children:(0,b.jsxs)("div",{className:"flex-1",children:[(0,b.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,b.jsx)("span",{className:"font-semibold",children:a.symbol}),(a=>{switch(a){case"bullish":return(0,b.jsx)(i,{className:"h-4 w-4 text-green-500"});case"bearish":return(0,b.jsx)(aS,{className:"h-4 w-4 text-red-500"});default:return(0,b.jsx)(bN,{className:"h-4 w-4 text-gray-500"})}})(a.impact),(0,b.jsx)(aK,{className:A(a),children:a.tier.replace("_"," ").toUpperCase()}),(0,b.jsx)(aK,{variant:"outline",className:"text-xs",children:a.freshness})]}),(0,b.jsx)("h4",{className:"font-medium mb-1",children:a.title}),(0,b.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:a.description}),(0,b.jsxs)("div",{className:"flex items-center gap-4 text-xs text-muted-foreground",children:[(0,b.jsxs)("span",{children:["Quality: ",a.qualityScore,"/10"]}),(0,b.jsxs)("span",{children:["Source: ",a.source]}),(0,b.jsxs)("span",{className:"flex items-center gap-1",children:[(0,b.jsx)(aQ,{className:"h-3 w-3"}),new Date(a.announcementTime).toLocaleString()]})]})]})})})},a.id))}),0===j.length&&!m&&(0,b.jsx)(aE,{children:(0,b.jsxs)(aI,{className:"text-center py-8",children:[(0,b.jsx)(n,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),(0,b.jsx)("p",{className:"text-muted-foreground",children:'No catalysts found. Click "Scan Catalysts" to detect market-moving events.'})]})})]})]})]})}let bV=h("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]),bW=h("bell-ring",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M22 8c0-2.3-.8-4.3-2-6",key:"5bb3ad"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}],["path",{d:"M4 2C2.8 3.7 2 5.7 2 8",key:"tap9e0"}]]),bX=h("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function bY({className:a}){let[d,e]=(0,c.useState)([]),[f,g]=(0,c.useState)(!1),[h,j]=(0,c.useState)(0);(0,c.useEffect)(()=>{let a=bT.subscribe(a=>{e(a),j(bT.getUnreadCount())});return e(bT.getAlerts()),j(bT.getUnreadCount()),a},[]);let l=async(a,b)=>{let c=a.actions?.find(a=>a.id===b);if(c){switch(c.type){case"execute_trade":console.log("Execute trade:",c.data);break;case"view_chart":console.log("View chart:",c.data);break;case"take_profit":console.log("Take profit:",c.data);break;case"update_stop":console.log("Update stop:",c.data);break;case"dismiss":bT.removeAlert(a.id)}bT.markAsRead(a.id)}};return(0,b.jsxs)("div",{className:`relative ${a}`,children:[(0,b.jsxs)(aD,{variant:"outline",size:"sm",onClick:()=>g(!f),className:"relative",children:[h>0?(0,b.jsx)(bW,{className:"h-4 w-4"}):(0,b.jsx)(bV,{className:"h-4 w-4"}),h>0&&(0,b.jsx)(aK,{className:"absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs bg-red-500",children:h>99?"99+":h})]}),f&&(0,b.jsxs)("div",{className:"absolute right-0 top-full mt-2 w-96 max-h-96 overflow-y-auto bg-white border rounded-lg shadow-lg z-50",children:[(0,b.jsx)("div",{className:"p-4 border-b",children:(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsx)("h3",{className:"font-semibold",children:"Alerts"}),(0,b.jsxs)("div",{className:"flex items-center gap-2",children:[h>0&&(0,b.jsx)(aD,{variant:"ghost",size:"sm",onClick:()=>bT.markAllAsRead(),children:"Mark all read"}),(0,b.jsx)(aD,{variant:"ghost",size:"sm",onClick:()=>g(!1),children:(0,b.jsx)(bX,{className:"h-4 w-4"})})]})]})}),(0,b.jsx)("div",{className:"max-h-80 overflow-y-auto",children:0===d.length?(0,b.jsxs)("div",{className:"p-8 text-center text-muted-foreground",children:[(0,b.jsx)(bV,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,b.jsx)("p",{children:"No alerts yet"})]}):d.map(a=>(0,b.jsx)("div",{className:`p-4 border-b hover:bg-gray-50 ${!a.read?"bg-blue-50/50":""}`,onClick:()=>!a.read&&bT.markAsRead(a.id),children:(0,b.jsxs)("div",{className:"flex items-start gap-3",children:[(0,b.jsx)("div",{className:`p-2 rounded-full ${(a=>{switch(a){case"critical":return"text-red-600 bg-red-50 border-red-200";case"high":return"text-orange-600 bg-orange-50 border-orange-200";case"medium":return"text-yellow-600 bg-yellow-50 border-yellow-200";case"low":return"text-blue-600 bg-blue-50 border-blue-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}})(a.priority)}`,children:(a=>{switch(a){case"new_catalyst":return(0,b.jsx)(n,{className:"h-4 w-4"});case"perfect_pick_found":return(0,b.jsx)(k,{className:"h-4 w-4"});case"pre_market_gap":return(0,b.jsx)(i,{className:"h-4 w-4"});case"pmh_break":case"volume_spike":return(0,b.jsx)(bN,{className:"h-4 w-4"});case"stop_loss_hit":return(0,b.jsx)(aS,{className:"h-4 w-4"});case"profit_target_hit":return(0,b.jsx)(aR,{className:"h-4 w-4"});case"entry_trigger":return(0,b.jsx)(aU,{className:"h-4 w-4"});default:return(0,b.jsx)(bV,{className:"h-4 w-4"})}})(a.type)}),(0,b.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,b.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,b.jsx)("h4",{className:"font-medium text-sm truncate",children:a.title}),!a.read&&(0,b.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"})]}),(0,b.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:a.message}),(0,b.jsx)("div",{className:"flex items-center justify-between",children:(0,b.jsxs)("div",{className:"flex items-center gap-2 text-xs text-muted-foreground",children:[(0,b.jsx)(aQ,{className:"h-3 w-3"}),(a=>{let b=new Date,c=new Date(a),d=Math.floor((b.getTime()-c.getTime())/6e4),e=Math.floor(d/60);return d<1?"Just now":d<60?`${d}m ago`:e<24?`${e}h ago`:c.toLocaleDateString()})(a.timestamp),(0,b.jsx)(aK,{variant:"outline",className:"text-xs",children:a.priority})]})}),a.actionable&&a.actions&&a.actions.length>0&&(0,b.jsx)("div",{className:"flex items-center gap-2 mt-2",children:a.actions.map(c=>(0,b.jsxs)(aD,{variant:"outline",size:"sm",onClick:b=>{b.stopPropagation(),l(a,c.id)},className:"text-xs",children:["execute_trade"===c.type&&(0,b.jsx)(aU,{className:"h-3 w-3 mr-1"}),"view_chart"===c.type&&(0,b.jsx)(aV,{className:"h-3 w-3 mr-1"}),"take_profit"===c.type&&(0,b.jsx)(aR,{className:"h-3 w-3 mr-1"}),c.label]},c.id))})]})]})},a.id))}),d.length>0&&(0,b.jsx)("div",{className:"p-4 border-t",children:(0,b.jsx)(aD,{variant:"ghost",size:"sm",onClick:()=>{bT.clearAllAlerts(),g(!1)},className:"w-full text-sm",children:"Clear all alerts"})})]})]})}let bZ=h("settings",[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),b$=h("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),b_=h("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);function b0(){let[a,d]=(0,c.useState)(null),[e,f]=(0,c.useState)(!1),[g,h]=(0,c.useState)(null);(0,c.useEffect)(()=>{i()},[]);let i=async()=>{f(!0),h(null);try{let a=await fetch("/api/ai?action=status"),b=await a.json();d(b)}catch(a){console.error("Error checking AI status:",a),h("Unable to connect to AI service")}finally{f(!1)}},j=async()=>{f(!0),h(null);try{let a=await fetch("/api/ai?action=model");if(a.ok){let{model:b}=await a.json();d(a=>a?{...a,model:b}:null),alert(`AI Connection Successful!
Using model: ${b}`)}else throw Error("Failed to connect to AI service")}catch(a){console.error("Error testing AI connection:",a),h("AI connection test failed")}finally{f(!1)}};return e&&!a?(0,b.jsx)(aE,{className:"bg-slate-800/50 border-slate-700",children:(0,b.jsx)(aI,{className:"p-6",children:(0,b.jsxs)("div",{className:"flex items-center justify-center",children:[(0,b.jsx)(p,{className:"h-6 w-6 animate-spin text-blue-400 mr-2"}),(0,b.jsx)("span",{className:"text-slate-300",children:"Checking AI configuration..."})]})})}):(0,b.jsx)("div",{className:"space-y-6",children:(0,b.jsxs)(aE,{className:"bg-slate-800/50 border-slate-700",children:[(0,b.jsx)(aF,{children:(0,b.jsxs)(aG,{className:"text-white flex items-center justify-between",children:[(0,b.jsxs)("div",{className:"flex items-center",children:[(0,b.jsx)(m,{className:"mr-2 h-5 w-5 text-blue-400"}),"AI Configuration"]}),(0,b.jsxs)(aD,{size:"sm",variant:"outline",onClick:i,disabled:e,className:"text-slate-300 hover:text-white",children:[e?(0,b.jsx)(p,{className:"h-4 w-4 animate-spin mr-1"}):(0,b.jsx)(bZ,{className:"h-4 w-4 mr-1"}),"Refresh Status"]})]})}),(0,b.jsxs)(aI,{children:[g&&(0,b.jsx)("div",{className:"mb-4 p-3 bg-red-900/20 border border-red-500/30 rounded-lg",children:(0,b.jsxs)("div",{className:"flex items-center text-red-400",children:[(0,b.jsx)(aS,{className:"h-4 w-4 mr-2"}),g]})}),a&&(0,b.jsxs)("div",{className:"space-y-4",children:[(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,b.jsxs)("div",{className:"text-center p-4 bg-slate-700/30 rounded-lg",children:[(0,b.jsx)("div",{className:"flex items-center justify-center mb-2",children:a.enabled?(0,b.jsx)(aT,{className:"h-6 w-6 text-green-400"}):(0,b.jsx)(b$,{className:"h-6 w-6 text-red-400"})}),(0,b.jsx)("div",{className:"text-sm text-slate-400",children:"Status"}),(0,b.jsx)("div",{className:`font-semibold ${a.enabled?"text-green-400":"text-red-400"}`,children:a.enabled?"Enabled":"Disabled"})]}),(0,b.jsxs)("div",{className:"text-center p-4 bg-slate-700/30 rounded-lg",children:[(0,b.jsx)("div",{className:"flex items-center justify-center mb-2",children:(0,b.jsx)(aW,{className:"h-6 w-6 text-blue-400"})}),(0,b.jsx)("div",{className:"text-sm text-slate-400",children:"Model"}),(0,b.jsx)("div",{className:"font-semibold text-white",children:a.model||"Not Available"})]}),(0,b.jsx)("div",{className:"text-center p-4 bg-slate-700/30 rounded-lg",children:(0,b.jsxs)(aD,{onClick:j,disabled:!a.enabled||e,className:"w-full bg-blue-600 hover:bg-blue-700",children:[e?(0,b.jsx)(p,{className:"h-4 w-4 animate-spin mr-2"}):(0,b.jsx)(aT,{className:"h-4 w-4 mr-2"}),"Test Connection"]})})]}),(0,b.jsxs)("div",{children:[(0,b.jsxs)("h3",{className:"text-lg font-semibold text-white mb-3 flex items-center",children:[(0,b.jsx)(bZ,{className:"h-5 w-5 mr-2"}),"Available Features"]}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,b.jsxs)("div",{className:"p-3 bg-slate-700/20 rounded-lg",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,b.jsx)("span",{className:"text-white font-medium",children:"Market Commentary"}),(0,b.jsx)(aK,{className:a.features.marketCommentary&&a.enabled?"bg-green-500/20 text-green-400":"bg-gray-500/20 text-gray-400",children:a.features.marketCommentary&&a.enabled?"Active":"Inactive"})]}),(0,b.jsx)("p",{className:"text-xs text-slate-400",children:"AI-powered market analysis and commentary on scan results"})]}),(0,b.jsxs)("div",{className:"p-3 bg-slate-700/20 rounded-lg",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,b.jsx)("span",{className:"text-white font-medium",children:"Risk Assessment"}),(0,b.jsx)(aK,{className:a.features.riskAssessment&&a.enabled?"bg-green-500/20 text-green-400":"bg-gray-500/20 text-gray-400",children:a.features.riskAssessment&&a.enabled?"Active":"Inactive"})]}),(0,b.jsx)("p",{className:"text-xs text-slate-400",children:"Individual setup risk analysis with AI-generated recommendations"})]}),(0,b.jsxs)("div",{className:"p-3 bg-slate-700/20 rounded-lg",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,b.jsx)("span",{className:"text-white font-medium",children:"Trading Recommendations"}),(0,b.jsx)(aK,{className:a.features.tradingRecommendations&&a.enabled?"bg-green-500/20 text-green-400":"bg-gray-500/20 text-gray-400",children:a.features.tradingRecommendations&&a.enabled?"Active":"Inactive"})]}),(0,b.jsx)("p",{className:"text-xs text-slate-400",children:"Personalized trading recommendations based on your preferences"})]})]})]}),!a.enabled&&(0,b.jsxs)("div",{className:"p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg",children:[(0,b.jsxs)("h4",{className:"text-white font-semibold mb-2 flex items-center",children:[(0,b.jsx)(b_,{className:"h-4 w-4 mr-2"}),"Enable AI Features"]}),(0,b.jsxs)("div",{className:"text-sm text-slate-300 space-y-2",children:[(0,b.jsx)("p",{children:"To enable AI-powered insights:"}),(0,b.jsxs)("ol",{className:"list-decimal list-inside space-y-1 ml-4",children:[(0,b.jsx)("li",{children:"Ensure OPENAI_API_KEY is set in your .env.local file"}),(0,b.jsx)("li",{children:"Set OPENAI_ENABLED=true in your environment variables"}),(0,b.jsx)("li",{children:"Restart the application"}),(0,b.jsx)("li",{children:'Click "Refresh Status" to verify the configuration'})]})]})]}),a.enabled&&(0,b.jsxs)("div",{className:"p-4 bg-green-900/20 border border-green-500/30 rounded-lg",children:[(0,b.jsxs)("h4",{className:"text-green-400 font-semibold mb-2 flex items-center",children:[(0,b.jsx)(aT,{className:"h-4 w-4 mr-2"}),"AI Features Active"]}),(0,b.jsx)("p",{className:"text-sm text-slate-300",children:"Your SwingTrader AI is enhanced with GPT-4o intelligence. You'll see AI-powered insights in your trading setup cards and market analysis sections."})]})]})]})]})})}function b1(){let[a,d]=(0,c.useState)("SPY"),[e,f]=(0,c.useState)(""),[g,h]=(0,c.useState)(!1),[r,s]=(0,c.useState)(null),[t,u]=(0,c.useState)(null),[v,w]=(0,c.useState)(null),[x,y]=(0,c.useState)("event-driven"),z=async a=>{h(!0),w(null),s(null),u(null);try{let[b,c]=await Promise.all([fetch(`/api/stocks/quote/${a}`),fetch(`/api/analysis/swing/${a}`)]);if(!b.ok||!c.ok)throw Error("Failed to fetch data");let[d,e]=await Promise.all([b.json(),c.json()]);u(d),s(e)}catch(a){w("Failed to analyze stock. Please try again."),console.error("Analysis error:",a)}finally{h(!1)}};return(0,b.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900",children:[(0,b.jsx)("header",{className:"border-b border-slate-800 bg-slate-900/50 backdrop-blur-sm",children:(0,b.jsx)("div",{className:"container mx-auto px-4 py-4",children:(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,b.jsx)(m,{className:"h-8 w-8 text-blue-400"}),(0,b.jsx)("h1",{className:"text-2xl font-bold text-white",children:"SwingTrader AI"})]}),(0,b.jsxs)("nav",{className:"hidden md:flex items-center space-x-6",children:[(0,b.jsx)("button",{onClick:()=>y("strategies"),className:`transition-colors ${"strategies"===x?"text-white":"text-slate-300 hover:text-white"}`,children:"Pro Strategies"}),(0,b.jsx)("button",{onClick:()=>y("scanner"),className:`transition-colors ${"scanner"===x?"text-white":"text-slate-300 hover:text-white"}`,children:"Basic Scanner"}),(0,b.jsx)("button",{onClick:()=>y("individual"),className:`transition-colors ${"individual"===x?"text-white":"text-slate-300 hover:text-white"}`,children:"Individual Analysis"}),(0,b.jsx)("button",{onClick:()=>y("trading"),className:`transition-colors ${"trading"===x?"text-white":"text-slate-300 hover:text-white"}`,children:"Paper Trading"}),(0,b.jsx)(bY,{}),(0,b.jsx)(aD,{variant:"outline",className:"border-blue-400 text-blue-400 hover:bg-blue-400 hover:text-white",children:"Sign In"})]})]})})}),(0,b.jsx)("section",{className:"py-20 px-4",children:(0,b.jsxs)("div",{className:"container mx-auto text-center",children:[(0,b.jsx)("h2",{className:"text-5xl font-bold text-white mb-6",children:"AI-Powered Swing Trading Analysis"}),(0,b.jsx)("p",{className:"text-xl text-slate-300 mb-8 max-w-3xl mx-auto",children:"Professional swing trading strategies with automated scanning, precise entry/exit rules, and risk management based on proven methodologies."}),(0,b.jsx)("div",{className:"flex justify-center mb-8",children:(0,b.jsxs)("div",{className:"bg-slate-800/50 rounded-lg p-1 flex",children:[(0,b.jsxs)("button",{onClick:()=>y("event-driven"),className:`px-6 py-3 rounded-md transition-all ${"event-driven"===x?"bg-blue-600 text-white":"text-slate-300 hover:text-white hover:bg-slate-700/50"}`,children:[(0,b.jsx)(k,{className:"inline mr-2 h-4 w-4"}),"Event-Driven"]}),(0,b.jsxs)("button",{onClick:()=>y("strategies"),className:`px-6 py-3 rounded-md transition-all ${"strategies"===x?"bg-blue-600 text-white":"text-slate-300 hover:text-white hover:bg-slate-700/50"}`,children:[(0,b.jsx)(n,{className:"inline mr-2 h-4 w-4"}),"Pro Strategies"]}),(0,b.jsxs)("button",{onClick:()=>y("scanner"),className:`px-6 py-3 rounded-md transition-all ${"scanner"===x?"bg-blue-600 text-white":"text-slate-300 hover:text-white hover:bg-slate-700/50"}`,children:[(0,b.jsx)(q,{className:"inline mr-2 h-4 w-4"}),"Basic Scanner"]}),(0,b.jsxs)("button",{onClick:()=>y("individual"),className:`px-6 py-3 rounded-md transition-all ${"individual"===x?"bg-blue-600 text-white":"text-slate-300 hover:text-white hover:bg-slate-700/50"}`,children:[(0,b.jsx)(o,{className:"inline mr-2 h-4 w-4"}),"Individual Analysis"]}),(0,b.jsxs)("button",{onClick:()=>y("trading"),className:`px-6 py-3 rounded-md transition-all ${"trading"===x?"bg-blue-600 text-white":"text-slate-300 hover:text-white hover:bg-slate-700/50"}`,children:[(0,b.jsx)(i,{className:"inline mr-2 h-4 w-4"}),"Trading"]}),(0,b.jsxs)("button",{onClick:()=>y("ai"),className:`px-6 py-3 rounded-md transition-all ${"ai"===x?"bg-blue-600 text-white":"text-slate-300 hover:text-white hover:bg-slate-700/50"}`,children:[(0,b.jsx)(m,{className:"inline mr-2 h-4 w-4"}),"AI Config"]})]})}),"event-driven"===x?(0,b.jsxs)("div",{className:"mb-8",children:[(0,b.jsx)("h3",{className:"text-lg text-slate-300 mb-4",children:"Event-Driven Catalyst Detection & Perfect-Pick Trading"}),(0,b.jsx)("p",{className:"text-slate-400 mb-6",children:"Advanced catalyst detection with pre-market gap scanning, technical gate analysis, and Perfect-Pick setups"})]}):"strategies"===x?(0,b.jsxs)("div",{className:"mb-8",children:[(0,b.jsx)("h3",{className:"text-lg text-slate-300 mb-4",children:"Professional Swing Trading Strategies"}),(0,b.jsx)("p",{className:"text-slate-400 mb-6",children:"Overnight Momentum & Technical Breakout strategies with precise entry/exit rules and position sizing"})]}):"scanner"===x?(0,b.jsxs)("div",{className:"mb-8",children:[(0,b.jsx)("h3",{className:"text-lg text-slate-300 mb-4",children:"Basic Swing Trading Scanner"}),(0,b.jsx)("p",{className:"text-slate-400 mb-6",children:"General swing trading analysis with technical indicators and trend detection"})]}):(0,b.jsxs)("div",{className:"mb-8",children:[(0,b.jsx)("h3",{className:"text-lg text-slate-300 mb-4",children:"Individual Stock Analysis"}),(0,b.jsx)("div",{className:"flex flex-wrap justify-center gap-2 mb-6",children:["SPY","QQQ","AAPL","TSLA","NVDA","MSFT","AMZN","GOOGL"].map(c=>(0,b.jsx)(aD,{variant:a===c?"default":"outline",onClick:()=>{d(c),z(c)},disabled:g,className:a===c?"bg-blue-600 hover:bg-blue-700":"border-slate-600 text-slate-300 hover:bg-slate-800",children:c},c))}),(0,b.jsxs)("form",{onSubmit:a=>{a.preventDefault(),e.trim()&&(z(e.toUpperCase()),d(e.toUpperCase()))},className:"flex justify-center gap-2 mb-6",children:[(0,b.jsxs)("div",{className:"relative",children:[(0,b.jsx)(o,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,b.jsx)("input",{type:"text",placeholder:"Enter symbol (e.g., AAPL)",value:e,onChange:a=>f(a.target.value),className:"pl-10 pr-4 py-2 bg-slate-800 border border-slate-600 rounded-md text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500",disabled:g})]}),(0,b.jsx)(aD,{type:"submit",disabled:g||!e.trim(),className:"bg-blue-600 hover:bg-blue-700",children:"Analyze"})]}),(0,b.jsx)(aD,{size:"lg",onClick:()=>z(a),disabled:g,className:"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3",children:g?(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(p,{className:"mr-2 h-5 w-5 animate-spin"}),"Analyzing ",a,"..."]}):(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(n,{className:"mr-2 h-5 w-5"}),"Get AI Analysis for ",a]})})]})]})}),"event-driven"===x&&(0,b.jsx)("section",{className:"py-12 px-4",children:(0,b.jsx)("div",{className:"container mx-auto",children:(0,b.jsx)(bU,{accountSize:1e5,riskPercent:2})})}),"strategies"===x&&(0,b.jsx)("section",{className:"py-12 px-4",children:(0,b.jsx)("div",{className:"container mx-auto",children:(0,b.jsx)(a$,{autoScan:!0,accountSize:1e5})})}),"scanner"===x&&(0,b.jsx)("section",{className:"py-12 px-4",children:(0,b.jsx)("div",{className:"container mx-auto",children:(0,b.jsx)(aN,{autoScan:!1})})}),"trading"===x&&(0,b.jsx)("section",{className:"py-12 px-4",children:(0,b.jsxs)("div",{className:"container mx-auto",children:[(0,b.jsxs)("div",{className:"mb-8",children:[(0,b.jsx)("h2",{className:"text-3xl font-bold text-white mb-4",children:"Paper Trading"}),(0,b.jsx)("p",{className:"text-slate-400 mb-6",children:"Execute swing trades with Alpaca or Interactive Brokers paper trading accounts"})]}),(0,b.jsxs)("div",{className:"text-center text-slate-400",children:[(0,b.jsx)("h3",{className:"text-xl mb-4",children:"Paper Trading Interface"}),(0,b.jsx)("p",{children:"IBKR and Alpaca integration is ready!"}),(0,b.jsx)("p",{className:"mt-2",children:"Trading interface temporarily disabled due to component loading issue."}),(0,b.jsxs)("p",{className:"mt-2",children:["API endpoints are working: ",(0,b.jsx)("code",{children:"/api/trading"})]})]})]})}),"ai"===x&&(0,b.jsx)("section",{className:"py-12 px-4",children:(0,b.jsxs)("div",{className:"container mx-auto",children:[(0,b.jsxs)("div",{className:"mb-8",children:[(0,b.jsx)("h2",{className:"text-3xl font-bold text-white mb-4",children:"AI Configuration"}),(0,b.jsx)("p",{className:"text-slate-400 mb-6",children:"Configure and manage AI-powered features for enhanced trading analysis"})]}),(0,b.jsx)(b0,{})]})}),v&&(0,b.jsx)("section",{className:"py-8 px-4",children:(0,b.jsx)("div",{className:"container mx-auto",children:(0,b.jsx)(aE,{className:"bg-red-900/20 border-red-500/50",children:(0,b.jsx)(aI,{className:"p-6",children:(0,b.jsx)("p",{className:"text-red-300 text-center",children:v})})})})}),"individual"===x&&(t||r)&&(0,b.jsx)("section",{className:"py-12 px-4",children:(0,b.jsxs)("div",{className:"container mx-auto",children:[(0,b.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[t&&(0,b.jsxs)(aE,{className:"bg-slate-800/50 border-slate-700",children:[(0,b.jsx)(aF,{children:(0,b.jsxs)(aG,{className:"text-white flex items-center",children:[(0,b.jsx)(j,{className:"mr-2 h-5 w-5 text-blue-400"}),t.symbol," Quote"]})}),(0,b.jsx)(aI,{children:(0,b.jsxs)("div",{className:"space-y-3",children:[(0,b.jsxs)("div",{className:"flex justify-between",children:[(0,b.jsx)("span",{className:"text-slate-300",children:"Price:"}),(0,b.jsx)("span",{className:"text-white font-semibold",children:aA(t.price)})]}),(0,b.jsxs)("div",{className:"flex justify-between",children:[(0,b.jsx)("span",{className:"text-slate-300",children:"Change:"}),(0,b.jsxs)("span",{className:t.change>=0?"text-green-400":"text-red-400",children:[aA(t.change)," (",aB(t.changePercent),")"]})]}),(0,b.jsxs)("div",{className:"flex justify-between",children:[(0,b.jsx)("span",{className:"text-slate-300",children:"Volume:"}),(0,b.jsx)("span",{className:"text-white",children:t.volume.toLocaleString()})]})]})})]}),r&&(0,b.jsxs)(aE,{className:"bg-slate-800/50 border-slate-700",children:[(0,b.jsx)(aF,{children:(0,b.jsxs)(aG,{className:"text-white flex items-center",children:[(0,b.jsx)(k,{className:"mr-2 h-5 w-5 text-green-400"}),"Trading Levels"]})}),(0,b.jsx)(aI,{children:(0,b.jsxs)("div",{className:"space-y-3",children:[(0,b.jsxs)("div",{className:"flex justify-between",children:[(0,b.jsx)("span",{className:"text-slate-300",children:"Entry:"}),(0,b.jsx)("span",{className:"text-white font-semibold",children:aA(r.entryPrice)})]}),(0,b.jsxs)("div",{className:"flex justify-between",children:[(0,b.jsx)("span",{className:"text-slate-300",children:"Stop Loss:"}),(0,b.jsx)("span",{className:"text-red-400",children:aA(r.stopLoss)})]}),(0,b.jsxs)("div",{className:"flex justify-between",children:[(0,b.jsx)("span",{className:"text-slate-300",children:"Take Profit:"}),(0,b.jsx)("span",{className:"text-green-400",children:aA(r.takeProfit)})]}),(0,b.jsxs)("div",{className:"flex justify-between",children:[(0,b.jsx)("span",{className:"text-slate-300",children:"Risk/Reward:"}),(0,b.jsxs)("span",{className:"text-blue-400 font-semibold",children:[r.riskRewardRatio.toFixed(2),":1"]})]})]})})]}),r&&(0,b.jsxs)(aE,{className:"bg-slate-800/50 border-slate-700",children:[(0,b.jsx)(aF,{children:(0,b.jsxs)(aG,{className:"text-white flex items-center",children:[(0,b.jsx)(m,{className:"mr-2 h-5 w-5 text-purple-400"}),"AI Analysis"]})}),(0,b.jsx)(aI,{children:(0,b.jsxs)("div",{className:"space-y-3",children:[(0,b.jsxs)("div",{className:"flex justify-between",children:[(0,b.jsx)("span",{className:"text-slate-300",children:"Trend:"}),(0,b.jsx)("span",{className:`font-semibold ${"BULLISH"===r.trend?"text-green-400":"BEARISH"===r.trend?"text-red-400":"text-yellow-400"}`,children:r.trend})]}),(0,b.jsxs)("div",{className:"flex justify-between",children:[(0,b.jsx)("span",{className:"text-slate-300",children:"Confidence:"}),(0,b.jsxs)("span",{className:"text-white font-semibold",children:[r.confidence.toFixed(1),"%"]})]}),(0,b.jsxs)("div",{className:"flex justify-between",children:[(0,b.jsx)("span",{className:"text-slate-300",children:"Recommendation:"}),(0,b.jsx)("span",{className:`font-semibold ${r.recommendation.includes("BUY")?"text-green-400":r.recommendation.includes("SELL")?"text-red-400":"text-yellow-400"}`,children:r.recommendation.replace("_"," ")})]})]})})]})]}),r&&(0,b.jsxs)(aE,{className:"mt-6 bg-slate-800/50 border-slate-700",children:[(0,b.jsx)(aF,{children:(0,b.jsxs)(aG,{className:"text-white flex items-center",children:[(0,b.jsx)(i,{className:"mr-2 h-5 w-5 text-orange-400"}),"Technical Indicators"]})}),(0,b.jsx)(aI,{children:(0,b.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:r.indicators.map((a,c)=>(0,b.jsxs)("div",{className:"p-4 bg-slate-700/50 rounded-lg",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,b.jsx)("h4",{className:"text-white font-medium",children:a.name}),(0,b.jsx)("span",{className:`px-2 py-1 rounded text-xs font-semibold ${"BUY"===a.signal?"bg-green-500/20 text-green-400":"SELL"===a.signal?"bg-red-500/20 text-red-400":"bg-yellow-500/20 text-yellow-400"}`,children:a.signal})]}),(0,b.jsx)("p",{className:"text-slate-300 text-sm",children:a.description})]},c))})})]}),r&&(r.supportLevels.length>0||r.resistanceLevels.length>0)&&(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mt-6",children:[r.supportLevels.length>0&&(0,b.jsxs)(aE,{className:"bg-slate-800/50 border-slate-700",children:[(0,b.jsx)(aF,{children:(0,b.jsxs)(aG,{className:"text-white flex items-center",children:[(0,b.jsx)(l,{className:"mr-2 h-5 w-5 text-green-400"}),"Support Levels"]})}),(0,b.jsx)(aI,{children:(0,b.jsx)("div",{className:"space-y-2",children:r.supportLevels.map((a,c)=>(0,b.jsxs)("div",{className:"flex justify-between",children:[(0,b.jsxs)("span",{className:"text-slate-300",children:["Support ",c+1,":"]}),(0,b.jsx)("span",{className:"text-green-400 font-semibold",children:aA(a)})]},c))})})]}),r.resistanceLevels.length>0&&(0,b.jsxs)(aE,{className:"bg-slate-800/50 border-slate-700",children:[(0,b.jsx)(aF,{children:(0,b.jsxs)(aG,{className:"text-white flex items-center",children:[(0,b.jsx)(l,{className:"mr-2 h-5 w-5 text-red-400"}),"Resistance Levels"]})}),(0,b.jsx)(aI,{children:(0,b.jsx)("div",{className:"space-y-2",children:r.resistanceLevels.map((a,c)=>(0,b.jsxs)("div",{className:"flex justify-between",children:[(0,b.jsxs)("span",{className:"text-slate-300",children:["Resistance ",c+1,":"]}),(0,b.jsx)("span",{className:"text-red-400 font-semibold",children:aA(a)})]},c))})})]})]})]})})]})}}];

//# sourceMappingURL=swing-trader-ai_src_app_page_tsx_e4650f89._.js.map