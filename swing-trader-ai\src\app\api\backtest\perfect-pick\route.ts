import { NextRequest, NextResponse } from 'next/server'
import { PerfectPickBacktester } from '@/lib/backtesting'
import { PolygonAPI } from '@/lib/polygon'
import { PerfectPickSetup } from '@/types/trading'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { setups, maxHoldingDays = 30, startDate, endDate } = body

    if (!setups || !Array.isArray(setups)) {
      return NextResponse.json(
        { success: false, error: 'Setups array is required' },
        { status: 400 }
      )
    }

    console.log(`🧪 Starting backtest for ${setups.length} Perfect-Pick setups...`)

    const backtester = new PerfectPickBacktester()
    const polygonAPI = new PolygonAPI(process.env.POLYGON_API_KEY)

    // Helper function to get historical data
    const getHistoricalData = async (symbol: string) => {
      const end = endDate || new Date().toISOString().split('T')[0]
      const start = startDate || new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 1 year ago
      
      return await polygonAPI.getHistoricalData(symbol, start, end, '1', 'day')
    }

    // Run backtest
    const results = await backtester.backtestMultipleSetups(
      setups as PerfectPickSetup[],
      getHistoricalData,
      maxHoldingDays
    )

    // Generate comprehensive summary
    const summary = backtester.generateSummary(results)
    const performanceByCatalystTier = backtester.getPerformanceByCatalystTier(results)
    const performanceByTechnicalGrade = backtester.getPerformanceByTechnicalGrade(results)

    console.log(`✅ Backtest complete. ${results.length} trades analyzed.`)

    const response = {
      success: true,
      data: {
        results,
        summary,
        performanceByCatalystTier,
        performanceByTechnicalGrade,
        metadata: {
          totalSetups: setups.length,
          successfulBacktests: results.length,
          failedBacktests: setups.length - results.length,
          maxHoldingDays,
          dateRange: { startDate, endDate },
          generatedAt: new Date().toISOString()
        }
      }
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error in Perfect-Pick backtest API:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to run backtest',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    const symbols = searchParams.get('symbols')?.split(',').filter(Boolean)
    const maxHoldingDays = parseInt(searchParams.get('maxHoldingDays') || '30')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const catalystTypes = searchParams.get('catalystTypes')?.split(',').filter(Boolean)
    const minSetupGrade = searchParams.get('minSetupGrade') || 'C'

    if (!symbols || symbols.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Symbols parameter is required' },
        { status: 400 }
      )
    }

    console.log(`🧪 Running historical Perfect-Pick backtest for ${symbols.length} symbols...`)

    // This would typically involve:
    // 1. Historical catalyst detection for the given symbols and date range
    // 2. Historical Perfect-Pick setup generation
    // 3. Backtesting those setups
    // For now, we'll return a simplified response

    const mockResults = symbols.map(symbol => ({
      symbol,
      totalTrades: Math.floor(Math.random() * 20) + 5,
      winRate: 0.6 + Math.random() * 0.3,
      avgRMultiple: 1.5 + Math.random() * 2,
      totalReturn: Math.random() * 50 - 10,
      maxDrawdown: -(Math.random() * 15 + 5)
    }))

    const response = {
      success: true,
      data: {
        historicalBacktest: mockResults,
        summary: {
          totalSymbols: symbols.length,
          avgWinRate: mockResults.reduce((sum, r) => sum + r.winRate, 0) / mockResults.length,
          avgRMultiple: mockResults.reduce((sum, r) => sum + r.avgRMultiple, 0) / mockResults.length,
          totalTrades: mockResults.reduce((sum, r) => sum + r.totalTrades, 0)
        },
        parameters: {
          symbols,
          maxHoldingDays,
          dateRange: { startDate, endDate },
          catalystTypes,
          minSetupGrade
        },
        generatedAt: new Date().toISOString()
      }
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error in historical backtest API:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to run historical backtest',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
