/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ai/route";
exports.ids = ["app/api/ai/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Froute&page=%2Fapi%2Fai%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Froute&page=%2Fapi%2Fai%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var C_Users_SJFit_Desktop_shittyidea_swing_trader_ai_src_app_api_ai_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/ai/route.ts */ \"(rsc)/./src/app/api/ai/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ai/route\",\n        pathname: \"/api/ai\",\n        filename: \"route\",\n        bundlePath: \"app/api/ai/route\"\n    },\n    distDir: \".next\" || 0,\n    relativeProjectDir:  false || '',\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\shittyidea\\\\swing-trader-ai\\\\src\\\\app\\\\api\\\\ai\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_SJFit_Desktop_shittyidea_swing_trader_ai_src_app_api_ai_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/ai/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = false;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        if (!(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Froute&page=%2Fapi%2Fai%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/ai/route.ts":
/*!*********************************!*\
  !*** ./src/app/api/ai/route.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_openai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/openai */ \"(rsc)/./src/lib/openai.ts\");\n\n\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const action = searchParams.get('action');\n        switch(action){\n            case 'status':\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    enabled: (0,_lib_openai__WEBPACK_IMPORTED_MODULE_1__.isOpenAIEnabled)(),\n                    model: (0,_lib_openai__WEBPACK_IMPORTED_MODULE_1__.isOpenAIEnabled)() ? await (0,_lib_openai__WEBPACK_IMPORTED_MODULE_1__.getLatestModel)() : null,\n                    features: {\n                        marketCommentary: true,\n                        riskAssessment: true,\n                        tradingRecommendations: true\n                    }\n                });\n            case 'model':\n                if (!(0,_lib_openai__WEBPACK_IMPORTED_MODULE_1__.isOpenAIEnabled)()) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'OpenAI not enabled'\n                    }, {\n                        status: 400\n                    });\n                }\n                const model = await (0,_lib_openai__WEBPACK_IMPORTED_MODULE_1__.getLatestModel)();\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    model\n                });\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Invalid action'\n                }, {\n                    status: 400\n                });\n        }\n    } catch (error) {\n        console.error('AI API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        if (!(0,_lib_openai__WEBPACK_IMPORTED_MODULE_1__.isOpenAIEnabled)()) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'OpenAI not enabled'\n            }, {\n                status: 400\n            });\n        }\n        const body = await request.json();\n        const { action, data } = body;\n        switch(action){\n            case 'market-commentary':\n                const { scanResults, marketConditions } = data;\n                const commentary = await (0,_lib_openai__WEBPACK_IMPORTED_MODULE_1__.generateMarketCommentary)(scanResults, marketConditions);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    commentary\n                });\n            case 'risk-assessment':\n                const { setup } = data;\n                const riskAssessment = await (0,_lib_openai__WEBPACK_IMPORTED_MODULE_1__.generateRiskAssessment)(setup);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    riskAssessment\n                });\n            case 'trading-recommendations':\n                const { scanResults: results, userPreferences } = data;\n                const recommendations = await (0,_lib_openai__WEBPACK_IMPORTED_MODULE_1__.generateTradingRecommendations)(results, userPreferences);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    recommendations\n                });\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Invalid action'\n                }, {\n                    status: 400\n                });\n        }\n    } catch (error) {\n        console.error('AI API POST error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/ai/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/openai.ts":
/*!***************************!*\
  !*** ./src/lib/openai.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   generateMarketCommentary: () => (/* binding */ generateMarketCommentary),\n/* harmony export */   generateRiskAssessment: () => (/* binding */ generateRiskAssessment),\n/* harmony export */   generateTradingRecommendations: () => (/* binding */ generateTradingRecommendations),\n/* harmony export */   getLatestModel: () => (/* binding */ getLatestModel),\n/* harmony export */   isOpenAIEnabled: () => (/* binding */ isOpenAIEnabled)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/openai/index.mjs\");\n\n// Rate limiting configuration\nconst RATE_LIMIT_DELAY = 1000; // 1 second between requests\nlet lastRequestTime = 0;\n// Initialize OpenAI client\nconst openai = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n    apiKey: process.env.OPENAI_API_KEY\n});\n// Check if OpenAI is enabled\nconst isOpenAIEnabled = ()=>{\n    return process.env.OPENAI_ENABLED === 'true' && !!process.env.OPENAI_API_KEY;\n};\n// Rate limiting helper\nconst enforceRateLimit = async ()=>{\n    const now = Date.now();\n    const timeSinceLastRequest = now - lastRequestTime;\n    if (timeSinceLastRequest < RATE_LIMIT_DELAY) {\n        const delay = RATE_LIMIT_DELAY - timeSinceLastRequest;\n        await new Promise((resolve)=>setTimeout(resolve, delay));\n    }\n    lastRequestTime = Date.now();\n};\n// Get the latest available OpenAI model\nconst getLatestModel = async ()=>{\n    try {\n        await enforceRateLimit();\n        const models = await openai.models.list();\n        const gptModels = models.data.filter((model)=>model.id.startsWith('gpt-')).sort((a, b)=>b.created - a.created);\n        // Prefer GPT-4 models, then GPT-3.5\n        const preferredModels = [\n            'gpt-4o',\n            'gpt-4o-mini',\n            'gpt-4-turbo',\n            'gpt-4',\n            'gpt-3.5-turbo'\n        ];\n        for (const preferred of preferredModels){\n            const found = gptModels.find((model)=>model.id === preferred);\n            if (found) {\n                console.log(`Using OpenAI model: ${found.id}`);\n                return found.id;\n            }\n        }\n        // Fallback to the latest GPT model\n        if (gptModels.length > 0) {\n            console.log(`Using fallback OpenAI model: ${gptModels[0].id}`);\n            return gptModels[0].id;\n        }\n        throw new Error('No GPT models available');\n    } catch (error) {\n        console.error('Error getting OpenAI models:', error);\n        return 'gpt-4o'; // Default fallback\n    }\n};\n// AI-powered market commentary\nconst generateMarketCommentary = async (scanResults, marketConditions)=>{\n    if (!isOpenAIEnabled()) {\n        return \"AI analysis disabled. Enable in configuration to get intelligent market insights.\";\n    }\n    try {\n        await enforceRateLimit();\n        const model = await getLatestModel();\n        const topResults = scanResults.slice(0, 5);\n        const prompt = `As a professional swing trading analyst, provide a concise market commentary based on the following scan results and market conditions:\n\nMarket Conditions:\n- Time: ${marketConditions.timeOfDay}\n- Market Hours: ${marketConditions.marketHours ? 'Open' : 'Closed'}\n- Optimal Scan Time: ${marketConditions.isOptimalScanTime ? 'Yes' : 'No'}\n\nTop Trading Opportunities:\n${topResults.map((result, i)=>`\n${i + 1}. ${result.symbol} (Score: ${result.overallScore.toFixed(1)}/100)\n   - Best Strategy: ${result.bestStrategy || 'None'}\n   - Price Action: Recent momentum and volume patterns\n   - Key Levels: Support/resistance analysis\n`).join('')}\n\nProvide a 2-3 paragraph market commentary focusing on:\n1. Overall market sentiment and trading conditions\n2. Key themes and sectors showing strength/weakness\n3. Risk considerations and trading recommendations\n\nKeep it professional, actionable, and under 200 words.`;\n        const completion = await openai.chat.completions.create({\n            model,\n            messages: [\n                {\n                    role: 'system',\n                    content: 'You are a professional swing trading analyst with expertise in technical analysis and market psychology. Provide clear, actionable insights.'\n                },\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ],\n            max_tokens: 300,\n            temperature: 0.7\n        });\n        return completion.choices[0]?.message?.content || 'Unable to generate market commentary at this time.';\n    } catch (error) {\n        console.error('Error generating market commentary:', error);\n        return 'Market commentary temporarily unavailable. Technical analysis remains fully functional.';\n    }\n};\n// AI-powered risk assessment for individual setups\nconst generateRiskAssessment = async (setup, marketData)=>{\n    if (!isOpenAIEnabled()) {\n        return {\n            riskScore: 5,\n            riskFactors: [\n                'AI analysis disabled'\n            ],\n            recommendations: [\n                'Enable AI features for enhanced risk assessment'\n            ],\n            sentiment: 'neutral'\n        };\n    }\n    try {\n        await enforceRateLimit();\n        const model = await getLatestModel();\n        const prompt = `Analyze this swing trading setup for risk assessment:\n\nSymbol: ${setup.symbol}\nStrategy: ${setup.strategy}\nConfidence: ${setup.confidence}%\nEntry: $${setup.entryPrice}\nStop Loss: $${setup.stopLoss}\nTargets: ${setup.targets.map((t)=>`$${t}`).join(', ')}\nPosition Size: ${setup.positionSize} shares\nRisk Amount: $${setup.riskAmount}\n\nProvide a JSON response with:\n{\n  \"riskScore\": 1-10 (1=low risk, 10=high risk),\n  \"riskFactors\": [\"factor1\", \"factor2\", ...],\n  \"recommendations\": [\"rec1\", \"rec2\", ...],\n  \"sentiment\": \"bullish|bearish|neutral\"\n}\n\nConsider: market conditions, position sizing, risk/reward ratio, strategy type, and current market volatility.`;\n        const completion = await openai.chat.completions.create({\n            model,\n            messages: [\n                {\n                    role: 'system',\n                    content: 'You are a risk management expert specializing in swing trading. Provide objective risk assessments in valid JSON format.'\n                },\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ],\n            max_tokens: 400,\n            temperature: 0.3\n        });\n        const response = completion.choices[0]?.message?.content;\n        if (response) {\n            try {\n                return JSON.parse(response);\n            } catch (parseError) {\n                console.error('Error parsing AI risk assessment:', parseError);\n            }\n        }\n        // Fallback response\n        return {\n            riskScore: 5,\n            riskFactors: [\n                'Unable to complete AI risk analysis'\n            ],\n            recommendations: [\n                'Review setup manually',\n                'Consider current market conditions'\n            ],\n            sentiment: 'neutral'\n        };\n    } catch (error) {\n        console.error('Error generating risk assessment:', error);\n        return {\n            riskScore: 5,\n            riskFactors: [\n                'AI risk assessment temporarily unavailable'\n            ],\n            recommendations: [\n                'Proceed with standard risk management'\n            ],\n            sentiment: 'neutral'\n        };\n    }\n};\n// AI-powered personalized trading recommendations\nconst generateTradingRecommendations = async (scanResults, userPreferences)=>{\n    if (!isOpenAIEnabled()) {\n        return {\n            topPicks: [\n                'AI recommendations disabled'\n            ],\n            avoidList: [],\n            marketOutlook: 'Enable AI features for personalized recommendations',\n            actionItems: [\n                'Configure OpenAI integration'\n            ]\n        };\n    }\n    try {\n        await enforceRateLimit();\n        const model = await getLatestModel();\n        const topResults = scanResults.slice(0, 10);\n        const prompt = `As a professional trading advisor, analyze these swing trading opportunities and provide personalized recommendations:\n\nUser Profile:\n- Risk Tolerance: ${userPreferences?.riskTolerance || 'medium'}\n- Trading Style: ${userPreferences?.tradingStyle || 'moderate'}\n- Account Size: $${userPreferences?.accountSize?.toLocaleString() || '100,000'}\n\nAvailable Opportunities:\n${topResults.map((result, i)=>`\n${i + 1}. ${result.symbol} (Score: ${result.overallScore.toFixed(1)}/100)\n   - Strategy: ${result.bestStrategy || 'None'}\n   - Confidence: High/Medium/Low based on score\n`).join('')}\n\nProvide a JSON response with:\n{\n  \"topPicks\": [\"symbol1\", \"symbol2\", \"symbol3\"],\n  \"avoidList\": [\"symbol1\", \"symbol2\"],\n  \"marketOutlook\": \"brief market outlook\",\n  \"actionItems\": [\"action1\", \"action2\", \"action3\"]\n}\n\nFocus on risk-appropriate recommendations for the user's profile.`;\n        const completion = await openai.chat.completions.create({\n            model,\n            messages: [\n                {\n                    role: 'system',\n                    content: 'You are a professional trading advisor. Provide personalized, risk-appropriate recommendations in valid JSON format.'\n                },\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ],\n            max_tokens: 500,\n            temperature: 0.4\n        });\n        const response = completion.choices[0]?.message?.content;\n        if (response) {\n            try {\n                return JSON.parse(response);\n            } catch (parseError) {\n                console.error('Error parsing AI recommendations:', parseError);\n            }\n        }\n        // Fallback response\n        return {\n            topPicks: topResults.slice(0, 3).map((r)=>r.symbol),\n            avoidList: [],\n            marketOutlook: 'Mixed market conditions - proceed with caution',\n            actionItems: [\n                'Review top-scoring setups',\n                'Monitor market conditions',\n                'Manage position sizes'\n            ]\n        };\n    } catch (error) {\n        console.error('Error generating trading recommendations:', error);\n        return {\n            topPicks: [],\n            avoidList: [],\n            marketOutlook: 'AI recommendations temporarily unavailable',\n            actionItems: [\n                'Use technical analysis for decision making'\n            ]\n        };\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    isOpenAIEnabled,\n    getLatestModel,\n    generateMarketCommentary,\n    generateRiskAssessment,\n    generateTradingRecommendations\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL29wZW5haS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTRCO0FBSTVCLDhCQUE4QjtBQUM5QixNQUFNQyxtQkFBbUIsTUFBTSw0QkFBNEI7QUFDM0QsSUFBSUMsa0JBQWtCO0FBRXRCLDJCQUEyQjtBQUMzQixNQUFNQyxTQUFTLElBQUlILDhDQUFNQSxDQUFDO0lBQ3hCSSxRQUFRQyxRQUFRQyxHQUFHLENBQUNDLGNBQWM7QUFDcEM7QUFFQSw2QkFBNkI7QUFDdEIsTUFBTUMsa0JBQWtCO0lBQzdCLE9BQU9ILFFBQVFDLEdBQUcsQ0FBQ0csY0FBYyxLQUFLLFVBQVUsQ0FBQyxDQUFDSixRQUFRQyxHQUFHLENBQUNDLGNBQWM7QUFDOUUsRUFBRTtBQUVGLHVCQUF1QjtBQUN2QixNQUFNRyxtQkFBbUI7SUFDdkIsTUFBTUMsTUFBTUMsS0FBS0QsR0FBRztJQUNwQixNQUFNRSx1QkFBdUJGLE1BQU1UO0lBRW5DLElBQUlXLHVCQUF1Qlosa0JBQWtCO1FBQzNDLE1BQU1hLFFBQVFiLG1CQUFtQlk7UUFDakMsTUFBTSxJQUFJRSxRQUFRQyxDQUFBQSxVQUFXQyxXQUFXRCxTQUFTRjtJQUNuRDtJQUVBWixrQkFBa0JVLEtBQUtELEdBQUc7QUFDNUI7QUFFQSx3Q0FBd0M7QUFDakMsTUFBTU8saUJBQWlCO0lBQzVCLElBQUk7UUFDRixNQUFNUjtRQUVOLE1BQU1TLFNBQVMsTUFBTWhCLE9BQU9nQixNQUFNLENBQUNDLElBQUk7UUFDdkMsTUFBTUMsWUFBWUYsT0FBT0csSUFBSSxDQUMxQkMsTUFBTSxDQUFDQyxDQUFBQSxRQUFTQSxNQUFNQyxFQUFFLENBQUNDLFVBQVUsQ0FBQyxTQUNwQ0MsSUFBSSxDQUFDLENBQUNDLEdBQUdDLElBQU1BLEVBQUVDLE9BQU8sR0FBR0YsRUFBRUUsT0FBTztRQUV2QyxvQ0FBb0M7UUFDcEMsTUFBTUMsa0JBQWtCO1lBQ3RCO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7U0FDRDtRQUVELEtBQUssTUFBTUMsYUFBYUQsZ0JBQWlCO1lBQ3ZDLE1BQU1FLFFBQVFaLFVBQVVhLElBQUksQ0FBQ1YsQ0FBQUEsUUFBU0EsTUFBTUMsRUFBRSxLQUFLTztZQUNuRCxJQUFJQyxPQUFPO2dCQUNURSxRQUFRQyxHQUFHLENBQUMsQ0FBQyxvQkFBb0IsRUFBRUgsTUFBTVIsRUFBRSxFQUFFO2dCQUM3QyxPQUFPUSxNQUFNUixFQUFFO1lBQ2pCO1FBQ0Y7UUFFQSxtQ0FBbUM7UUFDbkMsSUFBSUosVUFBVWdCLE1BQU0sR0FBRyxHQUFHO1lBQ3hCRixRQUFRQyxHQUFHLENBQUMsQ0FBQyw2QkFBNkIsRUFBRWYsU0FBUyxDQUFDLEVBQUUsQ0FBQ0ksRUFBRSxFQUFFO1lBQzdELE9BQU9KLFNBQVMsQ0FBQyxFQUFFLENBQUNJLEVBQUU7UUFDeEI7UUFFQSxNQUFNLElBQUlhLE1BQU07SUFDbEIsRUFBRSxPQUFPQyxPQUFPO1FBQ2RKLFFBQVFJLEtBQUssQ0FBQyxnQ0FBZ0NBO1FBQzlDLE9BQU8sVUFBVSxtQkFBbUI7SUFDdEM7QUFDRixFQUFFO0FBRUYsK0JBQStCO0FBQ3hCLE1BQU1DLDJCQUEyQixPQUN0Q0MsYUFDQUM7SUFFQSxJQUFJLENBQUNsQyxtQkFBbUI7UUFDdEIsT0FBTztJQUNUO0lBRUEsSUFBSTtRQUNGLE1BQU1FO1FBRU4sTUFBTWMsUUFBUSxNQUFNTjtRQUNwQixNQUFNeUIsYUFBYUYsWUFBWUcsS0FBSyxDQUFDLEdBQUc7UUFFeEMsTUFBTUMsU0FBUyxDQUFDOzs7UUFHWixFQUFFSCxpQkFBaUJJLFNBQVMsQ0FBQztnQkFDckIsRUFBRUosaUJBQWlCSyxXQUFXLEdBQUcsU0FBUyxTQUFTO3FCQUM5QyxFQUFFTCxpQkFBaUJNLGlCQUFpQixHQUFHLFFBQVEsS0FBSzs7O0FBR3pFLEVBQUVMLFdBQVdNLEdBQUcsQ0FBQyxDQUFDQyxRQUFRQyxJQUFNLENBQUM7QUFDakMsRUFBRUEsSUFBSSxFQUFFLEVBQUUsRUFBRUQsT0FBT0UsTUFBTSxDQUFDLFNBQVMsRUFBRUYsT0FBT0csWUFBWSxDQUFDQyxPQUFPLENBQUMsR0FBRztvQkFDaEQsRUFBRUosT0FBT0ssWUFBWSxJQUFJLE9BQU87OztBQUdwRCxDQUFDLEVBQUVDLElBQUksQ0FBQyxJQUFJOzs7Ozs7O3NEQU8wQyxDQUFDO1FBRW5ELE1BQU1DLGFBQWEsTUFBTXRELE9BQU91RCxJQUFJLENBQUNDLFdBQVcsQ0FBQ0MsTUFBTSxDQUFDO1lBQ3REcEM7WUFDQXFDLFVBQVU7Z0JBQ1I7b0JBQ0VDLE1BQU07b0JBQ05DLFNBQVM7Z0JBQ1g7Z0JBQ0E7b0JBQ0VELE1BQU07b0JBQ05DLFNBQVNsQjtnQkFDWDthQUNEO1lBQ0RtQixZQUFZO1lBQ1pDLGFBQWE7UUFDZjtRQUVBLE9BQU9SLFdBQVdTLE9BQU8sQ0FBQyxFQUFFLEVBQUVDLFNBQVNKLFdBQVc7SUFDcEQsRUFBRSxPQUFPeEIsT0FBTztRQUNkSixRQUFRSSxLQUFLLENBQUMsdUNBQXVDQTtRQUNyRCxPQUFPO0lBQ1Q7QUFDRixFQUFFO0FBRUYsbURBQW1EO0FBQzVDLE1BQU02Qix5QkFBeUIsT0FDcENDLE9BQ0FDO0lBT0EsSUFBSSxDQUFDOUQsbUJBQW1CO1FBQ3RCLE9BQU87WUFDTCtELFdBQVc7WUFDWEMsYUFBYTtnQkFBQzthQUF1QjtZQUNyQ0MsaUJBQWlCO2dCQUFDO2FBQWtEO1lBQ3BFQyxXQUFXO1FBQ2I7SUFDRjtJQUVBLElBQUk7UUFDRixNQUFNaEU7UUFFTixNQUFNYyxRQUFRLE1BQU1OO1FBRXBCLE1BQU0yQixTQUFTLENBQUM7O1FBRVosRUFBRXdCLE1BQU1qQixNQUFNLENBQUM7VUFDYixFQUFFaUIsTUFBTU0sUUFBUSxDQUFDO1lBQ2YsRUFBRU4sTUFBTU8sVUFBVSxDQUFDO1FBQ3ZCLEVBQUVQLE1BQU1RLFVBQVUsQ0FBQztZQUNmLEVBQUVSLE1BQU1TLFFBQVEsQ0FBQztTQUNwQixFQUFFVCxNQUFNVSxPQUFPLENBQUM5QixHQUFHLENBQUMrQixDQUFBQSxJQUFLLENBQUMsQ0FBQyxFQUFFQSxHQUFHLEVBQUV4QixJQUFJLENBQUMsTUFBTTtlQUN2QyxFQUFFYSxNQUFNWSxZQUFZLENBQUM7Y0FDdEIsRUFBRVosTUFBTWEsVUFBVSxDQUFDOzs7Ozs7Ozs7OzhHQVU2RSxDQUFDO1FBRTNHLE1BQU16QixhQUFhLE1BQU10RCxPQUFPdUQsSUFBSSxDQUFDQyxXQUFXLENBQUNDLE1BQU0sQ0FBQztZQUN0RHBDO1lBQ0FxQyxVQUFVO2dCQUNSO29CQUNFQyxNQUFNO29CQUNOQyxTQUFTO2dCQUNYO2dCQUNBO29CQUNFRCxNQUFNO29CQUNOQyxTQUFTbEI7Z0JBQ1g7YUFDRDtZQUNEbUIsWUFBWTtZQUNaQyxhQUFhO1FBQ2Y7UUFFQSxNQUFNa0IsV0FBVzFCLFdBQVdTLE9BQU8sQ0FBQyxFQUFFLEVBQUVDLFNBQVNKO1FBQ2pELElBQUlvQixVQUFVO1lBQ1osSUFBSTtnQkFDRixPQUFPQyxLQUFLQyxLQUFLLENBQUNGO1lBQ3BCLEVBQUUsT0FBT0csWUFBWTtnQkFDbkJuRCxRQUFRSSxLQUFLLENBQUMscUNBQXFDK0M7WUFDckQ7UUFDRjtRQUVBLG9CQUFvQjtRQUNwQixPQUFPO1lBQ0xmLFdBQVc7WUFDWEMsYUFBYTtnQkFBQzthQUFzQztZQUNwREMsaUJBQWlCO2dCQUFDO2dCQUF5QjthQUFxQztZQUNoRkMsV0FBVztRQUNiO0lBQ0YsRUFBRSxPQUFPbkMsT0FBTztRQUNkSixRQUFRSSxLQUFLLENBQUMscUNBQXFDQTtRQUNuRCxPQUFPO1lBQ0xnQyxXQUFXO1lBQ1hDLGFBQWE7Z0JBQUM7YUFBNkM7WUFDM0RDLGlCQUFpQjtnQkFBQzthQUF3QztZQUMxREMsV0FBVztRQUNiO0lBQ0Y7QUFDRixFQUFFO0FBRUYsa0RBQWtEO0FBQzNDLE1BQU1hLGlDQUFpQyxPQUM1QzlDLGFBQ0ErQztJQVdBLElBQUksQ0FBQ2hGLG1CQUFtQjtRQUN0QixPQUFPO1lBQ0xpRixVQUFVO2dCQUFDO2FBQThCO1lBQ3pDQyxXQUFXLEVBQUU7WUFDYkMsZUFBZTtZQUNmQyxhQUFhO2dCQUFDO2FBQStCO1FBQy9DO0lBQ0Y7SUFFQSxJQUFJO1FBQ0YsTUFBTWxGO1FBRU4sTUFBTWMsUUFBUSxNQUFNTjtRQUNwQixNQUFNeUIsYUFBYUYsWUFBWUcsS0FBSyxDQUFDLEdBQUc7UUFFeEMsTUFBTUMsU0FBUyxDQUFDOzs7a0JBR0YsRUFBRTJDLGlCQUFpQkssaUJBQWlCLFNBQVM7aUJBQzlDLEVBQUVMLGlCQUFpQk0sZ0JBQWdCLFdBQVc7aUJBQzlDLEVBQUVOLGlCQUFpQk8sYUFBYUMsb0JBQW9CLFVBQVU7OztBQUcvRSxFQUFFckQsV0FBV00sR0FBRyxDQUFDLENBQUNDLFFBQVFDLElBQU0sQ0FBQztBQUNqQyxFQUFFQSxJQUFJLEVBQUUsRUFBRSxFQUFFRCxPQUFPRSxNQUFNLENBQUMsU0FBUyxFQUFFRixPQUFPRyxZQUFZLENBQUNDLE9BQU8sQ0FBQyxHQUFHO2VBQ3JELEVBQUVKLE9BQU9LLFlBQVksSUFBSSxPQUFPOztBQUUvQyxDQUFDLEVBQUVDLElBQUksQ0FBQyxJQUFJOzs7Ozs7Ozs7O2lFQVVxRCxDQUFDO1FBRTlELE1BQU1DLGFBQWEsTUFBTXRELE9BQU91RCxJQUFJLENBQUNDLFdBQVcsQ0FBQ0MsTUFBTSxDQUFDO1lBQ3REcEM7WUFDQXFDLFVBQVU7Z0JBQ1I7b0JBQ0VDLE1BQU07b0JBQ05DLFNBQVM7Z0JBQ1g7Z0JBQ0E7b0JBQ0VELE1BQU07b0JBQ05DLFNBQVNsQjtnQkFDWDthQUNEO1lBQ0RtQixZQUFZO1lBQ1pDLGFBQWE7UUFDZjtRQUVBLE1BQU1rQixXQUFXMUIsV0FBV1MsT0FBTyxDQUFDLEVBQUUsRUFBRUMsU0FBU0o7UUFDakQsSUFBSW9CLFVBQVU7WUFDWixJQUFJO2dCQUNGLE9BQU9DLEtBQUtDLEtBQUssQ0FBQ0Y7WUFDcEIsRUFBRSxPQUFPRyxZQUFZO2dCQUNuQm5ELFFBQVFJLEtBQUssQ0FBQyxxQ0FBcUMrQztZQUNyRDtRQUNGO1FBRUEsb0JBQW9CO1FBQ3BCLE9BQU87WUFDTEcsVUFBVTlDLFdBQVdDLEtBQUssQ0FBQyxHQUFHLEdBQUdLLEdBQUcsQ0FBQ2dELENBQUFBLElBQUtBLEVBQUU3QyxNQUFNO1lBQ2xEc0MsV0FBVyxFQUFFO1lBQ2JDLGVBQWU7WUFDZkMsYUFBYTtnQkFBQztnQkFBNkI7Z0JBQTZCO2FBQXdCO1FBQ2xHO0lBQ0YsRUFBRSxPQUFPckQsT0FBTztRQUNkSixRQUFRSSxLQUFLLENBQUMsNkNBQTZDQTtRQUMzRCxPQUFPO1lBQ0xrRCxVQUFVLEVBQUU7WUFDWkMsV0FBVyxFQUFFO1lBQ2JDLGVBQWU7WUFDZkMsYUFBYTtnQkFBQzthQUE2QztRQUM3RDtJQUNGO0FBQ0YsRUFBRTtBQUVGLGlFQUFlO0lBQ2JwRjtJQUNBVTtJQUNBc0I7SUFDQTRCO0lBQ0FtQjtBQUNGLENBQUMsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxTSkZpdFxcRGVza3RvcFxcc2hpdHR5aWRlYVxcc3dpbmctdHJhZGVyLWFpXFxzcmNcXGxpYlxcb3BlbmFpLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBPcGVuQUkgZnJvbSAnb3BlbmFpJztcbmltcG9ydCB7IEVuaGFuY2VkU2NhblJlc3VsdCB9IGZyb20gJy4vZW5oYW5jZWRTd2luZ1NjYW5uZXInO1xuaW1wb3J0IHsgU3RyYXRlZ3lTZXR1cCB9IGZyb20gJy4vc3dpbmdTdHJhdGVnaWVzJztcblxuLy8gUmF0ZSBsaW1pdGluZyBjb25maWd1cmF0aW9uXG5jb25zdCBSQVRFX0xJTUlUX0RFTEFZID0gMTAwMDsgLy8gMSBzZWNvbmQgYmV0d2VlbiByZXF1ZXN0c1xubGV0IGxhc3RSZXF1ZXN0VGltZSA9IDA7XG5cbi8vIEluaXRpYWxpemUgT3BlbkFJIGNsaWVudFxuY29uc3Qgb3BlbmFpID0gbmV3IE9wZW5BSSh7XG4gIGFwaUtleTogcHJvY2Vzcy5lbnYuT1BFTkFJX0FQSV9LRVksXG59KTtcblxuLy8gQ2hlY2sgaWYgT3BlbkFJIGlzIGVuYWJsZWRcbmV4cG9ydCBjb25zdCBpc09wZW5BSUVuYWJsZWQgPSAoKTogYm9vbGVhbiA9PiB7XG4gIHJldHVybiBwcm9jZXNzLmVudi5PUEVOQUlfRU5BQkxFRCA9PT0gJ3RydWUnICYmICEhcHJvY2Vzcy5lbnYuT1BFTkFJX0FQSV9LRVk7XG59O1xuXG4vLyBSYXRlIGxpbWl0aW5nIGhlbHBlclxuY29uc3QgZW5mb3JjZVJhdGVMaW1pdCA9IGFzeW5jICgpOiBQcm9taXNlPHZvaWQ+ID0+IHtcbiAgY29uc3Qgbm93ID0gRGF0ZS5ub3coKTtcbiAgY29uc3QgdGltZVNpbmNlTGFzdFJlcXVlc3QgPSBub3cgLSBsYXN0UmVxdWVzdFRpbWU7XG4gIFxuICBpZiAodGltZVNpbmNlTGFzdFJlcXVlc3QgPCBSQVRFX0xJTUlUX0RFTEFZKSB7XG4gICAgY29uc3QgZGVsYXkgPSBSQVRFX0xJTUlUX0RFTEFZIC0gdGltZVNpbmNlTGFzdFJlcXVlc3Q7XG4gICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIGRlbGF5KSk7XG4gIH1cbiAgXG4gIGxhc3RSZXF1ZXN0VGltZSA9IERhdGUubm93KCk7XG59O1xuXG4vLyBHZXQgdGhlIGxhdGVzdCBhdmFpbGFibGUgT3BlbkFJIG1vZGVsXG5leHBvcnQgY29uc3QgZ2V0TGF0ZXN0TW9kZWwgPSBhc3luYyAoKTogUHJvbWlzZTxzdHJpbmc+ID0+IHtcbiAgdHJ5IHtcbiAgICBhd2FpdCBlbmZvcmNlUmF0ZUxpbWl0KCk7XG4gICAgXG4gICAgY29uc3QgbW9kZWxzID0gYXdhaXQgb3BlbmFpLm1vZGVscy5saXN0KCk7XG4gICAgY29uc3QgZ3B0TW9kZWxzID0gbW9kZWxzLmRhdGFcbiAgICAgIC5maWx0ZXIobW9kZWwgPT4gbW9kZWwuaWQuc3RhcnRzV2l0aCgnZ3B0LScpKVxuICAgICAgLnNvcnQoKGEsIGIpID0+IGIuY3JlYXRlZCAtIGEuY3JlYXRlZCk7XG4gICAgXG4gICAgLy8gUHJlZmVyIEdQVC00IG1vZGVscywgdGhlbiBHUFQtMy41XG4gICAgY29uc3QgcHJlZmVycmVkTW9kZWxzID0gW1xuICAgICAgJ2dwdC00bycsXG4gICAgICAnZ3B0LTRvLW1pbmknLCBcbiAgICAgICdncHQtNC10dXJibycsXG4gICAgICAnZ3B0LTQnLFxuICAgICAgJ2dwdC0zLjUtdHVyYm8nXG4gICAgXTtcbiAgICBcbiAgICBmb3IgKGNvbnN0IHByZWZlcnJlZCBvZiBwcmVmZXJyZWRNb2RlbHMpIHtcbiAgICAgIGNvbnN0IGZvdW5kID0gZ3B0TW9kZWxzLmZpbmQobW9kZWwgPT4gbW9kZWwuaWQgPT09IHByZWZlcnJlZCk7XG4gICAgICBpZiAoZm91bmQpIHtcbiAgICAgICAgY29uc29sZS5sb2coYFVzaW5nIE9wZW5BSSBtb2RlbDogJHtmb3VuZC5pZH1gKTtcbiAgICAgICAgcmV0dXJuIGZvdW5kLmlkO1xuICAgICAgfVxuICAgIH1cbiAgICBcbiAgICAvLyBGYWxsYmFjayB0byB0aGUgbGF0ZXN0IEdQVCBtb2RlbFxuICAgIGlmIChncHRNb2RlbHMubGVuZ3RoID4gMCkge1xuICAgICAgY29uc29sZS5sb2coYFVzaW5nIGZhbGxiYWNrIE9wZW5BSSBtb2RlbDogJHtncHRNb2RlbHNbMF0uaWR9YCk7XG4gICAgICByZXR1cm4gZ3B0TW9kZWxzWzBdLmlkO1xuICAgIH1cbiAgICBcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ05vIEdQVCBtb2RlbHMgYXZhaWxhYmxlJyk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgZ2V0dGluZyBPcGVuQUkgbW9kZWxzOicsIGVycm9yKTtcbiAgICByZXR1cm4gJ2dwdC00byc7IC8vIERlZmF1bHQgZmFsbGJhY2tcbiAgfVxufTtcblxuLy8gQUktcG93ZXJlZCBtYXJrZXQgY29tbWVudGFyeVxuZXhwb3J0IGNvbnN0IGdlbmVyYXRlTWFya2V0Q29tbWVudGFyeSA9IGFzeW5jIChcbiAgc2NhblJlc3VsdHM6IEVuaGFuY2VkU2NhblJlc3VsdFtdLFxuICBtYXJrZXRDb25kaXRpb25zOiBhbnlcbik6IFByb21pc2U8c3RyaW5nPiA9PiB7XG4gIGlmICghaXNPcGVuQUlFbmFibGVkKCkpIHtcbiAgICByZXR1cm4gXCJBSSBhbmFseXNpcyBkaXNhYmxlZC4gRW5hYmxlIGluIGNvbmZpZ3VyYXRpb24gdG8gZ2V0IGludGVsbGlnZW50IG1hcmtldCBpbnNpZ2h0cy5cIjtcbiAgfVxuXG4gIHRyeSB7XG4gICAgYXdhaXQgZW5mb3JjZVJhdGVMaW1pdCgpO1xuICAgIFxuICAgIGNvbnN0IG1vZGVsID0gYXdhaXQgZ2V0TGF0ZXN0TW9kZWwoKTtcbiAgICBjb25zdCB0b3BSZXN1bHRzID0gc2NhblJlc3VsdHMuc2xpY2UoMCwgNSk7XG4gICAgXG4gICAgY29uc3QgcHJvbXB0ID0gYEFzIGEgcHJvZmVzc2lvbmFsIHN3aW5nIHRyYWRpbmcgYW5hbHlzdCwgcHJvdmlkZSBhIGNvbmNpc2UgbWFya2V0IGNvbW1lbnRhcnkgYmFzZWQgb24gdGhlIGZvbGxvd2luZyBzY2FuIHJlc3VsdHMgYW5kIG1hcmtldCBjb25kaXRpb25zOlxuXG5NYXJrZXQgQ29uZGl0aW9uczpcbi0gVGltZTogJHttYXJrZXRDb25kaXRpb25zLnRpbWVPZkRheX1cbi0gTWFya2V0IEhvdXJzOiAke21hcmtldENvbmRpdGlvbnMubWFya2V0SG91cnMgPyAnT3BlbicgOiAnQ2xvc2VkJ31cbi0gT3B0aW1hbCBTY2FuIFRpbWU6ICR7bWFya2V0Q29uZGl0aW9ucy5pc09wdGltYWxTY2FuVGltZSA/ICdZZXMnIDogJ05vJ31cblxuVG9wIFRyYWRpbmcgT3Bwb3J0dW5pdGllczpcbiR7dG9wUmVzdWx0cy5tYXAoKHJlc3VsdCwgaSkgPT4gYFxuJHtpICsgMX0uICR7cmVzdWx0LnN5bWJvbH0gKFNjb3JlOiAke3Jlc3VsdC5vdmVyYWxsU2NvcmUudG9GaXhlZCgxKX0vMTAwKVxuICAgLSBCZXN0IFN0cmF0ZWd5OiAke3Jlc3VsdC5iZXN0U3RyYXRlZ3kgfHwgJ05vbmUnfVxuICAgLSBQcmljZSBBY3Rpb246IFJlY2VudCBtb21lbnR1bSBhbmQgdm9sdW1lIHBhdHRlcm5zXG4gICAtIEtleSBMZXZlbHM6IFN1cHBvcnQvcmVzaXN0YW5jZSBhbmFseXNpc1xuYCkuam9pbignJyl9XG5cblByb3ZpZGUgYSAyLTMgcGFyYWdyYXBoIG1hcmtldCBjb21tZW50YXJ5IGZvY3VzaW5nIG9uOlxuMS4gT3ZlcmFsbCBtYXJrZXQgc2VudGltZW50IGFuZCB0cmFkaW5nIGNvbmRpdGlvbnNcbjIuIEtleSB0aGVtZXMgYW5kIHNlY3RvcnMgc2hvd2luZyBzdHJlbmd0aC93ZWFrbmVzc1xuMy4gUmlzayBjb25zaWRlcmF0aW9ucyBhbmQgdHJhZGluZyByZWNvbW1lbmRhdGlvbnNcblxuS2VlcCBpdCBwcm9mZXNzaW9uYWwsIGFjdGlvbmFibGUsIGFuZCB1bmRlciAyMDAgd29yZHMuYDtcblxuICAgIGNvbnN0IGNvbXBsZXRpb24gPSBhd2FpdCBvcGVuYWkuY2hhdC5jb21wbGV0aW9ucy5jcmVhdGUoe1xuICAgICAgbW9kZWwsXG4gICAgICBtZXNzYWdlczogW1xuICAgICAgICB7XG4gICAgICAgICAgcm9sZTogJ3N5c3RlbScsXG4gICAgICAgICAgY29udGVudDogJ1lvdSBhcmUgYSBwcm9mZXNzaW9uYWwgc3dpbmcgdHJhZGluZyBhbmFseXN0IHdpdGggZXhwZXJ0aXNlIGluIHRlY2huaWNhbCBhbmFseXNpcyBhbmQgbWFya2V0IHBzeWNob2xvZ3kuIFByb3ZpZGUgY2xlYXIsIGFjdGlvbmFibGUgaW5zaWdodHMuJ1xuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgcm9sZTogJ3VzZXInLFxuICAgICAgICAgIGNvbnRlbnQ6IHByb21wdFxuICAgICAgICB9XG4gICAgICBdLFxuICAgICAgbWF4X3Rva2VuczogMzAwLFxuICAgICAgdGVtcGVyYXR1cmU6IDAuNyxcbiAgICB9KTtcblxuICAgIHJldHVybiBjb21wbGV0aW9uLmNob2ljZXNbMF0/Lm1lc3NhZ2U/LmNvbnRlbnQgfHwgJ1VuYWJsZSB0byBnZW5lcmF0ZSBtYXJrZXQgY29tbWVudGFyeSBhdCB0aGlzIHRpbWUuJztcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZW5lcmF0aW5nIG1hcmtldCBjb21tZW50YXJ5OicsIGVycm9yKTtcbiAgICByZXR1cm4gJ01hcmtldCBjb21tZW50YXJ5IHRlbXBvcmFyaWx5IHVuYXZhaWxhYmxlLiBUZWNobmljYWwgYW5hbHlzaXMgcmVtYWlucyBmdWxseSBmdW5jdGlvbmFsLic7XG4gIH1cbn07XG5cbi8vIEFJLXBvd2VyZWQgcmlzayBhc3Nlc3NtZW50IGZvciBpbmRpdmlkdWFsIHNldHVwc1xuZXhwb3J0IGNvbnN0IGdlbmVyYXRlUmlza0Fzc2Vzc21lbnQgPSBhc3luYyAoXG4gIHNldHVwOiBTdHJhdGVneVNldHVwICYgeyBzeW1ib2w6IHN0cmluZyB9LFxuICBtYXJrZXREYXRhPzogYW55XG4pOiBQcm9taXNlPHtcbiAgcmlza1Njb3JlOiBudW1iZXI7XG4gIHJpc2tGYWN0b3JzOiBzdHJpbmdbXTtcbiAgcmVjb21tZW5kYXRpb25zOiBzdHJpbmdbXTtcbiAgc2VudGltZW50OiAnYnVsbGlzaCcgfCAnYmVhcmlzaCcgfCAnbmV1dHJhbCc7XG59PiA9PiB7XG4gIGlmICghaXNPcGVuQUlFbmFibGVkKCkpIHtcbiAgICByZXR1cm4ge1xuICAgICAgcmlza1Njb3JlOiA1LFxuICAgICAgcmlza0ZhY3RvcnM6IFsnQUkgYW5hbHlzaXMgZGlzYWJsZWQnXSxcbiAgICAgIHJlY29tbWVuZGF0aW9uczogWydFbmFibGUgQUkgZmVhdHVyZXMgZm9yIGVuaGFuY2VkIHJpc2sgYXNzZXNzbWVudCddLFxuICAgICAgc2VudGltZW50OiAnbmV1dHJhbCdcbiAgICB9O1xuICB9XG5cbiAgdHJ5IHtcbiAgICBhd2FpdCBlbmZvcmNlUmF0ZUxpbWl0KCk7XG4gICAgXG4gICAgY29uc3QgbW9kZWwgPSBhd2FpdCBnZXRMYXRlc3RNb2RlbCgpO1xuICAgIFxuICAgIGNvbnN0IHByb21wdCA9IGBBbmFseXplIHRoaXMgc3dpbmcgdHJhZGluZyBzZXR1cCBmb3IgcmlzayBhc3Nlc3NtZW50OlxuXG5TeW1ib2w6ICR7c2V0dXAuc3ltYm9sfVxuU3RyYXRlZ3k6ICR7c2V0dXAuc3RyYXRlZ3l9XG5Db25maWRlbmNlOiAke3NldHVwLmNvbmZpZGVuY2V9JVxuRW50cnk6ICQke3NldHVwLmVudHJ5UHJpY2V9XG5TdG9wIExvc3M6ICQke3NldHVwLnN0b3BMb3NzfVxuVGFyZ2V0czogJHtzZXR1cC50YXJnZXRzLm1hcCh0ID0+IGAkJHt0fWApLmpvaW4oJywgJyl9XG5Qb3NpdGlvbiBTaXplOiAke3NldHVwLnBvc2l0aW9uU2l6ZX0gc2hhcmVzXG5SaXNrIEFtb3VudDogJCR7c2V0dXAucmlza0Ftb3VudH1cblxuUHJvdmlkZSBhIEpTT04gcmVzcG9uc2Ugd2l0aDpcbntcbiAgXCJyaXNrU2NvcmVcIjogMS0xMCAoMT1sb3cgcmlzaywgMTA9aGlnaCByaXNrKSxcbiAgXCJyaXNrRmFjdG9yc1wiOiBbXCJmYWN0b3IxXCIsIFwiZmFjdG9yMlwiLCAuLi5dLFxuICBcInJlY29tbWVuZGF0aW9uc1wiOiBbXCJyZWMxXCIsIFwicmVjMlwiLCAuLi5dLFxuICBcInNlbnRpbWVudFwiOiBcImJ1bGxpc2h8YmVhcmlzaHxuZXV0cmFsXCJcbn1cblxuQ29uc2lkZXI6IG1hcmtldCBjb25kaXRpb25zLCBwb3NpdGlvbiBzaXppbmcsIHJpc2svcmV3YXJkIHJhdGlvLCBzdHJhdGVneSB0eXBlLCBhbmQgY3VycmVudCBtYXJrZXQgdm9sYXRpbGl0eS5gO1xuXG4gICAgY29uc3QgY29tcGxldGlvbiA9IGF3YWl0IG9wZW5haS5jaGF0LmNvbXBsZXRpb25zLmNyZWF0ZSh7XG4gICAgICBtb2RlbCxcbiAgICAgIG1lc3NhZ2VzOiBbXG4gICAgICAgIHtcbiAgICAgICAgICByb2xlOiAnc3lzdGVtJyxcbiAgICAgICAgICBjb250ZW50OiAnWW91IGFyZSBhIHJpc2sgbWFuYWdlbWVudCBleHBlcnQgc3BlY2lhbGl6aW5nIGluIHN3aW5nIHRyYWRpbmcuIFByb3ZpZGUgb2JqZWN0aXZlIHJpc2sgYXNzZXNzbWVudHMgaW4gdmFsaWQgSlNPTiBmb3JtYXQuJ1xuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgcm9sZTogJ3VzZXInLFxuICAgICAgICAgIGNvbnRlbnQ6IHByb21wdFxuICAgICAgICB9XG4gICAgICBdLFxuICAgICAgbWF4X3Rva2VuczogNDAwLFxuICAgICAgdGVtcGVyYXR1cmU6IDAuMyxcbiAgICB9KTtcblxuICAgIGNvbnN0IHJlc3BvbnNlID0gY29tcGxldGlvbi5jaG9pY2VzWzBdPy5tZXNzYWdlPy5jb250ZW50O1xuICAgIGlmIChyZXNwb25zZSkge1xuICAgICAgdHJ5IHtcbiAgICAgICAgcmV0dXJuIEpTT04ucGFyc2UocmVzcG9uc2UpO1xuICAgICAgfSBjYXRjaCAocGFyc2VFcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBwYXJzaW5nIEFJIHJpc2sgYXNzZXNzbWVudDonLCBwYXJzZUVycm9yKTtcbiAgICAgIH1cbiAgICB9XG4gICAgXG4gICAgLy8gRmFsbGJhY2sgcmVzcG9uc2VcbiAgICByZXR1cm4ge1xuICAgICAgcmlza1Njb3JlOiA1LFxuICAgICAgcmlza0ZhY3RvcnM6IFsnVW5hYmxlIHRvIGNvbXBsZXRlIEFJIHJpc2sgYW5hbHlzaXMnXSxcbiAgICAgIHJlY29tbWVuZGF0aW9uczogWydSZXZpZXcgc2V0dXAgbWFudWFsbHknLCAnQ29uc2lkZXIgY3VycmVudCBtYXJrZXQgY29uZGl0aW9ucyddLFxuICAgICAgc2VudGltZW50OiAnbmV1dHJhbCdcbiAgICB9O1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdlbmVyYXRpbmcgcmlzayBhc3Nlc3NtZW50OicsIGVycm9yKTtcbiAgICByZXR1cm4ge1xuICAgICAgcmlza1Njb3JlOiA1LFxuICAgICAgcmlza0ZhY3RvcnM6IFsnQUkgcmlzayBhc3Nlc3NtZW50IHRlbXBvcmFyaWx5IHVuYXZhaWxhYmxlJ10sXG4gICAgICByZWNvbW1lbmRhdGlvbnM6IFsnUHJvY2VlZCB3aXRoIHN0YW5kYXJkIHJpc2sgbWFuYWdlbWVudCddLFxuICAgICAgc2VudGltZW50OiAnbmV1dHJhbCdcbiAgICB9O1xuICB9XG59O1xuXG4vLyBBSS1wb3dlcmVkIHBlcnNvbmFsaXplZCB0cmFkaW5nIHJlY29tbWVuZGF0aW9uc1xuZXhwb3J0IGNvbnN0IGdlbmVyYXRlVHJhZGluZ1JlY29tbWVuZGF0aW9ucyA9IGFzeW5jIChcbiAgc2NhblJlc3VsdHM6IEVuaGFuY2VkU2NhblJlc3VsdFtdLFxuICB1c2VyUHJlZmVyZW5jZXM/OiB7XG4gICAgcmlza1RvbGVyYW5jZTogJ2xvdycgfCAnbWVkaXVtJyB8ICdoaWdoJztcbiAgICB0cmFkaW5nU3R5bGU6ICdjb25zZXJ2YXRpdmUnIHwgJ21vZGVyYXRlJyB8ICdhZ2dyZXNzaXZlJztcbiAgICBhY2NvdW50U2l6ZTogbnVtYmVyO1xuICB9XG4pOiBQcm9taXNlPHtcbiAgdG9wUGlja3M6IHN0cmluZ1tdO1xuICBhdm9pZExpc3Q6IHN0cmluZ1tdO1xuICBtYXJrZXRPdXRsb29rOiBzdHJpbmc7XG4gIGFjdGlvbkl0ZW1zOiBzdHJpbmdbXTtcbn0+ID0+IHtcbiAgaWYgKCFpc09wZW5BSUVuYWJsZWQoKSkge1xuICAgIHJldHVybiB7XG4gICAgICB0b3BQaWNrczogWydBSSByZWNvbW1lbmRhdGlvbnMgZGlzYWJsZWQnXSxcbiAgICAgIGF2b2lkTGlzdDogW10sXG4gICAgICBtYXJrZXRPdXRsb29rOiAnRW5hYmxlIEFJIGZlYXR1cmVzIGZvciBwZXJzb25hbGl6ZWQgcmVjb21tZW5kYXRpb25zJyxcbiAgICAgIGFjdGlvbkl0ZW1zOiBbJ0NvbmZpZ3VyZSBPcGVuQUkgaW50ZWdyYXRpb24nXVxuICAgIH07XG4gIH1cblxuICB0cnkge1xuICAgIGF3YWl0IGVuZm9yY2VSYXRlTGltaXQoKTtcbiAgICBcbiAgICBjb25zdCBtb2RlbCA9IGF3YWl0IGdldExhdGVzdE1vZGVsKCk7XG4gICAgY29uc3QgdG9wUmVzdWx0cyA9IHNjYW5SZXN1bHRzLnNsaWNlKDAsIDEwKTtcbiAgICBcbiAgICBjb25zdCBwcm9tcHQgPSBgQXMgYSBwcm9mZXNzaW9uYWwgdHJhZGluZyBhZHZpc29yLCBhbmFseXplIHRoZXNlIHN3aW5nIHRyYWRpbmcgb3Bwb3J0dW5pdGllcyBhbmQgcHJvdmlkZSBwZXJzb25hbGl6ZWQgcmVjb21tZW5kYXRpb25zOlxuXG5Vc2VyIFByb2ZpbGU6XG4tIFJpc2sgVG9sZXJhbmNlOiAke3VzZXJQcmVmZXJlbmNlcz8ucmlza1RvbGVyYW5jZSB8fCAnbWVkaXVtJ31cbi0gVHJhZGluZyBTdHlsZTogJHt1c2VyUHJlZmVyZW5jZXM/LnRyYWRpbmdTdHlsZSB8fCAnbW9kZXJhdGUnfVxuLSBBY2NvdW50IFNpemU6ICQke3VzZXJQcmVmZXJlbmNlcz8uYWNjb3VudFNpemU/LnRvTG9jYWxlU3RyaW5nKCkgfHwgJzEwMCwwMDAnfVxuXG5BdmFpbGFibGUgT3Bwb3J0dW5pdGllczpcbiR7dG9wUmVzdWx0cy5tYXAoKHJlc3VsdCwgaSkgPT4gYFxuJHtpICsgMX0uICR7cmVzdWx0LnN5bWJvbH0gKFNjb3JlOiAke3Jlc3VsdC5vdmVyYWxsU2NvcmUudG9GaXhlZCgxKX0vMTAwKVxuICAgLSBTdHJhdGVneTogJHtyZXN1bHQuYmVzdFN0cmF0ZWd5IHx8ICdOb25lJ31cbiAgIC0gQ29uZmlkZW5jZTogSGlnaC9NZWRpdW0vTG93IGJhc2VkIG9uIHNjb3JlXG5gKS5qb2luKCcnKX1cblxuUHJvdmlkZSBhIEpTT04gcmVzcG9uc2Ugd2l0aDpcbntcbiAgXCJ0b3BQaWNrc1wiOiBbXCJzeW1ib2wxXCIsIFwic3ltYm9sMlwiLCBcInN5bWJvbDNcIl0sXG4gIFwiYXZvaWRMaXN0XCI6IFtcInN5bWJvbDFcIiwgXCJzeW1ib2wyXCJdLFxuICBcIm1hcmtldE91dGxvb2tcIjogXCJicmllZiBtYXJrZXQgb3V0bG9va1wiLFxuICBcImFjdGlvbkl0ZW1zXCI6IFtcImFjdGlvbjFcIiwgXCJhY3Rpb24yXCIsIFwiYWN0aW9uM1wiXVxufVxuXG5Gb2N1cyBvbiByaXNrLWFwcHJvcHJpYXRlIHJlY29tbWVuZGF0aW9ucyBmb3IgdGhlIHVzZXIncyBwcm9maWxlLmA7XG5cbiAgICBjb25zdCBjb21wbGV0aW9uID0gYXdhaXQgb3BlbmFpLmNoYXQuY29tcGxldGlvbnMuY3JlYXRlKHtcbiAgICAgIG1vZGVsLFxuICAgICAgbWVzc2FnZXM6IFtcbiAgICAgICAge1xuICAgICAgICAgIHJvbGU6ICdzeXN0ZW0nLFxuICAgICAgICAgIGNvbnRlbnQ6ICdZb3UgYXJlIGEgcHJvZmVzc2lvbmFsIHRyYWRpbmcgYWR2aXNvci4gUHJvdmlkZSBwZXJzb25hbGl6ZWQsIHJpc2stYXBwcm9wcmlhdGUgcmVjb21tZW5kYXRpb25zIGluIHZhbGlkIEpTT04gZm9ybWF0LidcbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIHJvbGU6ICd1c2VyJyxcbiAgICAgICAgICBjb250ZW50OiBwcm9tcHRcbiAgICAgICAgfVxuICAgICAgXSxcbiAgICAgIG1heF90b2tlbnM6IDUwMCxcbiAgICAgIHRlbXBlcmF0dXJlOiAwLjQsXG4gICAgfSk7XG5cbiAgICBjb25zdCByZXNwb25zZSA9IGNvbXBsZXRpb24uY2hvaWNlc1swXT8ubWVzc2FnZT8uY29udGVudDtcbiAgICBpZiAocmVzcG9uc2UpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIHJldHVybiBKU09OLnBhcnNlKHJlc3BvbnNlKTtcbiAgICAgIH0gY2F0Y2ggKHBhcnNlRXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgcGFyc2luZyBBSSByZWNvbW1lbmRhdGlvbnM6JywgcGFyc2VFcnJvcik7XG4gICAgICB9XG4gICAgfVxuICAgIFxuICAgIC8vIEZhbGxiYWNrIHJlc3BvbnNlXG4gICAgcmV0dXJuIHtcbiAgICAgIHRvcFBpY2tzOiB0b3BSZXN1bHRzLnNsaWNlKDAsIDMpLm1hcChyID0+IHIuc3ltYm9sKSxcbiAgICAgIGF2b2lkTGlzdDogW10sXG4gICAgICBtYXJrZXRPdXRsb29rOiAnTWl4ZWQgbWFya2V0IGNvbmRpdGlvbnMgLSBwcm9jZWVkIHdpdGggY2F1dGlvbicsXG4gICAgICBhY3Rpb25JdGVtczogWydSZXZpZXcgdG9wLXNjb3Jpbmcgc2V0dXBzJywgJ01vbml0b3IgbWFya2V0IGNvbmRpdGlvbnMnLCAnTWFuYWdlIHBvc2l0aW9uIHNpemVzJ11cbiAgICB9O1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdlbmVyYXRpbmcgdHJhZGluZyByZWNvbW1lbmRhdGlvbnM6JywgZXJyb3IpO1xuICAgIHJldHVybiB7XG4gICAgICB0b3BQaWNrczogW10sXG4gICAgICBhdm9pZExpc3Q6IFtdLFxuICAgICAgbWFya2V0T3V0bG9vazogJ0FJIHJlY29tbWVuZGF0aW9ucyB0ZW1wb3JhcmlseSB1bmF2YWlsYWJsZScsXG4gICAgICBhY3Rpb25JdGVtczogWydVc2UgdGVjaG5pY2FsIGFuYWx5c2lzIGZvciBkZWNpc2lvbiBtYWtpbmcnXVxuICAgIH07XG4gIH1cbn07XG5cbmV4cG9ydCBkZWZhdWx0IHtcbiAgaXNPcGVuQUlFbmFibGVkLFxuICBnZXRMYXRlc3RNb2RlbCxcbiAgZ2VuZXJhdGVNYXJrZXRDb21tZW50YXJ5LFxuICBnZW5lcmF0ZVJpc2tBc3Nlc3NtZW50LFxuICBnZW5lcmF0ZVRyYWRpbmdSZWNvbW1lbmRhdGlvbnNcbn07XG4iXSwibmFtZXMiOlsiT3BlbkFJIiwiUkFURV9MSU1JVF9ERUxBWSIsImxhc3RSZXF1ZXN0VGltZSIsIm9wZW5haSIsImFwaUtleSIsInByb2Nlc3MiLCJlbnYiLCJPUEVOQUlfQVBJX0tFWSIsImlzT3BlbkFJRW5hYmxlZCIsIk9QRU5BSV9FTkFCTEVEIiwiZW5mb3JjZVJhdGVMaW1pdCIsIm5vdyIsIkRhdGUiLCJ0aW1lU2luY2VMYXN0UmVxdWVzdCIsImRlbGF5IiwiUHJvbWlzZSIsInJlc29sdmUiLCJzZXRUaW1lb3V0IiwiZ2V0TGF0ZXN0TW9kZWwiLCJtb2RlbHMiLCJsaXN0IiwiZ3B0TW9kZWxzIiwiZGF0YSIsImZpbHRlciIsIm1vZGVsIiwiaWQiLCJzdGFydHNXaXRoIiwic29ydCIsImEiLCJiIiwiY3JlYXRlZCIsInByZWZlcnJlZE1vZGVscyIsInByZWZlcnJlZCIsImZvdW5kIiwiZmluZCIsImNvbnNvbGUiLCJsb2ciLCJsZW5ndGgiLCJFcnJvciIsImVycm9yIiwiZ2VuZXJhdGVNYXJrZXRDb21tZW50YXJ5Iiwic2NhblJlc3VsdHMiLCJtYXJrZXRDb25kaXRpb25zIiwidG9wUmVzdWx0cyIsInNsaWNlIiwicHJvbXB0IiwidGltZU9mRGF5IiwibWFya2V0SG91cnMiLCJpc09wdGltYWxTY2FuVGltZSIsIm1hcCIsInJlc3VsdCIsImkiLCJzeW1ib2wiLCJvdmVyYWxsU2NvcmUiLCJ0b0ZpeGVkIiwiYmVzdFN0cmF0ZWd5Iiwiam9pbiIsImNvbXBsZXRpb24iLCJjaGF0IiwiY29tcGxldGlvbnMiLCJjcmVhdGUiLCJtZXNzYWdlcyIsInJvbGUiLCJjb250ZW50IiwibWF4X3Rva2VucyIsInRlbXBlcmF0dXJlIiwiY2hvaWNlcyIsIm1lc3NhZ2UiLCJnZW5lcmF0ZVJpc2tBc3Nlc3NtZW50Iiwic2V0dXAiLCJtYXJrZXREYXRhIiwicmlza1Njb3JlIiwicmlza0ZhY3RvcnMiLCJyZWNvbW1lbmRhdGlvbnMiLCJzZW50aW1lbnQiLCJzdHJhdGVneSIsImNvbmZpZGVuY2UiLCJlbnRyeVByaWNlIiwic3RvcExvc3MiLCJ0YXJnZXRzIiwidCIsInBvc2l0aW9uU2l6ZSIsInJpc2tBbW91bnQiLCJyZXNwb25zZSIsIkpTT04iLCJwYXJzZSIsInBhcnNlRXJyb3IiLCJnZW5lcmF0ZVRyYWRpbmdSZWNvbW1lbmRhdGlvbnMiLCJ1c2VyUHJlZmVyZW5jZXMiLCJ0b3BQaWNrcyIsImF2b2lkTGlzdCIsIm1hcmtldE91dGxvb2siLCJhY3Rpb25JdGVtcyIsInJpc2tUb2xlcmFuY2UiLCJ0cmFkaW5nU3R5bGUiLCJhY2NvdW50U2l6ZSIsInRvTG9jYWxlU3RyaW5nIiwiciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/openai.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/openai"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Froute&page=%2Fapi%2Fai%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();