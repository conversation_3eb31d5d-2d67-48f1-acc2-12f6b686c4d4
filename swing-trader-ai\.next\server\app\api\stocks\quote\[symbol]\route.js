var R=require("../../../../../chunks/[turbopack]_runtime.js")("server/app/api/stocks/quote/[symbol]/route.js")
R.c("server/chunks/6bf44_6ff4e58a._.js")
R.c("server/chunks/[root-of-the-server]__3a23a990._.js")
R.m("[project]/swing-trader-ai/.next-internal/server/app/api/stocks/quote/[symbol]/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/swing-trader-ai/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/swing-trader-ai/src/app/api/stocks/quote/[symbol]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/swing-trader-ai/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/swing-trader-ai/src/app/api/stocks/quote/[symbol]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
