module.exports=[18622,(e,r,t)=>{r.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,r,t)=>{r.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},70406,(e,r,t)=>{r.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,r,t)=>{r.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},24361,(e,r,t)=>{r.exports=e.x("util",()=>require("util"))},32319,(e,r,t)=>{r.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},24725,(e,r,t)=>{r.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},20635,(e,r,t)=>{r.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},14747,(e,r,t)=>{r.exports=e.x("path",()=>require("path"))},78006,e=>{"use strict";e.s(["PolygonAPI",()=>s]);var r=e.i(55362);let t="https://api.polygon.io",a=process.env.POLYGON_API_KEY;class s{apiKey;constructor(e){if(this.apiKey=e||a||"",!this.apiKey)throw Error("Polygon API key is required")}async getStockQuote(e){try{let a=(await r.default.get(`${t}/v2/snapshot/locale/us/markets/stocks/tickers/${e}`,{params:{apikey:this.apiKey}})).data.results[0];if(!a)throw Error(`No data found for ${e}`);let s=a.value||a,o=s.day||{},n=s.prevDay||{};s.lastQuote;let i=(s.lastTrade||{}).p||o.c||n.c,c=n.c||o.o,l=i-c;return{symbol:e.toUpperCase(),name:s.name||e.toUpperCase(),price:i,change:l,changePercent:l/c*100,volume:o.v||0,marketCap:s.market_cap,pe:void 0,dividend:void 0}}catch(a){console.error("Error fetching stock quote from Polygon:",a);try{let a=(await r.default.get(`${t}/v2/aggs/ticker/${e}/prev`,{params:{adjusted:"true",apikey:this.apiKey}})).data.results[0];return{symbol:e.toUpperCase(),name:e.toUpperCase(),price:a.c,change:a.c-a.o,changePercent:(a.c-a.o)/a.o*100,volume:a.v,marketCap:void 0,pe:void 0,dividend:void 0}}catch(r){throw console.error("Polygon fallback also failed:",r),Error(`Failed to fetch quote for ${e}`)}}}async getHistoricalData(e,a="day",s=1,o,n){try{let i=await r.default.get(`${t}/v2/aggs/ticker/${e}/range/${s}/${a}/${o}/${n}`,{params:{adjusted:"true",sort:"asc",limit:5e4,apikey:this.apiKey}});if(!i.data.results||0===i.data.results.length)return console.warn(`No historical data found for ${e}`),[];return i.data.results.map(e=>({timestamp:e.t,open:e.o,high:e.h,low:e.l,close:e.c,volume:e.v}))}catch(r){throw console.error(`Error fetching historical data for ${e}:`,r),r.response&&(console.error(`Polygon API Error: ${r.response.status} - ${r.response.statusText}`),console.error("Response data:",r.response.data)),Error(`Failed to fetch historical data for ${e}: ${r.message}`)}}async getCompanyDetails(e){try{return(await r.default.get(`${t}/v3/reference/tickers/${e}`,{params:{apikey:this.apiKey}})).data.results}catch(e){return console.error("Error fetching company details:",e),null}}async getMarketStatus(){try{return(await r.default.get(`${t}/v1/marketstatus/now`,{params:{apikey:this.apiKey}})).data}catch(e){return console.error("Error fetching market status:",e),null}}async searchStocks(e,a=10){try{return(await r.default.get(`${t}/v3/reference/tickers`,{params:{search:e,market:"stocks",active:"true",limit:a,apikey:this.apiKey}})).data.results||[]}catch(e){return console.error("Error searching stocks:",e),[]}}}new s},29547,e=>{"use strict";e.s(["FMPAPI",()=>s]);var r=e.i(55362);let t="https://financialmodelingprep.com/api",a=process.env.FMP_API_KEY;class s{apiKey;constructor(e){if(this.apiKey=e||a||"",!this.apiKey)throw Error("FMP API key is required")}async getStockQuote(e){try{let a=(await r.default.get(`${t}/v3/quote/${e}`,{params:{apikey:this.apiKey}})).data[0];if(!a)throw Error(`No data found for symbol ${e}`);return{symbol:a.symbol,name:a.name||a.symbol,price:a.price,change:a.change,changePercent:a.changesPercentage,volume:a.volume,marketCap:a.marketCap,pe:a.pe,dividend:void 0}}catch(r){throw console.error("Error fetching FMP stock quote:",r),Error(`Failed to fetch quote for ${e}`)}}async getCompanyProfile(e){try{return(await r.default.get(`${t}/v3/profile/${e}`,{params:{apikey:this.apiKey}})).data[0]}catch(e){return console.error("Error fetching company profile:",e),null}}async getFinancialRatios(e){try{return(await r.default.get(`${t}/v3/ratios/${e}`,{params:{apikey:this.apiKey}})).data[0]}catch(e){return console.error("Error fetching financial ratios:",e),null}}async getKeyMetrics(e){try{return(await r.default.get(`${t}/v3/key-metrics/${e}`,{params:{apikey:this.apiKey}})).data[0]}catch(e){return console.error("Error fetching key metrics:",e),null}}async getAnalystRecommendations(e){try{return(await r.default.get(`${t}/v3/analyst-stock-recommendations/${e}`,{params:{apikey:this.apiKey}})).data}catch(e){return console.error("Error fetching analyst recommendations:",e),[]}}async getEarningsCalendar(e,a){try{let s={apikey:this.apiKey};return e&&(s.from=e),a&&(s.to=a),(await r.default.get(`${t}/v3/earning_calendar`,{params:s})).data}catch(e){return console.error("Error fetching earnings calendar:",e),[]}}async getEconomicCalendar(e,a){try{let s={apikey:this.apiKey};return e&&(s.from=e),a&&(s.to=a),(await r.default.get(`${t}/v3/economic_calendar`,{params:s})).data}catch(e){return console.error("Error fetching economic calendar:",e),[]}}async searchStocks(e,a=10){try{return(await r.default.get(`${t}/v3/search`,{params:{query:e,limit:a,apikey:this.apiKey}})).data}catch(e){return console.error("Error searching stocks:",e),[]}}async getSectorPerformance(){try{return(await r.default.get(`${t}/v3/sector-performance`,{params:{apikey:this.apiKey}})).data}catch(e){return console.error("Error fetching sector performance:",e),[]}}async getMarketMovers(e){try{return(await r.default.get(`${t}/v3/stock_market/${e}`,{params:{apikey:this.apiKey}})).data}catch(r){return console.error(`Error fetching market ${e}:`,r),[]}}async getEarningsCalendar(e,a=30){try{let s=new Date;s.setDate(s.getDate()-a);let o=new Date;return(await r.default.get(`${t}/v3/earning_calendar`,{params:{apikey:this.apiKey,from:s.toISOString().split("T")[0],to:o.toISOString().split("T")[0],...e&&{symbol:e.toUpperCase()}}})).data||[]}catch(e){return console.error("Error fetching earnings calendar:",e),[]}}async getStockNews(e,a=50){try{return(await r.default.get(`${t}/v3/stock_news`,{params:{apikey:this.apiKey,tickers:e.toUpperCase(),limit:a}})).data||[]}catch(e){return console.error("Error fetching stock news:",e),[]}}async getAnalystRecommendations(e,a=30){try{return(await r.default.get(`${t}/v3/analyst-stock-recommendations/${e.toUpperCase()}`,{params:{apikey:this.apiKey,limit:a}})).data||[]}catch(e){return console.error("Error fetching analyst recommendations:",e),[]}}async getInsiderTrading(e,a=30){try{let s=await r.default.get(`${t}/v4/insider-trading`,{params:{apikey:this.apiKey,symbol:e.toUpperCase(),limit:5*a}}),o=new Date;return o.setDate(o.getDate()-a),(s.data||[]).filter(e=>new Date(e.filingDate)>=o)}catch(e){return console.error("Error fetching insider trading:",e),[]}}async getSECFilings(e,a=30){try{let s=await r.default.get(`${t}/v3/sec_filings/${e.toUpperCase()}`,{params:{apikey:this.apiKey,limit:2*a}}),o=new Date;return o.setDate(o.getDate()-a),(s.data||[]).filter(e=>new Date(e.filedDate)>=o)}catch(e){return console.error("Error fetching SEC filings:",e),[]}}async getPreMarketQuote(e){try{let a=(await r.default.get(`${t}/v3/quote/${e.toUpperCase()}`,{params:{apikey:this.apiKey}})).data[0];if(!a)return null;return{symbol:a.symbol,price:a.price,previousClose:a.previousClose,change:a.change,changePercent:a.changesPercentage,volume:a.volume,marketCap:a.marketCap,avgVolume:a.avgVolume,preMarketPrice:a.preMarketPrice||a.price,preMarketChange:a.preMarketChange||a.change,preMarketChangePercent:a.preMarketChangePercent||a.changesPercentage}}catch(e){return console.error("Error fetching pre-market quote:",e),null}}async getMultiplePreMarketQuotes(e){try{let a=e.map(e=>e.toUpperCase()).join(",");return((await r.default.get(`${t}/v3/quote/${a}`,{params:{apikey:this.apiKey}})).data||[]).map(e=>({symbol:e.symbol,price:e.price,previousClose:e.previousClose,change:e.change,changePercent:e.changesPercentage,volume:e.volume,marketCap:e.marketCap,avgVolume:e.avgVolume,preMarketPrice:e.preMarketPrice||e.price,preMarketChange:e.preMarketChange||e.change,preMarketChangePercent:e.preMarketChangePercent||e.changesPercentage}))}catch(e){return console.error("Error fetching multiple pre-market quotes:",e),[]}}async getCompanyProfile(e){try{return(await r.default.get(`${t}/v3/profile/${e.toUpperCase()}`,{params:{apikey:this.apiKey}})).data[0]||null}catch(e){return console.error("Error fetching company profile:",e),null}}}new s},58445,e=>{"use strict";e.s(["TechnicalIndicators",()=>r]);class r{static sma(e,r){let t=[];for(let a=r-1;a<e.length;a++){let s=e.slice(a-r+1,a+1).reduce((e,r)=>e+r,0);t.push(s/r)}return t}static ema(e,r){let t=[],a=2/(r+1),s=e.slice(0,r).reduce((e,r)=>e+r,0)/r;t.push(s);for(let o=r;o<e.length;o++)t.push(s=e[o]*a+s*(1-a));return t}static rsi(e,r=14){let t=[],a=[];for(let r=1;r<e.length;r++){let s=e[r]-e[r-1];t.push(s>0?s:0),a.push(s<0?Math.abs(s):0)}let s=this.sma(t,r),o=this.sma(a,r);return s.map((e,r)=>100-100/(1+e/o[r]))}static macd(e,r=12,t=26,a=9){let s=this.ema(e,r),o=this.ema(e,t),n=s.slice(t-r).map((e,r)=>e-o[r]),i=this.ema(n,a),c=n.slice(a-1).map((e,r)=>e-i[r]);return{macd:n,signal:i,histogram:c}}static bollingerBands(e,r=20,t=2){return this.sma(e,r).map((a,s)=>{let o=Math.sqrt(e.slice(s,s+r).reduce((e,r)=>e+Math.pow(r-a,2),0)/r);return{upper:a+o*t,middle:a,lower:a-o*t}})}static findSupportResistance(e,r=20){let t=e.map(e=>e.high),a=e.map(e=>e.low),s=[],o=[];for(let n=r;n<e.length-r;n++){let e=t[n],i=a[n],c=t.slice(n-r,n).every(r=>r<=e)&&t.slice(n+1,n+r+1).every(r=>r<=e),l=a.slice(n-r,n).every(e=>e>=i)&&a.slice(n+1,n+r+1).every(e=>e>=i);c&&s.push(e),l&&o.push(i)}return{support:o,resistance:s}}static volumeAnalysis(e,r=20){let t=e.map(e=>e.volume),a=this.sma(t,r),s=t[t.length-1],o=a[a.length-1];return{currentVolume:s,averageVolume:o,volumeRatio:s/o,isHighVolume:s>1.5*o,isLowVolume:s<.5*o}}static analyzeSwingSetup(e){let r=e.map(e=>e.close),t=[],a=this.rsi(r),s=a[a.length-1],o="NEUTRAL",n=`RSI: ${s.toFixed(2)}`;s<30?(o="BUY",n+=" - Oversold condition, potential bounce"):s>70?(o="SELL",n+=" - Overbought condition, potential pullback"):n+=" - Neutral zone",t.push({name:"RSI",value:s,signal:o,description:n});let i=this.sma(r,20),c=this.sma(r,50),l=r[r.length-1],p=i[i.length-1],u=c[c.length-1],g="NEUTRAL",h=`Price vs SMA20: ${((l/p-1)*100).toFixed(2)}%`;l>p&&p>u?(g="BUY",h+=" - Bullish trend"):l<p&&p<u?(g="SELL",h+=" - Bearish trend"):h+=" - Mixed signals",t.push({name:"Moving Averages",value:(l/p-1)*100,signal:g,description:h});let m=this.macd(r),d=m.macd[m.macd.length-1],y=m.signal[m.signal.length-1],f=m.histogram[m.histogram.length-1],v="NEUTRAL",k=`MACD: ${d.toFixed(4)}, Signal: ${y.toFixed(4)}`;d>y&&f>0?(v="BUY",k+=" - Bullish momentum"):d<y&&f<0?(v="SELL",k+=" - Bearish momentum"):k+=" - Momentum shifting",t.push({name:"MACD",value:f,signal:v,description:k});let $=this.volumeAnalysis(e),w="NEUTRAL",x=`Volume: ${(100*$.volumeRatio).toFixed(0)}% of average`;return $.isHighVolume?(w="BUY",x+=" - High volume confirms move"):$.isLowVolume?(w="SELL",x+=" - Low volume, weak conviction"):x+=" - Normal volume",t.push({name:"Volume",value:$.volumeRatio,signal:w,description:x}),t}}},52784,(e,r,t)=>{}];

//# sourceMappingURL=%5Broot-of-the-server%5D__d605489d._.js.map