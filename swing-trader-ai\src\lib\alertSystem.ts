import { PerfectPickSetup, PreMarketGapScan, Catalyst } from '@/types/trading'

export type AlertType = 
  | 'new_catalyst'
  | 'perfect_pick_found'
  | 'pre_market_gap'
  | 'pmh_break'
  | 'stop_loss_hit'
  | 'profit_target_hit'
  | 'entry_trigger'
  | 'volume_spike'

export interface Alert {
  id: string
  type: AlertType
  symbol: string
  title: string
  message: string
  priority: 'low' | 'medium' | 'high' | 'critical'
  timestamp: string
  data?: any
  read: boolean
  actionable: boolean
  actions?: AlertAction[]
}

export interface AlertAction {
  id: string
  label: string
  type: 'execute_trade' | 'view_chart' | 'update_stop' | 'take_profit' | 'dismiss'
  data?: any
}

export class AlertSystem {
  private alerts: Alert[] = []
  private subscribers: ((alerts: Alert[]) => void)[] = []

  constructor() {
    // Alert system initialized without audio
  }

  /**
   * Subscribe to alert updates
   */
  subscribe(callback: (alerts: Alert[]) => void): () => void {
    this.subscribers.push(callback)
    
    // Return unsubscribe function
    return () => {
      const index = this.subscribers.indexOf(callback)
      if (index > -1) {
        this.subscribers.splice(index, 1)
      }
    }
  }

  /**
   * Add a new alert
   */
  addAlert(alert: Omit<Alert, 'id' | 'timestamp' | 'read'>): void {
    const newAlert: Alert = {
      ...alert,
      id: this.generateAlertId(),
      timestamp: new Date().toISOString(),
      read: false
    }

    this.alerts.unshift(newAlert) // Add to beginning of array
    
    // Keep only last 100 alerts
    if (this.alerts.length > 100) {
      this.alerts = this.alerts.slice(0, 100)
    }

    // Show browser notification for critical alerts
    if (newAlert.priority === 'critical') {
      this.showBrowserNotification(newAlert)
    }

    this.notifySubscribers()
  }

  /**
   * Mark alert as read
   */
  markAsRead(alertId: string): void {
    const alert = this.alerts.find(a => a.id === alertId)
    if (alert) {
      alert.read = true
      this.notifySubscribers()
    }
  }

  /**
   * Mark all alerts as read
   */
  markAllAsRead(): void {
    this.alerts.forEach(alert => alert.read = true)
    this.notifySubscribers()
  }

  /**
   * Remove alert
   */
  removeAlert(alertId: string): void {
    this.alerts = this.alerts.filter(a => a.id !== alertId)
    this.notifySubscribers()
  }

  /**
   * Clear all alerts
   */
  clearAllAlerts(): void {
    this.alerts = []
    this.notifySubscribers()
  }

  /**
   * Get all alerts
   */
  getAlerts(): Alert[] {
    return [...this.alerts]
  }

  /**
   * Get unread alerts count
   */
  getUnreadCount(): number {
    return this.alerts.filter(a => !a.read).length
  }

  /**
   * Create catalyst alert
   */
  createCatalystAlert(catalyst: Catalyst): void {
    const priority = catalyst.tier === 'tier_1' ? 'high' : 
                    catalyst.tier === 'tier_2' ? 'medium' : 'low'

    this.addAlert({
      type: 'new_catalyst',
      symbol: catalyst.symbol,
      title: `New ${catalyst.tier.replace('_', ' ').toUpperCase()} Catalyst: ${catalyst.symbol}`,
      message: catalyst.title,
      priority,
      data: { catalyst },
      actionable: true,
      actions: [
        {
          id: 'view_details',
          label: 'View Details',
          type: 'view_chart',
          data: { symbol: catalyst.symbol }
        }
      ]
    })
  }

  /**
   * Create Perfect-Pick alert
   */
  createPerfectPickAlert(setup: PerfectPickSetup): void {
    this.addAlert({
      type: 'perfect_pick_found',
      symbol: setup.symbol,
      title: `Perfect-Pick Setup Found: ${setup.symbol}`,
      message: `Grade ${setup.setupGrade} setup with ${setup.catalyst.type.replace(/_/g, ' ')} catalyst`,
      priority: setup.setupGrade.startsWith('A') ? 'high' : 'medium',
      data: { setup },
      actionable: true,
      actions: [
        {
          id: 'execute_trade',
          label: 'Execute Trade',
          type: 'execute_trade',
          data: { setup }
        },
        {
          id: 'view_chart',
          label: 'View Chart',
          type: 'view_chart',
          data: { symbol: setup.symbol }
        }
      ]
    })
  }

  /**
   * Create pre-market gap alert
   */
  createGapAlert(gapScan: PreMarketGapScan): void {
    const priority = gapScan.gapPercent > 10 ? 'high' : 
                    gapScan.gapPercent > 5 ? 'medium' : 'low'

    this.addAlert({
      type: 'pre_market_gap',
      symbol: gapScan.symbol,
      title: `Pre-Market Gap: ${gapScan.symbol}`,
      message: `${gapScan.gapPercent.toFixed(1)}% gap with ${gapScan.catalyst ? 'catalyst' : 'no catalyst'}`,
      priority,
      data: { gapScan },
      actionable: gapScan.meetsAllCriteria,
      actions: gapScan.meetsAllCriteria ? [
        {
          id: 'analyze_setup',
          label: 'Analyze Setup',
          type: 'view_chart',
          data: { symbol: gapScan.symbol }
        }
      ] : undefined
    })
  }

  /**
   * Create PMH break alert
   */
  createPMHBreakAlert(symbol: string, currentPrice: number, pmh: number): void {
    this.addAlert({
      type: 'pmh_break',
      symbol,
      title: `PMH Break: ${symbol}`,
      message: `Price ${currentPrice.toFixed(2)} broke above PMH ${pmh.toFixed(2)}`,
      priority: 'high',
      data: { currentPrice, pmh },
      actionable: true,
      actions: [
        {
          id: 'execute_entry',
          label: 'Execute Entry',
          type: 'execute_trade',
          data: { symbol, entryPrice: currentPrice }
        }
      ]
    })
  }

  /**
   * Create stop loss alert
   */
  createStopLossAlert(symbol: string, currentPrice: number, stopLoss: number): void {
    this.addAlert({
      type: 'stop_loss_hit',
      symbol,
      title: `Stop Loss Hit: ${symbol}`,
      message: `Price ${currentPrice.toFixed(2)} hit stop loss ${stopLoss.toFixed(2)}`,
      priority: 'critical',
      data: { currentPrice, stopLoss },
      actionable: true,
      actions: [
        {
          id: 'exit_position',
          label: 'Exit Position',
          type: 'execute_trade',
          data: { symbol, action: 'sell', price: currentPrice }
        }
      ]
    })
  }

  /**
   * Create profit target alert
   */
  createProfitTargetAlert(symbol: string, currentPrice: number, target: number, rLevel: number): void {
    this.addAlert({
      type: 'profit_target_hit',
      symbol,
      title: `Profit Target Hit: ${symbol}`,
      message: `Price ${currentPrice.toFixed(2)} hit ${rLevel}R target ${target.toFixed(2)}`,
      priority: 'high',
      data: { currentPrice, target, rLevel },
      actionable: true,
      actions: [
        {
          id: 'take_profit',
          label: 'Take Profit',
          type: 'take_profit',
          data: { symbol, price: currentPrice, rLevel }
        }
      ]
    })
  }

  /**
   * Create volume spike alert
   */
  createVolumeSpikeAlert(symbol: string, currentVolume: number, avgVolume: number): void {
    const volumeRatio = currentVolume / avgVolume
    
    this.addAlert({
      type: 'volume_spike',
      symbol,
      title: `Volume Spike: ${symbol}`,
      message: `Volume ${volumeRatio.toFixed(1)}x above average`,
      priority: volumeRatio > 5 ? 'high' : 'medium',
      data: { currentVolume, avgVolume, volumeRatio },
      actionable: false
    })
  }

  /**
   * Monitor Perfect-Pick setups for alerts
   */
  monitorPerfectPickSetups(setups: PerfectPickSetup[]): void {
    setups.forEach(setup => {
      // Check if this is a new setup (not already alerted)
      const existingAlert = this.alerts.find(
        a => a.type === 'perfect_pick_found' && 
             a.symbol === setup.symbol && 
             a.data?.setup?.createdAt === setup.createdAt
      )

      if (!existingAlert && setup.overallScore >= 80) {
        this.createPerfectPickAlert(setup)
      }
    })
  }

  /**
   * Monitor gap scans for alerts
   */
  monitorGapScans(gapScans: PreMarketGapScan[]): void {
    gapScans.forEach(gapScan => {
      // Check if this is a new gap (not already alerted)
      const existingAlert = this.alerts.find(
        a => a.type === 'pre_market_gap' && 
             a.symbol === gapScan.symbol &&
             Math.abs(new Date(a.timestamp).getTime() - new Date(gapScan.scanTime).getTime()) < 60000 // Within 1 minute
      )

      if (!existingAlert && gapScan.gapPercent >= 3) {
        this.createGapAlert(gapScan)
      }
    })
  }

  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private notifySubscribers(): void {
    this.subscribers.forEach(callback => callback([...this.alerts]))
  }



  private showBrowserNotification(alert: Alert): void {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(alert.title, {
        body: alert.message,
        icon: '/favicon.ico',
        tag: alert.symbol // Prevent duplicate notifications for same symbol
      })
    } else if ('Notification' in window && Notification.permission !== 'denied') {
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          new Notification(alert.title, {
            body: alert.message,
            icon: '/favicon.ico',
            tag: alert.symbol
          })
        }
      })
    }
  }
}

// Create singleton instance
export const alertSystem = new AlertSystem()
