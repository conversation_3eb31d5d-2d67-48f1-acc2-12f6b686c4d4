module.exports = [
"[project]/swing-trader-ai/.next-internal/server/app/api/scanner/strategies/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[project]/swing-trader-ai/src/lib/indicators.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "TechnicalIndicators",
    ()=>TechnicalIndicators
]);
class TechnicalIndicators {
    // Simple Moving Average
    static sma(data, period) {
        const result = [];
        for(let i = period - 1; i < data.length; i++){
            const sum = data.slice(i - period + 1, i + 1).reduce((a, b)=>a + b, 0);
            result.push(sum / period);
        }
        return result;
    }
    // Exponential Moving Average
    static ema(data, period) {
        const result = [];
        const multiplier = 2 / (period + 1);
        // Start with SMA for first value
        let ema = data.slice(0, period).reduce((a, b)=>a + b, 0) / period;
        result.push(ema);
        for(let i = period; i < data.length; i++){
            ema = data[i] * multiplier + ema * (1 - multiplier);
            result.push(ema);
        }
        return result;
    }
    // Relative Strength Index
    static rsi(data, period = 14) {
        const gains = [];
        const losses = [];
        for(let i = 1; i < data.length; i++){
            const change = data[i] - data[i - 1];
            gains.push(change > 0 ? change : 0);
            losses.push(change < 0 ? Math.abs(change) : 0);
        }
        const avgGains = this.sma(gains, period);
        const avgLosses = this.sma(losses, period);
        return avgGains.map((gain, i)=>{
            const rs = gain / avgLosses[i];
            return 100 - 100 / (1 + rs);
        });
    }
    // MACD (Moving Average Convergence Divergence)
    static macd(data, fastPeriod = 12, slowPeriod = 26, signalPeriod = 9) {
        const fastEMA = this.ema(data, fastPeriod);
        const slowEMA = this.ema(data, slowPeriod);
        // Align arrays (slowEMA starts later)
        const startIndex = slowPeriod - fastPeriod;
        const macdLine = fastEMA.slice(startIndex).map((fast, i)=>fast - slowEMA[i]);
        const signalLine = this.ema(macdLine, signalPeriod);
        const histogram = macdLine.slice(signalPeriod - 1).map((macd, i)=>macd - signalLine[i]);
        return {
            macd: macdLine,
            signal: signalLine,
            histogram
        };
    }
    // Bollinger Bands
    static bollingerBands(data, period = 20, stdDev = 2) {
        const sma = this.sma(data, period);
        const bands = sma.map((avg, i)=>{
            const slice = data.slice(i, i + period);
            const variance = slice.reduce((sum, val)=>sum + Math.pow(val - avg, 2), 0) / period;
            const standardDeviation = Math.sqrt(variance);
            return {
                upper: avg + standardDeviation * stdDev,
                middle: avg,
                lower: avg - standardDeviation * stdDev
            };
        });
        return bands;
    }
    // Support and Resistance Levels
    static findSupportResistance(candles, lookback = 20) {
        const highs = candles.map((c)=>c.high);
        const lows = candles.map((c)=>c.low);
        const resistance = [];
        const support = [];
        for(let i = lookback; i < candles.length - lookback; i++){
            const currentHigh = highs[i];
            const currentLow = lows[i];
            // Check if current high is a local maximum
            const isResistance = highs.slice(i - lookback, i).every((h)=>h <= currentHigh) && highs.slice(i + 1, i + lookback + 1).every((h)=>h <= currentHigh);
            // Check if current low is a local minimum
            const isSupport = lows.slice(i - lookback, i).every((l)=>l >= currentLow) && lows.slice(i + 1, i + lookback + 1).every((l)=>l >= currentLow);
            if (isResistance) resistance.push(currentHigh);
            if (isSupport) support.push(currentLow);
        }
        return {
            support,
            resistance
        };
    }
    // Volume analysis
    static volumeAnalysis(candles, period = 20) {
        const volumes = candles.map((c)=>c.volume);
        const avgVolume = this.sma(volumes, period);
        const currentVolume = volumes[volumes.length - 1];
        const currentAvgVolume = avgVolume[avgVolume.length - 1];
        return {
            currentVolume,
            averageVolume: currentAvgVolume,
            volumeRatio: currentVolume / currentAvgVolume,
            isHighVolume: currentVolume > currentAvgVolume * 1.5,
            isLowVolume: currentVolume < currentAvgVolume * 0.5
        };
    }
    // Swing Trading Analysis
    static analyzeSwingSetup(candles) {
        const closes = candles.map((c)=>c.close);
        const indicators = [];
        // RSI Analysis
        const rsi = this.rsi(closes);
        const currentRSI = rsi[rsi.length - 1];
        let rsiSignal = 'NEUTRAL';
        let rsiDescription = `RSI: ${currentRSI.toFixed(2)}`;
        if (currentRSI < 30) {
            rsiSignal = 'BUY';
            rsiDescription += ' - Oversold condition, potential bounce';
        } else if (currentRSI > 70) {
            rsiSignal = 'SELL';
            rsiDescription += ' - Overbought condition, potential pullback';
        } else {
            rsiDescription += ' - Neutral zone';
        }
        indicators.push({
            name: 'RSI',
            value: currentRSI,
            signal: rsiSignal,
            description: rsiDescription
        });
        // Moving Average Analysis
        const sma20 = this.sma(closes, 20);
        const sma50 = this.sma(closes, 50);
        const currentPrice = closes[closes.length - 1];
        const currentSMA20 = sma20[sma20.length - 1];
        const currentSMA50 = sma50[sma50.length - 1];
        let maSignal = 'NEUTRAL';
        let maDescription = `Price vs SMA20: ${((currentPrice / currentSMA20 - 1) * 100).toFixed(2)}%`;
        if (currentPrice > currentSMA20 && currentSMA20 > currentSMA50) {
            maSignal = 'BUY';
            maDescription += ' - Bullish trend';
        } else if (currentPrice < currentSMA20 && currentSMA20 < currentSMA50) {
            maSignal = 'SELL';
            maDescription += ' - Bearish trend';
        } else {
            maDescription += ' - Mixed signals';
        }
        indicators.push({
            name: 'Moving Averages',
            value: (currentPrice / currentSMA20 - 1) * 100,
            signal: maSignal,
            description: maDescription
        });
        // MACD Analysis
        const macdData = this.macd(closes);
        const currentMACD = macdData.macd[macdData.macd.length - 1];
        const currentSignal = macdData.signal[macdData.signal.length - 1];
        const currentHistogram = macdData.histogram[macdData.histogram.length - 1];
        let macdSignal = 'NEUTRAL';
        let macdDescription = `MACD: ${currentMACD.toFixed(4)}, Signal: ${currentSignal.toFixed(4)}`;
        if (currentMACD > currentSignal && currentHistogram > 0) {
            macdSignal = 'BUY';
            macdDescription += ' - Bullish momentum';
        } else if (currentMACD < currentSignal && currentHistogram < 0) {
            macdSignal = 'SELL';
            macdDescription += ' - Bearish momentum';
        } else {
            macdDescription += ' - Momentum shifting';
        }
        indicators.push({
            name: 'MACD',
            value: currentHistogram,
            signal: macdSignal,
            description: macdDescription
        });
        // Volume Analysis
        const volumeData = this.volumeAnalysis(candles);
        let volumeSignal = 'NEUTRAL';
        let volumeDescription = `Volume: ${(volumeData.volumeRatio * 100).toFixed(0)}% of average`;
        if (volumeData.isHighVolume) {
            volumeSignal = 'BUY';
            volumeDescription += ' - High volume confirms move';
        } else if (volumeData.isLowVolume) {
            volumeSignal = 'SELL';
            volumeDescription += ' - Low volume, weak conviction';
        } else {
            volumeDescription += ' - Normal volume';
        }
        indicators.push({
            name: 'Volume',
            value: volumeData.volumeRatio,
            signal: volumeSignal,
            description: volumeDescription
        });
        return indicators;
    }
}
}),
"[project]/swing-trader-ai/src/lib/swingStrategies.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "SwingTradingStrategies",
    ()=>SwingTradingStrategies
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$indicators$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/src/lib/indicators.ts [app-route] (ecmascript)");
;
class SwingTradingStrategies {
    static DEFAULT_CRITERIA = {
        minPrice: 5.0,
        minVolume: 500000,
        minMarketCap: **********,
        minATRPercent: 2.0,
        above200SMA: true,
        maxDistanceFrom8EMA: 2.0,
        minRoomToResistance: 1.0,
        scanTimeStart: "12:00",
        scanTimeEnd: "16:00",
        maxRiskPerTrade: 1.0,
        maxConcurrentPositions: 3
    };
    // Strategy #1: Overnight Momentum Continuation
    static analyzeOvernightMomentum(symbol, candles, quote, accountSize = 100000) {
        if (candles.length < 50) return null;
        const closes = candles.map((c)=>c.close);
        const highs = candles.map((c)=>c.high);
        const lows = candles.map((c)=>c.low);
        const volumes = candles.map((c)=>c.volume);
        const currentPrice = quote.price;
        const currentVolume = quote.volume;
        const changePercent = quote.changePercent;
        // Calculate technical indicators (adjusted for shorter history)
        const sma50 = __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$indicators$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TechnicalIndicators"].sma(closes, Math.min(50, closes.length - 1)) // Use 50-day instead of 200-day
        ;
        const ema8 = __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$indicators$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TechnicalIndicators"].ema(closes, Math.min(8, closes.length - 1));
        const atr = this.calculateATR(candles, Math.min(14, candles.length - 1));
        const current50SMA = sma50[sma50.length - 1];
        const current8EMA = ema8[ema8.length - 1];
        const currentATR = atr[atr.length - 1];
        // Basic qualification filters (using 50-day SMA instead of 200-day)
        if (!this.passesBasicFilters(quote, currentVolume, current50SMA, currentPrice)) {
            console.log(`❌ ${symbol} failed basic filters`);
            return null;
        }
        console.log(`✅ ${symbol} passed basic filters, analyzing strategy...`);
        // Check if it's a top intraday gainer (top decile movers)
        console.log(`📊 ${symbol} momentum check: ${changePercent}% (need ≥2%)`);
        if (changePercent < 2.0) {
            console.log(`❌ ${symbol} failed momentum: ${changePercent}% < 2%`);
            return null // Minimum 2% gain for momentum
            ;
        }
        // Check distance from 8-EMA (not wildly extended)
        const distanceFrom8EMA = Math.abs(currentPrice - current8EMA) / currentATR;
        console.log(`📈 ${symbol} EMA distance: ${distanceFrom8EMA.toFixed(2)} ATR (max ${this.DEFAULT_CRITERIA.maxDistanceFrom8EMA})`);
        if (distanceFrom8EMA > this.DEFAULT_CRITERIA.maxDistanceFrom8EMA) {
            console.log(`❌ ${symbol} failed EMA distance check`);
            return null;
        }
        // Look for defended intraday level (simplified - using VWAP proxy)
        const vwap = this.calculateVWAP(candles.slice(-1)[0]);
        const keyLevel = Math.max(vwap, currentPrice * 0.98) // Approximate defended level
        ;
        // Check if holding gains (>50% of day's range)
        const todayHigh = highs[highs.length - 1];
        const todayLow = lows[lows.length - 1];
        const dayRange = todayHigh - todayLow;
        const currentFromLow = currentPrice - todayLow;
        const holdingGainsPercent = currentFromLow / dayRange;
        console.log(`📊 ${symbol} holding gains: ${(holdingGainsPercent * 100).toFixed(1)}% (need ≥50%)`);
        if (holdingGainsPercent < 0.5) {
            console.log(`❌ ${symbol} failed holding gains check`);
            return null // Must hold >50% of range
            ;
        }
        // Calculate room to next resistance
        const roomToResistance = this.calculateRoomToResistance(candles, currentPrice, currentATR);
        console.log(`📊 ${symbol} room to resistance: ${roomToResistance.toFixed(2)} ATR (need ≥${this.DEFAULT_CRITERIA.minRoomToResistance})`);
        if (roomToResistance < this.DEFAULT_CRITERIA.minRoomToResistance) {
            console.log(`❌ ${symbol} failed resistance room check`);
            return null;
        }
        // Position sizing (risk 0.5-1% of account)
        const riskPercent = 0.75 // 0.75% risk for overnight holds
        ;
        const stopDistance = currentPrice - keyLevel;
        const riskAmount = accountSize * (riskPercent / 100);
        const positionSize = Math.floor(riskAmount / stopDistance);
        // Targets: Pre-market scale at 3-5%, opening hour at 5-8%
        const targets = [
            currentPrice * 1.03,
            currentPrice * 1.05,
            currentPrice * 1.08 // 8% extended target
        ];
        const confidence = this.calculateOvernightConfidence(changePercent, holdingGainsPercent, currentVolume, roomToResistance);
        return {
            strategy: 'overnight_momentum',
            confidence,
            entryPrice: currentPrice,
            stopLoss: keyLevel,
            targets,
            positionSize,
            riskAmount,
            holdingPeriod: 'overnight',
            keyLevel,
            invalidation: `Daily close below ${keyLevel.toFixed(2)} or gap down below level`,
            notes: [
                'Enter final 30-60 min before close',
                'Exit pre-market on strength or first 45min',
                'Hard stop if gaps below defended level',
                'Scale out aggressively if gaps >1 ATR up'
            ],
            // Precise entry execution
            preciseEntry: {
                price: currentPrice * 0.999,
                orderType: 'limit',
                timing: 'Final 30-60 minutes before market close',
                conditions: [
                    `Stock holding above ${keyLevel.toFixed(2)} (defended level)`,
                    `Volume above ${(currentVolume * 0.8).toLocaleString()} shares`,
                    `Price above ${current8EMA.toFixed(2)} (8-EMA)`,
                    'No late-day selling pressure'
                ],
                urgency: 'wait_for_pullback'
            },
            // Precise exit execution
            preciseExit: {
                stopLoss: {
                    price: keyLevel * 0.995,
                    orderType: 'stop',
                    reason: 'Defended level broken - invalidates setup',
                    triggerConditions: [
                        'Any close below defended level',
                        'Gap down below key level',
                        'Heavy selling into close'
                    ]
                },
                takeProfits: [
                    {
                        price: targets[0],
                        percentage: 33,
                        target: 'T1 - Pre-market (3%)',
                        reasoning: 'Take profits on pre-market strength',
                        orderType: 'limit'
                    },
                    {
                        price: targets[1],
                        percentage: 33,
                        target: 'T2 - Opening hour (5%)',
                        reasoning: 'Scale out on opening momentum',
                        orderType: 'limit'
                    },
                    {
                        price: targets[2],
                        percentage: 34,
                        target: 'T3 - Extended (8%)',
                        reasoning: 'Final exit on extended move',
                        orderType: 'limit'
                    }
                ]
            },
            // Risk management details
            riskManagement: {
                maxRiskDollars: riskAmount,
                accountRiskPercent: riskPercent,
                sharesForRisk: positionSize,
                invalidationPrice: keyLevel,
                timeStopHours: 18,
                maxDrawdownPercent: 2.0
            },
            // Execution plan
            executionPlan: {
                entryInstructions: [
                    '1. Wait for final 30-60 minutes before close',
                    '2. Confirm stock is holding defended level',
                    '3. Place limit order slightly below current price',
                    '4. Cancel if not filled by close'
                ],
                exitInstructions: [
                    '1. Set stop-loss immediately after fill',
                    '2. Monitor pre-market for gap up',
                    '3. Scale out 1/3 at each target level',
                    '4. Exit all by 10:15 AM if no momentum'
                ],
                monitoringPoints: [
                    'Pre-market price action and volume',
                    'Opening gap and first 15-minute candle',
                    'Key level defense throughout session',
                    'Overall market sentiment'
                ],
                contingencyPlans: [
                    'If gaps down: Exit immediately at market open',
                    'If gaps up >2%: Scale out more aggressively',
                    'If sideways: Exit by 10:15 AM',
                    'If market weakness: Tighten stops'
                ]
            }
        };
    }
    // Strategy #2: Technical Breakout Trend-Follow (8-EMA + 200-SMA)
    static analyzeTechnicalBreakout(symbol, candles, quote, accountSize = 100000) {
        if (candles.length < 50) return null;
        const closes = candles.map((c)=>c.close);
        const volumes = candles.map((c)=>c.volume);
        const currentPrice = quote.price;
        // Calculate technical indicators (adjusted for shorter history)
        const sma50 = __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$indicators$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TechnicalIndicators"].sma(closes, Math.min(50, closes.length - 1));
        const ema8 = __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$indicators$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TechnicalIndicators"].ema(closes, Math.min(8, closes.length - 1));
        const atr = this.calculateATR(candles, Math.min(14, candles.length - 1));
        const current50SMA = sma50[sma50.length - 1];
        const current8EMA = ema8[ema8.length - 1];
        const currentATR = atr[atr.length - 1];
        // Basic qualification filters (using 50-day SMA)
        if (!this.passesBasicFilters(quote, quote.volume, current50SMA, currentPrice)) {
            return null;
        }
        // Check if in clear uptrend above 50-SMA (adjusted from 200-SMA)
        if (currentPrice <= current50SMA) return null;
        // Check 8-EMA behavior - should be "hugging" the 8-EMA
        const distanceFrom8EMA = Math.abs(currentPrice - current8EMA);
        const emaDistancePercent = distanceFrom8EMA / currentPrice * 100;
        // Should be close to 8-EMA (within 2-3% for quality trend-follow)
        if (emaDistancePercent > 3.0) return null;
        // Check for recent breakout or EMA reclaim
        const recentEMAReclaim = this.checkEMAReclaim(closes, ema8, 5) // Last 5 days
        ;
        if (!recentEMAReclaim) return null;
        // Volume expansion check
        const avgVolume = __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$indicators$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TechnicalIndicators"].sma(volumes, 20);
        const currentAvgVolume = avgVolume[avgVolume.length - 1];
        const volumeExpansion = quote.volume / currentAvgVolume;
        if (volumeExpansion < 1.2) return null // Need some volume expansion
        ;
        // Calculate room to next resistance
        const roomToResistance = this.calculateRoomToResistance(candles, currentPrice, currentATR);
        if (roomToResistance < 1.5) return null // Need more room for trend-follow
        ;
        // Position sizing (risk 1% of account)
        const riskPercent = 1.0;
        const stopDistance = currentPrice - current8EMA // Stop at 8-EMA break
        ;
        const riskAmount = accountSize * (riskPercent / 100);
        const positionSize = Math.floor(riskAmount / stopDistance);
        // Targets: Scale at resistance levels
        const targets = [
            currentPrice * 1.05,
            currentPrice * 1.10,
            currentPrice * 1.15 // 15% extended target
        ];
        const confidence = this.calculateBreakoutConfidence(emaDistancePercent, volumeExpansion, roomToResistance, quote.changePercent);
        return {
            strategy: 'technical_breakout',
            confidence,
            entryPrice: currentPrice,
            stopLoss: current8EMA,
            targets,
            positionSize,
            riskAmount,
            holdingPeriod: 'days_to_weeks',
            keyLevel: current8EMA,
            invalidation: `Daily close below 8-EMA (${current8EMA.toFixed(2)})`,
            notes: [
                'Enter on afternoon reclaim of 8-EMA',
                'Add only on higher-low pullbacks to 8-EMA',
                'Scale partials at resistance levels',
                'Exit on daily close below 8-EMA'
            ],
            // Precise entry execution
            preciseEntry: {
                price: current8EMA * 1.002,
                orderType: 'limit',
                timing: 'Afternoon reclaim or first pullback to 8-EMA',
                conditions: [
                    `Price reclaiming ${current8EMA.toFixed(2)} (8-EMA) with volume`,
                    `Above ${current50SMA.toFixed(2)} (50-day SMA)`,
                    `Volume expansion above ${(quote.volume * 1.2).toLocaleString()}`,
                    'No major resistance overhead'
                ],
                urgency: 'breakout_confirmation'
            },
            // Precise exit execution
            preciseExit: {
                stopLoss: {
                    price: current8EMA * 0.998,
                    orderType: 'stop',
                    reason: '8-EMA breakdown invalidates trend-follow setup',
                    triggerConditions: [
                        'Daily close below 8-EMA',
                        'Intraday break with volume',
                        'Loss of 50-SMA support'
                    ]
                },
                takeProfits: [
                    {
                        price: targets[0],
                        percentage: 25,
                        target: 'R1 - First resistance (5%)',
                        reasoning: 'Take partial profits at first resistance',
                        orderType: 'limit'
                    },
                    {
                        price: targets[1],
                        percentage: 35,
                        target: 'R2 - Major resistance (10%)',
                        reasoning: 'Scale out at major resistance level',
                        orderType: 'limit'
                    },
                    {
                        price: targets[2],
                        percentage: 40,
                        target: 'R3 - Extension (15%)',
                        reasoning: 'Final exit on extended breakout',
                        orderType: 'limit'
                    }
                ]
            },
            // Risk management details
            riskManagement: {
                maxRiskDollars: riskAmount,
                accountRiskPercent: riskPercent,
                sharesForRisk: positionSize,
                invalidationPrice: current8EMA,
                timeStopHours: 72,
                maxDrawdownPercent: 3.0
            },
            // Execution plan
            executionPlan: {
                entryInstructions: [
                    '1. Wait for afternoon reclaim of 8-EMA',
                    '2. Confirm volume expansion on breakout',
                    '3. Place limit order above 8-EMA',
                    '4. Only enter on higher-low pullbacks'
                ],
                exitInstructions: [
                    '1. Set stop-loss below 8-EMA immediately',
                    '2. Scale out 25% at first resistance',
                    '3. Trail stop to breakeven after R1',
                    '4. Exit remaining on 8-EMA breakdown'
                ],
                monitoringPoints: [
                    '8-EMA as dynamic support/resistance',
                    'Volume confirmation on moves',
                    'Overall market trend alignment',
                    'Sector strength/weakness'
                ],
                contingencyPlans: [
                    'If fails at resistance: Tighten stops',
                    'If market weakness: Exit early',
                    'If sector rotation: Consider exit',
                    'If extended: Take more profits'
                ]
            }
        };
    }
    // Helper methods
    static passesBasicFilters(quote, volume, sma50, price) {
        const priceCheck = price >= this.DEFAULT_CRITERIA.minPrice;
        const volumeCheck = volume >= this.DEFAULT_CRITERIA.minVolume;
        const marketCapCheck = (quote.marketCap || 0) >= this.DEFAULT_CRITERIA.minMarketCap;
        const smaCheck = price > sma50;
        console.log(`🔍 Basic filters for ${quote.symbol}:`);
        console.log(`  💰 Price: ${price} >= ${this.DEFAULT_CRITERIA.minPrice} = ${priceCheck}`);
        console.log(`  📊 Volume: ${volume} >= ${this.DEFAULT_CRITERIA.minVolume} = ${volumeCheck}`);
        console.log(`  🏢 Market Cap: ${quote.marketCap || 0} >= ${this.DEFAULT_CRITERIA.minMarketCap} = ${marketCapCheck}`);
        console.log(`  📈 Above SMA50: ${price} > ${sma50} = ${smaCheck}`);
        const passes = priceCheck && volumeCheck && marketCapCheck && smaCheck;
        console.log(`  ✅ Overall: ${passes}`);
        return passes;
    }
    static calculateATR(candles, period) {
        const trueRanges = [];
        for(let i = 1; i < candles.length; i++){
            const high = candles[i].high;
            const low = candles[i].low;
            const prevClose = candles[i - 1].close;
            const tr = Math.max(high - low, Math.abs(high - prevClose), Math.abs(low - prevClose));
            trueRanges.push(tr);
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$indicators$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TechnicalIndicators"].sma(trueRanges, period);
    }
    static calculateVWAP(candle) {
        // Simplified VWAP calculation using typical price
        return (candle.high + candle.low + candle.close) / 3;
    }
    static calculateRoomToResistance(candles, currentPrice, atr) {
        // Find recent highs as resistance levels
        const recentHighs = candles.slice(-20).map((c)=>c.high);
        const maxHigh = Math.max(...recentHighs);
        const roomToHigh = maxHigh - currentPrice;
        return roomToHigh / atr;
    }
    static checkEMAReclaim(closes, ema8, lookback) {
        // Check if price recently reclaimed 8-EMA
        for(let i = Math.max(0, closes.length - lookback); i < closes.length - 1; i++){
            if (closes[i] < ema8[i] && closes[i + 1] > ema8[i + 1]) {
                return true // Found a reclaim
                ;
            }
        }
        return false;
    }
    static calculateOvernightConfidence(changePercent, holdingGains, volume, roomToResistance) {
        let confidence = 50;
        // Change percent bonus
        if (changePercent > 5) confidence += 15;
        else if (changePercent > 3) confidence += 10;
        else if (changePercent > 2) confidence += 5;
        // Holding gains bonus
        if (holdingGains > 0.8) confidence += 15;
        else if (holdingGains > 0.6) confidence += 10;
        else if (holdingGains > 0.5) confidence += 5;
        // Volume bonus
        if (volume > 2000000) confidence += 10;
        else if (volume > 1000000) confidence += 5;
        // Room to resistance
        if (roomToResistance > 2) confidence += 10;
        else if (roomToResistance > 1.5) confidence += 5;
        return Math.min(95, Math.max(30, confidence));
    }
    static calculateBreakoutConfidence(emaDistance, volumeExpansion, roomToResistance, changePercent) {
        let confidence = 60;
        // EMA proximity bonus (closer is better for trend-follow)
        if (emaDistance < 1) confidence += 15;
        else if (emaDistance < 2) confidence += 10;
        else if (emaDistance < 3) confidence += 5;
        // Volume expansion bonus
        if (volumeExpansion > 2) confidence += 15;
        else if (volumeExpansion > 1.5) confidence += 10;
        else if (volumeExpansion > 1.2) confidence += 5;
        // Room to resistance
        if (roomToResistance > 3) confidence += 15;
        else if (roomToResistance > 2) confidence += 10;
        else if (roomToResistance > 1.5) confidence += 5;
        // Positive momentum
        if (changePercent > 2) confidence += 5;
        return Math.min(95, Math.max(40, confidence));
    }
}
}),
"[externals]/util [external] (util, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}),
"[externals]/stream [external] (stream, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}),
"[externals]/path [external] (path, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}),
"[externals]/http [external] (http, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}),
"[externals]/https [external] (https, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}),
"[externals]/url [external] (url, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}),
"[externals]/fs [external] (fs, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}),
"[externals]/crypto [external] (crypto, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}),
"[externals]/assert [external] (assert, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}),
"[externals]/tty [external] (tty, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}),
"[externals]/os [external] (os, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}),
"[externals]/zlib [external] (zlib, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}),
"[externals]/events [external] (events, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}),
"[project]/swing-trader-ai/src/lib/polygon.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "PolygonAPI",
    ()=>PolygonAPI,
    "polygonAPI",
    ()=>polygonAPI
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/node_modules/axios/lib/axios.js [app-route] (ecmascript)");
;
const POLYGON_BASE_URL = 'https://api.polygon.io';
const API_KEY = process.env.POLYGON_API_KEY;
class PolygonAPI {
    apiKey;
    constructor(apiKey){
        this.apiKey = apiKey || API_KEY || '';
        if (!this.apiKey) {
            throw new Error('Polygon API key is required');
        }
    }
    // Get current stock quote using Polygon's snapshot endpoint (better for paid plans)
    async getStockQuote(symbol) {
        try {
            // Use snapshot endpoint for real-time data (available on paid plans)
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${POLYGON_BASE_URL}/v2/snapshot/locale/us/markets/stocks/tickers/${symbol}`, {
                params: {
                    apikey: this.apiKey
                }
            });
            if (!response.data || !response.data.ticker) {
                throw new Error(`No data found for ${symbol}`);
            }
            const data = response.data.ticker;
            // Extract data from Polygon snapshot response structure
            const dayData = data.day || {};
            const prevDayData = data.prevDay || {};
            const minData = data.min || {};
            // Use the most recent price available
            const currentPrice = dayData.c || minData.c || prevDayData.c;
            const prevClose = prevDayData.c;
            const change = data.todaysChange || currentPrice - prevClose;
            const changePercent = data.todaysChangePerc || change / prevClose * 100;
            return {
                symbol: symbol.toUpperCase(),
                name: data.name || symbol.toUpperCase(),
                price: currentPrice || 0,
                change: change || 0,
                changePercent: changePercent || 0,
                volume: dayData.v || minData.v || 1000000,
                marketCap: data.market_cap || this.estimateMarketCap(symbol, currentPrice || 0),
                pe: undefined,
                dividend: undefined
            };
        } catch (error) {
            console.error('Error fetching stock quote from Polygon:', error);
            // Fallback to previous day data if snapshot fails
            try {
                const fallbackResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/prev`, {
                    params: {
                        adjusted: 'true',
                        apikey: this.apiKey
                    }
                });
                const data = fallbackResponse.data.results[0];
                return {
                    symbol: symbol.toUpperCase(),
                    name: symbol.toUpperCase(),
                    price: data.c || 0,
                    change: data.c - data.o || 0,
                    changePercent: data.o ? (data.c - data.o) / data.o * 100 : 0,
                    volume: data.v || 1000000,
                    marketCap: this.estimateMarketCap(symbol, data.c || 0),
                    pe: undefined,
                    dividend: undefined
                };
            } catch (fallbackError) {
                console.error('Polygon fallback also failed:', fallbackError);
                throw new Error(`Failed to fetch quote for ${symbol}`);
            }
        }
    }
    /**
   * Estimate market cap based on symbol and price
   * This is a fallback when Polygon doesn't provide market cap data
   */ estimateMarketCap(symbol, price) {
        // Import stock universe data for better estimates
        const stockEstimates = {
            // Large cap (>$200B)
            'AAPL': 3000000000000,
            'MSFT': 2800000000000,
            'NVDA': 1800000000000,
            'GOOGL': 1700000000000,
            'GOOG': 1700000000000,
            'AMZN': ********00000,
            'TSLA': 800000000000,
            'META': 800000000000,
            'BRK.B': 900000000000,
            // Mid-large cap ($50B-$200B)
            'JPM': 500000000000,
            'V': 500000000000,
            'UNH': 500000000000,
            'JNJ': 450000000000,
            'XOM': 450000000000,
            'WMT': 600000000000,
            'PG': ********0000,
            'MA': ********0000,
            'HD': 350000000000,
            'CVX': 300000000000,
            'ABBV': 300000000000,
            'BAC': 300000000000,
            'COST': 350000000000,
            'AVGO': 600000000000,
            'TSM': 500000000000,
            // Mid cap ($10B-$50B)
            'NFLX': ********0000,
            'ORCL': 350000000000,
            'CRM': 250000000000,
            'ADBE': 2********000,
            'AMD': 2********000,
            'INTC': ********0000,
            'QCOM': 180000000000,
            'TMO': ********0000,
            'DHR': 180000000000,
            'CAT': 180000000000,
            'GE': 180000000000,
            'DIS': 180000000000,
            'VZ': 170000000000,
            'PFE': 160000000000,
            'NKE': ********0000,
            'MS': ********0000,
            'UBER': ********0000,
            'C': 1********000,
            'GS': 1********000,
            'T': 1********000,
            'AMGN': ********0000,
            'HON': 1********000,
            'LOW': ********0000,
            'BMY': 1********000,
            'CMCSA': ********0000,
            'SBUX': 1**********0,
            'MMM': 60000000000,
            // Smaller cap but popular swing trading stocks
            'PLTR': 60000000000,
            'SHOP': 80000000000,
            'GILD': 80000000000,
            'TGT': 70000000000,
            'COP': ********0000,
            'EOG': 70000000000,
            'SLB': 60000000000,
            'PYPL': 70000000000,
            'SQ': ********000,
            'COIN': 50000000000,
            'DASH': 50000000000,
            'MRNA': 30000000000,
            'SNOW': 50000000000,
            'ROKU': 5000000000,
            'HOOD': ********000,
            'LYFT': 6000000000,
            'SPG': 50000000000,
            'PLD': 1********000,
            'NEE': ********0000
        };
        // Return estimated market cap if available, otherwise estimate based on price
        if (stockEstimates[symbol]) {
            return stockEstimates[symbol];
        }
        // Rough estimation based on price (very approximate)
        if (price > 500) return **********00 // Assume large cap if high price
        ;
        if (price > 100) return 50000000000 // Assume mid-large cap
        ;
        if (price > 50) return ********000 // Assume mid cap
        ;
        if (price > 10) return 5000000000 // Assume small-mid cap
        ;
        return ********** // Default to $1B minimum for scanning
        ;
    }
    // Get historical candlestick data (optimized for paid plans)
    async getHistoricalData(symbol, timespan = 'day', multiplier = 1, from, to) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/range/${multiplier}/${timespan}/${from}/${to}`, {
                params: {
                    adjusted: 'true',
                    sort: 'asc',
                    limit: 50000,
                    apikey: this.apiKey
                }
            });
            if (!response.data.results || response.data.results.length === 0) {
                console.warn(`No historical data found for ${symbol}`);
                return [];
            }
            return response.data.results.map((candle)=>({
                    timestamp: candle.t,
                    open: candle.o,
                    high: candle.h,
                    low: candle.l,
                    close: candle.c,
                    volume: candle.v
                }));
        } catch (error) {
            console.error(`Error fetching historical data for ${symbol}:`, error);
            // Log the specific error for debugging
            if (error.response) {
                console.error(`Polygon API Error: ${error.response.status} - ${error.response.statusText}`);
                console.error('Response data:', error.response.data);
            }
            throw new Error(`Failed to fetch historical data for ${symbol}: ${error.message}`);
        }
    }
    // Get company details
    async getCompanyDetails(symbol) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${POLYGON_BASE_URL}/v3/reference/tickers/${symbol}`, {
                params: {
                    apikey: this.apiKey
                }
            });
            return response.data.results;
        } catch (error) {
            console.error('Error fetching company details:', error);
            return null;
        }
    }
    // Get market status
    async getMarketStatus() {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${POLYGON_BASE_URL}/v1/marketstatus/now`, {
                params: {
                    apikey: this.apiKey
                }
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching market status:', error);
            return null;
        }
    }
    // Search for stocks
    async searchStocks(query, limit = 10) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${POLYGON_BASE_URL}/v3/reference/tickers`, {
                params: {
                    search: query,
                    market: 'stocks',
                    active: 'true',
                    limit,
                    apikey: this.apiKey
                }
            });
            return response.data.results || [];
        } catch (error) {
            console.error('Error searching stocks:', error);
            return [];
        }
    }
}
const polygonAPI = new PolygonAPI();
}),
"[project]/swing-trader-ai/src/data/stockUniverse.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

/**
 * Comprehensive stock universe for swing trading
 * Focus on liquid, volatile large-cap stocks with good technical patterns
 */ __turbopack_context__.s([
    "DEFAULT_SWING_SYMBOLS",
    ()=>DEFAULT_SWING_SYMBOLS,
    "ENERGY_SYMBOLS",
    ()=>ENERGY_SYMBOLS,
    "FINANCIAL_SYMBOLS",
    ()=>FINANCIAL_SYMBOLS,
    "HEALTHCARE_SYMBOLS",
    ()=>HEALTHCARE_SYMBOLS,
    "PRIORITY_SWING_SYMBOLS",
    ()=>PRIORITY_SWING_SYMBOLS,
    "SWING_TRADING_UNIVERSE",
    ()=>SWING_TRADING_UNIVERSE,
    "TECH_SYMBOLS",
    ()=>TECH_SYMBOLS,
    "getHighVolumeStocks",
    ()=>getHighVolumeStocks,
    "getStocksBySector",
    ()=>getStocksBySector,
    "getStocksByVolatility",
    ()=>getStocksByVolatility,
    "getTopSwingTradingStocks",
    ()=>getTopSwingTradingStocks
]);
const SWING_TRADING_UNIVERSE = [
    // Technology - High Growth & Volatility
    {
        symbol: 'AAPL',
        name: 'Apple Inc.',
        sector: 'Technology',
        marketCap: 3000,
        avgVolume: 50000000,
        volatility: 'Medium',
        swingTradingRating: 9
    },
    {
        symbol: 'MSFT',
        name: 'Microsoft Corporation',
        sector: 'Technology',
        marketCap: 2800,
        avgVolume: 25000000,
        volatility: 'Medium',
        swingTradingRating: 8
    },
    {
        symbol: 'NVDA',
        name: 'NVIDIA Corporation',
        sector: 'Technology',
        marketCap: 1800,
        avgVolume: 45000000,
        volatility: 'High',
        swingTradingRating: 10
    },
    {
        symbol: 'GOOGL',
        name: 'Alphabet Inc. Class A',
        sector: 'Technology',
        marketCap: 1700,
        avgVolume: 25000000,
        volatility: 'Medium',
        swingTradingRating: 8
    },
    {
        symbol: 'GOOG',
        name: 'Alphabet Inc. Class C',
        sector: 'Technology',
        marketCap: 1700,
        avgVolume: ********,
        volatility: 'Medium',
        swingTradingRating: 8
    },
    {
        symbol: 'META',
        name: 'Meta Platforms Inc.',
        sector: 'Technology',
        marketCap: 800,
        avgVolume: ********,
        volatility: 'High',
        swingTradingRating: 9
    },
    {
        symbol: 'TSLA',
        name: 'Tesla Inc.',
        sector: 'Technology',
        marketCap: 800,
        avgVolume: 75000000,
        volatility: 'High',
        swingTradingRating: 10
    },
    {
        symbol: 'AMZN',
        name: 'Amazon.com Inc.',
        sector: 'Technology',
        marketCap: 1500,
        avgVolume: 35000000,
        volatility: 'Medium',
        swingTradingRating: 8
    },
    {
        symbol: 'NFLX',
        name: 'Netflix Inc.',
        sector: 'Technology',
        marketCap: 200,
        avgVolume: 8000000,
        volatility: 'High',
        swingTradingRating: 9
    },
    {
        symbol: 'ORCL',
        name: 'Oracle Corporation',
        sector: 'Technology',
        marketCap: 350,
        avgVolume: ********,
        volatility: 'Medium',
        swingTradingRating: 7
    },
    {
        symbol: 'CRM',
        name: 'Salesforce Inc.',
        sector: 'Technology',
        marketCap: 250,
        avgVolume: 6000000,
        volatility: 'High',
        swingTradingRating: 8
    },
    {
        symbol: 'ADBE',
        name: 'Adobe Inc.',
        sector: 'Technology',
        marketCap: 220,
        avgVolume: 3000000,
        volatility: 'Medium',
        swingTradingRating: 7
    },
    {
        symbol: 'AVGO',
        name: 'Broadcom Inc.',
        sector: 'Technology',
        marketCap: 600,
        avgVolume: 2000000,
        volatility: 'Medium',
        swingTradingRating: 8
    },
    {
        symbol: 'TSM',
        name: 'Taiwan Semiconductor',
        sector: 'Technology',
        marketCap: 500,
        avgVolume: ********,
        volatility: 'Medium',
        swingTradingRating: 8
    },
    {
        symbol: 'ASML',
        name: 'ASML Holding N.V.',
        sector: 'Technology',
        marketCap: 300,
        avgVolume: 1500000,
        volatility: 'High',
        swingTradingRating: 8
    },
    {
        symbol: 'AMD',
        name: 'Advanced Micro Devices',
        sector: 'Technology',
        marketCap: 220,
        avgVolume: 45000000,
        volatility: 'High',
        swingTradingRating: 9
    },
    {
        symbol: 'INTC',
        name: 'Intel Corporation',
        sector: 'Technology',
        marketCap: 200,
        avgVolume: 25000000,
        volatility: 'Medium',
        swingTradingRating: 7
    },
    {
        symbol: 'QCOM',
        name: 'QUALCOMM Incorporated',
        sector: 'Technology',
        marketCap: 180,
        avgVolume: 8000000,
        volatility: 'Medium',
        swingTradingRating: 8
    },
    {
        symbol: 'PLTR',
        name: 'Palantir Technologies',
        sector: 'Technology',
        marketCap: 60,
        avgVolume: 35000000,
        volatility: 'High',
        swingTradingRating: 9
    },
    {
        symbol: 'SNOW',
        name: 'Snowflake Inc.',
        sector: 'Technology',
        marketCap: 50,
        avgVolume: 4000000,
        volatility: 'High',
        swingTradingRating: 8
    },
    // Financial Services - Interest Rate Sensitive
    {
        symbol: 'JPM',
        name: 'JPMorgan Chase & Co.',
        sector: 'Financial',
        marketCap: 500,
        avgVolume: ********,
        volatility: 'Medium',
        swingTradingRating: 8
    },
    {
        symbol: 'BAC',
        name: 'Bank of America Corp.',
        sector: 'Financial',
        marketCap: 300,
        avgVolume: ********,
        volatility: 'Medium',
        swingTradingRating: 8
    },
    {
        symbol: 'WFC',
        name: 'Wells Fargo & Company',
        sector: 'Financial',
        marketCap: 180,
        avgVolume: ********,
        volatility: 'Medium',
        swingTradingRating: 7
    },
    {
        symbol: 'GS',
        name: 'Goldman Sachs Group',
        sector: 'Financial',
        marketCap: 120,
        avgVolume: 2500000,
        volatility: 'High',
        swingTradingRating: 8
    },
    {
        symbol: 'MS',
        name: 'Morgan Stanley',
        sector: 'Financial',
        marketCap: 150,
        avgVolume: 8000000,
        volatility: 'High',
        swingTradingRating: 8
    },
    {
        symbol: 'C',
        name: 'Citigroup Inc.',
        sector: 'Financial',
        marketCap: 120,
        avgVolume: ********,
        volatility: 'High',
        swingTradingRating: 8
    },
    {
        symbol: 'BRK.B',
        name: 'Berkshire Hathaway B',
        sector: 'Financial',
        marketCap: 900,
        avgVolume: 4000000,
        volatility: 'Low',
        swingTradingRating: 6
    },
    {
        symbol: 'V',
        name: 'Visa Inc.',
        sector: 'Financial',
        marketCap: 500,
        avgVolume: 6000000,
        volatility: 'Medium',
        swingTradingRating: 7
    },
    {
        symbol: 'MA',
        name: 'Mastercard Inc.',
        sector: 'Financial',
        marketCap: 400,
        avgVolume: 3000000,
        volatility: 'Medium',
        swingTradingRating: 7
    },
    {
        symbol: 'PYPL',
        name: 'PayPal Holdings Inc.',
        sector: 'Financial',
        marketCap: 70,
        avgVolume: ********,
        volatility: 'High',
        swingTradingRating: 8
    },
    // Healthcare & Biotech - Defensive with Growth
    {
        symbol: 'JNJ',
        name: 'Johnson & Johnson',
        sector: 'Healthcare',
        marketCap: 450,
        avgVolume: 7000000,
        volatility: 'Low',
        swingTradingRating: 6
    },
    {
        symbol: 'UNH',
        name: 'UnitedHealth Group',
        sector: 'Healthcare',
        marketCap: 500,
        avgVolume: 3000000,
        volatility: 'Medium',
        swingTradingRating: 7
    },
    {
        symbol: 'PFE',
        name: 'Pfizer Inc.',
        sector: 'Healthcare',
        marketCap: 160,
        avgVolume: 25000000,
        volatility: 'Medium',
        swingTradingRating: 7
    },
    {
        symbol: 'ABBV',
        name: 'AbbVie Inc.',
        sector: 'Healthcare',
        marketCap: 300,
        avgVolume: 6000000,
        volatility: 'Medium',
        swingTradingRating: 7
    },
    {
        symbol: 'TMO',
        name: 'Thermo Fisher Scientific',
        sector: 'Healthcare',
        marketCap: 200,
        avgVolume: 1500000,
        volatility: 'Medium',
        swingTradingRating: 7
    },
    {
        symbol: 'DHR',
        name: 'Danaher Corporation',
        sector: 'Healthcare',
        marketCap: 180,
        avgVolume: 2000000,
        volatility: 'Medium',
        swingTradingRating: 7
    },
    {
        symbol: 'BMY',
        name: 'Bristol Myers Squibb',
        sector: 'Healthcare',
        marketCap: 120,
        avgVolume: 10000000,
        volatility: 'Medium',
        swingTradingRating: 7
    },
    {
        symbol: 'AMGN',
        name: 'Amgen Inc.',
        sector: 'Healthcare',
        marketCap: 150,
        avgVolume: 2500000,
        volatility: 'Medium',
        swingTradingRating: 7
    },
    {
        symbol: 'GILD',
        name: 'Gilead Sciences Inc.',
        sector: 'Healthcare',
        marketCap: 80,
        avgVolume: 6000000,
        volatility: 'Medium',
        swingTradingRating: 7
    },
    {
        symbol: 'MRNA',
        name: 'Moderna Inc.',
        sector: 'Healthcare',
        marketCap: 30,
        avgVolume: 8000000,
        volatility: 'High',
        swingTradingRating: 9
    },
    // Consumer & Retail - Economic Sensitive
    {
        symbol: 'WMT',
        name: 'Walmart Inc.',
        sector: 'Consumer',
        marketCap: 600,
        avgVolume: 8000000,
        volatility: 'Low',
        swingTradingRating: 6
    },
    {
        symbol: 'HD',
        name: 'Home Depot Inc.',
        sector: 'Consumer',
        marketCap: 350,
        avgVolume: 3000000,
        volatility: 'Medium',
        swingTradingRating: 7
    },
    {
        symbol: 'MCD',
        name: 'McDonald\'s Corporation',
        sector: 'Consumer',
        marketCap: 200,
        avgVolume: 2500000,
        volatility: 'Low',
        swingTradingRating: 6
    },
    {
        symbol: 'NKE',
        name: 'Nike Inc.',
        sector: 'Consumer',
        marketCap: 150,
        avgVolume: 6000000,
        volatility: 'Medium',
        swingTradingRating: 7
    },
    {
        symbol: 'SBUX',
        name: 'Starbucks Corporation',
        sector: 'Consumer',
        marketCap: 110,
        avgVolume: 6000000,
        volatility: 'Medium',
        swingTradingRating: 7
    },
    {
        symbol: 'TGT',
        name: 'Target Corporation',
        sector: 'Consumer',
        marketCap: 70,
        avgVolume: 4000000,
        volatility: 'High',
        swingTradingRating: 8
    },
    {
        symbol: 'COST',
        name: 'Costco Wholesale Corp.',
        sector: 'Consumer',
        marketCap: 350,
        avgVolume: 2000000,
        volatility: 'Medium',
        swingTradingRating: 7
    },
    {
        symbol: 'LOW',
        name: 'Lowe\'s Companies Inc.',
        sector: 'Consumer',
        marketCap: 150,
        avgVolume: 3000000,
        volatility: 'Medium',
        swingTradingRating: 7
    },
    // Energy - Commodity Driven
    {
        symbol: 'XOM',
        name: 'Exxon Mobil Corporation',
        sector: 'Energy',
        marketCap: 450,
        avgVolume: ********,
        volatility: 'High',
        swingTradingRating: 8
    },
    {
        symbol: 'CVX',
        name: 'Chevron Corporation',
        sector: 'Energy',
        marketCap: 300,
        avgVolume: ********,
        volatility: 'Medium',
        swingTradingRating: 7
    },
    {
        symbol: 'COP',
        name: 'ConocoPhillips',
        sector: 'Energy',
        marketCap: 150,
        avgVolume: 8000000,
        volatility: 'High',
        swingTradingRating: 8
    },
    {
        symbol: 'SLB',
        name: 'Schlumberger Limited',
        sector: 'Energy',
        marketCap: 60,
        avgVolume: ********,
        volatility: 'High',
        swingTradingRating: 8
    },
    {
        symbol: 'EOG',
        name: 'EOG Resources Inc.',
        sector: 'Energy',
        marketCap: 70,
        avgVolume: 4000000,
        volatility: 'High',
        swingTradingRating: 8
    },
    // Industrial & Materials
    {
        symbol: 'CAT',
        name: 'Caterpillar Inc.',
        sector: 'Industrial',
        marketCap: 180,
        avgVolume: 3000000,
        volatility: 'High',
        swingTradingRating: 8
    },
    {
        symbol: 'BA',
        name: 'Boeing Company',
        sector: 'Industrial',
        marketCap: 120,
        avgVolume: 8000000,
        volatility: 'High',
        swingTradingRating: 9
    },
    {
        symbol: 'GE',
        name: 'General Electric Co.',
        sector: 'Industrial',
        marketCap: 180,
        avgVolume: 45000000,
        volatility: 'High',
        swingTradingRating: 9
    },
    {
        symbol: 'MMM',
        name: '3M Company',
        sector: 'Industrial',
        marketCap: 60,
        avgVolume: 3000000,
        volatility: 'Medium',
        swingTradingRating: 7
    },
    {
        symbol: 'HON',
        name: 'Honeywell International',
        sector: 'Industrial',
        marketCap: 140,
        avgVolume: 2500000,
        volatility: 'Medium',
        swingTradingRating: 7
    },
    // Communication & Media
    {
        symbol: 'DIS',
        name: 'Walt Disney Company',
        sector: 'Media',
        marketCap: 180,
        avgVolume: ********,
        volatility: 'High',
        swingTradingRating: 8
    },
    {
        symbol: 'CMCSA',
        name: 'Comcast Corporation',
        sector: 'Media',
        marketCap: 150,
        avgVolume: ********,
        volatility: 'Medium',
        swingTradingRating: 7
    },
    {
        symbol: 'VZ',
        name: 'Verizon Communications',
        sector: 'Telecom',
        marketCap: 170,
        avgVolume: ********,
        volatility: 'Low',
        swingTradingRating: 6
    },
    {
        symbol: 'T',
        name: 'AT&T Inc.',
        sector: 'Telecom',
        marketCap: 120,
        avgVolume: 35000000,
        volatility: 'Medium',
        swingTradingRating: 7
    },
    // High-Volatility Growth Stocks
    {
        symbol: 'ROKU',
        name: 'Roku Inc.',
        sector: 'Technology',
        marketCap: 5,
        avgVolume: 8000000,
        volatility: 'High',
        swingTradingRating: 9
    },
    {
        symbol: 'SHOP',
        name: 'Shopify Inc.',
        sector: 'Technology',
        marketCap: 80,
        avgVolume: 3000000,
        volatility: 'High',
        swingTradingRating: 9
    },
    {
        symbol: 'BLOCK',
        name: 'Block Inc.',
        sector: 'Financial',
        marketCap: 40,
        avgVolume: 8000000,
        volatility: 'High',
        swingTradingRating: 9
    },
    {
        symbol: 'UBER',
        name: 'Uber Technologies',
        sector: 'Technology',
        marketCap: 150,
        avgVolume: ********,
        volatility: 'High',
        swingTradingRating: 9
    },
    {
        symbol: 'LYFT',
        name: 'Lyft Inc.',
        sector: 'Technology',
        marketCap: 6,
        avgVolume: 4000000,
        volatility: 'High',
        swingTradingRating: 8
    },
    {
        symbol: 'DASH',
        name: 'DoorDash Inc.',
        sector: 'Technology',
        marketCap: 50,
        avgVolume: 3000000,
        volatility: 'High',
        swingTradingRating: 8
    },
    {
        symbol: 'COIN',
        name: 'Coinbase Global Inc.',
        sector: 'Financial',
        marketCap: 50,
        avgVolume: 8000000,
        volatility: 'High',
        swingTradingRating: 9
    },
    {
        symbol: 'HOOD',
        name: 'Robinhood Markets Inc.',
        sector: 'Financial',
        marketCap: 15,
        avgVolume: 10000000,
        volatility: 'High',
        swingTradingRating: 9
    },
    // REITs & Utilities (Lower volatility but good for certain strategies)
    {
        symbol: 'SPG',
        name: 'Simon Property Group',
        sector: 'REIT',
        marketCap: 50,
        avgVolume: 2000000,
        volatility: 'Medium',
        swingTradingRating: 6
    },
    {
        symbol: 'PLD',
        name: 'Prologis Inc.',
        sector: 'REIT',
        marketCap: 120,
        avgVolume: 2500000,
        volatility: 'Medium',
        swingTradingRating: 6
    },
    {
        symbol: 'NEE',
        name: 'NextEra Energy Inc.',
        sector: 'Utilities',
        marketCap: 150,
        avgVolume: 8000000,
        volatility: 'Low',
        swingTradingRating: 6
    }
];
const getStocksByVolatility = (volatility)=>{
    return SWING_TRADING_UNIVERSE.filter((stock)=>stock.volatility === volatility);
};
const getStocksBySector = (sector)=>{
    return SWING_TRADING_UNIVERSE.filter((stock)=>stock.sector === sector);
};
const getTopSwingTradingStocks = (limit = 50)=>{
    return SWING_TRADING_UNIVERSE.sort((a, b)=>b.swingTradingRating - a.swingTradingRating).slice(0, limit);
};
const getHighVolumeStocks = (minVolume = 10000000)=>{
    return SWING_TRADING_UNIVERSE.filter((stock)=>stock.avgVolume >= minVolume);
};
const DEFAULT_SWING_SYMBOLS = SWING_TRADING_UNIVERSE.map((stock)=>stock.symbol);
const PRIORITY_SWING_SYMBOLS = getTopSwingTradingStocks(75).map((stock)=>stock.symbol);
const TECH_SYMBOLS = getStocksBySector('Technology').map((stock)=>stock.symbol);
const FINANCIAL_SYMBOLS = getStocksBySector('Financial').map((stock)=>stock.symbol);
const HEALTHCARE_SYMBOLS = getStocksBySector('Healthcare').map((stock)=>stock.symbol);
const ENERGY_SYMBOLS = getStocksBySector('Energy').map((stock)=>stock.symbol);
}),
"[project]/swing-trader-ai/src/lib/enhancedSwingScanner.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "EnhancedSwingScanner",
    ()=>EnhancedSwingScanner,
    "enhancedSwingScanner",
    ()=>enhancedSwingScanner
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$swingStrategies$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/src/lib/swingStrategies.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$polygon$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/src/lib/polygon.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$data$2f$stockUniverse$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/src/data/stockUniverse.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/swing-trader-ai/node_modules/date-fns/format.js [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$date$2d$fns$2f$subDays$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/node_modules/date-fns/subDays.js [app-route] (ecmascript)");
;
;
;
;
class EnhancedSwingScanner {
    polygonAPI;
    accountSize;
    constructor(accountSize = 100000){
        this.polygonAPI = new __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$polygon$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PolygonAPI"](process.env.POLYGON_API_KEY);
        this.accountSize = accountSize;
    }
    // Main enhanced scanning function
    async scanWithStrategies(symbols, maxConcurrent = 5) {
        const startTime = Date.now();
        const results = [];
        const failed = [];
        console.log(`Starting enhanced strategy scan of ${symbols.length} stocks...`);
        // Check if we're in optimal scan time (12:00-16:00 ET)
        const marketConditions = this.getMarketConditions();
        // Process stocks in batches
        for(let i = 0; i < symbols.length; i += maxConcurrent){
            const batch = symbols.slice(i, i + maxConcurrent);
            const batchPromises = batch.map((symbol)=>this.scanSingleStockStrategies(symbol));
            const batchResults = await Promise.allSettled(batchPromises);
            batchResults.forEach((result, index)=>{
                const symbol = batch[index];
                if (result.status === 'fulfilled' && result.value) {
                    results.push(result.value);
                } else {
                    failed.push(symbol);
                    console.warn(`Failed to scan ${symbol}:`, result.status === 'rejected' ? result.reason : 'Unknown error');
                }
            });
            // Rate limiting delay
            if (i + maxConcurrent < symbols.length) {
                await new Promise((resolve)=>setTimeout(resolve, 1000));
            }
        }
        // Sort by overall score and assign ranks
        results.sort((a, b)=>b.overallScore - a.overallScore);
        results.forEach((result, index)=>{
            result.rank = index + 1;
        });
        // Calculate summary statistics
        const overnightSetups = results.filter((r)=>r.overnightSetup).length;
        const breakoutSetups = results.filter((r)=>r.breakoutSetup).length;
        const bothStrategies = results.filter((r)=>r.overnightSetup && r.breakoutSetup).length;
        const scanDuration = Date.now() - startTime;
        return {
            totalScanned: symbols.length,
            overnightSetups,
            breakoutSetups,
            bothStrategies,
            topSetups: results.slice(0, 25),
            scanDuration,
            marketConditions
        };
    }
    // Scan individual stock for both strategies
    async scanSingleStockStrategies(symbol) {
        try {
            console.log(`Starting scan for ${symbol}...`);
            // Get stock quote and historical data
            const [quote1, historicalData1] = await Promise.all([
                this.polygonAPI.getStockQuote(symbol),
                this.getHistoricalData(symbol)
            ]);
            console.log(`Quote for ${symbol}:`, quote1);
            console.log(`Historical data length for ${symbol}:`, historicalData1?.length);
            if (!quote1) {
                throw new Error(`No quote data available for ${symbol}`);
            }
            if (!historicalData1 || historicalData1.length < 30) {
                throw new Error(`Insufficient historical data for ${symbol} - need at least 30 days, got ${historicalData1?.length || 0}`);
            }
            // Analyze both strategies
            const overnightSetup = __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$swingStrategies$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SwingTradingStrategies"].analyzeOvernightMomentum(symbol, historicalData1, quote1, this.accountSize);
            const breakoutSetup = __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$swingStrategies$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SwingTradingStrategies"].analyzeTechnicalBreakout(symbol, historicalData1, quote1, this.accountSize);
            // Skip if no valid setups
            if (!overnightSetup && !breakoutSetup) {
                return null;
            }
            // Determine best strategy and overall score
            const { bestStrategy, overallScore } = this.calculateBestStrategy(overnightSetup, breakoutSetup);
            // Generate alerts and warnings
            const alerts = this.generateAlerts(overnightSetup, breakoutSetup, quote1);
            const riskWarnings = this.generateRiskWarnings(overnightSetup, breakoutSetup, quote1);
            const result = {
                symbol,
                name: quote1.name || symbol,
                sector: this.getSectorForSymbol(symbol),
                quote: quote1,
                overnightSetup: overnightSetup || undefined,
                breakoutSetup: breakoutSetup || undefined,
                bestStrategy,
                overallScore,
                rank: 0,
                scanTime: new Date().toISOString(),
                alerts,
                riskWarnings
            };
            console.log(`Successfully scanned ${symbol} with score ${overallScore}`);
            return result;
        } catch (error) {
            console.error(`❌ Error scanning ${symbol}:`, error);
            console.error('📊 Quote data:', quote);
            console.error('📈 Historical data length:', historicalData?.length);
            console.error('🔍 Error message:', error instanceof Error ? error.message : 'Unknown error');
            console.error('📋 Error stack:', error instanceof Error ? error.stack : 'No stack trace');
            // Try to identify the specific failure point
            if (!quote) {
                console.error('❌ Failure: No quote data');
            } else if (!historicalData || historicalData.length < 30) {
                console.error('❌ Failure: Insufficient historical data');
            } else {
                console.error('❌ Failure: Strategy analysis error');
            }
            return null;
        }
    }
    // Get historical data with optimized API usage
    async getHistoricalData(symbol) {
        const to = (0, __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(new Date(), 'yyyy-MM-dd');
        const from = (0, __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$date$2d$fns$2f$subDays$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["subDays"])(new Date(), 100), 'yyyy-MM-dd') // 100 days should be sufficient
        ;
        try {
            console.log(`Fetching historical data for ${symbol} from ${from} to ${to}`);
            const data = await this.polygonAPI.getHistoricalData(symbol, 'day', 1, from, to);
            if (data.length === 0) {
                console.warn(`No historical data returned for ${symbol}`);
                throw new Error('No historical data available');
            }
            console.log(`Successfully fetched ${data.length} days of data for ${symbol}`);
            return data;
        } catch (error) {
            console.error(`Failed to fetch historical data for ${symbol}:`, error);
            throw error;
        }
    }
    // Calculate best strategy and overall score
    calculateBestStrategy(overnight, breakout) {
        if (!overnight && !breakout) {
            return {
                overallScore: 0
            };
        }
        if (overnight && !breakout) {
            return {
                bestStrategy: 'overnight_momentum',
                overallScore: overnight.confidence
            };
        }
        if (breakout && !overnight) {
            return {
                bestStrategy: 'technical_breakout',
                overallScore: breakout.confidence
            };
        }
        if (overnight && breakout) {
            // Both strategies valid - choose higher confidence
            if (overnight.confidence > breakout.confidence) {
                return {
                    bestStrategy: 'overnight_momentum',
                    overallScore: overnight.confidence + 5
                } // Bonus for multiple setups
                ;
            } else {
                return {
                    bestStrategy: 'technical_breakout',
                    overallScore: breakout.confidence + 5
                };
            }
        }
        return {
            overallScore: 0
        };
    }
    // Generate trading alerts
    generateAlerts(overnight, breakout, quote1) {
        const alerts = [];
        if (overnight) {
            alerts.push(`🚀 OVERNIGHT MOMENTUM: Entry ${overnight.entryPrice.toFixed(2)}, Target ${overnight.targets[0].toFixed(2)}`);
            alerts.push(`⏰ Execute in final 30-60 min before close`);
            alerts.push(`🛑 Stop: ${overnight.stopLoss.toFixed(2)} (${((overnight.entryPrice - overnight.stopLoss) / overnight.entryPrice * 100).toFixed(1)}% risk)`);
        }
        if (breakout) {
            alerts.push(`📈 BREAKOUT SETUP: Entry ${breakout.entryPrice.toFixed(2)}, riding 8-EMA`);
            alerts.push(`🎯 Targets: ${breakout.targets.map((t)=>t.toFixed(2)).join(', ')}`);
            alerts.push(`🛑 Stop: Daily close below ${breakout.stopLoss.toFixed(2)}`);
        }
        if (quote1 && quote1.changePercent > 5) {
            alerts.push(`🔥 Strong momentum: +${quote1.changePercent.toFixed(1)}% today`);
        }
        return alerts;
    }
    // Generate risk warnings
    generateRiskWarnings(overnight, breakout, quote1) {
        const warnings = [];
        if (overnight) {
            warnings.push(`⚠️ Overnight gap risk - size down vs intraday trades`);
            if (quote1 && quote1.changePercent > 8) {
                warnings.push(`⚠️ Extended move (+${quote1.changePercent.toFixed(1)}%) - consider smaller size`);
            }
        }
        if (quote1 && (quote1.marketCap || 0) < **********) {
            warnings.push(`⚠️ Small cap overnight risk - volatile gaps possible`);
        }
        if (quote1 && quote1.volume < 1000000) {
            warnings.push(`⚠️ Lower volume - may have liquidity issues`);
        }
        return warnings;
    }
    // Get market conditions with proper timezone handling
    getMarketConditions() {
        const now = new Date();
        // Get current time in Eastern Time (market timezone)
        const etNow = new Date(now.toLocaleString("en-US", {
            timeZone: "America/New_York"
        }));
        const etHour = etNow.getHours();
        const etMinute = etNow.getMinutes();
        const etTimeDecimal = etHour + etMinute / 60;
        // Get local time for display
        const localHour = now.getHours();
        const localMinute = now.getMinutes();
        // Check if it's a weekday (Monday = 1, Friday = 5)
        const dayOfWeek = etNow.getDay();
        const isWeekday = dayOfWeek >= 1 && dayOfWeek <= 5;
        return {
            timeOfDay: `${localHour.toString().padStart(2, '0')}:${localMinute.toString().padStart(2, '0')} Local (${etHour.toString().padStart(2, '0')}:${etMinute.toString().padStart(2, '0')} ET)`,
            isOptimalScanTime: isWeekday && etTimeDecimal >= 12 && etTimeDecimal <= 16,
            marketHours: isWeekday && etTimeDecimal >= 9.5 && etTimeDecimal <= 16,
            etTime: `${etHour.toString().padStart(2, '0')}:${etMinute.toString().padStart(2, '0')} ET`,
            isWeekday
        };
    }
    // Get sector for symbol using stock universe data
    getSectorForSymbol(symbol) {
        const stockInfo = __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$data$2f$stockUniverse$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SWING_TRADING_UNIVERSE"].find((stock)=>stock.symbol === symbol);
        return stockInfo?.sector || 'Other';
    }
    // Quick scan with strategies
    async quickStrategyScan(prioritySymbols) {
        const summary = await this.scanWithStrategies(prioritySymbols, 8);
        return summary.topSetups;
    }
}
const enhancedSwingScanner = new EnhancedSwingScanner();
}),
"[project]/swing-trader-ai/src/app/api/scanner/strategies/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "GET",
    ()=>GET
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$enhancedSwingScanner$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/src/lib/enhancedSwingScanner.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$data$2f$stockUniverse$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/swing-trader-ai/src/data/stockUniverse.ts [app-route] (ecmascript)");
;
;
;
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const scanType = searchParams.get('type') || 'quick' // quick, full
        ;
        const accountSize = parseInt(searchParams.get('accountSize') || '100000');
        const limit = parseInt(searchParams.get('limit') || '20');
        console.log(`Starting ${scanType} strategy scan...`);
        // Set account size for position sizing
        const scanner = new __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$lib$2f$enhancedSwingScanner$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EnhancedSwingScanner"](accountSize);
        let symbols;
        let maxConcurrent;
        if (scanType === 'full') {
            // Full scan: All 70+ stocks with slower processing
            symbols = __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$data$2f$stockUniverse$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DEFAULT_SWING_SYMBOLS"];
            maxConcurrent = 3;
            console.log(`Full scan: ${symbols.length} stocks`);
        } else {
            // Quick scan: Top 30 swing trading candidates with faster processing
            symbols = __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$src$2f$data$2f$stockUniverse$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PRIORITY_SWING_SYMBOLS"];
            maxConcurrent = 6;
            console.log(`Quick scan: ${symbols.length} priority stocks`);
        }
        const summary = await scanner.scanWithStrategies(symbols, maxConcurrent);
        // Limit results if requested
        const limitedSummary = {
            ...summary,
            topSetups: summary.topSetups.slice(0, limit)
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(limitedSummary);
    } catch (error) {
        console.error('Error in strategy scanner API:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$swing$2d$trader$2d$ai$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to perform strategy scan'
        }, {
            status: 500
        });
    }
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__bc19f545._.js.map