/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/analysis/swing/[symbol]/route";
exports.ids = ["app/api/analysis/swing/[symbol]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalysis%2Fswing%2F%5Bsymbol%5D%2Froute&page=%2Fapi%2Fanalysis%2Fswing%2F%5Bsymbol%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalysis%2Fswing%2F%5Bsymbol%5D%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalysis%2Fswing%2F%5Bsymbol%5D%2Froute&page=%2Fapi%2Fanalysis%2Fswing%2F%5Bsymbol%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalysis%2Fswing%2F%5Bsymbol%5D%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var C_Users_SJFit_Desktop_shittyidea_swing_trader_ai_src_app_api_analysis_swing_symbol_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/analysis/swing/[symbol]/route.ts */ \"(rsc)/./src/app/api/analysis/swing/[symbol]/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/analysis/swing/[symbol]/route\",\n        pathname: \"/api/analysis/swing/[symbol]\",\n        filename: \"route\",\n        bundlePath: \"app/api/analysis/swing/[symbol]/route\"\n    },\n    distDir: \".next\" || 0,\n    relativeProjectDir:  false || '',\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\shittyidea\\\\swing-trader-ai\\\\src\\\\app\\\\api\\\\analysis\\\\swing\\\\[symbol]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_SJFit_Desktop_shittyidea_swing_trader_ai_src_app_api_analysis_swing_symbol_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/analysis/swing/[symbol]/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = false;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        if (!(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalysis%2Fswing%2F%5Bsymbol%5D%2Froute&page=%2Fapi%2Fanalysis%2Fswing%2F%5Bsymbol%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalysis%2Fswing%2F%5Bsymbol%5D%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/analysis/swing/[symbol]/route.ts":
/*!******************************************************!*\
  !*** ./src/app/api/analysis/swing/[symbol]/route.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_polygon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/polygon */ \"(rsc)/./src/lib/polygon.ts\");\n/* harmony import */ var _lib_swingAnalysis__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/swingAnalysis */ \"(rsc)/./src/lib/swingAnalysis.ts\");\n/* harmony import */ var _barrel_optimize_names_format_subDays_date_fns__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=format,subDays!=!date-fns */ \"(rsc)/./node_modules/date-fns/format.js\");\n/* harmony import */ var _barrel_optimize_names_format_subDays_date_fns__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=format,subDays!=!date-fns */ \"(rsc)/./node_modules/date-fns/subDays.js\");\n\n\n\n\nasync function GET(request, { params }) {\n    try {\n        const { symbol } = await params;\n        const { searchParams } = new URL(request.url);\n        const timeframe = searchParams.get('timeframe') || '1D';\n        const days = parseInt(searchParams.get('days') || '100');\n        if (!symbol) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Symbol parameter is required'\n            }, {\n                status: 400\n            });\n        }\n        // Get historical data for analysis\n        const polygonAPI = new _lib_polygon__WEBPACK_IMPORTED_MODULE_1__.PolygonAPI(process.env.POLYGON_API_KEY);\n        const to = (0,_barrel_optimize_names_format_subDays_date_fns__WEBPACK_IMPORTED_MODULE_3__.format)(new Date(), 'yyyy-MM-dd');\n        const from = (0,_barrel_optimize_names_format_subDays_date_fns__WEBPACK_IMPORTED_MODULE_3__.format)((0,_barrel_optimize_names_format_subDays_date_fns__WEBPACK_IMPORTED_MODULE_4__.subDays)(new Date(), days), 'yyyy-MM-dd');\n        const historicalData = await polygonAPI.getHistoricalData(symbol.toUpperCase(), 'day', 1, from, to);\n        if (historicalData.length < 50) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Insufficient historical data for analysis'\n            }, {\n                status: 400\n            });\n        }\n        // Perform swing trading analysis\n        const analysis = _lib_swingAnalysis__WEBPACK_IMPORTED_MODULE_2__.SwingTradingAnalyzer.analyzeSwingTrade(symbol.toUpperCase(), historicalData, timeframe);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(analysis);\n    } catch (error) {\n        console.error('Error in swing analysis API:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to perform swing trading analysis'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/analysis/swing/[symbol]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/indicators.ts":
/*!*******************************!*\
  !*** ./src/lib/indicators.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TechnicalIndicators: () => (/* binding */ TechnicalIndicators)\n/* harmony export */ });\nclass TechnicalIndicators {\n    // Simple Moving Average\n    static sma(data, period) {\n        const result = [];\n        for(let i = period - 1; i < data.length; i++){\n            const sum = data.slice(i - period + 1, i + 1).reduce((a, b)=>a + b, 0);\n            result.push(sum / period);\n        }\n        return result;\n    }\n    // Exponential Moving Average\n    static ema(data, period) {\n        const result = [];\n        const multiplier = 2 / (period + 1);\n        // Start with SMA for first value\n        let ema = data.slice(0, period).reduce((a, b)=>a + b, 0) / period;\n        result.push(ema);\n        for(let i = period; i < data.length; i++){\n            ema = data[i] * multiplier + ema * (1 - multiplier);\n            result.push(ema);\n        }\n        return result;\n    }\n    // Relative Strength Index\n    static rsi(data, period = 14) {\n        const gains = [];\n        const losses = [];\n        for(let i = 1; i < data.length; i++){\n            const change = data[i] - data[i - 1];\n            gains.push(change > 0 ? change : 0);\n            losses.push(change < 0 ? Math.abs(change) : 0);\n        }\n        const avgGains = this.sma(gains, period);\n        const avgLosses = this.sma(losses, period);\n        return avgGains.map((gain, i)=>{\n            const rs = gain / avgLosses[i];\n            return 100 - 100 / (1 + rs);\n        });\n    }\n    // MACD (Moving Average Convergence Divergence)\n    static macd(data, fastPeriod = 12, slowPeriod = 26, signalPeriod = 9) {\n        const fastEMA = this.ema(data, fastPeriod);\n        const slowEMA = this.ema(data, slowPeriod);\n        // Align arrays (slowEMA starts later)\n        const startIndex = slowPeriod - fastPeriod;\n        const macdLine = fastEMA.slice(startIndex).map((fast, i)=>fast - slowEMA[i]);\n        const signalLine = this.ema(macdLine, signalPeriod);\n        const histogram = macdLine.slice(signalPeriod - 1).map((macd, i)=>macd - signalLine[i]);\n        return {\n            macd: macdLine,\n            signal: signalLine,\n            histogram\n        };\n    }\n    // Bollinger Bands\n    static bollingerBands(data, period = 20, stdDev = 2) {\n        const sma = this.sma(data, period);\n        const bands = sma.map((avg, i)=>{\n            const slice = data.slice(i, i + period);\n            const variance = slice.reduce((sum, val)=>sum + Math.pow(val - avg, 2), 0) / period;\n            const standardDeviation = Math.sqrt(variance);\n            return {\n                upper: avg + standardDeviation * stdDev,\n                middle: avg,\n                lower: avg - standardDeviation * stdDev\n            };\n        });\n        return bands;\n    }\n    // Support and Resistance Levels\n    static findSupportResistance(candles, lookback = 20) {\n        const highs = candles.map((c)=>c.high);\n        const lows = candles.map((c)=>c.low);\n        const resistance = [];\n        const support = [];\n        for(let i = lookback; i < candles.length - lookback; i++){\n            const currentHigh = highs[i];\n            const currentLow = lows[i];\n            // Check if current high is a local maximum\n            const isResistance = highs.slice(i - lookback, i).every((h)=>h <= currentHigh) && highs.slice(i + 1, i + lookback + 1).every((h)=>h <= currentHigh);\n            // Check if current low is a local minimum\n            const isSupport = lows.slice(i - lookback, i).every((l)=>l >= currentLow) && lows.slice(i + 1, i + lookback + 1).every((l)=>l >= currentLow);\n            if (isResistance) resistance.push(currentHigh);\n            if (isSupport) support.push(currentLow);\n        }\n        return {\n            support,\n            resistance\n        };\n    }\n    // Volume analysis\n    static volumeAnalysis(candles, period = 20) {\n        const volumes = candles.map((c)=>c.volume);\n        const avgVolume = this.sma(volumes, period);\n        const currentVolume = volumes[volumes.length - 1];\n        const currentAvgVolume = avgVolume[avgVolume.length - 1];\n        return {\n            currentVolume,\n            averageVolume: currentAvgVolume,\n            volumeRatio: currentVolume / currentAvgVolume,\n            isHighVolume: currentVolume > currentAvgVolume * 1.5,\n            isLowVolume: currentVolume < currentAvgVolume * 0.5\n        };\n    }\n    // Swing Trading Analysis\n    static analyzeSwingSetup(candles) {\n        const closes = candles.map((c)=>c.close);\n        const indicators = [];\n        // RSI Analysis\n        const rsi = this.rsi(closes);\n        const currentRSI = rsi[rsi.length - 1];\n        let rsiSignal = 'NEUTRAL';\n        let rsiDescription = `RSI: ${currentRSI.toFixed(2)}`;\n        if (currentRSI < 30) {\n            rsiSignal = 'BUY';\n            rsiDescription += ' - Oversold condition, potential bounce';\n        } else if (currentRSI > 70) {\n            rsiSignal = 'SELL';\n            rsiDescription += ' - Overbought condition, potential pullback';\n        } else {\n            rsiDescription += ' - Neutral zone';\n        }\n        indicators.push({\n            name: 'RSI',\n            value: currentRSI,\n            signal: rsiSignal,\n            description: rsiDescription\n        });\n        // Moving Average Analysis\n        const sma20 = this.sma(closes, 20);\n        const sma50 = this.sma(closes, 50);\n        const currentPrice = closes[closes.length - 1];\n        const currentSMA20 = sma20[sma20.length - 1];\n        const currentSMA50 = sma50[sma50.length - 1];\n        let maSignal = 'NEUTRAL';\n        let maDescription = `Price vs SMA20: ${((currentPrice / currentSMA20 - 1) * 100).toFixed(2)}%`;\n        if (currentPrice > currentSMA20 && currentSMA20 > currentSMA50) {\n            maSignal = 'BUY';\n            maDescription += ' - Bullish trend';\n        } else if (currentPrice < currentSMA20 && currentSMA20 < currentSMA50) {\n            maSignal = 'SELL';\n            maDescription += ' - Bearish trend';\n        } else {\n            maDescription += ' - Mixed signals';\n        }\n        indicators.push({\n            name: 'Moving Averages',\n            value: (currentPrice / currentSMA20 - 1) * 100,\n            signal: maSignal,\n            description: maDescription\n        });\n        // MACD Analysis\n        const macdData = this.macd(closes);\n        const currentMACD = macdData.macd[macdData.macd.length - 1];\n        const currentSignal = macdData.signal[macdData.signal.length - 1];\n        const currentHistogram = macdData.histogram[macdData.histogram.length - 1];\n        let macdSignal = 'NEUTRAL';\n        let macdDescription = `MACD: ${currentMACD.toFixed(4)}, Signal: ${currentSignal.toFixed(4)}`;\n        if (currentMACD > currentSignal && currentHistogram > 0) {\n            macdSignal = 'BUY';\n            macdDescription += ' - Bullish momentum';\n        } else if (currentMACD < currentSignal && currentHistogram < 0) {\n            macdSignal = 'SELL';\n            macdDescription += ' - Bearish momentum';\n        } else {\n            macdDescription += ' - Momentum shifting';\n        }\n        indicators.push({\n            name: 'MACD',\n            value: currentHistogram,\n            signal: macdSignal,\n            description: macdDescription\n        });\n        // Volume Analysis\n        const volumeData = this.volumeAnalysis(candles);\n        let volumeSignal = 'NEUTRAL';\n        let volumeDescription = `Volume: ${(volumeData.volumeRatio * 100).toFixed(0)}% of average`;\n        if (volumeData.isHighVolume) {\n            volumeSignal = 'BUY';\n            volumeDescription += ' - High volume confirms move';\n        } else if (volumeData.isLowVolume) {\n            volumeSignal = 'SELL';\n            volumeDescription += ' - Low volume, weak conviction';\n        } else {\n            volumeDescription += ' - Normal volume';\n        }\n        indicators.push({\n            name: 'Volume',\n            value: volumeData.volumeRatio,\n            signal: volumeSignal,\n            description: volumeDescription\n        });\n        return indicators;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/indicators.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/polygon.ts":
/*!****************************!*\
  !*** ./src/lib/polygon.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PolygonAPI: () => (/* binding */ PolygonAPI),\n/* harmony export */   polygonAPI: () => (/* binding */ polygonAPI)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/lib/axios.js\");\n\nconst POLYGON_BASE_URL = 'https://api.polygon.io';\nconst API_KEY = process.env.POLYGON_API_KEY;\nclass PolygonAPI {\n    constructor(apiKey){\n        this.apiKey = apiKey || API_KEY || '';\n        if (!this.apiKey) {\n            throw new Error('Polygon API key is required');\n        }\n    }\n    // Get current stock quote using Polygon's snapshot endpoint (better for paid plans)\n    async getStockQuote(symbol) {\n        try {\n            // Use snapshot endpoint for real-time data (available on paid plans)\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${POLYGON_BASE_URL}/v2/snapshot/locale/us/markets/stocks/tickers/${symbol}`, {\n                params: {\n                    apikey: this.apiKey\n                }\n            });\n            if (!response.data || !response.data.results || response.data.results.length === 0) {\n                throw new Error(`No data found for ${symbol}`);\n            }\n            const data = response.data.results[0];\n            const ticker = data.value || data;\n            const dayData = ticker.day || {};\n            const prevDayData = ticker.prevDay || {};\n            const lastQuote = ticker.lastQuote || {};\n            const lastTrade = ticker.lastTrade || {};\n            // Use the most recent price available\n            const currentPrice = lastTrade.p || dayData.c || prevDayData.c;\n            const prevClose = prevDayData.c || dayData.o;\n            const change = currentPrice - prevClose;\n            const changePercent = change / prevClose * 100;\n            return {\n                symbol: symbol.toUpperCase(),\n                name: ticker.name || symbol.toUpperCase(),\n                price: currentPrice,\n                change,\n                changePercent,\n                volume: dayData.v || 0,\n                marketCap: ticker.market_cap,\n                pe: undefined,\n                dividend: undefined\n            };\n        } catch (error) {\n            console.error('Error fetching stock quote from Polygon:', error);\n            // Fallback to previous day data if snapshot fails\n            try {\n                const fallbackResponse = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/prev`, {\n                    params: {\n                        adjusted: 'true',\n                        apikey: this.apiKey\n                    }\n                });\n                const data = fallbackResponse.data.results[0];\n                return {\n                    symbol: symbol.toUpperCase(),\n                    name: symbol.toUpperCase(),\n                    price: data.c,\n                    change: data.c - data.o,\n                    changePercent: (data.c - data.o) / data.o * 100,\n                    volume: data.v,\n                    marketCap: undefined,\n                    pe: undefined,\n                    dividend: undefined\n                };\n            } catch (fallbackError) {\n                console.error('Polygon fallback also failed:', fallbackError);\n                throw new Error(`Failed to fetch quote for ${symbol}`);\n            }\n        }\n    }\n    // Get historical candlestick data (optimized for paid plans)\n    async getHistoricalData(symbol, timespan = 'day', multiplier = 1, from, to) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/range/${multiplier}/${timespan}/${from}/${to}`, {\n                params: {\n                    adjusted: 'true',\n                    sort: 'asc',\n                    limit: 50000,\n                    apikey: this.apiKey\n                }\n            });\n            if (!response.data.results || response.data.results.length === 0) {\n                console.warn(`No historical data found for ${symbol}`);\n                return [];\n            }\n            return response.data.results.map((candle)=>({\n                    timestamp: candle.t,\n                    open: candle.o,\n                    high: candle.h,\n                    low: candle.l,\n                    close: candle.c,\n                    volume: candle.v\n                }));\n        } catch (error) {\n            console.error(`Error fetching historical data for ${symbol}:`, error);\n            // Log the specific error for debugging\n            if (error.response) {\n                console.error(`Polygon API Error: ${error.response.status} - ${error.response.statusText}`);\n                console.error('Response data:', error.response.data);\n            }\n            throw new Error(`Failed to fetch historical data for ${symbol}: ${error.message}`);\n        }\n    }\n    // Get company details\n    async getCompanyDetails(symbol) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${POLYGON_BASE_URL}/v3/reference/tickers/${symbol}`, {\n                params: {\n                    apikey: this.apiKey\n                }\n            });\n            return response.data.results;\n        } catch (error) {\n            console.error('Error fetching company details:', error);\n            return null;\n        }\n    }\n    // Get market status\n    async getMarketStatus() {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${POLYGON_BASE_URL}/v1/marketstatus/now`, {\n                params: {\n                    apikey: this.apiKey\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching market status:', error);\n            return null;\n        }\n    }\n    // Search for stocks\n    async searchStocks(query, limit = 10) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${POLYGON_BASE_URL}/v3/reference/tickers`, {\n                params: {\n                    search: query,\n                    market: 'stocks',\n                    active: 'true',\n                    limit,\n                    apikey: this.apiKey\n                }\n            });\n            return response.data.results || [];\n        } catch (error) {\n            console.error('Error searching stocks:', error);\n            return [];\n        }\n    }\n}\n// Create a singleton instance\nconst polygonAPI = new PolygonAPI();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/polygon.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/swingAnalysis.ts":
/*!**********************************!*\
  !*** ./src/lib/swingAnalysis.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SwingTradingAnalyzer: () => (/* binding */ SwingTradingAnalyzer)\n/* harmony export */ });\n/* harmony import */ var _indicators__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./indicators */ \"(rsc)/./src/lib/indicators.ts\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\nclass SwingTradingAnalyzer {\n    static analyzeSwingTrade(symbol, candles, timeframe = '1D') {\n        if (candles.length < 50) {\n            throw new Error('Insufficient data for swing trading analysis');\n        }\n        const closes = candles.map((c)=>c.close);\n        const highs = candles.map((c)=>c.high);\n        const lows = candles.map((c)=>c.low);\n        const currentPrice = closes[closes.length - 1];\n        // Get technical indicators\n        const indicators = _indicators__WEBPACK_IMPORTED_MODULE_0__.TechnicalIndicators.analyzeSwingSetup(candles);\n        // Find support and resistance levels\n        const { support, resistance } = _indicators__WEBPACK_IMPORTED_MODULE_0__.TechnicalIndicators.findSupportResistance(candles);\n        // Determine trend\n        const trend = this.determineTrend(candles);\n        // Calculate entry, stop loss, and take profit levels\n        const levels = this.calculateTradingLevels(candles, trend);\n        // Calculate confidence score\n        const confidence = this.calculateConfidence(indicators, trend, levels);\n        // Generate recommendation\n        const recommendation = this.generateRecommendation(indicators, confidence, trend);\n        // Generate analysis text\n        const analysis = this.generateAnalysisText(symbol, indicators, trend, levels, confidence);\n        return {\n            symbol,\n            timeframe,\n            trend: trend.direction,\n            confidence,\n            entryPrice: levels.entry,\n            stopLoss: levels.stopLoss,\n            takeProfit: levels.takeProfit,\n            riskRewardRatio: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.calculateRiskReward)(levels.entry, levels.stopLoss, levels.takeProfit),\n            indicators,\n            supportLevels: support.slice(-3),\n            resistanceLevels: resistance.slice(-3),\n            analysis,\n            recommendation\n        };\n    }\n    static determineTrend(candles) {\n        const closes = candles.map((c)=>c.close);\n        const sma20 = _indicators__WEBPACK_IMPORTED_MODULE_0__.TechnicalIndicators.sma(closes, 20);\n        const sma50 = _indicators__WEBPACK_IMPORTED_MODULE_0__.TechnicalIndicators.sma(closes, 50);\n        const currentPrice = closes[closes.length - 1];\n        const currentSMA20 = sma20[sma20.length - 1];\n        const currentSMA50 = sma50[sma50.length - 1];\n        // Calculate trend strength based on price position relative to moving averages\n        let strength = 0;\n        let direction = 'SIDEWAYS';\n        if (currentPrice > currentSMA20 && currentSMA20 > currentSMA50) {\n            direction = 'BULLISH';\n            strength = Math.min((currentPrice / currentSMA50 - 1) * 100, 100);\n        } else if (currentPrice < currentSMA20 && currentSMA20 < currentSMA50) {\n            direction = 'BEARISH';\n            strength = Math.min((currentSMA50 / currentPrice - 1) * 100, 100);\n        } else {\n            // Sideways trend\n            const volatility = this.calculateVolatility(closes.slice(-20));\n            strength = Math.max(0, 50 - volatility * 10);\n        }\n        return {\n            direction,\n            strength: Math.abs(strength)\n        };\n    }\n    static calculateTradingLevels(candles, trend) {\n        const closes = candles.map((c)=>c.close);\n        const highs = candles.map((c)=>c.high);\n        const lows = candles.map((c)=>c.low);\n        const currentPrice = closes[closes.length - 1];\n        // Calculate ATR for stop loss placement\n        const atr = this.calculateATR(candles, 14);\n        const currentATR = atr[atr.length - 1];\n        let entry = currentPrice;\n        let stopLoss;\n        let takeProfit;\n        if (trend.direction === 'BULLISH') {\n            // For bullish trend, look for pullback entry\n            const recentLow = Math.min(...lows.slice(-10));\n            entry = Math.max(recentLow * 1.005, currentPrice * 0.995); // Slight pullback entry\n            stopLoss = entry - currentATR * 2;\n            takeProfit = entry + currentATR * 3 // 1.5:1 risk-reward\n            ;\n        } else if (trend.direction === 'BEARISH') {\n            // For bearish trend, look for bounce entry (short)\n            const recentHigh = Math.max(...highs.slice(-10));\n            entry = Math.min(recentHigh * 0.995, currentPrice * 1.005); // Slight bounce entry\n            stopLoss = entry + currentATR * 2;\n            takeProfit = entry - currentATR * 3 // 1.5:1 risk-reward\n            ;\n        } else {\n            // Sideways trend - range trading\n            const recentHigh = Math.max(...highs.slice(-20));\n            const recentLow = Math.min(...lows.slice(-20));\n            const midpoint = (recentHigh + recentLow) / 2;\n            if (currentPrice < midpoint) {\n                // Near support, look for bounce\n                entry = currentPrice;\n                stopLoss = recentLow * 0.995;\n                takeProfit = recentHigh * 0.995;\n            } else {\n                // Near resistance, look for rejection\n                entry = currentPrice;\n                stopLoss = recentHigh * 1.005;\n                takeProfit = recentLow * 1.005;\n            }\n        }\n        return {\n            entry,\n            stopLoss,\n            takeProfit\n        };\n    }\n    static calculateATR(candles, period) {\n        const trueRanges = [];\n        for(let i = 1; i < candles.length; i++){\n            const high = candles[i].high;\n            const low = candles[i].low;\n            const prevClose = candles[i - 1].close;\n            const tr = Math.max(high - low, Math.abs(high - prevClose), Math.abs(low - prevClose));\n            trueRanges.push(tr);\n        }\n        return _indicators__WEBPACK_IMPORTED_MODULE_0__.TechnicalIndicators.sma(trueRanges, period);\n    }\n    static calculateVolatility(prices) {\n        const returns = [];\n        for(let i = 1; i < prices.length; i++){\n            returns.push((prices[i] - prices[i - 1]) / prices[i - 1]);\n        }\n        const mean = returns.reduce((sum, ret)=>sum + ret, 0) / returns.length;\n        const variance = returns.reduce((sum, ret)=>sum + Math.pow(ret - mean, 2), 0) / returns.length;\n        return Math.sqrt(variance) * Math.sqrt(252) // Annualized volatility\n        ;\n    }\n    static calculateConfidence(indicators, trend, levels) {\n        let confidence = 50 // Base confidence\n        ;\n        // Add confidence based on indicator alignment\n        const bullishSignals = indicators.filter((ind)=>ind.signal === 'BUY').length;\n        const bearishSignals = indicators.filter((ind)=>ind.signal === 'SELL').length;\n        const totalSignals = indicators.length;\n        if (bullishSignals > bearishSignals) {\n            confidence += bullishSignals / totalSignals * 30;\n        } else if (bearishSignals > bullishSignals) {\n            confidence += bearishSignals / totalSignals * 30;\n        }\n        // Add confidence based on trend strength\n        confidence += trend.strength / 100 * 20;\n        // Add confidence based on risk-reward ratio\n        const riskReward = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.calculateRiskReward)(levels.entry, levels.stopLoss, levels.takeProfit);\n        if (riskReward >= 2) {\n            confidence += 10;\n        } else if (riskReward >= 1.5) {\n            confidence += 5;\n        }\n        return Math.min(Math.max(confidence, 0), 100);\n    }\n    static generateRecommendation(indicators, confidence, trend) {\n        if (confidence < 40) {\n            return 'NO_TRADE';\n        }\n        const bullishSignals = indicators.filter((ind)=>ind.signal === 'BUY').length;\n        const bearishSignals = indicators.filter((ind)=>ind.signal === 'SELL').length;\n        if (bullishSignals > bearishSignals && confidence >= 70) {\n            return 'STRONG_BUY';\n        } else if (bullishSignals > bearishSignals && confidence >= 50) {\n            return 'BUY';\n        } else if (bearishSignals > bullishSignals && confidence >= 70) {\n            return 'STRONG_SELL';\n        } else if (bearishSignals > bullishSignals && confidence >= 50) {\n            return 'SELL';\n        } else {\n            return 'HOLD';\n        }\n    }\n    static generateAnalysisText(symbol, indicators, trend, levels, confidence) {\n        const riskReward = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.calculateRiskReward)(levels.entry, levels.stopLoss, levels.takeProfit);\n        let analysis = `${symbol} Swing Trading Analysis:\\n\\n`;\n        analysis += `Trend: ${trend.direction} (Strength: ${trend.strength.toFixed(1)}%)\\n`;\n        analysis += `Confidence: ${confidence.toFixed(1)}%\\n\\n`;\n        analysis += `Entry: $${levels.entry.toFixed(2)}\\n`;\n        analysis += `Stop Loss: $${levels.stopLoss.toFixed(2)}\\n`;\n        analysis += `Take Profit: $${levels.takeProfit.toFixed(2)}\\n`;\n        analysis += `Risk/Reward: ${riskReward.toFixed(2)}:1\\n\\n`;\n        analysis += `Technical Indicators:\\n`;\n        indicators.forEach((indicator)=>{\n            analysis += `• ${indicator.description}\\n`;\n        });\n        return analysis;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/swingAnalysis.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculatePositionSize: () => (/* binding */ calculatePositionSize),\n/* harmony export */   calculateRiskReward: () => (/* binding */ calculateRiskReward),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatPercentage: () => (/* binding */ formatPercentage)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatCurrency(value) {\n    return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency: 'USD'\n    }).format(value);\n}\nfunction formatPercentage(value) {\n    return new Intl.NumberFormat('en-US', {\n        style: 'percent',\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n    }).format(value / 100);\n}\nfunction calculateRiskReward(entryPrice, stopLoss, takeProfit) {\n    const risk = Math.abs(entryPrice - stopLoss);\n    const reward = Math.abs(takeProfit - entryPrice);\n    return reward / risk;\n}\nfunction calculatePositionSize(accountSize, riskPercentage, entryPrice, stopLoss) {\n    const riskAmount = accountSize * (riskPercentage / 100);\n    const riskPerShare = Math.abs(entryPrice - stopLoss);\n    return Math.floor(riskAmount / riskPerShare);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/date-fns","vendor-chunks/tailwind-merge","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalysis%2Fswing%2F%5Bsymbol%5D%2Froute&page=%2Fapi%2Fanalysis%2Fswing%2F%5Bsymbol%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalysis%2Fswing%2F%5Bsymbol%5D%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();