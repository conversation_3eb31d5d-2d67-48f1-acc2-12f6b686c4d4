module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},14747,(e,t,r)=>{t.exports=e.x("path",()=>require("path"))},78006,e=>{"use strict";e.s(["PolygonAPI",()=>s]);var t=e.i(55362);let r="https://api.polygon.io",a=process.env.POLYGON_API_KEY;class s{apiKey;constructor(e){if(this.apiKey=e||a||"",!this.apiKey)throw Error("Polygon API key is required")}async getStockQuote(e){try{let a=(await t.default.get(`${r}/v2/snapshot/locale/us/markets/stocks/tickers/${e}`,{params:{apikey:this.apiKey}})).data.results[0];if(!a)throw Error(`No data found for ${e}`);let s=a.value||a,o=s.day||{},n=s.prevDay||{};s.lastQuote;let i=(s.lastTrade||{}).p||o.c||n.c,l=n.c||o.o,c=i-l;return{symbol:e.toUpperCase(),name:s.name||e.toUpperCase(),price:i,change:c,changePercent:c/l*100,volume:o.v||0,marketCap:s.market_cap,pe:void 0,dividend:void 0}}catch(a){console.error("Error fetching stock quote from Polygon:",a);try{let a=(await t.default.get(`${r}/v2/aggs/ticker/${e}/prev`,{params:{adjusted:"true",apikey:this.apiKey}})).data.results[0];return{symbol:e.toUpperCase(),name:e.toUpperCase(),price:a.c,change:a.c-a.o,changePercent:(a.c-a.o)/a.o*100,volume:a.v,marketCap:void 0,pe:void 0,dividend:void 0}}catch(t){throw console.error("Polygon fallback also failed:",t),Error(`Failed to fetch quote for ${e}`)}}}async getHistoricalData(e,a="day",s=1,o,n){try{let i=await t.default.get(`${r}/v2/aggs/ticker/${e}/range/${s}/${a}/${o}/${n}`,{params:{adjusted:"true",sort:"asc",limit:5e4,apikey:this.apiKey}});if(!i.data.results||0===i.data.results.length)return console.warn(`No historical data found for ${e}`),[];return i.data.results.map(e=>({timestamp:e.t,open:e.o,high:e.h,low:e.l,close:e.c,volume:e.v}))}catch(t){throw console.error(`Error fetching historical data for ${e}:`,t),t.response&&(console.error(`Polygon API Error: ${t.response.status} - ${t.response.statusText}`),console.error("Response data:",t.response.data)),Error(`Failed to fetch historical data for ${e}: ${t.message}`)}}async getCompanyDetails(e){try{return(await t.default.get(`${r}/v3/reference/tickers/${e}`,{params:{apikey:this.apiKey}})).data.results}catch(e){return console.error("Error fetching company details:",e),null}}async getMarketStatus(){try{return(await t.default.get(`${r}/v1/marketstatus/now`,{params:{apikey:this.apiKey}})).data}catch(e){return console.error("Error fetching market status:",e),null}}async searchStocks(e,a=10){try{return(await t.default.get(`${r}/v3/reference/tickers`,{params:{search:e,market:"stocks",active:"true",limit:a,apikey:this.apiKey}})).data.results||[]}catch(e){return console.error("Error searching stocks:",e),[]}}}new s},7e3,(e,t,r)=>{},36007,e=>{"use strict";e.s(["handler",()=>A,"patchFetch",()=>P,"routeModule",()=>w,"serverHooks",()=>C,"workAsyncStorage",()=>k,"workUnitAsyncStorage",()=>E],36007);var t=e.i(11971),r=e.i(6780),a=e.i(51842),s=e.i(62950),o=e.i(21346),n=e.i(30506),i=e.i(63077),l=e.i(34765),c=e.i(64182),u=e.i(85062),p=e.i(51548),d=e.i(95133),h=e.i(8819),v=e.i(41050),g=e.i(93695);e.i(96641);var x=e.i(3893);e.s(["GET",()=>f],22514);var y=e.i(59169),m=e.i(78006);async function f(e){try{let{searchParams:t}=new URL(e.url),r=t.get("q"),a=parseInt(t.get("limit")||"10");if(!r)return y.NextResponse.json({error:"Query parameter is required"},{status:400});let s=new m.PolygonAPI(process.env.POLYGON_API_KEY),o=await s.searchStocks(r,a);return y.NextResponse.json(o)}catch(e){return console.error("Error in search API:",e),y.NextResponse.json({error:"Failed to search stocks"},{status:500})}}var R=e.i(22514);let w=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/stocks/search/route",pathname:"/api/stocks/search",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/swing-trader-ai/src/app/api/stocks/search/route.ts",nextConfigOutput:"",userland:R}),{workAsyncStorage:k,workUnitAsyncStorage:E,serverHooks:C}=w;function P(){return(0,a.patchFetch)({workAsyncStorage:k,workUnitAsyncStorage:E})}async function A(e,t,a){var y;let m="/api/stocks/search/route";m=m.replace(/\/index$/,"")||"/";let f=await w.prepare(e,t,{srcPage:m,multiZoneDraftMode:!1});if(!f)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:R,params:k,nextConfig:E,isDraftMode:C,prerenderManifest:P,routerServerContext:A,isOnDemandRevalidate:$,revalidateOnlyGenerated:N,resolvedPathname:b}=f,q=(0,n.normalizeAppPath)(m),O=!!(P.dynamicRoutes[q]||P.routes[b]);if(O&&!C){let e=!!P.routes[b],t=P.dynamicRoutes[q];if(t&&!1===t.fallback&&!e)throw new g.NoFallbackError}let T=null;!O||w.isDev||C||(T="/index"===(T=b)?"/":T);let _=!0===w.isDev||!O,j=O&&!_,I=e.method||"GET",S=(0,o.getTracer)(),U=S.getActiveScopeSpan(),K={params:k,prerenderManifest:P,renderOpts:{experimental:{cacheComponents:!!E.experimental.cacheComponents,authInterrupts:!!E.experimental.authInterrupts},supportsDynamicResponse:_,incrementalCache:(0,s.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(y=E.experimental)?void 0:y.cacheLife,isRevalidate:j,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>w.onRequestError(e,t,a,A)},sharedContext:{buildId:R}},H=new i.NodeNextRequest(e),D=new i.NodeNextResponse(t),M=l.NextRequestAdapter.fromNodeNextRequest(H,(0,l.signalFromNodeResponse)(t));try{let n=async r=>w.handle(M,K).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=S.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==c.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let s=a.get("next.route");if(s){let e=`${I} ${s}`;r.setAttributes({"next.route":s,"http.route":s,"next.span_name":e}),r.updateName(e)}else r.updateName(`${I} ${e.url}`)}),i=async o=>{var i,l;let c=async({previousCacheEntry:r})=>{try{if(!(0,s.getRequestMeta)(e,"minimalMode")&&$&&N&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let i=await n(o);e.fetchMetrics=K.renderOpts.fetchMetrics;let l=K.renderOpts.pendingWaitUntil;l&&a.waitUntil&&(a.waitUntil(l),l=void 0);let c=K.renderOpts.collectedTags;if(!O)return await (0,p.sendResponse)(H,D,i,K.renderOpts.pendingWaitUntil),null;{let e=await i.blob(),t=(0,d.toNodeOutgoingHttpHeaders)(i.headers);c&&(t[v.NEXT_CACHE_TAGS_HEADER]=c),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==K.renderOpts.collectedRevalidate&&!(K.renderOpts.collectedRevalidate>=v.INFINITE_CACHE)&&K.renderOpts.collectedRevalidate,a=void 0===K.renderOpts.collectedExpire||K.renderOpts.collectedExpire>=v.INFINITE_CACHE?void 0:K.renderOpts.collectedExpire;return{value:{kind:x.CachedRouteKind.APP_ROUTE,status:i.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await w.onRequestError(e,t,{routerKind:"App Router",routePath:m,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:j,isOnDemandRevalidate:$})},A),t}},g=await w.handleResponse({req:e,nextConfig:E,cacheKey:T,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:P,isRoutePPREnabled:!1,isOnDemandRevalidate:$,revalidateOnlyGenerated:N,responseGenerator:c,waitUntil:a.waitUntil});if(!O)return null;if((null==g||null==(i=g.value)?void 0:i.kind)!==x.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==g||null==(l=g.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,s.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",$?"REVALIDATED":g.isMiss?"MISS":g.isStale?"STALE":"HIT"),C&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let y=(0,d.fromNodeOutgoingHttpHeaders)(g.value.headers);return(0,s.getRequestMeta)(e,"minimalMode")&&O||y.delete(v.NEXT_CACHE_TAGS_HEADER),!g.cacheControl||t.getHeader("Cache-Control")||y.get("Cache-Control")||y.set("Cache-Control",(0,h.getCacheControlHeader)(g.cacheControl)),await (0,p.sendResponse)(H,D,new Response(g.value.body,{headers:y,status:g.value.status||200})),null};U?await i(U):await S.withPropagatedContext(e.headers,()=>S.trace(c.BaseServerSpan.handleRequest,{spanName:`${I} ${e.url}`,kind:o.SpanKind.SERVER,attributes:{"http.method":I,"http.target":e.url}},i))}catch(t){if(t instanceof g.NoFallbackError||await w.onRequestError(e,t,{routerKind:"App Router",routePath:q,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:j,isOnDemandRevalidate:$})}),O)throw t;return await (0,p.sendResponse)(H,D,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__9ea9bc0e._.js.map