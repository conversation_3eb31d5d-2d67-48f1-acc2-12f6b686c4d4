module.exports=[65058,e=>{"use strict";let t,s,n,r;e.s(["handler",()=>nN,"patchFetch",()=>nC,"routeModule",()=>nO,"serverHooks",()=>nP,"workAsyncStorage",()=>nI,"workUnitAsyncStorage",()=>nE],65058);var a,i,o,l,c,u,h,d,p,f,m,g,y,w,_,b,v,x,S,$,A,R,k,O,I,E,P,C,N,T,j,M,L,D,B,U,W,q,F,H,X,J,K,z,V,G,Y,Q,Z,ee,et,es,en,er,ea,ei,eo,el,ec,eu,eh,ed,ep,ef,em,eg,ey,ew,e_,eb,ev,ex,eS,e$,eA,eR,ek=e.i(11971),eO=e.i(6780),eI=e.i(51842),eE=e.i(62950),eP=e.i(21346),eC=e.i(30506),eN=e.i(63077),eT=e.i(34765),ej=e.i(64182),eM=e.i(85062),eL=e.i(51548),eD=e.i(95133),eB=e.i(8819),eU=e.i(41050),eW=e.i(93695);e.i(96641);var eq=e.i(3893);e.s(["GET",()=>nA,"POST",()=>nR],89095);var eF=e.i(59169);function eH(e,t,s,n,r){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!r)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?r.call(e,s):r?r.value=s:t.set(e,s),s}function eX(e,t,s,n){if("a"===s&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===s?n:"a"===s?n.call(e):n?n.value:t.get(e)}let eJ=function(){let{crypto:e}=globalThis;if(e?.randomUUID)return eJ=e.randomUUID.bind(e),e.randomUUID();let t=new Uint8Array(1),s=e?()=>e.getRandomValues(t)[0]:()=>255*Math.random()&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,e=>(e^s()&15>>e/4).toString(16))};function eK(e){return"object"==typeof e&&null!==e&&("name"in e&&"AbortError"===e.name||"message"in e&&String(e.message).includes("FetchRequestCanceledException"))}let ez=e=>{if(e instanceof Error)return e;if("object"==typeof e&&null!==e){try{if("[object Error]"===Object.prototype.toString.call(e)){let t=Error(e.message,e.cause?{cause:e.cause}:{});return e.stack&&(t.stack=e.stack),e.cause&&!t.cause&&(t.cause=e.cause),e.name&&(t.name=e.name),t}}catch{}try{return Error(JSON.stringify(e))}catch{}}return Error(e)};class eV extends Error{}class eG extends eV{constructor(e,t,s,n){super(`${eG.makeMessage(e,t,s)}`),this.status=e,this.headers=n,this.requestID=n?.get("x-request-id"),this.error=t,this.code=t?.code,this.param=t?.param,this.type=t?.type}static makeMessage(e,t,s){let n=t?.message?"string"==typeof t.message?t.message:JSON.stringify(t.message):t?JSON.stringify(t):s;return e&&n?`${e} ${n}`:e?`${e} status code (no body)`:n||"(no status code or body)"}static generate(e,t,s,n){if(!e||!n)return new eQ({message:s,cause:ez(t)});let r=t?.error;return 400===e?new e0(e,r,s,n):401===e?new e1(e,r,s,n):403===e?new e2(e,r,s,n):404===e?new e3(e,r,s,n):409===e?new e4(e,r,s,n):422===e?new e5(e,r,s,n):429===e?new e8(e,r,s,n):e>=500?new e6(e,r,s,n):new eG(e,r,s,n)}}class eY extends eG{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0)}}class eQ extends eG{constructor({message:e,cause:t}){super(void 0,void 0,e||"Connection error.",void 0),t&&(this.cause=t)}}class eZ extends eQ{constructor({message:e}={}){super({message:e??"Request timed out."})}}class e0 extends eG{}class e1 extends eG{}class e2 extends eG{}class e3 extends eG{}class e4 extends eG{}class e5 extends eG{}class e8 extends eG{}class e6 extends eG{}class e9 extends eV{constructor(){super("Could not parse response content as the length limit was reached")}}class e7 extends eV{constructor(){super("Could not parse response content as the request was rejected by the content filter")}}class te extends Error{constructor(e){super(e)}}let tt=/^[a-z][a-z0-9+.-]*:/i,ts=e=>(ts=Array.isArray)(e),tn=ts;function tr(e){return"object"!=typeof e?{}:e??{}}function ta(e){return null!=e&&"object"==typeof e&&!Array.isArray(e)}let ti=e=>new Promise(t=>setTimeout(t,e)),to="5.23.2",tl=e=>"x32"===e?"x32":"x86_64"===e||"x64"===e?"x64":"arm"===e?"arm":"aarch64"===e||"arm64"===e?"arm64":e?`other:${e}`:"unknown",tc=e=>(e=e.toLowerCase()).includes("ios")?"iOS":"android"===e?"Android":"darwin"===e?"MacOS":"win32"===e?"Windows":"freebsd"===e?"FreeBSD":"openbsd"===e?"OpenBSD":"linux"===e?"Linux":e?`Other:${e}`:"Unknown";function tu(...e){let t=globalThis.ReadableStream;if(void 0===t)throw Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new t(...e)}function th(e){let t=Symbol.asyncIterator in e?e[Symbol.asyncIterator]():e[Symbol.iterator]();return tu({start(){},async pull(e){let{done:s,value:n}=await t.next();s?e.close():e.enqueue(n)},async cancel(){await t.return?.()}})}function td(e){if(e[Symbol.asyncIterator])return e;let t=e.getReader();return{async next(){try{let e=await t.read();return e?.done&&t.releaseLock(),e}catch(e){throw t.releaseLock(),e}},async return(){let e=t.cancel();return t.releaseLock(),await e,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function tp(e){if(null===e||"object"!=typeof e)return;if(e[Symbol.asyncIterator])return void await e[Symbol.asyncIterator]().return?.();let t=e.getReader(),s=t.cancel();t.releaseLock(),await s}let tf=({headers:e,body:t})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(t)}),tm="RFC3986",tg=e=>String(e),ty={RFC1738:e=>String(e).replace(/%20/g,"+"),RFC3986:tg},tw=(e,t)=>(tw=Object.hasOwn??Function.prototype.call.bind(Object.prototype.hasOwnProperty))(e,t),t_=(()=>{let e=[];for(let t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e})();function tb(e,t){if(ts(e)){let s=[];for(let n=0;n<e.length;n+=1)s.push(t(e[n]));return s}return t(e)}let tv={brackets:e=>String(e)+"[]",comma:"comma",indices:(e,t)=>String(e)+"["+t+"]",repeat:e=>String(e)},tx=function(e,t){Array.prototype.push.apply(e,ts(t)?t:[t])},tS={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:(e,t,s,n,r)=>{if(0===e.length)return e;let a=e;if("symbol"==typeof e?a=Symbol.prototype.toString.call(e):"string"!=typeof e&&(a=String(e)),"iso-8859-1"===s)return escape(a).replace(/%u[0-9a-f]{4}/gi,function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"});let i="";for(let e=0;e<a.length;e+=1024){let t=a.length>=1024?a.slice(e,e+1024):a,s=[];for(let e=0;e<t.length;++e){let n=t.charCodeAt(e);if(45===n||46===n||95===n||126===n||n>=48&&n<=57||n>=65&&n<=90||n>=97&&n<=122||"RFC1738"===r&&(40===n||41===n)){s[s.length]=t.charAt(e);continue}if(n<128){s[s.length]=t_[n];continue}if(n<2048){s[s.length]=t_[192|n>>6]+t_[128|63&n];continue}if(n<55296||n>=57344){s[s.length]=t_[224|n>>12]+t_[128|n>>6&63]+t_[128|63&n];continue}e+=1,n=65536+((1023&n)<<10|1023&t.charCodeAt(e)),s[s.length]=t_[240|n>>18]+t_[128|n>>12&63]+t_[128|n>>6&63]+t_[128|63&n]}i+=s.join("")}return i},encodeValuesOnly:!1,format:tm,formatter:tg,indices:!1,serializeDate:e=>(s??(s=Function.prototype.call.bind(Date.prototype.toISOString)))(e),skipNulls:!1,strictNullHandling:!1},t$={};function tA(e){let t;return(n??(n=(t=new globalThis.TextEncoder).encode.bind(t)))(e)}function tR(e){let t;return(r??(r=(t=new globalThis.TextDecoder).decode.bind(t)))(e)}class tk{constructor(){a.set(this,void 0),i.set(this,void 0),eH(this,a,new Uint8Array,"f"),eH(this,i,null,"f")}decode(e){let t;if(null==e)return[];let s=e instanceof ArrayBuffer?new Uint8Array(e):"string"==typeof e?tA(e):e;eH(this,a,function(e){let t=0;for(let s of e)t+=s.length;let s=new Uint8Array(t),n=0;for(let t of e)s.set(t,n),n+=t.length;return s}([eX(this,a,"f"),s]),"f");let n=[];for(;null!=(t=function(e,t){for(let s=t??0;s<e.length;s++){if(10===e[s])return{preceding:s,index:s+1,carriage:!1};if(13===e[s])return{preceding:s,index:s+1,carriage:!0}}return null}(eX(this,a,"f"),eX(this,i,"f")));){if(t.carriage&&null==eX(this,i,"f")){eH(this,i,t.index,"f");continue}if(null!=eX(this,i,"f")&&(t.index!==eX(this,i,"f")+1||t.carriage)){n.push(tR(eX(this,a,"f").subarray(0,eX(this,i,"f")-1))),eH(this,a,eX(this,a,"f").subarray(eX(this,i,"f")),"f"),eH(this,i,null,"f");continue}let e=null!==eX(this,i,"f")?t.preceding-1:t.preceding,s=tR(eX(this,a,"f").subarray(0,e));n.push(s),eH(this,a,eX(this,a,"f").subarray(t.index),"f"),eH(this,i,null,"f")}return n}flush(){return eX(this,a,"f").length?this.decode("\n"):[]}}a=new WeakMap,i=new WeakMap,tk.NEWLINE_CHARS=new Set(["\n","\r"]),tk.NEWLINE_REGEXP=/\r\n|[\n\r]/g;let tO={off:0,error:200,warn:300,info:400,debug:500},tI=(e,t,s)=>{if(e){if(Object.prototype.hasOwnProperty.call(tO,e))return e;tT(s).warn(`${t} was set to ${JSON.stringify(e)}, expected one of ${JSON.stringify(Object.keys(tO))}`)}};function tE(){}function tP(e,t,s){return!t||tO[e]>tO[s]?tE:t[e].bind(t)}let tC={error:tE,warn:tE,info:tE,debug:tE},tN=new WeakMap;function tT(e){let t=e.logger,s=e.logLevel??"off";if(!t)return tC;let n=tN.get(t);if(n&&n[0]===s)return n[1];let r={error:tP("error",t,s),warn:tP("warn",t,s),info:tP("info",t,s),debug:tP("debug",t,s)};return tN.set(t,[s,r]),r}let tj=e=>(e.options&&(e.options={...e.options},delete e.options.headers),e.headers&&(e.headers=Object.fromEntries((e.headers instanceof Headers?[...e.headers]:Object.entries(e.headers)).map(([e,t])=>[e,"authorization"===e.toLowerCase()||"cookie"===e.toLowerCase()||"set-cookie"===e.toLowerCase()?"***":t]))),"retryOfRequestLogID"in e&&(e.retryOfRequestLogID&&(e.retryOf=e.retryOfRequestLogID),delete e.retryOfRequestLogID),e);class tM{constructor(e,t,s){this.iterator=e,o.set(this,void 0),this.controller=t,eH(this,o,s,"f")}static fromSSEResponse(e,t,s){let n=!1,r=s?tT(s):console;async function*a(){if(n)throw new eV("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");n=!0;let s=!1;try{for await(let n of tL(e,t))if(!s){if(n.data.startsWith("[DONE]")){s=!0;continue}if(null!==n.event&&n.event.startsWith("thread.")){let e;try{e=JSON.parse(n.data)}catch(e){throw console.error("Could not parse message into JSON:",n.data),console.error("From chunk:",n.raw),e}if("error"==n.event)throw new eG(void 0,e.error,e.message,void 0);yield{event:n.event,data:e}}else{let t;try{t=JSON.parse(n.data)}catch(e){throw r.error("Could not parse message into JSON:",n.data),r.error("From chunk:",n.raw),e}if(t&&t.error)throw new eG(void 0,t.error,void 0,e.headers);yield t}}s=!0}catch(e){if(eK(e))return;throw e}finally{s||t.abort()}}return new tM(a,t,s)}static fromReadableStream(e,t,s){let n=!1;async function*r(){let t=new tk;for await(let s of td(e))for(let e of t.decode(s))yield e;for(let e of t.flush())yield e}return new tM(async function*(){if(n)throw new eV("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");n=!0;let e=!1;try{for await(let t of r())!e&&t&&(yield JSON.parse(t));e=!0}catch(e){if(eK(e))return;throw e}finally{e||t.abort()}},t,s)}[(o=new WeakMap,Symbol.asyncIterator)](){return this.iterator()}tee(){let e=[],t=[],s=this.iterator(),n=n=>({next:()=>{if(0===n.length){let n=s.next();e.push(n),t.push(n)}return n.shift()}});return[new tM(()=>n(e),this.controller,eX(this,o,"f")),new tM(()=>n(t),this.controller,eX(this,o,"f"))]}toReadableStream(){let e,t=this;return tu({async start(){e=t[Symbol.asyncIterator]()},async pull(t){try{let{value:s,done:n}=await e.next();if(n)return t.close();let r=tA(JSON.stringify(s)+"\n");t.enqueue(r)}catch(e){t.error(e)}},async cancel(){await e.return?.()}})}}async function*tL(e,t){if(!e.body){if(t.abort(),void 0!==globalThis.navigator&&"ReactNative"===globalThis.navigator.product)throw new eV("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api");throw new eV("Attempted to iterate over a response with no body")}let s=new tB,n=new tk;for await(let t of tD(td(e.body)))for(let e of n.decode(t)){let t=s.decode(e);t&&(yield t)}for(let e of n.flush()){let t=s.decode(e);t&&(yield t)}}async function*tD(e){let t=new Uint8Array;for await(let s of e){let e;if(null==s)continue;let n=s instanceof ArrayBuffer?new Uint8Array(s):"string"==typeof s?tA(s):s,r=new Uint8Array(t.length+n.length);for(r.set(t),r.set(n,t.length),t=r;-1!==(e=function(e){for(let t=0;t<e.length-1;t++){if(10===e[t]&&10===e[t+1]||13===e[t]&&13===e[t+1])return t+2;if(13===e[t]&&10===e[t+1]&&t+3<e.length&&13===e[t+2]&&10===e[t+3])return t+4}return -1}(t));)yield t.slice(0,e),t=t.slice(e)}t.length>0&&(yield t)}class tB{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;let e={event:this.event,data:this.data.join("\n"),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],e}if(this.chunks.push(e),e.startsWith(":"))return null;let[t,s,n]=function(e,t){let s=e.indexOf(":");return -1!==s?[e.substring(0,s),t,e.substring(s+t.length)]:[e,"",""]}(e,":");return n.startsWith(" ")&&(n=n.substring(1)),"event"===t?this.event=n:"data"===t&&this.data.push(n),null}}async function tU(e,t){let{response:s,requestLogID:n,retryOfRequestLogID:r,startTime:a}=t,i=await (async()=>{if(t.options.stream)return(tT(e).debug("response",s.status,s.url,s.headers,s.body),t.options.__streamClass)?t.options.__streamClass.fromSSEResponse(s,t.controller,e):tM.fromSSEResponse(s,t.controller,e);if(204===s.status)return null;if(t.options.__binaryResponse)return s;let n=s.headers.get("content-type"),r=n?.split(";")[0]?.trim();return r?.includes("application/json")||r?.endsWith("+json")?tW(await s.json(),s):await s.text()})();return tT(e).debug(`[${n}] response parsed`,tj({retryOfRequestLogID:r,url:s.url,status:s.status,body:i,durationMs:Date.now()-a})),i}function tW(e,t){return!e||"object"!=typeof e||Array.isArray(e)?e:Object.defineProperty(e,"_request_id",{value:t.headers.get("x-request-id"),enumerable:!1})}class tq extends Promise{constructor(e,t,s=tU){super(e=>{e(null)}),this.responsePromise=t,this.parseResponse=s,l.set(this,void 0),eH(this,l,e,"f")}_thenUnwrap(e){return new tq(eX(this,l,"f"),this.responsePromise,async(t,s)=>tW(e(await this.parseResponse(t,s),s),s.response))}asResponse(){return this.responsePromise.then(e=>e.response)}async withResponse(){let[e,t]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:t,request_id:t.headers.get("x-request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(e=>this.parseResponse(eX(this,l,"f"),e))),this.parsedPromise}then(e,t){return this.parse().then(e,t)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}}l=new WeakMap;class tF{constructor(e,t,s,n){c.set(this,void 0),eH(this,c,e,"f"),this.options=n,this.response=t,this.body=s}hasNextPage(){return!!this.getPaginatedItems().length&&null!=this.nextPageRequestOptions()}async getNextPage(){let e=this.nextPageRequestOptions();if(!e)throw new eV("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await eX(this,c,"f").requestAPIList(this.constructor,e)}async *iterPages(){let e=this;for(yield e;e.hasNextPage();)e=await e.getNextPage(),yield e}async *[(c=new WeakMap,Symbol.asyncIterator)](){for await(let e of this.iterPages())for(let t of e.getPaginatedItems())yield t}}class tH extends tq{constructor(e,t,s){super(e,t,async(e,t)=>new s(e,t.response,await tU(e,t),t.options))}async *[Symbol.asyncIterator](){for await(let e of(await this))yield e}}class tX extends tF{constructor(e,t,s,n){super(e,t,s,n),this.data=s.data||[],this.object=s.object}getPaginatedItems(){return this.data??[]}nextPageRequestOptions(){return null}}class tJ extends tF{constructor(e,t,s,n){super(e,t,s,n),this.data=s.data||[],this.has_more=s.has_more||!1}getPaginatedItems(){return this.data??[]}hasNextPage(){return!1!==this.has_more&&super.hasNextPage()}nextPageRequestOptions(){let e=this.getPaginatedItems(),t=e[e.length-1]?.id;return t?{...this.options,query:{...tr(this.options.query),after:t}}:null}}class tK extends tF{constructor(e,t,s,n){super(e,t,s,n),this.data=s.data||[],this.has_more=s.has_more||!1,this.last_id=s.last_id||""}getPaginatedItems(){return this.data??[]}hasNextPage(){return!1!==this.has_more&&super.hasNextPage()}nextPageRequestOptions(){let e=this.last_id;return e?{...this.options,query:{...tr(this.options.query),after:e}}:null}}let tz=()=>{if("undefined"==typeof File){let{process:e}=globalThis;throw Error("`File` is not defined as a global, which is required for file uploads."+("string"==typeof e?.versions?.node&&20>parseInt(e.versions.node.split("."))?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function tV(e,t,s){return tz(),new File(e,t??"unknown_file",s)}function tG(e){return("object"==typeof e&&null!==e&&("name"in e&&e.name&&String(e.name)||"url"in e&&e.url&&String(e.url)||"filename"in e&&e.filename&&String(e.filename)||"path"in e&&e.path&&String(e.path))||"").split(/[\\/]/).pop()||void 0}let tY=e=>null!=e&&"object"==typeof e&&"function"==typeof e[Symbol.asyncIterator],tQ=async(e,t)=>({...e,body:await t0(e.body,t)}),tZ=new WeakMap,t0=async(e,t)=>{if(!await function(e){let t="function"==typeof e?e:e.fetch,s=tZ.get(t);if(s)return s;let n=(async()=>{try{let e="Response"in t?t.Response:(await t("data:,")).constructor,s=new FormData;if(s.toString()===await new e(s).text())return!1;return!0}catch{return!0}})();return tZ.set(t,n),n}(t))throw TypeError("The provided fetch function does not support file uploads with the current global FormData class.");let s=new FormData;return await Promise.all(Object.entries(e||{}).map(([e,t])=>t3(s,e,t))),s},t1=e=>e instanceof Blob&&"name"in e,t2=e=>{if((e=>"object"==typeof e&&null!==e&&(e instanceof Response||tY(e)||t1(e)))(e))return!0;if(Array.isArray(e))return e.some(t2);if(e&&"object"==typeof e){for(let t in e)if(t2(e[t]))return!0}return!1},t3=async(e,t,s)=>{if(void 0!==s){if(null==s)throw TypeError(`Received null for "${t}"; to pass null in FormData, you must use the string 'null'`);if("string"==typeof s||"number"==typeof s||"boolean"==typeof s)e.append(t,String(s));else if(s instanceof Response)e.append(t,tV([await s.blob()],tG(s)));else if(tY(s))e.append(t,tV([await new Response(th(s)).blob()],tG(s)));else if(t1(s))e.append(t,s,tG(s));else if(Array.isArray(s))await Promise.all(s.map(s=>t3(e,t+"[]",s)));else if("object"==typeof s)await Promise.all(Object.entries(s).map(([s,n])=>t3(e,`${t}[${s}]`,n)));else throw TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${s} instead`)}},t4=e=>null!=e&&"object"==typeof e&&"number"==typeof e.size&&"string"==typeof e.type&&"function"==typeof e.text&&"function"==typeof e.slice&&"function"==typeof e.arrayBuffer;async function t5(e,t,s){let n,r;if(tz(),null!=(n=e=await e)&&"object"==typeof n&&"string"==typeof n.name&&"number"==typeof n.lastModified&&t4(n))return e instanceof File?e:tV([await e.arrayBuffer()],e.name);if(null!=(r=e)&&"object"==typeof r&&"string"==typeof r.url&&"function"==typeof r.blob){let n=await e.blob();return t||(t=new URL(e.url).pathname.split(/[\\/]/).pop()),tV(await t8(n),t,s)}let a=await t8(e);if(t||(t=tG(e)),!s?.type){let e=a.find(e=>"object"==typeof e&&"type"in e&&e.type);"string"==typeof e&&(s={...s,type:e})}return tV(a,t,s)}async function t8(e){let t=[];if("string"==typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer)t.push(e);else if(t4(e))t.push(e instanceof Blob?e:await e.arrayBuffer());else if(tY(e))for await(let s of e)t.push(...await t8(s));else{let t=e?.constructor?.name;throw Error(`Unexpected data type: ${typeof e}${t?`; constructor: ${t}`:""}${function(e){if("object"!=typeof e||null===e)return"";let t=Object.getOwnPropertyNames(e);return`; props: [${t.map(e=>`"${e}"`).join(", ")}]`}(e)}`)}return t}e.s([],69855);class t6{constructor(e){this._client=e}}function t9(e){return e.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}let t7=Object.freeze(Object.create(null)),se=((e=t9)=>function(t,...s){let n;if(1===t.length)return t[0];let r=!1,a=[],i=t.reduce((t,n,i)=>{/[?#]/.test(n)&&(r=!0);let o=s[i],l=(r?encodeURIComponent:e)(""+o);return i!==s.length&&(null==o||"object"==typeof o&&o.toString===Object.getPrototypeOf(Object.getPrototypeOf(o.hasOwnProperty??t7)??t7)?.toString)&&(l=o+"",a.push({start:t.length+n.length,length:l.length,error:`Value of type ${Object.prototype.toString.call(o).slice(8,-1)} is not a valid path parameter`})),t+n+(i===s.length?"":l)},""),o=i.split(/[?#]/,1)[0],l=/(?<=^|\/)(?:\.|%2e){1,2}(?=\/|$)/gi;for(;null!==(n=l.exec(o));)a.push({start:n.index,length:n[0].length,error:`Value "${n[0]}" can't be safely passed as a path parameter`});if(a.sort((e,t)=>e.start-t.start),a.length>0){let e=0,t=a.reduce((t,s)=>{let n=" ".repeat(s.start-e),r="^".repeat(s.length);return e=s.start+s.length,t+n+r},"");throw new eV(`Path parameters result in path with invalid segments:
${a.map(e=>e.error).join("\n")}
${i}
${t}`)}return i})(t9);class st extends t6{list(e,t={},s){return this._client.getAPIList(se`/chat/completions/${e}/messages`,tJ,{query:t,...s})}}function ss(e){return void 0!==e&&"function"in e&&void 0!==e.function}function sn(e){return e?.$brand==="auto-parseable-response-format"}function sr(e){return e?.$brand==="auto-parseable-tool"}function sa(e,t){let s=e.choices.map(e=>{var s,n;if("length"===e.finish_reason)throw new e9;if("content_filter"===e.finish_reason)throw new e7;return so(e.message.tool_calls),{...e,message:{...e.message,...e.message.tool_calls?{tool_calls:e.message.tool_calls?.map(e=>(function(e,t){let s=e.tools?.find(e=>ss(e)&&e.function?.name===t.function.name);return{...t,function:{...t.function,parsed_arguments:sr(s)?s.$parseRaw(t.function.arguments):s?.function.strict?JSON.parse(t.function.arguments):null}}})(t,e))??void 0}:void 0,parsed:e.message.content&&!e.message.refusal?(s=t,n=e.message.content,s.response_format?.type!=="json_schema"?null:s.response_format?.type==="json_schema"?"$parseRaw"in s.response_format?s.response_format.$parseRaw(n):JSON.parse(n):null):null}}});return{...e,choices:s}}function si(e){return!!sn(e.response_format)||(e.tools?.some(e=>sr(e)||"function"===e.type&&!0===e.function.strict)??!1)}function so(e){for(let t of e||[])if("function"!==t.type)throw new eV(`Currently only \`function\` tool calls are supported; Received \`${t.type}\``)}let sl=e=>e?.role==="assistant",sc=e=>e?.role==="tool";class su{constructor(){u.add(this),this.controller=new AbortController,h.set(this,void 0),d.set(this,()=>{}),p.set(this,()=>{}),f.set(this,void 0),m.set(this,()=>{}),g.set(this,()=>{}),y.set(this,{}),w.set(this,!1),_.set(this,!1),b.set(this,!1),v.set(this,!1),eH(this,h,new Promise((e,t)=>{eH(this,d,e,"f"),eH(this,p,t,"f")}),"f"),eH(this,f,new Promise((e,t)=>{eH(this,m,e,"f"),eH(this,g,t,"f")}),"f"),eX(this,h,"f").catch(()=>{}),eX(this,f,"f").catch(()=>{})}_run(e){setTimeout(()=>{e().then(()=>{this._emitFinal(),this._emit("end")},eX(this,u,"m",x).bind(this))},0)}_connected(){this.ended||(eX(this,d,"f").call(this),this._emit("connect"))}get ended(){return eX(this,w,"f")}get errored(){return eX(this,_,"f")}get aborted(){return eX(this,b,"f")}abort(){this.controller.abort()}on(e,t){return(eX(this,y,"f")[e]||(eX(this,y,"f")[e]=[])).push({listener:t}),this}off(e,t){let s=eX(this,y,"f")[e];if(!s)return this;let n=s.findIndex(e=>e.listener===t);return n>=0&&s.splice(n,1),this}once(e,t){return(eX(this,y,"f")[e]||(eX(this,y,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,s)=>{eH(this,v,!0,"f"),"error"!==e&&this.once("error",s),this.once(e,t)})}async done(){eH(this,v,!0,"f"),await eX(this,f,"f")}_emit(e,...t){if(eX(this,w,"f"))return;"end"===e&&(eH(this,w,!0,"f"),eX(this,m,"f").call(this));let s=eX(this,y,"f")[e];if(s&&(eX(this,y,"f")[e]=s.filter(e=>!e.once),s.forEach(({listener:e})=>e(...t))),"abort"===e){let e=t[0];eX(this,v,"f")||s?.length||Promise.reject(e),eX(this,p,"f").call(this,e),eX(this,g,"f").call(this,e),this._emit("end");return}if("error"===e){let e=t[0];eX(this,v,"f")||s?.length||Promise.reject(e),eX(this,p,"f").call(this,e),eX(this,g,"f").call(this,e),this._emit("end")}}_emitFinal(){}}h=new WeakMap,d=new WeakMap,p=new WeakMap,f=new WeakMap,m=new WeakMap,g=new WeakMap,y=new WeakMap,w=new WeakMap,_=new WeakMap,b=new WeakMap,v=new WeakMap,u=new WeakSet,x=function(e){if(eH(this,_,!0,"f"),e instanceof Error&&"AbortError"===e.name&&(e=new eY),e instanceof eY)return eH(this,b,!0,"f"),this._emit("abort",e);if(e instanceof eV)return this._emit("error",e);if(e instanceof Error){let t=new eV(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new eV(String(e)))};class sh extends su{constructor(){super(...arguments),S.add(this),this._chatCompletions=[],this.messages=[]}_addChatCompletion(e){this._chatCompletions.push(e),this._emit("chatCompletion",e);let t=e.choices[0]?.message;return t&&this._addMessage(t),e}_addMessage(e,t=!0){if("content"in e||(e.content=null),this.messages.push(e),t){if(this._emit("message",e),sc(e)&&e.content)this._emit("functionToolCallResult",e.content);else if(sl(e)&&e.tool_calls)for(let t of e.tool_calls)"function"===t.type&&this._emit("functionToolCall",t.function)}}async finalChatCompletion(){await this.done();let e=this._chatCompletions[this._chatCompletions.length-1];if(!e)throw new eV("stream ended without producing a ChatCompletion");return e}async finalContent(){return await this.done(),eX(this,S,"m",$).call(this)}async finalMessage(){return await this.done(),eX(this,S,"m",A).call(this)}async finalFunctionToolCall(){return await this.done(),eX(this,S,"m",R).call(this)}async finalFunctionToolCallResult(){return await this.done(),eX(this,S,"m",k).call(this)}async totalUsage(){return await this.done(),eX(this,S,"m",O).call(this)}allChatCompletions(){return[...this._chatCompletions]}_emitFinal(){let e=this._chatCompletions[this._chatCompletions.length-1];e&&this._emit("finalChatCompletion",e);let t=eX(this,S,"m",A).call(this);t&&this._emit("finalMessage",t);let s=eX(this,S,"m",$).call(this);s&&this._emit("finalContent",s);let n=eX(this,S,"m",R).call(this);n&&this._emit("finalFunctionToolCall",n);let r=eX(this,S,"m",k).call(this);null!=r&&this._emit("finalFunctionToolCallResult",r),this._chatCompletions.some(e=>e.usage)&&this._emit("totalUsage",eX(this,S,"m",O).call(this))}async _createChatCompletion(e,t,s){let n=s?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),eX(this,S,"m",I).call(this,t);let r=await e.chat.completions.create({...t,stream:!1},{...s,signal:this.controller.signal});return this._connected(),this._addChatCompletion(sa(r,t))}async _runChatCompletion(e,t,s){for(let e of t.messages)this._addMessage(e,!1);return await this._createChatCompletion(e,t,s)}async _runTools(e,t,s){let n="tool",{tool_choice:r="auto",stream:a,...i}=t,o="string"!=typeof r&&"function"===r.type&&r?.function?.name,{maxChatCompletions:l=10}=s||{},c=t.tools.map(e=>{if(sr(e)){if(!e.$callback)throw new eV("Tool given to `.runTools()` that does not have an associated function");return{type:"function",function:{function:e.$callback,name:e.function.name,description:e.function.description||"",parameters:e.function.parameters,parse:e.$parseRaw,strict:!0}}}return e}),u={};for(let e of c)"function"===e.type&&(u[e.function.name||e.function.function.name]=e.function);let h="tools"in t?c.map(e=>"function"===e.type?{type:"function",function:{name:e.function.name||e.function.function.name,parameters:e.function.parameters,description:e.function.description,strict:e.function.strict}}:e):void 0;for(let e of t.messages)this._addMessage(e,!1);for(let t=0;t<l;++t){let t=await this._createChatCompletion(e,{...i,tool_choice:r,tools:h,messages:[...this.messages]},s),a=t.choices[0]?.message;if(!a)throw new eV("missing message in ChatCompletion response");if(!a.tool_calls?.length)break;for(let e of a.tool_calls){let t;if("function"!==e.type)continue;let s=e.id,{name:r,arguments:a}=e.function,i=u[r];if(i){if(o&&o!==r){let e=`Invalid tool_call: ${JSON.stringify(r)}. ${JSON.stringify(o)} requested. Please try again`;this._addMessage({role:n,tool_call_id:s,content:e});continue}}else{let e=`Invalid tool_call: ${JSON.stringify(r)}. Available options are: ${Object.keys(u).map(e=>JSON.stringify(e)).join(", ")}. Please try again`;this._addMessage({role:n,tool_call_id:s,content:e});continue}try{t="function"==typeof i.parse?await i.parse(a):a}catch(t){let e=t instanceof Error?t.message:String(t);this._addMessage({role:n,tool_call_id:s,content:e});continue}let l=await i.function(t,this),c=eX(this,S,"m",E).call(this,l);if(this._addMessage({role:n,tool_call_id:s,content:c}),o)return}}}}S=new WeakSet,$=function(){return eX(this,S,"m",A).call(this).content??null},A=function(){let e=this.messages.length;for(;e-- >0;){let t=this.messages[e];if(sl(t))return{...t,content:t.content??null,refusal:t.refusal??null}}throw new eV("stream ended without producing a ChatCompletionMessage with role=assistant")},R=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(sl(t)&&t?.tool_calls?.length)return t.tool_calls.filter(e=>"function"===e.type).at(-1)?.function}},k=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(sc(t)&&null!=t.content&&"string"==typeof t.content&&this.messages.some(e=>"assistant"===e.role&&e.tool_calls?.some(e=>"function"===e.type&&e.id===t.tool_call_id)))return t.content}},O=function(){let e={completion_tokens:0,prompt_tokens:0,total_tokens:0};for(let{usage:t}of this._chatCompletions)t&&(e.completion_tokens+=t.completion_tokens,e.prompt_tokens+=t.prompt_tokens,e.total_tokens+=t.total_tokens);return e},I=function(e){if(null!=e.n&&e.n>1)throw new eV("ChatCompletion convenience helpers only support n=1 at this time. To use n>1, please use chat.completions.create() directly.")},E=function(e){return"string"==typeof e?e:void 0===e?"undefined":JSON.stringify(e)};class sd extends sh{static runTools(e,t,s){let n=new sd,r={...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"runTools"}};return n._run(()=>n._runTools(e,t,r)),n}_addMessage(e,t=!0){super._addMessage(e,t),sl(e)&&e.content&&this._emit("content",e.content)}}let sp={STR:1,NUM:2,ARR:4,OBJ:8,NULL:16,BOOL:32,NAN:64,INFINITY:128,MINUS_INFINITY:256,INF:384,ALL:511};class sf extends Error{}class sm extends Error{}let sg=e=>(function(e,t=sp.ALL){if("string"!=typeof e)throw TypeError(`expecting str, got ${typeof e}`);if(!e.trim())throw Error(`${e} is empty`);return((e,t)=>{let s=e.length,n=0,r=e=>{throw new sf(`${e} at position ${n}`)},a=e=>{throw new sm(`${e} at position ${n}`)},i=()=>(h(),n>=s&&r("Unexpected end of input"),'"'===e[n])?o():"{"===e[n]?l():"["===e[n]?c():"null"===e.substring(n,n+4)||sp.NULL&t&&s-n<4&&"null".startsWith(e.substring(n))?(n+=4,null):"true"===e.substring(n,n+4)||sp.BOOL&t&&s-n<4&&"true".startsWith(e.substring(n))?(n+=4,!0):"false"===e.substring(n,n+5)||sp.BOOL&t&&s-n<5&&"false".startsWith(e.substring(n))?(n+=5,!1):"Infinity"===e.substring(n,n+8)||sp.INFINITY&t&&s-n<8&&"Infinity".startsWith(e.substring(n))?(n+=8,1/0):"-Infinity"===e.substring(n,n+9)||sp.MINUS_INFINITY&t&&1<s-n&&s-n<9&&"-Infinity".startsWith(e.substring(n))?(n+=9,-1/0):"NaN"===e.substring(n,n+3)||sp.NAN&t&&s-n<3&&"NaN".startsWith(e.substring(n))?(n+=3,NaN):u(),o=()=>{let i=n,o=!1;for(n++;n<s&&('"'!==e[n]||o&&"\\"===e[n-1]);)o="\\"===e[n]&&!o,n++;if('"'==e.charAt(n))try{return JSON.parse(e.substring(i,++n-Number(o)))}catch(e){a(String(e))}else if(sp.STR&t)try{return JSON.parse(e.substring(i,n-Number(o))+'"')}catch(t){return JSON.parse(e.substring(i,e.lastIndexOf("\\"))+'"')}r("Unterminated string literal")},l=()=>{n++,h();let a={};try{for(;"}"!==e[n];){if(h(),n>=s&&sp.OBJ&t)return a;let r=o();h(),n++;try{let e=i();Object.defineProperty(a,r,{value:e,writable:!0,enumerable:!0,configurable:!0})}catch(e){if(sp.OBJ&t)return a;throw e}h(),","===e[n]&&n++}}catch(e){if(sp.OBJ&t)return a;r("Expected '}' at end of object")}return n++,a},c=()=>{n++;let s=[];try{for(;"]"!==e[n];)s.push(i()),h(),","===e[n]&&n++}catch(e){if(sp.ARR&t)return s;r("Expected ']' at end of array")}return n++,s},u=()=>{if(0===n){"-"===e&&sp.NUM&t&&r("Not sure what '-' is");try{return JSON.parse(e)}catch(s){if(sp.NUM&t)try{if("."===e[e.length-1])return JSON.parse(e.substring(0,e.lastIndexOf(".")));return JSON.parse(e.substring(0,e.lastIndexOf("e")))}catch(e){}a(String(s))}}let i=n;for("-"===e[n]&&n++;e[n]&&!",]}".includes(e[n]);)n++;n!=s||sp.NUM&t||r("Unterminated number literal");try{return JSON.parse(e.substring(i,n))}catch(s){"-"===e.substring(i,n)&&sp.NUM&t&&r("Not sure what '-' is");try{return JSON.parse(e.substring(i,e.lastIndexOf("e")))}catch(e){a(String(e))}}},h=()=>{for(;n<s&&" \n\r	".includes(e[n]);)n++};return i()})(e.trim(),t)})(e,sp.ALL^sp.NUM);class sy extends sh{constructor(e){super(),P.add(this),C.set(this,void 0),N.set(this,void 0),T.set(this,void 0),eH(this,C,e,"f"),eH(this,N,[],"f")}get currentChatCompletionSnapshot(){return eX(this,T,"f")}static fromReadableStream(e){let t=new sy(null);return t._run(()=>t._fromReadableStream(e)),t}static createChatCompletion(e,t,s){let n=new sy(t);return n._run(()=>n._runChatCompletion(e,{...t,stream:!0},{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),n}async _createChatCompletion(e,t,s){super._createChatCompletion;let n=s?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),eX(this,P,"m",j).call(this);let r=await e.chat.completions.create({...t,stream:!0},{...s,signal:this.controller.signal});for await(let e of(this._connected(),r))eX(this,P,"m",L).call(this,e);if(r.controller.signal?.aborted)throw new eY;return this._addChatCompletion(eX(this,P,"m",U).call(this))}async _fromReadableStream(e,t){let s,n=t?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),eX(this,P,"m",j).call(this),this._connected();let r=tM.fromReadableStream(e,this.controller);for await(let e of r)s&&s!==e.id&&this._addChatCompletion(eX(this,P,"m",U).call(this)),eX(this,P,"m",L).call(this,e),s=e.id;if(r.controller.signal?.aborted)throw new eY;return this._addChatCompletion(eX(this,P,"m",U).call(this))}[(C=new WeakMap,N=new WeakMap,T=new WeakMap,P=new WeakSet,j=function(){this.ended||eH(this,T,void 0,"f")},M=function(e){let t=eX(this,N,"f")[e.index];return t||(t={content_done:!1,refusal_done:!1,logprobs_content_done:!1,logprobs_refusal_done:!1,done_tool_calls:new Set,current_tool_call_index:null},eX(this,N,"f")[e.index]=t),t},L=function(e){if(this.ended)return;let t=eX(this,P,"m",q).call(this,e);for(let s of(this._emit("chunk",e,t),e.choices)){let e=t.choices[s.index];null!=s.delta.content&&e.message?.role==="assistant"&&e.message?.content&&(this._emit("content",s.delta.content,e.message.content),this._emit("content.delta",{delta:s.delta.content,snapshot:e.message.content,parsed:e.message.parsed})),null!=s.delta.refusal&&e.message?.role==="assistant"&&e.message?.refusal&&this._emit("refusal.delta",{delta:s.delta.refusal,snapshot:e.message.refusal}),s.logprobs?.content!=null&&e.message?.role==="assistant"&&this._emit("logprobs.content.delta",{content:s.logprobs?.content,snapshot:e.logprobs?.content??[]}),s.logprobs?.refusal!=null&&e.message?.role==="assistant"&&this._emit("logprobs.refusal.delta",{refusal:s.logprobs?.refusal,snapshot:e.logprobs?.refusal??[]});let n=eX(this,P,"m",M).call(this,e);for(let t of(e.finish_reason&&(eX(this,P,"m",B).call(this,e),null!=n.current_tool_call_index&&eX(this,P,"m",D).call(this,e,n.current_tool_call_index)),s.delta.tool_calls??[]))n.current_tool_call_index!==t.index&&(eX(this,P,"m",B).call(this,e),null!=n.current_tool_call_index&&eX(this,P,"m",D).call(this,e,n.current_tool_call_index)),n.current_tool_call_index=t.index;for(let t of s.delta.tool_calls??[]){let s=e.message.tool_calls?.[t.index];s?.type&&(s?.type==="function"?this._emit("tool_calls.function.arguments.delta",{name:s.function?.name,index:t.index,arguments:s.function.arguments,parsed_arguments:s.function.parsed_arguments,arguments_delta:t.function?.arguments??""}):sb(s?.type))}}},D=function(e,t){if(eX(this,P,"m",M).call(this,e).done_tool_calls.has(t))return;let s=e.message.tool_calls?.[t];if(!s)throw Error("no tool call snapshot");if(!s.type)throw Error("tool call snapshot missing `type`");if("function"===s.type){let e=eX(this,C,"f")?.tools?.find(e=>ss(e)&&e.function.name===s.function.name);this._emit("tool_calls.function.arguments.done",{name:s.function.name,index:t,arguments:s.function.arguments,parsed_arguments:sr(e)?e.$parseRaw(s.function.arguments):e?.function.strict?JSON.parse(s.function.arguments):null})}else sb(s.type)},B=function(e){let t=eX(this,P,"m",M).call(this,e);if(e.message.content&&!t.content_done){t.content_done=!0;let s=eX(this,P,"m",W).call(this);this._emit("content.done",{content:e.message.content,parsed:s?s.$parseRaw(e.message.content):null})}e.message.refusal&&!t.refusal_done&&(t.refusal_done=!0,this._emit("refusal.done",{refusal:e.message.refusal})),e.logprobs?.content&&!t.logprobs_content_done&&(t.logprobs_content_done=!0,this._emit("logprobs.content.done",{content:e.logprobs.content})),e.logprobs?.refusal&&!t.logprobs_refusal_done&&(t.logprobs_refusal_done=!0,this._emit("logprobs.refusal.done",{refusal:e.logprobs.refusal}))},U=function(){if(this.ended)throw new eV("stream has ended, this shouldn't happen");let e=eX(this,T,"f");if(!e)throw new eV("request ended without sending any chunks");return eH(this,T,void 0,"f"),eH(this,N,[],"f"),function(e,t){var s;let{id:n,choices:r,created:a,model:i,system_fingerprint:o,...l}=e;return s={...l,id:n,choices:r.map(({message:t,finish_reason:s,index:n,logprobs:r,...a})=>{if(!s)throw new eV(`missing finish_reason for choice ${n}`);let{content:i=null,function_call:o,tool_calls:l,...c}=t,u=t.role;if(!u)throw new eV(`missing role for choice ${n}`);if(o){let{arguments:e,name:l}=o;if(null==e)throw new eV(`missing function_call.arguments for choice ${n}`);if(!l)throw new eV(`missing function_call.name for choice ${n}`);return{...a,message:{content:i,function_call:{arguments:e,name:l},role:u,refusal:t.refusal??null},finish_reason:s,index:n,logprobs:r}}return l?{...a,index:n,finish_reason:s,logprobs:r,message:{...c,role:u,content:i,refusal:t.refusal??null,tool_calls:l.map((t,s)=>{let{function:r,type:a,id:i,...o}=t,{arguments:l,name:c,...u}=r||{};if(null==i)throw new eV(`missing choices[${n}].tool_calls[${s}].id
${sw(e)}`);if(null==a)throw new eV(`missing choices[${n}].tool_calls[${s}].type
${sw(e)}`);if(null==c)throw new eV(`missing choices[${n}].tool_calls[${s}].function.name
${sw(e)}`);if(null==l)throw new eV(`missing choices[${n}].tool_calls[${s}].function.arguments
${sw(e)}`);return{...o,id:i,type:a,function:{...u,name:c,arguments:l}}})}}:{...a,message:{...c,content:i,role:u,refusal:t.refusal??null},finish_reason:s,index:n,logprobs:r}}),created:a,model:i,object:"chat.completion",...o?{system_fingerprint:o}:{}},t&&si(t)?sa(s,t):{...s,choices:s.choices.map(e=>(so(e.message.tool_calls),{...e,message:{...e.message,parsed:null,...e.message.tool_calls?{tool_calls:e.message.tool_calls}:void 0}}))}}(e,eX(this,C,"f"))},W=function(){let e=eX(this,C,"f")?.response_format;return sn(e)?e:null},q=function(e){var t,s,n,r;let a=eX(this,T,"f"),{choices:i,...o}=e;for(let{delta:i,finish_reason:l,index:c,logprobs:u=null,...h}of(a?Object.assign(a,o):a=eH(this,T,{...o,choices:[]},"f"),e.choices)){let e=a.choices[c];if(e||(e=a.choices[c]={finish_reason:l,index:c,message:{},logprobs:u,...h}),u)if(e.logprobs){let{content:n,refusal:r,...a}=u;s_(a),Object.assign(e.logprobs,a),n&&((t=e.logprobs).content??(t.content=[]),e.logprobs.content.push(...n)),r&&((s=e.logprobs).refusal??(s.refusal=[]),e.logprobs.refusal.push(...r))}else e.logprobs=Object.assign({},u);if(l&&(e.finish_reason=l,eX(this,C,"f")&&si(eX(this,C,"f")))){if("length"===l)throw new e9;if("content_filter"===l)throw new e7}if(Object.assign(e,h),!i)continue;let{content:o,refusal:d,function_call:p,role:f,tool_calls:m,...g}=i;if(s_(g),Object.assign(e.message,g),d&&(e.message.refusal=(e.message.refusal||"")+d),f&&(e.message.role=f),p&&(e.message.function_call?(p.name&&(e.message.function_call.name=p.name),p.arguments&&((n=e.message.function_call).arguments??(n.arguments=""),e.message.function_call.arguments+=p.arguments)):e.message.function_call=p),o&&(e.message.content=(e.message.content||"")+o,!e.message.refusal&&eX(this,P,"m",W).call(this)&&(e.message.parsed=sg(e.message.content))),m)for(let{index:t,id:s,type:n,function:a,...i}of(e.message.tool_calls||(e.message.tool_calls=[]),m)){let o=(r=e.message.tool_calls)[t]??(r[t]={});Object.assign(o,i),s&&(o.id=s),n&&(o.type=n),a&&(o.function??(o.function={name:a.name??"",arguments:""})),a?.name&&(o.function.name=a.name),a?.arguments&&(o.function.arguments+=a.arguments,function(e,t){if(!e||!("tools"in e)||!e.tools)return!1;let s=e.tools?.find(e=>ss(e)&&e.function?.name===t.function.name);return ss(s)&&(sr(s)||s?.function.strict||!1)}(eX(this,C,"f"),o)&&(o.function.parsed_arguments=sg(o.function.arguments)))}}return a},Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("chunk",s=>{let n=t.shift();n?n.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),this.on("error",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new tM(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}function sw(e){return JSON.stringify(e)}function s_(e){}function sb(e){}class sv extends sy{static fromReadableStream(e){let t=new sv(null);return t._run(()=>t._fromReadableStream(e)),t}static runTools(e,t,s){let n=new sv(t),r={...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"runTools"}};return n._run(()=>n._runTools(e,t,r)),n}}class sx extends t6{constructor(){super(...arguments),this.messages=new st(this._client)}create(e,t){return this._client.post("/chat/completions",{body:e,...t,stream:e.stream??!1})}retrieve(e,t){return this._client.get(se`/chat/completions/${e}`,t)}update(e,t,s){return this._client.post(se`/chat/completions/${e}`,{body:t,...s})}list(e={},t){return this._client.getAPIList("/chat/completions",tJ,{query:e,...t})}delete(e,t){return this._client.delete(se`/chat/completions/${e}`,t)}parse(e,t){for(let t of e.tools??[]){if("function"!==t.type)throw new eV(`Currently only \`function\` tool types support auto-parsing; Received \`${t.type}\``);if(!0!==t.function.strict)throw new eV(`The \`${t.function.name}\` tool is not marked with \`strict: true\`. Only strict function tools can be auto-parsed`)}return this._client.chat.completions.create(e,{...t,headers:{...t?.headers,"X-Stainless-Helper-Method":"chat.completions.parse"}})._thenUnwrap(t=>sa(t,e))}runTools(e,t){return e.stream?sv.runTools(this._client,e,t):sd.runTools(this._client,e,t)}stream(e,t){return sy.createChatCompletion(this._client,e,t)}}sx.Messages=st;class sS extends t6{constructor(){super(...arguments),this.completions=new sx(this._client)}}sS.Completions=sx;let s$=Symbol("brand.privateNullableHeaders"),sA=e=>{let t=new Headers,s=new Set;for(let n of e){let e=new Set;for(let[r,a]of function*(e){let t;if(!e)return;if(s$ in e){let{values:t,nulls:s}=e;for(let e of(yield*t.entries(),s))yield[e,null];return}let s=!1;for(let n of(e instanceof Headers?t=e.entries():tn(e)?t=e:(s=!0,t=Object.entries(e??{})),t)){let e=n[0];if("string"!=typeof e)throw TypeError("expected header name to be a string");let t=tn(n[1])?n[1]:[n[1]],r=!1;for(let n of t)void 0!==n&&(s&&!r&&(r=!0,yield[e,null]),yield[e,n])}}(n)){let n=r.toLowerCase();e.has(n)||(t.delete(r),e.add(n)),null===a?(t.delete(r),s.add(n)):(t.append(r,a),s.delete(n))}}return{[s$]:!0,values:t,nulls:s}};class sR extends t6{create(e,t){return this._client.post("/audio/speech",{body:e,...t,headers:sA([{Accept:"application/octet-stream"},t?.headers]),__binaryResponse:!0})}}class sk extends t6{create(e,t){return this._client.post("/audio/transcriptions",tQ({body:e,...t,stream:e.stream??!1,__metadata:{model:e.model}},this._client))}}class sO extends t6{create(e,t){return this._client.post("/audio/translations",tQ({body:e,...t,__metadata:{model:e.model}},this._client))}}class sI extends t6{constructor(){super(...arguments),this.transcriptions=new sk(this._client),this.translations=new sO(this._client),this.speech=new sR(this._client)}}sI.Transcriptions=sk,sI.Translations=sO,sI.Speech=sR;class sE extends t6{create(e,t){return this._client.post("/batches",{body:e,...t})}retrieve(e,t){return this._client.get(se`/batches/${e}`,t)}list(e={},t){return this._client.getAPIList("/batches",tJ,{query:e,...t})}cancel(e,t){return this._client.post(se`/batches/${e}/cancel`,t)}}class sP extends t6{create(e,t){return this._client.post("/assistants",{body:e,...t,headers:sA([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(se`/assistants/${e}`,{...t,headers:sA([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,s){return this._client.post(se`/assistants/${e}`,{body:t,...s,headers:sA([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e={},t){return this._client.getAPIList("/assistants",tJ,{query:e,...t,headers:sA([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}delete(e,t){return this._client.delete(se`/assistants/${e}`,{...t,headers:sA([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class sC extends t6{create(e,t){return this._client.post("/realtime/sessions",{body:e,...t,headers:sA([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class sN extends t6{create(e,t){return this._client.post("/realtime/transcription_sessions",{body:e,...t,headers:sA([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class sT extends t6{constructor(){super(...arguments),this.sessions=new sC(this._client),this.transcriptionSessions=new sN(this._client)}}sT.Sessions=sC,sT.TranscriptionSessions=sN;class sj extends t6{create(e,t,s){return this._client.post(se`/threads/${e}/messages`,{body:t,...s,headers:sA([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}retrieve(e,t,s){let{thread_id:n}=t;return this._client.get(se`/threads/${n}/messages/${e}`,{...s,headers:sA([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}update(e,t,s){let{thread_id:n,...r}=t;return this._client.post(se`/threads/${n}/messages/${e}`,{body:r,...s,headers:sA([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t={},s){return this._client.getAPIList(se`/threads/${e}/messages`,tJ,{query:t,...s,headers:sA([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}delete(e,t,s){let{thread_id:n}=t;return this._client.delete(se`/threads/${n}/messages/${e}`,{...s,headers:sA([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}class sM extends t6{retrieve(e,t,s){let{thread_id:n,run_id:r,...a}=t;return this._client.get(se`/threads/${n}/runs/${r}/steps/${e}`,{query:a,...s,headers:sA([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t,s){let{thread_id:n,...r}=t;return this._client.getAPIList(se`/threads/${n}/runs/${e}/steps`,tJ,{query:r,...s,headers:sA([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}let sL=e=>void 0!==globalThis.process?globalThis.process.env?.[e]?.trim()??void 0:void 0!==globalThis.Deno?globalThis.Deno.env?.get?.(e)?.trim():void 0;class sD extends su{constructor(){super(...arguments),F.add(this),X.set(this,[]),J.set(this,{}),K.set(this,{}),z.set(this,void 0),V.set(this,void 0),G.set(this,void 0),Y.set(this,void 0),Q.set(this,void 0),Z.set(this,void 0),ee.set(this,void 0),et.set(this,void 0),es.set(this,void 0)}[(X=new WeakMap,J=new WeakMap,K=new WeakMap,z=new WeakMap,V=new WeakMap,G=new WeakMap,Y=new WeakMap,Q=new WeakMap,Z=new WeakMap,ee=new WeakMap,et=new WeakMap,es=new WeakMap,F=new WeakSet,Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("event",s=>{let n=t.shift();n?n.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),this.on("error",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}static fromReadableStream(e){let t=new H;return t._run(()=>t._fromReadableStream(e)),t}async _fromReadableStream(e,t){let s=t?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),this._connected();let n=tM.fromReadableStream(e,this.controller);for await(let e of n)eX(this,F,"m",en).call(this,e);if(n.controller.signal?.aborted)throw new eY;return this._addRun(eX(this,F,"m",er).call(this))}toReadableStream(){return new tM(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}static createToolAssistantStream(e,t,s,n){let r=new H;return r._run(()=>r._runToolAssistantStream(e,t,s,{...n,headers:{...n?.headers,"X-Stainless-Helper-Method":"stream"}})),r}async _createToolAssistantStream(e,t,s,n){let r=n?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort()));let a={...s,stream:!0},i=await e.submitToolOutputs(t,a,{...n,signal:this.controller.signal});for await(let e of(this._connected(),i))eX(this,F,"m",en).call(this,e);if(i.controller.signal?.aborted)throw new eY;return this._addRun(eX(this,F,"m",er).call(this))}static createThreadAssistantStream(e,t,s){let n=new H;return n._run(()=>n._threadAssistantStream(e,t,{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),n}static createAssistantStream(e,t,s,n){let r=new H;return r._run(()=>r._runAssistantStream(e,t,s,{...n,headers:{...n?.headers,"X-Stainless-Helper-Method":"stream"}})),r}currentEvent(){return eX(this,ee,"f")}currentRun(){return eX(this,et,"f")}currentMessageSnapshot(){return eX(this,z,"f")}currentRunStepSnapshot(){return eX(this,es,"f")}async finalRunSteps(){return await this.done(),Object.values(eX(this,J,"f"))}async finalMessages(){return await this.done(),Object.values(eX(this,K,"f"))}async finalRun(){if(await this.done(),!eX(this,V,"f"))throw Error("Final run was not received.");return eX(this,V,"f")}async _createThreadAssistantStream(e,t,s){let n=s?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort()));let r={...t,stream:!0},a=await e.createAndRun(r,{...s,signal:this.controller.signal});for await(let e of(this._connected(),a))eX(this,F,"m",en).call(this,e);if(a.controller.signal?.aborted)throw new eY;return this._addRun(eX(this,F,"m",er).call(this))}async _createAssistantStream(e,t,s,n){let r=n?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort()));let a={...s,stream:!0},i=await e.create(t,a,{...n,signal:this.controller.signal});for await(let e of(this._connected(),i))eX(this,F,"m",en).call(this,e);if(i.controller.signal?.aborted)throw new eY;return this._addRun(eX(this,F,"m",er).call(this))}static accumulateDelta(e,t){for(let[s,n]of Object.entries(t)){if(!e.hasOwnProperty(s)){e[s]=n;continue}let t=e[s];if(null==t||"index"===s||"type"===s){e[s]=n;continue}if("string"==typeof t&&"string"==typeof n)t+=n;else if("number"==typeof t&&"number"==typeof n)t+=n;else if(ta(t)&&ta(n))t=this.accumulateDelta(t,n);else if(Array.isArray(t)&&Array.isArray(n)){if(t.every(e=>"string"==typeof e||"number"==typeof e)){t.push(...n);continue}for(let e of n){if(!ta(e))throw Error(`Expected array delta entry to be an object but got: ${e}`);let s=e.index;if(null==s)throw console.error(e),Error("Expected array delta entry to have an `index` property");if("number"!=typeof s)throw Error(`Expected array delta entry \`index\` property to be a number but got ${s}`);let n=t[s];null==n?t.push(e):t[s]=this.accumulateDelta(n,e)}continue}else throw Error(`Unhandled record type: ${s}, deltaValue: ${n}, accValue: ${t}`);e[s]=t}return e}_addRun(e){return e}async _threadAssistantStream(e,t,s){return await this._createThreadAssistantStream(t,e,s)}async _runAssistantStream(e,t,s,n){return await this._createAssistantStream(t,e,s,n)}async _runToolAssistantStream(e,t,s,n){return await this._createToolAssistantStream(t,e,s,n)}}H=sD,en=function(e){if(!this.ended)switch(eH(this,ee,e,"f"),eX(this,F,"m",eo).call(this,e),e.event){case"thread.created":break;case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.requires_action":case"thread.run.completed":case"thread.run.incomplete":case"thread.run.failed":case"thread.run.cancelling":case"thread.run.cancelled":case"thread.run.expired":eX(this,F,"m",eh).call(this,e);break;case"thread.run.step.created":case"thread.run.step.in_progress":case"thread.run.step.delta":case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":eX(this,F,"m",ei).call(this,e);break;case"thread.message.created":case"thread.message.in_progress":case"thread.message.delta":case"thread.message.completed":case"thread.message.incomplete":eX(this,F,"m",ea).call(this,e);break;case"error":throw Error("Encountered an error event in event processing - errors should be processed earlier")}},er=function(){if(this.ended)throw new eV("stream has ended, this shouldn't happen");if(!eX(this,V,"f"))throw Error("Final run has not been received");return eX(this,V,"f")},ea=function(e){let[t,s]=eX(this,F,"m",ec).call(this,e,eX(this,z,"f"));for(let e of(eH(this,z,t,"f"),eX(this,K,"f")[t.id]=t,s)){let s=t.content[e.index];s?.type=="text"&&this._emit("textCreated",s.text)}switch(e.event){case"thread.message.created":this._emit("messageCreated",e.data);break;case"thread.message.in_progress":break;case"thread.message.delta":if(this._emit("messageDelta",e.data.delta,t),e.data.delta.content)for(let s of e.data.delta.content){if("text"==s.type&&s.text){let e=s.text,n=t.content[s.index];if(n&&"text"==n.type)this._emit("textDelta",e,n.text);else throw Error("The snapshot associated with this text delta is not text or missing")}if(s.index!=eX(this,G,"f")){if(eX(this,Y,"f"))switch(eX(this,Y,"f").type){case"text":this._emit("textDone",eX(this,Y,"f").text,eX(this,z,"f"));break;case"image_file":this._emit("imageFileDone",eX(this,Y,"f").image_file,eX(this,z,"f"))}eH(this,G,s.index,"f")}eH(this,Y,t.content[s.index],"f")}break;case"thread.message.completed":case"thread.message.incomplete":if(void 0!==eX(this,G,"f")){let t=e.data.content[eX(this,G,"f")];if(t)switch(t.type){case"image_file":this._emit("imageFileDone",t.image_file,eX(this,z,"f"));break;case"text":this._emit("textDone",t.text,eX(this,z,"f"))}}eX(this,z,"f")&&this._emit("messageDone",e.data),eH(this,z,void 0,"f")}},ei=function(e){let t=eX(this,F,"m",el).call(this,e);switch(eH(this,es,t,"f"),e.event){case"thread.run.step.created":this._emit("runStepCreated",e.data);break;case"thread.run.step.delta":let s=e.data.delta;if(s.step_details&&"tool_calls"==s.step_details.type&&s.step_details.tool_calls&&"tool_calls"==t.step_details.type)for(let e of s.step_details.tool_calls)e.index==eX(this,Q,"f")?this._emit("toolCallDelta",e,t.step_details.tool_calls[e.index]):(eX(this,Z,"f")&&this._emit("toolCallDone",eX(this,Z,"f")),eH(this,Q,e.index,"f"),eH(this,Z,t.step_details.tool_calls[e.index],"f"),eX(this,Z,"f")&&this._emit("toolCallCreated",eX(this,Z,"f")));this._emit("runStepDelta",e.data.delta,t);break;case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":eH(this,es,void 0,"f"),"tool_calls"==e.data.step_details.type&&eX(this,Z,"f")&&(this._emit("toolCallDone",eX(this,Z,"f")),eH(this,Z,void 0,"f")),this._emit("runStepDone",e.data,t)}},eo=function(e){eX(this,X,"f").push(e),this._emit("event",e)},el=function(e){switch(e.event){case"thread.run.step.created":return eX(this,J,"f")[e.data.id]=e.data,e.data;case"thread.run.step.delta":let t=eX(this,J,"f")[e.data.id];if(!t)throw Error("Received a RunStepDelta before creation of a snapshot");let s=e.data;if(s.delta){let n=H.accumulateDelta(t,s.delta);eX(this,J,"f")[e.data.id]=n}return eX(this,J,"f")[e.data.id];case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":case"thread.run.step.in_progress":eX(this,J,"f")[e.data.id]=e.data}if(eX(this,J,"f")[e.data.id])return eX(this,J,"f")[e.data.id];throw Error("No snapshot available")},ec=function(e,t){let s=[];switch(e.event){case"thread.message.created":return[e.data,s];case"thread.message.delta":if(!t)throw Error("Received a delta with no existing snapshot (there should be one from message creation)");let n=e.data;if(n.delta.content)for(let e of n.delta.content)if(e.index in t.content){let s=t.content[e.index];t.content[e.index]=eX(this,F,"m",eu).call(this,e,s)}else t.content[e.index]=e,s.push(e);return[t,s];case"thread.message.in_progress":case"thread.message.completed":case"thread.message.incomplete":if(t)return[t,s];throw Error("Received thread message event with no existing snapshot")}throw Error("Tried to accumulate a non-message event")},eu=function(e,t){return H.accumulateDelta(t,e)},eh=function(e){switch(eH(this,et,e.data,"f"),e.event){case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":break;case"thread.run.requires_action":case"thread.run.cancelled":case"thread.run.failed":case"thread.run.completed":case"thread.run.expired":case"thread.run.incomplete":eH(this,V,e.data,"f"),eX(this,Z,"f")&&(this._emit("toolCallDone",eX(this,Z,"f")),eH(this,Z,void 0,"f"))}};class sB extends t6{constructor(){super(...arguments),this.steps=new sM(this._client)}create(e,t,s){let{include:n,...r}=t;return this._client.post(se`/threads/${e}/runs`,{query:{include:n},body:r,...s,headers:sA([{"OpenAI-Beta":"assistants=v2"},s?.headers]),stream:t.stream??!1})}retrieve(e,t,s){let{thread_id:n}=t;return this._client.get(se`/threads/${n}/runs/${e}`,{...s,headers:sA([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}update(e,t,s){let{thread_id:n,...r}=t;return this._client.post(se`/threads/${n}/runs/${e}`,{body:r,...s,headers:sA([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t={},s){return this._client.getAPIList(se`/threads/${e}/runs`,tJ,{query:t,...s,headers:sA([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}cancel(e,t,s){let{thread_id:n}=t;return this._client.post(se`/threads/${n}/runs/${e}/cancel`,{...s,headers:sA([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async createAndPoll(e,t,s){let n=await this.create(e,t,s);return await this.poll(n.id,{thread_id:e},s)}createAndStream(e,t,s){return sD.createAssistantStream(e,this._client.beta.threads.runs,t,s)}async poll(e,t,s){let n=sA([s?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":s?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:r,response:a}=await this.retrieve(e,t,{...s,headers:{...s?.headers,...n}}).withResponse();switch(r.status){case"queued":case"in_progress":case"cancelling":let i=5e3;if(s?.pollIntervalMs)i=s.pollIntervalMs;else{let e=a.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(i=t)}}await ti(i);break;case"requires_action":case"incomplete":case"cancelled":case"completed":case"failed":case"expired":return r}}}stream(e,t,s){return sD.createAssistantStream(e,this._client.beta.threads.runs,t,s)}submitToolOutputs(e,t,s){let{thread_id:n,...r}=t;return this._client.post(se`/threads/${n}/runs/${e}/submit_tool_outputs`,{body:r,...s,headers:sA([{"OpenAI-Beta":"assistants=v2"},s?.headers]),stream:t.stream??!1})}async submitToolOutputsAndPoll(e,t,s){let n=await this.submitToolOutputs(e,t,s);return await this.poll(n.id,t,s)}submitToolOutputsStream(e,t,s){return sD.createToolAssistantStream(e,this._client.beta.threads.runs,t,s)}}sB.Steps=sM;class sU extends t6{constructor(){super(...arguments),this.runs=new sB(this._client),this.messages=new sj(this._client)}create(e={},t){return this._client.post("/threads",{body:e,...t,headers:sA([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(se`/threads/${e}`,{...t,headers:sA([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,s){return this._client.post(se`/threads/${e}`,{body:t,...s,headers:sA([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}delete(e,t){return this._client.delete(se`/threads/${e}`,{...t,headers:sA([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}createAndRun(e,t){return this._client.post("/threads/runs",{body:e,...t,headers:sA([{"OpenAI-Beta":"assistants=v2"},t?.headers]),stream:e.stream??!1})}async createAndRunPoll(e,t){let s=await this.createAndRun(e,t);return await this.runs.poll(s.id,{thread_id:s.thread_id},t)}createAndRunStream(e,t){return sD.createThreadAssistantStream(e,this._client.beta.threads,t)}}sU.Runs=sB,sU.Messages=sj;class sW extends t6{constructor(){super(...arguments),this.realtime=new sT(this._client),this.assistants=new sP(this._client),this.threads=new sU(this._client)}}sW.Realtime=sT,sW.Assistants=sP,sW.Threads=sU;class sq extends t6{create(e,t){return this._client.post("/completions",{body:e,...t,stream:e.stream??!1})}}class sF extends t6{retrieve(e,t,s){let{container_id:n}=t;return this._client.get(se`/containers/${n}/files/${e}/content`,{...s,headers:sA([{Accept:"application/binary"},s?.headers]),__binaryResponse:!0})}}class sH extends t6{constructor(){super(...arguments),this.content=new sF(this._client)}create(e,t,s){return this._client.post(se`/containers/${e}/files`,tQ({body:t,...s},this._client))}retrieve(e,t,s){let{container_id:n}=t;return this._client.get(se`/containers/${n}/files/${e}`,s)}list(e,t={},s){return this._client.getAPIList(se`/containers/${e}/files`,tJ,{query:t,...s})}delete(e,t,s){let{container_id:n}=t;return this._client.delete(se`/containers/${n}/files/${e}`,{...s,headers:sA([{Accept:"*/*"},s?.headers])})}}sH.Content=sF;class sX extends t6{constructor(){super(...arguments),this.files=new sH(this._client)}create(e,t){return this._client.post("/containers",{body:e,...t})}retrieve(e,t){return this._client.get(se`/containers/${e}`,t)}list(e={},t){return this._client.getAPIList("/containers",tJ,{query:e,...t})}delete(e,t){return this._client.delete(se`/containers/${e}`,{...t,headers:sA([{Accept:"*/*"},t?.headers])})}}sX.Files=sH;class sJ extends t6{create(e,t,s){let{include:n,...r}=t;return this._client.post(se`/conversations/${e}/items`,{query:{include:n},body:r,...s})}retrieve(e,t,s){let{conversation_id:n,...r}=t;return this._client.get(se`/conversations/${n}/items/${e}`,{query:r,...s})}list(e,t={},s){return this._client.getAPIList(se`/conversations/${e}/items`,tK,{query:t,...s})}delete(e,t,s){let{conversation_id:n}=t;return this._client.delete(se`/conversations/${n}/items/${e}`,s)}}class sK extends t6{constructor(){super(...arguments),this.items=new sJ(this._client)}create(e={},t){return this._client.post("/conversations",{body:e,...t})}retrieve(e,t){return this._client.get(se`/conversations/${e}`,t)}update(e,t,s){return this._client.post(se`/conversations/${e}`,{body:t,...s})}delete(e,t){return this._client.delete(se`/conversations/${e}`,t)}}sK.Items=sJ;class sz extends t6{create(e,t){let s=!!e.encoding_format,n=s?e.encoding_format:"base64";s&&tT(this._client).debug("embeddings/user defined encoding_format:",e.encoding_format);let r=this._client.post("/embeddings",{body:{...e,encoding_format:n},...t});return s?r:(tT(this._client).debug("embeddings/decoding base64 embeddings from base64"),r._thenUnwrap(e=>(e&&e.data&&e.data.forEach(e=>{let t=e.embedding;e.embedding=(e=>{if("undefined"!=typeof Buffer){let t=Buffer.from(e,"base64");return Array.from(new Float32Array(t.buffer,t.byteOffset,t.length/Float32Array.BYTES_PER_ELEMENT))}{let t=atob(e),s=t.length,n=new Uint8Array(s);for(let e=0;e<s;e++)n[e]=t.charCodeAt(e);return Array.from(new Float32Array(n.buffer))}})(t)}),e)))}}class sV extends t6{retrieve(e,t,s){let{eval_id:n,run_id:r}=t;return this._client.get(se`/evals/${n}/runs/${r}/output_items/${e}`,s)}list(e,t,s){let{eval_id:n,...r}=t;return this._client.getAPIList(se`/evals/${n}/runs/${e}/output_items`,tJ,{query:r,...s})}}class sG extends t6{constructor(){super(...arguments),this.outputItems=new sV(this._client)}create(e,t,s){return this._client.post(se`/evals/${e}/runs`,{body:t,...s})}retrieve(e,t,s){let{eval_id:n}=t;return this._client.get(se`/evals/${n}/runs/${e}`,s)}list(e,t={},s){return this._client.getAPIList(se`/evals/${e}/runs`,tJ,{query:t,...s})}delete(e,t,s){let{eval_id:n}=t;return this._client.delete(se`/evals/${n}/runs/${e}`,s)}cancel(e,t,s){let{eval_id:n}=t;return this._client.post(se`/evals/${n}/runs/${e}`,s)}}sG.OutputItems=sV;class sY extends t6{constructor(){super(...arguments),this.runs=new sG(this._client)}create(e,t){return this._client.post("/evals",{body:e,...t})}retrieve(e,t){return this._client.get(se`/evals/${e}`,t)}update(e,t,s){return this._client.post(se`/evals/${e}`,{body:t,...s})}list(e={},t){return this._client.getAPIList("/evals",tJ,{query:e,...t})}delete(e,t){return this._client.delete(se`/evals/${e}`,t)}}sY.Runs=sG;class sQ extends t6{create(e,t){return this._client.post("/files",tQ({body:e,...t},this._client))}retrieve(e,t){return this._client.get(se`/files/${e}`,t)}list(e={},t){return this._client.getAPIList("/files",tJ,{query:e,...t})}delete(e,t){return this._client.delete(se`/files/${e}`,t)}content(e,t){return this._client.get(se`/files/${e}/content`,{...t,headers:sA([{Accept:"application/binary"},t?.headers]),__binaryResponse:!0})}async waitForProcessing(e,{pollInterval:t=5e3,maxWait:s=18e5}={}){let n=new Set(["processed","error","deleted"]),r=Date.now(),a=await this.retrieve(e);for(;!a.status||!n.has(a.status);)if(await ti(t),a=await this.retrieve(e),Date.now()-r>s)throw new eZ({message:`Giving up on waiting for file ${e} to finish processing after ${s} milliseconds.`});return a}}class sZ extends t6{}class s0 extends t6{run(e,t){return this._client.post("/fine_tuning/alpha/graders/run",{body:e,...t})}validate(e,t){return this._client.post("/fine_tuning/alpha/graders/validate",{body:e,...t})}}class s1 extends t6{constructor(){super(...arguments),this.graders=new s0(this._client)}}s1.Graders=s0;class s2 extends t6{create(e,t,s){return this._client.getAPIList(se`/fine_tuning/checkpoints/${e}/permissions`,tX,{body:t,method:"post",...s})}retrieve(e,t={},s){return this._client.get(se`/fine_tuning/checkpoints/${e}/permissions`,{query:t,...s})}delete(e,t,s){let{fine_tuned_model_checkpoint:n}=t;return this._client.delete(se`/fine_tuning/checkpoints/${n}/permissions/${e}`,s)}}class s3 extends t6{constructor(){super(...arguments),this.permissions=new s2(this._client)}}s3.Permissions=s2;class s4 extends t6{list(e,t={},s){return this._client.getAPIList(se`/fine_tuning/jobs/${e}/checkpoints`,tJ,{query:t,...s})}}class s5 extends t6{constructor(){super(...arguments),this.checkpoints=new s4(this._client)}create(e,t){return this._client.post("/fine_tuning/jobs",{body:e,...t})}retrieve(e,t){return this._client.get(se`/fine_tuning/jobs/${e}`,t)}list(e={},t){return this._client.getAPIList("/fine_tuning/jobs",tJ,{query:e,...t})}cancel(e,t){return this._client.post(se`/fine_tuning/jobs/${e}/cancel`,t)}listEvents(e,t={},s){return this._client.getAPIList(se`/fine_tuning/jobs/${e}/events`,tJ,{query:t,...s})}pause(e,t){return this._client.post(se`/fine_tuning/jobs/${e}/pause`,t)}resume(e,t){return this._client.post(se`/fine_tuning/jobs/${e}/resume`,t)}}s5.Checkpoints=s4;class s8 extends t6{constructor(){super(...arguments),this.methods=new sZ(this._client),this.jobs=new s5(this._client),this.checkpoints=new s3(this._client),this.alpha=new s1(this._client)}}s8.Methods=sZ,s8.Jobs=s5,s8.Checkpoints=s3,s8.Alpha=s1;class s6 extends t6{}class s9 extends t6{constructor(){super(...arguments),this.graderModels=new s6(this._client)}}s9.GraderModels=s6;class s7 extends t6{createVariation(e,t){return this._client.post("/images/variations",tQ({body:e,...t},this._client))}edit(e,t){return this._client.post("/images/edits",tQ({body:e,...t,stream:e.stream??!1},this._client))}generate(e,t){return this._client.post("/images/generations",{body:e,...t,stream:e.stream??!1})}}class ne extends t6{retrieve(e,t){return this._client.get(se`/models/${e}`,t)}list(e){return this._client.getAPIList("/models",tX,e)}delete(e,t){return this._client.delete(se`/models/${e}`,t)}}class nt extends t6{create(e,t){return this._client.post("/moderations",{body:e,...t})}}class ns extends t6{create(e,t){return this._client.post("/realtime/client_secrets",{body:e,...t})}}class nn extends t6{constructor(){super(...arguments),this.clientSecrets=new ns(this._client)}}function nr(e,t){let s=e.output.map(e=>{if("function_call"===e.type)return{...e,parsed_arguments:function(e,t){var s,n;let r=(s=e.tools??[],n=t.name,s.find(e=>"function"===e.type&&e.name===n));return{...t,...t,parsed_arguments:r?.$brand==="auto-parseable-tool"?r.$parseRaw(t.arguments):r?.strict?JSON.parse(t.arguments):null}}(t,e)};if("message"===e.type){let s=e.content.map(e=>{var s,n;return"output_text"===e.type?{...e,parsed:(s=t,n=e.text,s.text?.format?.type!=="json_schema"?null:"$parseRaw"in s.text?.format?(s.text?.format).$parseRaw(n):JSON.parse(n))}:e});return{...e,content:s}}return e}),n=Object.assign({},e,{output:s});return Object.getOwnPropertyDescriptor(e,"output_text")||na(n),Object.defineProperty(n,"output_parsed",{enumerable:!0,get(){for(let e of n.output)if("message"===e.type){for(let t of e.content)if("output_text"===t.type&&null!==t.parsed)return t.parsed}return null}}),n}function na(e){let t=[];for(let s of e.output)if("message"===s.type)for(let e of s.content)"output_text"===e.type&&t.push(e.text);e.output_text=t.join("")}nn.ClientSecrets=ns;class ni extends su{constructor(e){super(),ed.add(this),ep.set(this,void 0),ef.set(this,void 0),em.set(this,void 0),eH(this,ep,e,"f")}static createResponse(e,t,s){let n=new ni(t);return n._run(()=>n._createOrRetrieveResponse(e,t,{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),n}async _createOrRetrieveResponse(e,t,s){let n,r=s?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),eX(this,ed,"m",eg).call(this);let a=null;for await(let r of("response_id"in t?(n=await e.responses.retrieve(t.response_id,{stream:!0},{...s,signal:this.controller.signal,stream:!0}),a=t.starting_after??null):n=await e.responses.create({...t,stream:!0},{...s,signal:this.controller.signal}),this._connected(),n))eX(this,ed,"m",ey).call(this,r,a);if(n.controller.signal?.aborted)throw new eY;return eX(this,ed,"m",ew).call(this)}[(ep=new WeakMap,ef=new WeakMap,em=new WeakMap,ed=new WeakSet,eg=function(){this.ended||eH(this,ef,void 0,"f")},ey=function(e,t){if(this.ended)return;let s=(e,s)=>{(null==t||s.sequence_number>t)&&this._emit(e,s)},n=eX(this,ed,"m",e_).call(this,e);switch(s("event",e),e.type){case"response.output_text.delta":{let t=n.output[e.output_index];if(!t)throw new eV(`missing output at index ${e.output_index}`);if("message"===t.type){let n=t.content[e.content_index];if(!n)throw new eV(`missing content at index ${e.content_index}`);if("output_text"!==n.type)throw new eV(`expected content to be 'output_text', got ${n.type}`);s("response.output_text.delta",{...e,snapshot:n.text})}break}case"response.function_call_arguments.delta":{let t=n.output[e.output_index];if(!t)throw new eV(`missing output at index ${e.output_index}`);"function_call"===t.type&&s("response.function_call_arguments.delta",{...e,snapshot:t.arguments});break}default:s(e.type,e)}},ew=function(){if(this.ended)throw new eV("stream has ended, this shouldn't happen");let e=eX(this,ef,"f");if(!e)throw new eV("request ended without sending any events");eH(this,ef,void 0,"f");let t=function(e,t){var s;return t&&(s=t,sn(s.text?.format))?nr(e,t):{...e,output_parsed:null,output:e.output.map(e=>"function_call"===e.type?{...e,parsed_arguments:null}:"message"===e.type?{...e,content:e.content.map(e=>({...e,parsed:null}))}:e)}}(e,eX(this,ep,"f"));return eH(this,em,t,"f"),t},e_=function(e){let t=eX(this,ef,"f");if(!t){if("response.created"!==e.type)throw new eV(`When snapshot hasn't been set yet, expected 'response.created' event, got ${e.type}`);return eH(this,ef,e.response,"f")}switch(e.type){case"response.output_item.added":t.output.push(e.item);break;case"response.content_part.added":{let s=t.output[e.output_index];if(!s)throw new eV(`missing output at index ${e.output_index}`);let n=s.type,r=e.part;"message"===n&&"reasoning_text"!==r.type?s.content.push(r):"reasoning"===n&&"reasoning_text"===r.type&&(s.content||(s.content=[]),s.content.push(r));break}case"response.output_text.delta":{let s=t.output[e.output_index];if(!s)throw new eV(`missing output at index ${e.output_index}`);if("message"===s.type){let t=s.content[e.content_index];if(!t)throw new eV(`missing content at index ${e.content_index}`);if("output_text"!==t.type)throw new eV(`expected content to be 'output_text', got ${t.type}`);t.text+=e.delta}break}case"response.function_call_arguments.delta":{let s=t.output[e.output_index];if(!s)throw new eV(`missing output at index ${e.output_index}`);"function_call"===s.type&&(s.arguments+=e.delta);break}case"response.reasoning_text.delta":{let s=t.output[e.output_index];if(!s)throw new eV(`missing output at index ${e.output_index}`);if("reasoning"===s.type){let t=s.content?.[e.content_index];if(!t)throw new eV(`missing content at index ${e.content_index}`);if("reasoning_text"!==t.type)throw new eV(`expected content to be 'reasoning_text', got ${t.type}`);t.text+=e.delta}break}case"response.completed":eH(this,ef,e.response,"f")}return t},Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("event",s=>{let n=t.shift();n?n.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),this.on("error",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}async finalResponse(){await this.done();let e=eX(this,em,"f");if(!e)throw new eV("stream ended without producing a ChatCompletion");return e}}class no extends t6{list(e,t={},s){return this._client.getAPIList(se`/responses/${e}/input_items`,tJ,{query:t,...s})}}class nl extends t6{constructor(){super(...arguments),this.inputItems=new no(this._client)}create(e,t){return this._client.post("/responses",{body:e,...t,stream:e.stream??!1})._thenUnwrap(e=>("object"in e&&"response"===e.object&&na(e),e))}retrieve(e,t={},s){return this._client.get(se`/responses/${e}`,{query:t,...s,stream:t?.stream??!1})._thenUnwrap(e=>("object"in e&&"response"===e.object&&na(e),e))}delete(e,t){return this._client.delete(se`/responses/${e}`,{...t,headers:sA([{Accept:"*/*"},t?.headers])})}parse(e,t){return this._client.responses.create(e,t)._thenUnwrap(t=>nr(t,e))}stream(e,t){return ni.createResponse(this._client,e,t)}cancel(e,t){return this._client.post(se`/responses/${e}/cancel`,t)}}nl.InputItems=no;class nc extends t6{create(e,t,s){return this._client.post(se`/uploads/${e}/parts`,tQ({body:t,...s},this._client))}}class nu extends t6{constructor(){super(...arguments),this.parts=new nc(this._client)}create(e,t){return this._client.post("/uploads",{body:e,...t})}cancel(e,t){return this._client.post(se`/uploads/${e}/cancel`,t)}complete(e,t,s){return this._client.post(se`/uploads/${e}/complete`,{body:t,...s})}}nu.Parts=nc;let nh=async e=>{let t=await Promise.allSettled(e),s=t.filter(e=>"rejected"===e.status);if(s.length){for(let e of s)console.error(e.reason);throw Error(`${s.length} promise(s) failed - see the above errors`)}let n=[];for(let e of t)"fulfilled"===e.status&&n.push(e.value);return n};class nd extends t6{create(e,t,s){return this._client.post(se`/vector_stores/${e}/file_batches`,{body:t,...s,headers:sA([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}retrieve(e,t,s){let{vector_store_id:n}=t;return this._client.get(se`/vector_stores/${n}/file_batches/${e}`,{...s,headers:sA([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}cancel(e,t,s){let{vector_store_id:n}=t;return this._client.post(se`/vector_stores/${n}/file_batches/${e}/cancel`,{...s,headers:sA([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async createAndPoll(e,t,s){let n=await this.create(e,t);return await this.poll(e,n.id,s)}listFiles(e,t,s){let{vector_store_id:n,...r}=t;return this._client.getAPIList(se`/vector_stores/${n}/file_batches/${e}/files`,tJ,{query:r,...s,headers:sA([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async poll(e,t,s){let n=sA([s?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":s?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:r,response:a}=await this.retrieve(t,{vector_store_id:e},{...s,headers:n}).withResponse();switch(r.status){case"in_progress":let i=5e3;if(s?.pollIntervalMs)i=s.pollIntervalMs;else{let e=a.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(i=t)}}await ti(i);break;case"failed":case"cancelled":case"completed":return r}}}async uploadAndPoll(e,{files:t,fileIds:s=[]},n){if(null==t||0==t.length)throw Error("No `files` provided to process. If you've already uploaded files you should use `.createAndPoll()` instead");let r=Math.min(n?.maxConcurrency??5,t.length),a=this._client,i=t.values(),o=[...s];async function l(e){for(let t of e){let e=await a.files.create({file:t,purpose:"assistants"},n);o.push(e.id)}}let c=Array(r).fill(i).map(l);return await nh(c),await this.createAndPoll(e,{file_ids:o})}}class np extends t6{create(e,t,s){return this._client.post(se`/vector_stores/${e}/files`,{body:t,...s,headers:sA([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}retrieve(e,t,s){let{vector_store_id:n}=t;return this._client.get(se`/vector_stores/${n}/files/${e}`,{...s,headers:sA([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}update(e,t,s){let{vector_store_id:n,...r}=t;return this._client.post(se`/vector_stores/${n}/files/${e}`,{body:r,...s,headers:sA([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t={},s){return this._client.getAPIList(se`/vector_stores/${e}/files`,tJ,{query:t,...s,headers:sA([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}delete(e,t,s){let{vector_store_id:n}=t;return this._client.delete(se`/vector_stores/${n}/files/${e}`,{...s,headers:sA([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async createAndPoll(e,t,s){let n=await this.create(e,t,s);return await this.poll(e,n.id,s)}async poll(e,t,s){let n=sA([s?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":s?.pollIntervalMs?.toString()??void 0}]);for(;;){let r=await this.retrieve(t,{vector_store_id:e},{...s,headers:n}).withResponse(),a=r.data;switch(a.status){case"in_progress":let i=5e3;if(s?.pollIntervalMs)i=s.pollIntervalMs;else{let e=r.response.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(i=t)}}await ti(i);break;case"failed":case"completed":return a}}}async upload(e,t,s){let n=await this._client.files.create({file:t,purpose:"assistants"},s);return this.create(e,{file_id:n.id},s)}async uploadAndPoll(e,t,s){let n=await this.upload(e,t,s);return await this.poll(e,n.id,s)}content(e,t,s){let{vector_store_id:n}=t;return this._client.getAPIList(se`/vector_stores/${n}/files/${e}/content`,tX,{...s,headers:sA([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}class nf extends t6{constructor(){super(...arguments),this.files=new np(this._client),this.fileBatches=new nd(this._client)}create(e,t){return this._client.post("/vector_stores",{body:e,...t,headers:sA([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(se`/vector_stores/${e}`,{...t,headers:sA([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,s){return this._client.post(se`/vector_stores/${e}`,{body:t,...s,headers:sA([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e={},t){return this._client.getAPIList("/vector_stores",tJ,{query:e,...t,headers:sA([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}delete(e,t){return this._client.delete(se`/vector_stores/${e}`,{...t,headers:sA([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}search(e,t,s){return this._client.getAPIList(se`/vector_stores/${e}/search`,tX,{body:t,method:"post",...s,headers:sA([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}nf.Files=np,nf.FileBatches=nd;class nm extends t6{constructor(){super(...arguments),eb.add(this)}async unwrap(e,t,s=this._client.webhookSecret,n=300){return await this.verifySignature(e,t,s,n),JSON.parse(e)}async verifySignature(e,t,s=this._client.webhookSecret,n=300){if("undefined"==typeof crypto||"function"!=typeof crypto.subtle.importKey||"function"!=typeof crypto.subtle.verify)throw Error("Webhook signature verification is only supported when the `crypto` global is defined");eX(this,eb,"m",ev).call(this,s);let r=sA([t]).values,a=eX(this,eb,"m",ex).call(this,r,"webhook-signature"),i=eX(this,eb,"m",ex).call(this,r,"webhook-timestamp"),o=eX(this,eb,"m",ex).call(this,r,"webhook-id"),l=parseInt(i,10);if(isNaN(l))throw new te("Invalid webhook timestamp format");let c=Math.floor(Date.now()/1e3);if(c-l>n)throw new te("Webhook timestamp is too old");if(l>c+n)throw new te("Webhook timestamp is too new");let u=a.split(" ").map(e=>e.startsWith("v1,")?e.substring(3):e),h=s.startsWith("whsec_")?Buffer.from(s.replace("whsec_",""),"base64"):Buffer.from(s,"utf-8"),d=o?`${o}.${i}.${e}`:`${i}.${e}`,p=await crypto.subtle.importKey("raw",h,{name:"HMAC",hash:"SHA-256"},!1,["verify"]);for(let e of u)try{let t=Buffer.from(e,"base64");if(await crypto.subtle.verify("HMAC",p,t,new TextEncoder().encode(d)))return}catch{continue}throw new te("The given webhook signature does not match the expected signature")}}eb=new WeakSet,ev=function(e){if("string"!=typeof e||0===e.length)throw Error("The webhook secret must either be set using the env var, OPENAI_WEBHOOK_SECRET, on the client class, OpenAI({ webhookSecret: '123' }), or passed to this function")},ex=function(e,t){if(!e)throw Error("Headers are required");let s=e.get(t);if(null==s)throw Error(`Missing required header: ${t}`);return s},e.i(69855);class ng{constructor({baseURL:e=sL("OPENAI_BASE_URL"),apiKey:t=sL("OPENAI_API_KEY"),organization:s=sL("OPENAI_ORG_ID")??null,project:n=sL("OPENAI_PROJECT_ID")??null,webhookSecret:r=sL("OPENAI_WEBHOOK_SECRET")??null,...a}={}){if(eS.add(this),eA.set(this,void 0),this.completions=new sq(this),this.chat=new sS(this),this.embeddings=new sz(this),this.files=new sQ(this),this.images=new s7(this),this.audio=new sI(this),this.moderations=new nt(this),this.models=new ne(this),this.fineTuning=new s8(this),this.graders=new s9(this),this.vectorStores=new nf(this),this.webhooks=new nm(this),this.beta=new sW(this),this.batches=new sE(this),this.uploads=new nu(this),this.responses=new nl(this),this.realtime=new nn(this),this.conversations=new sK(this),this.evals=new sY(this),this.containers=new sX(this),void 0===t)throw new eV("Missing credentials. Please pass an `apiKey`, or set the `OPENAI_API_KEY` environment variable.");let i={apiKey:t,organization:s,project:n,webhookSecret:r,...a,baseURL:e||"https://api.openai.com/v1"};i.dangerouslyAllowBrowser,this.baseURL=i.baseURL,this.timeout=i.timeout??e$.DEFAULT_TIMEOUT,this.logger=i.logger??console;let o="warn";this.logLevel=o,this.logLevel=tI(i.logLevel,"ClientOptions.logLevel",this)??tI(sL("OPENAI_LOG"),"process.env['OPENAI_LOG']",this)??o,this.fetchOptions=i.fetchOptions,this.maxRetries=i.maxRetries??2,this.fetch=i.fetch??function(){if("undefined"!=typeof fetch)return fetch;throw Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new OpenAI({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}(),eH(this,eA,tf,"f"),this._options=i,this.apiKey="string"==typeof t?t:"Missing Key",this.organization=s,this.project=n,this.webhookSecret=r}withOptions(e){return new this.constructor({...this._options,baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetch:this.fetch,fetchOptions:this.fetchOptions,apiKey:this.apiKey,organization:this.organization,project:this.project,webhookSecret:this.webhookSecret,...e})}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:e,nulls:t}){}async authHeaders(e){return sA([{Authorization:`Bearer ${this.apiKey}`}])}stringifyQuery(e){return function(e,t={}){let s,n=e,r=function(e=tS){let t;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw TypeError("Encoder has to be a function.");let s=e.charset||tS.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let n=tm;if(void 0!==e.format){if(!tw(ty,e.format))throw TypeError("Unknown format option provided.");n=e.format}let r=ty[n],a=tS.filter;if(("function"==typeof e.filter||ts(e.filter))&&(a=e.filter),t=e.arrayFormat&&e.arrayFormat in tv?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":tS.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw TypeError("`commaRoundTrip` must be a boolean, or absent");let i=void 0===e.allowDots?!0==!!e.encodeDotInKeys||tS.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:tS.addQueryPrefix,allowDots:i,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:tS.allowEmptyArrays,arrayFormat:t,charset:s,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:tS.charsetSentinel,commaRoundTrip:!!e.commaRoundTrip,delimiter:void 0===e.delimiter?tS.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:tS.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:tS.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:tS.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:tS.encodeValuesOnly,filter:a,format:n,formatter:r,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:tS.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:tS.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:tS.strictNullHandling}}(t);"function"==typeof r.filter?n=(0,r.filter)("",n):ts(r.filter)&&(s=r.filter);let a=[];if("object"!=typeof n||null===n)return"";let i=tv[r.arrayFormat],o="comma"===i&&r.commaRoundTrip;s||(s=Object.keys(n)),r.sort&&s.sort(r.sort);let l=new WeakMap;for(let e=0;e<s.length;++e){let t=s[e];r.skipNulls&&null===n[t]||tx(a,function e(t,s,n,r,a,i,o,l,c,u,h,d,p,f,m,g,y,w){var _,b;let v,x=t,S=w,$=0,A=!1;for(;void 0!==(S=S.get(t$))&&!A;){let e=S.get(t);if($+=1,void 0!==e)if(e===$)throw RangeError("Cyclic object value");else A=!0;void 0===S.get(t$)&&($=0)}if("function"==typeof u?x=u(s,x):x instanceof Date?x=p?.(x):"comma"===n&&ts(x)&&(x=tb(x,function(e){return e instanceof Date?p?.(e):e})),null===x){if(i)return c&&!g?c(s,tS.encoder,y,"key",f):s;x=""}if("string"==typeof(_=x)||"number"==typeof _||"boolean"==typeof _||"symbol"==typeof _||"bigint"==typeof _||(b=x)&&"object"==typeof b&&b.constructor&&b.constructor.isBuffer&&b.constructor.isBuffer(b)){if(c){let e=g?s:c(s,tS.encoder,y,"key",f);return[m?.(e)+"="+m?.(c(x,tS.encoder,y,"value",f))]}return[m?.(s)+"="+m?.(String(x))]}let R=[];if(void 0===x)return R;if("comma"===n&&ts(x))g&&c&&(x=tb(x,c)),v=[{value:x.length>0?x.join(",")||null:void 0}];else if(ts(u))v=u;else{let e=Object.keys(x);v=h?e.sort(h):e}let k=l?String(s).replace(/\./g,"%2E"):String(s),O=r&&ts(x)&&1===x.length?k+"[]":k;if(a&&ts(x)&&0===x.length)return O+"[]";for(let s=0;s<v.length;++s){let _=v[s],b="object"==typeof _&&void 0!==_.value?_.value:x[_];if(o&&null===b)continue;let S=d&&l?_.replace(/\./g,"%2E"):_,A=ts(x)?"function"==typeof n?n(O,S):O:O+(d?"."+S:"["+S+"]");w.set(t,$);let k=new WeakMap;k.set(t$,w),tx(R,e(b,A,n,r,a,i,o,l,"comma"===n&&g&&ts(x)?null:c,u,h,d,p,f,m,g,y,k))}return R}(n[t],t,i,o,r.allowEmptyArrays,r.strictNullHandling,r.skipNulls,r.encodeDotInKeys,r.encode?r.encoder:null,r.filter,r.sort,r.allowDots,r.serializeDate,r.format,r.formatter,r.encodeValuesOnly,r.charset,l))}let c=a.join(r.delimiter),u=!0===r.addQueryPrefix?"?":"";return r.charsetSentinel&&("iso-8859-1"===r.charset?u+="utf8=%26%2310003%3B&":u+="utf8=%E2%9C%93&"),c.length>0?u+c:""}(e,{arrayFormat:"brackets"})}getUserAgent(){return`${this.constructor.name}/JS ${to}`}defaultIdempotencyKey(){return`stainless-node-retry-${eJ()}`}makeStatusError(e,t,s,n){return eG.generate(e,t,s,n)}async _callApiKey(){let e,t=this._options.apiKey;if("function"!=typeof t)return!1;try{e=await t()}catch(e){if(e instanceof eV)throw e;throw new eV(`Failed to get token from 'apiKey' function: ${e.message}`,{cause:e})}if("string"!=typeof e||!e)throw new eV(`Expected 'apiKey' function argument to return a string but it returned ${e}`);return this.apiKey=e,!0}buildURL(e,t,s){let n=!eX(this,eS,"m",eR).call(this)&&s||this.baseURL,r=new URL(tt.test(e)?e:n+(n.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),a=this.defaultQuery();return!function(e){if(!e)return!0;for(let t in e)return!1;return!0}(a)&&(t={...a,...t}),"object"==typeof t&&t&&!Array.isArray(t)&&(r.search=this.stringifyQuery(t)),r.toString()}async prepareOptions(e){await this._callApiKey()}async prepareRequest(e,{url:t,options:s}){}get(e,t){return this.methodRequest("get",e,t)}post(e,t){return this.methodRequest("post",e,t)}patch(e,t){return this.methodRequest("patch",e,t)}put(e,t){return this.methodRequest("put",e,t)}delete(e,t){return this.methodRequest("delete",e,t)}methodRequest(e,t,s){return this.request(Promise.resolve(s).then(s=>({method:e,path:t,...s})))}request(e,t=null){return new tq(this,this.makeRequest(e,t,void 0))}async makeRequest(e,t,s){let n=await e,r=n.maxRetries??this.maxRetries;null==t&&(t=r),await this.prepareOptions(n);let{req:a,url:i,timeout:o}=await this.buildRequest(n,{retryCount:r-t});await this.prepareRequest(a,{url:i,options:n});let l="log_"+(0x1000000*Math.random()|0).toString(16).padStart(6,"0"),c=void 0===s?"":`, retryOf: ${s}`,u=Date.now();if(tT(this).debug(`[${l}] sending request`,tj({retryOfRequestLogID:s,method:n.method,url:i,options:n,headers:a.headers})),n.signal?.aborted)throw new eY;let h=new AbortController,d=await this.fetchWithTimeout(i,a,o,h).catch(ez),p=Date.now();if(d instanceof globalThis.Error){let e=`retrying, ${t} attempts remaining`;if(n.signal?.aborted)throw new eY;let r=eK(d)||/timed? ?out/i.test(String(d)+("cause"in d?String(d.cause):""));if(t)return tT(this).info(`[${l}] connection ${r?"timed out":"failed"} - ${e}`),tT(this).debug(`[${l}] connection ${r?"timed out":"failed"} (${e})`,tj({retryOfRequestLogID:s,url:i,durationMs:p-u,message:d.message})),this.retryRequest(n,t,s??l);if(tT(this).info(`[${l}] connection ${r?"timed out":"failed"} - error; no more retries left`),tT(this).debug(`[${l}] connection ${r?"timed out":"failed"} (error; no more retries left)`,tj({retryOfRequestLogID:s,url:i,durationMs:p-u,message:d.message})),r)throw new eZ;throw new eQ({cause:d})}let f=[...d.headers.entries()].filter(([e])=>"x-request-id"===e).map(([e,t])=>", "+e+": "+JSON.stringify(t)).join(""),m=`[${l}${c}${f}] ${a.method} ${i} ${d.ok?"succeeded":"failed"} with status ${d.status} in ${p-u}ms`;if(!d.ok){let e=await this.shouldRetry(d);if(t&&e){let e=`retrying, ${t} attempts remaining`;return await tp(d.body),tT(this).info(`${m} - ${e}`),tT(this).debug(`[${l}] response error (${e})`,tj({retryOfRequestLogID:s,url:d.url,status:d.status,headers:d.headers,durationMs:p-u})),this.retryRequest(n,t,s??l,d.headers)}let r=e?"error; no more retries left":"error; not retryable";tT(this).info(`${m} - ${r}`);let a=await d.text().catch(e=>ez(e).message),i=(e=>{try{return JSON.parse(e)}catch(e){return}})(a),o=i?void 0:a;throw tT(this).debug(`[${l}] response error (${r})`,tj({retryOfRequestLogID:s,url:d.url,status:d.status,headers:d.headers,message:o,durationMs:Date.now()-u})),this.makeStatusError(d.status,i,o,d.headers)}return tT(this).info(m),tT(this).debug(`[${l}] response start`,tj({retryOfRequestLogID:s,url:d.url,status:d.status,headers:d.headers,durationMs:p-u})),{response:d,options:n,controller:h,requestLogID:l,retryOfRequestLogID:s,startTime:u}}getAPIList(e,t,s){return this.requestAPIList(t,{method:"get",path:e,...s})}requestAPIList(e,t){return new tH(this,this.makeRequest(t,null,void 0),e)}async fetchWithTimeout(e,t,s,n){let{signal:r,method:a,...i}=t||{};r&&r.addEventListener("abort",()=>n.abort());let o=setTimeout(()=>n.abort(),s),l=globalThis.ReadableStream&&i.body instanceof globalThis.ReadableStream||"object"==typeof i.body&&null!==i.body&&Symbol.asyncIterator in i.body,c={signal:n.signal,...l?{duplex:"half"}:{},method:"GET",...i};a&&(c.method=a.toUpperCase());try{return await this.fetch.call(void 0,e,c)}finally{clearTimeout(o)}}async shouldRetry(e){let t=e.headers.get("x-should-retry");return"true"===t||"false"!==t&&(408===e.status||409===e.status||429===e.status||!!(e.status>=500))}async retryRequest(e,t,s,n){let r,a=n?.get("retry-after-ms");if(a){let e=parseFloat(a);Number.isNaN(e)||(r=e)}let i=n?.get("retry-after");if(i&&!r){let e=parseFloat(i);r=Number.isNaN(e)?Date.parse(i)-Date.now():1e3*e}if(!(r&&0<=r&&r<6e4)){let s=e.maxRetries??this.maxRetries;r=this.calculateDefaultRetryTimeoutMillis(t,s)}return await ti(r),this.makeRequest(e,t-1,s)}calculateDefaultRetryTimeoutMillis(e,t){return Math.min(.5*Math.pow(2,t-e),8)*(1-.25*Math.random())*1e3}async buildRequest(e,{retryCount:t=0}={}){let s={...e},{method:n,path:r,query:a,defaultBaseURL:i}=s,o=this.buildURL(r,a,i);"timeout"in s&&((e,t)=>{if("number"!=typeof t||!Number.isInteger(t))throw new eV(`${e} must be an integer`);if(t<0)throw new eV(`${e} must be a positive integer`)})("timeout",s.timeout),s.timeout=s.timeout??this.timeout;let{bodyHeaders:l,body:c}=this.buildBody({options:s}),u=await this.buildHeaders({options:e,method:n,bodyHeaders:l,retryCount:t});return{req:{method:n,headers:u,...s.signal&&{signal:s.signal},...globalThis.ReadableStream&&c instanceof globalThis.ReadableStream&&{duplex:"half"},...c&&{body:c},...this.fetchOptions??{},...s.fetchOptions??{}},url:o,timeout:s.timeout}}async buildHeaders({options:e,method:s,bodyHeaders:n,retryCount:r}){let a={};this.idempotencyHeader&&"get"!==s&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),a[this.idempotencyHeader]=e.idempotencyKey);let i=sA([a,{Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(r),...e.timeout?{"X-Stainless-Timeout":String(Math.trunc(e.timeout/1e3))}:{},...t??(t=(()=>{let e="undefined"!=typeof Deno&&null!=Deno.build?"deno":"undefined"!=typeof EdgeRuntime?"edge":"[object process]"===Object.prototype.toString.call(void 0!==globalThis.process?globalThis.process:0)?"node":"unknown";if("deno"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":to,"X-Stainless-OS":tc(Deno.build.os),"X-Stainless-Arch":tl(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":"string"==typeof Deno.version?Deno.version:Deno.version?.deno??"unknown"};if("undefined"!=typeof EdgeRuntime)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":to,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if("node"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":to,"X-Stainless-OS":tc(globalThis.process.platform??"unknown"),"X-Stainless-Arch":tl(globalThis.process.arch??"unknown"),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":globalThis.process.version??"unknown"};let t=function(){if("undefined"==typeof navigator||!navigator)return null;for(let{key:e,pattern:t}of[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}]){let s=t.exec(navigator.userAgent);if(s){let t=s[1]||0,n=s[2]||0,r=s[3]||0;return{browser:e,version:`${t}.${n}.${r}`}}}return null}();return t?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":to,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${t.browser}`,"X-Stainless-Runtime-Version":t.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":to,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}})()),"OpenAI-Organization":this.organization,"OpenAI-Project":this.project},await this.authHeaders(e),this._options.defaultHeaders,n,e.headers]);return this.validateHeaders(i),i.values}buildBody({options:{body:e,headers:t}}){if(!e)return{bodyHeaders:void 0,body:void 0};let s=sA([t]);return ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof DataView||"string"==typeof e&&s.values.has("content-type")||globalThis.Blob&&e instanceof globalThis.Blob||e instanceof FormData||e instanceof URLSearchParams||globalThis.ReadableStream&&e instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:e}:"object"==typeof e&&(Symbol.asyncIterator in e||Symbol.iterator in e&&"next"in e&&"function"==typeof e.next)?{bodyHeaders:void 0,body:th(e)}:eX(this,eA,"f").call(this,{body:e,headers:s})}}e$=ng,eA=new WeakMap,eS=new WeakSet,eR=function(){return"https://api.openai.com/v1"!==this.baseURL},ng.OpenAI=e$,ng.DEFAULT_TIMEOUT=6e5,ng.OpenAIError=eV,ng.APIError=eG,ng.APIConnectionError=eQ,ng.APIConnectionTimeoutError=eZ,ng.APIUserAbortError=eY,ng.NotFoundError=e3,ng.ConflictError=e4,ng.RateLimitError=e8,ng.BadRequestError=e0,ng.AuthenticationError=e1,ng.InternalServerError=e6,ng.PermissionDeniedError=e2,ng.UnprocessableEntityError=e5,ng.InvalidWebhookSignatureError=te,ng.toFile=t5,ng.Completions=sq,ng.Chat=sS,ng.Embeddings=sz,ng.Files=sQ,ng.Images=s7,ng.Audio=sI,ng.Moderations=nt,ng.Models=ne,ng.FineTuning=s8,ng.Graders=s9,ng.VectorStores=nf,ng.Webhooks=nm,ng.Beta=sW,ng.Batches=sE,ng.Uploads=nu,ng.Responses=nl,ng.Realtime=nn,ng.Conversations=sK,ng.Evals=sY,ng.Containers=sX;let ny=0,nw=new ng({apiKey:process.env.OPENAI_API_KEY}),n_=()=>"true"===process.env.OPENAI_ENABLED&&!!process.env.OPENAI_API_KEY,nb=async()=>{let e=Date.now()-ny;if(e<1e3){let t=1e3-e;await new Promise(e=>setTimeout(e,t))}ny=Date.now()},nv=async()=>{try{await nb();let e=(await nw.models.list()).data.filter(e=>e.id.startsWith("gpt-")).sort((e,t)=>t.created-e.created);for(let t of["gpt-4o","gpt-4o-mini","gpt-4-turbo","gpt-4","gpt-3.5-turbo"]){let s=e.find(e=>e.id===t);if(s)return console.log(`Using OpenAI model: ${s.id}`),s.id}if(e.length>0)return console.log(`Using fallback OpenAI model: ${e[0].id}`),e[0].id;throw Error("No GPT models available")}catch(e){return console.error("Error getting OpenAI models:",e),"gpt-4o"}},nx=async(e,t)=>{if(!n_())return"AI analysis disabled. Enable in configuration to get intelligent market insights.";try{await nb();let s=await nv(),n=e.slice(0,5),r=`As a professional swing trading analyst, provide a concise market commentary based on the following scan results and market conditions:

Market Conditions:
- Time: ${t.timeOfDay}
- Market Hours: ${t.marketHours?"Open":"Closed"}
- Optimal Scan Time: ${t.isOptimalScanTime?"Yes":"No"}

Top Trading Opportunities:
${n.map((e,t)=>`
${t+1}. ${e.symbol} (Score: ${e.overallScore.toFixed(1)}/100)
   - Best Strategy: ${e.bestStrategy||"None"}
   - Price Action: Recent momentum and volume patterns
   - Key Levels: Support/resistance analysis
`).join("")}

Provide a 2-3 paragraph market commentary focusing on:
1. Overall market sentiment and trading conditions
2. Key themes and sectors showing strength/weakness
3. Risk considerations and trading recommendations

Keep it professional, actionable, and under 200 words.`,a=await nw.chat.completions.create({model:s,messages:[{role:"system",content:"You are a professional swing trading analyst with expertise in technical analysis and market psychology. Provide clear, actionable insights."},{role:"user",content:r}],max_tokens:300,temperature:.7});return a.choices[0]?.message?.content||"Unable to generate market commentary at this time."}catch(e){return console.error("Error generating market commentary:",e),"Market commentary temporarily unavailable. Technical analysis remains fully functional."}},nS=async(e,t)=>{if(!n_())return{riskScore:5,riskFactors:["AI analysis disabled"],recommendations:["Enable AI features for enhanced risk assessment"],sentiment:"neutral"};try{await nb();let t=await nv(),s=`Analyze this swing trading setup for risk assessment:

Symbol: ${e.symbol}
Strategy: ${e.strategy}
Confidence: ${e.confidence}%
Entry: $${e.entryPrice}
Stop Loss: $${e.stopLoss}
Targets: ${e.targets.map(e=>`$${e}`).join(", ")}
Position Size: ${e.positionSize} shares
Risk Amount: $${e.riskAmount}

Provide a JSON response with:
{
  "riskScore": 1-10 (1=low risk, 10=high risk),
  "riskFactors": ["factor1", "factor2", ...],
  "recommendations": ["rec1", "rec2", ...],
  "sentiment": "bullish|bearish|neutral"
}

Consider: market conditions, position sizing, risk/reward ratio, strategy type, and current market volatility.`,n=await nw.chat.completions.create({model:t,messages:[{role:"system",content:"You are a risk management expert specializing in swing trading. Provide objective risk assessments in valid JSON format."},{role:"user",content:s}],max_tokens:400,temperature:.3}),r=n.choices[0]?.message?.content;if(r)try{return JSON.parse(r)}catch(e){console.error("Error parsing AI risk assessment:",e)}return{riskScore:5,riskFactors:["Unable to complete AI risk analysis"],recommendations:["Review setup manually","Consider current market conditions"],sentiment:"neutral"}}catch(e){return console.error("Error generating risk assessment:",e),{riskScore:5,riskFactors:["AI risk assessment temporarily unavailable"],recommendations:["Proceed with standard risk management"],sentiment:"neutral"}}},n$=async(e,t)=>{if(!n_())return{topPicks:["AI recommendations disabled"],avoidList:[],marketOutlook:"Enable AI features for personalized recommendations",actionItems:["Configure OpenAI integration"]};try{await nb();let s=await nv(),n=e.slice(0,10),r=`As a professional trading advisor, analyze these swing trading opportunities and provide personalized recommendations:

User Profile:
- Risk Tolerance: ${t?.riskTolerance||"medium"}
- Trading Style: ${t?.tradingStyle||"moderate"}
- Account Size: $${t?.accountSize?.toLocaleString()||"100,000"}

Available Opportunities:
${n.map((e,t)=>`
${t+1}. ${e.symbol} (Score: ${e.overallScore.toFixed(1)}/100)
   - Strategy: ${e.bestStrategy||"None"}
   - Confidence: High/Medium/Low based on score
`).join("")}

Provide a JSON response with:
{
  "topPicks": ["symbol1", "symbol2", "symbol3"],
  "avoidList": ["symbol1", "symbol2"],
  "marketOutlook": "brief market outlook",
  "actionItems": ["action1", "action2", "action3"]
}

Focus on risk-appropriate recommendations for the user's profile.`,a=await nw.chat.completions.create({model:s,messages:[{role:"system",content:"You are a professional trading advisor. Provide personalized, risk-appropriate recommendations in valid JSON format."},{role:"user",content:r}],max_tokens:500,temperature:.4}),i=a.choices[0]?.message?.content;if(i)try{return JSON.parse(i)}catch(e){console.error("Error parsing AI recommendations:",e)}return{topPicks:n.slice(0,3).map(e=>e.symbol),avoidList:[],marketOutlook:"Mixed market conditions - proceed with caution",actionItems:["Review top-scoring setups","Monitor market conditions","Manage position sizes"]}}catch(e){return console.error("Error generating trading recommendations:",e),{topPicks:[],avoidList:[],marketOutlook:"AI recommendations temporarily unavailable",actionItems:["Use technical analysis for decision making"]}}};async function nA(e){try{let{searchParams:t}=new URL(e.url);switch(t.get("action")){case"status":return eF.NextResponse.json({enabled:n_(),model:n_()?await nv():null,features:{marketCommentary:!0,riskAssessment:!0,tradingRecommendations:!0}});case"model":if(!n_())return eF.NextResponse.json({error:"OpenAI not enabled"},{status:400});let s=await nv();return eF.NextResponse.json({model:s});default:return eF.NextResponse.json({error:"Invalid action"},{status:400})}}catch(e){return console.error("AI API error:",e),eF.NextResponse.json({error:"Internal server error"},{status:500})}}async function nR(e){try{if(!n_())return eF.NextResponse.json({error:"OpenAI not enabled"},{status:400});let{action:t,data:s}=await e.json();switch(t){case"market-commentary":let{scanResults:n,marketConditions:r}=s,a=await nx(n,r);return eF.NextResponse.json({commentary:a});case"risk-assessment":let{setup:i}=s,o=await nS(i);return eF.NextResponse.json({riskAssessment:o});case"trading-recommendations":let{scanResults:l,userPreferences:c}=s,u=await n$(l,c);return eF.NextResponse.json({recommendations:u});default:return eF.NextResponse.json({error:"Invalid action"},{status:400})}}catch(e){return console.error("AI API POST error:",e),eF.NextResponse.json({error:"Internal server error"},{status:500})}}var nk=e.i(89095);let nO=new ek.AppRouteRouteModule({definition:{kind:eO.RouteKind.APP_ROUTE,page:"/api/ai/route",pathname:"/api/ai",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/swing-trader-ai/src/app/api/ai/route.ts",nextConfigOutput:"",userland:nk}),{workAsyncStorage:nI,workUnitAsyncStorage:nE,serverHooks:nP}=nO;function nC(){return(0,eI.patchFetch)({workAsyncStorage:nI,workUnitAsyncStorage:nE})}async function nN(e,t,s){var n;let r="/api/ai/route";r=r.replace(/\/index$/,"")||"/";let a=await nO.prepare(e,t,{srcPage:r,multiZoneDraftMode:!1});if(!a)return t.statusCode=400,t.end("Bad Request"),null==s.waitUntil||s.waitUntil.call(s,Promise.resolve()),null;let{buildId:i,params:o,nextConfig:l,isDraftMode:c,prerenderManifest:u,routerServerContext:h,isOnDemandRevalidate:d,revalidateOnlyGenerated:p,resolvedPathname:f}=a,m=(0,eC.normalizeAppPath)(r),g=!!(u.dynamicRoutes[m]||u.routes[f]);if(g&&!c){let e=!!u.routes[f],t=u.dynamicRoutes[m];if(t&&!1===t.fallback&&!e)throw new eW.NoFallbackError}let y=null;!g||nO.isDev||c||(y="/index"===(y=f)?"/":y);let w=!0===nO.isDev||!g,_=g&&!w,b=e.method||"GET",v=(0,eP.getTracer)(),x=v.getActiveScopeSpan(),S={params:o,prerenderManifest:u,renderOpts:{experimental:{cacheComponents:!!l.experimental.cacheComponents,authInterrupts:!!l.experimental.authInterrupts},supportsDynamicResponse:w,incrementalCache:(0,eE.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(n=l.experimental)?void 0:n.cacheLife,isRevalidate:_,waitUntil:s.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,s,n)=>nO.onRequestError(e,t,n,h)},sharedContext:{buildId:i}},$=new eN.NodeNextRequest(e),A=new eN.NodeNextResponse(t),R=eT.NextRequestAdapter.fromNodeNextRequest($,(0,eT.signalFromNodeResponse)(t));try{let n=async s=>nO.handle(R,S).finally(()=>{if(!s)return;s.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let n=v.getRootSpanAttributes();if(!n)return;if(n.get("next.span_type")!==ej.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${n.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let r=n.get("next.route");if(r){let e=`${b} ${r}`;s.setAttributes({"next.route":r,"http.route":r,"next.span_name":e}),s.updateName(e)}else s.updateName(`${b} ${e.url}`)}),a=async a=>{var i,o;let f=async({previousCacheEntry:i})=>{try{if(!(0,eE.getRequestMeta)(e,"minimalMode")&&d&&p&&!i)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let r=await n(a);e.fetchMetrics=S.renderOpts.fetchMetrics;let o=S.renderOpts.pendingWaitUntil;o&&s.waitUntil&&(s.waitUntil(o),o=void 0);let l=S.renderOpts.collectedTags;if(!g)return await (0,eL.sendResponse)($,A,r,S.renderOpts.pendingWaitUntil),null;{let e=await r.blob(),t=(0,eD.toNodeOutgoingHttpHeaders)(r.headers);l&&(t[eU.NEXT_CACHE_TAGS_HEADER]=l),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let s=void 0!==S.renderOpts.collectedRevalidate&&!(S.renderOpts.collectedRevalidate>=eU.INFINITE_CACHE)&&S.renderOpts.collectedRevalidate,n=void 0===S.renderOpts.collectedExpire||S.renderOpts.collectedExpire>=eU.INFINITE_CACHE?void 0:S.renderOpts.collectedExpire;return{value:{kind:eq.CachedRouteKind.APP_ROUTE,status:r.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:s,expire:n}}}}catch(t){throw(null==i?void 0:i.isStale)&&await nO.onRequestError(e,t,{routerKind:"App Router",routePath:r,routeType:"route",revalidateReason:(0,eM.getRevalidateReason)({isRevalidate:_,isOnDemandRevalidate:d})},h),t}},m=await nO.handleResponse({req:e,nextConfig:l,cacheKey:y,routeKind:eO.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:u,isRoutePPREnabled:!1,isOnDemandRevalidate:d,revalidateOnlyGenerated:p,responseGenerator:f,waitUntil:s.waitUntil});if(!g)return null;if((null==m||null==(i=m.value)?void 0:i.kind)!==eq.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==m||null==(o=m.value)?void 0:o.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,eE.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",d?"REVALIDATED":m.isMiss?"MISS":m.isStale?"STALE":"HIT"),c&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let w=(0,eD.fromNodeOutgoingHttpHeaders)(m.value.headers);return(0,eE.getRequestMeta)(e,"minimalMode")&&g||w.delete(eU.NEXT_CACHE_TAGS_HEADER),!m.cacheControl||t.getHeader("Cache-Control")||w.get("Cache-Control")||w.set("Cache-Control",(0,eB.getCacheControlHeader)(m.cacheControl)),await (0,eL.sendResponse)($,A,new Response(m.value.body,{headers:w,status:m.value.status||200})),null};x?await a(x):await v.withPropagatedContext(e.headers,()=>v.trace(ej.BaseServerSpan.handleRequest,{spanName:`${b} ${e.url}`,kind:eP.SpanKind.SERVER,attributes:{"http.method":b,"http.target":e.url}},a))}catch(t){if(t instanceof eW.NoFallbackError||await nO.onRequestError(e,t,{routerKind:"App Router",routePath:m,routeType:"route",revalidateReason:(0,eM.getRevalidateReason)({isRevalidate:_,isOnDemandRevalidate:d})}),g)throw t;return await (0,eL.sendResponse)($,A,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=6bf44_next_dist_esm_build_templates_app-route_b5542e9d.js.map