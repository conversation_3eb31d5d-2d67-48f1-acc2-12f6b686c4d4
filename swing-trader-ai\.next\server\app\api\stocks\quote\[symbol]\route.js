/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/stocks/quote/[symbol]/route";
exports.ids = ["app/api/stocks/quote/[symbol]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstocks%2Fquote%2F%5Bsymbol%5D%2Froute&page=%2Fapi%2Fstocks%2Fquote%2F%5Bsymbol%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstocks%2Fquote%2F%5Bsymbol%5D%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstocks%2Fquote%2F%5Bsymbol%5D%2Froute&page=%2Fapi%2Fstocks%2Fquote%2F%5Bsymbol%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstocks%2Fquote%2F%5Bsymbol%5D%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var C_Users_SJFit_Desktop_shittyidea_swing_trader_ai_src_app_api_stocks_quote_symbol_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/stocks/quote/[symbol]/route.ts */ \"(rsc)/./src/app/api/stocks/quote/[symbol]/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/stocks/quote/[symbol]/route\",\n        pathname: \"/api/stocks/quote/[symbol]\",\n        filename: \"route\",\n        bundlePath: \"app/api/stocks/quote/[symbol]/route\"\n    },\n    distDir: \".next\" || 0,\n    relativeProjectDir:  false || '',\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\shittyidea\\\\swing-trader-ai\\\\src\\\\app\\\\api\\\\stocks\\\\quote\\\\[symbol]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_SJFit_Desktop_shittyidea_swing_trader_ai_src_app_api_stocks_quote_symbol_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/stocks/quote/[symbol]/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = false;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        if (!(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstocks%2Fquote%2F%5Bsymbol%5D%2Froute&page=%2Fapi%2Fstocks%2Fquote%2F%5Bsymbol%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstocks%2Fquote%2F%5Bsymbol%5D%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/stocks/quote/[symbol]/route.ts":
/*!****************************************************!*\
  !*** ./src/app/api/stocks/quote/[symbol]/route.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_fmp__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/fmp */ \"(rsc)/./src/lib/fmp.ts\");\n\n\nasync function GET(request, { params }) {\n    try {\n        const { symbol } = await params;\n        if (!symbol) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Symbol parameter is required'\n            }, {\n                status: 400\n            });\n        }\n        console.log(`📊 Fetching quote for ${symbol}`);\n        // Use FMP API for stock quotes\n        const fmpAPI = new _lib_fmp__WEBPACK_IMPORTED_MODULE_1__.FMPAPI(process.env.FMP_API_KEY);\n        try {\n            const quote = await fmpAPI.getStockQuote(symbol.toUpperCase());\n            const response = {\n                success: true,\n                data: quote,\n                timestamp: new Date().toISOString()\n            };\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response);\n        } catch (apiError) {\n            console.error(`FMP API error for ${symbol}:`, apiError);\n            // Return a fallback response to prevent UI errors\n            const fallbackResponse = {\n                success: false,\n                error: 'API rate limit or service unavailable',\n                data: {\n                    symbol: symbol.toUpperCase(),\n                    price: 0,\n                    change: 0,\n                    changesPercentage: 0,\n                    volume: 0,\n                    avgVolume: 0,\n                    marketCap: 0,\n                    pe: 0,\n                    eps: 0,\n                    dayHigh: 0,\n                    dayLow: 0,\n                    open: 0,\n                    previousClose: 0\n                },\n                timestamp: new Date().toISOString()\n            };\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(fallbackResponse, {\n                status: 200\n            }) // Return 200 to prevent UI errors\n            ;\n        }\n    } catch (error) {\n        console.error('Error in quote API:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to fetch stock quote',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/stocks/quote/[symbol]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/fmp.ts":
/*!************************!*\
  !*** ./src/lib/fmp.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FMPAPI: () => (/* binding */ FMPAPI),\n/* harmony export */   fmpAPI: () => (/* binding */ fmpAPI)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/lib/axios.js\");\n\nconst FMP_BASE_URL = 'https://financialmodelingprep.com/api';\nconst API_KEY = process.env.FMP_API_KEY;\nclass FMPAPI {\n    constructor(apiKey){\n        this.apiKey = apiKey || API_KEY || '';\n        if (!this.apiKey) {\n            throw new Error('FMP API key is required');\n        }\n    }\n    // Get real-time stock quote\n    async getStockQuote(symbol) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${FMP_BASE_URL}/v3/quote/${symbol}`, {\n                params: {\n                    apikey: this.apiKey\n                }\n            });\n            const data = response.data[0];\n            if (!data) {\n                throw new Error(`No data found for symbol ${symbol}`);\n            }\n            return {\n                symbol: data.symbol,\n                name: data.name || data.symbol,\n                price: data.price,\n                change: data.change,\n                changePercent: data.changesPercentage,\n                volume: data.volume,\n                marketCap: data.marketCap,\n                pe: data.pe,\n                dividend: undefined // Will be fetched separately if needed\n            };\n        } catch (error) {\n            console.error('Error fetching FMP stock quote:', error);\n            throw new Error(`Failed to fetch quote for ${symbol}`);\n        }\n    }\n    // Get company profile\n    async getCompanyProfile(symbol) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${FMP_BASE_URL}/v3/profile/${symbol}`, {\n                params: {\n                    apikey: this.apiKey\n                }\n            });\n            return response.data[0];\n        } catch (error) {\n            console.error('Error fetching company profile:', error);\n            return null;\n        }\n    }\n    // Get financial ratios\n    async getFinancialRatios(symbol) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${FMP_BASE_URL}/v3/ratios/${symbol}`, {\n                params: {\n                    apikey: this.apiKey\n                }\n            });\n            return response.data[0] // Most recent ratios\n            ;\n        } catch (error) {\n            console.error('Error fetching financial ratios:', error);\n            return null;\n        }\n    }\n    // Get key metrics\n    async getKeyMetrics(symbol) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${FMP_BASE_URL}/v3/key-metrics/${symbol}`, {\n                params: {\n                    apikey: this.apiKey\n                }\n            });\n            return response.data[0] // Most recent metrics\n            ;\n        } catch (error) {\n            console.error('Error fetching key metrics:', error);\n            return null;\n        }\n    }\n    // Get analyst recommendations\n    async getAnalystRecommendations(symbol) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${FMP_BASE_URL}/v3/analyst-stock-recommendations/${symbol}`, {\n                params: {\n                    apikey: this.apiKey\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching analyst recommendations:', error);\n            return [];\n        }\n    }\n    // Get earnings calendar\n    async getEarningsCalendar(from, to) {\n        try {\n            const params = {\n                apikey: this.apiKey\n            };\n            if (from) params.from = from;\n            if (to) params.to = to;\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${FMP_BASE_URL}/v3/earning_calendar`, {\n                params\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching earnings calendar:', error);\n            return [];\n        }\n    }\n    // Get economic calendar\n    async getEconomicCalendar(from, to) {\n        try {\n            const params = {\n                apikey: this.apiKey\n            };\n            if (from) params.from = from;\n            if (to) params.to = to;\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${FMP_BASE_URL}/v3/economic_calendar`, {\n                params\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching economic calendar:', error);\n            return [];\n        }\n    }\n    // Search for stocks\n    async searchStocks(query, limit = 10) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${FMP_BASE_URL}/v3/search`, {\n                params: {\n                    query,\n                    limit,\n                    apikey: this.apiKey\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error searching stocks:', error);\n            return [];\n        }\n    }\n    // Get sector performance\n    async getSectorPerformance() {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${FMP_BASE_URL}/v3/sector-performance`, {\n                params: {\n                    apikey: this.apiKey\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching sector performance:', error);\n            return [];\n        }\n    }\n    // Get market gainers/losers\n    async getMarketMovers(type) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${FMP_BASE_URL}/v3/stock_market/${type}`, {\n                params: {\n                    apikey: this.apiKey\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error(`Error fetching market ${type}:`, error);\n            return [];\n        }\n    }\n    // ===== CATALYST DETECTION ENDPOINTS =====\n    // Get earnings calendar for catalyst detection\n    async getEarningsCalendar(symbol, days = 30) {\n        try {\n            const fromDate = new Date();\n            fromDate.setDate(fromDate.getDate() - days);\n            const toDate = new Date();\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${FMP_BASE_URL}/v3/earning_calendar`, {\n                params: {\n                    apikey: this.apiKey,\n                    from: fromDate.toISOString().split('T')[0],\n                    to: toDate.toISOString().split('T')[0],\n                    ...symbol && {\n                        symbol: symbol.toUpperCase()\n                    }\n                }\n            });\n            return response.data || [];\n        } catch (error) {\n            console.error('Error fetching earnings calendar:', error);\n            return [];\n        }\n    }\n    // Get stock news for catalyst detection\n    async getStockNews(symbol, limit = 50) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${FMP_BASE_URL}/v3/stock_news`, {\n                params: {\n                    apikey: this.apiKey,\n                    tickers: symbol.toUpperCase(),\n                    limit\n                }\n            });\n            return response.data || [];\n        } catch (error) {\n            console.error('Error fetching stock news:', error);\n            return [];\n        }\n    }\n    // Get analyst recommendations\n    async getAnalystRecommendations(symbol, days = 30) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${FMP_BASE_URL}/v3/analyst-stock-recommendations/${symbol.toUpperCase()}`, {\n                params: {\n                    apikey: this.apiKey,\n                    limit: days\n                }\n            });\n            return response.data || [];\n        } catch (error) {\n            console.error('Error fetching analyst recommendations:', error);\n            return [];\n        }\n    }\n    // Get insider trading data\n    async getInsiderTrading(symbol, days = 30) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${FMP_BASE_URL}/v4/insider-trading`, {\n                params: {\n                    apikey: this.apiKey,\n                    symbol: symbol.toUpperCase(),\n                    limit: days * 5 // Approximate multiple to get enough data\n                }\n            });\n            // Filter to last N days\n            const cutoffDate = new Date();\n            cutoffDate.setDate(cutoffDate.getDate() - days);\n            return (response.data || []).filter((trade)=>new Date(trade.filingDate) >= cutoffDate);\n        } catch (error) {\n            console.error('Error fetching insider trading:', error);\n            return [];\n        }\n    }\n    // Get SEC filings\n    async getSECFilings(symbol, days = 30) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${FMP_BASE_URL}/v3/sec_filings/${symbol.toUpperCase()}`, {\n                params: {\n                    apikey: this.apiKey,\n                    limit: days * 2 // Get more filings to filter by date\n                }\n            });\n            // Filter to last N days\n            const cutoffDate = new Date();\n            cutoffDate.setDate(cutoffDate.getDate() - days);\n            return (response.data || []).filter((filing)=>new Date(filing.filedDate) >= cutoffDate);\n        } catch (error) {\n            console.error('Error fetching SEC filings:', error);\n            return [];\n        }\n    }\n    // Get pre-market quotes for gap scanning\n    async getPreMarketQuote(symbol) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${FMP_BASE_URL}/v3/quote/${symbol.toUpperCase()}`, {\n                params: {\n                    apikey: this.apiKey\n                }\n            });\n            const data = response.data[0];\n            if (!data) return null;\n            return {\n                symbol: data.symbol,\n                price: data.price,\n                previousClose: data.previousClose,\n                change: data.change,\n                changePercent: data.changesPercentage,\n                volume: data.volume,\n                marketCap: data.marketCap,\n                avgVolume: data.avgVolume,\n                // Pre-market specific data (if available)\n                preMarketPrice: data.preMarketPrice || data.price,\n                preMarketChange: data.preMarketChange || data.change,\n                preMarketChangePercent: data.preMarketChangePercent || data.changesPercentage\n            };\n        } catch (error) {\n            console.error('Error fetching pre-market quote:', error);\n            return null;\n        }\n    }\n    // Get multiple pre-market quotes efficiently\n    async getMultiplePreMarketQuotes(symbols) {\n        try {\n            const symbolsString = symbols.map((s)=>s.toUpperCase()).join(',');\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${FMP_BASE_URL}/v3/quote/${symbolsString}`, {\n                params: {\n                    apikey: this.apiKey\n                }\n            });\n            return (response.data || []).map((data)=>({\n                    symbol: data.symbol,\n                    price: data.price,\n                    previousClose: data.previousClose,\n                    change: data.change,\n                    changePercent: data.changesPercentage,\n                    volume: data.volume,\n                    marketCap: data.marketCap,\n                    avgVolume: data.avgVolume,\n                    preMarketPrice: data.preMarketPrice || data.price,\n                    preMarketChange: data.preMarketChange || data.change,\n                    preMarketChangePercent: data.preMarketChangePercent || data.changesPercentage\n                }));\n        } catch (error) {\n            console.error('Error fetching multiple pre-market quotes:', error);\n            return [];\n        }\n    }\n    // Get company profile for additional context\n    async getCompanyProfile(symbol) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${FMP_BASE_URL}/v3/profile/${symbol.toUpperCase()}`, {\n                params: {\n                    apikey: this.apiKey\n                }\n            });\n            return response.data[0] || null;\n        } catch (error) {\n            console.error('Error fetching company profile:', error);\n            return null;\n        }\n    }\n}\n// Create a singleton instance\nconst fmpAPI = new FMPAPI();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/fmp.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstocks%2Fquote%2F%5Bsymbol%5D%2Froute&page=%2Fapi%2Fstocks%2Fquote%2F%5Bsymbol%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstocks%2Fquote%2F%5Bsymbol%5D%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();