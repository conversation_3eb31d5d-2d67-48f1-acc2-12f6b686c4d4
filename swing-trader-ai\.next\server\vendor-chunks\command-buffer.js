/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/command-buffer";
exports.ids = ["vendor-chunks/command-buffer"];
exports.modules = {

/***/ "(rsc)/./node_modules/command-buffer/dist/command-buffer.js":
/*!************************************************************!*\
  !*** ./node_modules/command-buffer/dist/command-buffer.js ***!
  \************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval("/* module decorator */ module = __webpack_require__.nmd(module);\nvar __WEBPACK_AMD_DEFINE_RESULT__;/*\n * `command-buffer` is a simple command buffer abstraction library written in JavaScript.\n *\n * Author: Pilwon Huh\n * Git Repo: https://github.com/pilwon/command-buffer\n * Licnese: MIT\n * Version: 0.1.0\n */\n\n;(function () {\n  var objectTypes = {\n    boolean: false,\n    function: true,\n    object: true,\n    number: false,\n    string: false,\n    undefined: false\n  };\n\n  // Used as a reference to the global object.\n  var root = (objectTypes[typeof window] && window) || this;\n\n  // Detect free variable `exports`.\n  var freeExports = objectTypes[typeof exports] && exports && !exports.nodeType && exports;\n\n  // Detect free variable `module`.\n  var freeModule = objectTypes[\"object\"] && module && !module.nodeType && module;\n\n  // Detect the popular CommonJS extension `module.exports`.\n  var moduleExports = freeModule && freeModule.exports === freeExports && freeExports;\n\n  // Detect free variable `global` from Node.js or Browserified code and use it as `root`.\n  var freeGlobal = objectTypes[typeof global] && global;\n  if (freeGlobal && (freeGlobal.global === freeGlobal || freeGlobal.window === freeGlobal)) {\n    root = freeGlobal;\n  }\n\n  var CommandBuffer = function (onCommandCallback, onCommandCallbackContext) {\n    this._cb = onCommandCallback;\n    this._cbCtx = onCommandCallbackContext;\n    this._commands = [];\n    this._commandsCurrent = [];\n    this._paused = false;\n    this._processing = false;\n  };\n\n  CommandBuffer.prototype._concatCurrentCommands = function () {\n    this._commands = this._commandsCurrent.concat(this._commands);\n    this._commandsCurrent.length = 0;\n  };\n\n  CommandBuffer.prototype._process = function () {\n    var command;\n\n    while (!this._paused) {\n      this._processing = true;\n\n      if (!this._commands.length) {\n        this._concatCurrentCommands();\n      }\n\n      command = this._commands.shift();\n      if (!command) { break; }\n\n      this._cb.call(this._cbCtx, command.type, command.data);\n\n      this._concatCurrentCommands();\n    }\n\n    this._processing = false;\n  };\n\n  CommandBuffer.prototype.pause = function () {\n    this._paused = true;\n  };\n\n  CommandBuffer.prototype.resume = function () {\n    this._paused = false;\n    this._concatCurrentCommands();\n    if (!this._processing) {\n      this._process();\n    }\n  };\n\n  CommandBuffer.prototype.run = function (type, data) {\n    this._commandsCurrent.push({\n      type: type,\n      data: data\n    });\n    if (!this._processing) {\n      this._process();\n    }\n  };\n\n  CommandBuffer.prototype.schedule = function (type, data) {\n    this._commands.push({\n      type: type,\n      data: data\n    });\n    if (!this._processing) {\n      this._process();\n    }\n  };\n\n  // some AMD build optimizers like r.js check for condition patterns like the following:\n  if (true) {\n    // Expose CommandBuffer to the global object even when an AMD loader is present in\n    // case CommandBuffer was injected by a third-party script and not intended to be\n    // loaded as a module. The global assignment can be reverted in the CommandBuffer\n    // module by its `noConflict()` method.\n    root.CommandBuffer = CommandBuffer;\n\n    // define as an anonymous module so, through path mapping, it can be\n    // referenced as the \"underscore\" module\n    !(__WEBPACK_AMD_DEFINE_RESULT__ = (function() {\n      return CommandBuffer;\n    }).call(exports, __webpack_require__, exports, module),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n  }\n  // check for `exports` after `define` in case a build optimizer adds an `exports` object\n  else {}\n}.call(this));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/command-buffer/dist/command-buffer.js\n");

/***/ })

};
;