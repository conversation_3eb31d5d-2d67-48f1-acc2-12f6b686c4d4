module.exports=[78015,(a,b,c)=>{let{createClientModuleProxy:d}=a.r(427);a.n(d("[project]/swing-trader-ai/node_modules/next/dist/client/components/builtin/global-error.js <module evaluation>"))},32396,(a,b,c)=>{let{createClientModuleProxy:d}=a.r(427);a.n(d("[project]/swing-trader-ai/node_modules/next/dist/client/components/builtin/global-error.js"))},44938,a=>{"use strict";a.i(78015);var b=a.i(32396);a.n(b)}];

//# sourceMappingURL=6bf44_next_dist_client_components_builtin_global-error_db3b6b97.js.map