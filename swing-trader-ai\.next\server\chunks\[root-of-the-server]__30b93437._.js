module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},14747,(e,t,r)=>{t.exports=e.x("path",()=>require("path"))},29547,e=>{"use strict";e.s(["FMPAPI",()=>n]);var t=e.i(55362);let r="https://financialmodelingprep.com/api",a=process.env.FMP_API_KEY;class n{apiKey;constructor(e){if(this.apiKey=e||a||"",!this.apiKey)throw Error("FMP API key is required")}async getStockQuote(e){try{let a=(await t.default.get(`${r}/v3/quote/${e}`,{params:{apikey:this.apiKey}})).data[0];if(!a)throw Error(`No data found for symbol ${e}`);return{symbol:a.symbol,name:a.name||a.symbol,price:a.price,change:a.change,changePercent:a.changesPercentage,volume:a.volume,marketCap:a.marketCap,pe:a.pe,dividend:void 0}}catch(t){throw console.error("Error fetching FMP stock quote:",t),Error(`Failed to fetch quote for ${e}`)}}async getCompanyProfile(e){try{return(await t.default.get(`${r}/v3/profile/${e}`,{params:{apikey:this.apiKey}})).data[0]}catch(e){return console.error("Error fetching company profile:",e),null}}async getFinancialRatios(e){try{return(await t.default.get(`${r}/v3/ratios/${e}`,{params:{apikey:this.apiKey}})).data[0]}catch(e){return console.error("Error fetching financial ratios:",e),null}}async getKeyMetrics(e){try{return(await t.default.get(`${r}/v3/key-metrics/${e}`,{params:{apikey:this.apiKey}})).data[0]}catch(e){return console.error("Error fetching key metrics:",e),null}}async getAnalystRecommendations(e){try{return(await t.default.get(`${r}/v3/analyst-stock-recommendations/${e}`,{params:{apikey:this.apiKey}})).data}catch(e){return console.error("Error fetching analyst recommendations:",e),[]}}async getEarningsCalendar(e,a){try{let n={apikey:this.apiKey};return e&&(n.from=e),a&&(n.to=a),(await t.default.get(`${r}/v3/earning_calendar`,{params:n})).data}catch(e){return console.error("Error fetching earnings calendar:",e),[]}}async getEconomicCalendar(e,a){try{let n={apikey:this.apiKey};return e&&(n.from=e),a&&(n.to=a),(await t.default.get(`${r}/v3/economic_calendar`,{params:n})).data}catch(e){return console.error("Error fetching economic calendar:",e),[]}}async searchStocks(e,a=10){try{return(await t.default.get(`${r}/v3/search`,{params:{query:e,limit:a,apikey:this.apiKey}})).data}catch(e){return console.error("Error searching stocks:",e),[]}}async getSectorPerformance(){try{return(await t.default.get(`${r}/v3/sector-performance`,{params:{apikey:this.apiKey}})).data}catch(e){return console.error("Error fetching sector performance:",e),[]}}async getMarketMovers(e){try{return(await t.default.get(`${r}/v3/stock_market/${e}`,{params:{apikey:this.apiKey}})).data}catch(t){return console.error(`Error fetching market ${e}:`,t),[]}}async getEarningsCalendar(e,a=30){try{let n=new Date;n.setDate(n.getDate()-a);let o=new Date;return(await t.default.get(`${r}/v3/earning_calendar`,{params:{apikey:this.apiKey,from:n.toISOString().split("T")[0],to:o.toISOString().split("T")[0],...e&&{symbol:e.toUpperCase()}}})).data||[]}catch(e){return console.error("Error fetching earnings calendar:",e),[]}}async getStockNews(e,a=50){try{return(await t.default.get(`${r}/v3/stock_news`,{params:{apikey:this.apiKey,tickers:e.toUpperCase(),limit:a}})).data||[]}catch(e){return console.error("Error fetching stock news:",e),[]}}async getAnalystRecommendations(e,a=30){try{return(await t.default.get(`${r}/v3/analyst-stock-recommendations/${e.toUpperCase()}`,{params:{apikey:this.apiKey,limit:a}})).data||[]}catch(e){return console.error("Error fetching analyst recommendations:",e),[]}}async getInsiderTrading(e,a=30){try{let n=await t.default.get(`${r}/v4/insider-trading`,{params:{apikey:this.apiKey,symbol:e.toUpperCase(),limit:5*a}}),o=new Date;return o.setDate(o.getDate()-a),(n.data||[]).filter(e=>new Date(e.filingDate)>=o)}catch(e){return console.error("Error fetching insider trading:",e),[]}}async getSECFilings(e,a=30){try{let n=await t.default.get(`${r}/v3/sec_filings/${e.toUpperCase()}`,{params:{apikey:this.apiKey,limit:2*a}}),o=new Date;return o.setDate(o.getDate()-a),(n.data||[]).filter(e=>new Date(e.filedDate)>=o)}catch(e){return console.error("Error fetching SEC filings:",e),[]}}async getPreMarketQuote(e){try{let a=(await t.default.get(`${r}/v3/quote/${e.toUpperCase()}`,{params:{apikey:this.apiKey}})).data[0];if(!a)return null;return{symbol:a.symbol,price:a.price,previousClose:a.previousClose,change:a.change,changePercent:a.changesPercentage,volume:a.volume,marketCap:a.marketCap,avgVolume:a.avgVolume,preMarketPrice:a.preMarketPrice||a.price,preMarketChange:a.preMarketChange||a.change,preMarketChangePercent:a.preMarketChangePercent||a.changesPercentage}}catch(e){return console.error("Error fetching pre-market quote:",e),null}}async getMultiplePreMarketQuotes(e){try{let a=e.map(e=>e.toUpperCase()).join(",");return((await t.default.get(`${r}/v3/quote/${a}`,{params:{apikey:this.apiKey}})).data||[]).map(e=>({symbol:e.symbol,price:e.price,previousClose:e.previousClose,change:e.change,changePercent:e.changesPercentage,volume:e.volume,marketCap:e.marketCap,avgVolume:e.avgVolume,preMarketPrice:e.preMarketPrice||e.price,preMarketChange:e.preMarketChange||e.change,preMarketChangePercent:e.preMarketChangePercent||e.changesPercentage}))}catch(e){return console.error("Error fetching multiple pre-market quotes:",e),[]}}async getCompanyProfile(e){try{return(await t.default.get(`${r}/v3/profile/${e.toUpperCase()}`,{params:{apikey:this.apiKey}})).data[0]||null}catch(e){return console.error("Error fetching company profile:",e),null}}}new n},57153,(e,t,r)=>{},54640,e=>{"use strict";e.s(["handler",()=>$,"patchFetch",()=>P,"routeModule",()=>w,"serverHooks",()=>R,"workAsyncStorage",()=>E,"workUnitAsyncStorage",()=>C],54640);var t=e.i(11971),r=e.i(6780),a=e.i(51842),n=e.i(62950),o=e.i(21346),s=e.i(30506),i=e.i(63077),l=e.i(34765),c=e.i(64182),p=e.i(85062),u=e.i(51548),d=e.i(95133),h=e.i(8819),g=e.i(41050),y=e.i(93695);e.i(96641);var m=e.i(3893);e.s(["GET",()=>k],3966);var f=e.i(59169),v=e.i(29547);async function k(e,{params:t}){try{let{symbol:e}=await t;if(!e)return f.NextResponse.json({error:"Symbol parameter is required"},{status:400});let r=new v.FMPAPI(process.env.FMP_API_KEY),a=await r.getStockQuote(e.toUpperCase());return f.NextResponse.json(a)}catch(e){return console.error("Error in quote API:",e),f.NextResponse.json({error:"Failed to fetch stock quote"},{status:500})}}var x=e.i(3966);let w=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/stocks/quote/[symbol]/route",pathname:"/api/stocks/quote/[symbol]",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/swing-trader-ai/src/app/api/stocks/quote/[symbol]/route.ts",nextConfigOutput:"",userland:x}),{workAsyncStorage:E,workUnitAsyncStorage:C,serverHooks:R}=w;function P(){return(0,a.patchFetch)({workAsyncStorage:E,workUnitAsyncStorage:C})}async function $(e,t,a){var f;let v="/api/stocks/quote/[symbol]/route";v=v.replace(/\/index$/,"")||"/";let k=await w.prepare(e,t,{srcPage:v,multiZoneDraftMode:!1});if(!k)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:x,params:E,nextConfig:C,isDraftMode:R,prerenderManifest:P,routerServerContext:$,isOnDemandRevalidate:b,revalidateOnlyGenerated:q,resolvedPathname:M}=k,A=(0,s.normalizeAppPath)(v),K=!!(P.dynamicRoutes[A]||P.routes[M]);if(K&&!R){let e=!!P.routes[M],t=P.dynamicRoutes[A];if(t&&!1===t.fallback&&!e)throw new y.NoFallbackError}let S=null;!K||w.isDev||R||(S="/index"===(S=M)?"/":S);let _=!0===w.isDev||!K,N=K&&!_,D=e.method||"GET",T=(0,o.getTracer)(),U=T.getActiveScopeSpan(),j={params:E,prerenderManifest:P,renderOpts:{experimental:{cacheComponents:!!C.experimental.cacheComponents,authInterrupts:!!C.experimental.authInterrupts},supportsDynamicResponse:_,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(f=C.experimental)?void 0:f.cacheLife,isRevalidate:N,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>w.onRequestError(e,t,a,$)},sharedContext:{buildId:x}},I=new i.NodeNextRequest(e),O=new i.NodeNextResponse(t),F=l.NextRequestAdapter.fromNodeNextRequest(I,(0,l.signalFromNodeResponse)(t));try{let s=async r=>w.handle(F,j).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=T.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==c.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${D} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${D} ${e.url}`)}),i=async o=>{var i,l;let c=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&b&&q&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let i=await s(o);e.fetchMetrics=j.renderOpts.fetchMetrics;let l=j.renderOpts.pendingWaitUntil;l&&a.waitUntil&&(a.waitUntil(l),l=void 0);let c=j.renderOpts.collectedTags;if(!K)return await (0,u.sendResponse)(I,O,i,j.renderOpts.pendingWaitUntil),null;{let e=await i.blob(),t=(0,d.toNodeOutgoingHttpHeaders)(i.headers);c&&(t[g.NEXT_CACHE_TAGS_HEADER]=c),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==j.renderOpts.collectedRevalidate&&!(j.renderOpts.collectedRevalidate>=g.INFINITE_CACHE)&&j.renderOpts.collectedRevalidate,a=void 0===j.renderOpts.collectedExpire||j.renderOpts.collectedExpire>=g.INFINITE_CACHE?void 0:j.renderOpts.collectedExpire;return{value:{kind:m.CachedRouteKind.APP_ROUTE,status:i.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await w.onRequestError(e,t,{routerKind:"App Router",routePath:v,routeType:"route",revalidateReason:(0,p.getRevalidateReason)({isRevalidate:N,isOnDemandRevalidate:b})},$),t}},y=await w.handleResponse({req:e,nextConfig:C,cacheKey:S,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:P,isRoutePPREnabled:!1,isOnDemandRevalidate:b,revalidateOnlyGenerated:q,responseGenerator:c,waitUntil:a.waitUntil});if(!K)return null;if((null==y||null==(i=y.value)?void 0:i.kind)!==m.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==y||null==(l=y.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",b?"REVALIDATED":y.isMiss?"MISS":y.isStale?"STALE":"HIT"),R&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let f=(0,d.fromNodeOutgoingHttpHeaders)(y.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&K||f.delete(g.NEXT_CACHE_TAGS_HEADER),!y.cacheControl||t.getHeader("Cache-Control")||f.get("Cache-Control")||f.set("Cache-Control",(0,h.getCacheControlHeader)(y.cacheControl)),await (0,u.sendResponse)(I,O,new Response(y.value.body,{headers:f,status:y.value.status||200})),null};U?await i(U):await T.withPropagatedContext(e.headers,()=>T.trace(c.BaseServerSpan.handleRequest,{spanName:`${D} ${e.url}`,kind:o.SpanKind.SERVER,attributes:{"http.method":D,"http.target":e.url}},i))}catch(t){if(t instanceof y.NoFallbackError||await w.onRequestError(e,t,{routerKind:"App Router",routePath:A,routeType:"route",revalidateReason:(0,p.getRevalidateReason)({isRevalidate:N,isOnDemandRevalidate:b})}),K)throw t;return await (0,u.sendResponse)(I,O,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__30b93437._.js.map