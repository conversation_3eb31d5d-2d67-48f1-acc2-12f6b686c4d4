var R=require("../../../chunks/[turbopack]_runtime.js")("server/app/api/ai/route.js")
R.c("server/chunks/6bf44_next_ac9c0162._.js")
R.c("server/chunks/6bf44_openai_bfcce7e2._.js")
R.c("server/chunks/[root-of-the-server]__673bba53._.js")
R.m("[project]/swing-trader-ai/.next-internal/server/app/api/ai/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/swing-trader-ai/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/swing-trader-ai/src/app/api/ai/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/swing-trader-ai/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/swing-trader-ai/src/app/api/ai/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
