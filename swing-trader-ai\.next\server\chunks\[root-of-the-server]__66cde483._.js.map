{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/lib/alpaca.ts"], "sourcesContent": ["import Alpaca from '@alpacahq/alpaca-trade-api'\n\nexport class AlpacaAPI {\n  private client: Alpaca\n\n  constructor() {\n    this.client = new Alpaca({\n      keyId: process.env.ALPACA_API_KEY!,\n      secretKey: process.env.ALPACA_SECRET_KEY!,\n      baseUrl: process.env.ALPACA_BASE_URL || 'https://paper-api.alpaca.markets',\n      usePolygon: false\n    })\n  }\n\n  // Get account information\n  async getAccount() {\n    try {\n      return await this.client.getAccount()\n    } catch (error) {\n      console.error('Error fetching account:', error)\n      throw error\n    }\n  }\n\n  // Get account positions\n  async getPositions() {\n    try {\n      return await this.client.getPositions()\n    } catch (error) {\n      console.error('Error fetching positions:', error)\n      throw error\n    }\n  }\n\n  // Get orders\n  async getOrders(status?: 'open' | 'closed' | 'all') {\n    try {\n      return await this.client.getOrders({\n        status: status || 'all',\n        limit: 100,\n        nested: true,\n        until: undefined,\n        after: undefined,\n        direction: undefined,\n        symbols: undefined\n      })\n    } catch (error) {\n      console.error('Error fetching orders:', error)\n      throw error\n    }\n  }\n\n  // Place a market order\n  async placeMarketOrder(\n    symbol: string,\n    qty: number,\n    side: 'buy' | 'sell',\n    timeInForce: 'day' | 'gtc' | 'ioc' | 'fok' = 'day'\n  ) {\n    try {\n      return await this.client.createOrder({\n        symbol,\n        qty,\n        side,\n        type: 'market',\n        time_in_force: timeInForce\n      })\n    } catch (error) {\n      console.error('Error placing market order:', error)\n      throw error\n    }\n  }\n\n  // Place a limit order\n  async placeLimitOrder(\n    symbol: string,\n    qty: number,\n    side: 'buy' | 'sell',\n    limitPrice: number,\n    timeInForce: 'day' | 'gtc' | 'ioc' | 'fok' = 'day'\n  ) {\n    try {\n      return await this.client.createOrder({\n        symbol,\n        qty,\n        side,\n        type: 'limit',\n        limit_price: limitPrice,\n        time_in_force: timeInForce\n      })\n    } catch (error) {\n      console.error('Error placing limit order:', error)\n      throw error\n    }\n  }\n\n  // Place a stop-loss order\n  async placeStopLossOrder(\n    symbol: string,\n    qty: number,\n    side: 'buy' | 'sell',\n    stopPrice: number,\n    timeInForce: 'day' | 'gtc' | 'ioc' | 'fok' = 'day'\n  ) {\n    try {\n      return await this.client.createOrder({\n        symbol,\n        qty,\n        side,\n        type: 'stop',\n        stop_price: stopPrice,\n        time_in_force: timeInForce\n      })\n    } catch (error) {\n      console.error('Error placing stop-loss order:', error)\n      throw error\n    }\n  }\n\n  // Place a bracket order (entry + stop loss + take profit)\n  async placeBracketOrder(\n    symbol: string,\n    qty: number,\n    side: 'buy' | 'sell',\n    limitPrice: number,\n    stopLoss: number,\n    takeProfit: number,\n    timeInForce: 'day' | 'gtc' = 'day'\n  ) {\n    try {\n      return await this.client.createOrder({\n        symbol,\n        qty,\n        side,\n        type: 'limit',\n        limit_price: limitPrice,\n        time_in_force: timeInForce,\n        order_class: 'bracket',\n        stop_loss: {\n          stop_price: stopLoss\n        },\n        take_profit: {\n          limit_price: takeProfit\n        }\n      })\n    } catch (error) {\n      console.error('Error placing bracket order:', error)\n      throw error\n    }\n  }\n\n  // Cancel an order\n  async cancelOrder(orderId: string) {\n    try {\n      return await this.client.cancelOrder(orderId)\n    } catch (error) {\n      console.error('Error canceling order:', error)\n      throw error\n    }\n  }\n\n  // Cancel all orders\n  async cancelAllOrders() {\n    try {\n      return await this.client.cancelAllOrders()\n    } catch (error) {\n      console.error('Error canceling all orders:', error)\n      throw error\n    }\n  }\n\n  // Get portfolio history\n  async getPortfolioHistory(period?: '1D' | '7D' | '1M' | '3M' | '1Y' | 'all') {\n    try {\n      return await this.client.getPortfolioHistory({\n        period: period || '1M',\n        timeframe: '1Day'\n      })\n    } catch (error) {\n      console.error('Error fetching portfolio history:', error)\n      throw error\n    }\n  }\n\n  // Get market calendar\n  async getMarketCalendar(start?: string, end?: string) {\n    try {\n      return await this.client.getCalendar({\n        start,\n        end\n      })\n    } catch (error) {\n      console.error('Error fetching market calendar:', error)\n      throw error\n    }\n  }\n\n  // Check if market is open\n  async isMarketOpen() {\n    try {\n      const clock = await this.client.getClock()\n      return clock.is_open\n    } catch (error) {\n      console.error('Error checking market status:', error)\n      return false\n    }\n  }\n\n  // Get buying power\n  async getBuyingPower() {\n    try {\n      const account = await this.getAccount()\n      return parseFloat(account.buying_power)\n    } catch (error) {\n      console.error('Error fetching buying power:', error)\n      return 0\n    }\n  }\n\n  // Calculate position size based on risk\n  calculatePositionSize(\n    accountValue: number,\n    riskPercentage: number,\n    entryPrice: number,\n    stopLoss: number\n  ): number {\n    const riskAmount = accountValue * (riskPercentage / 100)\n    const riskPerShare = Math.abs(entryPrice - stopLoss)\n    return Math.floor(riskAmount / riskPerShare)\n  }\n}\n\n// Create a singleton instance\nexport const alpacaAPI = new AlpacaAPI()\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEO,MAAM;IACH,OAAc;IAEtB,aAAc;QACZ,IAAI,CAAC,MAAM,GAAG,IAAI,2MAAM,CAAC;YACvB,OAAO,QAAQ,GAAG,CAAC,cAAc;YACjC,WAAW,QAAQ,GAAG,CAAC,iBAAiB;YACxC,SAAS,QAAQ,GAAG,CAAC,eAAe,IAAI;YACxC,YAAY;QACd;IACF;IAEA,0BAA0B;IAC1B,MAAM,aAAa;QACjB,IAAI;YACF,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;IAEA,wBAAwB;IACxB,MAAM,eAAe;QACnB,IAAI;YACF,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY;QACvC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,aAAa;IACb,MAAM,UAAU,MAAkC,EAAE;QAClD,IAAI;YACF,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;gBACjC,QAAQ,UAAU;gBAClB,OAAO;gBACP,QAAQ;gBACR,OAAO;gBACP,OAAO;gBACP,WAAW;gBACX,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA,uBAAuB;IACvB,MAAM,iBACJ,MAAc,EACd,GAAW,EACX,IAAoB,EACpB,cAA6C,KAAK,EAClD;QACA,IAAI;YACF,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;gBACnC;gBACA;gBACA;gBACA,MAAM;gBACN,eAAe;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;IACF;IAEA,sBAAsB;IACtB,MAAM,gBACJ,MAAc,EACd,GAAW,EACX,IAAoB,EACpB,UAAkB,EAClB,cAA6C,KAAK,EAClD;QACA,IAAI;YACF,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;gBACnC;gBACA;gBACA;gBACA,MAAM;gBACN,aAAa;gBACb,eAAe;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR;IACF;IAEA,0BAA0B;IAC1B,MAAM,mBACJ,MAAc,EACd,GAAW,EACX,IAAoB,EACpB,SAAiB,EACjB,cAA6C,KAAK,EAClD;QACA,IAAI;YACF,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;gBACnC;gBACA;gBACA;gBACA,MAAM;gBACN,YAAY;gBACZ,eAAe;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM;QACR;IACF;IAEA,0DAA0D;IAC1D,MAAM,kBACJ,MAAc,EACd,GAAW,EACX,IAAoB,EACpB,UAAkB,EAClB,QAAgB,EAChB,UAAkB,EAClB,cAA6B,KAAK,EAClC;QACA,IAAI;YACF,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;gBACnC;gBACA;gBACA;gBACA,MAAM;gBACN,aAAa;gBACb,eAAe;gBACf,aAAa;gBACb,WAAW;oBACT,YAAY;gBACd;gBACA,aAAa;oBACX,aAAa;gBACf;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR;IACF;IAEA,kBAAkB;IAClB,MAAM,YAAY,OAAe,EAAE;QACjC,IAAI;YACF,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;QACvC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA,oBAAoB;IACpB,MAAM,kBAAkB;QACtB,IAAI;YACF,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,eAAe;QAC1C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;IACF;IAEA,wBAAwB;IACxB,MAAM,oBAAoB,MAAiD,EAAE;QAC3E,IAAI;YACF,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC;gBAC3C,QAAQ,UAAU;gBAClB,WAAW;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,MAAM;QACR;IACF;IAEA,sBAAsB;IACtB,MAAM,kBAAkB,KAAc,EAAE,GAAY,EAAE;QACpD,IAAI;YACF,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;gBACnC;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR;IACF;IAEA,0BAA0B;IAC1B,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,QAAQ,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ;YACxC,OAAO,MAAM,OAAO;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;QACT;IACF;IAEA,mBAAmB;IACnB,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,UAAU,MAAM,IAAI,CAAC,UAAU;YACrC,OAAO,WAAW,QAAQ,YAAY;QACxC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO;QACT;IACF;IAEA,wCAAwC;IACxC,sBACE,YAAoB,EACpB,cAAsB,EACtB,UAAkB,EAClB,QAAgB,EACR;QACR,MAAM,aAAa,eAAe,CAAC,iBAAiB,GAAG;QACvD,MAAM,eAAe,KAAK,GAAG,CAAC,aAAa;QAC3C,OAAO,KAAK,KAAK,CAAC,aAAa;IACjC;AACF;AAGO,MAAM,YAAY,IAAI", "debugId": null}}, {"offset": {"line": 348, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/lib/ibkr.ts"], "sourcesContent": ["import { I<PERSON>pi, EventName, ErrorCode, Contract, Order, OrderState } from '@stoqey/ib';\n\nexport interface IBKRConfig {\n  host: string;\n  port: number;\n  clientId: number;\n  paperTrading: boolean;\n}\n\nexport interface IBKRPosition {\n  symbol: string;\n  position: number;\n  marketPrice: number;\n  marketValue: number;\n  averageCost: number;\n  unrealizedPNL: number;\n  realizedPNL: number;\n}\n\nexport interface IBKROrder {\n  orderId: number;\n  symbol: string;\n  action: 'BUY' | 'SELL';\n  quantity: number;\n  orderType: string;\n  price?: number;\n  status: string;\n  filled: number;\n  remaining: number;\n}\n\nexport interface IBKRAccountSummary {\n  totalCashValue: number;\n  netLiquidation: number;\n  grossPositionValue: number;\n  availableFunds: number;\n  buyingPower: number;\n  unrealizedPnL: number;\n  realizedPnL: number;\n}\n\nexport class IBKRAPI {\n  private ib: IBApi;\n  private config: IBKRConfig;\n  private connected: boolean = false;\n  private nextOrderId: number = 1;\n  private positions: Map<string, IBKRPosition> = new Map();\n  private orders: Map<number, IBKROrder> = new Map();\n  private accountSummary: IBKRAccountSummary | null = null;\n\n  constructor(config: IBKRConfig) {\n    this.config = config;\n    this.ib = new IBApi({\n      host: config.host,\n      port: config.port,\n      clientId: config.clientId,\n    });\n\n    this.setupEventHandlers();\n  }\n\n  private setupEventHandlers() {\n    // Connection events\n    this.ib.on(EventName.connected, () => {\n      console.log('✅ Connected to IBKR');\n      this.connected = true;\n      this.requestNextOrderId();\n      this.requestAccountSummary();\n      this.requestPositions();\n    });\n\n    this.ib.on(EventName.disconnected, () => {\n      console.log('❌ Disconnected from IBKR');\n      this.connected = false;\n    });\n\n    this.ib.on(EventName.error, (err, code, reqId) => {\n      console.error(`IBKR Error ${code}:`, err);\n    });\n\n    // Order management\n    this.ib.on(EventName.nextValidId, (orderId: number) => {\n      this.nextOrderId = orderId;\n      console.log(`Next valid order ID: ${orderId}`);\n    });\n\n    this.ib.on(EventName.orderStatus, (orderId, status, filled, remaining, avgFillPrice, permId, parentId, lastFillPrice, clientId, whyHeld, mktCapPrice) => {\n      const order = this.orders.get(orderId);\n      if (order) {\n        order.status = status;\n        order.filled = filled;\n        order.remaining = remaining;\n        this.orders.set(orderId, order);\n      }\n    });\n\n    // Position updates\n    this.ib.on(EventName.position, (account, contract, position, avgCost) => {\n      const symbol = contract.symbol;\n      const existingPosition = this.positions.get(symbol) || {\n        symbol,\n        position: 0,\n        marketPrice: 0,\n        marketValue: 0,\n        averageCost: 0,\n        unrealizedPNL: 0,\n        realizedPNL: 0\n      };\n\n      existingPosition.position = position;\n      existingPosition.averageCost = avgCost;\n      this.positions.set(symbol, existingPosition);\n    });\n\n    // Account summary\n    this.ib.on(EventName.accountSummary, (reqId, account, tag, value, currency) => {\n      if (!this.accountSummary) {\n        this.accountSummary = {\n          totalCashValue: 0,\n          netLiquidation: 0,\n          grossPositionValue: 0,\n          availableFunds: 0,\n          buyingPower: 0,\n          unrealizedPnL: 0,\n          realizedPnL: 0\n        };\n      }\n\n      switch (tag) {\n        case 'TotalCashValue':\n          this.accountSummary.totalCashValue = parseFloat(value);\n          break;\n        case 'NetLiquidation':\n          this.accountSummary.netLiquidation = parseFloat(value);\n          break;\n        case 'GrossPositionValue':\n          this.accountSummary.grossPositionValue = parseFloat(value);\n          break;\n        case 'AvailableFunds':\n          this.accountSummary.availableFunds = parseFloat(value);\n          break;\n        case 'BuyingPower':\n          this.accountSummary.buyingPower = parseFloat(value);\n          break;\n        case 'UnrealizedPnL':\n          this.accountSummary.unrealizedPnL = parseFloat(value);\n          break;\n        case 'RealizedPnL':\n          this.accountSummary.realizedPnL = parseFloat(value);\n          break;\n      }\n    });\n  }\n\n  async connect(): Promise<void> {\n    return new Promise((resolve, reject) => {\n      if (this.connected) {\n        resolve();\n        return;\n      }\n\n      const timeout = setTimeout(() => {\n        reject(new Error('Connection timeout'));\n      }, 10000);\n\n      this.ib.once(EventName.connected, () => {\n        clearTimeout(timeout);\n        resolve();\n      });\n\n      this.ib.once(EventName.error, (err) => {\n        clearTimeout(timeout);\n        reject(err);\n      });\n\n      this.ib.connect();\n    });\n  }\n\n  disconnect(): void {\n    if (this.connected) {\n      this.ib.disconnect();\n    }\n  }\n\n  private requestNextOrderId(): void {\n    this.ib.reqIds(1);\n  }\n\n  private requestAccountSummary(): void {\n    this.ib.reqAccountSummary(1, 'All', 'TotalCashValue,NetLiquidation,GrossPositionValue,AvailableFunds,BuyingPower,UnrealizedPnL,RealizedPnL');\n  }\n\n  private requestPositions(): void {\n    this.ib.reqPositions();\n  }\n\n  // Create a stock contract\n  private createStockContract(symbol: string): Contract {\n    return {\n      symbol: symbol.toUpperCase(),\n      secType: 'STK',\n      exchange: 'SMART',\n      currency: 'USD',\n    };\n  }\n\n  // Place a market order\n  async placeMarketOrder(symbol: string, action: 'BUY' | 'SELL', quantity: number): Promise<number> {\n    if (!this.connected) {\n      throw new Error('Not connected to IBKR');\n    }\n\n    const contract = this.createStockContract(symbol);\n    const order: Order = {\n      orderId: this.nextOrderId,\n      action,\n      totalQuantity: quantity,\n      orderType: 'MKT',\n    };\n\n    // Store order for tracking\n    this.orders.set(this.nextOrderId, {\n      orderId: this.nextOrderId,\n      symbol: symbol.toUpperCase(),\n      action,\n      quantity,\n      orderType: 'MKT',\n      status: 'Submitted',\n      filled: 0,\n      remaining: quantity\n    });\n\n    this.ib.placeOrder(this.nextOrderId, contract, order);\n    const orderId = this.nextOrderId;\n    this.nextOrderId++;\n\n    return orderId;\n  }\n\n  // Place a limit order\n  async placeLimitOrder(symbol: string, action: 'BUY' | 'SELL', quantity: number, price: number): Promise<number> {\n    if (!this.connected) {\n      throw new Error('Not connected to IBKR');\n    }\n\n    const contract = this.createStockContract(symbol);\n    const order: Order = {\n      orderId: this.nextOrderId,\n      action,\n      totalQuantity: quantity,\n      orderType: 'LMT',\n      lmtPrice: price,\n    };\n\n    // Store order for tracking\n    this.orders.set(this.nextOrderId, {\n      orderId: this.nextOrderId,\n      symbol: symbol.toUpperCase(),\n      action,\n      quantity,\n      orderType: 'LMT',\n      price,\n      status: 'Submitted',\n      filled: 0,\n      remaining: quantity\n    });\n\n    this.ib.placeOrder(this.nextOrderId, contract, order);\n    const orderId = this.nextOrderId;\n    this.nextOrderId++;\n\n    return orderId;\n  }\n\n  // Place a stop-loss order\n  async placeStopOrder(symbol: string, action: 'BUY' | 'SELL', quantity: number, stopPrice: number): Promise<number> {\n    if (!this.connected) {\n      throw new Error('Not connected to IBKR');\n    }\n\n    const contract = this.createStockContract(symbol);\n    const order: Order = {\n      orderId: this.nextOrderId,\n      action,\n      totalQuantity: quantity,\n      orderType: 'STP',\n      auxPrice: stopPrice,\n    };\n\n    // Store order for tracking\n    this.orders.set(this.nextOrderId, {\n      orderId: this.nextOrderId,\n      symbol: symbol.toUpperCase(),\n      action,\n      quantity,\n      orderType: 'STP',\n      price: stopPrice,\n      status: 'Submitted',\n      filled: 0,\n      remaining: quantity\n    });\n\n    this.ib.placeOrder(this.nextOrderId, contract, order);\n    const orderId = this.nextOrderId;\n    this.nextOrderId++;\n\n    return orderId;\n  }\n\n  // Cancel an order\n  async cancelOrder(orderId: number): Promise<void> {\n    if (!this.connected) {\n      throw new Error('Not connected to IBKR');\n    }\n\n    this.ib.cancelOrder(orderId);\n  }\n\n  // Get account summary\n  getAccountSummary(): IBKRAccountSummary | null {\n    return this.accountSummary;\n  }\n\n  // Get all positions\n  getPositions(): IBKRPosition[] {\n    return Array.from(this.positions.values());\n  }\n\n  // Get position for specific symbol\n  getPosition(symbol: string): IBKRPosition | null {\n    return this.positions.get(symbol.toUpperCase()) || null;\n  }\n\n  // Get all orders\n  getOrders(): IBKROrder[] {\n    return Array.from(this.orders.values());\n  }\n\n  // Get specific order\n  getOrder(orderId: number): IBKROrder | null {\n    return this.orders.get(orderId) || null;\n  }\n\n  // Check if connected\n  isConnected(): boolean {\n    return this.connected;\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAyCO,MAAM;IACH,GAAU;IACV,OAAmB;IACnB,YAAqB,MAAM;IAC3B,cAAsB,EAAE;IACxB,YAAuC,IAAI,MAAM;IACjD,SAAiC,IAAI,MAAM;IAC3C,iBAA4C,KAAK;IAEzD,YAAY,MAAkB,CAAE;QAC9B,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,EAAE,GAAG,IAAI,mLAAK,CAAC;YAClB,MAAM,OAAO,IAAI;YACjB,MAAM,OAAO,IAAI;YACjB,UAAU,OAAO,QAAQ;QAC3B;QAEA,IAAI,CAAC,kBAAkB;IACzB;IAEQ,qBAAqB;QAC3B,oBAAoB;QACpB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,uLAAS,CAAC,SAAS,EAAE;YAC9B,QAAQ,GAAG,CAAC;YACZ,IAAI,CAAC,SAAS,GAAG;YACjB,IAAI,CAAC,kBAAkB;YACvB,IAAI,CAAC,qBAAqB;YAC1B,IAAI,CAAC,gBAAgB;QACvB;QAEA,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,uLAAS,CAAC,YAAY,EAAE;YACjC,QAAQ,GAAG,CAAC;YACZ,IAAI,CAAC,SAAS,GAAG;QACnB;QAEA,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,uLAAS,CAAC,KAAK,EAAE,CAAC,KAAK,MAAM;YACtC,QAAQ,KAAK,CAAC,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,EAAE;QACvC;QAEA,mBAAmB;QACnB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,uLAAS,CAAC,WAAW,EAAE,CAAC;YACjC,IAAI,CAAC,WAAW,GAAG;YACnB,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,SAAS;QAC/C;QAEA,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,uLAAS,CAAC,WAAW,EAAE,CAAC,SAAS,QAAQ,QAAQ,WAAW,cAAc,QAAQ,UAAU,eAAe,UAAU,SAAS;YACvI,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;YAC9B,IAAI,OAAO;gBACT,MAAM,MAAM,GAAG;gBACf,MAAM,MAAM,GAAG;gBACf,MAAM,SAAS,GAAG;gBAClB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS;YAC3B;QACF;QAEA,mBAAmB;QACnB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,uLAAS,CAAC,QAAQ,EAAE,CAAC,SAAS,UAAU,UAAU;YAC3D,MAAM,SAAS,SAAS,MAAM;YAC9B,MAAM,mBAAmB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW;gBACrD;gBACA,UAAU;gBACV,aAAa;gBACb,aAAa;gBACb,aAAa;gBACb,eAAe;gBACf,aAAa;YACf;YAEA,iBAAiB,QAAQ,GAAG;YAC5B,iBAAiB,WAAW,GAAG;YAC/B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ;QAC7B;QAEA,kBAAkB;QAClB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,uLAAS,CAAC,cAAc,EAAE,CAAC,OAAO,SAAS,KAAK,OAAO;YAChE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBACxB,IAAI,CAAC,cAAc,GAAG;oBACpB,gBAAgB;oBAChB,gBAAgB;oBAChB,oBAAoB;oBACpB,gBAAgB;oBAChB,aAAa;oBACb,eAAe;oBACf,aAAa;gBACf;YACF;YAEA,OAAQ;gBACN,KAAK;oBACH,IAAI,CAAC,cAAc,CAAC,cAAc,GAAG,WAAW;oBAChD;gBACF,KAAK;oBACH,IAAI,CAAC,cAAc,CAAC,cAAc,GAAG,WAAW;oBAChD;gBACF,KAAK;oBACH,IAAI,CAAC,cAAc,CAAC,kBAAkB,GAAG,WAAW;oBACpD;gBACF,KAAK;oBACH,IAAI,CAAC,cAAc,CAAC,cAAc,GAAG,WAAW;oBAChD;gBACF,KAAK;oBACH,IAAI,CAAC,cAAc,CAAC,WAAW,GAAG,WAAW;oBAC7C;gBACF,KAAK;oBACH,IAAI,CAAC,cAAc,CAAC,aAAa,GAAG,WAAW;oBAC/C;gBACF,KAAK;oBACH,IAAI,CAAC,cAAc,CAAC,WAAW,GAAG,WAAW;oBAC7C;YACJ;QACF;IACF;IAEA,MAAM,UAAyB;QAC7B,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,IAAI,IAAI,CAAC,SAAS,EAAE;gBAClB;gBACA;YACF;YAEA,MAAM,UAAU,WAAW;gBACzB,OAAO,IAAI,MAAM;YACnB,GAAG;YAEH,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,uLAAS,CAAC,SAAS,EAAE;gBAChC,aAAa;gBACb;YACF;YAEA,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,uLAAS,CAAC,KAAK,EAAE,CAAC;gBAC7B,aAAa;gBACb,OAAO;YACT;YAEA,IAAI,CAAC,EAAE,CAAC,OAAO;QACjB;IACF;IAEA,aAAmB;QACjB,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,EAAE,CAAC,UAAU;QACpB;IACF;IAEQ,qBAA2B;QACjC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC;IACjB;IAEQ,wBAA8B;QACpC,IAAI,CAAC,EAAE,CAAC,iBAAiB,CAAC,GAAG,OAAO;IACtC;IAEQ,mBAAyB;QAC/B,IAAI,CAAC,EAAE,CAAC,YAAY;IACtB;IAEA,0BAA0B;IAClB,oBAAoB,MAAc,EAAY;QACpD,OAAO;YACL,QAAQ,OAAO,WAAW;YAC1B,SAAS;YACT,UAAU;YACV,UAAU;QACZ;IACF;IAEA,uBAAuB;IACvB,MAAM,iBAAiB,MAAc,EAAE,MAAsB,EAAE,QAAgB,EAAmB;QAChG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,IAAI,CAAC,mBAAmB,CAAC;QAC1C,MAAM,QAAe;YACnB,SAAS,IAAI,CAAC,WAAW;YACzB;YACA,eAAe;YACf,WAAW;QACb;QAEA,2BAA2B;QAC3B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE;YAChC,SAAS,IAAI,CAAC,WAAW;YACzB,QAAQ,OAAO,WAAW;YAC1B;YACA;YACA,WAAW;YACX,QAAQ;YACR,QAAQ;YACR,WAAW;QACb;QAEA,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU;QAC/C,MAAM,UAAU,IAAI,CAAC,WAAW;QAChC,IAAI,CAAC,WAAW;QAEhB,OAAO;IACT;IAEA,sBAAsB;IACtB,MAAM,gBAAgB,MAAc,EAAE,MAAsB,EAAE,QAAgB,EAAE,KAAa,EAAmB;QAC9G,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,IAAI,CAAC,mBAAmB,CAAC;QAC1C,MAAM,QAAe;YACnB,SAAS,IAAI,CAAC,WAAW;YACzB;YACA,eAAe;YACf,WAAW;YACX,UAAU;QACZ;QAEA,2BAA2B;QAC3B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE;YAChC,SAAS,IAAI,CAAC,WAAW;YACzB,QAAQ,OAAO,WAAW;YAC1B;YACA;YACA,WAAW;YACX;YACA,QAAQ;YACR,QAAQ;YACR,WAAW;QACb;QAEA,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU;QAC/C,MAAM,UAAU,IAAI,CAAC,WAAW;QAChC,IAAI,CAAC,WAAW;QAEhB,OAAO;IACT;IAEA,0BAA0B;IAC1B,MAAM,eAAe,MAAc,EAAE,MAAsB,EAAE,QAAgB,EAAE,SAAiB,EAAmB;QACjH,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,IAAI,CAAC,mBAAmB,CAAC;QAC1C,MAAM,QAAe;YACnB,SAAS,IAAI,CAAC,WAAW;YACzB;YACA,eAAe;YACf,WAAW;YACX,UAAU;QACZ;QAEA,2BAA2B;QAC3B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE;YAChC,SAAS,IAAI,CAAC,WAAW;YACzB,QAAQ,OAAO,WAAW;YAC1B;YACA;YACA,WAAW;YACX,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,WAAW;QACb;QAEA,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU;QAC/C,MAAM,UAAU,IAAI,CAAC,WAAW;QAChC,IAAI,CAAC,WAAW;QAEhB,OAAO;IACT;IAEA,kBAAkB;IAClB,MAAM,YAAY,OAAe,EAAiB;QAChD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC;IACtB;IAEA,sBAAsB;IACtB,oBAA+C;QAC7C,OAAO,IAAI,CAAC,cAAc;IAC5B;IAEA,oBAAoB;IACpB,eAA+B;QAC7B,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM;IACzC;IAEA,mCAAmC;IACnC,YAAY,MAAc,EAAuB;QAC/C,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,WAAW,OAAO;IACrD;IAEA,iBAAiB;IACjB,YAAyB;QACvB,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;IACtC;IAEA,qBAAqB;IACrB,SAAS,OAAe,EAAoB;QAC1C,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY;IACrC;IAEA,qBAAqB;IACrB,cAAuB;QACrB,OAAO,IAAI,CAAC,SAAS;IACvB;AACF", "debugId": null}}, {"offset": {"line": 622, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/lib/tradingBroker.ts"], "sourcesContent": ["import { AlpacaAPI } from './alpaca';\nimport { IBKRAPI, IBKRConfig } from './ibkr';\n\nexport type BrokerType = 'alpaca' | 'ibkr';\n\nexport interface UnifiedPosition {\n  symbol: string;\n  quantity: number;\n  marketValue: number;\n  averagePrice: number;\n  unrealizedPL: number;\n  side: 'long' | 'short';\n}\n\nexport interface UnifiedOrder {\n  id: string;\n  symbol: string;\n  side: 'buy' | 'sell';\n  quantity: number;\n  orderType: 'market' | 'limit' | 'stop' | 'stop_limit';\n  price?: number;\n  stopPrice?: number;\n  status: string;\n  filledQuantity: number;\n  remainingQuantity: number;\n  broker: BrokerType;\n}\n\nexport interface UnifiedAccount {\n  totalValue: number;\n  cashValue: number;\n  buyingPower: number;\n  unrealizedPL: number;\n  realizedPL: number;\n  positions: UnifiedPosition[];\n}\n\nexport interface TradeRequest {\n  symbol: string;\n  side: 'buy' | 'sell';\n  quantity: number;\n  orderType: 'market' | 'limit' | 'stop' | 'stop_limit';\n  price?: number;\n  stopPrice?: number;\n  timeInForce?: 'day' | 'gtc' | 'ioc' | 'fok';\n}\n\nexport class UnifiedTradingBroker {\n  private alpacaAPI: AlpacaAPI | null = null;\n  private ibkrAPI: IBKRAPI | null = null;\n  private activeBroker: BrokerType;\n\n  constructor(brokerType: BrokerType = 'alpaca') {\n    this.activeBroker = brokerType;\n  }\n\n  // Initialize Alpaca\n  async initializeAlpaca(apiKey: string, secretKey: string, baseUrl: string): Promise<void> {\n    this.alpacaAPI = new AlpacaAPI(apiKey, secretKey, baseUrl);\n    if (this.activeBroker === 'alpaca') {\n      console.log('✅ Alpaca API initialized');\n    }\n  }\n\n  // Initialize IBKR\n  async initializeIBKR(config: IBKRConfig): Promise<void> {\n    this.ibkrAPI = new IBKRAPI(config);\n    if (this.activeBroker === 'ibkr') {\n      await this.ibkrAPI.connect();\n      console.log('✅ IBKR API initialized and connected');\n    }\n  }\n\n  // Switch active broker\n  async switchBroker(brokerType: BrokerType): Promise<void> {\n    this.activeBroker = brokerType;\n    \n    if (brokerType === 'ibkr' && this.ibkrAPI && !this.ibkrAPI.isConnected()) {\n      await this.ibkrAPI.connect();\n    }\n    \n    console.log(`✅ Switched to ${brokerType.toUpperCase()} broker`);\n  }\n\n  // Get active broker\n  getActiveBroker(): BrokerType {\n    return this.activeBroker;\n  }\n\n  // Place a trade\n  async placeTrade(request: TradeRequest): Promise<UnifiedOrder> {\n    if (this.activeBroker === 'alpaca') {\n      return this.placeAlpacaTrade(request);\n    } else if (this.activeBroker === 'ibkr') {\n      return this.placeIBKRTrade(request);\n    } else {\n      throw new Error('No active broker configured');\n    }\n  }\n\n  private async placeAlpacaTrade(request: TradeRequest): Promise<UnifiedOrder> {\n    if (!this.alpacaAPI) {\n      throw new Error('Alpaca API not initialized');\n    }\n\n    let alpacaOrder;\n    \n    switch (request.orderType) {\n      case 'market':\n        alpacaOrder = await this.alpacaAPI.placeMarketOrder(\n          request.symbol,\n          request.side,\n          request.quantity\n        );\n        break;\n      case 'limit':\n        if (!request.price) throw new Error('Limit price required for limit order');\n        alpacaOrder = await this.alpacaAPI.placeLimitOrder(\n          request.symbol,\n          request.side,\n          request.quantity,\n          request.price\n        );\n        break;\n      case 'stop':\n        if (!request.stopPrice) throw new Error('Stop price required for stop order');\n        alpacaOrder = await this.alpacaAPI.placeStopOrder(\n          request.symbol,\n          request.side,\n          request.quantity,\n          request.stopPrice\n        );\n        break;\n      default:\n        throw new Error(`Order type ${request.orderType} not supported for Alpaca`);\n    }\n\n    return {\n      id: alpacaOrder.id,\n      symbol: request.symbol,\n      side: request.side,\n      quantity: request.quantity,\n      orderType: request.orderType,\n      price: request.price,\n      stopPrice: request.stopPrice,\n      status: alpacaOrder.status,\n      filledQuantity: parseFloat(alpacaOrder.filled_qty || '0'),\n      remainingQuantity: request.quantity - parseFloat(alpacaOrder.filled_qty || '0'),\n      broker: 'alpaca'\n    };\n  }\n\n  private async placeIBKRTrade(request: TradeRequest): Promise<UnifiedOrder> {\n    if (!this.ibkrAPI) {\n      throw new Error('IBKR API not initialized');\n    }\n\n    let orderId: number;\n    const action = request.side.toUpperCase() as 'BUY' | 'SELL';\n\n    switch (request.orderType) {\n      case 'market':\n        orderId = await this.ibkrAPI.placeMarketOrder(request.symbol, action, request.quantity);\n        break;\n      case 'limit':\n        if (!request.price) throw new Error('Limit price required for limit order');\n        orderId = await this.ibkrAPI.placeLimitOrder(request.symbol, action, request.quantity, request.price);\n        break;\n      case 'stop':\n        if (!request.stopPrice) throw new Error('Stop price required for stop order');\n        orderId = await this.ibkrAPI.placeStopOrder(request.symbol, action, request.quantity, request.stopPrice);\n        break;\n      default:\n        throw new Error(`Order type ${request.orderType} not supported for IBKR`);\n    }\n\n    return {\n      id: orderId.toString(),\n      symbol: request.symbol,\n      side: request.side,\n      quantity: request.quantity,\n      orderType: request.orderType,\n      price: request.price,\n      stopPrice: request.stopPrice,\n      status: 'Submitted',\n      filledQuantity: 0,\n      remainingQuantity: request.quantity,\n      broker: 'ibkr'\n    };\n  }\n\n  // Cancel an order\n  async cancelOrder(orderId: string): Promise<void> {\n    if (this.activeBroker === 'alpaca') {\n      if (!this.alpacaAPI) throw new Error('Alpaca API not initialized');\n      await this.alpacaAPI.cancelOrder(orderId);\n    } else if (this.activeBroker === 'ibkr') {\n      if (!this.ibkrAPI) throw new Error('IBKR API not initialized');\n      await this.ibkrAPI.cancelOrder(parseInt(orderId));\n    }\n  }\n\n  // Get account information\n  async getAccount(): Promise<UnifiedAccount> {\n    if (this.activeBroker === 'alpaca') {\n      return this.getAlpacaAccount();\n    } else if (this.activeBroker === 'ibkr') {\n      return this.getIBKRAccount();\n    } else {\n      throw new Error('No active broker configured');\n    }\n  }\n\n  private async getAlpacaAccount(): Promise<UnifiedAccount> {\n    if (!this.alpacaAPI) throw new Error('Alpaca API not initialized');\n\n    const account = await this.alpacaAPI.getAccount();\n    const positions = await this.alpacaAPI.getPositions();\n\n    return {\n      totalValue: parseFloat(account.portfolio_value),\n      cashValue: parseFloat(account.cash),\n      buyingPower: parseFloat(account.buying_power),\n      unrealizedPL: parseFloat(account.unrealized_pl || '0'),\n      realizedPL: 0, // Alpaca doesn't provide this directly\n      positions: positions.map(pos => ({\n        symbol: pos.symbol,\n        quantity: Math.abs(parseFloat(pos.qty)),\n        marketValue: parseFloat(pos.market_value || '0'),\n        averagePrice: parseFloat(pos.avg_entry_price || '0'),\n        unrealizedPL: parseFloat(pos.unrealized_pl || '0'),\n        side: parseFloat(pos.qty) > 0 ? 'long' : 'short'\n      }))\n    };\n  }\n\n  private async getIBKRAccount(): Promise<UnifiedAccount> {\n    if (!this.ibkrAPI) throw new Error('IBKR API not initialized');\n\n    const accountSummary = this.ibkrAPI.getAccountSummary();\n    const positions = this.ibkrAPI.getPositions();\n\n    if (!accountSummary) {\n      throw new Error('IBKR account summary not available');\n    }\n\n    return {\n      totalValue: accountSummary.netLiquidation,\n      cashValue: accountSummary.totalCashValue,\n      buyingPower: accountSummary.buyingPower,\n      unrealizedPL: accountSummary.unrealizedPnL,\n      realizedPL: accountSummary.realizedPnL,\n      positions: positions.map(pos => ({\n        symbol: pos.symbol,\n        quantity: Math.abs(pos.position),\n        marketValue: pos.marketValue,\n        averagePrice: pos.averageCost,\n        unrealizedPL: pos.unrealizedPNL,\n        side: pos.position > 0 ? 'long' : 'short'\n      }))\n    };\n  }\n\n  // Get all orders\n  async getOrders(): Promise<UnifiedOrder[]> {\n    if (this.activeBroker === 'alpaca') {\n      if (!this.alpacaAPI) throw new Error('Alpaca API not initialized');\n      const orders = await this.alpacaAPI.getOrders();\n      return orders.map(order => ({\n        id: order.id,\n        symbol: order.symbol,\n        side: order.side as 'buy' | 'sell',\n        quantity: parseFloat(order.qty),\n        orderType: order.order_type as any,\n        price: order.limit_price ? parseFloat(order.limit_price) : undefined,\n        stopPrice: order.stop_price ? parseFloat(order.stop_price) : undefined,\n        status: order.status,\n        filledQuantity: parseFloat(order.filled_qty || '0'),\n        remainingQuantity: parseFloat(order.qty) - parseFloat(order.filled_qty || '0'),\n        broker: 'alpaca'\n      }));\n    } else if (this.activeBroker === 'ibkr') {\n      if (!this.ibkrAPI) throw new Error('IBKR API not initialized');\n      const orders = this.ibkrAPI.getOrders();\n      return orders.map(order => ({\n        id: order.orderId.toString(),\n        symbol: order.symbol,\n        side: order.action.toLowerCase() as 'buy' | 'sell',\n        quantity: order.quantity,\n        orderType: order.orderType.toLowerCase() as any,\n        price: order.price,\n        status: order.status,\n        filledQuantity: order.filled,\n        remainingQuantity: order.remaining,\n        broker: 'ibkr'\n      }));\n    } else {\n      throw new Error('No active broker configured');\n    }\n  }\n\n  // Get positions\n  async getPositions(): Promise<UnifiedPosition[]> {\n    const account = await this.getAccount();\n    return account.positions;\n  }\n\n  // Check if broker is connected/ready\n  isReady(): boolean {\n    if (this.activeBroker === 'alpaca') {\n      return this.alpacaAPI !== null;\n    } else if (this.activeBroker === 'ibkr') {\n      return this.ibkrAPI !== null && this.ibkrAPI.isConnected();\n    }\n    return false;\n  }\n\n  // Disconnect (mainly for IBKR)\n  disconnect(): void {\n    if (this.ibkrAPI) {\n      this.ibkrAPI.disconnect();\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AA8CO,MAAM;IACH,YAA8B,KAAK;IACnC,UAA0B,KAAK;IAC/B,aAAyB;IAEjC,YAAY,aAAyB,QAAQ,CAAE;QAC7C,IAAI,CAAC,YAAY,GAAG;IACtB;IAEA,oBAAoB;IACpB,MAAM,iBAAiB,MAAc,EAAE,SAAiB,EAAE,OAAe,EAAiB;QACxF,IAAI,CAAC,SAAS,GAAG,IAAI,4JAAS,CAAC,QAAQ,WAAW;QAClD,IAAI,IAAI,CAAC,YAAY,KAAK,UAAU;YAClC,QAAQ,GAAG,CAAC;QACd;IACF;IAEA,kBAAkB;IAClB,MAAM,eAAe,MAAkB,EAAiB;QACtD,IAAI,CAAC,OAAO,GAAG,IAAI,wJAAO,CAAC;QAC3B,IAAI,IAAI,CAAC,YAAY,KAAK,QAAQ;YAChC,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO;YAC1B,QAAQ,GAAG,CAAC;QACd;IACF;IAEA,uBAAuB;IACvB,MAAM,aAAa,UAAsB,EAAiB;QACxD,IAAI,CAAC,YAAY,GAAG;QAEpB,IAAI,eAAe,UAAU,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI;YACxE,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO;QAC5B;QAEA,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,WAAW,WAAW,GAAG,OAAO,CAAC;IAChE;IAEA,oBAAoB;IACpB,kBAA8B;QAC5B,OAAO,IAAI,CAAC,YAAY;IAC1B;IAEA,gBAAgB;IAChB,MAAM,WAAW,OAAqB,EAAyB;QAC7D,IAAI,IAAI,CAAC,YAAY,KAAK,UAAU;YAClC,OAAO,IAAI,CAAC,gBAAgB,CAAC;QAC/B,OAAO,IAAI,IAAI,CAAC,YAAY,KAAK,QAAQ;YACvC,OAAO,IAAI,CAAC,cAAc,CAAC;QAC7B,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,MAAc,iBAAiB,OAAqB,EAAyB;QAC3E,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;QAEJ,OAAQ,QAAQ,SAAS;YACvB,KAAK;gBACH,cAAc,MAAM,IAAI,CAAC,SAAS,CAAC,gBAAgB,CACjD,QAAQ,MAAM,EACd,QAAQ,IAAI,EACZ,QAAQ,QAAQ;gBAElB;YACF,KAAK;gBACH,IAAI,CAAC,QAAQ,KAAK,EAAE,MAAM,IAAI,MAAM;gBACpC,cAAc,MAAM,IAAI,CAAC,SAAS,CAAC,eAAe,CAChD,QAAQ,MAAM,EACd,QAAQ,IAAI,EACZ,QAAQ,QAAQ,EAChB,QAAQ,KAAK;gBAEf;YACF,KAAK;gBACH,IAAI,CAAC,QAAQ,SAAS,EAAE,MAAM,IAAI,MAAM;gBACxC,cAAc,MAAM,IAAI,CAAC,SAAS,CAAC,cAAc,CAC/C,QAAQ,MAAM,EACd,QAAQ,IAAI,EACZ,QAAQ,QAAQ,EAChB,QAAQ,SAAS;gBAEnB;YACF;gBACE,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,QAAQ,SAAS,CAAC,yBAAyB,CAAC;QAC9E;QAEA,OAAO;YACL,IAAI,YAAY,EAAE;YAClB,QAAQ,QAAQ,MAAM;YACtB,MAAM,QAAQ,IAAI;YAClB,UAAU,QAAQ,QAAQ;YAC1B,WAAW,QAAQ,SAAS;YAC5B,OAAO,QAAQ,KAAK;YACpB,WAAW,QAAQ,SAAS;YAC5B,QAAQ,YAAY,MAAM;YAC1B,gBAAgB,WAAW,YAAY,UAAU,IAAI;YACrD,mBAAmB,QAAQ,QAAQ,GAAG,WAAW,YAAY,UAAU,IAAI;YAC3E,QAAQ;QACV;IACF;IAEA,MAAc,eAAe,OAAqB,EAAyB;QACzE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;QACJ,MAAM,SAAS,QAAQ,IAAI,CAAC,WAAW;QAEvC,OAAQ,QAAQ,SAAS;YACvB,KAAK;gBACH,UAAU,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,MAAM,EAAE,QAAQ,QAAQ,QAAQ;gBACtF;YACF,KAAK;gBACH,IAAI,CAAC,QAAQ,KAAK,EAAE,MAAM,IAAI,MAAM;gBACpC,UAAU,MAAM,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,QAAQ,MAAM,EAAE,QAAQ,QAAQ,QAAQ,EAAE,QAAQ,KAAK;gBACpG;YACF,KAAK;gBACH,IAAI,CAAC,QAAQ,SAAS,EAAE,MAAM,IAAI,MAAM;gBACxC,UAAU,MAAM,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ,MAAM,EAAE,QAAQ,QAAQ,QAAQ,EAAE,QAAQ,SAAS;gBACvG;YACF;gBACE,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,QAAQ,SAAS,CAAC,uBAAuB,CAAC;QAC5E;QAEA,OAAO;YACL,IAAI,QAAQ,QAAQ;YACpB,QAAQ,QAAQ,MAAM;YACtB,MAAM,QAAQ,IAAI;YAClB,UAAU,QAAQ,QAAQ;YAC1B,WAAW,QAAQ,SAAS;YAC5B,OAAO,QAAQ,KAAK;YACpB,WAAW,QAAQ,SAAS;YAC5B,QAAQ;YACR,gBAAgB;YAChB,mBAAmB,QAAQ,QAAQ;YACnC,QAAQ;QACV;IACF;IAEA,kBAAkB;IAClB,MAAM,YAAY,OAAe,EAAiB;QAChD,IAAI,IAAI,CAAC,YAAY,KAAK,UAAU;YAClC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,IAAI,MAAM;YACrC,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;QACnC,OAAO,IAAI,IAAI,CAAC,YAAY,KAAK,QAAQ;YACvC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,IAAI,MAAM;YACnC,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,SAAS;QAC1C;IACF;IAEA,0BAA0B;IAC1B,MAAM,aAAsC;QAC1C,IAAI,IAAI,CAAC,YAAY,KAAK,UAAU;YAClC,OAAO,IAAI,CAAC,gBAAgB;QAC9B,OAAO,IAAI,IAAI,CAAC,YAAY,KAAK,QAAQ;YACvC,OAAO,IAAI,CAAC,cAAc;QAC5B,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,MAAc,mBAA4C;QACxD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,IAAI,MAAM;QAErC,MAAM,UAAU,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU;QAC/C,MAAM,YAAY,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY;QAEnD,OAAO;YACL,YAAY,WAAW,QAAQ,eAAe;YAC9C,WAAW,WAAW,QAAQ,IAAI;YAClC,aAAa,WAAW,QAAQ,YAAY;YAC5C,cAAc,WAAW,QAAQ,aAAa,IAAI;YAClD,YAAY;YACZ,WAAW,UAAU,GAAG,CAAC,CAAA,MAAO,CAAC;oBAC/B,QAAQ,IAAI,MAAM;oBAClB,UAAU,KAAK,GAAG,CAAC,WAAW,IAAI,GAAG;oBACrC,aAAa,WAAW,IAAI,YAAY,IAAI;oBAC5C,cAAc,WAAW,IAAI,eAAe,IAAI;oBAChD,cAAc,WAAW,IAAI,aAAa,IAAI;oBAC9C,MAAM,WAAW,IAAI,GAAG,IAAI,IAAI,SAAS;gBAC3C,CAAC;QACH;IACF;IAEA,MAAc,iBAA0C;QACtD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,IAAI,MAAM;QAEnC,MAAM,iBAAiB,IAAI,CAAC,OAAO,CAAC,iBAAiB;QACrD,MAAM,YAAY,IAAI,CAAC,OAAO,CAAC,YAAY;QAE3C,IAAI,CAAC,gBAAgB;YACnB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;YACL,YAAY,eAAe,cAAc;YACzC,WAAW,eAAe,cAAc;YACxC,aAAa,eAAe,WAAW;YACvC,cAAc,eAAe,aAAa;YAC1C,YAAY,eAAe,WAAW;YACtC,WAAW,UAAU,GAAG,CAAC,CAAA,MAAO,CAAC;oBAC/B,QAAQ,IAAI,MAAM;oBAClB,UAAU,KAAK,GAAG,CAAC,IAAI,QAAQ;oBAC/B,aAAa,IAAI,WAAW;oBAC5B,cAAc,IAAI,WAAW;oBAC7B,cAAc,IAAI,aAAa;oBAC/B,MAAM,IAAI,QAAQ,GAAG,IAAI,SAAS;gBACpC,CAAC;QACH;IACF;IAEA,iBAAiB;IACjB,MAAM,YAAqC;QACzC,IAAI,IAAI,CAAC,YAAY,KAAK,UAAU;YAClC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,IAAI,MAAM;YACrC,MAAM,SAAS,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS;YAC7C,OAAO,OAAO,GAAG,CAAC,CAAA,QAAS,CAAC;oBAC1B,IAAI,MAAM,EAAE;oBACZ,QAAQ,MAAM,MAAM;oBACpB,MAAM,MAAM,IAAI;oBAChB,UAAU,WAAW,MAAM,GAAG;oBAC9B,WAAW,MAAM,UAAU;oBAC3B,OAAO,MAAM,WAAW,GAAG,WAAW,MAAM,WAAW,IAAI;oBAC3D,WAAW,MAAM,UAAU,GAAG,WAAW,MAAM,UAAU,IAAI;oBAC7D,QAAQ,MAAM,MAAM;oBACpB,gBAAgB,WAAW,MAAM,UAAU,IAAI;oBAC/C,mBAAmB,WAAW,MAAM,GAAG,IAAI,WAAW,MAAM,UAAU,IAAI;oBAC1E,QAAQ;gBACV,CAAC;QACH,OAAO,IAAI,IAAI,CAAC,YAAY,KAAK,QAAQ;YACvC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,IAAI,MAAM;YACnC,MAAM,SAAS,IAAI,CAAC,OAAO,CAAC,SAAS;YACrC,OAAO,OAAO,GAAG,CAAC,CAAA,QAAS,CAAC;oBAC1B,IAAI,MAAM,OAAO,CAAC,QAAQ;oBAC1B,QAAQ,MAAM,MAAM;oBACpB,MAAM,MAAM,MAAM,CAAC,WAAW;oBAC9B,UAAU,MAAM,QAAQ;oBACxB,WAAW,MAAM,SAAS,CAAC,WAAW;oBACtC,OAAO,MAAM,KAAK;oBAClB,QAAQ,MAAM,MAAM;oBACpB,gBAAgB,MAAM,MAAM;oBAC5B,mBAAmB,MAAM,SAAS;oBAClC,QAAQ;gBACV,CAAC;QACH,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,gBAAgB;IAChB,MAAM,eAA2C;QAC/C,MAAM,UAAU,MAAM,IAAI,CAAC,UAAU;QACrC,OAAO,QAAQ,SAAS;IAC1B;IAEA,qCAAqC;IACrC,UAAmB;QACjB,IAAI,IAAI,CAAC,YAAY,KAAK,UAAU;YAClC,OAAO,IAAI,CAAC,SAAS,KAAK;QAC5B,OAAO,IAAI,IAAI,CAAC,YAAY,KAAK,QAAQ;YACvC,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ,IAAI,CAAC,OAAO,CAAC,WAAW;QAC1D;QACA,OAAO;IACT;IAEA,+BAA+B;IAC/B,aAAmB;QACjB,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,OAAO,CAAC,UAAU;QACzB;IACF;AACF", "debugId": null}}, {"offset": {"line": 868, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/app/api/trading/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { UnifiedTradingBroker, TradeRequest } from '@/lib/tradingBroker';\n\n// Global broker instance (in production, you'd want proper session management)\nlet tradingBroker: UnifiedTradingBroker | null = null;\n\n// Initialize the trading broker\nasync function initializeBroker() {\n  if (!tradingBroker) {\n    tradingBroker = new UnifiedTradingBroker('alpaca'); // Default to Alpaca\n\n    // Initialize Alpaca if credentials are available\n    const alpacaKey = process.env.ALPACA_API_KEY;\n    const alpacaSecret = process.env.ALPACA_SECRET_KEY;\n    const alpacaBaseUrl = process.env.ALPACA_BASE_URL || 'https://paper-api.alpaca.markets';\n\n    if (alpacaKey && alpacaSecret) {\n      await tradingBroker.initializeAlpaca(alpacaKey, alpacaSecret, alpacaBaseUrl);\n    }\n\n    // Initialize IBKR if credentials are available\n    const ibkrHost = process.env.IBKR_HOST || 'localhost';\n    const ibkrPort = parseInt(process.env.IBKR_PORT || '7497'); // Paper trading port\n    const ibkrClientId = parseInt(process.env.IBKR_CLIENT_ID || '1');\n\n    try {\n      await tradingBroker.initializeIBKR({\n        host: ibkrHost,\n        port: ibkrPort,\n        clientId: ibkrClientId,\n        paperTrading: true\n      });\n    } catch (error) {\n      console.warn('IBKR initialization failed (this is normal if TWS/Gateway is not running):', error);\n    }\n  }\n  return tradingBroker;\n}\n\n// GET /api/trading - Get account info, positions, and orders\nexport async function GET(request: NextRequest) {\n  try {\n    const broker = await initializeBroker();\n    const { searchParams } = new URL(request.url);\n    const action = searchParams.get('action');\n\n    switch (action) {\n      case 'account':\n        const account = await broker.getAccount();\n        return NextResponse.json({\n          success: true,\n          data: account,\n          broker: broker.getActiveBroker()\n        });\n\n      case 'positions':\n        const positions = await broker.getPositions();\n        return NextResponse.json({\n          success: true,\n          data: positions,\n          broker: broker.getActiveBroker()\n        });\n\n      case 'orders':\n        const orders = await broker.getOrders();\n        return NextResponse.json({\n          success: true,\n          data: orders,\n          broker: broker.getActiveBroker()\n        });\n\n      case 'status':\n        return NextResponse.json({\n          success: true,\n          data: {\n            activeBroker: broker.getActiveBroker(),\n            isReady: broker.isReady(),\n            brokers: {\n              alpaca: process.env.ALPACA_API_KEY ? 'configured' : 'not configured',\n              ibkr: process.env.IBKR_HOST ? 'configured' : 'not configured'\n            }\n          }\n        });\n\n      default:\n        return NextResponse.json({\n          success: false,\n          error: 'Invalid action. Use: account, positions, orders, or status'\n        }, { status: 400 });\n    }\n  } catch (error) {\n    console.error('Trading API error:', error);\n    return NextResponse.json({\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error'\n    }, { status: 500 });\n  }\n}\n\n// POST /api/trading - Place trades, switch brokers, cancel orders\nexport async function POST(request: NextRequest) {\n  try {\n    const broker = await initializeBroker();\n    const body = await request.json();\n    const { action } = body;\n\n    switch (action) {\n      case 'place_order':\n        const tradeRequest: TradeRequest = {\n          symbol: body.symbol,\n          side: body.side,\n          quantity: body.quantity,\n          orderType: body.orderType || 'market',\n          price: body.price,\n          stopPrice: body.stopPrice,\n          timeInForce: body.timeInForce || 'day'\n        };\n\n        const order = await broker.placeTrade(tradeRequest);\n        return NextResponse.json({\n          success: true,\n          data: order,\n          message: `${tradeRequest.side.toUpperCase()} order placed for ${tradeRequest.quantity} shares of ${tradeRequest.symbol}`\n        });\n\n      case 'cancel_order':\n        if (!body.orderId) {\n          return NextResponse.json({\n            success: false,\n            error: 'Order ID is required'\n          }, { status: 400 });\n        }\n\n        await broker.cancelOrder(body.orderId);\n        return NextResponse.json({\n          success: true,\n          message: `Order ${body.orderId} cancelled`\n        });\n\n      case 'switch_broker':\n        if (!body.broker || !['alpaca', 'ibkr'].includes(body.broker)) {\n          return NextResponse.json({\n            success: false,\n            error: 'Valid broker required (alpaca or ibkr)'\n          }, { status: 400 });\n        }\n\n        await broker.switchBroker(body.broker);\n        return NextResponse.json({\n          success: true,\n          data: {\n            activeBroker: broker.getActiveBroker(),\n            isReady: broker.isReady()\n          },\n          message: `Switched to ${body.broker.toUpperCase()}`\n        });\n\n      case 'execute_swing_trade':\n        // Execute a complete swing trade setup\n        const {\n          symbol,\n          strategy,\n          accountSize,\n          riskPercentage = 1.0,\n          entryPrice,\n          stopLoss,\n          takeProfit,\n          quantity: customQuantity,\n          orderType = 'limit'\n        } = body;\n\n        if (!symbol || !entryPrice || !stopLoss) {\n          return NextResponse.json({\n            success: false,\n            error: 'Symbol, entry price, and stop loss are required'\n          }, { status: 400 });\n        }\n\n        // Calculate position size based on risk or use custom quantity\n        let finalQuantity = customQuantity;\n        let riskAmount = 0;\n        let riskPerShare = 0;\n\n        if (!customQuantity) {\n          riskAmount = (accountSize || 100000) * (riskPercentage / 100);\n          riskPerShare = Math.abs(entryPrice - stopLoss);\n          finalQuantity = Math.floor(riskAmount / riskPerShare);\n        } else {\n          riskPerShare = Math.abs(entryPrice - stopLoss);\n          riskAmount = finalQuantity * riskPerShare;\n        }\n\n        if (finalQuantity <= 0) {\n          return NextResponse.json({\n            success: false,\n            error: 'Calculated position size is too small'\n          }, { status: 400 });\n        }\n\n        // Place the entry order\n        const entryOrderRequest: TradeRequest = {\n          symbol,\n          side: 'buy',\n          quantity: finalQuantity,\n          orderType: orderType as 'market' | 'limit',\n          price: orderType === 'limit' ? entryPrice : undefined\n        };\n\n        const entryOrder = await broker.placeTrade(entryOrderRequest);\n\n        // Prepare stop loss and take profit orders (bracket order simulation)\n        const stopLossOrder = {\n          symbol,\n          side: 'sell' as const,\n          quantity: finalQuantity,\n          orderType: 'stop' as const,\n          stopPrice: stopLoss\n        };\n\n        const takeProfitOrder = takeProfit ? {\n          symbol,\n          side: 'sell' as const,\n          quantity: finalQuantity,\n          orderType: 'limit' as const,\n          price: takeProfit\n        } : null;\n\n        return NextResponse.json({\n          success: true,\n          data: {\n            orderId: entryOrder.id || entryOrder.orderId,\n            entryOrder,\n            strategy,\n            positionSize: finalQuantity,\n            riskAmount,\n            riskPerShare,\n            stopLoss,\n            takeProfit,\n            orderType,\n            stopLossOrder,\n            takeProfitOrder,\n            executionTime: new Date().toISOString()\n          },\n          message: `Swing trade executed: ${orderType.toUpperCase()} order for ${finalQuantity} shares of ${symbol} at $${entryPrice}`\n        });\n\n      default:\n        return NextResponse.json({\n          success: false,\n          error: 'Invalid action. Use: place_order, cancel_order, switch_broker, or execute_swing_trade'\n        }, { status: 400 });\n    }\n  } catch (error) {\n    console.error('Trading API error:', error);\n    return NextResponse.json({\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEA,+EAA+E;AAC/E,IAAI,gBAA6C;AAEjD,gCAAgC;AAChC,eAAe;IACb,IAAI,CAAC,eAAe;QAClB,gBAAgB,IAAI,8KAAoB,CAAC,WAAW,oBAAoB;QAExE,iDAAiD;QACjD,MAAM,YAAY,QAAQ,GAAG,CAAC,cAAc;QAC5C,MAAM,eAAe,QAAQ,GAAG,CAAC,iBAAiB;QAClD,MAAM,gBAAgB,QAAQ,GAAG,CAAC,eAAe,IAAI;QAErD,IAAI,aAAa,cAAc;YAC7B,MAAM,cAAc,gBAAgB,CAAC,WAAW,cAAc;QAChE;QAEA,+CAA+C;QAC/C,MAAM,WAAW,QAAQ,GAAG,CAAC,SAAS,IAAI;QAC1C,MAAM,WAAW,SAAS,QAAQ,GAAG,CAAC,SAAS,IAAI,SAAS,qBAAqB;QACjF,MAAM,eAAe,SAAS,QAAQ,GAAG,CAAC,cAAc,IAAI;QAE5D,IAAI;YACF,MAAM,cAAc,cAAc,CAAC;gBACjC,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,cAAc;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,8EAA8E;QAC7F;IACF;IACA,OAAO;AACT;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,SAAS,MAAM;QACrB,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC;QAEhC,OAAQ;YACN,KAAK;gBACH,MAAM,UAAU,MAAM,OAAO,UAAU;gBACvC,OAAO,yKAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;oBACN,QAAQ,OAAO,eAAe;gBAChC;YAEF,KAAK;gBACH,MAAM,YAAY,MAAM,OAAO,YAAY;gBAC3C,OAAO,yKAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;oBACN,QAAQ,OAAO,eAAe;gBAChC;YAEF,KAAK;gBACH,MAAM,SAAS,MAAM,OAAO,SAAS;gBACrC,OAAO,yKAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;oBACN,QAAQ,OAAO,eAAe;gBAChC;YAEF,KAAK;gBACH,OAAO,yKAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;wBACJ,cAAc,OAAO,eAAe;wBACpC,SAAS,OAAO,OAAO;wBACvB,SAAS;4BACP,QAAQ,QAAQ,GAAG,CAAC,cAAc,GAAG,eAAe;4BACpD,MAAM,QAAQ,GAAG,CAAC,SAAS,GAAG,eAAe;wBAC/C;oBACF;gBACF;YAEF;gBACE,OAAO,yKAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;QACrB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,yKAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,SAAS,MAAM;QACrB,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,MAAM,EAAE,GAAG;QAEnB,OAAQ;YACN,KAAK;gBACH,MAAM,eAA6B;oBACjC,QAAQ,KAAK,MAAM;oBACnB,MAAM,KAAK,IAAI;oBACf,UAAU,KAAK,QAAQ;oBACvB,WAAW,KAAK,SAAS,IAAI;oBAC7B,OAAO,KAAK,KAAK;oBACjB,WAAW,KAAK,SAAS;oBACzB,aAAa,KAAK,WAAW,IAAI;gBACnC;gBAEA,MAAM,QAAQ,MAAM,OAAO,UAAU,CAAC;gBACtC,OAAO,yKAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;oBACN,SAAS,GAAG,aAAa,IAAI,CAAC,WAAW,GAAG,kBAAkB,EAAE,aAAa,QAAQ,CAAC,WAAW,EAAE,aAAa,MAAM,EAAE;gBAC1H;YAEF,KAAK;gBACH,IAAI,CAAC,KAAK,OAAO,EAAE;oBACjB,OAAO,yKAAY,CAAC,IAAI,CAAC;wBACvB,SAAS;wBACT,OAAO;oBACT,GAAG;wBAAE,QAAQ;oBAAI;gBACnB;gBAEA,MAAM,OAAO,WAAW,CAAC,KAAK,OAAO;gBACrC,OAAO,yKAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,SAAS,CAAC,MAAM,EAAE,KAAK,OAAO,CAAC,UAAU,CAAC;gBAC5C;YAEF,KAAK;gBACH,IAAI,CAAC,KAAK,MAAM,IAAI,CAAC;oBAAC;oBAAU;iBAAO,CAAC,QAAQ,CAAC,KAAK,MAAM,GAAG;oBAC7D,OAAO,yKAAY,CAAC,IAAI,CAAC;wBACvB,SAAS;wBACT,OAAO;oBACT,GAAG;wBAAE,QAAQ;oBAAI;gBACnB;gBAEA,MAAM,OAAO,YAAY,CAAC,KAAK,MAAM;gBACrC,OAAO,yKAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;wBACJ,cAAc,OAAO,eAAe;wBACpC,SAAS,OAAO,OAAO;oBACzB;oBACA,SAAS,CAAC,YAAY,EAAE,KAAK,MAAM,CAAC,WAAW,IAAI;gBACrD;YAEF,KAAK;gBACH,uCAAuC;gBACvC,MAAM,EACJ,MAAM,EACN,QAAQ,EACR,WAAW,EACX,iBAAiB,GAAG,EACpB,UAAU,EACV,QAAQ,EACR,UAAU,EACV,UAAU,cAAc,EACxB,YAAY,OAAO,EACpB,GAAG;gBAEJ,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,UAAU;oBACvC,OAAO,yKAAY,CAAC,IAAI,CAAC;wBACvB,SAAS;wBACT,OAAO;oBACT,GAAG;wBAAE,QAAQ;oBAAI;gBACnB;gBAEA,+DAA+D;gBAC/D,IAAI,gBAAgB;gBACpB,IAAI,aAAa;gBACjB,IAAI,eAAe;gBAEnB,IAAI,CAAC,gBAAgB;oBACnB,aAAa,CAAC,eAAe,MAAM,IAAI,CAAC,iBAAiB,GAAG;oBAC5D,eAAe,KAAK,GAAG,CAAC,aAAa;oBACrC,gBAAgB,KAAK,KAAK,CAAC,aAAa;gBAC1C,OAAO;oBACL,eAAe,KAAK,GAAG,CAAC,aAAa;oBACrC,aAAa,gBAAgB;gBAC/B;gBAEA,IAAI,iBAAiB,GAAG;oBACtB,OAAO,yKAAY,CAAC,IAAI,CAAC;wBACvB,SAAS;wBACT,OAAO;oBACT,GAAG;wBAAE,QAAQ;oBAAI;gBACnB;gBAEA,wBAAwB;gBACxB,MAAM,oBAAkC;oBACtC;oBACA,MAAM;oBACN,UAAU;oBACV,WAAW;oBACX,OAAO,cAAc,UAAU,aAAa;gBAC9C;gBAEA,MAAM,aAAa,MAAM,OAAO,UAAU,CAAC;gBAE3C,sEAAsE;gBACtE,MAAM,gBAAgB;oBACpB;oBACA,MAAM;oBACN,UAAU;oBACV,WAAW;oBACX,WAAW;gBACb;gBAEA,MAAM,kBAAkB,aAAa;oBACnC;oBACA,MAAM;oBACN,UAAU;oBACV,WAAW;oBACX,OAAO;gBACT,IAAI;gBAEJ,OAAO,yKAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;wBACJ,SAAS,WAAW,EAAE,IAAI,WAAW,OAAO;wBAC5C;wBACA;wBACA,cAAc;wBACd;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA,eAAe,IAAI,OAAO,WAAW;oBACvC;oBACA,SAAS,CAAC,sBAAsB,EAAE,UAAU,WAAW,GAAG,WAAW,EAAE,cAAc,WAAW,EAAE,OAAO,KAAK,EAAE,YAAY;gBAC9H;YAEF;gBACE,OAAO,yKAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;QACrB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,yKAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}