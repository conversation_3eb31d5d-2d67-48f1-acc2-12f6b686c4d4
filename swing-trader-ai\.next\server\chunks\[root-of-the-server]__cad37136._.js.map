{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/lib/polygon.ts"], "sourcesContent": ["import axios from 'axios'\nimport { CandlestickData, StockData } from '@/types/trading'\n\nconst POLYGON_BASE_URL = 'https://api.polygon.io'\nconst API_KEY = process.env.POLYGON_API_KEY\n\nexport class PolygonAPI {\n  private apiKey: string\n\n  constructor(apiKey?: string) {\n    this.apiKey = apiKey || API_KEY || ''\n    if (!this.apiKey) {\n      throw new Error('Polygon API key is required')\n    }\n  }\n\n  // Get current stock quote using Polygon's snapshot endpoint (better for paid plans)\n  async getStockQuote(symbol: string): Promise<StockData> {\n    try {\n      // Use snapshot endpoint for real-time data (available on paid plans)\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v2/snapshot/locale/us/markets/stocks/tickers/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      if (!response.data || !response.data.ticker) {\n        throw new Error(`No data found for ${symbol}`)\n      }\n\n      const data = response.data.ticker\n\n      // Extract data from Polygon snapshot response structure\n      const dayData = data.day || {}\n      const prevDayData = data.prevDay || {}\n      const minData = data.min || {}\n\n      // Use the most recent price available\n      const currentPrice = dayData.c || minData.c || prevDayData.c\n      const prevClose = prevDayData.c\n      const change = data.todaysChange || (currentPrice - prevClose)\n      const changePercent = data.todaysChangePerc || ((change / prevClose) * 100)\n\n      return {\n        symbol: symbol.toUpperCase(),\n        name: data.name || symbol.toUpperCase(),\n        price: currentPrice || 0,\n        change: change || 0,\n        changePercent: changePercent || 0,\n        volume: dayData.v || minData.v || 1000000, // Default to 1M volume if missing\n        marketCap: data.market_cap || this.estimateMarketCap(symbol, currentPrice || 0),\n        pe: undefined,\n        dividend: undefined\n      }\n    } catch (error) {\n      console.error('Error fetching stock quote from Polygon:', error)\n\n      // Fallback to previous day data if snapshot fails\n      try {\n        const fallbackResponse = await axios.get(\n          `${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/prev`,\n          {\n            params: {\n              adjusted: 'true',\n              apikey: this.apiKey\n            }\n          }\n        )\n\n        const data = fallbackResponse.data.results[0]\n        return {\n          symbol: symbol.toUpperCase(),\n          name: symbol.toUpperCase(),\n          price: data.c || 0,\n          change: (data.c - data.o) || 0,\n          changePercent: data.o ? ((data.c - data.o) / data.o) * 100 : 0,\n          volume: data.v || 1000000,\n          marketCap: this.estimateMarketCap(symbol, data.c || 0),\n          pe: undefined,\n          dividend: undefined\n        }\n      } catch (fallbackError) {\n        console.error('Polygon fallback also failed:', fallbackError)\n        throw new Error(`Failed to fetch quote for ${symbol}`)\n      }\n    }\n  }\n\n  /**\n   * Estimate market cap based on symbol and price\n   * This is a fallback when Polygon doesn't provide market cap data\n   */\n  private estimateMarketCap(symbol: string, price: number): number {\n    // Import stock universe data for better estimates\n    const stockEstimates: { [key: string]: number } = {\n      // Large cap (>$200B)\n      'AAPL': 3000000000000, 'MSFT': 2*********000, 'NVDA': 1*********000,\n      'GOOGL': 1700000000000, 'GOOG': 1700000000000, 'AMZN': 1500000000000,\n      'TSLA': *********000, 'META': *********000, 'BRK.B': 900000000000,\n\n      // Mid-large cap ($50B-$200B)\n      'JPM': 500000000000, 'V': 500000000000, 'UNH': 500000000000,\n      'JNJ': 450000000000, 'XOM': 450000000000, 'WMT': 600000000000,\n      'PG': 400000000000, 'MA': 400000000000, 'HD': 350000000000,\n      'CVX': 300000000000, 'ABBV': 300000000000, 'BAC': 300000000000,\n      'COST': 350000000000, 'AVGO': 600000000000, 'TSM': 500000000000,\n\n      // Mid cap ($10B-$50B)\n      'NFLX': 200000000000, 'ORCL': 350000000000, 'CRM': 250000000000,\n      'ADBE': 220000000000, 'AMD': 220000000000, 'INTC': 200000000000,\n      'QCOM': 1*********00, 'TMO': 200000000000, 'DHR': 1*********00,\n      'CAT': 1*********00, 'GE': 1*********00, 'DIS': 1*********00,\n      'VZ': 170000000000, 'PFE': 160000000000, 'NKE': 150000000000,\n      'MS': 150000000000, 'UBER': 150000000000, 'C': 120000000000,\n      'GS': 120000000000, 'T': 120000000000, 'AMGN': 150000000000,\n      'HON': 140000000000, 'LOW': 150000000000, 'BMY': 120000000000,\n      'CMCSA': 150000000000, 'SBUX': 1********000, 'MMM': 60000000000,\n\n      // Smaller cap but popular swing trading stocks\n      'PLTR': 60000000000, 'SHOP': *********00, 'GILD': *********00,\n      'TGT': 70000000000, 'COP': 150000000000, 'EOG': 70000000000,\n      'SLB': 60000000000, 'PYPL': 70000000000, 'SQ': 40000000000,\n      'COIN': 50000000000, 'DASH': 50000000000, 'MRNA': 30000000000,\n      'SNOW': 50000000000, 'ROKU': 5000000000, 'HOOD': 15000000000,\n      'LYFT': 6000000000, 'SPG': 50000000000, 'PLD': 120000000000,\n      'NEE': 150000000000\n    }\n\n    // Return estimated market cap if available, otherwise estimate based on price\n    if (stockEstimates[symbol]) {\n      return stockEstimates[symbol]\n    }\n\n    // Rough estimation based on price (very approximate)\n    if (price > 500) return ********0000 // Assume large cap if high price\n    if (price > 100) return 50000000000  // Assume mid-large cap\n    if (price > 50) return 20000000000   // Assume mid cap\n    if (price > 10) return 5000000000    // Assume small-mid cap\n    return ********00 // Default to $1B minimum for scanning\n  }\n\n  // Get historical candlestick data (optimized for paid plans)\n  async getHistoricalData(\n    symbol: string,\n    timespan: 'minute' | 'hour' | 'day' | 'week' | 'month' = 'day',\n    multiplier: number = 1,\n    from: string,\n    to: string\n  ): Promise<CandlestickData[]> {\n    try {\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/range/${multiplier}/${timespan}/${from}/${to}`,\n        {\n          params: {\n            adjusted: 'true',\n            sort: 'asc',\n            limit: 50000, // Higher limit for paid plans\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      if (!response.data.results || response.data.results.length === 0) {\n        console.warn(`No historical data found for ${symbol}`)\n        return []\n      }\n\n      return response.data.results.map((candle: any) => ({\n        timestamp: candle.t,\n        open: candle.o,\n        high: candle.h,\n        low: candle.l,\n        close: candle.c,\n        volume: candle.v\n      }))\n    } catch (error) {\n      console.error(`Error fetching historical data for ${symbol}:`, error)\n\n      // Log the specific error for debugging\n      if (error.response) {\n        console.error(`Polygon API Error: ${error.response.status} - ${error.response.statusText}`)\n        console.error('Response data:', error.response.data)\n      }\n\n      throw new Error(`Failed to fetch historical data for ${symbol}: ${error.message}`)\n    }\n  }\n\n  // Get company details\n  async getCompanyDetails(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v3/reference/tickers/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data.results\n    } catch (error) {\n      console.error('Error fetching company details:', error)\n      return null\n    }\n  }\n\n  // Get market status\n  async getMarketStatus() {\n    try {\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v1/marketstatus/now`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error fetching market status:', error)\n      return null\n    }\n  }\n\n  // Search for stocks\n  async searchStocks(query: string, limit: number = 10) {\n    try {\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v3/reference/tickers`,\n        {\n          params: {\n            search: query,\n            market: 'stocks',\n            active: 'true',\n            limit,\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data.results || []\n    } catch (error) {\n      console.error('Error searching stocks:', error)\n      return []\n    }\n  }\n}\n\n// Create a singleton instance\nexport const polygonAPI = new PolygonAPI()\n"], "names": [], "mappings": ";;;;;;AAAA;;AAGA,MAAM,mBAAmB;AACzB,MAAM,UAAU,QAAQ,GAAG,CAAC,eAAe;AAEpC,MAAM;IACH,OAAc;IAEtB,YAAY,MAAe,CAAE;QAC3B,IAAI,CAAC,MAAM,GAAG,UAAU,WAAW;QACnC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,oFAAoF;IACpF,MAAM,cAAc,MAAc,EAAsB;QACtD,IAAI;YACF,qEAAqE;YACrE,MAAM,WAAW,MAAM,2KAAK,CAAC,GAAG,CAC9B,GAAG,iBAAiB,8CAA8C,EAAE,QAAQ,EAC5E;gBACE,QAAQ;oBACN,QAAQ,IAAI,CAAC,MAAM;gBACrB;YACF;YAGF,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,MAAM,EAAE;gBAC3C,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,QAAQ;YAC/C;YAEA,MAAM,OAAO,SAAS,IAAI,CAAC,MAAM;YAEjC,wDAAwD;YACxD,MAAM,UAAU,KAAK,GAAG,IAAI,CAAC;YAC7B,MAAM,cAAc,KAAK,OAAO,IAAI,CAAC;YACrC,MAAM,UAAU,KAAK,GAAG,IAAI,CAAC;YAE7B,sCAAsC;YACtC,MAAM,eAAe,QAAQ,CAAC,IAAI,QAAQ,CAAC,IAAI,YAAY,CAAC;YAC5D,MAAM,YAAY,YAAY,CAAC;YAC/B,MAAM,SAAS,KAAK,YAAY,IAAK,eAAe;YACpD,MAAM,gBAAgB,KAAK,gBAAgB,IAAK,AAAC,SAAS,YAAa;YAEvE,OAAO;gBACL,QAAQ,OAAO,WAAW;gBAC1B,MAAM,KAAK,IAAI,IAAI,OAAO,WAAW;gBACrC,OAAO,gBAAgB;gBACvB,QAAQ,UAAU;gBAClB,eAAe,iBAAiB;gBAChC,QAAQ,QAAQ,CAAC,IAAI,QAAQ,CAAC,IAAI;gBAClC,WAAW,KAAK,UAAU,IAAI,IAAI,CAAC,iBAAiB,CAAC,QAAQ,gBAAgB;gBAC7E,IAAI;gBACJ,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAE1D,kDAAkD;YAClD,IAAI;gBACF,MAAM,mBAAmB,MAAM,2KAAK,CAAC,GAAG,CACtC,GAAG,iBAAiB,gBAAgB,EAAE,OAAO,KAAK,CAAC,EACnD;oBACE,QAAQ;wBACN,UAAU;wBACV,QAAQ,IAAI,CAAC,MAAM;oBACrB;gBACF;gBAGF,MAAM,OAAO,iBAAiB,IAAI,CAAC,OAAO,CAAC,EAAE;gBAC7C,OAAO;oBACL,QAAQ,OAAO,WAAW;oBAC1B,MAAM,OAAO,WAAW;oBACxB,OAAO,KAAK,CAAC,IAAI;oBACjB,QAAQ,AAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAK;oBAC7B,eAAe,KAAK,CAAC,GAAG,AAAC,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,KAAK,CAAC,GAAI,MAAM;oBAC7D,QAAQ,KAAK,CAAC,IAAI;oBAClB,WAAW,IAAI,CAAC,iBAAiB,CAAC,QAAQ,KAAK,CAAC,IAAI;oBACpD,IAAI;oBACJ,UAAU;gBACZ;YACF,EAAE,OAAO,eAAe;gBACtB,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,QAAQ;YACvD;QACF;IACF;IAEA;;;GAGC,GACD,AAAQ,kBAAkB,MAAc,EAAE,KAAa,EAAU;QAC/D,kDAAkD;QAClD,MAAM,iBAA4C;YAChD,qBAAqB;YACrB,QAAQ;YAAe,QAAQ;YAAe,QAAQ;YACtD,SAAS;YAAe,QAAQ;YAAe,QAAQ;YACvD,QAAQ;YAAc,QAAQ;YAAc,SAAS;YAErD,6BAA6B;YAC7B,OAAO;YAAc,KAAK;YAAc,OAAO;YAC/C,OAAO;YAAc,OAAO;YAAc,OAAO;YACjD,MAAM;YAAc,MAAM;YAAc,MAAM;YAC9C,OAAO;YAAc,QAAQ;YAAc,OAAO;YAClD,QAAQ;YAAc,QAAQ;YAAc,OAAO;YAEnD,sBAAsB;YACtB,QAAQ;YAAc,QAAQ;YAAc,OAAO;YACnD,QAAQ;YAAc,OAAO;YAAc,QAAQ;YACnD,QAAQ;YAAc,OAAO;YAAc,OAAO;YAClD,OAAO;YAAc,MAAM;YAAc,OAAO;YAChD,MAAM;YAAc,OAAO;YAAc,OAAO;YAChD,MAAM;YAAc,QAAQ;YAAc,KAAK;YAC/C,MAAM;YAAc,KAAK;YAAc,QAAQ;YAC/C,OAAO;YAAc,OAAO;YAAc,OAAO;YACjD,SAAS;YAAc,QAAQ;YAAc,OAAO;YAEpD,+CAA+C;YAC/C,QAAQ;YAAa,QAAQ;YAAa,QAAQ;YAClD,OAAO;YAAa,OAAO;YAAc,OAAO;YAChD,OAAO;YAAa,QAAQ;YAAa,MAAM;YAC/C,QAAQ;YAAa,QAAQ;YAAa,QAAQ;YAClD,QAAQ;YAAa,QAAQ;YAAY,QAAQ;YACjD,QAAQ;YAAY,OAAO;YAAa,OAAO;YAC/C,OAAO;QACT;QAEA,8EAA8E;QAC9E,IAAI,cAAc,CAAC,OAAO,EAAE;YAC1B,OAAO,cAAc,CAAC,OAAO;QAC/B;QAEA,qDAAqD;QACrD,IAAI,QAAQ,KAAK,OAAO,aAAa,iCAAiC;;QACtE,IAAI,QAAQ,KAAK,OAAO,YAAa,uBAAuB;;QAC5D,IAAI,QAAQ,IAAI,OAAO,YAAc,iBAAiB;;QACtD,IAAI,QAAQ,IAAI,OAAO,WAAc,uBAAuB;;QAC5D,OAAO,WAAW,sCAAsC;;IAC1D;IAEA,6DAA6D;IAC7D,MAAM,kBACJ,MAAc,EACd,WAAyD,KAAK,EAC9D,aAAqB,CAAC,EACtB,IAAY,EACZ,EAAU,EACkB;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,2KAAK,CAAC,GAAG,CAC9B,GAAG,iBAAiB,gBAAgB,EAAE,OAAO,OAAO,EAAE,WAAW,CAAC,EAAE,SAAS,CAAC,EAAE,KAAK,CAAC,EAAE,IAAI,EAC5F;gBACE,QAAQ;oBACN,UAAU;oBACV,MAAM;oBACN,OAAO;oBACP,QAAQ,IAAI,CAAC,MAAM;gBACrB;YACF;YAGF,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,GAAG;gBAChE,QAAQ,IAAI,CAAC,CAAC,6BAA6B,EAAE,QAAQ;gBACrD,OAAO,EAAE;YACX;YAEA,OAAO,SAAS,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,SAAgB,CAAC;oBACjD,WAAW,OAAO,CAAC;oBACnB,MAAM,OAAO,CAAC;oBACd,MAAM,OAAO,CAAC;oBACd,KAAK,OAAO,CAAC;oBACb,OAAO,OAAO,CAAC;oBACf,QAAQ,OAAO,CAAC;gBAClB,CAAC;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,OAAO,CAAC,CAAC,EAAE;YAE/D,uCAAuC;YACvC,IAAI,MAAM,QAAQ,EAAE;gBAClB,QAAQ,KAAK,CAAC,CAAC,mBAAmB,EAAE,MAAM,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,QAAQ,CAAC,UAAU,EAAE;gBAC1F,QAAQ,KAAK,CAAC,kBAAkB,MAAM,QAAQ,CAAC,IAAI;YACrD;YAEA,MAAM,IAAI,MAAM,CAAC,oCAAoC,EAAE,OAAO,EAAE,EAAE,MAAM,OAAO,EAAE;QACnF;IACF;IAEA,sBAAsB;IACtB,MAAM,kBAAkB,MAAc,EAAE;QACtC,IAAI;YACF,MAAM,WAAW,MAAM,2KAAK,CAAC,GAAG,CAC9B,GAAG,iBAAiB,sBAAsB,EAAE,QAAQ,EACpD;gBACE,QAAQ;oBACN,QAAQ,IAAI,CAAC,MAAM;gBACrB;YACF;YAGF,OAAO,SAAS,IAAI,CAAC,OAAO;QAC9B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO;QACT;IACF;IAEA,oBAAoB;IACpB,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,2KAAK,CAAC,GAAG,CAC9B,GAAG,iBAAiB,oBAAoB,CAAC,EACzC;gBACE,QAAQ;oBACN,QAAQ,IAAI,CAAC,MAAM;gBACrB;YACF;YAGF,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;QACT;IACF;IAEA,oBAAoB;IACpB,MAAM,aAAa,KAAa,EAAE,QAAgB,EAAE,EAAE;QACpD,IAAI;YACF,MAAM,WAAW,MAAM,2KAAK,CAAC,GAAG,CAC9B,GAAG,iBAAiB,qBAAqB,CAAC,EAC1C;gBACE,QAAQ;oBACN,QAAQ;oBACR,QAAQ;oBACR,QAAQ;oBACR;oBACA,QAAQ,IAAI,CAAC,MAAM;gBACrB;YACF;YAGF,OAAO,SAAS,IAAI,CAAC,OAAO,IAAI,EAAE;QACpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,EAAE;QACX;IACF;AACF;AAGO,MAAM,aAAa,IAAI", "debugId": null}}, {"offset": {"line": 400, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/lib/ibkr.ts"], "sourcesContent": ["import { I<PERSON>pi, EventName, ErrorCode, Contract, Order, OrderState } from '@stoqey/ib';\n\nexport interface IBKRConfig {\n  host: string;\n  port: number;\n  clientId: number;\n  paperTrading: boolean;\n}\n\nexport interface IBKRPosition {\n  symbol: string;\n  position: number;\n  marketPrice: number;\n  marketValue: number;\n  averageCost: number;\n  unrealizedPNL: number;\n  realizedPNL: number;\n}\n\nexport interface IBKROrder {\n  orderId: number;\n  symbol: string;\n  action: 'BUY' | 'SELL';\n  quantity: number;\n  orderType: string;\n  price?: number;\n  status: string;\n  filled: number;\n  remaining: number;\n}\n\nexport interface IBKRAccountSummary {\n  totalCashValue: number;\n  netLiquidation: number;\n  grossPositionValue: number;\n  availableFunds: number;\n  buyingPower: number;\n  unrealizedPnL: number;\n  realizedPnL: number;\n}\n\nexport class IBKRAPI {\n  private ib: IBApi;\n  private config: IBKRConfig;\n  private connected: boolean = false;\n  private nextOrderId: number = 1;\n  private positions: Map<string, IBKRPosition> = new Map();\n  private orders: Map<number, IBKROrder> = new Map();\n  private accountSummary: IBKRAccountSummary | null = null;\n\n  constructor(config: IBKRConfig) {\n    this.config = config;\n    this.ib = new IBApi({\n      host: config.host,\n      port: config.port,\n      clientId: config.clientId,\n    });\n\n    this.setupEventHandlers();\n  }\n\n  private setupEventHandlers() {\n    // Connection events\n    this.ib.on(EventName.connected, () => {\n      console.log('✅ Connected to IBKR');\n      this.connected = true;\n      this.requestNextOrderId();\n      this.requestAccountSummary();\n      this.requestPositions();\n    });\n\n    this.ib.on(EventName.disconnected, () => {\n      console.log('❌ Disconnected from IBKR');\n      this.connected = false;\n    });\n\n    this.ib.on(EventName.error, (err, code, reqId) => {\n      console.error(`IBKR Error ${code}:`, err);\n    });\n\n    // Order management\n    this.ib.on(EventName.nextValidId, (orderId: number) => {\n      this.nextOrderId = orderId;\n      console.log(`Next valid order ID: ${orderId}`);\n    });\n\n    this.ib.on(EventName.orderStatus, (orderId, status, filled, remaining, avgFillPrice, permId, parentId, lastFillPrice, clientId, whyHeld, mktCapPrice) => {\n      const order = this.orders.get(orderId);\n      if (order) {\n        order.status = status;\n        order.filled = filled;\n        order.remaining = remaining;\n        this.orders.set(orderId, order);\n      }\n    });\n\n    // Position updates\n    this.ib.on(EventName.position, (account, contract, position, avgCost) => {\n      const symbol = contract.symbol;\n      const existingPosition = this.positions.get(symbol) || {\n        symbol,\n        position: 0,\n        marketPrice: 0,\n        marketValue: 0,\n        averageCost: 0,\n        unrealizedPNL: 0,\n        realizedPNL: 0\n      };\n\n      existingPosition.position = position;\n      existingPosition.averageCost = avgCost;\n      this.positions.set(symbol, existingPosition);\n    });\n\n    // Account summary\n    this.ib.on(EventName.accountSummary, (reqId, account, tag, value, currency) => {\n      if (!this.accountSummary) {\n        this.accountSummary = {\n          totalCashValue: 0,\n          netLiquidation: 0,\n          grossPositionValue: 0,\n          availableFunds: 0,\n          buyingPower: 0,\n          unrealizedPnL: 0,\n          realizedPnL: 0\n        };\n      }\n\n      switch (tag) {\n        case 'TotalCashValue':\n          this.accountSummary.totalCashValue = parseFloat(value);\n          break;\n        case 'NetLiquidation':\n          this.accountSummary.netLiquidation = parseFloat(value);\n          break;\n        case 'GrossPositionValue':\n          this.accountSummary.grossPositionValue = parseFloat(value);\n          break;\n        case 'AvailableFunds':\n          this.accountSummary.availableFunds = parseFloat(value);\n          break;\n        case 'BuyingPower':\n          this.accountSummary.buyingPower = parseFloat(value);\n          break;\n        case 'UnrealizedPnL':\n          this.accountSummary.unrealizedPnL = parseFloat(value);\n          break;\n        case 'RealizedPnL':\n          this.accountSummary.realizedPnL = parseFloat(value);\n          break;\n      }\n    });\n  }\n\n  async connect(): Promise<void> {\n    return new Promise((resolve, reject) => {\n      if (this.connected) {\n        resolve();\n        return;\n      }\n\n      const timeout = setTimeout(() => {\n        reject(new Error('Connection timeout'));\n      }, 10000);\n\n      this.ib.once(EventName.connected, () => {\n        clearTimeout(timeout);\n        resolve();\n      });\n\n      this.ib.once(EventName.error, (err) => {\n        clearTimeout(timeout);\n        reject(err);\n      });\n\n      this.ib.connect();\n    });\n  }\n\n  disconnect(): void {\n    if (this.connected) {\n      this.ib.disconnect();\n    }\n  }\n\n  private requestNextOrderId(): void {\n    this.ib.reqIds(1);\n  }\n\n  private requestAccountSummary(): void {\n    this.ib.reqAccountSummary(1, 'All', 'TotalCashValue,NetLiquidation,GrossPositionValue,AvailableFunds,BuyingPower,UnrealizedPnL,RealizedPnL');\n  }\n\n  private requestPositions(): void {\n    this.ib.reqPositions();\n  }\n\n  // Create a stock contract\n  private createStockContract(symbol: string): Contract {\n    return {\n      symbol: symbol.toUpperCase(),\n      secType: 'STK',\n      exchange: 'SMART',\n      currency: 'USD',\n    };\n  }\n\n  // Place a market order\n  async placeMarketOrder(symbol: string, action: 'BUY' | 'SELL', quantity: number): Promise<number> {\n    if (!this.connected) {\n      throw new Error('Not connected to IBKR');\n    }\n\n    const contract = this.createStockContract(symbol);\n    const order: Order = {\n      orderId: this.nextOrderId,\n      action,\n      totalQuantity: quantity,\n      orderType: 'MKT',\n    };\n\n    // Store order for tracking\n    this.orders.set(this.nextOrderId, {\n      orderId: this.nextOrderId,\n      symbol: symbol.toUpperCase(),\n      action,\n      quantity,\n      orderType: 'MKT',\n      status: 'Submitted',\n      filled: 0,\n      remaining: quantity\n    });\n\n    this.ib.placeOrder(this.nextOrderId, contract, order);\n    const orderId = this.nextOrderId;\n    this.nextOrderId++;\n\n    return orderId;\n  }\n\n  // Place a limit order\n  async placeLimitOrder(symbol: string, action: 'BUY' | 'SELL', quantity: number, price: number): Promise<number> {\n    if (!this.connected) {\n      throw new Error('Not connected to IBKR');\n    }\n\n    const contract = this.createStockContract(symbol);\n    const order: Order = {\n      orderId: this.nextOrderId,\n      action,\n      totalQuantity: quantity,\n      orderType: 'LMT',\n      lmtPrice: price,\n    };\n\n    // Store order for tracking\n    this.orders.set(this.nextOrderId, {\n      orderId: this.nextOrderId,\n      symbol: symbol.toUpperCase(),\n      action,\n      quantity,\n      orderType: 'LMT',\n      price,\n      status: 'Submitted',\n      filled: 0,\n      remaining: quantity\n    });\n\n    this.ib.placeOrder(this.nextOrderId, contract, order);\n    const orderId = this.nextOrderId;\n    this.nextOrderId++;\n\n    return orderId;\n  }\n\n  // Place a stop-loss order\n  async placeStopOrder(symbol: string, action: 'BUY' | 'SELL', quantity: number, stopPrice: number): Promise<number> {\n    if (!this.connected) {\n      throw new Error('Not connected to IBKR');\n    }\n\n    const contract = this.createStockContract(symbol);\n    const order: Order = {\n      orderId: this.nextOrderId,\n      action,\n      totalQuantity: quantity,\n      orderType: 'STP',\n      auxPrice: stopPrice,\n    };\n\n    // Store order for tracking\n    this.orders.set(this.nextOrderId, {\n      orderId: this.nextOrderId,\n      symbol: symbol.toUpperCase(),\n      action,\n      quantity,\n      orderType: 'STP',\n      price: stopPrice,\n      status: 'Submitted',\n      filled: 0,\n      remaining: quantity\n    });\n\n    this.ib.placeOrder(this.nextOrderId, contract, order);\n    const orderId = this.nextOrderId;\n    this.nextOrderId++;\n\n    return orderId;\n  }\n\n  // Cancel an order\n  async cancelOrder(orderId: number): Promise<void> {\n    if (!this.connected) {\n      throw new Error('Not connected to IBKR');\n    }\n\n    this.ib.cancelOrder(orderId);\n  }\n\n  // Get account summary\n  getAccountSummary(): IBKRAccountSummary | null {\n    return this.accountSummary;\n  }\n\n  // Get all positions\n  getPositions(): IBKRPosition[] {\n    return Array.from(this.positions.values());\n  }\n\n  // Get position for specific symbol\n  getPosition(symbol: string): IBKRPosition | null {\n    return this.positions.get(symbol.toUpperCase()) || null;\n  }\n\n  // Get all orders\n  getOrders(): IBKROrder[] {\n    return Array.from(this.orders.values());\n  }\n\n  // Get specific order\n  getOrder(orderId: number): IBKROrder | null {\n    return this.orders.get(orderId) || null;\n  }\n\n  // Check if connected\n  isConnected(): boolean {\n    return this.connected;\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAyCO,MAAM;IACH,GAAU;IACV,OAAmB;IACnB,YAAqB,MAAM;IAC3B,cAAsB,EAAE;IACxB,YAAuC,IAAI,MAAM;IACjD,SAAiC,IAAI,MAAM;IAC3C,iBAA4C,KAAK;IAEzD,YAAY,MAAkB,CAAE;QAC9B,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,EAAE,GAAG,IAAI,mLAAK,CAAC;YAClB,MAAM,OAAO,IAAI;YACjB,MAAM,OAAO,IAAI;YACjB,UAAU,OAAO,QAAQ;QAC3B;QAEA,IAAI,CAAC,kBAAkB;IACzB;IAEQ,qBAAqB;QAC3B,oBAAoB;QACpB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,uLAAS,CAAC,SAAS,EAAE;YAC9B,QAAQ,GAAG,CAAC;YACZ,IAAI,CAAC,SAAS,GAAG;YACjB,IAAI,CAAC,kBAAkB;YACvB,IAAI,CAAC,qBAAqB;YAC1B,IAAI,CAAC,gBAAgB;QACvB;QAEA,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,uLAAS,CAAC,YAAY,EAAE;YACjC,QAAQ,GAAG,CAAC;YACZ,IAAI,CAAC,SAAS,GAAG;QACnB;QAEA,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,uLAAS,CAAC,KAAK,EAAE,CAAC,KAAK,MAAM;YACtC,QAAQ,KAAK,CAAC,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,EAAE;QACvC;QAEA,mBAAmB;QACnB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,uLAAS,CAAC,WAAW,EAAE,CAAC;YACjC,IAAI,CAAC,WAAW,GAAG;YACnB,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,SAAS;QAC/C;QAEA,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,uLAAS,CAAC,WAAW,EAAE,CAAC,SAAS,QAAQ,QAAQ,WAAW,cAAc,QAAQ,UAAU,eAAe,UAAU,SAAS;YACvI,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;YAC9B,IAAI,OAAO;gBACT,MAAM,MAAM,GAAG;gBACf,MAAM,MAAM,GAAG;gBACf,MAAM,SAAS,GAAG;gBAClB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS;YAC3B;QACF;QAEA,mBAAmB;QACnB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,uLAAS,CAAC,QAAQ,EAAE,CAAC,SAAS,UAAU,UAAU;YAC3D,MAAM,SAAS,SAAS,MAAM;YAC9B,MAAM,mBAAmB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW;gBACrD;gBACA,UAAU;gBACV,aAAa;gBACb,aAAa;gBACb,aAAa;gBACb,eAAe;gBACf,aAAa;YACf;YAEA,iBAAiB,QAAQ,GAAG;YAC5B,iBAAiB,WAAW,GAAG;YAC/B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ;QAC7B;QAEA,kBAAkB;QAClB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,uLAAS,CAAC,cAAc,EAAE,CAAC,OAAO,SAAS,KAAK,OAAO;YAChE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBACxB,IAAI,CAAC,cAAc,GAAG;oBACpB,gBAAgB;oBAChB,gBAAgB;oBAChB,oBAAoB;oBACpB,gBAAgB;oBAChB,aAAa;oBACb,eAAe;oBACf,aAAa;gBACf;YACF;YAEA,OAAQ;gBACN,KAAK;oBACH,IAAI,CAAC,cAAc,CAAC,cAAc,GAAG,WAAW;oBAChD;gBACF,KAAK;oBACH,IAAI,CAAC,cAAc,CAAC,cAAc,GAAG,WAAW;oBAChD;gBACF,KAAK;oBACH,IAAI,CAAC,cAAc,CAAC,kBAAkB,GAAG,WAAW;oBACpD;gBACF,KAAK;oBACH,IAAI,CAAC,cAAc,CAAC,cAAc,GAAG,WAAW;oBAChD;gBACF,KAAK;oBACH,IAAI,CAAC,cAAc,CAAC,WAAW,GAAG,WAAW;oBAC7C;gBACF,KAAK;oBACH,IAAI,CAAC,cAAc,CAAC,aAAa,GAAG,WAAW;oBAC/C;gBACF,KAAK;oBACH,IAAI,CAAC,cAAc,CAAC,WAAW,GAAG,WAAW;oBAC7C;YACJ;QACF;IACF;IAEA,MAAM,UAAyB;QAC7B,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,IAAI,IAAI,CAAC,SAAS,EAAE;gBAClB;gBACA;YACF;YAEA,MAAM,UAAU,WAAW;gBACzB,OAAO,IAAI,MAAM;YACnB,GAAG;YAEH,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,uLAAS,CAAC,SAAS,EAAE;gBAChC,aAAa;gBACb;YACF;YAEA,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,uLAAS,CAAC,KAAK,EAAE,CAAC;gBAC7B,aAAa;gBACb,OAAO;YACT;YAEA,IAAI,CAAC,EAAE,CAAC,OAAO;QACjB;IACF;IAEA,aAAmB;QACjB,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,EAAE,CAAC,UAAU;QACpB;IACF;IAEQ,qBAA2B;QACjC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC;IACjB;IAEQ,wBAA8B;QACpC,IAAI,CAAC,EAAE,CAAC,iBAAiB,CAAC,GAAG,OAAO;IACtC;IAEQ,mBAAyB;QAC/B,IAAI,CAAC,EAAE,CAAC,YAAY;IACtB;IAEA,0BAA0B;IAClB,oBAAoB,MAAc,EAAY;QACpD,OAAO;YACL,QAAQ,OAAO,WAAW;YAC1B,SAAS;YACT,UAAU;YACV,UAAU;QACZ;IACF;IAEA,uBAAuB;IACvB,MAAM,iBAAiB,MAAc,EAAE,MAAsB,EAAE,QAAgB,EAAmB;QAChG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,IAAI,CAAC,mBAAmB,CAAC;QAC1C,MAAM,QAAe;YACnB,SAAS,IAAI,CAAC,WAAW;YACzB;YACA,eAAe;YACf,WAAW;QACb;QAEA,2BAA2B;QAC3B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE;YAChC,SAAS,IAAI,CAAC,WAAW;YACzB,QAAQ,OAAO,WAAW;YAC1B;YACA;YACA,WAAW;YACX,QAAQ;YACR,QAAQ;YACR,WAAW;QACb;QAEA,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU;QAC/C,MAAM,UAAU,IAAI,CAAC,WAAW;QAChC,IAAI,CAAC,WAAW;QAEhB,OAAO;IACT;IAEA,sBAAsB;IACtB,MAAM,gBAAgB,MAAc,EAAE,MAAsB,EAAE,QAAgB,EAAE,KAAa,EAAmB;QAC9G,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,IAAI,CAAC,mBAAmB,CAAC;QAC1C,MAAM,QAAe;YACnB,SAAS,IAAI,CAAC,WAAW;YACzB;YACA,eAAe;YACf,WAAW;YACX,UAAU;QACZ;QAEA,2BAA2B;QAC3B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE;YAChC,SAAS,IAAI,CAAC,WAAW;YACzB,QAAQ,OAAO,WAAW;YAC1B;YACA;YACA,WAAW;YACX;YACA,QAAQ;YACR,QAAQ;YACR,WAAW;QACb;QAEA,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU;QAC/C,MAAM,UAAU,IAAI,CAAC,WAAW;QAChC,IAAI,CAAC,WAAW;QAEhB,OAAO;IACT;IAEA,0BAA0B;IAC1B,MAAM,eAAe,MAAc,EAAE,MAAsB,EAAE,QAAgB,EAAE,SAAiB,EAAmB;QACjH,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,IAAI,CAAC,mBAAmB,CAAC;QAC1C,MAAM,QAAe;YACnB,SAAS,IAAI,CAAC,WAAW;YACzB;YACA,eAAe;YACf,WAAW;YACX,UAAU;QACZ;QAEA,2BAA2B;QAC3B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE;YAChC,SAAS,IAAI,CAAC,WAAW;YACzB,QAAQ,OAAO,WAAW;YAC1B;YACA;YACA,WAAW;YACX,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,WAAW;QACb;QAEA,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU;QAC/C,MAAM,UAAU,IAAI,CAAC,WAAW;QAChC,IAAI,CAAC,WAAW;QAEhB,OAAO;IACT;IAEA,kBAAkB;IAClB,MAAM,YAAY,OAAe,EAAiB;QAChD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC;IACtB;IAEA,sBAAsB;IACtB,oBAA+C;QAC7C,OAAO,IAAI,CAAC,cAAc;IAC5B;IAEA,oBAAoB;IACpB,eAA+B;QAC7B,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM;IACzC;IAEA,mCAAmC;IACnC,YAAY,MAAc,EAAuB;QAC/C,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,WAAW,OAAO;IACrD;IAEA,iBAAiB;IACjB,YAAyB;QACvB,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;IACtC;IAEA,qBAAqB;IACrB,SAAS,OAAe,EAAoB;QAC1C,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY;IACrC;IAEA,qBAAqB;IACrB,cAAuB;QACrB,OAAO,IAAI,CAAC,SAAS;IACvB;AACF", "debugId": null}}, {"offset": {"line": 674, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/lib/catalystDetection.ts"], "sourcesContent": ["import { \n  Catalyst, \n  CatalystType, \n  CatalystTier, \n  CatalystImpact, \n  CatalystImpactMeasurement \n} from '@/types/trading'\nimport { PolygonAPI } from './polygon'\n\nexport class CatalystDetectionEngine {\n  private polygonAPI: PolygonAPI\n  private catalystCache: Map<string, Catalyst[]> = new Map()\n  private impactMeasurements: Map<string, CatalystImpactMeasurement> = new Map()\n\n  constructor(polygonApiKey?: string) {\n    this.polygonAPI = new PolygonAPI(polygonApiKey)\n  }\n\n  /**\n   * Detect catalysts for a specific symbol\n   */\n  async detectCatalysts(symbol: string): Promise<Catalyst[]> {\n    const catalysts: Catalyst[] = []\n\n    try {\n      // Check cache first (5-minute cache)\n      const cacheKey = `${symbol}_${Math.floor(Date.now() / (5 * 60 * 1000))}`\n      if (this.catalystCache.has(cacheKey)) {\n        return this.catalystCache.get(cacheKey)!\n      }\n\n      // Detect different types of catalysts in parallel\n      const [\n        earningsCatalysts,\n        newsCatalysts,\n        analystCatalysts,\n        insiderCatalysts,\n        secFilingCatalysts\n      ] = await Promise.all([\n        this.detectEarningsCatalysts(symbol),\n        this.detectNewsCatalysts(symbol),\n        this.detectAnalystCatalysts(symbol),\n        this.detectInsiderCatalysts(symbol),\n        this.detectSECFilingCatalysts(symbol)\n      ])\n\n      catalysts.push(\n        ...earningsCatalysts,\n        ...newsCatalysts,\n        ...analystCatalysts,\n        ...insiderCatalysts,\n        ...secFilingCatalysts\n      )\n\n      // Sort by quality score and freshness\n      catalysts.sort((a, b) => {\n        const freshnessWeight = this.getFreshnessWeight(a.freshness) - this.getFreshnessWeight(b.freshness)\n        if (freshnessWeight !== 0) return freshnessWeight\n        return b.qualityScore - a.qualityScore\n      })\n\n      // Cache results\n      this.catalystCache.set(cacheKey, catalysts)\n\n      return catalysts\n    } catch (error) {\n      console.error(`Error detecting catalysts for ${symbol}:`, error)\n      return []\n    }\n  }\n\n  /**\n   * Detect earnings-related catalysts\n   */\n  private async detectEarningsCatalysts(symbol: string): Promise<Catalyst[]> {\n    const catalysts: Catalyst[] = []\n\n    try {\n      // Get recent earnings data from FMP\n      const earningsData = await this.fmpAPI.getEarningsCalendar(symbol, 30) // Last 30 days\n      \n      for (const earnings of earningsData) {\n        if (this.isEarningsBeat(earnings)) {\n          const catalyst: Catalyst = {\n            id: `earnings_${symbol}_${earnings.date}`,\n            symbol,\n            type: 'earnings_beat_guidance',\n            tier: 'tier_1', // Highest priority\n            impact: 'bullish',\n            title: `${symbol} Beats Earnings Expectations`,\n            description: `Q${earnings.quarter} earnings beat: EPS ${earnings.actualEPS} vs ${earnings.estimatedEPS} expected`,\n            source: 'FMP Earnings Data',\n            announcementTime: earnings.date,\n            discoveredTime: new Date().toISOString(),\n            qualityScore: this.calculateEarningsQualityScore(earnings),\n            freshness: this.calculateFreshness(earnings.date),\n            estimatedDuration: 'short_term',\n            verified: true,\n            tags: ['earnings', 'beat', 'guidance'],\n            metadata: {\n              actualEPS: earnings.actualEPS,\n              estimatedEPS: earnings.estimatedEPS,\n              beatPercent: ((earnings.actualEPS - earnings.estimatedEPS) / earnings.estimatedEPS) * 100,\n              guidanceRaised: earnings.guidanceRaised || false\n            }\n          }\n          catalysts.push(catalyst)\n        }\n      }\n    } catch (error) {\n      console.error(`Error detecting earnings catalysts for ${symbol}:`, error)\n    }\n\n    return catalysts\n  }\n\n  /**\n   * Detect news-related catalysts\n   */\n  private async detectNewsCatalysts(symbol: string): Promise<Catalyst[]> {\n    const catalysts: Catalyst[] = []\n\n    try {\n      // Get recent news from FMP\n      const newsData = await this.fmpAPI.getStockNews(symbol, 50) // Last 50 articles\n      \n      for (const news of newsData) {\n        const catalystType = this.classifyNewsAsCatalyst(news)\n        if (catalystType) {\n          const catalyst: Catalyst = {\n            id: `news_${symbol}_${news.publishedDate}_${news.title.slice(0, 20).replace(/\\s+/g, '_')}`,\n            symbol,\n            type: catalystType.type,\n            tier: catalystType.tier,\n            impact: catalystType.impact,\n            title: news.title,\n            description: news.text?.slice(0, 200) + '...' || news.title,\n            source: news.site,\n            sourceUrl: news.url,\n            announcementTime: news.publishedDate,\n            discoveredTime: new Date().toISOString(),\n            qualityScore: this.calculateNewsQualityScore(news, catalystType.type),\n            freshness: this.calculateFreshness(news.publishedDate),\n            estimatedDuration: this.estimateNewsDuration(catalystType.type),\n            verified: this.isReliableNewsSource(news.site),\n            tags: this.extractNewsKeywords(news.title + ' ' + (news.text || '')),\n            metadata: {\n              site: news.site,\n              sentiment: news.sentiment || 'neutral'\n            }\n          }\n          catalysts.push(catalyst)\n        }\n      }\n    } catch (error) {\n      console.error(`Error detecting news catalysts for ${symbol}:`, error)\n    }\n\n    return catalysts\n  }\n\n  /**\n   * Detect analyst upgrade/downgrade catalysts\n   */\n  private async detectAnalystCatalysts(symbol: string): Promise<Catalyst[]> {\n    const catalysts: Catalyst[] = []\n\n    try {\n      // Get analyst recommendations from FMP\n      const analystData = await this.fmpAPI.getAnalystRecommendations(symbol, 30)\n      \n      for (const recommendation of analystData) {\n        if (this.isSignificantAnalystChange(recommendation)) {\n          const isUpgrade = recommendation.newGrade > recommendation.previousGrade\n          const catalyst: Catalyst = {\n            id: `analyst_${symbol}_${recommendation.date}_${recommendation.analystCompany}`,\n            symbol,\n            type: isUpgrade ? 'analyst_upgrade' : 'analyst_downgrade',\n            tier: 'tier_2',\n            impact: isUpgrade ? 'bullish' : 'bearish',\n            title: `${recommendation.analystCompany} ${isUpgrade ? 'Upgrades' : 'Downgrades'} ${symbol}`,\n            description: `${recommendation.analystName} at ${recommendation.analystCompany} ${isUpgrade ? 'upgraded' : 'downgraded'} to ${recommendation.newGrade}`,\n            source: 'FMP Analyst Data',\n            announcementTime: recommendation.date,\n            discoveredTime: new Date().toISOString(),\n            qualityScore: this.calculateAnalystQualityScore(recommendation),\n            freshness: this.calculateFreshness(recommendation.date),\n            estimatedDuration: 'medium_term',\n            verified: true,\n            tags: ['analyst', isUpgrade ? 'upgrade' : 'downgrade', recommendation.analystCompany.toLowerCase()],\n            metadata: {\n              analystCompany: recommendation.analystCompany,\n              analystName: recommendation.analystName,\n              previousGrade: recommendation.previousGrade,\n              newGrade: recommendation.newGrade,\n              priceTarget: recommendation.priceTarget\n            }\n          }\n          catalysts.push(catalyst)\n        }\n      }\n    } catch (error) {\n      console.error(`Error detecting analyst catalysts for ${symbol}:`, error)\n    }\n\n    return catalysts\n  }\n\n  /**\n   * Detect insider trading catalysts\n   */\n  private async detectInsiderCatalysts(symbol: string): Promise<Catalyst[]> {\n    const catalysts: Catalyst[] = []\n\n    try {\n      // Get insider trading data from FMP\n      const insiderData = await this.fmpAPI.getInsiderTrading(symbol, 30)\n      \n      for (const trade of insiderData) {\n        if (this.isSignificantInsiderTrade(trade)) {\n          const isBuying = trade.transactionType.toLowerCase().includes('buy') || \n                          trade.transactionType.toLowerCase().includes('purchase')\n          \n          const catalyst: Catalyst = {\n            id: `insider_${symbol}_${trade.filingDate}_${trade.reportingName}`,\n            symbol,\n            type: isBuying ? 'insider_buying' : 'insider_selling',\n            tier: 'tier_2',\n            impact: isBuying ? 'bullish' : 'bearish',\n            title: `${trade.reportingName} ${isBuying ? 'Buys' : 'Sells'} ${symbol} Shares`,\n            description: `${trade.reportingName} (${trade.typeOfOwner}) ${trade.transactionType} ${trade.securitiesTransacted} shares at $${trade.price}`,\n            source: 'SEC Insider Trading Filings',\n            announcementTime: trade.filingDate,\n            discoveredTime: new Date().toISOString(),\n            qualityScore: this.calculateInsiderQualityScore(trade),\n            freshness: this.calculateFreshness(trade.filingDate),\n            estimatedDuration: 'medium_term',\n            verified: true,\n            tags: ['insider', isBuying ? 'buying' : 'selling', trade.typeOfOwner.toLowerCase()],\n            metadata: {\n              reportingName: trade.reportingName,\n              typeOfOwner: trade.typeOfOwner,\n              transactionType: trade.transactionType,\n              securitiesTransacted: trade.securitiesTransacted,\n              price: trade.price,\n              dollarValue: trade.securitiesTransacted * trade.price\n            }\n          }\n          catalysts.push(catalyst)\n        }\n      }\n    } catch (error) {\n      console.error(`Error detecting insider catalysts for ${symbol}:`, error)\n    }\n\n    return catalysts\n  }\n\n  /**\n   * Detect SEC filing catalysts\n   */\n  private async detectSECFilingCatalysts(symbol: string): Promise<Catalyst[]> {\n    const catalysts: Catalyst[] = []\n\n    try {\n      // Get recent SEC filings from FMP\n      const filings = await this.fmpAPI.getSECFilings(symbol, 30)\n      \n      for (const filing of filings) {\n        if (this.isSignificantSECFiling(filing)) {\n          const catalyst: Catalyst = {\n            id: `sec_${symbol}_${filing.filedDate}_${filing.type}`,\n            symbol,\n            type: 'sec_filing',\n            tier: this.getSECFilingTier(filing.type),\n            impact: this.getSECFilingImpact(filing.type),\n            title: `${symbol} Files ${filing.type}`,\n            description: `${filing.type} filing: ${filing.description || 'SEC regulatory filing'}`,\n            source: 'SEC EDGAR Database',\n            sourceUrl: filing.link,\n            announcementTime: filing.filedDate,\n            discoveredTime: new Date().toISOString(),\n            qualityScore: this.calculateSECFilingQualityScore(filing),\n            freshness: this.calculateFreshness(filing.filedDate),\n            estimatedDuration: this.estimateSECFilingDuration(filing.type),\n            verified: true,\n            tags: ['sec', 'filing', filing.type.toLowerCase()],\n            metadata: {\n              filingType: filing.type,\n              cik: filing.cik,\n              acceptedDate: filing.acceptedDate\n            }\n          }\n          catalysts.push(catalyst)\n        }\n      }\n    } catch (error) {\n      console.error(`Error detecting SEC filing catalysts for ${symbol}:`, error)\n    }\n\n    return catalysts\n  }\n\n  // Helper methods for catalyst classification and scoring\n  private getFreshnessWeight(freshness: string): number {\n    switch (freshness) {\n      case 'fresh': return 3\n      case 'moderate': return 2\n      case 'stale': return 1\n      default: return 0\n    }\n  }\n\n  private calculateFreshness(dateString: string): 'fresh' | 'moderate' | 'stale' {\n    const date = new Date(dateString)\n    const now = new Date()\n    const hoursAgo = (now.getTime() - date.getTime()) / (1000 * 60 * 60)\n    \n    if (hoursAgo < 24) return 'fresh'\n    if (hoursAgo < 72) return 'moderate'\n    return 'stale'\n  }\n\n  private isEarningsBeat(earnings: any): boolean {\n    return earnings.actualEPS > earnings.estimatedEPS && \n           (earnings.guidanceRaised || earnings.actualEPS > earnings.estimatedEPS * 1.05)\n  }\n\n  private calculateEarningsQualityScore(earnings: any): number {\n    let score = 5 // Base score\n    \n    // Beat percentage\n    const beatPercent = ((earnings.actualEPS - earnings.estimatedEPS) / earnings.estimatedEPS) * 100\n    if (beatPercent > 20) score += 3\n    else if (beatPercent > 10) score += 2\n    else if (beatPercent > 5) score += 1\n    \n    // Guidance raised\n    if (earnings.guidanceRaised) score += 2\n    \n    // Revenue beat\n    if (earnings.actualRevenue > earnings.estimatedRevenue) score += 1\n    \n    return Math.min(10, score)\n  }\n\n  private classifyNewsAsCatalyst(news: any): { type: CatalystType, tier: CatalystTier, impact: CatalystImpact } | null {\n    const title = news.title.toLowerCase()\n    const text = (news.text || '').toLowerCase()\n    const content = title + ' ' + text\n\n    // FDA/Drug related\n    if (content.includes('fda') && (content.includes('approval') || content.includes('approved'))) {\n      return { type: 'fda_approval', tier: 'tier_1', impact: 'bullish' }\n    }\n    \n    if (content.includes('trial') && (content.includes('positive') || content.includes('successful'))) {\n      return { type: 'drug_trial_results', tier: 'tier_1', impact: 'bullish' }\n    }\n\n    // Contract/Partnership\n    if (content.includes('contract') && (content.includes('win') || content.includes('awarded'))) {\n      return { type: 'contract_win', tier: 'tier_1', impact: 'bullish' }\n    }\n    \n    if (content.includes('partnership') || content.includes('collaboration')) {\n      return { type: 'partnership', tier: 'tier_1', impact: 'bullish' }\n    }\n\n    // M&A\n    if (content.includes('merger') || content.includes('acquisition') || content.includes('buyout')) {\n      return { type: 'merger_acquisition', tier: 'tier_1', impact: 'bullish' }\n    }\n\n    // Stock split\n    if (content.includes('stock split') || content.includes('share split')) {\n      return { type: 'stock_split', tier: 'tier_2', impact: 'bullish' }\n    }\n\n    return null\n  }\n\n  private calculateNewsQualityScore(news: any, catalystType: CatalystType): number {\n    let score = 5 // Base score\n    \n    // Source reliability\n    if (this.isReliableNewsSource(news.site)) score += 2\n    \n    // Catalyst type importance\n    if (['fda_approval', 'merger_acquisition', 'earnings_beat_guidance'].includes(catalystType)) {\n      score += 2\n    }\n    \n    // Sentiment\n    if (news.sentiment === 'positive') score += 1\n    else if (news.sentiment === 'negative') score -= 1\n    \n    return Math.max(1, Math.min(10, score))\n  }\n\n  private isReliableNewsSource(site: string): boolean {\n    const reliableSources = [\n      'reuters.com', 'bloomberg.com', 'wsj.com', 'cnbc.com', \n      'marketwatch.com', 'yahoo.com', 'sec.gov', 'fda.gov'\n    ]\n    return reliableSources.some(source => site.toLowerCase().includes(source))\n  }\n\n  private extractNewsKeywords(text: string): string[] {\n    const keywords = []\n    const content = text.toLowerCase()\n    \n    const keywordMap = {\n      'earnings': ['earnings', 'eps', 'revenue', 'profit'],\n      'fda': ['fda', 'approval', 'drug', 'trial'],\n      'merger': ['merger', 'acquisition', 'buyout', 'takeover'],\n      'partnership': ['partnership', 'collaboration', 'alliance'],\n      'contract': ['contract', 'deal', 'agreement'],\n      'upgrade': ['upgrade', 'raised', 'increased'],\n      'downgrade': ['downgrade', 'lowered', 'reduced']\n    }\n    \n    for (const [category, terms] of Object.entries(keywordMap)) {\n      if (terms.some(term => content.includes(term))) {\n        keywords.push(category)\n      }\n    }\n    \n    return keywords\n  }\n\n  private estimateNewsDuration(catalystType: CatalystType): 'intraday' | 'short_term' | 'medium_term' | 'long_term' {\n    switch (catalystType) {\n      case 'earnings_beat_guidance':\n      case 'fda_approval':\n      case 'merger_acquisition':\n        return 'short_term'\n      case 'analyst_upgrade':\n      case 'analyst_downgrade':\n      case 'partnership':\n        return 'medium_term'\n      case 'stock_split':\n        return 'long_term'\n      default:\n        return 'short_term'\n    }\n  }\n\n  private isSignificantAnalystChange(recommendation: any): boolean {\n    // Check if it's a meaningful grade change\n    const gradeChange = Math.abs(recommendation.newGrade - recommendation.previousGrade)\n    return gradeChange >= 1 && recommendation.priceTarget > 0\n  }\n\n  private calculateAnalystQualityScore(recommendation: any): number {\n    let score = 5 // Base score\n    \n    // Analyst firm reputation (simplified)\n    const topFirms = ['goldman sachs', 'morgan stanley', 'jp morgan', 'bank of america']\n    if (topFirms.some(firm => recommendation.analystCompany.toLowerCase().includes(firm))) {\n      score += 2\n    }\n    \n    // Grade change magnitude\n    const gradeChange = Math.abs(recommendation.newGrade - recommendation.previousGrade)\n    if (gradeChange >= 2) score += 2\n    else if (gradeChange >= 1) score += 1\n    \n    // Price target change\n    if (recommendation.priceTargetChange > 10) score += 1\n    \n    return Math.min(10, score)\n  }\n\n  private isSignificantInsiderTrade(trade: any): boolean {\n    const dollarValue = trade.securitiesTransacted * trade.price\n    return dollarValue >= 1000000 && // $1M+ transactions\n           trade.typeOfOwner !== 'Other' // Exclude generic \"Other\" category\n  }\n\n  private calculateInsiderQualityScore(trade: any): number {\n    let score = 5 // Base score\n    \n    const dollarValue = trade.securitiesTransacted * trade.price\n    \n    // Transaction size\n    if (dollarValue >= ********) score += 3 // $10M+\n    else if (dollarValue >= 5000000) score += 2 // $5M+\n    else if (dollarValue >= 1000000) score += 1 // $1M+\n    \n    // Insider type\n    if (trade.typeOfOwner.toLowerCase().includes('ceo') || \n        trade.typeOfOwner.toLowerCase().includes('cfo')) {\n      score += 2\n    } else if (trade.typeOfOwner.toLowerCase().includes('director')) {\n      score += 1\n    }\n    \n    return Math.min(10, score)\n  }\n\n  private isSignificantSECFiling(filing: any): boolean {\n    const significantFilings = ['8-K', '10-K', '10-Q', '13D', '13G', 'S-1', 'S-4']\n    return significantFilings.includes(filing.type)\n  }\n\n  private getSECFilingTier(filingType: string): CatalystTier {\n    const tier1Filings = ['8-K', '13D', 'S-4'] // Material events, activist investors, M&A\n    const tier2Filings = ['10-K', '10-Q', '13G'] // Regular reports, passive investors\n    \n    if (tier1Filings.includes(filingType)) return 'tier_1'\n    if (tier2Filings.includes(filingType)) return 'tier_2'\n    return 'tier_3'\n  }\n\n  private getSECFilingImpact(filingType: string): CatalystImpact {\n    // Most SEC filings are neutral until analyzed\n    return 'neutral'\n  }\n\n  private calculateSECFilingQualityScore(filing: any): number {\n    let score = 5 // Base score\n    \n    // Filing type importance\n    if (['8-K', '13D'].includes(filing.type)) score += 2\n    else if (['10-K', '10-Q'].includes(filing.type)) score += 1\n    \n    return Math.min(10, score)\n  }\n\n  private estimateSECFilingDuration(filingType: string): 'intraday' | 'short_term' | 'medium_term' | 'long_term' {\n    switch (filingType) {\n      case '8-K': return 'short_term' // Material events\n      case '13D': return 'medium_term' // Activist investors\n      case 'S-4': return 'long_term' // M&A registration\n      default: return 'medium_term'\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;AAOA;;AAEO,MAAM;IACH,WAAsB;IACtB,gBAAyC,IAAI,MAAK;IAClD,qBAA6D,IAAI,MAAK;IAE9E,YAAY,aAAsB,CAAE;QAClC,IAAI,CAAC,UAAU,GAAG,IAAI,8JAAU,CAAC;IACnC;IAEA;;GAEC,GACD,MAAM,gBAAgB,MAAc,EAAuB;QACzD,MAAM,YAAwB,EAAE;QAEhC,IAAI;YACF,qCAAqC;YACrC,MAAM,WAAW,GAAG,OAAO,CAAC,EAAE,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,KAAK,IAAI,IAAI;YACxE,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW;gBACpC,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;YAChC;YAEA,kDAAkD;YAClD,MAAM,CACJ,mBACA,eACA,kBACA,kBACA,mBACD,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACpB,IAAI,CAAC,uBAAuB,CAAC;gBAC7B,IAAI,CAAC,mBAAmB,CAAC;gBACzB,IAAI,CAAC,sBAAsB,CAAC;gBAC5B,IAAI,CAAC,sBAAsB,CAAC;gBAC5B,IAAI,CAAC,wBAAwB,CAAC;aAC/B;YAED,UAAU,IAAI,IACT,sBACA,kBACA,qBACA,qBACA;YAGL,sCAAsC;YACtC,UAAU,IAAI,CAAC,CAAC,GAAG;gBACjB,MAAM,kBAAkB,IAAI,CAAC,kBAAkB,CAAC,EAAE,SAAS,IAAI,IAAI,CAAC,kBAAkB,CAAC,EAAE,SAAS;gBAClG,IAAI,oBAAoB,GAAG,OAAO;gBAClC,OAAO,EAAE,YAAY,GAAG,EAAE,YAAY;YACxC;YAEA,gBAAgB;YAChB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU;YAEjC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,8BAA8B,EAAE,OAAO,CAAC,CAAC,EAAE;YAC1D,OAAO,EAAE;QACX;IACF;IAEA;;GAEC,GACD,MAAc,wBAAwB,MAAc,EAAuB;QACzE,MAAM,YAAwB,EAAE;QAEhC,IAAI;YACF,oCAAoC;YACpC,MAAM,eAAe,MAAM,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,QAAQ,IAAI,eAAe;;YAEtF,KAAK,MAAM,YAAY,aAAc;gBACnC,IAAI,IAAI,CAAC,cAAc,CAAC,WAAW;oBACjC,MAAM,WAAqB;wBACzB,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,EAAE,SAAS,IAAI,EAAE;wBACzC;wBACA,MAAM;wBACN,MAAM;wBACN,QAAQ;wBACR,OAAO,GAAG,OAAO,4BAA4B,CAAC;wBAC9C,aAAa,CAAC,CAAC,EAAE,SAAS,OAAO,CAAC,oBAAoB,EAAE,SAAS,SAAS,CAAC,IAAI,EAAE,SAAS,YAAY,CAAC,SAAS,CAAC;wBACjH,QAAQ;wBACR,kBAAkB,SAAS,IAAI;wBAC/B,gBAAgB,IAAI,OAAO,WAAW;wBACtC,cAAc,IAAI,CAAC,6BAA6B,CAAC;wBACjD,WAAW,IAAI,CAAC,kBAAkB,CAAC,SAAS,IAAI;wBAChD,mBAAmB;wBACnB,UAAU;wBACV,MAAM;4BAAC;4BAAY;4BAAQ;yBAAW;wBACtC,UAAU;4BACR,WAAW,SAAS,SAAS;4BAC7B,cAAc,SAAS,YAAY;4BACnC,aAAa,AAAC,CAAC,SAAS,SAAS,GAAG,SAAS,YAAY,IAAI,SAAS,YAAY,GAAI;4BACtF,gBAAgB,SAAS,cAAc,IAAI;wBAC7C;oBACF;oBACA,UAAU,IAAI,CAAC;gBACjB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,uCAAuC,EAAE,OAAO,CAAC,CAAC,EAAE;QACrE;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAc,oBAAoB,MAAc,EAAuB;QACrE,MAAM,YAAwB,EAAE;QAEhC,IAAI;YACF,2BAA2B;YAC3B,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,IAAI,mBAAmB;;YAE/E,KAAK,MAAM,QAAQ,SAAU;gBAC3B,MAAM,eAAe,IAAI,CAAC,sBAAsB,CAAC;gBACjD,IAAI,cAAc;oBAChB,MAAM,WAAqB;wBACzB,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE,KAAK,aAAa,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,QAAQ,MAAM;wBAC1F;wBACA,MAAM,aAAa,IAAI;wBACvB,MAAM,aAAa,IAAI;wBACvB,QAAQ,aAAa,MAAM;wBAC3B,OAAO,KAAK,KAAK;wBACjB,aAAa,KAAK,IAAI,EAAE,MAAM,GAAG,OAAO,SAAS,KAAK,KAAK;wBAC3D,QAAQ,KAAK,IAAI;wBACjB,WAAW,KAAK,GAAG;wBACnB,kBAAkB,KAAK,aAAa;wBACpC,gBAAgB,IAAI,OAAO,WAAW;wBACtC,cAAc,IAAI,CAAC,yBAAyB,CAAC,MAAM,aAAa,IAAI;wBACpE,WAAW,IAAI,CAAC,kBAAkB,CAAC,KAAK,aAAa;wBACrD,mBAAmB,IAAI,CAAC,oBAAoB,CAAC,aAAa,IAAI;wBAC9D,UAAU,IAAI,CAAC,oBAAoB,CAAC,KAAK,IAAI;wBAC7C,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,KAAK,GAAG,MAAM,CAAC,KAAK,IAAI,IAAI,EAAE;wBAClE,UAAU;4BACR,MAAM,KAAK,IAAI;4BACf,WAAW,KAAK,SAAS,IAAI;wBAC/B;oBACF;oBACA,UAAU,IAAI,CAAC;gBACjB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,OAAO,CAAC,CAAC,EAAE;QACjE;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAc,uBAAuB,MAAc,EAAuB;QACxE,MAAM,YAAwB,EAAE;QAEhC,IAAI;YACF,uCAAuC;YACvC,MAAM,cAAc,MAAM,IAAI,CAAC,MAAM,CAAC,yBAAyB,CAAC,QAAQ;YAExE,KAAK,MAAM,kBAAkB,YAAa;gBACxC,IAAI,IAAI,CAAC,0BAA0B,CAAC,iBAAiB;oBACnD,MAAM,YAAY,eAAe,QAAQ,GAAG,eAAe,aAAa;oBACxE,MAAM,WAAqB;wBACzB,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE,eAAe,IAAI,CAAC,CAAC,EAAE,eAAe,cAAc,EAAE;wBAC/E;wBACA,MAAM,YAAY,oBAAoB;wBACtC,MAAM;wBACN,QAAQ,YAAY,YAAY;wBAChC,OAAO,GAAG,eAAe,cAAc,CAAC,CAAC,EAAE,YAAY,aAAa,aAAa,CAAC,EAAE,QAAQ;wBAC5F,aAAa,GAAG,eAAe,WAAW,CAAC,IAAI,EAAE,eAAe,cAAc,CAAC,CAAC,EAAE,YAAY,aAAa,aAAa,IAAI,EAAE,eAAe,QAAQ,EAAE;wBACvJ,QAAQ;wBACR,kBAAkB,eAAe,IAAI;wBACrC,gBAAgB,IAAI,OAAO,WAAW;wBACtC,cAAc,IAAI,CAAC,4BAA4B,CAAC;wBAChD,WAAW,IAAI,CAAC,kBAAkB,CAAC,eAAe,IAAI;wBACtD,mBAAmB;wBACnB,UAAU;wBACV,MAAM;4BAAC;4BAAW,YAAY,YAAY;4BAAa,eAAe,cAAc,CAAC,WAAW;yBAAG;wBACnG,UAAU;4BACR,gBAAgB,eAAe,cAAc;4BAC7C,aAAa,eAAe,WAAW;4BACvC,eAAe,eAAe,aAAa;4BAC3C,UAAU,eAAe,QAAQ;4BACjC,aAAa,eAAe,WAAW;wBACzC;oBACF;oBACA,UAAU,IAAI,CAAC;gBACjB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,sCAAsC,EAAE,OAAO,CAAC,CAAC,EAAE;QACpE;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAc,uBAAuB,MAAc,EAAuB;QACxE,MAAM,YAAwB,EAAE;QAEhC,IAAI;YACF,oCAAoC;YACpC,MAAM,cAAc,MAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,QAAQ;YAEhE,KAAK,MAAM,SAAS,YAAa;gBAC/B,IAAI,IAAI,CAAC,yBAAyB,CAAC,QAAQ;oBACzC,MAAM,WAAW,MAAM,eAAe,CAAC,WAAW,GAAG,QAAQ,CAAC,UAC9C,MAAM,eAAe,CAAC,WAAW,GAAG,QAAQ,CAAC;oBAE7D,MAAM,WAAqB;wBACzB,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE,MAAM,UAAU,CAAC,CAAC,EAAE,MAAM,aAAa,EAAE;wBAClE;wBACA,MAAM,WAAW,mBAAmB;wBACpC,MAAM;wBACN,QAAQ,WAAW,YAAY;wBAC/B,OAAO,GAAG,MAAM,aAAa,CAAC,CAAC,EAAE,WAAW,SAAS,QAAQ,CAAC,EAAE,OAAO,OAAO,CAAC;wBAC/E,aAAa,GAAG,MAAM,aAAa,CAAC,EAAE,EAAE,MAAM,WAAW,CAAC,EAAE,EAAE,MAAM,eAAe,CAAC,CAAC,EAAE,MAAM,oBAAoB,CAAC,YAAY,EAAE,MAAM,KAAK,EAAE;wBAC7I,QAAQ;wBACR,kBAAkB,MAAM,UAAU;wBAClC,gBAAgB,IAAI,OAAO,WAAW;wBACtC,cAAc,IAAI,CAAC,4BAA4B,CAAC;wBAChD,WAAW,IAAI,CAAC,kBAAkB,CAAC,MAAM,UAAU;wBACnD,mBAAmB;wBACnB,UAAU;wBACV,MAAM;4BAAC;4BAAW,WAAW,WAAW;4BAAW,MAAM,WAAW,CAAC,WAAW;yBAAG;wBACnF,UAAU;4BACR,eAAe,MAAM,aAAa;4BAClC,aAAa,MAAM,WAAW;4BAC9B,iBAAiB,MAAM,eAAe;4BACtC,sBAAsB,MAAM,oBAAoB;4BAChD,OAAO,MAAM,KAAK;4BAClB,aAAa,MAAM,oBAAoB,GAAG,MAAM,KAAK;wBACvD;oBACF;oBACA,UAAU,IAAI,CAAC;gBACjB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,sCAAsC,EAAE,OAAO,CAAC,CAAC,EAAE;QACpE;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAc,yBAAyB,MAAc,EAAuB;QAC1E,MAAM,YAAwB,EAAE;QAEhC,IAAI;YACF,kCAAkC;YAClC,MAAM,UAAU,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,QAAQ;YAExD,KAAK,MAAM,UAAU,QAAS;gBAC5B,IAAI,IAAI,CAAC,sBAAsB,CAAC,SAAS;oBACvC,MAAM,WAAqB;wBACzB,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,OAAO,SAAS,CAAC,CAAC,EAAE,OAAO,IAAI,EAAE;wBACtD;wBACA,MAAM;wBACN,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,IAAI;wBACvC,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI;wBAC3C,OAAO,GAAG,OAAO,OAAO,EAAE,OAAO,IAAI,EAAE;wBACvC,aAAa,GAAG,OAAO,IAAI,CAAC,SAAS,EAAE,OAAO,WAAW,IAAI,yBAAyB;wBACtF,QAAQ;wBACR,WAAW,OAAO,IAAI;wBACtB,kBAAkB,OAAO,SAAS;wBAClC,gBAAgB,IAAI,OAAO,WAAW;wBACtC,cAAc,IAAI,CAAC,8BAA8B,CAAC;wBAClD,WAAW,IAAI,CAAC,kBAAkB,CAAC,OAAO,SAAS;wBACnD,mBAAmB,IAAI,CAAC,yBAAyB,CAAC,OAAO,IAAI;wBAC7D,UAAU;wBACV,MAAM;4BAAC;4BAAO;4BAAU,OAAO,IAAI,CAAC,WAAW;yBAAG;wBAClD,UAAU;4BACR,YAAY,OAAO,IAAI;4BACvB,KAAK,OAAO,GAAG;4BACf,cAAc,OAAO,YAAY;wBACnC;oBACF;oBACA,UAAU,IAAI,CAAC;gBACjB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,yCAAyC,EAAE,OAAO,CAAC,CAAC,EAAE;QACvE;QAEA,OAAO;IACT;IAEA,yDAAyD;IACjD,mBAAmB,SAAiB,EAAU;QACpD,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAEQ,mBAAmB,UAAkB,EAAkC;QAC7E,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE;QAEnE,IAAI,WAAW,IAAI,OAAO;QAC1B,IAAI,WAAW,IAAI,OAAO;QAC1B,OAAO;IACT;IAEQ,eAAe,QAAa,EAAW;QAC7C,OAAO,SAAS,SAAS,GAAG,SAAS,YAAY,IAC1C,CAAC,SAAS,cAAc,IAAI,SAAS,SAAS,GAAG,SAAS,YAAY,GAAG,IAAI;IACtF;IAEQ,8BAA8B,QAAa,EAAU;QAC3D,IAAI,QAAQ,EAAE,aAAa;;QAE3B,kBAAkB;QAClB,MAAM,cAAc,AAAC,CAAC,SAAS,SAAS,GAAG,SAAS,YAAY,IAAI,SAAS,YAAY,GAAI;QAC7F,IAAI,cAAc,IAAI,SAAS;aAC1B,IAAI,cAAc,IAAI,SAAS;aAC/B,IAAI,cAAc,GAAG,SAAS;QAEnC,kBAAkB;QAClB,IAAI,SAAS,cAAc,EAAE,SAAS;QAEtC,eAAe;QACf,IAAI,SAAS,aAAa,GAAG,SAAS,gBAAgB,EAAE,SAAS;QAEjE,OAAO,KAAK,GAAG,CAAC,IAAI;IACtB;IAEQ,uBAAuB,IAAS,EAA6E;QACnH,MAAM,QAAQ,KAAK,KAAK,CAAC,WAAW;QACpC,MAAM,OAAO,CAAC,KAAK,IAAI,IAAI,EAAE,EAAE,WAAW;QAC1C,MAAM,UAAU,QAAQ,MAAM;QAE9B,mBAAmB;QACnB,IAAI,QAAQ,QAAQ,CAAC,UAAU,CAAC,QAAQ,QAAQ,CAAC,eAAe,QAAQ,QAAQ,CAAC,WAAW,GAAG;YAC7F,OAAO;gBAAE,MAAM;gBAAgB,MAAM;gBAAU,QAAQ;YAAU;QACnE;QAEA,IAAI,QAAQ,QAAQ,CAAC,YAAY,CAAC,QAAQ,QAAQ,CAAC,eAAe,QAAQ,QAAQ,CAAC,aAAa,GAAG;YACjG,OAAO;gBAAE,MAAM;gBAAsB,MAAM;gBAAU,QAAQ;YAAU;QACzE;QAEA,uBAAuB;QACvB,IAAI,QAAQ,QAAQ,CAAC,eAAe,CAAC,QAAQ,QAAQ,CAAC,UAAU,QAAQ,QAAQ,CAAC,UAAU,GAAG;YAC5F,OAAO;gBAAE,MAAM;gBAAgB,MAAM;gBAAU,QAAQ;YAAU;QACnE;QAEA,IAAI,QAAQ,QAAQ,CAAC,kBAAkB,QAAQ,QAAQ,CAAC,kBAAkB;YACxE,OAAO;gBAAE,MAAM;gBAAe,MAAM;gBAAU,QAAQ;YAAU;QAClE;QAEA,MAAM;QACN,IAAI,QAAQ,QAAQ,CAAC,aAAa,QAAQ,QAAQ,CAAC,kBAAkB,QAAQ,QAAQ,CAAC,WAAW;YAC/F,OAAO;gBAAE,MAAM;gBAAsB,MAAM;gBAAU,QAAQ;YAAU;QACzE;QAEA,cAAc;QACd,IAAI,QAAQ,QAAQ,CAAC,kBAAkB,QAAQ,QAAQ,CAAC,gBAAgB;YACtE,OAAO;gBAAE,MAAM;gBAAe,MAAM;gBAAU,QAAQ;YAAU;QAClE;QAEA,OAAO;IACT;IAEQ,0BAA0B,IAAS,EAAE,YAA0B,EAAU;QAC/E,IAAI,QAAQ,EAAE,aAAa;;QAE3B,qBAAqB;QACrB,IAAI,IAAI,CAAC,oBAAoB,CAAC,KAAK,IAAI,GAAG,SAAS;QAEnD,2BAA2B;QAC3B,IAAI;YAAC;YAAgB;YAAsB;SAAyB,CAAC,QAAQ,CAAC,eAAe;YAC3F,SAAS;QACX;QAEA,YAAY;QACZ,IAAI,KAAK,SAAS,KAAK,YAAY,SAAS;aACvC,IAAI,KAAK,SAAS,KAAK,YAAY,SAAS;QAEjD,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI;IAClC;IAEQ,qBAAqB,IAAY,EAAW;QAClD,MAAM,kBAAkB;YACtB;YAAe;YAAiB;YAAW;YAC3C;YAAmB;YAAa;YAAW;SAC5C;QACD,OAAO,gBAAgB,IAAI,CAAC,CAAA,SAAU,KAAK,WAAW,GAAG,QAAQ,CAAC;IACpE;IAEQ,oBAAoB,IAAY,EAAY;QAClD,MAAM,WAAW,EAAE;QACnB,MAAM,UAAU,KAAK,WAAW;QAEhC,MAAM,aAAa;YACjB,YAAY;gBAAC;gBAAY;gBAAO;gBAAW;aAAS;YACpD,OAAO;gBAAC;gBAAO;gBAAY;gBAAQ;aAAQ;YAC3C,UAAU;gBAAC;gBAAU;gBAAe;gBAAU;aAAW;YACzD,eAAe;gBAAC;gBAAe;gBAAiB;aAAW;YAC3D,YAAY;gBAAC;gBAAY;gBAAQ;aAAY;YAC7C,WAAW;gBAAC;gBAAW;gBAAU;aAAY;YAC7C,aAAa;gBAAC;gBAAa;gBAAW;aAAU;QAClD;QAEA,KAAK,MAAM,CAAC,UAAU,MAAM,IAAI,OAAO,OAAO,CAAC,YAAa;YAC1D,IAAI,MAAM,IAAI,CAAC,CAAA,OAAQ,QAAQ,QAAQ,CAAC,QAAQ;gBAC9C,SAAS,IAAI,CAAC;YAChB;QACF;QAEA,OAAO;IACT;IAEQ,qBAAqB,YAA0B,EAA2D;QAChH,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEQ,2BAA2B,cAAmB,EAAW;QAC/D,0CAA0C;QAC1C,MAAM,cAAc,KAAK,GAAG,CAAC,eAAe,QAAQ,GAAG,eAAe,aAAa;QACnF,OAAO,eAAe,KAAK,eAAe,WAAW,GAAG;IAC1D;IAEQ,6BAA6B,cAAmB,EAAU;QAChE,IAAI,QAAQ,EAAE,aAAa;;QAE3B,uCAAuC;QACvC,MAAM,WAAW;YAAC;YAAiB;YAAkB;YAAa;SAAkB;QACpF,IAAI,SAAS,IAAI,CAAC,CAAA,OAAQ,eAAe,cAAc,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ;YACrF,SAAS;QACX;QAEA,yBAAyB;QACzB,MAAM,cAAc,KAAK,GAAG,CAAC,eAAe,QAAQ,GAAG,eAAe,aAAa;QACnF,IAAI,eAAe,GAAG,SAAS;aAC1B,IAAI,eAAe,GAAG,SAAS;QAEpC,sBAAsB;QACtB,IAAI,eAAe,iBAAiB,GAAG,IAAI,SAAS;QAEpD,OAAO,KAAK,GAAG,CAAC,IAAI;IACtB;IAEQ,0BAA0B,KAAU,EAAW;QACrD,MAAM,cAAc,MAAM,oBAAoB,GAAG,MAAM,KAAK;QAC5D,OAAO,eAAe,WAAW,oBAAoB;QAC9C,MAAM,WAAW,KAAK,QAAQ,mCAAmC;;IAC1E;IAEQ,6BAA6B,KAAU,EAAU;QACvD,IAAI,QAAQ,EAAE,aAAa;;QAE3B,MAAM,cAAc,MAAM,oBAAoB,GAAG,MAAM,KAAK;QAE5D,mBAAmB;QACnB,IAAI,eAAe,UAAU,SAAS,GAAE,QAAQ;aAC3C,IAAI,eAAe,SAAS,SAAS,GAAE,OAAO;aAC9C,IAAI,eAAe,SAAS,SAAS,GAAE,OAAO;QAEnD,eAAe;QACf,IAAI,MAAM,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,UACzC,MAAM,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ;YACnD,SAAS;QACX,OAAO,IAAI,MAAM,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,aAAa;YAC/D,SAAS;QACX;QAEA,OAAO,KAAK,GAAG,CAAC,IAAI;IACtB;IAEQ,uBAAuB,MAAW,EAAW;QACnD,MAAM,qBAAqB;YAAC;YAAO;YAAQ;YAAQ;YAAO;YAAO;YAAO;SAAM;QAC9E,OAAO,mBAAmB,QAAQ,CAAC,OAAO,IAAI;IAChD;IAEQ,iBAAiB,UAAkB,EAAgB;QACzD,MAAM,eAAe;YAAC;YAAO;YAAO;SAAM,CAAC,2CAA2C;;QACtF,MAAM,eAAe;YAAC;YAAQ;YAAQ;SAAM,CAAC,qCAAqC;;QAElF,IAAI,aAAa,QAAQ,CAAC,aAAa,OAAO;QAC9C,IAAI,aAAa,QAAQ,CAAC,aAAa,OAAO;QAC9C,OAAO;IACT;IAEQ,mBAAmB,UAAkB,EAAkB;QAC7D,8CAA8C;QAC9C,OAAO;IACT;IAEQ,+BAA+B,MAAW,EAAU;QAC1D,IAAI,QAAQ,EAAE,aAAa;;QAE3B,yBAAyB;QACzB,IAAI;YAAC;YAAO;SAAM,CAAC,QAAQ,CAAC,OAAO,IAAI,GAAG,SAAS;aAC9C,IAAI;YAAC;YAAQ;SAAO,CAAC,QAAQ,CAAC,OAAO,IAAI,GAAG,SAAS;QAE1D,OAAO,KAAK,GAAG,CAAC,IAAI;IACtB;IAEQ,0BAA0B,UAAkB,EAA2D;QAC7G,OAAQ;YACN,KAAK;gBAAO,OAAO,aAAa,kBAAkB;;YAClD,KAAK;gBAAO,OAAO,cAAc,qBAAqB;;YACtD,KAAK;gBAAO,OAAO,YAAY,mBAAmB;;YAClD;gBAAS,OAAO;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 1245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/lib/preMarketGapScanner.ts"], "sourcesContent": ["import { PreMarketGapScan, Catalyst } from '@/types/trading'\nimport { PolygonAPI } from './polygon'\nimport { IBKRAPI } from './ibkr'\nimport { CatalystDetectionEngine } from './catalystDetection'\n\nexport class PreMarketGapScanner {\n  private polygonAPI: PolygonAPI\n  private ibkrAPI: IBKRAPI\n  private catalystEngine: CatalystDetectionEngine\n  private useIBKR: boolean\n  \n  // Default universe of 65+ stocks to scan\n  private readonly SCAN_UNIVERSE = [\n    // Mega caps\n    'AAPL', 'MSFT', 'GOOGL', 'GOOG', 'AMZN', 'NVDA', 'META', 'TSLA', 'BRK.B', 'UNH',\n    'JNJ', 'XOM', 'JPM', 'V', 'PG', 'HD', 'CVX', 'MA', 'BAC', 'ABBV',\n    'PFE', 'AVGO', 'KO', 'MRK', 'PEP', 'TMO', 'COST', 'DIS', 'ABT', 'ACN',\n    'MCD', 'CSCO', 'LIN', 'VZ', 'ADBE', 'WMT', 'CRM', 'NFLX', 'DHR', 'NKE',\n    'TXN', 'NEE', 'BMY', 'ORCL', 'PM', 'RTX', 'UPS', 'QCOM', 'T', 'LOW',\n    \n    // High-beta growth stocks\n    'AMD', 'CRM', 'SNOW', 'PLTR', 'ROKU', 'ZM', 'DOCU', 'PTON', 'SHOP', 'SQ',\n    'PYPL', 'UBER', 'LYFT', 'ABNB', 'COIN', 'RBLX', 'U', 'DKNG', 'CRWD', 'ZS',\n    \n    // Biotech/Pharma (catalyst-heavy)\n    'GILD', 'BIIB', 'REGN', 'VRTX', 'ILMN', 'MRNA', 'BNTX', 'AMGN', 'CELG', 'ISRG'\n  ]\n\n  constructor(polygonApiKey?: string, useIBKR: boolean = true) {\n    this.polygonAPI = new PolygonAPI(polygonApiKey)\n    this.ibkrAPI = new IBKRAPI({\n      host: '127.0.0.1',\n      port: 4002, // IB Gateway port (4002 for paper, 4001 for live)\n      clientId: 1,\n      paperTrading: true\n    })\n    this.catalystEngine = new CatalystDetectionEngine(polygonApiKey)\n    this.useIBKR = useIBKR\n  }\n\n  /**\n   * Run comprehensive pre-market gap scan\n   */\n  async runGapScan(customUniverse?: string[]): Promise<PreMarketGapScan[]> {\n    const universe = customUniverse || this.SCAN_UNIVERSE\n    const results: PreMarketGapScan[] = []\n\n    console.log(`🔍 Starting pre-market gap scan on ${universe.length} symbols...`)\n\n    try {\n      let quotes: any[] = []\n\n      if (this.useIBKR) {\n        console.log('📊 Attempting to use IBKR for market data...')\n        try {\n          // Try to connect to IBKR first\n          await this.ibkrAPI.connect()\n\n          // Get quotes from IBKR\n          quotes = await this.getIBKRQuotes(universe)\n\n          if (quotes.length > 0) {\n            console.log(`✅ Retrieved ${quotes.length} quotes from IBKR`)\n          } else {\n            throw new Error('No quotes received from IBKR')\n          }\n        } catch (ibkrError) {\n          console.warn('⚠️ IBKR connection failed, falling back to FMP:', ibkrError)\n          this.useIBKR = false\n        }\n      }\n\n      // Fallback to Polygon if IBKR failed or not enabled\n      if (!this.useIBKR || quotes.length === 0) {\n        console.log('📊 Using Polygon API for market data...')\n        quotes = await this.getPolygonQuotes(universe)\n      }\n\n      // Process each quote in parallel\n      const scanPromises = quotes.map(quote => this.processSingleStock(quote))\n      const scanResults = await Promise.all(scanPromises)\n\n      // Filter out null results and sort by gap percentage\n      const validResults = scanResults\n        .filter((result): result is PreMarketGapScan => result !== null)\n        .sort((a, b) => b.gapPercent - a.gapPercent)\n\n      console.log(`✅ Gap scan complete. Found ${validResults.length} results.`)\n\n      return validResults\n    } catch (error) {\n      console.error('Error running gap scan:', error)\n      return []\n    }\n  }\n\n  /**\n   * Get quotes from IBKR\n   */\n  private async getIBKRQuotes(symbols: string[]): Promise<any[]> {\n    try {\n      const quotes = []\n\n      // Process symbols in batches to avoid overwhelming IBKR\n      const batchSize = 10\n      for (let i = 0; i < symbols.length; i += batchSize) {\n        const batch = symbols.slice(i, i + batchSize)\n\n        const batchPromises = batch.map(async (symbol) => {\n          try {\n            const quote = await this.ibkrAPI.getMarketData(symbol)\n            if (quote) {\n              return {\n                symbol: quote.symbol,\n                price: quote.last || quote.close,\n                previousClose: quote.previousClose,\n                change: quote.change,\n                changesPercentage: quote.changePercent,\n                volume: quote.volume,\n                marketCap: quote.marketCap || 0,\n                avgVolume: quote.avgVolume || 0,\n                preMarketPrice: quote.last || quote.close,\n                preMarketChange: quote.change,\n                preMarketChangePercent: quote.changePercent\n              }\n            }\n            return null\n          } catch (error) {\n            console.error(`Error getting IBKR quote for ${symbol}:`, error)\n            return null\n          }\n        })\n\n        const batchResults = await Promise.all(batchPromises)\n        quotes.push(...batchResults.filter(q => q !== null))\n\n        // Small delay between batches\n        if (i + batchSize < symbols.length) {\n          await new Promise(resolve => setTimeout(resolve, 100))\n        }\n      }\n\n      return quotes\n    } catch (error) {\n      console.error('Error getting IBKR quotes:', error)\n      return []\n    }\n  }\n\n  /**\n   * Get quotes from Polygon API\n   */\n  private async getPolygonQuotes(symbols: string[]): Promise<any[]> {\n    try {\n      const quotes = []\n\n      // Process symbols in batches to avoid overwhelming Polygon\n      const batchSize = 10\n      for (let i = 0; i < symbols.length; i += batchSize) {\n        const batch = symbols.slice(i, i + batchSize)\n\n        const batchPromises = batch.map(async (symbol) => {\n          try {\n            // Get current quote from Polygon\n            const quote = await this.polygonAPI.getStockQuote(symbol)\n            if (quote) {\n              return {\n                symbol: quote.symbol,\n                price: quote.price,\n                previousClose: quote.previousClose,\n                change: quote.change,\n                changesPercentage: quote.changePercent,\n                volume: quote.volume,\n                marketCap: quote.marketCap || 0,\n                avgVolume: quote.avgVolume || 0,\n                preMarketPrice: quote.price, // Polygon provides real-time data\n                preMarketChange: quote.change,\n                preMarketChangePercent: quote.changePercent\n              }\n            }\n            return null\n          } catch (error) {\n            console.error(`Error getting Polygon quote for ${symbol}:`, error)\n            return null\n          }\n        })\n\n        const batchResults = await Promise.all(batchPromises)\n        quotes.push(...batchResults.filter(q => q !== null))\n\n        // Small delay between batches to respect rate limits\n        if (i + batchSize < symbols.length) {\n          await new Promise(resolve => setTimeout(resolve, 100))\n        }\n      }\n\n      return quotes\n    } catch (error) {\n      console.error('Error getting Polygon quotes:', error)\n      return []\n    }\n  }\n\n  /**\n   * Process a single stock for gap scan criteria\n   */\n  private async processSingleStock(quote: any): Promise<PreMarketGapScan | null> {\n    try {\n      const symbol = quote.symbol\n      const currentPrice = quote.preMarketPrice || quote.price\n      const previousClose = quote.previousClose\n      \n      if (!currentPrice || !previousClose || previousClose <= 0) {\n        return null\n      }\n\n      const gapPercent = ((currentPrice - previousClose) / previousClose) * 100\n      \n      // Quick filter: only process stocks with 3%+ gaps\n      if (gapPercent < 3.0) {\n        return null\n      }\n\n      // Get additional data\n      const [companyProfile, catalysts] = await Promise.all([\n        this.fmpAPI.getCompanyProfile(symbol),\n        this.catalystEngine.detectCatalysts(symbol)\n      ])\n\n      if (!companyProfile) {\n        return null\n      }\n\n      // Calculate pre-market metrics\n      const preMarketVolume = quote.volume || 0\n      const avgDailyVolume = quote.avgVolume || 1\n      const preMarketDollarVolume = preMarketVolume * currentPrice\n      const marketCap = quote.marketCap || companyProfile.mktCap || 0\n\n      // Check all criteria\n      const criteriaChecks = {\n        priceAbove1Dollar: currentPrice > 1.0,\n        gapAbove3Percent: gapPercent >= 3.0,\n        marketCapAbove800M: marketCap >= *********,\n        preMarketVolumeAbove20K: preMarketVolume >= 20000,\n        preMarketDollarVolumeAbove1M: preMarketDollarVolume >= 1000000,\n        excludesPennyStocks: currentPrice > 1.0 && marketCap >= *********,\n        hasCatalyst: catalysts.length > 0\n      }\n\n      const meetsAllCriteria = Object.values(criteriaChecks).every(check => check)\n\n      // Get the best catalyst (highest quality score)\n      const bestCatalyst = catalysts.length > 0 \n        ? catalysts.reduce((best, current) => \n            current.qualityScore > best.qualityScore ? current : best\n          )\n        : undefined\n\n      const result: PreMarketGapScan = {\n        symbol,\n        name: companyProfile.companyName || symbol,\n        sector: companyProfile.sector || 'Unknown',\n        price: currentPrice,\n        previousClose,\n        gapPercent,\n        preMarketHigh: currentPrice, // Simplified - would need intraday data for actual PMH\n        preMarketLow: currentPrice * 0.98, // Estimated based on current price\n        preMarketVolume,\n        preMarketDollarVolume,\n        marketCap,\n        averageDailyVolume: avgDailyVolume,\n        catalyst: bestCatalyst,\n        scanTime: new Date().toISOString(),\n        meetsAllCriteria,\n        criteriaChecks\n      }\n\n      return result\n    } catch (error) {\n      console.error(`Error processing ${quote.symbol}:`, error)\n      return null\n    }\n  }\n\n  /**\n   * Get filtered results that meet all Perfect-Pick criteria\n   */\n  async getPerfectPickCandidates(customUniverse?: string[]): Promise<PreMarketGapScan[]> {\n    const allResults = await this.runGapScan(customUniverse)\n    \n    return allResults.filter(result => \n      result.meetsAllCriteria && \n      result.catalyst && \n      result.catalyst.tier === 'tier_1' // Only highest tier catalysts\n    )\n  }\n\n  /**\n   * Get results by gap percentage ranges\n   */\n  async getGapRangeResults(\n    minGap: number = 3, \n    maxGap: number = 15, \n    customUniverse?: string[]\n  ): Promise<PreMarketGapScan[]> {\n    const allResults = await this.runGapScan(customUniverse)\n    \n    return allResults.filter(result => \n      result.gapPercent >= minGap && \n      result.gapPercent <= maxGap\n    )\n  }\n\n  /**\n   * Get results by catalyst type\n   */\n  async getCatalystTypeResults(\n    catalystTypes: string[], \n    customUniverse?: string[]\n  ): Promise<PreMarketGapScan[]> {\n    const allResults = await this.runGapScan(customUniverse)\n    \n    return allResults.filter(result => \n      result.catalyst && \n      catalystTypes.includes(result.catalyst.type)\n    )\n  }\n\n  /**\n   * Get scheduled scan times (4 AM, 6 AM, 8 AM, 9 AM EST)\n   */\n  getScheduledScanTimes(): Date[] {\n    const now = new Date()\n    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())\n    \n    const scanTimes = [\n      new Date(today.getTime() + 4 * 60 * 60 * 1000), // 4 AM EST\n      new Date(today.getTime() + 6 * 60 * 60 * 1000), // 6 AM EST\n      new Date(today.getTime() + 8 * 60 * 60 * 1000), // 8 AM EST\n      new Date(today.getTime() + 9 * 60 * 60 * 1000)  // 9 AM EST\n    ]\n    \n    // Adjust for EST (UTC-5) or EDT (UTC-4)\n    const isEDT = this.isDaylightSavingTime(now)\n    const offsetHours = isEDT ? 4 : 5\n    \n    return scanTimes.map(time => \n      new Date(time.getTime() + offsetHours * 60 * 60 * 1000)\n    )\n  }\n\n  /**\n   * Check if current time is during daylight saving time\n   */\n  private isDaylightSavingTime(date: Date): boolean {\n    const year = date.getFullYear()\n    \n    // DST starts second Sunday in March\n    const dstStart = new Date(year, 2, 1) // March 1st\n    dstStart.setDate(dstStart.getDate() + (7 - dstStart.getDay()) + 7) // Second Sunday\n    \n    // DST ends first Sunday in November\n    const dstEnd = new Date(year, 10, 1) // November 1st\n    dstEnd.setDate(dstEnd.getDate() + (7 - dstEnd.getDay())) // First Sunday\n    \n    return date >= dstStart && date < dstEnd\n  }\n\n  /**\n   * Get real-time updates for existing scan results\n   */\n  async updateScanResults(existingResults: PreMarketGapScan[]): Promise<PreMarketGapScan[]> {\n    const symbols = existingResults.map(result => result.symbol)\n    const updatedQuotes = await this.fmpAPI.getMultiplePreMarketQuotes(symbols)\n    \n    const updatedResults: PreMarketGapScan[] = []\n    \n    for (const quote of updatedQuotes) {\n      const existingResult = existingResults.find(r => r.symbol === quote.symbol)\n      if (!existingResult) continue\n      \n      const currentPrice = quote.preMarketPrice || quote.price\n      const gapPercent = ((currentPrice - quote.previousClose) / quote.previousClose) * 100\n      \n      const updatedResult: PreMarketGapScan = {\n        ...existingResult,\n        price: currentPrice,\n        gapPercent,\n        preMarketVolume: quote.volume || 0,\n        preMarketDollarVolume: (quote.volume || 0) * currentPrice,\n        scanTime: new Date().toISOString()\n      }\n      \n      // Re-check criteria with updated data\n      updatedResult.criteriaChecks.gapAbove3Percent = gapPercent >= 3.0\n      updatedResult.criteriaChecks.preMarketVolumeAbove20K = updatedResult.preMarketVolume >= 20000\n      updatedResult.criteriaChecks.preMarketDollarVolumeAbove1M = updatedResult.preMarketDollarVolume >= 1000000\n      updatedResult.meetsAllCriteria = Object.values(updatedResult.criteriaChecks).every(check => check)\n      \n      updatedResults.push(updatedResult)\n    }\n    \n    return updatedResults.sort((a, b) => b.gapPercent - a.gapPercent)\n  }\n\n  /**\n   * Get summary statistics for scan results\n   */\n  getScanSummary(results: PreMarketGapScan[]) {\n    const totalScanned = this.SCAN_UNIVERSE.length\n    const gapsFound = results.length\n    const perfectPicks = results.filter(r => r.meetsAllCriteria).length\n    const withCatalysts = results.filter(r => r.catalyst).length\n    \n    const avgGap = results.length > 0 \n      ? results.reduce((sum, r) => sum + r.gapPercent, 0) / results.length \n      : 0\n    \n    const sectorBreakdown = results.reduce((acc, result) => {\n      acc[result.sector] = (acc[result.sector] || 0) + 1\n      return acc\n    }, {} as Record<string, number>)\n    \n    const catalystTypeBreakdown = results\n      .filter(r => r.catalyst)\n      .reduce((acc, result) => {\n        const type = result.catalyst!.type\n        acc[type] = (acc[type] || 0) + 1\n        return acc\n      }, {} as Record<string, number>)\n    \n    return {\n      totalScanned,\n      gapsFound,\n      perfectPicks,\n      withCatalysts,\n      avgGap: Math.round(avgGap * 100) / 100,\n      sectorBreakdown,\n      catalystTypeBreakdown,\n      scanTime: new Date().toISOString()\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;AAEO,MAAM;IACH,WAAsB;IACtB,QAAgB;IAChB,eAAuC;IACvC,QAAgB;IAExB,yCAAyC;IACxB,gBAAgB;QAC/B,YAAY;QACZ;QAAQ;QAAQ;QAAS;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAS;QAC1E;QAAO;QAAO;QAAO;QAAK;QAAM;QAAM;QAAO;QAAM;QAAO;QAC1D;QAAO;QAAQ;QAAM;QAAO;QAAO;QAAO;QAAQ;QAAO;QAAO;QAChE;QAAO;QAAQ;QAAO;QAAM;QAAQ;QAAO;QAAO;QAAQ;QAAO;QACjE;QAAO;QAAO;QAAO;QAAQ;QAAM;QAAO;QAAO;QAAQ;QAAK;QAE9D,0BAA0B;QAC1B;QAAO;QAAO;QAAQ;QAAQ;QAAQ;QAAM;QAAQ;QAAQ;QAAQ;QACpE;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAK;QAAQ;QAAQ;QAErE,kCAAkC;QAClC;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;KACzE,CAAA;IAED,YAAY,aAAsB,EAAE,UAAmB,IAAI,CAAE;QAC3D,IAAI,CAAC,UAAU,GAAG,IAAI,8JAAU,CAAC;QACjC,IAAI,CAAC,OAAO,GAAG,IAAI,wJAAO,CAAC;YACzB,MAAM;YACN,MAAM;YACN,UAAU;YACV,cAAc;QAChB;QACA,IAAI,CAAC,cAAc,GAAG,IAAI,qLAAuB,CAAC;QAClD,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA;;GAEC,GACD,MAAM,WAAW,cAAyB,EAA+B;QACvE,MAAM,WAAW,kBAAkB,IAAI,CAAC,aAAa;QACrD,MAAM,UAA8B,EAAE;QAEtC,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,SAAS,MAAM,CAAC,WAAW,CAAC;QAE9E,IAAI;YACF,IAAI,SAAgB,EAAE;YAEtB,IAAI,IAAI,CAAC,OAAO,EAAE;gBAChB,QAAQ,GAAG,CAAC;gBACZ,IAAI;oBACF,+BAA+B;oBAC/B,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO;oBAE1B,uBAAuB;oBACvB,SAAS,MAAM,IAAI,CAAC,aAAa,CAAC;oBAElC,IAAI,OAAO,MAAM,GAAG,GAAG;wBACrB,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,OAAO,MAAM,CAAC,iBAAiB,CAAC;oBAC7D,OAAO;wBACL,MAAM,IAAI,MAAM;oBAClB;gBACF,EAAE,OAAO,WAAW;oBAClB,QAAQ,IAAI,CAAC,mDAAmD;oBAChE,IAAI,CAAC,OAAO,GAAG;gBACjB;YACF;YAEA,oDAAoD;YACpD,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,OAAO,MAAM,KAAK,GAAG;gBACxC,QAAQ,GAAG,CAAC;gBACZ,SAAS,MAAM,IAAI,CAAC,gBAAgB,CAAC;YACvC;YAEA,iCAAiC;YACjC,MAAM,eAAe,OAAO,GAAG,CAAC,CAAA,QAAS,IAAI,CAAC,kBAAkB,CAAC;YACjE,MAAM,cAAc,MAAM,QAAQ,GAAG,CAAC;YAEtC,qDAAqD;YACrD,MAAM,eAAe,YAClB,MAAM,CAAC,CAAC,SAAuC,WAAW,MAC1D,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,UAAU,GAAG,EAAE,UAAU;YAE7C,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,aAAa,MAAM,CAAC,SAAS,CAAC;YAExE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,EAAE;QACX;IACF;IAEA;;GAEC,GACD,MAAc,cAAc,OAAiB,EAAkB;QAC7D,IAAI;YACF,MAAM,SAAS,EAAE;YAEjB,wDAAwD;YACxD,MAAM,YAAY;YAClB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,KAAK,UAAW;gBAClD,MAAM,QAAQ,QAAQ,KAAK,CAAC,GAAG,IAAI;gBAEnC,MAAM,gBAAgB,MAAM,GAAG,CAAC,OAAO;oBACrC,IAAI;wBACF,MAAM,QAAQ,MAAM,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;wBAC/C,IAAI,OAAO;4BACT,OAAO;gCACL,QAAQ,MAAM,MAAM;gCACpB,OAAO,MAAM,IAAI,IAAI,MAAM,KAAK;gCAChC,eAAe,MAAM,aAAa;gCAClC,QAAQ,MAAM,MAAM;gCACpB,mBAAmB,MAAM,aAAa;gCACtC,QAAQ,MAAM,MAAM;gCACpB,WAAW,MAAM,SAAS,IAAI;gCAC9B,WAAW,MAAM,SAAS,IAAI;gCAC9B,gBAAgB,MAAM,IAAI,IAAI,MAAM,KAAK;gCACzC,iBAAiB,MAAM,MAAM;gCAC7B,wBAAwB,MAAM,aAAa;4BAC7C;wBACF;wBACA,OAAO;oBACT,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,OAAO,CAAC,CAAC,EAAE;wBACzD,OAAO;oBACT;gBACF;gBAEA,MAAM,eAAe,MAAM,QAAQ,GAAG,CAAC;gBACvC,OAAO,IAAI,IAAI,aAAa,MAAM,CAAC,CAAA,IAAK,MAAM;gBAE9C,8BAA8B;gBAC9B,IAAI,IAAI,YAAY,QAAQ,MAAM,EAAE;oBAClC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACnD;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO,EAAE;QACX;IACF;IAEA;;GAEC,GACD,MAAc,iBAAiB,OAAiB,EAAkB;QAChE,IAAI;YACF,MAAM,SAAS,EAAE;YAEjB,2DAA2D;YAC3D,MAAM,YAAY;YAClB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,KAAK,UAAW;gBAClD,MAAM,QAAQ,QAAQ,KAAK,CAAC,GAAG,IAAI;gBAEnC,MAAM,gBAAgB,MAAM,GAAG,CAAC,OAAO;oBACrC,IAAI;wBACF,iCAAiC;wBACjC,MAAM,QAAQ,MAAM,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC;wBAClD,IAAI,OAAO;4BACT,OAAO;gCACL,QAAQ,MAAM,MAAM;gCACpB,OAAO,MAAM,KAAK;gCAClB,eAAe,MAAM,aAAa;gCAClC,QAAQ,MAAM,MAAM;gCACpB,mBAAmB,MAAM,aAAa;gCACtC,QAAQ,MAAM,MAAM;gCACpB,WAAW,MAAM,SAAS,IAAI;gCAC9B,WAAW,MAAM,SAAS,IAAI;gCAC9B,gBAAgB,MAAM,KAAK;gCAC3B,iBAAiB,MAAM,MAAM;gCAC7B,wBAAwB,MAAM,aAAa;4BAC7C;wBACF;wBACA,OAAO;oBACT,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,OAAO,CAAC,CAAC,EAAE;wBAC5D,OAAO;oBACT;gBACF;gBAEA,MAAM,eAAe,MAAM,QAAQ,GAAG,CAAC;gBACvC,OAAO,IAAI,IAAI,aAAa,MAAM,CAAC,CAAA,IAAK,MAAM;gBAE9C,qDAAqD;gBACrD,IAAI,IAAI,YAAY,QAAQ,MAAM,EAAE;oBAClC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACnD;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO,EAAE;QACX;IACF;IAEA;;GAEC,GACD,MAAc,mBAAmB,KAAU,EAAoC;QAC7E,IAAI;YACF,MAAM,SAAS,MAAM,MAAM;YAC3B,MAAM,eAAe,MAAM,cAAc,IAAI,MAAM,KAAK;YACxD,MAAM,gBAAgB,MAAM,aAAa;YAEzC,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,iBAAiB,GAAG;gBACzD,OAAO;YACT;YAEA,MAAM,aAAa,AAAC,CAAC,eAAe,aAAa,IAAI,gBAAiB;YAEtE,kDAAkD;YAClD,IAAI,aAAa,KAAK;gBACpB,OAAO;YACT;YAEA,sBAAsB;YACtB,MAAM,CAAC,gBAAgB,UAAU,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACpD,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC;gBAC9B,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC;aACrC;YAED,IAAI,CAAC,gBAAgB;gBACnB,OAAO;YACT;YAEA,+BAA+B;YAC/B,MAAM,kBAAkB,MAAM,MAAM,IAAI;YACxC,MAAM,iBAAiB,MAAM,SAAS,IAAI;YAC1C,MAAM,wBAAwB,kBAAkB;YAChD,MAAM,YAAY,MAAM,SAAS,IAAI,eAAe,MAAM,IAAI;YAE9D,qBAAqB;YACrB,MAAM,iBAAiB;gBACrB,mBAAmB,eAAe;gBAClC,kBAAkB,cAAc;gBAChC,oBAAoB,aAAa;gBACjC,yBAAyB,mBAAmB;gBAC5C,8BAA8B,yBAAyB;gBACvD,qBAAqB,eAAe,OAAO,aAAa;gBACxD,aAAa,UAAU,MAAM,GAAG;YAClC;YAEA,MAAM,mBAAmB,OAAO,MAAM,CAAC,gBAAgB,KAAK,CAAC,CAAA,QAAS;YAEtE,gDAAgD;YAChD,MAAM,eAAe,UAAU,MAAM,GAAG,IACpC,UAAU,MAAM,CAAC,CAAC,MAAM,UACtB,QAAQ,YAAY,GAAG,KAAK,YAAY,GAAG,UAAU,QAEvD;YAEJ,MAAM,SAA2B;gBAC/B;gBACA,MAAM,eAAe,WAAW,IAAI;gBACpC,QAAQ,eAAe,MAAM,IAAI;gBACjC,OAAO;gBACP;gBACA;gBACA,eAAe;gBACf,cAAc,eAAe;gBAC7B;gBACA;gBACA;gBACA,oBAAoB;gBACpB,UAAU;gBACV,UAAU,IAAI,OAAO,WAAW;gBAChC;gBACA;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,iBAAiB,EAAE,MAAM,MAAM,CAAC,CAAC,CAAC,EAAE;YACnD,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAM,yBAAyB,cAAyB,EAA+B;QACrF,MAAM,aAAa,MAAM,IAAI,CAAC,UAAU,CAAC;QAEzC,OAAO,WAAW,MAAM,CAAC,CAAA,SACvB,OAAO,gBAAgB,IACvB,OAAO,QAAQ,IACf,OAAO,QAAQ,CAAC,IAAI,KAAK,SAAS,8BAA8B;;IAEpE;IAEA;;GAEC,GACD,MAAM,mBACJ,SAAiB,CAAC,EAClB,SAAiB,EAAE,EACnB,cAAyB,EACI;QAC7B,MAAM,aAAa,MAAM,IAAI,CAAC,UAAU,CAAC;QAEzC,OAAO,WAAW,MAAM,CAAC,CAAA,SACvB,OAAO,UAAU,IAAI,UACrB,OAAO,UAAU,IAAI;IAEzB;IAEA;;GAEC,GACD,MAAM,uBACJ,aAAuB,EACvB,cAAyB,EACI;QAC7B,MAAM,aAAa,MAAM,IAAI,CAAC,UAAU,CAAC;QAEzC,OAAO,WAAW,MAAM,CAAC,CAAA,SACvB,OAAO,QAAQ,IACf,cAAc,QAAQ,CAAC,OAAO,QAAQ,CAAC,IAAI;IAE/C;IAEA;;GAEC,GACD,wBAAgC;QAC9B,MAAM,MAAM,IAAI;QAChB,MAAM,QAAQ,IAAI,KAAK,IAAI,WAAW,IAAI,IAAI,QAAQ,IAAI,IAAI,OAAO;QAErE,MAAM,YAAY;YAChB,IAAI,KAAK,MAAM,OAAO,KAAK,IAAI,KAAK,KAAK;YACzC,IAAI,KAAK,MAAM,OAAO,KAAK,IAAI,KAAK,KAAK;YACzC,IAAI,KAAK,MAAM,OAAO,KAAK,IAAI,KAAK,KAAK;YACzC,IAAI,KAAK,MAAM,OAAO,KAAK,IAAI,KAAK,KAAK,MAAO,WAAW;SAC5D;QAED,wCAAwC;QACxC,MAAM,QAAQ,IAAI,CAAC,oBAAoB,CAAC;QACxC,MAAM,cAAc,QAAQ,IAAI;QAEhC,OAAO,UAAU,GAAG,CAAC,CAAA,OACnB,IAAI,KAAK,KAAK,OAAO,KAAK,cAAc,KAAK,KAAK;IAEtD;IAEA;;GAEC,GACD,AAAQ,qBAAqB,IAAU,EAAW;QAChD,MAAM,OAAO,KAAK,WAAW;QAE7B,oCAAoC;QACpC,MAAM,WAAW,IAAI,KAAK,MAAM,GAAG,GAAG,YAAY;;QAClD,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK,CAAC,IAAI,SAAS,MAAM,EAAE,IAAI,IAAG,gBAAgB;QAEnF,oCAAoC;QACpC,MAAM,SAAS,IAAI,KAAK,MAAM,IAAI,GAAG,eAAe;;QACpD,OAAO,OAAO,CAAC,OAAO,OAAO,KAAK,CAAC,IAAI,OAAO,MAAM,EAAE,IAAG,eAAe;QAExE,OAAO,QAAQ,YAAY,OAAO;IACpC;IAEA;;GAEC,GACD,MAAM,kBAAkB,eAAmC,EAA+B;QACxF,MAAM,UAAU,gBAAgB,GAAG,CAAC,CAAA,SAAU,OAAO,MAAM;QAC3D,MAAM,gBAAgB,MAAM,IAAI,CAAC,MAAM,CAAC,0BAA0B,CAAC;QAEnE,MAAM,iBAAqC,EAAE;QAE7C,KAAK,MAAM,SAAS,cAAe;YACjC,MAAM,iBAAiB,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,MAAM,MAAM;YAC1E,IAAI,CAAC,gBAAgB;YAErB,MAAM,eAAe,MAAM,cAAc,IAAI,MAAM,KAAK;YACxD,MAAM,aAAa,AAAC,CAAC,eAAe,MAAM,aAAa,IAAI,MAAM,aAAa,GAAI;YAElF,MAAM,gBAAkC;gBACtC,GAAG,cAAc;gBACjB,OAAO;gBACP;gBACA,iBAAiB,MAAM,MAAM,IAAI;gBACjC,uBAAuB,CAAC,MAAM,MAAM,IAAI,CAAC,IAAI;gBAC7C,UAAU,IAAI,OAAO,WAAW;YAClC;YAEA,sCAAsC;YACtC,cAAc,cAAc,CAAC,gBAAgB,GAAG,cAAc;YAC9D,cAAc,cAAc,CAAC,uBAAuB,GAAG,cAAc,eAAe,IAAI;YACxF,cAAc,cAAc,CAAC,4BAA4B,GAAG,cAAc,qBAAqB,IAAI;YACnG,cAAc,gBAAgB,GAAG,OAAO,MAAM,CAAC,cAAc,cAAc,EAAE,KAAK,CAAC,CAAA,QAAS;YAE5F,eAAe,IAAI,CAAC;QACtB;QAEA,OAAO,eAAe,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,UAAU,GAAG,EAAE,UAAU;IAClE;IAEA;;GAEC,GACD,eAAe,OAA2B,EAAE;QAC1C,MAAM,eAAe,IAAI,CAAC,aAAa,CAAC,MAAM;QAC9C,MAAM,YAAY,QAAQ,MAAM;QAChC,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,gBAAgB,EAAE,MAAM;QACnE,MAAM,gBAAgB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;QAE5D,MAAM,SAAS,QAAQ,MAAM,GAAG,IAC5B,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,UAAU,EAAE,KAAK,QAAQ,MAAM,GAClE;QAEJ,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAC,KAAK;YAC3C,GAAG,CAAC,OAAO,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI;YACjD,OAAO;QACT,GAAG,CAAC;QAEJ,MAAM,wBAAwB,QAC3B,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EACtB,MAAM,CAAC,CAAC,KAAK;YACZ,MAAM,OAAO,OAAO,QAAQ,CAAE,IAAI;YAClC,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,IAAI;YAC/B,OAAO;QACT,GAAG,CAAC;QAEN,OAAO;YACL;YACA;YACA;YACA;YACA,QAAQ,KAAK,KAAK,CAAC,SAAS,OAAO;YACnC;YACA;YACA,UAAU,IAAI,OAAO,WAAW;QAClC;IACF;AACF", "debugId": null}}, {"offset": {"line": 1667, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/lib/indicators.ts"], "sourcesContent": ["import { CandlestickData, TechnicalIndicator } from '@/types/trading'\n\nexport class TechnicalIndicators {\n  // Simple Moving Average\n  static sma(data: number[], period: number): number[] {\n    const result: number[] = []\n    for (let i = period - 1; i < data.length; i++) {\n      const sum = data.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0)\n      result.push(sum / period)\n    }\n    return result\n  }\n\n  // Exponential Moving Average\n  static ema(data: number[], period: number): number[] {\n    const result: number[] = []\n    const multiplier = 2 / (period + 1)\n    \n    // Start with SMA for first value\n    let ema = data.slice(0, period).reduce((a, b) => a + b, 0) / period\n    result.push(ema)\n    \n    for (let i = period; i < data.length; i++) {\n      ema = (data[i] * multiplier) + (ema * (1 - multiplier))\n      result.push(ema)\n    }\n    \n    return result\n  }\n\n  // Relative Strength Index\n  static rsi(data: number[], period: number = 14): number[] {\n    const gains: number[] = []\n    const losses: number[] = []\n    \n    for (let i = 1; i < data.length; i++) {\n      const change = data[i] - data[i - 1]\n      gains.push(change > 0 ? change : 0)\n      losses.push(change < 0 ? Math.abs(change) : 0)\n    }\n    \n    const avgGains = this.sma(gains, period)\n    const avgLosses = this.sma(losses, period)\n    \n    return avgGains.map((gain, i) => {\n      const rs = gain / avgLosses[i]\n      return 100 - (100 / (1 + rs))\n    })\n  }\n\n  // MACD (Moving Average Convergence Divergence)\n  static macd(data: number[], fastPeriod: number = 12, slowPeriod: number = 26, signalPeriod: number = 9) {\n    const fastEMA = this.ema(data, fastPeriod)\n    const slowEMA = this.ema(data, slowPeriod)\n    \n    // Align arrays (slowEMA starts later)\n    const startIndex = slowPeriod - fastPeriod\n    const macdLine = fastEMA.slice(startIndex).map((fast, i) => fast - slowEMA[i])\n    \n    const signalLine = this.ema(macdLine, signalPeriod)\n    const histogram = macdLine.slice(signalPeriod - 1).map((macd, i) => macd - signalLine[i])\n    \n    return {\n      macd: macdLine,\n      signal: signalLine,\n      histogram\n    }\n  }\n\n  // Bollinger Bands\n  static bollingerBands(data: number[], period: number = 20, stdDev: number = 2) {\n    const sma = this.sma(data, period)\n    const bands = sma.map((avg, i) => {\n      const slice = data.slice(i, i + period)\n      const variance = slice.reduce((sum, val) => sum + Math.pow(val - avg, 2), 0) / period\n      const standardDeviation = Math.sqrt(variance)\n      \n      return {\n        upper: avg + (standardDeviation * stdDev),\n        middle: avg,\n        lower: avg - (standardDeviation * stdDev)\n      }\n    })\n    \n    return bands\n  }\n\n  // Support and Resistance Levels\n  static findSupportResistance(candles: CandlestickData[], lookback: number = 20): { support: number[], resistance: number[] } {\n    const highs = candles.map(c => c.high)\n    const lows = candles.map(c => c.low)\n    \n    const resistance: number[] = []\n    const support: number[] = []\n    \n    for (let i = lookback; i < candles.length - lookback; i++) {\n      const currentHigh = highs[i]\n      const currentLow = lows[i]\n      \n      // Check if current high is a local maximum\n      const isResistance = highs.slice(i - lookback, i).every(h => h <= currentHigh) &&\n                          highs.slice(i + 1, i + lookback + 1).every(h => h <= currentHigh)\n      \n      // Check if current low is a local minimum\n      const isSupport = lows.slice(i - lookback, i).every(l => l >= currentLow) &&\n                       lows.slice(i + 1, i + lookback + 1).every(l => l >= currentLow)\n      \n      if (isResistance) resistance.push(currentHigh)\n      if (isSupport) support.push(currentLow)\n    }\n    \n    return { support, resistance }\n  }\n\n  // Volume analysis\n  static volumeAnalysis(candles: CandlestickData[], period: number = 20) {\n    const volumes = candles.map(c => c.volume)\n    const avgVolume = this.sma(volumes, period)\n    const currentVolume = volumes[volumes.length - 1]\n    const currentAvgVolume = avgVolume[avgVolume.length - 1]\n\n    return {\n      currentVolume,\n      averageVolume: currentAvgVolume,\n      volumeRatio: currentVolume / currentAvgVolume,\n      isHighVolume: currentVolume > currentAvgVolume * 1.5,\n      isLowVolume: currentVolume < currentAvgVolume * 0.5\n    }\n  }\n\n  // Swing Trading Analysis\n  static analyzeSwingSetup(candles: CandlestickData[]): TechnicalIndicator[] {\n    const closes = candles.map(c => c.close)\n    const indicators: TechnicalIndicator[] = []\n\n    // RSI Analysis\n    const rsi = this.rsi(closes)\n    const currentRSI = rsi[rsi.length - 1]\n\n    let rsiSignal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'\n    let rsiDescription = `RSI: ${currentRSI.toFixed(2)}`\n\n    if (currentRSI < 30) {\n      rsiSignal = 'BUY'\n      rsiDescription += ' - Oversold condition, potential bounce'\n    } else if (currentRSI > 70) {\n      rsiSignal = 'SELL'\n      rsiDescription += ' - Overbought condition, potential pullback'\n    } else {\n      rsiDescription += ' - Neutral zone'\n    }\n\n    indicators.push({\n      name: 'RSI',\n      value: currentRSI,\n      signal: rsiSignal,\n      description: rsiDescription\n    })\n\n    // Moving Average Analysis\n    const sma20 = this.sma(closes, 20)\n    const sma50 = this.sma(closes, 50)\n    const currentPrice = closes[closes.length - 1]\n    const currentSMA20 = sma20[sma20.length - 1]\n    const currentSMA50 = sma50[sma50.length - 1]\n\n    let maSignal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'\n    let maDescription = `Price vs SMA20: ${((currentPrice / currentSMA20 - 1) * 100).toFixed(2)}%`\n\n    if (currentPrice > currentSMA20 && currentSMA20 > currentSMA50) {\n      maSignal = 'BUY'\n      maDescription += ' - Bullish trend'\n    } else if (currentPrice < currentSMA20 && currentSMA20 < currentSMA50) {\n      maSignal = 'SELL'\n      maDescription += ' - Bearish trend'\n    } else {\n      maDescription += ' - Mixed signals'\n    }\n\n    indicators.push({\n      name: 'Moving Averages',\n      value: (currentPrice / currentSMA20 - 1) * 100,\n      signal: maSignal,\n      description: maDescription\n    })\n\n    // MACD Analysis\n    const macdData = this.macd(closes)\n    const currentMACD = macdData.macd[macdData.macd.length - 1]\n    const currentSignal = macdData.signal[macdData.signal.length - 1]\n    const currentHistogram = macdData.histogram[macdData.histogram.length - 1]\n\n    let macdSignal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'\n    let macdDescription = `MACD: ${currentMACD.toFixed(4)}, Signal: ${currentSignal.toFixed(4)}`\n\n    if (currentMACD > currentSignal && currentHistogram > 0) {\n      macdSignal = 'BUY'\n      macdDescription += ' - Bullish momentum'\n    } else if (currentMACD < currentSignal && currentHistogram < 0) {\n      macdSignal = 'SELL'\n      macdDescription += ' - Bearish momentum'\n    } else {\n      macdDescription += ' - Momentum shifting'\n    }\n\n    indicators.push({\n      name: 'MACD',\n      value: currentHistogram,\n      signal: macdSignal,\n      description: macdDescription\n    })\n\n    // Volume Analysis\n    const volumeData = this.volumeAnalysis(candles)\n    let volumeSignal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'\n    let volumeDescription = `Volume: ${(volumeData.volumeRatio * 100).toFixed(0)}% of average`\n\n    if (volumeData.isHighVolume) {\n      volumeSignal = 'BUY'\n      volumeDescription += ' - High volume confirms move'\n    } else if (volumeData.isLowVolume) {\n      volumeSignal = 'SELL'\n      volumeDescription += ' - Low volume, weak conviction'\n    } else {\n      volumeDescription += ' - Normal volume'\n    }\n\n    indicators.push({\n      name: 'Volume',\n      value: volumeData.volumeRatio,\n      signal: volumeSignal,\n      description: volumeDescription\n    })\n\n    return indicators\n  }\n}\n"], "names": [], "mappings": ";;;;AAEO,MAAM;IACX,wBAAwB;IACxB,OAAO,IAAI,IAAc,EAAE,MAAc,EAAY;QACnD,MAAM,SAAmB,EAAE;QAC3B,IAAK,IAAI,IAAI,SAAS,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YAC7C,MAAM,MAAM,KAAK,KAAK,CAAC,IAAI,SAAS,GAAG,IAAI,GAAG,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG;YACtE,OAAO,IAAI,CAAC,MAAM;QACpB;QACA,OAAO;IACT;IAEA,6BAA6B;IAC7B,OAAO,IAAI,IAAc,EAAE,MAAc,EAAY;QACnD,MAAM,SAAmB,EAAE;QAC3B,MAAM,aAAa,IAAI,CAAC,SAAS,CAAC;QAElC,iCAAiC;QACjC,IAAI,MAAM,KAAK,KAAK,CAAC,GAAG,QAAQ,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG,KAAK;QAC7D,OAAO,IAAI,CAAC;QAEZ,IAAK,IAAI,IAAI,QAAQ,IAAI,KAAK,MAAM,EAAE,IAAK;YACzC,MAAM,AAAC,IAAI,CAAC,EAAE,GAAG,aAAe,MAAM,CAAC,IAAI,UAAU;YACrD,OAAO,IAAI,CAAC;QACd;QAEA,OAAO;IACT;IAEA,0BAA0B;IAC1B,OAAO,IAAI,IAAc,EAAE,SAAiB,EAAE,EAAY;QACxD,MAAM,QAAkB,EAAE;QAC1B,MAAM,SAAmB,EAAE;QAE3B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YACpC,MAAM,SAAS,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE;YACpC,MAAM,IAAI,CAAC,SAAS,IAAI,SAAS;YACjC,OAAO,IAAI,CAAC,SAAS,IAAI,KAAK,GAAG,CAAC,UAAU;QAC9C;QAEA,MAAM,WAAW,IAAI,CAAC,GAAG,CAAC,OAAO;QACjC,MAAM,YAAY,IAAI,CAAC,GAAG,CAAC,QAAQ;QAEnC,OAAO,SAAS,GAAG,CAAC,CAAC,MAAM;YACzB,MAAM,KAAK,OAAO,SAAS,CAAC,EAAE;YAC9B,OAAO,MAAO,MAAM,CAAC,IAAI,EAAE;QAC7B;IACF;IAEA,+CAA+C;IAC/C,OAAO,KAAK,IAAc,EAAE,aAAqB,EAAE,EAAE,aAAqB,EAAE,EAAE,eAAuB,CAAC,EAAE;QACtG,MAAM,UAAU,IAAI,CAAC,GAAG,CAAC,MAAM;QAC/B,MAAM,UAAU,IAAI,CAAC,GAAG,CAAC,MAAM;QAE/B,sCAAsC;QACtC,MAAM,aAAa,aAAa;QAChC,MAAM,WAAW,QAAQ,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC,MAAM,IAAM,OAAO,OAAO,CAAC,EAAE;QAE7E,MAAM,aAAa,IAAI,CAAC,GAAG,CAAC,UAAU;QACtC,MAAM,YAAY,SAAS,KAAK,CAAC,eAAe,GAAG,GAAG,CAAC,CAAC,MAAM,IAAM,OAAO,UAAU,CAAC,EAAE;QAExF,OAAO;YACL,MAAM;YACN,QAAQ;YACR;QACF;IACF;IAEA,kBAAkB;IAClB,OAAO,eAAe,IAAc,EAAE,SAAiB,EAAE,EAAE,SAAiB,CAAC,EAAE;QAC7E,MAAM,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM;QAC3B,MAAM,QAAQ,IAAI,GAAG,CAAC,CAAC,KAAK;YAC1B,MAAM,QAAQ,KAAK,KAAK,CAAC,GAAG,IAAI;YAChC,MAAM,WAAW,MAAM,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,KAAK,GAAG,CAAC,MAAM,KAAK,IAAI,KAAK;YAC/E,MAAM,oBAAoB,KAAK,IAAI,CAAC;YAEpC,OAAO;gBACL,OAAO,MAAO,oBAAoB;gBAClC,QAAQ;gBACR,OAAO,MAAO,oBAAoB;YACpC;QACF;QAEA,OAAO;IACT;IAEA,gCAAgC;IAChC,OAAO,sBAAsB,OAA0B,EAAE,WAAmB,EAAE,EAA+C;QAC3H,MAAM,QAAQ,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;QACrC,MAAM,OAAO,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG;QAEnC,MAAM,aAAuB,EAAE;QAC/B,MAAM,UAAoB,EAAE;QAE5B,IAAK,IAAI,IAAI,UAAU,IAAI,QAAQ,MAAM,GAAG,UAAU,IAAK;YACzD,MAAM,cAAc,KAAK,CAAC,EAAE;YAC5B,MAAM,aAAa,IAAI,CAAC,EAAE;YAE1B,2CAA2C;YAC3C,MAAM,eAAe,MAAM,KAAK,CAAC,IAAI,UAAU,GAAG,KAAK,CAAC,CAAA,IAAK,KAAK,gBAC9C,MAAM,KAAK,CAAC,IAAI,GAAG,IAAI,WAAW,GAAG,KAAK,CAAC,CAAA,IAAK,KAAK;YAEzE,0CAA0C;YAC1C,MAAM,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,GAAG,KAAK,CAAC,CAAA,IAAK,KAAK,eAC7C,KAAK,KAAK,CAAC,IAAI,GAAG,IAAI,WAAW,GAAG,KAAK,CAAC,CAAA,IAAK,KAAK;YAErE,IAAI,cAAc,WAAW,IAAI,CAAC;YAClC,IAAI,WAAW,QAAQ,IAAI,CAAC;QAC9B;QAEA,OAAO;YAAE;YAAS;QAAW;IAC/B;IAEA,kBAAkB;IAClB,OAAO,eAAe,OAA0B,EAAE,SAAiB,EAAE,EAAE;QACrE,MAAM,UAAU,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;QACzC,MAAM,YAAY,IAAI,CAAC,GAAG,CAAC,SAAS;QACpC,MAAM,gBAAgB,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE;QACjD,MAAM,mBAAmB,SAAS,CAAC,UAAU,MAAM,GAAG,EAAE;QAExD,OAAO;YACL;YACA,eAAe;YACf,aAAa,gBAAgB;YAC7B,cAAc,gBAAgB,mBAAmB;YACjD,aAAa,gBAAgB,mBAAmB;QAClD;IACF;IAEA,yBAAyB;IACzB,OAAO,kBAAkB,OAA0B,EAAwB;QACzE,MAAM,SAAS,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;QACvC,MAAM,aAAmC,EAAE;QAE3C,eAAe;QACf,MAAM,MAAM,IAAI,CAAC,GAAG,CAAC;QACrB,MAAM,aAAa,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;QAEtC,IAAI,YAAwC;QAC5C,IAAI,iBAAiB,CAAC,KAAK,EAAE,WAAW,OAAO,CAAC,IAAI;QAEpD,IAAI,aAAa,IAAI;YACnB,YAAY;YACZ,kBAAkB;QACpB,OAAO,IAAI,aAAa,IAAI;YAC1B,YAAY;YACZ,kBAAkB;QACpB,OAAO;YACL,kBAAkB;QACpB;QAEA,WAAW,IAAI,CAAC;YACd,MAAM;YACN,OAAO;YACP,QAAQ;YACR,aAAa;QACf;QAEA,0BAA0B;QAC1B,MAAM,QAAQ,IAAI,CAAC,GAAG,CAAC,QAAQ;QAC/B,MAAM,QAAQ,IAAI,CAAC,GAAG,CAAC,QAAQ;QAC/B,MAAM,eAAe,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;QAC9C,MAAM,eAAe,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;QAC5C,MAAM,eAAe,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;QAE5C,IAAI,WAAuC;QAC3C,IAAI,gBAAgB,CAAC,gBAAgB,EAAE,CAAC,CAAC,eAAe,eAAe,CAAC,IAAI,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAE9F,IAAI,eAAe,gBAAgB,eAAe,cAAc;YAC9D,WAAW;YACX,iBAAiB;QACnB,OAAO,IAAI,eAAe,gBAAgB,eAAe,cAAc;YACrE,WAAW;YACX,iBAAiB;QACnB,OAAO;YACL,iBAAiB;QACnB;QAEA,WAAW,IAAI,CAAC;YACd,MAAM;YACN,OAAO,CAAC,eAAe,eAAe,CAAC,IAAI;YAC3C,QAAQ;YACR,aAAa;QACf;QAEA,gBAAgB;QAChB,MAAM,WAAW,IAAI,CAAC,IAAI,CAAC;QAC3B,MAAM,cAAc,SAAS,IAAI,CAAC,SAAS,IAAI,CAAC,MAAM,GAAG,EAAE;QAC3D,MAAM,gBAAgB,SAAS,MAAM,CAAC,SAAS,MAAM,CAAC,MAAM,GAAG,EAAE;QACjE,MAAM,mBAAmB,SAAS,SAAS,CAAC,SAAS,SAAS,CAAC,MAAM,GAAG,EAAE;QAE1E,IAAI,aAAyC;QAC7C,IAAI,kBAAkB,CAAC,MAAM,EAAE,YAAY,OAAO,CAAC,GAAG,UAAU,EAAE,cAAc,OAAO,CAAC,IAAI;QAE5F,IAAI,cAAc,iBAAiB,mBAAmB,GAAG;YACvD,aAAa;YACb,mBAAmB;QACrB,OAAO,IAAI,cAAc,iBAAiB,mBAAmB,GAAG;YAC9D,aAAa;YACb,mBAAmB;QACrB,OAAO;YACL,mBAAmB;QACrB;QAEA,WAAW,IAAI,CAAC;YACd,MAAM;YACN,OAAO;YACP,QAAQ;YACR,aAAa;QACf;QAEA,kBAAkB;QAClB,MAAM,aAAa,IAAI,CAAC,cAAc,CAAC;QACvC,IAAI,eAA2C;QAC/C,IAAI,oBAAoB,CAAC,QAAQ,EAAE,CAAC,WAAW,WAAW,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,YAAY,CAAC;QAE1F,IAAI,WAAW,YAAY,EAAE;YAC3B,eAAe;YACf,qBAAqB;QACvB,OAAO,IAAI,WAAW,WAAW,EAAE;YACjC,eAAe;YACf,qBAAqB;QACvB,OAAO;YACL,qBAAqB;QACvB;QAEA,WAAW,IAAI,CAAC;YACd,MAAM;YACN,OAAO,WAAW,WAAW;YAC7B,QAAQ;YACR,aAAa;QACf;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1870, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/lib/technicalGateAnalysis.ts"], "sourcesContent": ["import { TechnicalGateAnalysis, CandlestickData } from '@/types/trading'\nimport { TechnicalIndicators } from './indicators'\nimport { PolygonAPI } from './polygon'\n\nexport class TechnicalGateAnalysis {\n  private polygonAPI: PolygonAPI\n\n  constructor(polygonApiKey?: string) {\n    this.polygonAPI = new PolygonAPI(polygonApiKey)\n  }\n\n  /**\n   * Perform comprehensive technical gate analysis\n   */\n  async analyzeTechnicalGate(symbol: string): Promise<TechnicalGateAnalysis | null> {\n    try {\n      // Get historical data (need at least 200 days for SMA200)\n      const historicalData = await this.getHistoricalData(symbol, 250)\n      \n      if (!historicalData || historicalData.length < 200) {\n        console.error(`Insufficient data for ${symbol} - need at least 200 days`)\n        return null\n      }\n\n      const currentPrice = historicalData[historicalData.length - 1].close\n      \n      // Calculate technical indicators\n      const sma200 = TechnicalIndicators.calculateSMA(historicalData, 200)\n      const ema8 = TechnicalIndicators.calculateEMA(historicalData, 8)\n      const vwap = this.calculateVWAP(historicalData.slice(-20)) // 20-day VWAP\n      \n      // Analyze trend confirmation\n      const dailyTrendConfirmed = this.analyzeDailyTrend(historicalData)\n      \n      // Check moving average conditions\n      const aboveSMA200 = currentPrice > sma200[sma200.length - 1]\n      const aboveEMA8 = currentPrice > ema8[ema8.length - 1]\n      const respectsEMA8 = this.checkEMA8Respect(historicalData, ema8)\n      \n      // Check for all-time high\n      const isAtAllTimeHigh = this.checkAllTimeHigh(historicalData)\n      \n      // Check for clean breakout\n      const hasCleanBreakout = this.checkCleanBreakout(historicalData)\n      \n      // Check volume expansion\n      const volumeExpansion = this.checkVolumeExpansion(historicalData)\n      \n      // Calculate resistance and support levels\n      const resistanceLevels = this.calculateResistanceLevels(historicalData)\n      const supportLevels = this.calculateSupportLevels(historicalData)\n      \n      // Calculate overall grade and score\n      const gateScore = this.calculateGateScore({\n        dailyTrendConfirmed,\n        aboveSMA200,\n        aboveEMA8,\n        respectsEMA8,\n        isAtAllTimeHigh,\n        hasCleanBreakout,\n        volumeExpansion\n      })\n      \n      const overallGrade = this.calculateOverallGrade(gateScore)\n      \n      const analysis: TechnicalGateAnalysis = {\n        symbol,\n        dailyTrendConfirmed,\n        aboveSMA200,\n        aboveEMA8,\n        respectsEMA8,\n        isAtAllTimeHigh,\n        hasCleanBreakout,\n        volumeExpansion,\n        overallGrade,\n        gateScore,\n        resistanceLevels,\n        supportLevels,\n        keyTechnicalLevels: {\n          sma200: sma200[sma200.length - 1],\n          ema8: ema8[ema8.length - 1],\n          vwap,\n          previousHigh: Math.max(...historicalData.slice(-20).map(d => d.high)),\n          previousLow: Math.min(...historicalData.slice(-20).map(d => d.low))\n        }\n      }\n      \n      return analysis\n    } catch (error) {\n      console.error(`Error analyzing technical gate for ${symbol}:`, error)\n      return null\n    }\n  }\n\n  /**\n   * Get historical candlestick data\n   */\n  private async getHistoricalData(symbol: string, days: number): Promise<CandlestickData[]> {\n    try {\n      const endDate = new Date()\n      const startDate = new Date()\n      startDate.setDate(startDate.getDate() - days)\n      \n      return await this.polygonAPI.getHistoricalData(\n        symbol,\n        startDate.toISOString().split('T')[0],\n        endDate.toISOString().split('T')[0],\n        '1',\n        'day'\n      )\n    } catch (error) {\n      console.error(`Error fetching historical data for ${symbol}:`, error)\n      return []\n    }\n  }\n\n  /**\n   * Analyze daily trend confirmation (higher highs, higher lows over 20+ days)\n   */\n  private analyzeDailyTrend(data: CandlestickData[]): boolean {\n    if (data.length < 20) return false\n    \n    const recent20Days = data.slice(-20)\n    const first10Days = recent20Days.slice(0, 10)\n    const last10Days = recent20Days.slice(10)\n    \n    const firstPeriodHigh = Math.max(...first10Days.map(d => d.high))\n    const firstPeriodLow = Math.min(...first10Days.map(d => d.low))\n    const lastPeriodHigh = Math.max(...last10Days.map(d => d.high))\n    const lastPeriodLow = Math.min(...last10Days.map(d => d.low))\n    \n    // Check for higher highs and higher lows\n    return lastPeriodHigh > firstPeriodHigh && lastPeriodLow > firstPeriodLow\n  }\n\n  /**\n   * Check if stock consistently respects/reclaims 8-EMA\n   */\n  private checkEMA8Respect(data: CandlestickData[], ema8: number[]): boolean {\n    if (data.length < 20 || ema8.length < 20) return false\n    \n    const recent20Days = data.slice(-20)\n    const recent20EMA = ema8.slice(-20)\n    \n    let respectCount = 0\n    \n    for (let i = 0; i < recent20Days.length; i++) {\n      const candle = recent20Days[i]\n      const emaValue = recent20EMA[i]\n      \n      // Check if low didn't break significantly below EMA8 (allow 2% cushion)\n      if (candle.low >= emaValue * 0.98) {\n        respectCount++\n      }\n    }\n    \n    // Stock respects EMA8 if it holds above it 70% of the time\n    return respectCount / recent20Days.length >= 0.7\n  }\n\n  /**\n   * Check if stock is at or near all-time high\n   */\n  private checkAllTimeHigh(data: CandlestickData[]): boolean {\n    const currentPrice = data[data.length - 1].close\n    const allTimeHigh = Math.max(...data.map(d => d.high))\n    \n    // Consider \"at ATH\" if within 5% of all-time high\n    return currentPrice >= allTimeHigh * 0.95\n  }\n\n  /**\n   * Check for clean breakout from consolidation patterns\n   */\n  private checkCleanBreakout(data: CandlestickData[]): boolean {\n    if (data.length < 30) return false\n    \n    const recent30Days = data.slice(-30)\n    const last5Days = recent30Days.slice(-5)\n    const consolidationPeriod = recent30Days.slice(-30, -5)\n    \n    // Calculate consolidation range\n    const consolidationHigh = Math.max(...consolidationPeriod.map(d => d.high))\n    const consolidationLow = Math.min(...consolidationPeriod.map(d => d.low))\n    const consolidationRange = (consolidationHigh - consolidationLow) / consolidationLow\n    \n    // Check if recent price broke above consolidation with volume\n    const recentHigh = Math.max(...last5Days.map(d => d.high))\n    const recentVolume = last5Days.reduce((sum, d) => sum + d.volume, 0) / last5Days.length\n    const avgVolume = consolidationPeriod.reduce((sum, d) => sum + d.volume, 0) / consolidationPeriod.length\n    \n    // Clean breakout criteria:\n    // 1. Consolidation range < 20% (tight consolidation)\n    // 2. Recent high > consolidation high\n    // 3. Volume expansion on breakout\n    return consolidationRange < 0.20 && \n           recentHigh > consolidationHigh && \n           recentVolume > avgVolume * 1.5\n  }\n\n  /**\n   * Check for volume expansion on breakout days\n   */\n  private checkVolumeExpansion(data: CandlestickData[]): boolean {\n    if (data.length < 20) return false\n    \n    const recent5Days = data.slice(-5)\n    const previous20Days = data.slice(-25, -5)\n    \n    const recentAvgVolume = recent5Days.reduce((sum, d) => sum + d.volume, 0) / recent5Days.length\n    const historicalAvgVolume = previous20Days.reduce((sum, d) => sum + d.volume, 0) / previous20Days.length\n    \n    // Volume expansion if recent volume is 150%+ of historical average\n    return recentAvgVolume > historicalAvgVolume * 1.5\n  }\n\n  /**\n   * Calculate VWAP (Volume Weighted Average Price)\n   */\n  private calculateVWAP(data: CandlestickData[]): number {\n    let totalVolume = 0\n    let totalVolumePrice = 0\n    \n    for (const candle of data) {\n      const typicalPrice = (candle.high + candle.low + candle.close) / 3\n      totalVolumePrice += typicalPrice * candle.volume\n      totalVolume += candle.volume\n    }\n    \n    return totalVolume > 0 ? totalVolumePrice / totalVolume : 0\n  }\n\n  /**\n   * Calculate resistance levels using pivot highs\n   */\n  private calculateResistanceLevels(data: CandlestickData[]): number[] {\n    const resistanceLevels: number[] = []\n    const lookback = 5 // Look for pivots with 5 days on each side\n    \n    for (let i = lookback; i < data.length - lookback; i++) {\n      const current = data[i]\n      let isPivotHigh = true\n      \n      // Check if current high is higher than surrounding highs\n      for (let j = i - lookback; j <= i + lookback; j++) {\n        if (j !== i && data[j].high >= current.high) {\n          isPivotHigh = false\n          break\n        }\n      }\n      \n      if (isPivotHigh) {\n        resistanceLevels.push(current.high)\n      }\n    }\n    \n    // Return top 5 most recent resistance levels\n    return resistanceLevels.slice(-5).sort((a, b) => b - a)\n  }\n\n  /**\n   * Calculate support levels using pivot lows\n   */\n  private calculateSupportLevels(data: CandlestickData[]): number[] {\n    const supportLevels: number[] = []\n    const lookback = 5\n    \n    for (let i = lookback; i < data.length - lookback; i++) {\n      const current = data[i]\n      let isPivotLow = true\n      \n      // Check if current low is lower than surrounding lows\n      for (let j = i - lookback; j <= i + lookback; j++) {\n        if (j !== i && data[j].low <= current.low) {\n          isPivotLow = false\n          break\n        }\n      }\n      \n      if (isPivotLow) {\n        supportLevels.push(current.low)\n      }\n    }\n    \n    // Return top 5 most recent support levels\n    return supportLevels.slice(-5).sort((a, b) => b - a)\n  }\n\n  /**\n   * Calculate overall gate score (0-100)\n   */\n  private calculateGateScore(conditions: {\n    dailyTrendConfirmed: boolean\n    aboveSMA200: boolean\n    aboveEMA8: boolean\n    respectsEMA8: boolean\n    isAtAllTimeHigh: boolean\n    hasCleanBreakout: boolean\n    volumeExpansion: boolean\n  }): number {\n    let score = 0\n    \n    // Required conditions (higher weight)\n    if (conditions.dailyTrendConfirmed) score += 20\n    if (conditions.aboveSMA200) score += 20\n    if (conditions.aboveEMA8) score += 15\n    \n    // Premium conditions (bonus points)\n    if (conditions.respectsEMA8) score += 15\n    if (conditions.isAtAllTimeHigh) score += 15\n    if (conditions.hasCleanBreakout) score += 10\n    if (conditions.volumeExpansion) score += 5\n    \n    return Math.min(100, score)\n  }\n\n  /**\n   * Calculate overall grade based on score\n   */\n  private calculateOverallGrade(score: number): 'A' | 'B' | 'C' | 'D' | 'F' {\n    if (score >= 90) return 'A'\n    if (score >= 80) return 'B'\n    if (score >= 70) return 'C'\n    if (score >= 60) return 'D'\n    return 'F'\n  }\n\n  /**\n   * Batch analyze multiple symbols\n   */\n  async batchAnalyzeTechnicalGates(symbols: string[]): Promise<TechnicalGateAnalysis[]> {\n    const results: TechnicalGateAnalysis[] = []\n    \n    // Process in chunks to avoid API rate limits\n    const chunkSize = 5\n    for (let i = 0; i < symbols.length; i += chunkSize) {\n      const chunk = symbols.slice(i, i + chunkSize)\n      const chunkPromises = chunk.map(symbol => this.analyzeTechnicalGate(symbol))\n      const chunkResults = await Promise.all(chunkPromises)\n      \n      results.push(...chunkResults.filter((result): result is TechnicalGateAnalysis => result !== null))\n      \n      // Small delay between chunks\n      if (i + chunkSize < symbols.length) {\n        await new Promise(resolve => setTimeout(resolve, 1000))\n      }\n    }\n    \n    return results.sort((a, b) => b.gateScore - a.gateScore)\n  }\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;AAEO,MAAM;IACH,WAAsB;IAE9B,YAAY,aAAsB,CAAE;QAClC,IAAI,CAAC,UAAU,GAAG,IAAI,8JAAU,CAAC;IACnC;IAEA;;GAEC,GACD,MAAM,qBAAqB,MAAc,EAAyC;QAChF,IAAI;YACF,0DAA0D;YAC1D,MAAM,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ;YAE5D,IAAI,CAAC,kBAAkB,eAAe,MAAM,GAAG,KAAK;gBAClD,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,OAAO,yBAAyB,CAAC;gBACxE,OAAO;YACT;YAEA,MAAM,eAAe,cAAc,CAAC,eAAe,MAAM,GAAG,EAAE,CAAC,KAAK;YAEpE,iCAAiC;YACjC,MAAM,SAAS,0KAAmB,CAAC,YAAY,CAAC,gBAAgB;YAChE,MAAM,OAAO,0KAAmB,CAAC,YAAY,CAAC,gBAAgB;YAC9D,MAAM,OAAO,IAAI,CAAC,aAAa,CAAC,eAAe,KAAK,CAAC,CAAC,KAAK,cAAc;;YAEzE,6BAA6B;YAC7B,MAAM,sBAAsB,IAAI,CAAC,iBAAiB,CAAC;YAEnD,kCAAkC;YAClC,MAAM,cAAc,eAAe,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;YAC5D,MAAM,YAAY,eAAe,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;YACtD,MAAM,eAAe,IAAI,CAAC,gBAAgB,CAAC,gBAAgB;YAE3D,0BAA0B;YAC1B,MAAM,kBAAkB,IAAI,CAAC,gBAAgB,CAAC;YAE9C,2BAA2B;YAC3B,MAAM,mBAAmB,IAAI,CAAC,kBAAkB,CAAC;YAEjD,yBAAyB;YACzB,MAAM,kBAAkB,IAAI,CAAC,oBAAoB,CAAC;YAElD,0CAA0C;YAC1C,MAAM,mBAAmB,IAAI,CAAC,yBAAyB,CAAC;YACxD,MAAM,gBAAgB,IAAI,CAAC,sBAAsB,CAAC;YAElD,oCAAoC;YACpC,MAAM,YAAY,IAAI,CAAC,kBAAkB,CAAC;gBACxC;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;YAEA,MAAM,eAAe,IAAI,CAAC,qBAAqB,CAAC;YAEhD,MAAM,WAAkC;gBACtC;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA,oBAAoB;oBAClB,QAAQ,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;oBACjC,MAAM,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;oBAC3B;oBACA,cAAc,KAAK,GAAG,IAAI,eAAe,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;oBACnE,aAAa,KAAK,GAAG,IAAI,eAAe,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG;gBACnE;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,OAAO,CAAC,CAAC,EAAE;YAC/D,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAc,kBAAkB,MAAc,EAAE,IAAY,EAA8B;QACxF,IAAI;YACF,MAAM,UAAU,IAAI;YACpB,MAAM,YAAY,IAAI;YACtB,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;YAExC,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAC5C,QACA,UAAU,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EACrC,QAAQ,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EACnC,KACA;QAEJ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,OAAO,CAAC,CAAC,EAAE;YAC/D,OAAO,EAAE;QACX;IACF;IAEA;;GAEC,GACD,AAAQ,kBAAkB,IAAuB,EAAW;QAC1D,IAAI,KAAK,MAAM,GAAG,IAAI,OAAO;QAE7B,MAAM,eAAe,KAAK,KAAK,CAAC,CAAC;QACjC,MAAM,cAAc,aAAa,KAAK,CAAC,GAAG;QAC1C,MAAM,aAAa,aAAa,KAAK,CAAC;QAEtC,MAAM,kBAAkB,KAAK,GAAG,IAAI,YAAY,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;QAC/D,MAAM,iBAAiB,KAAK,GAAG,IAAI,YAAY,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG;QAC7D,MAAM,iBAAiB,KAAK,GAAG,IAAI,WAAW,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;QAC7D,MAAM,gBAAgB,KAAK,GAAG,IAAI,WAAW,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG;QAE3D,yCAAyC;QACzC,OAAO,iBAAiB,mBAAmB,gBAAgB;IAC7D;IAEA;;GAEC,GACD,AAAQ,iBAAiB,IAAuB,EAAE,IAAc,EAAW;QACzE,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,MAAM,GAAG,IAAI,OAAO;QAEjD,MAAM,eAAe,KAAK,KAAK,CAAC,CAAC;QACjC,MAAM,cAAc,KAAK,KAAK,CAAC,CAAC;QAEhC,IAAI,eAAe;QAEnB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;YAC5C,MAAM,SAAS,YAAY,CAAC,EAAE;YAC9B,MAAM,WAAW,WAAW,CAAC,EAAE;YAE/B,wEAAwE;YACxE,IAAI,OAAO,GAAG,IAAI,WAAW,MAAM;gBACjC;YACF;QACF;QAEA,2DAA2D;QAC3D,OAAO,eAAe,aAAa,MAAM,IAAI;IAC/C;IAEA;;GAEC,GACD,AAAQ,iBAAiB,IAAuB,EAAW;QACzD,MAAM,eAAe,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,CAAC,KAAK;QAChD,MAAM,cAAc,KAAK,GAAG,IAAI,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;QAEpD,kDAAkD;QAClD,OAAO,gBAAgB,cAAc;IACvC;IAEA;;GAEC,GACD,AAAQ,mBAAmB,IAAuB,EAAW;QAC3D,IAAI,KAAK,MAAM,GAAG,IAAI,OAAO;QAE7B,MAAM,eAAe,KAAK,KAAK,CAAC,CAAC;QACjC,MAAM,YAAY,aAAa,KAAK,CAAC,CAAC;QACtC,MAAM,sBAAsB,aAAa,KAAK,CAAC,CAAC,IAAI,CAAC;QAErD,gCAAgC;QAChC,MAAM,oBAAoB,KAAK,GAAG,IAAI,oBAAoB,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;QACzE,MAAM,mBAAmB,KAAK,GAAG,IAAI,oBAAoB,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG;QACvE,MAAM,qBAAqB,CAAC,oBAAoB,gBAAgB,IAAI;QAEpE,8DAA8D;QAC9D,MAAM,aAAa,KAAK,GAAG,IAAI,UAAU,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;QACxD,MAAM,eAAe,UAAU,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE,KAAK,UAAU,MAAM;QACvF,MAAM,YAAY,oBAAoB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE,KAAK,oBAAoB,MAAM;QAExG,2BAA2B;QAC3B,qDAAqD;QACrD,sCAAsC;QACtC,kCAAkC;QAClC,OAAO,qBAAqB,QACrB,aAAa,qBACb,eAAe,YAAY;IACpC;IAEA;;GAEC,GACD,AAAQ,qBAAqB,IAAuB,EAAW;QAC7D,IAAI,KAAK,MAAM,GAAG,IAAI,OAAO;QAE7B,MAAM,cAAc,KAAK,KAAK,CAAC,CAAC;QAChC,MAAM,iBAAiB,KAAK,KAAK,CAAC,CAAC,IAAI,CAAC;QAExC,MAAM,kBAAkB,YAAY,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE,KAAK,YAAY,MAAM;QAC9F,MAAM,sBAAsB,eAAe,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE,KAAK,eAAe,MAAM;QAExG,mEAAmE;QACnE,OAAO,kBAAkB,sBAAsB;IACjD;IAEA;;GAEC,GACD,AAAQ,cAAc,IAAuB,EAAU;QACrD,IAAI,cAAc;QAClB,IAAI,mBAAmB;QAEvB,KAAK,MAAM,UAAU,KAAM;YACzB,MAAM,eAAe,CAAC,OAAO,IAAI,GAAG,OAAO,GAAG,GAAG,OAAO,KAAK,IAAI;YACjE,oBAAoB,eAAe,OAAO,MAAM;YAChD,eAAe,OAAO,MAAM;QAC9B;QAEA,OAAO,cAAc,IAAI,mBAAmB,cAAc;IAC5D;IAEA;;GAEC,GACD,AAAQ,0BAA0B,IAAuB,EAAY;QACnE,MAAM,mBAA6B,EAAE;QACrC,MAAM,WAAW,EAAE,2CAA2C;;QAE9D,IAAK,IAAI,IAAI,UAAU,IAAI,KAAK,MAAM,GAAG,UAAU,IAAK;YACtD,MAAM,UAAU,IAAI,CAAC,EAAE;YACvB,IAAI,cAAc;YAElB,yDAAyD;YACzD,IAAK,IAAI,IAAI,IAAI,UAAU,KAAK,IAAI,UAAU,IAAK;gBACjD,IAAI,MAAM,KAAK,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,QAAQ,IAAI,EAAE;oBAC3C,cAAc;oBACd;gBACF;YACF;YAEA,IAAI,aAAa;gBACf,iBAAiB,IAAI,CAAC,QAAQ,IAAI;YACpC;QACF;QAEA,6CAA6C;QAC7C,OAAO,iBAAiB,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;IACvD;IAEA;;GAEC,GACD,AAAQ,uBAAuB,IAAuB,EAAY;QAChE,MAAM,gBAA0B,EAAE;QAClC,MAAM,WAAW;QAEjB,IAAK,IAAI,IAAI,UAAU,IAAI,KAAK,MAAM,GAAG,UAAU,IAAK;YACtD,MAAM,UAAU,IAAI,CAAC,EAAE;YACvB,IAAI,aAAa;YAEjB,sDAAsD;YACtD,IAAK,IAAI,IAAI,IAAI,UAAU,KAAK,IAAI,UAAU,IAAK;gBACjD,IAAI,MAAM,KAAK,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,QAAQ,GAAG,EAAE;oBACzC,aAAa;oBACb;gBACF;YACF;YAEA,IAAI,YAAY;gBACd,cAAc,IAAI,CAAC,QAAQ,GAAG;YAChC;QACF;QAEA,0CAA0C;QAC1C,OAAO,cAAc,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;IACpD;IAEA;;GAEC,GACD,AAAQ,mBAAmB,UAQ1B,EAAU;QACT,IAAI,QAAQ;QAEZ,sCAAsC;QACtC,IAAI,WAAW,mBAAmB,EAAE,SAAS;QAC7C,IAAI,WAAW,WAAW,EAAE,SAAS;QACrC,IAAI,WAAW,SAAS,EAAE,SAAS;QAEnC,oCAAoC;QACpC,IAAI,WAAW,YAAY,EAAE,SAAS;QACtC,IAAI,WAAW,eAAe,EAAE,SAAS;QACzC,IAAI,WAAW,gBAAgB,EAAE,SAAS;QAC1C,IAAI,WAAW,eAAe,EAAE,SAAS;QAEzC,OAAO,KAAK,GAAG,CAAC,KAAK;IACvB;IAEA;;GAEC,GACD,AAAQ,sBAAsB,KAAa,EAA+B;QACxE,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,2BAA2B,OAAiB,EAAoC;QACpF,MAAM,UAAmC,EAAE;QAE3C,6CAA6C;QAC7C,MAAM,YAAY;QAClB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,KAAK,UAAW;YAClD,MAAM,QAAQ,QAAQ,KAAK,CAAC,GAAG,IAAI;YACnC,MAAM,gBAAgB,MAAM,GAAG,CAAC,CAAA,SAAU,IAAI,CAAC,oBAAoB,CAAC;YACpE,MAAM,eAAe,MAAM,QAAQ,GAAG,CAAC;YAEvC,QAAQ,IAAI,IAAI,aAAa,MAAM,CAAC,CAAC,SAA4C,WAAW;YAE5F,6BAA6B;YAC7B,IAAI,IAAI,YAAY,QAAQ,MAAM,EAAE;gBAClC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD;QACF;QAEA,OAAO,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;IACzD;AACF", "debugId": null}}, {"offset": {"line": 2141, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/lib/perfectPickTradingSystem.ts"], "sourcesContent": ["import { \n  PerfectPickSetup, \n  PreMarketGapScan, \n  TechnicalGateAnalysis, \n  IntradayEntryTrigger,\n  Catalyst,\n  CandlestickData\n} from '@/types/trading'\nimport { PreMarketGapScanner } from './preMarketGapScanner'\nimport { TechnicalGateAnalysis as TechnicalGateAnalyzer } from './technicalGateAnalysis'\nimport { CatalystDetectionEngine } from './catalystDetection'\nimport { PolygonAPI } from './polygon'\n\nexport class PerfectPickTradingSystem {\n  private gapScanner: PreMarketGapScanner\n  private technicalAnalyzer: TechnicalGateAnalyzer\n  private catalystEngine: CatalystDetectionEngine\n  private polygonAPI: PolygonAPI\n\n  constructor(polygonApiKey?: string, useIBKR: boolean = true) {\n    this.gapScanner = new PreMarketGapScanner(polygonApiKey, useIBKR)\n    this.technicalAnalyzer = new TechnicalGateAnalyzer(polygonApiKey)\n    this.catalystEngine = new CatalystDetectionEngine(polygonApiKey)\n    this.polygonAPI = new PolygonAPI(polygonApiKey)\n  }\n\n  /**\n   * Run complete Perfect-Pick analysis pipeline\n   */\n  async runPerfectPickScan(\n    accountSize: number = 100000,\n    riskPercent: number = 2,\n    customUniverse?: string[]\n  ): Promise<PerfectPickSetup[]> {\n    console.log('🎯 Starting Perfect-Pick Trading System scan...')\n\n    try {\n      // Step 1: Pre-Market Gap Scan\n      console.log('📊 Running pre-market gap scan...')\n      const gapResults = await this.gapScanner.runGapScan(customUniverse)\n      \n      // Filter for basic criteria first\n      const qualifiedGaps = gapResults.filter(gap => \n        gap.gapPercent >= 3.0 && \n        gap.gapPercent <= 15.0 && // Exclude over-extended gaps\n        gap.marketCap >= ********* &&\n        gap.price > 1.0\n      )\n\n      console.log(`✅ Found ${qualifiedGaps.length} qualified gap candidates`)\n\n      if (qualifiedGaps.length === 0) {\n        return []\n      }\n\n      // Step 2: Technical Gate Analysis\n      console.log('🔍 Running technical gate analysis...')\n      const symbols = qualifiedGaps.map(gap => gap.symbol)\n      const technicalAnalyses = await this.technicalAnalyzer.batchAnalyzeTechnicalGates(symbols)\n      \n      // Filter for passing technical gates (Grade B or better)\n      const passingTechnical = technicalAnalyses.filter(analysis => \n        ['A', 'B'].includes(analysis.overallGrade) &&\n        analysis.aboveSMA200 &&\n        analysis.aboveEMA8\n      )\n\n      console.log(`✅ ${passingTechnical.length} stocks passed technical gate`)\n\n      // Step 3: Combine and create Perfect-Pick setups\n      const perfectPickSetups: PerfectPickSetup[] = []\n\n      for (const gapResult of qualifiedGaps) {\n        const technicalAnalysis = passingTechnical.find(t => t.symbol === gapResult.symbol)\n        if (!technicalAnalysis) continue\n\n        const catalyst = gapResult.catalyst\n        if (!catalyst || !this.isValidCatalyst(catalyst)) continue\n\n        // Create Perfect-Pick setup\n        const setup = await this.createPerfectPickSetup(\n          gapResult,\n          technicalAnalysis,\n          catalyst,\n          accountSize,\n          riskPercent\n        )\n\n        if (setup && this.validatePerfectPickSetup(setup)) {\n          perfectPickSetups.push(setup)\n        }\n      }\n\n      // Sort by overall score\n      perfectPickSetups.sort((a, b) => b.overallScore - a.overallScore)\n\n      console.log(`🎯 Generated ${perfectPickSetups.length} Perfect-Pick setups`)\n      \n      return perfectPickSetups\n    } catch (error) {\n      console.error('Error running Perfect-Pick scan:', error)\n      return []\n    }\n  }\n\n  /**\n   * Create a complete Perfect-Pick setup\n   */\n  private async createPerfectPickSetup(\n    gapScan: PreMarketGapScan,\n    technicalGate: TechnicalGateAnalysis,\n    catalyst: Catalyst,\n    accountSize: number,\n    riskPercent: number\n  ): Promise<PerfectPickSetup | null> {\n    try {\n      const symbol = gapScan.symbol\n      const currentPrice = gapScan.price\n      \n      // Calculate risk management\n      const preMarketLow = gapScan.preMarketLow\n      const stopLoss = preMarketLow * 0.99 // Slightly below PML for safety\n      const riskPerShare = currentPrice - stopLoss\n      \n      if (riskPerShare <= 0) {\n        return null // Invalid risk setup\n      }\n\n      const maxRiskAmount = accountSize * (riskPercent / 100)\n      const positionSize = Math.floor(maxRiskAmount / riskPerShare)\n      const maxPositionValue = accountSize * 0.05 // 5% max position size\n      const maxShares = Math.floor(maxPositionValue / currentPrice)\n      \n      const finalPositionSize = Math.min(positionSize, maxShares)\n      const actualRiskAmount = finalPositionSize * riskPerShare\n\n      // Calculate reward targets (minimum 3:1 R/R required)\n      const target3R = currentPrice + (riskPerShare * 3)\n      const target4R = currentPrice + (riskPerShare * 4)\n      const target5R = currentPrice + (riskPerShare * 5)\n      const riskRewardRatio = 3 // Minimum required\n\n      // Check for exclusion reasons\n      const exclusionReasons = this.checkExclusionCriteria(gapScan, technicalGate, catalyst)\n\n      // Validation checks\n      const validationChecks = {\n        hasValidCatalyst: this.isValidCatalyst(catalyst),\n        meetsGapCriteria: gapScan.meetsAllCriteria,\n        passesTechnicalGate: ['A', 'B'].includes(technicalGate.overallGrade),\n        hasEntryTrigger: true, // Will be determined intraday\n        meetsRiskReward: riskRewardRatio >= 3,\n        noExclusionFlags: exclusionReasons.length === 0\n      }\n\n      // Calculate overall score\n      const overallScore = this.calculateOverallScore(gapScan, technicalGate, catalyst, validationChecks)\n      const setupGrade = this.calculateSetupGrade(overallScore)\n\n      const setup: PerfectPickSetup = {\n        symbol,\n        name: gapScan.name,\n        catalyst,\n        gapScan,\n        technicalGate,\n        riskManagement: {\n          entryPrice: currentPrice,\n          stopLoss,\n          stopLossType: 'pre_market_low',\n          riskPerShare,\n          positionSize: finalPositionSize,\n          accountRiskPercent: riskPercent,\n          maxPositionPercent: 5\n        },\n        rewardPlanning: {\n          riskRewardRatio,\n          target3R,\n          target4R,\n          target5R,\n          scaleOutPlan: [\n            { level: 3, percentage: 25 }, // Take 25% at 3R\n            { level: 4, percentage: 25 }, // Take 25% at 4R\n            { level: 5, percentage: 25 }  // Take 25% at 5R, hold 25% for trend\n          ]\n        },\n        overallScore,\n        setupGrade,\n        exclusionReasons,\n        validationChecks,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      }\n\n      return setup\n    } catch (error) {\n      console.error(`Error creating Perfect-Pick setup for ${gapScan.symbol}:`, error)\n      return null\n    }\n  }\n\n  /**\n   * Generate intraday entry trigger\n   */\n  async generateEntryTrigger(symbol: string, preMarketHigh: number): Promise<IntradayEntryTrigger | null> {\n    try {\n      // Get current intraday data\n      const currentQuote = await this.fmpAPI.getStockQuote(symbol)\n      const currentPrice = currentQuote.price\n      \n      // Calculate VWAP (simplified - would need intraday data for accurate VWAP)\n      const vwap = currentPrice * 0.995 // Approximation\n      \n      // Determine entry signal type\n      let entrySignalType: 'pmh_break' | 'vwap_pullback' | 'first_candle_close'\n      let urgency: 'immediate' | 'wait_for_pullback' | 'breakout_confirmation'\n      let conditions: string[] = []\n\n      if (currentPrice > preMarketHigh) {\n        entrySignalType = 'pmh_break'\n        urgency = 'immediate'\n        conditions.push('Clean break above pre-market high')\n      } else if (currentPrice <= vwap && currentPrice > vwap * 0.98) {\n        entrySignalType = 'vwap_pullback'\n        urgency = 'wait_for_pullback'\n        conditions.push('Pullback to VWAP support')\n      } else {\n        entrySignalType = 'first_candle_close'\n        urgency = 'breakout_confirmation'\n        conditions.push('Wait for first 5-min candle close above PMH')\n      }\n\n      // Check volume confirmation (simplified)\n      const volumeConfirmation = currentQuote.volume > (currentQuote.volume || 0) * 1.5\n      const vwapRising = true // Would need historical VWAP data to determine\n      const noMajorResistance = true // Would need to check against resistance levels\n\n      const trigger: IntradayEntryTrigger = {\n        symbol,\n        preMarketHigh,\n        preMarketLow: preMarketHigh * 0.95, // Approximation\n        vwap,\n        entrySignalType,\n        entryPrice: currentPrice,\n        entryTime: new Date().toISOString(),\n        volumeConfirmation,\n        vwapRising,\n        noMajorResistance,\n        triggerValid: volumeConfirmation && vwapRising && noMajorResistance,\n        urgency,\n        conditions\n      }\n\n      return trigger\n    } catch (error) {\n      console.error(`Error generating entry trigger for ${symbol}:`, error)\n      return null\n    }\n  }\n\n  /**\n   * Validate catalyst quality and tier\n   */\n  private isValidCatalyst(catalyst: Catalyst): boolean {\n    // Tier 1 catalysts (highest priority)\n    const tier1Types = [\n      'earnings_beat_guidance',\n      'fda_approval',\n      'drug_trial_results',\n      'contract_win',\n      'partnership',\n      'merger_acquisition'\n    ]\n\n    // Tier 2 catalysts (secondary)\n    const tier2Types = [\n      'analyst_upgrade',\n      'stock_split',\n      'sector_rotation'\n    ]\n\n    const isValidType = tier1Types.includes(catalyst.type) || tier2Types.includes(catalyst.type)\n    const isFresh = catalyst.freshness === 'fresh' || catalyst.freshness === 'moderate'\n    const hasQuality = catalyst.qualityScore >= 6\n    const isVerified = catalyst.verified\n\n    return isValidType && isFresh && hasQuality && isVerified\n  }\n\n  /**\n   * Check for exclusion criteria\n   */\n  private checkExclusionCriteria(\n    gapScan: PreMarketGapScan,\n    technicalGate: TechnicalGateAnalysis,\n    catalyst: Catalyst\n  ): string[] {\n    const exclusions: string[] = []\n\n    // Anti-pattern filters\n    if (!technicalGate.dailyTrendConfirmed) {\n      exclusions.push('Stock not in confirmed daily uptrend')\n    }\n\n    if (!technicalGate.aboveSMA200) {\n      exclusions.push('Stock below 200-day SMA')\n    }\n\n    if (gapScan.gapPercent > 15) {\n      exclusions.push('Gap too extended (>15%)')\n    }\n\n    if (gapScan.averageDailyVolume < 500000) {\n      exclusions.push('Low liquidity (avg daily volume <500K)')\n    }\n\n    if (catalyst.impact === 'bearish') {\n      exclusions.push('Negative catalyst detected')\n    }\n\n    if (catalyst.freshness === 'stale') {\n      exclusions.push('Catalyst is stale (>72 hours old)')\n    }\n\n    return exclusions\n  }\n\n  /**\n   * Calculate overall setup score (0-100)\n   */\n  private calculateOverallScore(\n    gapScan: PreMarketGapScan,\n    technicalGate: TechnicalGateAnalysis,\n    catalyst: Catalyst,\n    validationChecks: any\n  ): number {\n    let score = 0\n\n    // Gap quality (25 points max)\n    score += Math.min(25, gapScan.gapPercent * 2) // 3% gap = 6 points, 10% gap = 20 points\n\n    // Technical gate score (35 points max)\n    score += (technicalGate.gateScore / 100) * 35\n\n    // Catalyst quality (25 points max)\n    score += (catalyst.qualityScore / 10) * 25\n\n    // Validation bonus (15 points max)\n    const validationCount = Object.values(validationChecks).filter(Boolean).length\n    score += (validationCount / Object.keys(validationChecks).length) * 15\n\n    return Math.min(100, Math.round(score))\n  }\n\n  /**\n   * Calculate setup grade\n   */\n  private calculateSetupGrade(score: number): 'A+' | 'A' | 'B+' | 'B' | 'C+' | 'C' | 'D' | 'F' {\n    if (score >= 95) return 'A+'\n    if (score >= 90) return 'A'\n    if (score >= 85) return 'B+'\n    if (score >= 80) return 'B'\n    if (score >= 75) return 'C+'\n    if (score >= 70) return 'C'\n    if (score >= 60) return 'D'\n    return 'F'\n  }\n\n  /**\n   * Validate complete Perfect-Pick setup\n   */\n  private validatePerfectPickSetup(setup: PerfectPickSetup): boolean {\n    const checks = setup.validationChecks\n    \n    // Must pass all critical checks\n    const criticalChecks = [\n      checks.hasValidCatalyst,\n      checks.meetsGapCriteria,\n      checks.passesTechnicalGate,\n      checks.meetsRiskReward,\n      checks.noExclusionFlags\n    ]\n\n    return criticalChecks.every(check => check) && setup.overallScore >= 70\n  }\n\n  /**\n   * Get setup summary statistics\n   */\n  getSetupSummary(setups: PerfectPickSetup[]) {\n    const totalSetups = setups.length\n    const gradeBreakdown = setups.reduce((acc, setup) => {\n      acc[setup.setupGrade] = (acc[setup.setupGrade] || 0) + 1\n      return acc\n    }, {} as Record<string, number>)\n\n    const catalystBreakdown = setups.reduce((acc, setup) => {\n      acc[setup.catalyst.type] = (acc[setup.catalyst.type] || 0) + 1\n      return acc\n    }, {} as Record<string, number>)\n\n    const avgScore = totalSetups > 0 \n      ? setups.reduce((sum, setup) => sum + setup.overallScore, 0) / totalSetups \n      : 0\n\n    const avgGap = totalSetups > 0\n      ? setups.reduce((sum, setup) => sum + setup.gapScan.gapPercent, 0) / totalSetups\n      : 0\n\n    return {\n      totalSetups,\n      avgScore: Math.round(avgScore * 100) / 100,\n      avgGap: Math.round(avgGap * 100) / 100,\n      gradeBreakdown,\n      catalystBreakdown,\n      generatedAt: new Date().toISOString()\n    }\n  }\n\n  /**\n   * Update existing setups with current market data\n   */\n  async updatePerfectPickSetups(setups: PerfectPickSetup[]): Promise<PerfectPickSetup[]> {\n    const updatedSetups: PerfectPickSetup[] = []\n\n    for (const setup of setups) {\n      try {\n        // Get current quote\n        const currentQuote = await this.fmpAPI.getStockQuote(setup.symbol)\n        const currentPrice = currentQuote.price\n\n        // Update entry trigger if needed\n        const entryTrigger = await this.generateEntryTrigger(setup.symbol, setup.gapScan.preMarketHigh)\n\n        // Update the setup\n        const updatedSetup: PerfectPickSetup = {\n          ...setup,\n          entryTrigger,\n          riskManagement: {\n            ...setup.riskManagement,\n            entryPrice: currentPrice\n          },\n          updatedAt: new Date().toISOString()\n        }\n\n        updatedSetups.push(updatedSetup)\n      } catch (error) {\n        console.error(`Error updating setup for ${setup.symbol}:`, error)\n        updatedSetups.push(setup) // Keep original if update fails\n      }\n    }\n\n    return updatedSetups\n  }\n}\n"], "names": [], "mappings": ";;;;AAQA;AACA;AACA;AACA;;;;;AAEO,MAAM;IACH,WAA+B;IAC/B,kBAAwC;IACxC,eAAuC;IACvC,WAAsB;IAE9B,YAAY,aAAsB,EAAE,UAAmB,IAAI,CAAE;QAC3D,IAAI,CAAC,UAAU,GAAG,IAAI,mLAAmB,CAAC,eAAe;QACzD,IAAI,CAAC,iBAAiB,GAAG,IAAI,uLAAqB,CAAC;QACnD,IAAI,CAAC,cAAc,GAAG,IAAI,qLAAuB,CAAC;QAClD,IAAI,CAAC,UAAU,GAAG,IAAI,8JAAU,CAAC;IACnC;IAEA;;GAEC,GACD,MAAM,mBACJ,cAAsB,MAAM,EAC5B,cAAsB,CAAC,EACvB,cAAyB,EACI;QAC7B,QAAQ,GAAG,CAAC;QAEZ,IAAI;YACF,8BAA8B;YAC9B,QAAQ,GAAG,CAAC;YACZ,MAAM,aAAa,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC;YAEpD,kCAAkC;YAClC,MAAM,gBAAgB,WAAW,MAAM,CAAC,CAAA,MACtC,IAAI,UAAU,IAAI,OAClB,IAAI,UAAU,IAAI,QAAQ,6BAA6B;gBACvD,IAAI,SAAS,IAAI,aACjB,IAAI,KAAK,GAAG;YAGd,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,cAAc,MAAM,CAAC,yBAAyB,CAAC;YAEtE,IAAI,cAAc,MAAM,KAAK,GAAG;gBAC9B,OAAO,EAAE;YACX;YAEA,kCAAkC;YAClC,QAAQ,GAAG,CAAC;YACZ,MAAM,UAAU,cAAc,GAAG,CAAC,CAAA,MAAO,IAAI,MAAM;YACnD,MAAM,oBAAoB,MAAM,IAAI,CAAC,iBAAiB,CAAC,0BAA0B,CAAC;YAElF,yDAAyD;YACzD,MAAM,mBAAmB,kBAAkB,MAAM,CAAC,CAAA,WAChD;oBAAC;oBAAK;iBAAI,CAAC,QAAQ,CAAC,SAAS,YAAY,KACzC,SAAS,WAAW,IACpB,SAAS,SAAS;YAGpB,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,iBAAiB,MAAM,CAAC,6BAA6B,CAAC;YAEvE,iDAAiD;YACjD,MAAM,oBAAwC,EAAE;YAEhD,KAAK,MAAM,aAAa,cAAe;gBACrC,MAAM,oBAAoB,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;gBAClF,IAAI,CAAC,mBAAmB;gBAExB,MAAM,WAAW,UAAU,QAAQ;gBACnC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW;gBAElD,4BAA4B;gBAC5B,MAAM,QAAQ,MAAM,IAAI,CAAC,sBAAsB,CAC7C,WACA,mBACA,UACA,aACA;gBAGF,IAAI,SAAS,IAAI,CAAC,wBAAwB,CAAC,QAAQ;oBACjD,kBAAkB,IAAI,CAAC;gBACzB;YACF;YAEA,wBAAwB;YACxB,kBAAkB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,YAAY,GAAG,EAAE,YAAY;YAEhE,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,kBAAkB,MAAM,CAAC,oBAAoB,CAAC;YAE1E,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO,EAAE;QACX;IACF;IAEA;;GAEC,GACD,MAAc,uBACZ,OAAyB,EACzB,aAAoC,EACpC,QAAkB,EAClB,WAAmB,EACnB,WAAmB,EACe;QAClC,IAAI;YACF,MAAM,SAAS,QAAQ,MAAM;YAC7B,MAAM,eAAe,QAAQ,KAAK;YAElC,4BAA4B;YAC5B,MAAM,eAAe,QAAQ,YAAY;YACzC,MAAM,WAAW,eAAe,KAAK,gCAAgC;;YACrE,MAAM,eAAe,eAAe;YAEpC,IAAI,gBAAgB,GAAG;gBACrB,OAAO,KAAK,qBAAqB;;YACnC;YAEA,MAAM,gBAAgB,cAAc,CAAC,cAAc,GAAG;YACtD,MAAM,eAAe,KAAK,KAAK,CAAC,gBAAgB;YAChD,MAAM,mBAAmB,cAAc,KAAK,uBAAuB;;YACnE,MAAM,YAAY,KAAK,KAAK,CAAC,mBAAmB;YAEhD,MAAM,oBAAoB,KAAK,GAAG,CAAC,cAAc;YACjD,MAAM,mBAAmB,oBAAoB;YAE7C,sDAAsD;YACtD,MAAM,WAAW,eAAgB,eAAe;YAChD,MAAM,WAAW,eAAgB,eAAe;YAChD,MAAM,WAAW,eAAgB,eAAe;YAChD,MAAM,kBAAkB,EAAE,mBAAmB;;YAE7C,8BAA8B;YAC9B,MAAM,mBAAmB,IAAI,CAAC,sBAAsB,CAAC,SAAS,eAAe;YAE7E,oBAAoB;YACpB,MAAM,mBAAmB;gBACvB,kBAAkB,IAAI,CAAC,eAAe,CAAC;gBACvC,kBAAkB,QAAQ,gBAAgB;gBAC1C,qBAAqB;oBAAC;oBAAK;iBAAI,CAAC,QAAQ,CAAC,cAAc,YAAY;gBACnE,iBAAiB;gBACjB,iBAAiB,mBAAmB;gBACpC,kBAAkB,iBAAiB,MAAM,KAAK;YAChD;YAEA,0BAA0B;YAC1B,MAAM,eAAe,IAAI,CAAC,qBAAqB,CAAC,SAAS,eAAe,UAAU;YAClF,MAAM,aAAa,IAAI,CAAC,mBAAmB,CAAC;YAE5C,MAAM,QAA0B;gBAC9B;gBACA,MAAM,QAAQ,IAAI;gBAClB;gBACA;gBACA;gBACA,gBAAgB;oBACd,YAAY;oBACZ;oBACA,cAAc;oBACd;oBACA,cAAc;oBACd,oBAAoB;oBACpB,oBAAoB;gBACtB;gBACA,gBAAgB;oBACd;oBACA;oBACA;oBACA;oBACA,cAAc;wBACZ;4BAAE,OAAO;4BAAG,YAAY;wBAAG;wBAC3B;4BAAE,OAAO;4BAAG,YAAY;wBAAG;wBAC3B;4BAAE,OAAO;4BAAG,YAAY;wBAAG,EAAG,qCAAqC;qBACpE;gBACH;gBACA;gBACA;gBACA;gBACA;gBACA,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,sCAAsC,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC,EAAE;YAC1E,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAM,qBAAqB,MAAc,EAAE,aAAqB,EAAwC;QACtG,IAAI;YACF,4BAA4B;YAC5B,MAAM,eAAe,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;YACrD,MAAM,eAAe,aAAa,KAAK;YAEvC,2EAA2E;YAC3E,MAAM,OAAO,eAAe,MAAM,gBAAgB;;YAElD,8BAA8B;YAC9B,IAAI;YACJ,IAAI;YACJ,IAAI,aAAuB,EAAE;YAE7B,IAAI,eAAe,eAAe;gBAChC,kBAAkB;gBAClB,UAAU;gBACV,WAAW,IAAI,CAAC;YAClB,OAAO,IAAI,gBAAgB,QAAQ,eAAe,OAAO,MAAM;gBAC7D,kBAAkB;gBAClB,UAAU;gBACV,WAAW,IAAI,CAAC;YAClB,OAAO;gBACL,kBAAkB;gBAClB,UAAU;gBACV,WAAW,IAAI,CAAC;YAClB;YAEA,yCAAyC;YACzC,MAAM,qBAAqB,aAAa,MAAM,GAAG,CAAC,aAAa,MAAM,IAAI,CAAC,IAAI;YAC9E,MAAM,aAAa,KAAK,+CAA+C;;YACvE,MAAM,oBAAoB,KAAK,gDAAgD;;YAE/E,MAAM,UAAgC;gBACpC;gBACA;gBACA,cAAc,gBAAgB;gBAC9B;gBACA;gBACA,YAAY;gBACZ,WAAW,IAAI,OAAO,WAAW;gBACjC;gBACA;gBACA;gBACA,cAAc,sBAAsB,cAAc;gBAClD;gBACA;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,OAAO,CAAC,CAAC,EAAE;YAC/D,OAAO;QACT;IACF;IAEA;;GAEC,GACD,AAAQ,gBAAgB,QAAkB,EAAW;QACnD,sCAAsC;QACtC,MAAM,aAAa;YACjB;YACA;YACA;YACA;YACA;YACA;SACD;QAED,+BAA+B;QAC/B,MAAM,aAAa;YACjB;YACA;YACA;SACD;QAED,MAAM,cAAc,WAAW,QAAQ,CAAC,SAAS,IAAI,KAAK,WAAW,QAAQ,CAAC,SAAS,IAAI;QAC3F,MAAM,UAAU,SAAS,SAAS,KAAK,WAAW,SAAS,SAAS,KAAK;QACzE,MAAM,aAAa,SAAS,YAAY,IAAI;QAC5C,MAAM,aAAa,SAAS,QAAQ;QAEpC,OAAO,eAAe,WAAW,cAAc;IACjD;IAEA;;GAEC,GACD,AAAQ,uBACN,OAAyB,EACzB,aAAoC,EACpC,QAAkB,EACR;QACV,MAAM,aAAuB,EAAE;QAE/B,uBAAuB;QACvB,IAAI,CAAC,cAAc,mBAAmB,EAAE;YACtC,WAAW,IAAI,CAAC;QAClB;QAEA,IAAI,CAAC,cAAc,WAAW,EAAE;YAC9B,WAAW,IAAI,CAAC;QAClB;QAEA,IAAI,QAAQ,UAAU,GAAG,IAAI;YAC3B,WAAW,IAAI,CAAC;QAClB;QAEA,IAAI,QAAQ,kBAAkB,GAAG,QAAQ;YACvC,WAAW,IAAI,CAAC;QAClB;QAEA,IAAI,SAAS,MAAM,KAAK,WAAW;YACjC,WAAW,IAAI,CAAC;QAClB;QAEA,IAAI,SAAS,SAAS,KAAK,SAAS;YAClC,WAAW,IAAI,CAAC;QAClB;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,sBACN,OAAyB,EACzB,aAAoC,EACpC,QAAkB,EAClB,gBAAqB,EACb;QACR,IAAI,QAAQ;QAEZ,8BAA8B;QAC9B,SAAS,KAAK,GAAG,CAAC,IAAI,QAAQ,UAAU,GAAG,IAAG,yCAAyC;QAEvF,uCAAuC;QACvC,SAAS,AAAC,cAAc,SAAS,GAAG,MAAO;QAE3C,mCAAmC;QACnC,SAAS,AAAC,SAAS,YAAY,GAAG,KAAM;QAExC,mCAAmC;QACnC,MAAM,kBAAkB,OAAO,MAAM,CAAC,kBAAkB,MAAM,CAAC,SAAS,MAAM;QAC9E,SAAS,AAAC,kBAAkB,OAAO,IAAI,CAAC,kBAAkB,MAAM,GAAI;QAEpE,OAAO,KAAK,GAAG,CAAC,KAAK,KAAK,KAAK,CAAC;IAClC;IAEA;;GAEC,GACD,AAAQ,oBAAoB,KAAa,EAAoD;QAC3F,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,yBAAyB,KAAuB,EAAW;QACjE,MAAM,SAAS,MAAM,gBAAgB;QAErC,gCAAgC;QAChC,MAAM,iBAAiB;YACrB,OAAO,gBAAgB;YACvB,OAAO,gBAAgB;YACvB,OAAO,mBAAmB;YAC1B,OAAO,eAAe;YACtB,OAAO,gBAAgB;SACxB;QAED,OAAO,eAAe,KAAK,CAAC,CAAA,QAAS,UAAU,MAAM,YAAY,IAAI;IACvE;IAEA;;GAEC,GACD,gBAAgB,MAA0B,EAAE;QAC1C,MAAM,cAAc,OAAO,MAAM;QACjC,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAC,KAAK;YACzC,GAAG,CAAC,MAAM,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,UAAU,CAAC,IAAI,CAAC,IAAI;YACvD,OAAO;QACT,GAAG,CAAC;QAEJ,MAAM,oBAAoB,OAAO,MAAM,CAAC,CAAC,KAAK;YAC5C,GAAG,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;YAC7D,OAAO;QACT,GAAG,CAAC;QAEJ,MAAM,WAAW,cAAc,IAC3B,OAAO,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,YAAY,EAAE,KAAK,cAC7D;QAEJ,MAAM,SAAS,cAAc,IACzB,OAAO,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,OAAO,CAAC,UAAU,EAAE,KAAK,cACnE;QAEJ,OAAO;YACL;YACA,UAAU,KAAK,KAAK,CAAC,WAAW,OAAO;YACvC,QAAQ,KAAK,KAAK,CAAC,SAAS,OAAO;YACnC;YACA;YACA,aAAa,IAAI,OAAO,WAAW;QACrC;IACF;IAEA;;GAEC,GACD,MAAM,wBAAwB,MAA0B,EAA+B;QACrF,MAAM,gBAAoC,EAAE;QAE5C,KAAK,MAAM,SAAS,OAAQ;YAC1B,IAAI;gBACF,oBAAoB;gBACpB,MAAM,eAAe,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,MAAM;gBACjE,MAAM,eAAe,aAAa,KAAK;gBAEvC,iCAAiC;gBACjC,MAAM,eAAe,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,MAAM,EAAE,MAAM,OAAO,CAAC,aAAa;gBAE9F,mBAAmB;gBACnB,MAAM,eAAiC;oBACrC,GAAG,KAAK;oBACR;oBACA,gBAAgB;wBACd,GAAG,MAAM,cAAc;wBACvB,YAAY;oBACd;oBACA,WAAW,IAAI,OAAO,WAAW;gBACnC;gBAEA,cAAc,IAAI,CAAC;YACrB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,MAAM,MAAM,CAAC,CAAC,CAAC,EAAE;gBAC3D,cAAc,IAAI,CAAC,QAAO,gCAAgC;YAC5D;QACF;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 2505, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/app/api/scanner/perfect-pick/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { PerfectPickTradingSystem } from '@/lib/perfectPickTradingSystem'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    \n    const accountSize = parseInt(searchParams.get('accountSize') || '100000')\n    const riskPercent = parseFloat(searchParams.get('riskPercent') || '2')\n    const customUniverse = searchParams.get('universe')?.split(',').filter(Boolean)\n    const limit = parseInt(searchParams.get('limit') || '20')\n    const dataSource = searchParams.get('dataSource') || 'IBKR'\n    const useIBKR = dataSource === 'IBKR'\n\n    console.log('🎯 Perfect-Pick API called with params:', {\n      accountSize,\n      riskPercent,\n      customUniverse: customUniverse?.length || 'default',\n      limit,\n      dataSource,\n      useIBKR\n    })\n\n    // Initialize Perfect-Pick Trading System with data source preference\n    const perfectPickSystem = new PerfectPickTradingSystem(\n      process.env.POLYGON_API_KEY,\n      useIBKR\n    )\n\n    // Run the complete Perfect-Pick scan\n    const setups = await perfectPickSystem.runPerfectPickScan(\n      accountSize,\n      riskPercent,\n      customUniverse\n    )\n\n    // Limit results\n    const limitedSetups = setups.slice(0, limit)\n\n    // Get summary statistics\n    const summary = perfectPickSystem.getSetupSummary(limitedSetups)\n\n    const response = {\n      success: true,\n      data: {\n        setups: limitedSetups,\n        summary,\n        scanParams: {\n          accountSize,\n          riskPercent,\n          universeSize: customUniverse?.length || 'default',\n          limit\n        },\n        timestamp: new Date().toISOString()\n      }\n    }\n\n    return NextResponse.json(response)\n  } catch (error) {\n    console.error('Error in Perfect-Pick API:', error)\n    return NextResponse.json(\n      { \n        success: false, \n        error: 'Failed to run Perfect-Pick scan',\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const { action, data } = body\n\n    const perfectPickSystem = new PerfectPickTradingSystem(\n      process.env.FMP_API_KEY,\n      process.env.POLYGON_API_KEY\n    )\n\n    switch (action) {\n      case 'update_setups':\n        const updatedSetups = await perfectPickSystem.updatePerfectPickSetups(data.setups)\n        return NextResponse.json({\n          success: true,\n          data: { setups: updatedSetups }\n        })\n\n      case 'generate_entry_trigger':\n        const entryTrigger = await perfectPickSystem.generateEntryTrigger(\n          data.symbol,\n          data.preMarketHigh\n        )\n        return NextResponse.json({\n          success: true,\n          data: { entryTrigger }\n        })\n\n      default:\n        return NextResponse.json(\n          { success: false, error: 'Invalid action' },\n          { status: 400 }\n        )\n    }\n  } catch (error) {\n    console.error('Error in Perfect-Pick POST API:', error)\n    return NextResponse.json(\n      { \n        success: false, \n        error: 'Failed to process Perfect-Pick request',\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAE5C,MAAM,cAAc,SAAS,aAAa,GAAG,CAAC,kBAAkB;QAChE,MAAM,cAAc,WAAW,aAAa,GAAG,CAAC,kBAAkB;QAClE,MAAM,iBAAiB,aAAa,GAAG,CAAC,aAAa,MAAM,KAAK,OAAO;QACvE,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,aAAa,aAAa,GAAG,CAAC,iBAAiB;QACrD,MAAM,UAAU,eAAe;QAE/B,QAAQ,GAAG,CAAC,2CAA2C;YACrD;YACA;YACA,gBAAgB,gBAAgB,UAAU;YAC1C;YACA;YACA;QACF;QAEA,qEAAqE;QACrE,MAAM,oBAAoB,IAAI,6LAAwB,CACpD,QAAQ,GAAG,CAAC,eAAe,EAC3B;QAGF,qCAAqC;QACrC,MAAM,SAAS,MAAM,kBAAkB,kBAAkB,CACvD,aACA,aACA;QAGF,gBAAgB;QAChB,MAAM,gBAAgB,OAAO,KAAK,CAAC,GAAG;QAEtC,yBAAyB;QACzB,MAAM,UAAU,kBAAkB,eAAe,CAAC;QAElD,MAAM,WAAW;YACf,SAAS;YACT,MAAM;gBACJ,QAAQ;gBACR;gBACA,YAAY;oBACV;oBACA;oBACA,cAAc,gBAAgB,UAAU;oBACxC;gBACF;gBACA,WAAW,IAAI,OAAO,WAAW;YACnC;QACF;QAEA,OAAO,yKAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,yKAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;QAEzB,MAAM,oBAAoB,IAAI,6LAAwB,CACpD,QAAQ,GAAG,CAAC,WAAW,EACvB,QAAQ,GAAG,CAAC,eAAe;QAG7B,OAAQ;YACN,KAAK;gBACH,MAAM,gBAAgB,MAAM,kBAAkB,uBAAuB,CAAC,KAAK,MAAM;gBACjF,OAAO,yKAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;wBAAE,QAAQ;oBAAc;gBAChC;YAEF,KAAK;gBACH,MAAM,eAAe,MAAM,kBAAkB,oBAAoB,CAC/D,KAAK,MAAM,EACX,KAAK,aAAa;gBAEpB,OAAO,yKAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;wBAAE;oBAAa;gBACvB;YAEF;gBACE,OAAO,yKAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,OAAO;gBAAiB,GAC1C;oBAAE,QAAQ;gBAAI;QAEpB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO,yKAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}