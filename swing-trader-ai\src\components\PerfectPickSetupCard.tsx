'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  TrendingUp,
  TrendingDown,
  Target,
  Shield,
  Clock,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  Play,
  Eye,
  Zap,
  Activity,
  Star,
  ChevronDown,
  ChevronUp
} from 'lucide-react'
import { PerfectPickSetup } from '@/types/trading'
import { formatCurrency, formatPercentage } from '@/lib/utils'

interface PerfectPickSetupCardProps {
  setup: PerfectPickSetup
  onExecuteTrade?: (setup: PerfectPickSetup) => void
  onViewChart?: (symbol: string) => void
  onGenerateEntryTrigger?: (symbol: string, preMarketHigh: number) => void
}

export function PerfectPickSetupCard({ 
  setup, 
  onExecuteTrade, 
  onViewChart,
  onGenerateEntryTrigger 
}: PerfectPickSetupCardProps) {
  const [showDetails, setShowDetails] = useState(false)

  const getCatalystBadgeColor = (tier: string) => {
    switch (tier) {
      case 'tier_1': return 'bg-green-500 text-white'
      case 'tier_2': return 'bg-yellow-500 text-white'
      case 'tier_3': return 'bg-gray-500 text-white'
      default: return 'bg-gray-400 text-white'
    }
  }

  const getCatalystImpactIcon = (impact: string) => {
    switch (impact) {
      case 'bullish': return <TrendingUp className="h-4 w-4 text-green-500" />
      case 'bearish': return <TrendingDown className="h-4 w-4 text-red-500" />
      default: return <Activity className="h-4 w-4 text-gray-500" />
    }
  }

  const getSetupGradeColor = (grade: string) => {
    if (grade.startsWith('A')) return 'text-green-600 bg-green-50 border-green-200'
    if (grade.startsWith('B')) return 'text-blue-600 bg-blue-50 border-blue-200'
    if (grade.startsWith('C')) return 'text-yellow-600 bg-yellow-50 border-yellow-200'
    return 'text-red-600 bg-red-50 border-red-200'
  }

  const getFreshnessColor = (freshness: string) => {
    switch (freshness) {
      case 'fresh': return 'text-green-600 bg-green-50'
      case 'moderate': return 'text-yellow-600 bg-yellow-50'
      case 'stale': return 'text-red-600 bg-red-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  const riskRewardRatio = setup.rewardPlanning.riskRewardRatio
  const potentialProfit = setup.riskManagement.positionSize * setup.riskManagement.riskPerShare * riskRewardRatio
  const riskAmount = setup.riskManagement.positionSize * setup.riskManagement.riskPerShare

  return (
    <Card className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-blue-500">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div>
              <CardTitle className="text-xl font-bold flex items-center gap-2">
                {setup.symbol}
                <Star className="h-5 w-5 text-yellow-500" />
              </CardTitle>
              <p className="text-sm text-muted-foreground">{setup.name}</p>
            </div>
            <div className="flex items-center gap-2">
              <Badge className={`${getSetupGradeColor(setup.setupGrade)} border`}>
                Grade {setup.setupGrade}
              </Badge>
              <Badge variant="outline" className="font-semibold">
                Score: {setup.overallScore}/100
              </Badge>
            </div>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-blue-600">
              {formatCurrency(setup.gapScan.price)}
            </div>
            <div className="text-sm font-semibold text-green-600">
              Gap: +{formatPercentage(setup.gapScan.gapPercent)}
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Catalyst Information */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border">
          <div className="flex items-center gap-2 mb-2">
            <Zap className="h-5 w-5 text-purple-600" />
            <span className="font-semibold text-purple-800">Catalyst</span>
            {getCatalystImpactIcon(setup.catalyst.impact)}
            <Badge className={getCatalystBadgeColor(setup.catalyst.tier)}>
              {setup.catalyst.tier.replace('_', ' ').toUpperCase()}
            </Badge>
            <Badge className={getFreshnessColor(setup.catalyst.freshness)}>
              {setup.catalyst.freshness}
            </Badge>
          </div>
          <h4 className="font-medium text-gray-800 mb-1">{setup.catalyst.title}</h4>
          <p className="text-sm text-gray-600 mb-2">{setup.catalyst.description}</p>
          <div className="flex items-center gap-4 text-xs text-gray-500">
            <span>Quality: {setup.catalyst.qualityScore}/10</span>
            <span>Source: {setup.catalyst.source}</span>
            <span className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              {new Date(setup.catalyst.announcementTime).toLocaleString()}
            </span>
          </div>
        </div>

        {/* Risk/Reward Summary */}
        <div className="grid grid-cols-4 gap-4">
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="text-sm text-blue-600 font-medium">Entry</div>
            <div className="text-lg font-bold text-blue-800">
              {formatCurrency(setup.riskManagement.entryPrice)}
            </div>
          </div>
          <div className="text-center p-3 bg-red-50 rounded-lg">
            <div className="text-sm text-red-600 font-medium">Stop Loss</div>
            <div className="text-lg font-bold text-red-800">
              {formatCurrency(setup.riskManagement.stopLoss)}
            </div>
          </div>
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <div className="text-sm text-green-600 font-medium">Target (3R)</div>
            <div className="text-lg font-bold text-green-800">
              {formatCurrency(setup.rewardPlanning.target3R)}
            </div>
          </div>
          <div className="text-center p-3 bg-purple-50 rounded-lg">
            <div className="text-sm text-purple-600 font-medium">R/R Ratio</div>
            <div className="text-lg font-bold text-purple-800">
              {riskRewardRatio}:1
            </div>
          </div>
        </div>

        {/* Position Sizing */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="grid grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Position Size:</span>
              <div className="font-semibold">{setup.riskManagement.positionSize} shares</div>
            </div>
            <div>
              <span className="text-gray-600">Risk Amount:</span>
              <div className="font-semibold text-red-600">{formatCurrency(riskAmount)}</div>
            </div>
            <div>
              <span className="text-gray-600">Potential Profit (3R):</span>
              <div className="font-semibold text-green-600">{formatCurrency(potentialProfit)}</div>
            </div>
          </div>
        </div>

        {/* Technical Gate Summary */}
        <div className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
          <div className="flex items-center gap-2">
            <Target className="h-4 w-4 text-slate-600" />
            <span className="text-sm font-medium">Technical Gate:</span>
            <Badge variant={setup.technicalGate.overallGrade === 'A' ? 'default' : 'secondary'}>
              Grade {setup.technicalGate.overallGrade}
            </Badge>
            <span className="text-xs text-muted-foreground">
              ({setup.technicalGate.gateScore}/100)
            </span>
          </div>
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            {setup.technicalGate.aboveSMA200 && <CheckCircle className="h-3 w-3 text-green-500" />}
            {setup.technicalGate.aboveEMA8 && <CheckCircle className="h-3 w-3 text-green-500" />}
            {setup.technicalGate.dailyTrendConfirmed && <CheckCircle className="h-3 w-3 text-green-500" />}
          </div>
        </div>

        {/* Entry Trigger Status */}
        {setup.entryTrigger && (
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center gap-2 mb-1">
              <Play className="h-4 w-4 text-yellow-600" />
              <span className="font-medium text-yellow-800">Entry Trigger</span>
              <Badge variant="outline" className="text-xs">
                {setup.entryTrigger.urgency.replace('_', ' ')}
              </Badge>
            </div>
            <p className="text-sm text-yellow-700">
              {setup.entryTrigger.entrySignalType.replace('_', ' ').toUpperCase()} at {formatCurrency(setup.entryTrigger.entryPrice)}
            </p>
          </div>
        )}

        {/* Validation Checks */}
        <div className="grid grid-cols-2 gap-2 text-xs">
          {Object.entries(setup.validationChecks).map(([key, value]) => (
            <div key={key} className="flex items-center gap-1">
              {value ? (
                <CheckCircle className="h-3 w-3 text-green-500" />
              ) : (
                <AlertTriangle className="h-3 w-3 text-red-500" />
              )}
              <span className={value ? 'text-green-700' : 'text-red-700'}>
                {key.replace(/([A-Z])/g, ' $1').toLowerCase()}
              </span>
            </div>
          ))}
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-2 pt-2">
          <Button 
            onClick={() => onExecuteTrade?.(setup)}
            className="flex-1 bg-blue-600 hover:bg-blue-700"
            disabled={!setup.validationChecks.noExclusionFlags}
          >
            <Play className="h-4 w-4 mr-2" />
            Execute Trade
          </Button>
          <Button 
            variant="outline" 
            onClick={() => onViewChart?.(setup.symbol)}
            className="flex-1"
          >
            <Eye className="h-4 w-4 mr-2" />
            View Chart
          </Button>
          {!setup.entryTrigger && (
            <Button 
              variant="outline" 
              onClick={() => onGenerateEntryTrigger?.(setup.symbol, setup.gapScan.preMarketHigh)}
              className="flex-1"
            >
              <Target className="h-4 w-4 mr-2" />
              Entry Trigger
            </Button>
          )}
        </div>

        {/* Expandable Details */}
        <Button
          variant="ghost"
          onClick={() => setShowDetails(!showDetails)}
          className="w-full text-sm"
        >
          {showDetails ? (
            <>
              <ChevronUp className="h-4 w-4 mr-2" />
              Hide Details
            </>
          ) : (
            <>
              <ChevronDown className="h-4 w-4 mr-2" />
              Show Details
            </>
          )}
        </Button>

        {/* Detailed Information */}
        {showDetails && (
          <div className="space-y-3 pt-3 border-t">
            {/* Scale-out Plan */}
            <div>
              <h4 className="font-medium mb-2">Scale-out Plan</h4>
              <div className="space-y-1 text-sm">
                {setup.rewardPlanning.scaleOutPlan.map((plan, index) => (
                  <div key={index} className="flex justify-between">
                    <span>At {plan.level}R ({formatCurrency(setup.riskManagement.entryPrice + (setup.riskManagement.riskPerShare * plan.level))}):</span>
                    <span className="font-medium">{plan.percentage}%</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Technical Levels */}
            <div>
              <h4 className="font-medium mb-2">Key Technical Levels</h4>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>SMA200: {formatCurrency(setup.technicalGate.keyTechnicalLevels.sma200)}</div>
                <div>EMA8: {formatCurrency(setup.technicalGate.keyTechnicalLevels.ema8)}</div>
                <div>VWAP: {formatCurrency(setup.technicalGate.keyTechnicalLevels.vwap)}</div>
                <div>Prev High: {formatCurrency(setup.technicalGate.keyTechnicalLevels.previousHigh)}</div>
              </div>
            </div>

            {/* Exclusion Reasons */}
            {setup.exclusionReasons.length > 0 && (
              <div>
                <h4 className="font-medium mb-2 text-red-600">Exclusion Reasons</h4>
                <ul className="text-sm text-red-600 space-y-1">
                  {setup.exclusionReasons.map((reason, index) => (
                    <li key={index} className="flex items-center gap-1">
                      <AlertTriangle className="h-3 w-3" />
                      {reason}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
