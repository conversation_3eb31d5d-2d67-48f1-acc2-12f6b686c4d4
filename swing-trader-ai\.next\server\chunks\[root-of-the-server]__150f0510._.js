module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},14747,(e,t,r)=>{t.exports=e.x("path",()=>require("path"))},78006,e=>{"use strict";e.s(["PolygonAPI",()=>n]);var t=e.i(55362);let r="https://api.polygon.io",a=process.env.POLYGON_API_KEY;class n{apiKey;constructor(e){if(this.apiKey=e||a||"",!this.apiKey)throw Error("Polygon API key is required")}async getStockQuote(e){try{let a=(await t.default.get(`${r}/v2/snapshot/locale/us/markets/stocks/tickers/${e}`,{params:{apikey:this.apiKey}})).data.results[0];if(!a)throw Error(`No data found for ${e}`);let n=a.value||a,o=n.day||{},i=n.prevDay||{};n.lastQuote;let s=(n.lastTrade||{}).p||o.c||i.c,l=i.c||o.o,c=s-l;return{symbol:e.toUpperCase(),name:n.name||e.toUpperCase(),price:s,change:c,changePercent:c/l*100,volume:o.v||0,marketCap:n.market_cap,pe:void 0,dividend:void 0}}catch(a){console.error("Error fetching stock quote from Polygon:",a);try{let a=(await t.default.get(`${r}/v2/aggs/ticker/${e}/prev`,{params:{adjusted:"true",apikey:this.apiKey}})).data.results[0];return{symbol:e.toUpperCase(),name:e.toUpperCase(),price:a.c,change:a.c-a.o,changePercent:(a.c-a.o)/a.o*100,volume:a.v,marketCap:void 0,pe:void 0,dividend:void 0}}catch(t){throw console.error("Polygon fallback also failed:",t),Error(`Failed to fetch quote for ${e}`)}}}async getHistoricalData(e,a="day",n=1,o,i){try{let s=await t.default.get(`${r}/v2/aggs/ticker/${e}/range/${n}/${a}/${o}/${i}`,{params:{adjusted:"true",sort:"asc",limit:5e4,apikey:this.apiKey}});if(!s.data.results||0===s.data.results.length)return console.warn(`No historical data found for ${e}`),[];return s.data.results.map(e=>({timestamp:e.t,open:e.o,high:e.h,low:e.l,close:e.c,volume:e.v}))}catch(t){throw console.error(`Error fetching historical data for ${e}:`,t),t.response&&(console.error(`Polygon API Error: ${t.response.status} - ${t.response.statusText}`),console.error("Response data:",t.response.data)),Error(`Failed to fetch historical data for ${e}: ${t.message}`)}}async getCompanyDetails(e){try{return(await t.default.get(`${r}/v3/reference/tickers/${e}`,{params:{apikey:this.apiKey}})).data.results}catch(e){return console.error("Error fetching company details:",e),null}}async getMarketStatus(){try{return(await t.default.get(`${r}/v1/marketstatus/now`,{params:{apikey:this.apiKey}})).data}catch(e){return console.error("Error fetching market status:",e),null}}async searchStocks(e,a=10){try{return(await t.default.get(`${r}/v3/reference/tickers`,{params:{search:e,market:"stocks",active:"true",limit:a,apikey:this.apiKey}})).data.results||[]}catch(e){return console.error("Error searching stocks:",e),[]}}}new n},29547,e=>{"use strict";e.s(["FMPAPI",()=>n]);var t=e.i(55362);let r="https://financialmodelingprep.com/api",a=process.env.FMP_API_KEY;class n{apiKey;constructor(e){if(this.apiKey=e||a||"",!this.apiKey)throw Error("FMP API key is required")}async getStockQuote(e){try{let a=(await t.default.get(`${r}/v3/quote/${e}`,{params:{apikey:this.apiKey}})).data[0];if(!a)throw Error(`No data found for symbol ${e}`);return{symbol:a.symbol,name:a.name||a.symbol,price:a.price,change:a.change,changePercent:a.changesPercentage,volume:a.volume,marketCap:a.marketCap,pe:a.pe,dividend:void 0}}catch(t){throw console.error("Error fetching FMP stock quote:",t),Error(`Failed to fetch quote for ${e}`)}}async getCompanyProfile(e){try{return(await t.default.get(`${r}/v3/profile/${e}`,{params:{apikey:this.apiKey}})).data[0]}catch(e){return console.error("Error fetching company profile:",e),null}}async getFinancialRatios(e){try{return(await t.default.get(`${r}/v3/ratios/${e}`,{params:{apikey:this.apiKey}})).data[0]}catch(e){return console.error("Error fetching financial ratios:",e),null}}async getKeyMetrics(e){try{return(await t.default.get(`${r}/v3/key-metrics/${e}`,{params:{apikey:this.apiKey}})).data[0]}catch(e){return console.error("Error fetching key metrics:",e),null}}async getAnalystRecommendations(e){try{return(await t.default.get(`${r}/v3/analyst-stock-recommendations/${e}`,{params:{apikey:this.apiKey}})).data}catch(e){return console.error("Error fetching analyst recommendations:",e),[]}}async getEarningsCalendar(e,a){try{let n={apikey:this.apiKey};return e&&(n.from=e),a&&(n.to=a),(await t.default.get(`${r}/v3/earning_calendar`,{params:n})).data}catch(e){return console.error("Error fetching earnings calendar:",e),[]}}async getEconomicCalendar(e,a){try{let n={apikey:this.apiKey};return e&&(n.from=e),a&&(n.to=a),(await t.default.get(`${r}/v3/economic_calendar`,{params:n})).data}catch(e){return console.error("Error fetching economic calendar:",e),[]}}async searchStocks(e,a=10){try{return(await t.default.get(`${r}/v3/search`,{params:{query:e,limit:a,apikey:this.apiKey}})).data}catch(e){return console.error("Error searching stocks:",e),[]}}async getSectorPerformance(){try{return(await t.default.get(`${r}/v3/sector-performance`,{params:{apikey:this.apiKey}})).data}catch(e){return console.error("Error fetching sector performance:",e),[]}}async getMarketMovers(e){try{return(await t.default.get(`${r}/v3/stock_market/${e}`,{params:{apikey:this.apiKey}})).data}catch(t){return console.error(`Error fetching market ${e}:`,t),[]}}async getEarningsCalendar(e,a=30){try{let n=new Date;n.setDate(n.getDate()-a);let o=new Date;return(await t.default.get(`${r}/v3/earning_calendar`,{params:{apikey:this.apiKey,from:n.toISOString().split("T")[0],to:o.toISOString().split("T")[0],...e&&{symbol:e.toUpperCase()}}})).data||[]}catch(e){return console.error("Error fetching earnings calendar:",e),[]}}async getStockNews(e,a=50){try{return(await t.default.get(`${r}/v3/stock_news`,{params:{apikey:this.apiKey,tickers:e.toUpperCase(),limit:a}})).data||[]}catch(e){return console.error("Error fetching stock news:",e),[]}}async getAnalystRecommendations(e,a=30){try{return(await t.default.get(`${r}/v3/analyst-stock-recommendations/${e.toUpperCase()}`,{params:{apikey:this.apiKey,limit:a}})).data||[]}catch(e){return console.error("Error fetching analyst recommendations:",e),[]}}async getInsiderTrading(e,a=30){try{let n=await t.default.get(`${r}/v4/insider-trading`,{params:{apikey:this.apiKey,symbol:e.toUpperCase(),limit:5*a}}),o=new Date;return o.setDate(o.getDate()-a),(n.data||[]).filter(e=>new Date(e.filingDate)>=o)}catch(e){return console.error("Error fetching insider trading:",e),[]}}async getSECFilings(e,a=30){try{let n=await t.default.get(`${r}/v3/sec_filings/${e.toUpperCase()}`,{params:{apikey:this.apiKey,limit:2*a}}),o=new Date;return o.setDate(o.getDate()-a),(n.data||[]).filter(e=>new Date(e.filedDate)>=o)}catch(e){return console.error("Error fetching SEC filings:",e),[]}}async getPreMarketQuote(e){try{let a=(await t.default.get(`${r}/v3/quote/${e.toUpperCase()}`,{params:{apikey:this.apiKey}})).data[0];if(!a)return null;return{symbol:a.symbol,price:a.price,previousClose:a.previousClose,change:a.change,changePercent:a.changesPercentage,volume:a.volume,marketCap:a.marketCap,avgVolume:a.avgVolume,preMarketPrice:a.preMarketPrice||a.price,preMarketChange:a.preMarketChange||a.change,preMarketChangePercent:a.preMarketChangePercent||a.changesPercentage}}catch(e){return console.error("Error fetching pre-market quote:",e),null}}async getMultiplePreMarketQuotes(e){try{let a=e.map(e=>e.toUpperCase()).join(",");return((await t.default.get(`${r}/v3/quote/${a}`,{params:{apikey:this.apiKey}})).data||[]).map(e=>({symbol:e.symbol,price:e.price,previousClose:e.previousClose,change:e.change,changePercent:e.changesPercentage,volume:e.volume,marketCap:e.marketCap,avgVolume:e.avgVolume,preMarketPrice:e.preMarketPrice||e.price,preMarketChange:e.preMarketChange||e.change,preMarketChangePercent:e.preMarketChangePercent||e.changesPercentage}))}catch(e){return console.error("Error fetching multiple pre-market quotes:",e),[]}}async getCompanyProfile(e){try{return(await t.default.get(`${r}/v3/profile/${e.toUpperCase()}`,{params:{apikey:this.apiKey}})).data[0]||null}catch(e){return console.error("Error fetching company profile:",e),null}}}new n},58445,e=>{"use strict";e.s(["TechnicalIndicators",()=>t]);class t{static sma(e,t){let r=[];for(let a=t-1;a<e.length;a++){let n=e.slice(a-t+1,a+1).reduce((e,t)=>e+t,0);r.push(n/t)}return r}static ema(e,t){let r=[],a=2/(t+1),n=e.slice(0,t).reduce((e,t)=>e+t,0)/t;r.push(n);for(let o=t;o<e.length;o++)r.push(n=e[o]*a+n*(1-a));return r}static rsi(e,t=14){let r=[],a=[];for(let t=1;t<e.length;t++){let n=e[t]-e[t-1];r.push(n>0?n:0),a.push(n<0?Math.abs(n):0)}let n=this.sma(r,t),o=this.sma(a,t);return n.map((e,t)=>100-100/(1+e/o[t]))}static macd(e,t=12,r=26,a=9){let n=this.ema(e,t),o=this.ema(e,r),i=n.slice(r-t).map((e,t)=>e-o[t]),s=this.ema(i,a),l=i.slice(a-1).map((e,t)=>e-s[t]);return{macd:i,signal:s,histogram:l}}static bollingerBands(e,t=20,r=2){return this.sma(e,t).map((a,n)=>{let o=Math.sqrt(e.slice(n,n+t).reduce((e,t)=>e+Math.pow(t-a,2),0)/t);return{upper:a+o*r,middle:a,lower:a-o*r}})}static findSupportResistance(e,t=20){let r=e.map(e=>e.high),a=e.map(e=>e.low),n=[],o=[];for(let i=t;i<e.length-t;i++){let e=r[i],s=a[i],l=r.slice(i-t,i).every(t=>t<=e)&&r.slice(i+1,i+t+1).every(t=>t<=e),c=a.slice(i-t,i).every(e=>e>=s)&&a.slice(i+1,i+t+1).every(e=>e>=s);l&&n.push(e),c&&o.push(s)}return{support:o,resistance:n}}static volumeAnalysis(e,t=20){let r=e.map(e=>e.volume),a=this.sma(r,t),n=r[r.length-1],o=a[a.length-1];return{currentVolume:n,averageVolume:o,volumeRatio:n/o,isHighVolume:n>1.5*o,isLowVolume:n<.5*o}}static analyzeSwingSetup(e){let t=e.map(e=>e.close),r=[],a=this.rsi(t),n=a[a.length-1],o="NEUTRAL",i=`RSI: ${n.toFixed(2)}`;n<30?(o="BUY",i+=" - Oversold condition, potential bounce"):n>70?(o="SELL",i+=" - Overbought condition, potential pullback"):i+=" - Neutral zone",r.push({name:"RSI",value:n,signal:o,description:i});let s=this.sma(t,20),l=this.sma(t,50),c=t[t.length-1],u=s[s.length-1],p=l[l.length-1],d="NEUTRAL",m=`Price vs SMA20: ${((c/u-1)*100).toFixed(2)}%`;c>u&&u>p?(d="BUY",m+=" - Bullish trend"):c<u&&u<p?(d="SELL",m+=" - Bearish trend"):m+=" - Mixed signals",r.push({name:"Moving Averages",value:(c/u-1)*100,signal:d,description:m});let h=this.macd(t),g=h.macd[h.macd.length-1],y=h.signal[h.signal.length-1],f=h.histogram[h.histogram.length-1],v="NEUTRAL",k=`MACD: ${g.toFixed(4)}, Signal: ${y.toFixed(4)}`;g>y&&f>0?(v="BUY",k+=" - Bullish momentum"):g<y&&f<0?(v="SELL",k+=" - Bearish momentum"):k+=" - Momentum shifting",r.push({name:"MACD",value:f,signal:v,description:k});let w=this.volumeAnalysis(e),E="NEUTRAL",x=`Volume: ${(100*w.volumeRatio).toFixed(0)}% of average`;return w.isHighVolume?(E="BUY",x+=" - High volume confirms move"):w.isLowVolume?(E="SELL",x+=" - Low volume, weak conviction"):x+=" - Normal volume",r.push({name:"Volume",value:w.volumeRatio,signal:E,description:x}),r}}},96341,e=>{"use strict";e.s(["SwingTradingStrategies",()=>r]);var t=e.i(58445);class r{static DEFAULT_CRITERIA={minPrice:5,minVolume:5e5,minMarketCap:8e8,minATRPercent:2,above200SMA:!0,maxDistanceFrom8EMA:2,minRoomToResistance:1,scanTimeStart:"12:00",scanTimeEnd:"16:00",maxRiskPerTrade:1,maxConcurrentPositions:3};static analyzeOvernightMomentum(e,r,a,n=1e5){if(r.length<50)return null;let o=r.map(e=>e.close),i=r.map(e=>e.high),s=r.map(e=>e.low);r.map(e=>e.volume);let l=a.price,c=a.volume,u=a.changePercent,p=t.TechnicalIndicators.sma(o,Math.min(50,o.length-1)),d=t.TechnicalIndicators.ema(o,Math.min(8,o.length-1)),m=this.calculateATR(r,Math.min(14,r.length-1)),h=p[p.length-1],g=d[d.length-1],y=m[m.length-1];if(!this.passesBasicFilters(a,c,h,l)||u<2||Math.abs(l-g)/y>this.DEFAULT_CRITERIA.maxDistanceFrom8EMA)return null;let f=Math.max(this.calculateVWAP(r.slice(-1)[0]),.98*l),v=i[i.length-1],k=s[s.length-1],w=(l-k)/(v-k);if(w<.5)return null;let E=this.calculateRoomToResistance(r,l,y);if(E<this.DEFAULT_CRITERIA.minRoomToResistance)return null;let x=.0075*n,R=Math.floor(x/(l-f)),P=[1.03*l,1.05*l,1.08*l];return{strategy:"overnight_momentum",confidence:this.calculateOvernightConfidence(u,w,c,E),entryPrice:l,stopLoss:f,targets:P,positionSize:R,riskAmount:x,holdingPeriod:"overnight",keyLevel:f,invalidation:`Daily close below ${f.toFixed(2)} or gap down below level`,notes:["Enter final 30-60 min before close","Exit pre-market on strength or first 45min","Hard stop if gaps below defended level","Scale out aggressively if gaps >1 ATR up"],preciseEntry:{price:.999*l,orderType:"limit",timing:"Final 30-60 minutes before market close",conditions:[`Stock holding above ${f.toFixed(2)} (defended level)`,`Volume above ${(.8*c).toLocaleString()} shares`,`Price above ${g.toFixed(2)} (8-EMA)`,"No late-day selling pressure"],urgency:"wait_for_pullback"},preciseExit:{stopLoss:{price:.995*f,orderType:"stop",reason:"Defended level broken - invalidates setup",triggerConditions:["Any close below defended level","Gap down below key level","Heavy selling into close"]},takeProfits:[{price:P[0],percentage:33,target:"T1 - Pre-market (3%)",reasoning:"Take profits on pre-market strength",orderType:"limit"},{price:P[1],percentage:33,target:"T2 - Opening hour (5%)",reasoning:"Scale out on opening momentum",orderType:"limit"},{price:P[2],percentage:34,target:"T3 - Extended (8%)",reasoning:"Final exit on extended move",orderType:"limit"}]},riskManagement:{maxRiskDollars:x,accountRiskPercent:.75,sharesForRisk:R,invalidationPrice:f,timeStopHours:18,maxDrawdownPercent:2},executionPlan:{entryInstructions:["1. Wait for final 30-60 minutes before close","2. Confirm stock is holding defended level","3. Place limit order slightly below current price","4. Cancel if not filled by close"],exitInstructions:["1. Set stop-loss immediately after fill","2. Monitor pre-market for gap up","3. Scale out 1/3 at each target level","4. Exit all by 10:15 AM if no momentum"],monitoringPoints:["Pre-market price action and volume","Opening gap and first 15-minute candle","Key level defense throughout session","Overall market sentiment"],contingencyPlans:["If gaps down: Exit immediately at market open","If gaps up >2%: Scale out more aggressively","If sideways: Exit by 10:15 AM","If market weakness: Tighten stops"]}}}static analyzeTechnicalBreakout(e,r,a,n=1e5){if(r.length<50)return null;let o=r.map(e=>e.close),i=r.map(e=>e.volume),s=a.price,l=t.TechnicalIndicators.sma(o,Math.min(50,o.length-1)),c=t.TechnicalIndicators.ema(o,Math.min(8,o.length-1)),u=this.calculateATR(r,Math.min(14,r.length-1)),p=l[l.length-1],d=c[c.length-1],m=u[u.length-1];if(!this.passesBasicFilters(a,a.volume,p,s)||s<=p)return null;let h=Math.abs(s-d)/s*100;if(h>3||!this.checkEMAReclaim(o,c,5))return null;let g=t.TechnicalIndicators.sma(i,20),y=g[g.length-1],f=a.volume/y;if(f<1.2)return null;let v=this.calculateRoomToResistance(r,s,m);if(v<1.5)return null;let k=.01*n,w=Math.floor(k/(s-d)),E=[1.05*s,1.1*s,1.15*s];return{strategy:"technical_breakout",confidence:this.calculateBreakoutConfidence(h,f,v,a.changePercent),entryPrice:s,stopLoss:d,targets:E,positionSize:w,riskAmount:k,holdingPeriod:"days_to_weeks",keyLevel:d,invalidation:`Daily close below 8-EMA (${d.toFixed(2)})`,notes:["Enter on afternoon reclaim of 8-EMA","Add only on higher-low pullbacks to 8-EMA","Scale partials at resistance levels","Exit on daily close below 8-EMA"],preciseEntry:{price:1.002*d,orderType:"limit",timing:"Afternoon reclaim or first pullback to 8-EMA",conditions:[`Price reclaiming ${d.toFixed(2)} (8-EMA) with volume`,`Above ${p.toFixed(2)} (50-day SMA)`,`Volume expansion above ${(1.2*a.volume).toLocaleString()}`,"No major resistance overhead"],urgency:"breakout_confirmation"},preciseExit:{stopLoss:{price:.998*d,orderType:"stop",reason:"8-EMA breakdown invalidates trend-follow setup",triggerConditions:["Daily close below 8-EMA","Intraday break with volume","Loss of 50-SMA support"]},takeProfits:[{price:E[0],percentage:25,target:"R1 - First resistance (5%)",reasoning:"Take partial profits at first resistance",orderType:"limit"},{price:E[1],percentage:35,target:"R2 - Major resistance (10%)",reasoning:"Scale out at major resistance level",orderType:"limit"},{price:E[2],percentage:40,target:"R3 - Extension (15%)",reasoning:"Final exit on extended breakout",orderType:"limit"}]},riskManagement:{maxRiskDollars:k,accountRiskPercent:1,sharesForRisk:w,invalidationPrice:d,timeStopHours:72,maxDrawdownPercent:3},executionPlan:{entryInstructions:["1. Wait for afternoon reclaim of 8-EMA","2. Confirm volume expansion on breakout","3. Place limit order above 8-EMA","4. Only enter on higher-low pullbacks"],exitInstructions:["1. Set stop-loss below 8-EMA immediately","2. Scale out 25% at first resistance","3. Trail stop to breakeven after R1","4. Exit remaining on 8-EMA breakdown"],monitoringPoints:["8-EMA as dynamic support/resistance","Volume confirmation on moves","Overall market trend alignment","Sector strength/weakness"],contingencyPlans:["If fails at resistance: Tighten stops","If market weakness: Exit early","If sector rotation: Consider exit","If extended: Take more profits"]}}}static passesBasicFilters(e,t,r,a){return a>=this.DEFAULT_CRITERIA.minPrice&&t>=this.DEFAULT_CRITERIA.minVolume&&(e.marketCap||0)>=this.DEFAULT_CRITERIA.minMarketCap&&a>r}static calculateATR(e,r){let a=[];for(let t=1;t<e.length;t++){let r=e[t].high,n=e[t].low,o=e[t-1].close,i=Math.max(r-n,Math.abs(r-o),Math.abs(n-o));a.push(i)}return t.TechnicalIndicators.sma(a,r)}static calculateVWAP(e){return(e.high+e.low+e.close)/3}static calculateRoomToResistance(e,t,r){return(Math.max(...e.slice(-20).map(e=>e.high))-t)/r}static checkEMAReclaim(e,t,r){for(let a=Math.max(0,e.length-r);a<e.length-1;a++)if(e[a]<t[a]&&e[a+1]>t[a+1])return!0;return!1}static calculateOvernightConfidence(e,t,r,a){let n=50;return e>5?n+=15:e>3?n+=10:e>2&&(n+=5),t>.8?n+=15:t>.6?n+=10:t>.5&&(n+=5),r>2e6?n+=10:r>1e6&&(n+=5),a>2?n+=10:a>1.5&&(n+=5),Math.min(95,Math.max(30,n))}static calculateBreakoutConfidence(e,t,r,a){let n=60;return e<1?n+=15:e<2?n+=10:e<3&&(n+=5),t>2?n+=15:t>1.5?n+=10:t>1.2&&(n+=5),r>3?n+=15:r>2?n+=10:r>1.5&&(n+=5),a>2&&(n+=5),Math.min(95,Math.max(40,n))}}},68559,(e,t,r)=>{},66740,e=>{"use strict";e.s(["handler",()=>$,"patchFetch",()=>T,"routeModule",()=>M,"serverHooks",()=>S,"workAsyncStorage",()=>C,"workUnitAsyncStorage",()=>A],66740);var t=e.i(11971),r=e.i(6780),a=e.i(51842),n=e.i(62950),o=e.i(21346),i=e.i(30506),s=e.i(63077),l=e.i(34765),c=e.i(64182),u=e.i(85062),p=e.i(51548),d=e.i(95133),m=e.i(8819),h=e.i(41050),g=e.i(93695);e.i(96641);var y=e.i(3893);e.s(["GET",()=>R],4650);var f=e.i(59169),v=e.i(96341),k=e.i(78006),w=e.i(29547),E=e.i(86678),x=e.i(97669);async function R(e,{params:t}){try{let{symbol:r}=await t,{searchParams:a}=new URL(e.url),n=parseInt(a.get("accountSize")||"100000"),o=a.get("strategy");if(!r)return f.NextResponse.json({error:"Symbol parameter is required"},{status:400});let i=new w.FMPAPI(process.env.FMP_API_KEY),s=new k.PolygonAPI(process.env.POLYGON_API_KEY),[l,c]=await Promise.all([i.getStockQuote(r.toUpperCase()),P(s,r.toUpperCase())]);if(!c||c.length<30)return f.NextResponse.json({error:"Insufficient historical data for strategy analysis - need at least 30 days"},{status:400});let u={symbol:r.toUpperCase(),quote:l,accountSize:n,scanTime:new Date().toISOString()};return o&&"overnight"!==o&&"both"!==o||(u.overnightSetup=v.SwingTradingStrategies.analyzeOvernightMomentum(r.toUpperCase(),c,l,n)),o&&"breakout"!==o&&"both"!==o||(u.breakoutSetup=v.SwingTradingStrategies.analyzeTechnicalBreakout(r.toUpperCase(),c,l,n)),u.overnightSetup&&u.breakoutSetup?(u.bestStrategy=u.overnightSetup.confidence>u.breakoutSetup.confidence?"overnight_momentum":"technical_breakout",u.overallScore=Math.max(u.overnightSetup.confidence,u.breakoutSetup.confidence)+5):u.overnightSetup?(u.bestStrategy="overnight_momentum",u.overallScore=u.overnightSetup.confidence):u.breakoutSetup?(u.bestStrategy="technical_breakout",u.overallScore=u.breakoutSetup.confidence):(u.overallScore=0,u.message="No valid swing trading setups found for current market conditions"),f.NextResponse.json(u)}catch(e){return console.error("Error in strategy analysis API:",e),f.NextResponse.json({error:"Failed to perform strategy analysis"},{status:500})}}async function P(e,t){let r=(0,E.format)(new Date,"yyyy-MM-dd"),a=(0,E.format)((0,x.subDays)(new Date,100),"yyyy-MM-dd");try{return await e.getHistoricalData(t,"day",1,a,r)}catch(e){throw console.warn(`Historical data failed for ${t}`),e}}var b=e.i(4650);let M=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/analysis/strategy/[symbol]/route",pathname:"/api/analysis/strategy/[symbol]",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/swing-trader-ai/src/app/api/analysis/strategy/[symbol]/route.ts",nextConfigOutput:"",userland:b}),{workAsyncStorage:C,workUnitAsyncStorage:A,serverHooks:S}=M;function T(){return(0,a.patchFetch)({workAsyncStorage:C,workUnitAsyncStorage:A})}async function $(e,t,a){var f;let v="/api/analysis/strategy/[symbol]/route";v=v.replace(/\/index$/,"")||"/";let k=await M.prepare(e,t,{srcPage:v,multiZoneDraftMode:!1});if(!k)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:w,params:E,nextConfig:x,isDraftMode:R,prerenderManifest:P,routerServerContext:b,isOnDemandRevalidate:C,revalidateOnlyGenerated:A,resolvedPathname:S}=k,T=(0,i.normalizeAppPath)(v),$=!!(P.dynamicRoutes[T]||P.routes[S]);if($&&!R){let e=!!P.routes[S],t=P.dynamicRoutes[T];if(t&&!1===t.fallback&&!e)throw new g.NoFallbackError}let I=null;!$||M.isDev||R||(I="/index"===(I=S)?"/":I);let D=!0===M.isDev||!$,_=$&&!D,F=e.method||"GET",U=(0,o.getTracer)(),K=U.getActiveScopeSpan(),N={params:E,prerenderManifest:P,renderOpts:{experimental:{cacheComponents:!!x.experimental.cacheComponents,authInterrupts:!!x.experimental.authInterrupts},supportsDynamicResponse:D,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(f=x.experimental)?void 0:f.cacheLife,isRevalidate:_,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>M.onRequestError(e,t,a,b)},sharedContext:{buildId:w}},O=new s.NodeNextRequest(e),L=new s.NodeNextResponse(t),q=l.NextRequestAdapter.fromNodeNextRequest(O,(0,l.signalFromNodeResponse)(t));try{let i=async r=>M.handle(q,N).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=U.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==c.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${F} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${F} ${e.url}`)}),s=async o=>{var s,l;let c=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&C&&A&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let s=await i(o);e.fetchMetrics=N.renderOpts.fetchMetrics;let l=N.renderOpts.pendingWaitUntil;l&&a.waitUntil&&(a.waitUntil(l),l=void 0);let c=N.renderOpts.collectedTags;if(!$)return await (0,p.sendResponse)(O,L,s,N.renderOpts.pendingWaitUntil),null;{let e=await s.blob(),t=(0,d.toNodeOutgoingHttpHeaders)(s.headers);c&&(t[h.NEXT_CACHE_TAGS_HEADER]=c),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==N.renderOpts.collectedRevalidate&&!(N.renderOpts.collectedRevalidate>=h.INFINITE_CACHE)&&N.renderOpts.collectedRevalidate,a=void 0===N.renderOpts.collectedExpire||N.renderOpts.collectedExpire>=h.INFINITE_CACHE?void 0:N.renderOpts.collectedExpire;return{value:{kind:y.CachedRouteKind.APP_ROUTE,status:s.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await M.onRequestError(e,t,{routerKind:"App Router",routePath:v,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:_,isOnDemandRevalidate:C})},b),t}},g=await M.handleResponse({req:e,nextConfig:x,cacheKey:I,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:P,isRoutePPREnabled:!1,isOnDemandRevalidate:C,revalidateOnlyGenerated:A,responseGenerator:c,waitUntil:a.waitUntil});if(!$)return null;if((null==g||null==(s=g.value)?void 0:s.kind)!==y.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==g||null==(l=g.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",C?"REVALIDATED":g.isMiss?"MISS":g.isStale?"STALE":"HIT"),R&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let f=(0,d.fromNodeOutgoingHttpHeaders)(g.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&$||f.delete(h.NEXT_CACHE_TAGS_HEADER),!g.cacheControl||t.getHeader("Cache-Control")||f.get("Cache-Control")||f.set("Cache-Control",(0,m.getCacheControlHeader)(g.cacheControl)),await (0,p.sendResponse)(O,L,new Response(g.value.body,{headers:f,status:g.value.status||200})),null};K?await s(K):await U.withPropagatedContext(e.headers,()=>U.trace(c.BaseServerSpan.handleRequest,{spanName:`${F} ${e.url}`,kind:o.SpanKind.SERVER,attributes:{"http.method":F,"http.target":e.url}},s))}catch(t){if(t instanceof g.NoFallbackError||await M.onRequestError(e,t,{routerKind:"App Router",routePath:T,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:_,isOnDemandRevalidate:C})}),$)throw t;return await (0,p.sendResponse)(O,L,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__150f0510._.js.map