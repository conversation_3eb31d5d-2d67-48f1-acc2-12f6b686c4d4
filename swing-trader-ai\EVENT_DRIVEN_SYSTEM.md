# Event-Driven Catalyst Detection & Perfect-Pick Trading System

## Overview

The Event-Driven Catalyst Detection and Perfect-Pick Trading System is a comprehensive swing trading platform that automatically identifies high-probability trading opportunities based on fundamental catalysts combined with precise technical analysis.

## 🎯 Key Features

### 1. Real-Time Catalyst Detection Engine
- **Earnings Beats with Raised Guidance** (Tier 1 - Highest Priority)
- **FDA Approvals & Drug Trial Results** (Tier 1)
- **Major Contract Wins & Partnerships** (Tier 1)
- **Analyst Upgrades/Downgrades** (Tier 2)
- **Stock Split Announcements** (Tier 2)
- **Merger & Acquisition News** (Tier 1)
- **Insider Trading Activity** (>$1M transactions)
- **SEC Filing Monitoring** (8-K, 13D, etc.)

### 2. Pre-Market Gap Scanner
- **Automatic scanning at 4:00 AM, 6:00 AM, 8:00 AM, and 9:00 AM EST**
- **Filters:**
  - Price > $1.00
  - Gap ≥ +3.0% from previous close
  - Market cap ≥ $800M (large caps only)
  - Pre-market volume ≥ 20,000 shares
  - Pre-market dollar volume ≥ $1,000,000
  - Must have identifiable catalyst

### 3. Technical Gate Analysis
- **Daily Trend Confirmation** (higher highs, higher lows over 20+ days)
- **Price above 200-day Simple Moving Average**
- **Price above 8-day Exponential Moving Average**
- **EMA8 Respect Analysis** (stock consistently holds above 8-EMA)
- **All-Time High Detection** (no overhead resistance)
- **Clean Breakout Pattern Recognition**
- **Volume Expansion Confirmation**

### 4. Perfect-Pick Setup Generation
- **Combines catalyst quality + technical strength + gap criteria**
- **Automated position sizing based on account risk (1%, 2%, or 3%)**
- **Pre-Market Low (PML) based stop losses**
- **Minimum 3:1 Risk/Reward ratio requirement**
- **Scaled profit targets (3R, 4R, 5R)**

### 5. Intraday Entry Trigger System
- **Pre-Market High (PMH) break detection**
- **VWAP pullback entry opportunities**
- **Volume expansion validation**
- **First 5-minute candle close confirmation**

### 6. Real-Time Alert System
- **Browser notifications for critical alerts**
- **Catalyst discovery alerts**
- **Perfect-Pick setup notifications**
- **PMH break alerts**
- **Stop loss and profit target alerts**
- **Volume spike notifications**

### 7. Comprehensive Backtesting Framework
- **Historical Perfect-Pick criteria validation**
- **Success rate tracking by catalyst type**
- **Performance analysis by setup grade**
- **R-multiple distribution analysis**
- **Monthly/quarterly performance reports**

## 🏗️ System Architecture

### Core Components

1. **CatalystDetectionEngine** (`/src/lib/catalystDetection.ts`)
   - Real-time news analysis
   - SEC filing monitoring
   - Earnings data processing
   - Analyst recommendation tracking

2. **PreMarketGapScanner** (`/src/lib/preMarketGapScanner.ts`)
   - Automated universe scanning
   - Gap criteria validation
   - Catalyst correlation

3. **TechnicalGateAnalyzer** (`/src/lib/technicalGateAnalysis.ts`)
   - Moving average analysis
   - Trend confirmation
   - Support/resistance calculation
   - Volume analysis

4. **PerfectPickTradingSystem** (`/src/lib/perfectPickTradingSystem.ts`)
   - Complete pipeline integration
   - Setup scoring and ranking
   - Risk management calculations

5. **AlertSystem** (`/src/lib/alertSystem.ts`)
   - Real-time notifications
   - Priority-based alerting
   - Action-based alerts

6. **PerfectPickBacktester** (`/src/lib/backtesting.ts`)
   - Historical validation
   - Performance analytics
   - Strategy optimization

### API Endpoints

- `GET /api/scanner/perfect-pick` - Run Perfect-Pick scan
- `GET /api/scanner/gap-scan` - Pre-market gap scanning
- `GET /api/catalyst/detect` - Catalyst detection for symbols
- `POST /api/backtest/perfect-pick` - Backtest Perfect-Pick setups

### UI Components

- **EventDrivenScanner** - Main scanner interface
- **PerfectPickSetupCard** - Enhanced setup display with catalyst info
- **AlertCenter** - Real-time notification center

## 📊 Perfect-Pick Criteria

### Tier 1 Catalysts (Highest Priority)
- Earnings beat + raised guidance
- FDA approvals
- Major partnership announcements
- Merger & acquisition news

### Tier 2 Catalysts (Secondary)
- Analyst upgrades with significant price target increases
- Stock split announcements
- Sector/theme momentum plays

### Technical Requirements
- ✅ Primary uptrend confirmed (20+ days)
- ✅ Price above 200-day SMA
- ✅ Price above 8-day EMA
- ✅ Clean gap (3-15% range)
- ✅ Adequate liquidity (>500K avg daily volume)
- ✅ Large cap (>$800M market cap)

### Risk Management Rules
- **Stop Loss:** Pre-Market Low (PML) of gap day
- **Position Sizing:** 1-3% account risk, max 5% position size
- **Minimum R/R:** 3:1 risk/reward ratio required
- **Scale-out Plan:** 25% at 3R, 25% at 4R, 25% at 5R, hold 25% for trend

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- FMP API key (Financial Modeling Prep)
- Polygon.io API key

### Environment Variables
```env
FMP_API_KEY=your_fmp_api_key
POLYGON_API_KEY=your_polygon_api_key
```

### Installation
```bash
npm install
npm run dev
```

### Usage
1. Navigate to the **Event-Driven** tab
2. Click **Scan Perfect-Picks** to run the complete analysis
3. Review generated setups with catalyst information
4. Use the **Alert Center** (bell icon) for real-time notifications
5. Execute trades through the integrated trading interface

## 📈 Performance Metrics

The system tracks comprehensive performance metrics:

- **Win Rate** - Percentage of profitable trades
- **Average R-Multiple** - Average risk-adjusted return
- **Profit Factor** - Gross profit / Gross loss ratio
- **Sharpe Ratio** - Risk-adjusted return metric
- **Maximum Drawdown** - Largest peak-to-trough decline
- **Average Holding Period** - Days per trade
- **Success Rate by Catalyst Type** - Performance breakdown
- **Success Rate by Setup Grade** - Quality correlation

## 🔧 Configuration

### Scanner Universe
Default universe includes 65+ stocks:
- Mega caps: AAPL, MSFT, GOOGL, AMZN, NVDA, META, TSLA, etc.
- High-beta growth: AMD, CRM, SNOW, PLTR, ROKU, etc.
- Biotech/Pharma: GILD, BIIB, REGN, VRTX, MRNA, etc.

### Scan Schedule
- **4:00 AM EST** - Initial pre-market scan
- **6:00 AM EST** - Updated scan with more volume
- **8:00 AM EST** - Final pre-market scan
- **9:00 AM EST** - Market open preparation
- **Every 15 minutes** during market hours for updates

### Alert Priorities
- **Critical** - Stop losses hit, major catalyst news
- **High** - Perfect-Pick setups found, PMH breaks
- **Medium** - Gap opportunities, analyst upgrades
- **Low** - Volume spikes, general market updates

## 🧪 Backtesting

The system includes comprehensive backtesting capabilities:

```javascript
// Example backtest API call
const response = await fetch('/api/backtest/perfect-pick', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    setups: perfectPickSetups,
    maxHoldingDays: 30,
    startDate: '2023-01-01',
    endDate: '2024-01-01'
  })
})
```

## 📱 Mobile Responsiveness

The system is fully responsive and works on:
- Desktop browsers
- Tablet devices
- Mobile phones
- Progressive Web App (PWA) capabilities

## 🔒 Risk Management

### Built-in Safety Features
- **Maximum position size caps** (5% of account)
- **Automatic stop loss calculation** based on PML
- **Risk percentage limits** (1-3% per trade)
- **Anti-pattern filters** (exclude downtrending stocks)
- **Liquidity requirements** (minimum volume thresholds)

### Exclusion Criteria
- Stocks in daily downtrends
- Gaps without identifiable catalysts
- Low liquidity names (<500K avg volume)
- Over-extended gaps (>15%)
- Recent negative catalysts

## 📞 Support & Documentation

For additional support:
- Review the comprehensive code documentation
- Check the API endpoint documentation
- Examine the component prop interfaces
- Review the backtesting examples

## 🎯 Future Enhancements

Planned improvements:
- Machine learning catalyst classification
- Options flow integration
- Social sentiment analysis
- Advanced chart pattern recognition
- Portfolio-level risk management
- Real-time P&L tracking
- Advanced backtesting metrics

---

**Disclaimer:** This system is for educational and research purposes. Always conduct your own analysis and risk management before making trading decisions.
