module.exports=[27478,e=>{"use strict";e.s(["CatalystDetectionEngine",()=>r]);var t=e.i(29547),a=e.i(78006);class r{fmpAPI;polygonAPI;catalystCache=new Map;impactMeasurements=new Map;constructor(e,r){this.fmpAPI=new t.FMPAPI(e),this.polygonAPI=new a.PolygonAPI(r)}async detectCatalysts(e){let t=[];try{let a=`${e}_${Math.floor(Date.now()/3e5)}`;if(this.catalystCache.has(a))return this.catalystCache.get(a);let[r,i,s,n,l]=await Promise.all([this.detectEarningsCatalysts(e),this.detectNewsCatalysts(e),this.detectAnalystCatalysts(e),this.detectInsiderCatalysts(e),this.detectSECFilingCatalysts(e)]);return t.push(...r,...i,...s,...n,...l),t.sort((e,t)=>{let a=this.getFreshnessWeight(e.freshness)-this.getFreshnessWeight(t.freshness);return 0!==a?a:t.qualityScore-e.qualityScore}),this.catalystCache.set(a,t),t}catch(t){return console.error(`Error detecting catalysts for ${e}:`,t),[]}}async detectEarningsCatalysts(e){let t=[];try{for(let a of(await this.fmpAPI.getEarningsCalendar(e,30)))if(this.isEarningsBeat(a)){let r={id:`earnings_${e}_${a.date}`,symbol:e,type:"earnings_beat_guidance",tier:"tier_1",impact:"bullish",title:`${e} Beats Earnings Expectations`,description:`Q${a.quarter} earnings beat: EPS ${a.actualEPS} vs ${a.estimatedEPS} expected`,source:"FMP Earnings Data",announcementTime:a.date,discoveredTime:new Date().toISOString(),qualityScore:this.calculateEarningsQualityScore(a),freshness:this.calculateFreshness(a.date),estimatedDuration:"short_term",verified:!0,tags:["earnings","beat","guidance"],metadata:{actualEPS:a.actualEPS,estimatedEPS:a.estimatedEPS,beatPercent:(a.actualEPS-a.estimatedEPS)/a.estimatedEPS*100,guidanceRaised:a.guidanceRaised||!1}};t.push(r)}}catch(t){console.error(`Error detecting earnings catalysts for ${e}:`,t)}return t}async detectNewsCatalysts(e){let t=[];try{for(let a of(await this.fmpAPI.getStockNews(e,50))){let r=this.classifyNewsAsCatalyst(a);if(r){let i={id:`news_${e}_${a.publishedDate}_${a.title.slice(0,20).replace(/\s+/g,"_")}`,symbol:e,type:r.type,tier:r.tier,impact:r.impact,title:a.title,description:a.text?.slice(0,200)+"...",source:a.site,sourceUrl:a.url,announcementTime:a.publishedDate,discoveredTime:new Date().toISOString(),qualityScore:this.calculateNewsQualityScore(a,r.type),freshness:this.calculateFreshness(a.publishedDate),estimatedDuration:this.estimateNewsDuration(r.type),verified:this.isReliableNewsSource(a.site),tags:this.extractNewsKeywords(a.title+" "+(a.text||"")),metadata:{site:a.site,sentiment:a.sentiment||"neutral"}};t.push(i)}}}catch(t){console.error(`Error detecting news catalysts for ${e}:`,t)}return t}async detectAnalystCatalysts(e){let t=[];try{for(let a of(await this.fmpAPI.getAnalystRecommendations(e,30)))if(this.isSignificantAnalystChange(a)){let r=a.newGrade>a.previousGrade,i={id:`analyst_${e}_${a.date}_${a.analystCompany}`,symbol:e,type:r?"analyst_upgrade":"analyst_downgrade",tier:"tier_2",impact:r?"bullish":"bearish",title:`${a.analystCompany} ${r?"Upgrades":"Downgrades"} ${e}`,description:`${a.analystName} at ${a.analystCompany} ${r?"upgraded":"downgraded"} to ${a.newGrade}`,source:"FMP Analyst Data",announcementTime:a.date,discoveredTime:new Date().toISOString(),qualityScore:this.calculateAnalystQualityScore(a),freshness:this.calculateFreshness(a.date),estimatedDuration:"medium_term",verified:!0,tags:["analyst",r?"upgrade":"downgrade",a.analystCompany.toLowerCase()],metadata:{analystCompany:a.analystCompany,analystName:a.analystName,previousGrade:a.previousGrade,newGrade:a.newGrade,priceTarget:a.priceTarget}};t.push(i)}}catch(t){console.error(`Error detecting analyst catalysts for ${e}:`,t)}return t}async detectInsiderCatalysts(e){let t=[];try{for(let a of(await this.fmpAPI.getInsiderTrading(e,30)))if(this.isSignificantInsiderTrade(a)){let r=a.transactionType.toLowerCase().includes("buy")||a.transactionType.toLowerCase().includes("purchase"),i={id:`insider_${e}_${a.filingDate}_${a.reportingName}`,symbol:e,type:r?"insider_buying":"insider_selling",tier:"tier_2",impact:r?"bullish":"bearish",title:`${a.reportingName} ${r?"Buys":"Sells"} ${e} Shares`,description:`${a.reportingName} (${a.typeOfOwner}) ${a.transactionType} ${a.securitiesTransacted} shares at $${a.price}`,source:"SEC Insider Trading Filings",announcementTime:a.filingDate,discoveredTime:new Date().toISOString(),qualityScore:this.calculateInsiderQualityScore(a),freshness:this.calculateFreshness(a.filingDate),estimatedDuration:"medium_term",verified:!0,tags:["insider",r?"buying":"selling",a.typeOfOwner.toLowerCase()],metadata:{reportingName:a.reportingName,typeOfOwner:a.typeOfOwner,transactionType:a.transactionType,securitiesTransacted:a.securitiesTransacted,price:a.price,dollarValue:a.securitiesTransacted*a.price}};t.push(i)}}catch(t){console.error(`Error detecting insider catalysts for ${e}:`,t)}return t}async detectSECFilingCatalysts(e){let t=[];try{for(let a of(await this.fmpAPI.getSECFilings(e,30)))if(this.isSignificantSECFiling(a)){let r={id:`sec_${e}_${a.filedDate}_${a.type}`,symbol:e,type:"sec_filing",tier:this.getSECFilingTier(a.type),impact:this.getSECFilingImpact(a.type),title:`${e} Files ${a.type}`,description:`${a.type} filing: ${a.description||"SEC regulatory filing"}`,source:"SEC EDGAR Database",sourceUrl:a.link,announcementTime:a.filedDate,discoveredTime:new Date().toISOString(),qualityScore:this.calculateSECFilingQualityScore(a),freshness:this.calculateFreshness(a.filedDate),estimatedDuration:this.estimateSECFilingDuration(a.type),verified:!0,tags:["sec","filing",a.type.toLowerCase()],metadata:{filingType:a.type,cik:a.cik,acceptedDate:a.acceptedDate}};t.push(r)}}catch(t){console.error(`Error detecting SEC filing catalysts for ${e}:`,t)}return t}getFreshnessWeight(e){switch(e){case"fresh":return 3;case"moderate":return 2;case"stale":return 1;default:return 0}}calculateFreshness(e){let t=new Date(e),a=(new Date().getTime()-t.getTime())/36e5;return a<24?"fresh":a<72?"moderate":"stale"}isEarningsBeat(e){return e.actualEPS>e.estimatedEPS&&(e.guidanceRaised||e.actualEPS>1.05*e.estimatedEPS)}calculateEarningsQualityScore(e){let t=5,a=(e.actualEPS-e.estimatedEPS)/e.estimatedEPS*100;return a>20?t+=3:a>10?t+=2:a>5&&(t+=1),e.guidanceRaised&&(t+=2),e.actualRevenue>e.estimatedRevenue&&(t+=1),Math.min(10,t)}classifyNewsAsCatalyst(e){let t=e.title.toLowerCase()+" "+(e.text||"").toLowerCase();return t.includes("fda")&&(t.includes("approval")||t.includes("approved"))?{type:"fda_approval",tier:"tier_1",impact:"bullish"}:t.includes("trial")&&(t.includes("positive")||t.includes("successful"))?{type:"drug_trial_results",tier:"tier_1",impact:"bullish"}:t.includes("contract")&&(t.includes("win")||t.includes("awarded"))?{type:"contract_win",tier:"tier_1",impact:"bullish"}:t.includes("partnership")||t.includes("collaboration")?{type:"partnership",tier:"tier_1",impact:"bullish"}:t.includes("merger")||t.includes("acquisition")||t.includes("buyout")?{type:"merger_acquisition",tier:"tier_1",impact:"bullish"}:t.includes("stock split")||t.includes("share split")?{type:"stock_split",tier:"tier_2",impact:"bullish"}:null}calculateNewsQualityScore(e,t){let a=5;return this.isReliableNewsSource(e.site)&&(a+=2),["fda_approval","merger_acquisition","earnings_beat_guidance"].includes(t)&&(a+=2),"positive"===e.sentiment?a+=1:"negative"===e.sentiment&&(a-=1),Math.max(1,Math.min(10,a))}isReliableNewsSource(e){return["reuters.com","bloomberg.com","wsj.com","cnbc.com","marketwatch.com","yahoo.com","sec.gov","fda.gov"].some(t=>e.toLowerCase().includes(t))}extractNewsKeywords(e){let t=[],a=e.toLowerCase();for(let[e,r]of Object.entries({earnings:["earnings","eps","revenue","profit"],fda:["fda","approval","drug","trial"],merger:["merger","acquisition","buyout","takeover"],partnership:["partnership","collaboration","alliance"],contract:["contract","deal","agreement"],upgrade:["upgrade","raised","increased"],downgrade:["downgrade","lowered","reduced"]}))r.some(e=>a.includes(e))&&t.push(e);return t}estimateNewsDuration(e){switch(e){case"earnings_beat_guidance":case"fda_approval":case"merger_acquisition":default:return"short_term";case"analyst_upgrade":case"analyst_downgrade":case"partnership":return"medium_term";case"stock_split":return"long_term"}}isSignificantAnalystChange(e){return Math.abs(e.newGrade-e.previousGrade)>=1&&e.priceTarget>0}calculateAnalystQualityScore(e){let t=5;["goldman sachs","morgan stanley","jp morgan","bank of america"].some(t=>e.analystCompany.toLowerCase().includes(t))&&(t+=2);let a=Math.abs(e.newGrade-e.previousGrade);return a>=2?t+=2:a>=1&&(t+=1),e.priceTargetChange>10&&(t+=1),Math.min(10,t)}isSignificantInsiderTrade(e){return e.securitiesTransacted*e.price>=1e6&&"Other"!==e.typeOfOwner}calculateInsiderQualityScore(e){let t=5,a=e.securitiesTransacted*e.price;return a>=1e7?t+=3:a>=5e6?t+=2:a>=1e6&&(t+=1),e.typeOfOwner.toLowerCase().includes("ceo")||e.typeOfOwner.toLowerCase().includes("cfo")?t+=2:e.typeOfOwner.toLowerCase().includes("director")&&(t+=1),Math.min(10,t)}isSignificantSECFiling(e){return["8-K","10-K","10-Q","13D","13G","S-1","S-4"].includes(e.type)}getSECFilingTier(e){return["8-K","13D","S-4"].includes(e)?"tier_1":["10-K","10-Q","13G"].includes(e)?"tier_2":"tier_3"}getSECFilingImpact(e){return"neutral"}calculateSECFilingQualityScore(e){let t=5;return["8-K","13D"].includes(e.type)?t+=2:["10-K","10-Q"].includes(e.type)&&(t+=1),Math.min(10,t)}estimateSECFilingDuration(e){switch(e){case"8-K":return"short_term";case"13D":default:return"medium_term";case"S-4":return"long_term"}}}},80412,e=>{"use strict";e.s(["PreMarketGapScanner",()=>i]);var t=e.i(29547),a=e.i(78006),r=e.i(27478);class i{fmpAPI;polygonAPI;catalystEngine;SCAN_UNIVERSE=["AAPL","MSFT","GOOGL","GOOG","AMZN","NVDA","META","TSLA","BRK.B","UNH","JNJ","XOM","JPM","V","PG","HD","CVX","MA","BAC","ABBV","PFE","AVGO","KO","MRK","PEP","TMO","COST","DIS","ABT","ACN","MCD","CSCO","LIN","VZ","ADBE","WMT","CRM","NFLX","DHR","NKE","TXN","NEE","BMY","ORCL","PM","RTX","UPS","QCOM","T","LOW","AMD","CRM","SNOW","PLTR","ROKU","ZM","DOCU","PTON","SHOP","SQ","PYPL","UBER","LYFT","ABNB","COIN","RBLX","U","DKNG","CRWD","ZS","GILD","BIIB","REGN","VRTX","ILMN","MRNA","BNTX","AMGN","CELG","ISRG"];constructor(e,i){this.fmpAPI=new t.FMPAPI(e),this.polygonAPI=new a.PolygonAPI(i),this.catalystEngine=new r.CatalystDetectionEngine(e,i)}async runGapScan(e){let t=e||this.SCAN_UNIVERSE;console.log(`🔍 Starting pre-market gap scan on ${t.length} symbols...`);try{let e=(await this.fmpAPI.getMultiplePreMarketQuotes(t)).map(e=>this.processSingleStock(e)),a=(await Promise.all(e)).filter(e=>null!==e).sort((e,t)=>t.gapPercent-e.gapPercent);return console.log(`✅ Gap scan complete. Found ${a.length} results.`),a}catch(e){return console.error("Error running gap scan:",e),[]}}async processSingleStock(e){try{let t=e.symbol,a=e.preMarketPrice||e.price,r=e.previousClose;if(!a||!r||r<=0)return null;let i=(a-r)/r*100;if(i<3)return null;let[s,n]=await Promise.all([this.fmpAPI.getCompanyProfile(t),this.catalystEngine.detectCatalysts(t)]);if(!s)return null;let l=e.volume||0,c=e.avgVolume||1,o=l*a,u=e.marketCap||s.mktCap||0,d={priceAbove1Dollar:a>1,gapAbove3Percent:i>=3,marketCapAbove800M:u>=8e8,preMarketVolumeAbove20K:l>=2e4,preMarketDollarVolumeAbove1M:o>=1e6,excludesPennyStocks:a>1&&u>=8e8,hasCatalyst:n.length>0},p=Object.values(d).every(e=>e),g=n.length>0?n.reduce((e,t)=>t.qualityScore>e.qualityScore?t:e):void 0;return{symbol:t,name:s.companyName||t,sector:s.sector||"Unknown",price:a,previousClose:r,gapPercent:i,preMarketHigh:a,preMarketLow:.98*a,preMarketVolume:l,preMarketDollarVolume:o,marketCap:u,averageDailyVolume:c,catalyst:g,scanTime:new Date().toISOString(),meetsAllCriteria:p,criteriaChecks:d}}catch(t){return console.error(`Error processing ${e.symbol}:`,t),null}}async getPerfectPickCandidates(e){return(await this.runGapScan(e)).filter(e=>e.meetsAllCriteria&&e.catalyst&&"tier_1"===e.catalyst.tier)}async getGapRangeResults(e=3,t=15,a){return(await this.runGapScan(a)).filter(a=>a.gapPercent>=e&&a.gapPercent<=t)}async getCatalystTypeResults(e,t){return(await this.runGapScan(t)).filter(t=>t.catalyst&&e.includes(t.catalyst.type))}getScheduledScanTimes(){let e=new Date,t=new Date(e.getFullYear(),e.getMonth(),e.getDate()),a=[new Date(t.getTime()+144e5),new Date(t.getTime()+216e5),new Date(t.getTime()+288e5),new Date(t.getTime()+324e5)],r=this.isDaylightSavingTime(e)?4:5;return a.map(e=>new Date(e.getTime()+60*r*6e4))}isDaylightSavingTime(e){let t=e.getFullYear(),a=new Date(t,2,1);a.setDate(a.getDate()+(7-a.getDay())+7);let r=new Date(t,10,1);return r.setDate(r.getDate()+(7-r.getDay())),e>=a&&e<r}async updateScanResults(e){let t=e.map(e=>e.symbol),a=await this.fmpAPI.getMultiplePreMarketQuotes(t),r=[];for(let t of a){let a=e.find(e=>e.symbol===t.symbol);if(!a)continue;let i=t.preMarketPrice||t.price,s=(i-t.previousClose)/t.previousClose*100,n={...a,price:i,gapPercent:s,preMarketVolume:t.volume||0,preMarketDollarVolume:(t.volume||0)*i,scanTime:new Date().toISOString()};n.criteriaChecks.gapAbove3Percent=s>=3,n.criteriaChecks.preMarketVolumeAbove20K=n.preMarketVolume>=2e4,n.criteriaChecks.preMarketDollarVolumeAbove1M=n.preMarketDollarVolume>=1e6,n.meetsAllCriteria=Object.values(n.criteriaChecks).every(e=>e),r.push(n)}return r.sort((e,t)=>t.gapPercent-e.gapPercent)}getScanSummary(e){let t=this.SCAN_UNIVERSE.length,a=e.length,r=e.filter(e=>e.meetsAllCriteria).length,i=e.filter(e=>e.catalyst).length,s=e.length>0?e.reduce((e,t)=>e+t.gapPercent,0)/e.length:0;return{totalScanned:t,gapsFound:a,perfectPicks:r,withCatalysts:i,avgGap:Math.round(100*s)/100,sectorBreakdown:e.reduce((e,t)=>(e[t.sector]=(e[t.sector]||0)+1,e),{}),catalystTypeBreakdown:e.filter(e=>e.catalyst).reduce((e,t)=>{let a=t.catalyst.type;return e[a]=(e[a]||0)+1,e},{}),scanTime:new Date().toISOString()}}}}];

//# sourceMappingURL=swing-trader-ai_src_lib_38622aa6._.js.map