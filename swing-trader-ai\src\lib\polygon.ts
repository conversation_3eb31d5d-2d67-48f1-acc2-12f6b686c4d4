import axios from 'axios'
import { CandlestickData, StockData } from '@/types/trading'

const POLYGON_BASE_URL = 'https://api.polygon.io'
const API_KEY = process.env.POLYGON_API_KEY

export class PolygonAPI {
  private apiKey: string

  constructor(apiKey?: string) {
    this.apiKey = apiKey || API_KEY || ''
    if (!this.apiKey) {
      throw new Error('Polygon API key is required')
    }
  }

  // Get current stock quote using Polygon's snapshot endpoint (better for paid plans)
  async getStockQuote(symbol: string): Promise<StockData> {
    try {
      // Use snapshot endpoint for real-time data (available on paid plans)
      const response = await axios.get(
        `${POLYGON_BASE_URL}/v2/snapshot/locale/us/markets/stocks/tickers/${symbol}`,
        {
          params: {
            apikey: this.apiKey
          }
        }
      )

      if (!response.data || !response.data.ticker) {
        throw new Error(`No data found for ${symbol}`)
      }

      const data = response.data.ticker

      // Extract data from Polygon snapshot response structure
      const dayData = data.day || {}
      const prevDayData = data.prevDay || {}
      const minData = data.min || {}

      // Use the most recent price available
      const currentPrice = dayData.c || minData.c || prevDayData.c
      const prevClose = prevDayData.c
      const change = data.todaysChange || (currentPrice - prevClose)
      const changePercent = data.todaysChangePerc || ((change / prevClose) * 100)

      return {
        symbol: symbol.toUpperCase(),
        name: data.name || symbol.toUpperCase(),
        price: currentPrice || 0,
        change: change || 0,
        changePercent: changePercent || 0,
        volume: dayData.v || minData.v || 1000000, // Default to 1M volume if missing
        marketCap: data.market_cap || this.estimateMarketCap(symbol, currentPrice || 0),
        pe: undefined,
        dividend: undefined
      }
    } catch (error) {
      console.error('Error fetching stock quote from Polygon:', error)

      // Fallback to previous day data if snapshot fails
      try {
        const fallbackResponse = await axios.get(
          `${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/prev`,
          {
            params: {
              adjusted: 'true',
              apikey: this.apiKey
            }
          }
        )

        const data = fallbackResponse.data.results[0]
        return {
          symbol: symbol.toUpperCase(),
          name: symbol.toUpperCase(),
          price: data.c || 0,
          change: (data.c - data.o) || 0,
          changePercent: data.o ? ((data.c - data.o) / data.o) * 100 : 0,
          volume: data.v || 1000000,
          marketCap: this.estimateMarketCap(symbol, data.c || 0),
          pe: undefined,
          dividend: undefined
        }
      } catch (fallbackError) {
        console.error('Polygon fallback also failed:', fallbackError)
        throw new Error(`Failed to fetch quote for ${symbol}`)
      }
    }
  }

  /**
   * Estimate market cap based on symbol and price
   * This is a fallback when Polygon doesn't provide market cap data
   */
  private estimateMarketCap(symbol: string, price: number): number {
    // Import stock universe data for better estimates
    const stockEstimates: { [key: string]: number } = {
      // Large cap (>$200B)
      'AAPL': 3000000000000, 'MSFT': 2800000000000, 'NVDA': 1800000000000,
      'GOOGL': 1700000000000, 'GOOG': 1700000000000, 'AMZN': 1500000000000,
      'TSLA': 800000000000, 'META': 800000000000, 'BRK.B': 900000000000,

      // Mid-large cap ($50B-$200B)
      'JPM': 500000000000, 'V': 500000000000, 'UNH': 500000000000,
      'JNJ': 450000000000, 'XOM': 450000000000, 'WMT': 600000000000,
      'PG': 400000000000, 'MA': 400000000000, 'HD': 350000000000,
      'CVX': 300000000000, 'ABBV': 300000000000, 'BAC': 300000000000,
      'COST': 350000000000, 'AVGO': 600000000000, 'TSM': 500000000000,

      // Mid cap ($10B-$50B)
      'NFLX': 200000000000, 'ORCL': 350000000000, 'CRM': 250000000000,
      'ADBE': 220000000000, 'AMD': 220000000000, 'INTC': 200000000000,
      'QCOM': 180000000000, 'TMO': 200000000000, 'DHR': 180000000000,
      'CAT': 180000000000, 'GE': 180000000000, 'DIS': 180000000000,
      'VZ': 170000000000, 'PFE': 160000000000, 'NKE': 150000000000,
      'MS': 150000000000, 'UBER': 150000000000, 'C': 120000000000,
      'GS': 120000000000, 'T': 120000000000, 'AMGN': 150000000000,
      'HON': 140000000000, 'LOW': 150000000000, 'BMY': 120000000000,
      'CMCSA': 150000000000, 'SBUX': 110000000000, 'MMM': 60000000000,

      // Smaller cap but popular swing trading stocks
      'PLTR': 60000000000, 'SHOP': 80000000000, 'GILD': 80000000000,
      'TGT': 70000000000, 'COP': 150000000000, 'EOG': 70000000000,
      'SLB': 60000000000, 'PYPL': 70000000000, 'SQ': 40000000000,
      'COIN': 50000000000, 'DASH': 50000000000, 'MRNA': 30000000000,
      'SNOW': 50000000000, 'ROKU': 5000000000, 'HOOD': 15000000000,
      'LYFT': 6000000000, 'SPG': 50000000000, 'PLD': 120000000000,
      'NEE': 150000000000
    }

    // Return estimated market cap if available, otherwise estimate based on price
    if (stockEstimates[symbol]) {
      return stockEstimates[symbol]
    }

    // Rough estimation based on price (very approximate)
    if (price > 500) return 100000000000 // Assume large cap if high price
    if (price > 100) return 50000000000  // Assume mid-large cap
    if (price > 50) return 20000000000   // Assume mid cap
    if (price > 10) return 5000000000    // Assume small-mid cap
    return 1000000000 // Default to $1B minimum for scanning
  }

  // Get historical candlestick data (optimized for paid plans)
  async getHistoricalData(
    symbol: string,
    timespan: 'minute' | 'hour' | 'day' | 'week' | 'month' = 'day',
    multiplier: number = 1,
    from: string,
    to: string
  ): Promise<CandlestickData[]> {
    try {
      const response = await axios.get(
        `${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/range/${multiplier}/${timespan}/${from}/${to}`,
        {
          params: {
            adjusted: 'true',
            sort: 'asc',
            limit: 50000, // Higher limit for paid plans
            apikey: this.apiKey
          }
        }
      )

      if (!response.data.results || response.data.results.length === 0) {
        console.warn(`No historical data found for ${symbol}`)
        return []
      }

      return response.data.results.map((candle: any) => ({
        timestamp: candle.t,
        open: candle.o,
        high: candle.h,
        low: candle.l,
        close: candle.c,
        volume: candle.v
      }))
    } catch (error) {
      console.error(`Error fetching historical data for ${symbol}:`, error)

      // Log the specific error for debugging
      if (error.response) {
        console.error(`Polygon API Error: ${error.response.status} - ${error.response.statusText}`)
        console.error('Response data:', error.response.data)
      }

      throw new Error(`Failed to fetch historical data for ${symbol}: ${error.message}`)
    }
  }

  // Get company details
  async getCompanyDetails(symbol: string) {
    try {
      const response = await axios.get(
        `${POLYGON_BASE_URL}/v3/reference/tickers/${symbol}`,
        {
          params: {
            apikey: this.apiKey
          }
        }
      )

      return response.data.results
    } catch (error) {
      console.error('Error fetching company details:', error)
      return null
    }
  }

  // Get market status
  async getMarketStatus() {
    try {
      const response = await axios.get(
        `${POLYGON_BASE_URL}/v1/marketstatus/now`,
        {
          params: {
            apikey: this.apiKey
          }
        }
      )

      return response.data
    } catch (error) {
      console.error('Error fetching market status:', error)
      return null
    }
  }

  // Search for stocks
  async searchStocks(query: string, limit: number = 10) {
    try {
      const response = await axios.get(
        `${POLYGON_BASE_URL}/v3/reference/tickers`,
        {
          params: {
            search: query,
            market: 'stocks',
            active: 'true',
            limit,
            apikey: this.apiKey
          }
        }
      )

      return response.data.results || []
    } catch (error) {
      console.error('Error searching stocks:', error)
      return []
    }
  }
}

// Create a singleton instance
export const polygonAPI = new PolygonAPI()
