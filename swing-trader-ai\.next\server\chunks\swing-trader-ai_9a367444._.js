module.exports=[96341,e=>{"use strict";e.s(["SwingTradingStrategies",()=>i]);var t=e.i(58445);class i{static DEFAULT_CRITERIA={minPrice:5,minVolume:5e5,minMarketCap:8e8,minATRPercent:2,above200SMA:!0,maxDistanceFrom8EMA:2,minRoomToResistance:1,scanTimeStart:"12:00",scanTimeEnd:"16:00",maxRiskPerTrade:1,maxConcurrentPositions:3};static analyzeOvernightMomentum(e,i,n,a=1e5){if(i.length<50)return null;let r=i.map(e=>e.close),o=i.map(e=>e.high),l=i.map(e=>e.low);i.map(e=>e.volume);let s=n.price,c=n.volume,m=n.changePercent,u=t.TechnicalIndicators.sma(r,Math.min(50,r.length-1)),h=t.TechnicalIndicators.ema(r,Math.min(8,r.length-1)),g=this.calculateATR(i,Math.min(14,i.length-1)),d=u[u.length-1],p=h[h.length-1],f=g[g.length-1];if(!this.passesBasicFilters(n,c,d,s)||m<2||Math.abs(s-p)/f>this.DEFAULT_CRITERIA.maxDistanceFrom8EMA)return null;let k=Math.max(this.calculateVWAP(i.slice(-1)[0]),.98*s),T=o[o.length-1],y=l[l.length-1],v=(s-y)/(T-y);if(v<.5)return null;let M=this.calculateRoomToResistance(i,s,f);if(M<this.DEFAULT_CRITERIA.minRoomToResistance)return null;let A=.0075*a,E=Math.floor(A/(s-k)),x=[1.03*s,1.05*s,1.08*s];return{strategy:"overnight_momentum",confidence:this.calculateOvernightConfidence(m,v,c,M),entryPrice:s,stopLoss:k,targets:x,positionSize:E,riskAmount:A,holdingPeriod:"overnight",keyLevel:k,invalidation:`Daily close below ${k.toFixed(2)} or gap down below level`,notes:["Enter final 30-60 min before close","Exit pre-market on strength or first 45min","Hard stop if gaps below defended level","Scale out aggressively if gaps >1 ATR up"],preciseEntry:{price:.999*s,orderType:"limit",timing:"Final 30-60 minutes before market close",conditions:[`Stock holding above ${k.toFixed(2)} (defended level)`,`Volume above ${(.8*c).toLocaleString()} shares`,`Price above ${p.toFixed(2)} (8-EMA)`,"No late-day selling pressure"],urgency:"wait_for_pullback"},preciseExit:{stopLoss:{price:.995*k,orderType:"stop",reason:"Defended level broken - invalidates setup",triggerConditions:["Any close below defended level","Gap down below key level","Heavy selling into close"]},takeProfits:[{price:x[0],percentage:33,target:"T1 - Pre-market (3%)",reasoning:"Take profits on pre-market strength",orderType:"limit"},{price:x[1],percentage:33,target:"T2 - Opening hour (5%)",reasoning:"Scale out on opening momentum",orderType:"limit"},{price:x[2],percentage:34,target:"T3 - Extended (8%)",reasoning:"Final exit on extended move",orderType:"limit"}]},riskManagement:{maxRiskDollars:A,accountRiskPercent:.75,sharesForRisk:E,invalidationPrice:k,timeStopHours:18,maxDrawdownPercent:2},executionPlan:{entryInstructions:["1. Wait for final 30-60 minutes before close","2. Confirm stock is holding defended level","3. Place limit order slightly below current price","4. Cancel if not filled by close"],exitInstructions:["1. Set stop-loss immediately after fill","2. Monitor pre-market for gap up","3. Scale out 1/3 at each target level","4. Exit all by 10:15 AM if no momentum"],monitoringPoints:["Pre-market price action and volume","Opening gap and first 15-minute candle","Key level defense throughout session","Overall market sentiment"],contingencyPlans:["If gaps down: Exit immediately at market open","If gaps up >2%: Scale out more aggressively","If sideways: Exit by 10:15 AM","If market weakness: Tighten stops"]}}}static analyzeTechnicalBreakout(e,i,n,a=1e5){if(i.length<50)return null;let r=i.map(e=>e.close),o=i.map(e=>e.volume),l=n.price,s=t.TechnicalIndicators.sma(r,Math.min(50,r.length-1)),c=t.TechnicalIndicators.ema(r,Math.min(8,r.length-1)),m=this.calculateATR(i,Math.min(14,i.length-1)),u=s[s.length-1],h=c[c.length-1],g=m[m.length-1];if(!this.passesBasicFilters(n,n.volume,u,l)||l<=u)return null;let d=Math.abs(l-h)/l*100;if(d>3||!this.checkEMAReclaim(r,c,5))return null;let p=t.TechnicalIndicators.sma(o,20),f=p[p.length-1],k=n.volume/f;if(k<1.2)return null;let T=this.calculateRoomToResistance(i,l,g);if(T<1.5)return null;let y=.01*a,v=Math.floor(y/(l-h)),M=[1.05*l,1.1*l,1.15*l];return{strategy:"technical_breakout",confidence:this.calculateBreakoutConfidence(d,k,T,n.changePercent),entryPrice:l,stopLoss:h,targets:M,positionSize:v,riskAmount:y,holdingPeriod:"days_to_weeks",keyLevel:h,invalidation:`Daily close below 8-EMA (${h.toFixed(2)})`,notes:["Enter on afternoon reclaim of 8-EMA","Add only on higher-low pullbacks to 8-EMA","Scale partials at resistance levels","Exit on daily close below 8-EMA"],preciseEntry:{price:1.002*h,orderType:"limit",timing:"Afternoon reclaim or first pullback to 8-EMA",conditions:[`Price reclaiming ${h.toFixed(2)} (8-EMA) with volume`,`Above ${u.toFixed(2)} (50-day SMA)`,`Volume expansion above ${(1.2*n.volume).toLocaleString()}`,"No major resistance overhead"],urgency:"breakout_confirmation"},preciseExit:{stopLoss:{price:.998*h,orderType:"stop",reason:"8-EMA breakdown invalidates trend-follow setup",triggerConditions:["Daily close below 8-EMA","Intraday break with volume","Loss of 50-SMA support"]},takeProfits:[{price:M[0],percentage:25,target:"R1 - First resistance (5%)",reasoning:"Take partial profits at first resistance",orderType:"limit"},{price:M[1],percentage:35,target:"R2 - Major resistance (10%)",reasoning:"Scale out at major resistance level",orderType:"limit"},{price:M[2],percentage:40,target:"R3 - Extension (15%)",reasoning:"Final exit on extended breakout",orderType:"limit"}]},riskManagement:{maxRiskDollars:y,accountRiskPercent:1,sharesForRisk:v,invalidationPrice:h,timeStopHours:72,maxDrawdownPercent:3},executionPlan:{entryInstructions:["1. Wait for afternoon reclaim of 8-EMA","2. Confirm volume expansion on breakout","3. Place limit order above 8-EMA","4. Only enter on higher-low pullbacks"],exitInstructions:["1. Set stop-loss below 8-EMA immediately","2. Scale out 25% at first resistance","3. Trail stop to breakeven after R1","4. Exit remaining on 8-EMA breakdown"],monitoringPoints:["8-EMA as dynamic support/resistance","Volume confirmation on moves","Overall market trend alignment","Sector strength/weakness"],contingencyPlans:["If fails at resistance: Tighten stops","If market weakness: Exit early","If sector rotation: Consider exit","If extended: Take more profits"]}}}static passesBasicFilters(e,t,i,n){return n>=this.DEFAULT_CRITERIA.minPrice&&t>=this.DEFAULT_CRITERIA.minVolume&&(e.marketCap||0)>=this.DEFAULT_CRITERIA.minMarketCap&&n>i}static calculateATR(e,i){let n=[];for(let t=1;t<e.length;t++){let i=e[t].high,a=e[t].low,r=e[t-1].close,o=Math.max(i-a,Math.abs(i-r),Math.abs(a-r));n.push(o)}return t.TechnicalIndicators.sma(n,i)}static calculateVWAP(e){return(e.high+e.low+e.close)/3}static calculateRoomToResistance(e,t,i){return(Math.max(...e.slice(-20).map(e=>e.high))-t)/i}static checkEMAReclaim(e,t,i){for(let n=Math.max(0,e.length-i);n<e.length-1;n++)if(e[n]<t[n]&&e[n+1]>t[n+1])return!0;return!1}static calculateOvernightConfidence(e,t,i,n){let a=50;return e>5?a+=15:e>3?a+=10:e>2&&(a+=5),t>.8?a+=15:t>.6?a+=10:t>.5&&(a+=5),i>2e6?a+=10:i>1e6&&(a+=5),n>2?a+=10:n>1.5&&(a+=5),Math.min(95,Math.max(30,a))}static calculateBreakoutConfidence(e,t,i,n){let a=60;return e<1?a+=15:e<2?a+=10:e<3&&(a+=5),t>2?a+=15:t>1.5?a+=10:t>1.2&&(a+=5),i>3?a+=15:i>2?a+=10:i>1.5&&(a+=5),n>2&&(a+=5),Math.min(95,Math.max(40,a))}}},64433,(e,t,i)=>{}];

//# sourceMappingURL=swing-trader-ai_9a367444._.js.map