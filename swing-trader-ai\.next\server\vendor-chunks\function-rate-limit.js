/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/function-rate-limit";
exports.ids = ["vendor-chunks/function-rate-limit"];
exports.modules = {

/***/ "(rsc)/./node_modules/function-rate-limit/index.js":
/*!***************************************************!*\
  !*** ./node_modules/function-rate-limit/index.js ***!
  \***************************************************/
/***/ ((module) => {

eval("module.exports = rateLimit;\n\nfunction rateLimit(limitCount, limitInterval, fn) {\n  var fifo = [];\n\n  // count starts at limit\n  // each call of `fn` decrements the count\n  // it is incremented after limitInterval\n  var count = limitCount;\n\n  function call_next(args) {\n    setTimeout(function() {\n      if (fifo.length > 0) {\n        call_next();\n      }\n      else {\n        count = count + 1;\n      }\n    }, limitInterval);\n\n    var call_args = fifo.shift();\n\n    // if there is no next item in the queue\n    // and we were called with args, trigger function immediately\n    if (!call_args && args) {\n      fn.apply(args[0], args[1]);\n      return;\n    }\n\n    fn.apply(call_args[0], call_args[1]);\n  }\n\n  return function rate_limited_function() {\n    var ctx = this;\n    var args = Array.prototype.slice.call(arguments);\n    if (count <= 0) {\n      fifo.push([ctx, args]);\n      return;\n    }\n\n    count = count - 1;\n    call_next([ctx, args]);\n  };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZnVuY3Rpb24tcmF0ZS1saW1pdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLOztBQUVMOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFNKRml0XFxEZXNrdG9wXFxzaGl0dHlpZGVhXFxzd2luZy10cmFkZXItYWlcXG5vZGVfbW9kdWxlc1xcZnVuY3Rpb24tcmF0ZS1saW1pdFxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByYXRlTGltaXQ7XG5cbmZ1bmN0aW9uIHJhdGVMaW1pdChsaW1pdENvdW50LCBsaW1pdEludGVydmFsLCBmbikge1xuICB2YXIgZmlmbyA9IFtdO1xuXG4gIC8vIGNvdW50IHN0YXJ0cyBhdCBsaW1pdFxuICAvLyBlYWNoIGNhbGwgb2YgYGZuYCBkZWNyZW1lbnRzIHRoZSBjb3VudFxuICAvLyBpdCBpcyBpbmNyZW1lbnRlZCBhZnRlciBsaW1pdEludGVydmFsXG4gIHZhciBjb3VudCA9IGxpbWl0Q291bnQ7XG5cbiAgZnVuY3Rpb24gY2FsbF9uZXh0KGFyZ3MpIHtcbiAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uKCkge1xuICAgICAgaWYgKGZpZm8ubGVuZ3RoID4gMCkge1xuICAgICAgICBjYWxsX25leHQoKTtcbiAgICAgIH1cbiAgICAgIGVsc2Uge1xuICAgICAgICBjb3VudCA9IGNvdW50ICsgMTtcbiAgICAgIH1cbiAgICB9LCBsaW1pdEludGVydmFsKTtcblxuICAgIHZhciBjYWxsX2FyZ3MgPSBmaWZvLnNoaWZ0KCk7XG5cbiAgICAvLyBpZiB0aGVyZSBpcyBubyBuZXh0IGl0ZW0gaW4gdGhlIHF1ZXVlXG4gICAgLy8gYW5kIHdlIHdlcmUgY2FsbGVkIHdpdGggYXJncywgdHJpZ2dlciBmdW5jdGlvbiBpbW1lZGlhdGVseVxuICAgIGlmICghY2FsbF9hcmdzICYmIGFyZ3MpIHtcbiAgICAgIGZuLmFwcGx5KGFyZ3NbMF0sIGFyZ3NbMV0pO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGZuLmFwcGx5KGNhbGxfYXJnc1swXSwgY2FsbF9hcmdzWzFdKTtcbiAgfVxuXG4gIHJldHVybiBmdW5jdGlvbiByYXRlX2xpbWl0ZWRfZnVuY3Rpb24oKSB7XG4gICAgdmFyIGN0eCA9IHRoaXM7XG4gICAgdmFyIGFyZ3MgPSBBcnJheS5wcm90b3R5cGUuc2xpY2UuY2FsbChhcmd1bWVudHMpO1xuICAgIGlmIChjb3VudCA8PSAwKSB7XG4gICAgICBmaWZvLnB1c2goW2N0eCwgYXJnc10pO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGNvdW50ID0gY291bnQgLSAxO1xuICAgIGNhbGxfbmV4dChbY3R4LCBhcmdzXSk7XG4gIH07XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/function-rate-limit/index.js\n");

/***/ })

};
;