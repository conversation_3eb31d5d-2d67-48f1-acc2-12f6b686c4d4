{"version": 3, "sources": ["turbopack:///[project]/swing-trader-ai/src/app/page.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/swing-trader-ai/src/app/page.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/swing-trader-ai/src/app/page.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "6OAEe,CAAA,EAAA,AADf,EAAA,CAAA,CAAA,KACe,uBAAA,AAAuB,EAClC,WAAa,MAAM,AAAI,MAAM,kSAAoS,EACjU,iEACA,gEAHW,CAAA,EADf,AACe,EADf,CAAA,CAAA,KACe,uBAAA,AAAuB,EAClC,WAAa,MAAM,AAAI,MAAM,8QAAgR,EAC7S,6CACA", "ignoreList": [0]}