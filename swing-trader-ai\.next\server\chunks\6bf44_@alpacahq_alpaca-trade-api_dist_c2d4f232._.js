module.exports = [
"[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/api.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

const axios = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/node_modules/axios/index.js [app-route] (ecmascript)");
const joinUrl = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/urljoin/index.js [app-route] (ecmascript)");
function httpRequest(endpoint, queryParams, body, method) {
    const { baseUrl, keyId, secretKey, apiVersion, oauth } = this.configuration;
    const req = {
        method: method || "GET",
        url: joinUrl(baseUrl, apiVersion, endpoint),
        params: queryParams || {},
        headers: oauth !== "" ? {
            "content-type": method !== "DELETE" ? "application/json" : "",
            Authorization: "Bearer " + oauth
        } : {
            "content-type": method !== "DELETE" ? "application/json" : "",
            "APCA-API-KEY-ID": keyId,
            "APCA-API-SECRET-KEY": secretKey
        },
        data: body || undefined
    };
    return axios(req);
}
function dataHttpRequest(endpoint, queryParams, body, method) {
    const { dataBaseUrl, keyId, secretKey, oauth } = this.configuration;
    const req = {
        method: method || "GET",
        url: joinUrl(dataBaseUrl, "v1", endpoint),
        params: queryParams || {},
        headers: oauth !== "" ? {
            "content-type": method !== "DELETE" ? "application/json" : "",
            Authorization: "Bearer " + oauth
        } : {
            "content-type": method !== "DELETE" ? "application/json" : "",
            "APCA-API-KEY-ID": keyId,
            "APCA-API-SECRET-KEY": secretKey
        },
        data: body || undefined
    };
    return axios(req);
}
function sendRequest(f, endpoint, queryParams, body, method) {
    return f(endpoint, queryParams, body, method).then((resp)=>resp.data);
}
module.exports = {
    httpRequest,
    dataHttpRequest,
    sendRequest
};
}),
"[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/account.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

const { omitBy, isNil } = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/lodash/lodash.js [app-route] (ecmascript)");
function get() {
    return this.sendRequest("/account");
}
function updateConfigs(configs) {
    return this.sendRequest("/account/configurations", null, configs, "PATCH");
}
function getConfigs() {
    return this.sendRequest("/account/configurations");
}
function getActivities({ activityTypes, until, after, direction, date, pageSize, pageToken }) {
    if (Array.isArray(activityTypes)) {
        activityTypes = activityTypes.join(",");
    }
    const queryParams = omitBy({
        activity_types: activityTypes,
        until,
        after,
        direction,
        date,
        page_size: pageSize,
        page_token: pageToken
    }, isNil);
    return this.sendRequest("/account/activities", queryParams);
}
function getPortfolioHistory({ date_start, date_end, period, timeframe, extended_hours }) {
    const queryParams = omitBy({
        date_start,
        date_end,
        period,
        timeframe,
        extended_hours
    }, isNil);
    return this.sendRequest("/account/portfolio/history", queryParams);
}
module.exports = {
    get,
    getConfigs,
    updateConfigs,
    getActivities,
    getPortfolioHistory
};
}),
"[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/position.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

function getAll() {
    return this.sendRequest("/positions");
}
function getOne(symbol) {
    return this.sendRequest("/positions/" + symbol);
}
function closeAll() {
    return this.sendRequest("/positions", null, null, "DELETE");
}
function closeOne(symbol) {
    return this.sendRequest("/positions/" + symbol, null, null, "DELETE");
}
module.exports = {
    getAll,
    getOne,
    closeAll,
    closeOne
};
}),
"[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/utils/dateformat.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

// certain endpoints don't accept ISO dates,
// so to allow the user to use regular JS date objects
// with the api, we need to convert them to strings
function toDateString(date) {
    if (date == null || typeof date === 'string') return date;
    const year = date.getUTCFullYear();
    const month = numPad(date.getUTCMonth() + 1);
    const day = numPad(date.getUTCDate());
    return `${year}-${month}-${day}`;
}
function numPad(num) {
    if (num < 10) {
        return '0' + num;
    }
    return num;
}
module.exports = {
    toDateString
};
}),
"[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/calendar.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

const { omitBy, isNil } = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/lodash/lodash.js [app-route] (ecmascript)");
const { toDateString } = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/utils/dateformat.js [app-route] (ecmascript)");
function get({ start, end } = {}) {
    const queryParams = omitBy({
        start: toDateString(start),
        end: toDateString(end)
    }, isNil);
    return this.sendRequest("/calendar", queryParams);
}
module.exports = {
    get
};
}),
"[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/clock.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

function get() {
    return this.sendRequest("/clock");
}
module.exports = {
    get
};
}),
"[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/asset.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

function getAll(options = {}) {
    return this.sendRequest("/assets", options);
}
function getOne(symbol) {
    return this.sendRequest("/assets/" + symbol);
}
module.exports = {
    getAll,
    getOne
};
}),
"[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/order.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

const { omitBy, isNil } = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/lodash/lodash.js [app-route] (ecmascript)");
function getAll({ status, until, after, limit, direction, nested, symbols } = {}) {
    const queryParams = omitBy({
        status,
        until,
        after,
        limit,
        direction,
        nested,
        symbols
    }, isNil);
    return this.sendRequest("/orders", queryParams);
}
function getOne(id) {
    return this.sendRequest("/orders/" + id);
}
function getByClientOrderId(clientOrderId) {
    const queryParams = {
        client_order_id: clientOrderId
    };
    return this.sendRequest("/orders:by_client_order_id", queryParams);
}
function post(order) {
    return this.sendRequest("/orders", null, order, "POST");
}
function cancel(id) {
    return this.sendRequest("/orders/" + id, null, null, "DELETE");
}
function cancelAll() {
    return this.sendRequest("/orders", null, null, "DELETE");
}
function patchOrder(id, newOrder) {
    return this.sendRequest(`/orders/${id}`, null, newOrder, "PATCH");
}
module.exports = {
    getAll,
    getOne,
    getByClientOrderId,
    post,
    patchOrder,
    cancel,
    cancelAll
};
}),
"[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/watchlist.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

// todo: try this
// const { omitBy, isNil } = require('lodash')
function getAll() {
    return this.sendRequest("/watchlists");
}
function getOne(id) {
    return this.sendRequest(`/watchlists/${id}`);
}
function addWatchlist(name, symbols = undefined) {
    const body = {
        name: name,
        symbols: symbols
    };
    return this.sendRequest("/watchlists", null, body, "POST");
}
function addToWatchlist(id, symbol) {
    const body = {
        symbol: symbol
    };
    return this.sendRequest(`/watchlists/${id}`, null, body, "POST");
}
function updateWatchlist(id, newWatchList) {
    return this.sendRequest(`/watchlists/${id}`, null, newWatchList, "PUT");
}
function deleteWatchlist(id) {
    return this.sendRequest(`/watchlists/${id}`, null, null, "DELETE");
}
function deleteFromWatchlist(id, symbol) {
    return this.sendRequest(`/watchlists/${id}/${symbol}`, null, null, "DELETE");
}
module.exports = {
    getAll,
    getOne,
    addWatchlist,
    addToWatchlist,
    updateWatchlist,
    deleteWatchlist,
    deleteFromWatchlist
};
}),
"[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/datav2/entityv2.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

var __importDefault = /*TURBOPACK member replacement*/ __turbopack_context__.e && /*TURBOPACK member replacement*/ __turbopack_context__.e.__importDefault || function(mod) {
    return mod && mod.__esModule ? mod : {
        "default": mod
    };
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.mergeCorporateActions = exports.getCorporateActionsSize = exports.convertCorporateActions = exports.NewTimeframe = exports.TimeFrameUnit = exports.AlpacaNews = exports.AlpacaOptionSnapshotV1Beta1 = exports.AlpacaOptionQuoteV1Beta1 = exports.AlpacaOptionTradeV1Beta1 = exports.AlpacaOptionBarV1Beta1 = exports.AlpacaCryptoOrderbook = exports.AlpacaCryptoSnapshot = exports.AlpacaCryptoBar = exports.AlpacaCryptoQuote = exports.AlpacaCryptoTrade = exports.AlpacaCorrectionV2 = exports.AlpacaCancelErrorV2 = exports.AlpacaLuldV2 = exports.AlpacaStatusV2 = exports.AlpacaSnapshotV2 = exports.AlpacaBarV2 = exports.AlpacaQuoteV2 = exports.AlpacaTradeV2 = void 0;
const mapKeys_1 = __importDefault(__turbopack_context__.r("[project]/swing-trader-ai/node_modules/lodash/mapKeys.js [app-route] (ecmascript)"));
const mapValues_1 = __importDefault(__turbopack_context__.r("[project]/swing-trader-ai/node_modules/lodash/mapValues.js [app-route] (ecmascript)"));
const trade_mapping_v2 = {
    S: "Symbol",
    i: "ID",
    x: "Exchange",
    p: "Price",
    s: "Size",
    t: "Timestamp",
    c: "Conditions",
    z: "Tape"
};
const quote_mapping_v2 = {
    S: "Symbol",
    bx: "BidExchange",
    bp: "BidPrice",
    bs: "BidSize",
    ax: "AskExchange",
    ap: "AskPrice",
    as: "AskSize",
    t: "Timestamp",
    c: "Conditions",
    z: "Tape"
};
const bar_mapping_v2 = {
    S: "Symbol",
    o: "OpenPrice",
    h: "HighPrice",
    l: "LowPrice",
    c: "ClosePrice",
    v: "Volume",
    t: "Timestamp",
    vw: "VWAP",
    n: "TradeCount"
};
const snapshot_mapping_v2 = {
    symbol: "symbol",
    latestTrade: "LatestTrade",
    latestQuote: "LatestQuote",
    minuteBar: "MinuteBar",
    dailyBar: "DailyBar",
    prevDailyBar: "PrevDailyBar"
};
const status_mapping_v2 = {
    S: "Symbol",
    sc: "StatusCode",
    sm: "StatusMessage",
    rc: "ReasonCode",
    rm: "ReasonMessage",
    t: "Timestamp",
    z: "Tape"
};
const luld_mapping_v2 = {
    S: "Symbol",
    u: "LimitUpPrice",
    d: "LimitDownPrice",
    i: "Indicator",
    t: "Timestamp",
    z: "Tape"
};
const cancel_error_mapping_v2 = {
    S: "Symbol",
    i: "ID",
    x: "Exchange",
    p: "Price",
    s: "Size",
    a: "CancelErrorAction",
    z: "Tape",
    t: "Timestamp"
};
const correction_mapping_v2 = {
    S: "Symbol",
    x: "Exchange",
    oi: "OriginalID",
    op: "OriginalPrice",
    os: "OriginalSize",
    oc: "OriginalConditions",
    ci: "CorrectedID",
    cp: "CorrectedPrice",
    cs: "CorrectedSize",
    cc: "CorrectedConditions",
    z: "Tape",
    t: "Timestamp"
};
const crypto_trade_mapping = {
    S: "Symbol",
    t: "Timestamp",
    x: "Exchange",
    p: "Price",
    s: "Size",
    tks: "TakerSide",
    i: "ID"
};
const crypto_quote_mapping = {
    t: "Timestamp",
    bp: "BidPrice",
    bs: "BidSize",
    ap: "AskPrice",
    as: "AskSize"
};
const crypto_bar_mapping = {
    t: "Timestamp",
    o: "Open",
    h: "High",
    l: "Low",
    c: "Close",
    v: "Volume",
    vw: "VWAP",
    n: "TradeCount"
};
const crypto_snapshot_mapping = {
    latestTrade: "LatestTrade",
    latestQuote: "LatestQuote",
    minuteBar: "MinuteBar",
    dailyBar: "DailyBar",
    prevDailyBar: "PrevDailyBar"
};
const crypto_orderbook_entry_mapping = {
    p: "Price",
    s: "Size"
};
const crypto_orderbook_mapping = {
    t: "Timestamp",
    b: "Bids",
    a: "Asks"
};
const news_image_mapping = {
    size: "Size",
    url: "URL"
};
const news_mapping = {
    id: "ID",
    author: "Author",
    created_at: "CreatedAt",
    updated_at: "UpdatedAt",
    headline: "Headline",
    summary: "Summary",
    content: "Content",
    images: "Images",
    url: "URL",
    symbols: "Symbols",
    source: "Source"
};
const option_bar_mapping = {
    S: "Symbol",
    o: "Open",
    h: "High",
    l: "Low",
    c: "Close",
    v: "Volume",
    t: "Timestamp",
    vw: "VWAP",
    n: "TradeCount"
};
const option_tarde_mapping = {
    S: "Symbol",
    x: "Exchange",
    p: "Price",
    s: "Size",
    t: "Timestamp",
    c: "Condition"
};
const option_quote_mapping = {
    S: "Symbol",
    bx: "BidExchange",
    bp: "BidPrice",
    bs: "BidSize",
    ax: "AskExchange",
    ap: "AskPrice",
    as: "AskSize",
    t: "Timestamp",
    c: "Conditions",
    z: "Tape"
};
const option_snapshot_mapping = {
    symbol: "symbol",
    latestTrade: "LatestTrade",
    latestQuote: "LatestQuote",
    impliedVolatility: "ImpliedVolatility",
    greeks: "Greeks"
};
function AlpacaTradeV2(data) {
    return aliasObjectKey(data, trade_mapping_v2);
}
exports.AlpacaTradeV2 = AlpacaTradeV2;
function AlpacaQuoteV2(data) {
    return aliasObjectKey(data, quote_mapping_v2);
}
exports.AlpacaQuoteV2 = AlpacaQuoteV2;
function AlpacaBarV2(data) {
    return aliasObjectKey(data, bar_mapping_v2);
}
exports.AlpacaBarV2 = AlpacaBarV2;
function AlpacaSnapshotV2(data) {
    const snapshot = aliasObjectKey(data, snapshot_mapping_v2);
    return (0, mapValues_1.default)(snapshot, (value, key)=>{
        return convertSnapshotData(key, value, false);
    });
}
exports.AlpacaSnapshotV2 = AlpacaSnapshotV2;
function AlpacaStatusV2(data) {
    return aliasObjectKey(data, status_mapping_v2);
}
exports.AlpacaStatusV2 = AlpacaStatusV2;
function AlpacaLuldV2(data) {
    return aliasObjectKey(data, luld_mapping_v2);
}
exports.AlpacaLuldV2 = AlpacaLuldV2;
function AlpacaCancelErrorV2(data) {
    return aliasObjectKey(data, cancel_error_mapping_v2);
}
exports.AlpacaCancelErrorV2 = AlpacaCancelErrorV2;
function AlpacaCorrectionV2(data) {
    return aliasObjectKey(data, correction_mapping_v2);
}
exports.AlpacaCorrectionV2 = AlpacaCorrectionV2;
function AlpacaCryptoTrade(data) {
    return aliasObjectKey(data, crypto_trade_mapping);
}
exports.AlpacaCryptoTrade = AlpacaCryptoTrade;
function AlpacaCryptoQuote(data) {
    return aliasObjectKey(data, crypto_quote_mapping);
}
exports.AlpacaCryptoQuote = AlpacaCryptoQuote;
function AlpacaCryptoBar(data) {
    return aliasObjectKey(data, crypto_bar_mapping);
}
exports.AlpacaCryptoBar = AlpacaCryptoBar;
function AlpacaCryptoSnapshot(data) {
    const snapshot = aliasObjectKey(data, crypto_snapshot_mapping);
    return (0, mapValues_1.default)(snapshot, (value, key)=>{
        return convertSnapshotData(key, value, true);
    });
}
exports.AlpacaCryptoSnapshot = AlpacaCryptoSnapshot;
function AlpacaCryptoOrderbook(data) {
    const mapFn = (entries)=>entries.map((entry)=>aliasObjectKey(entry, crypto_orderbook_entry_mapping));
    const orderbook = aliasObjectKey(data, crypto_orderbook_mapping);
    return Object.assign(Object.assign({}, orderbook), {
        Bids: mapFn(orderbook.Bids),
        Asks: mapFn(orderbook.Asks)
    });
}
exports.AlpacaCryptoOrderbook = AlpacaCryptoOrderbook;
function AlpacaOptionBarV1Beta1(data) {
    return aliasObjectKey(data, option_bar_mapping);
}
exports.AlpacaOptionBarV1Beta1 = AlpacaOptionBarV1Beta1;
function AlpacaOptionTradeV1Beta1(data) {
    return aliasObjectKey(data, option_tarde_mapping);
}
exports.AlpacaOptionTradeV1Beta1 = AlpacaOptionTradeV1Beta1;
function AlpacaOptionQuoteV1Beta1(data) {
    return aliasObjectKey(data, option_quote_mapping);
}
exports.AlpacaOptionQuoteV1Beta1 = AlpacaOptionQuoteV1Beta1;
function AlpacaOptionSnapshotV1Beta1(data) {
    const snapshot = aliasObjectKey(data, option_snapshot_mapping);
    return (0, mapValues_1.default)(snapshot, (value, key)=>{
        return convertOptionSnapshotData(key, value);
    });
}
exports.AlpacaOptionSnapshotV1Beta1 = AlpacaOptionSnapshotV1Beta1;
function aliasObjectKey(data, mapping) {
    return (0, mapKeys_1.default)(data, (_value, key)=>{
        return Object.hasOwn(mapping, key) ? mapping[key] : key;
    });
}
function convertSnapshotData(key, data, isCrypto) {
    switch(key){
        case "LatestTrade":
            return isCrypto ? AlpacaCryptoTrade(data) : AlpacaTradeV2(data);
        case "LatestQuote":
            return isCrypto ? AlpacaCryptoQuote(data) : AlpacaQuoteV2(data);
        case "MinuteBar":
        case "DailyBar":
        case "PrevDailyBar":
            return isCrypto ? AlpacaCryptoBar(data) : AlpacaBarV2(data);
        default:
            return data;
    }
}
function convertOptionSnapshotData(key, data) {
    switch(key){
        case "LatestTrade":
            return AlpacaOptionTradeV1Beta1(data);
        case "LatestQuote":
            return AlpacaOptionQuoteV1Beta1(data);
        default:
            return data;
    }
}
function AlpacaNews(data) {
    const mappedNews = aliasObjectKey(data, news_mapping);
    if (mappedNews.Images) {
        mappedNews.Images.forEach((element)=>{
            return aliasObjectKey(element, news_image_mapping);
        });
    }
    return mappedNews;
}
exports.AlpacaNews = AlpacaNews;
var TimeFrameUnit;
(function(TimeFrameUnit) {
    TimeFrameUnit["MIN"] = "Min";
    TimeFrameUnit["HOUR"] = "Hour";
    TimeFrameUnit["DAY"] = "Day";
    TimeFrameUnit["WEEK"] = "Week";
    TimeFrameUnit["MONTH"] = "Month";
})(TimeFrameUnit || (exports.TimeFrameUnit = TimeFrameUnit = {}));
function NewTimeframe(amount, unit) {
    if (amount <= 0) {
        throw new Error("amount must be a positive integer value");
    }
    if (unit == TimeFrameUnit.MIN && amount > 59) {
        throw new Error("minute timeframe can only be used with amount between 1-59");
    }
    if (unit == TimeFrameUnit.HOUR && amount > 23) {
        throw new Error("hour timeframe can only be used with amounts 1-23");
    }
    if ((unit == TimeFrameUnit.DAY || unit == TimeFrameUnit.WEEK) && amount != 1) {
        throw new Error("day and week timeframes can only be used with amount 1");
    }
    if (unit == TimeFrameUnit.MONTH && ![
        1,
        2,
        3,
        6,
        12
    ].includes(amount)) {
        throw new Error("month timeframe can only be used with amount 1, 2, 3, 6 and 12");
    }
    return `${amount}${unit}`;
}
exports.NewTimeframe = NewTimeframe;
const cash_dividend_mapping = {
    ex_date: "ExDate",
    foreign: "Foreign",
    payable_date: "PayableDate",
    process_date: "ProcessDate",
    rate: "Rate",
    record_date: "RecordDate",
    special: "Special",
    symbol: "Symbol"
};
const reverse_split_mapping = {
    ex_date: "ExDate",
    new_rate: "NewRate",
    old_rate: "OldRate",
    payable_date: "PayableDate",
    process_date: "ProcessDate",
    record_date: "RecordDate",
    symbol: "Symbol"
};
const forward_split_mapping = {
    due_bill_redemption_date: "DueBillRedemptionDate",
    ex_date: "ExDate",
    new_rate: "NewRate",
    old_rate: "OldRate",
    payable_date: "PayableDate",
    process_date: "ProcessDate",
    record_date: "RecordDate",
    symbol: "Symbol"
};
const unit_split_mapping = {
    alternate_rate: "AlternateRate",
    alternate_symbol: "AlternateSymbol",
    effective_date: "EffectiveDate",
    new_rate: "NewRate",
    new_symbol: "NewSymbol",
    old_rate: "OldRate",
    old_symbol: "OldSymbol",
    process_date: "ProcessDate"
};
const cash_merger_mapping = {
    acquiree_symbol: "AcquireeSymbol",
    acquirer_symbol: "AcquirerSymbol",
    effective_date: "EffectiveDate",
    process_date: "ProcessDate",
    rate: "Rate"
};
const stock_merger_mapping = {
    acquiree_rate: "AcquireeRate",
    acquiree_symbol: "AcquireeSymbol",
    acquirer_rate: "AcquirerRate",
    acquirer_symbol: "AcquirerSymbol",
    effective_date: "EffectiveDate",
    payable_date: "PayableDate",
    process_date: "ProcessDate"
};
const stock_and_cash_merger_mapping = {
    stock_merger_mapping,
    cash_rate: "CashRate"
};
const stock_dividends_mapping = {
    ex_date: "ExDate",
    payable_date: "PayableDate",
    process_date: "ProcessDate",
    rate: "Rate",
    record_date: "RecordDate",
    symbol: "Symbol"
};
const redemption_mapping = {
    payable_date: "PayableDate",
    process_date: "ProcessDate",
    rate: "Rate",
    symbol: "Symbol"
};
const spin_off_mapping = {
    ex_date: "ExDate",
    new_rate: "NewRate",
    new_symbol: "NewSymbol",
    process_date: "ProcessDate",
    record_date: "RecordDate",
    source_rate: "Rate",
    source_symbol: "SourceSymbol"
};
const name_change_mapping = {
    new_symbol: "NewSymbol",
    old_symbol: "OldSymbol",
    process_date: "ProcessDate"
};
const worthless_removal_mapping = {
    symbol: "Symbol",
    process_date: "ProcessDate"
};
const rights_distribution_mapping = {
    source_symbol: "SourceSymbol",
    new_symbol: "NewSymbol",
    rate: "Rate",
    process_date: "ProcessDate",
    ex_date: "ExDate",
    payable_date: "PayableDate",
    record_date: "RecordDate",
    expiration_date: "ExpirationDate"
};
function convertCorporateActions(data) {
    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o;
    let cas = {};
    if (((_a = data.cash_dividends) === null || _a === void 0 ? void 0 : _a.length) > 0) {
        cas.CashDividends = cas.CashDividends ? cas.CashDividends : Array();
        data.cash_dividends.forEach((cd)=>{
            cas.CashDividends.push(aliasObjectKey(cd, cash_dividend_mapping));
        });
    }
    if (((_b = data.reverse_splits) === null || _b === void 0 ? void 0 : _b.length) > 0) {
        cas.ReverseSplits = cas.ReverseSplits ? cas.ReverseSplits : Array();
        data.reverse_splits.forEach((rs)=>{
            cas.ReverseSplits.push(aliasObjectKey(rs, reverse_split_mapping));
        });
    }
    if (((_c = data.forward_splits) === null || _c === void 0 ? void 0 : _c.length) > 0) {
        cas.ForwardSplits = cas.ForwardSplits ? cas.ForwardSplits : Array();
        data.forward_splits.forEach((fs)=>{
            cas.ForwardSplits.push(aliasObjectKey(fs, forward_split_mapping));
        });
    }
    if (((_d = data.unit_splits) === null || _d === void 0 ? void 0 : _d.length) > 0) {
        cas.UnitSplits = cas.UnitSplits ? cas.UnitSplits : Array();
        data.unit_splits.forEach((fs)=>{
            cas.UnitSplits.push(aliasObjectKey(fs, unit_split_mapping));
        });
    }
    if (((_e = data.cash_mergers) === null || _e === void 0 ? void 0 : _e.length) > 0) {
        cas.CashMergers = cas.CashMergers ? cas.CashMergers : Array();
        data.cash_mergers.forEach((cm)=>{
            cas.CashMergers.push(aliasObjectKey(cm, cash_merger_mapping));
        });
    }
    if (((_f = data.stock_mergers) === null || _f === void 0 ? void 0 : _f.length) > 0) {
        cas.StockMergers = cas.StockMergers ? cas.StockMergers : Array();
        data.stock_mergers.forEach((sm)=>{
            cas.StockMergers.push(aliasObjectKey(sm, stock_merger_mapping));
        });
    }
    if (((_g = data.stock_and_cash_mergers) === null || _g === void 0 ? void 0 : _g.length) > 0) {
        cas.StockAndCashMerger = cas.StockAndCashMerger ? cas.StockAndCashMerger : Array();
        data.stock_and_cash_mergers.forEach((scm)=>{
            cas.StockAndCashMerger.push(aliasObjectKey(scm, stock_and_cash_merger_mapping));
        });
    }
    if (((_h = data.stock_dividends) === null || _h === void 0 ? void 0 : _h.length) > 0) {
        cas.StockDividends = cas.StockDividends ? cas.StockDividends : Array();
        data.stock_dividends.forEach((sd)=>{
            cas.StockDividends.push(aliasObjectKey(sd, stock_dividends_mapping));
        });
    }
    if (((_j = data.redemptions) === null || _j === void 0 ? void 0 : _j.length) > 0) {
        cas.Redemptions = cas.Redemptions ? cas.Redemptions : Array();
        data.redemptions.forEach((r)=>{
            cas.Redemptions.push(aliasObjectKey(r, redemption_mapping));
        });
    }
    if (((_k = data.spin_offs) === null || _k === void 0 ? void 0 : _k.length) > 0) {
        cas.SpinOffs = cas.SpinOffs ? cas.SpinOffs : Array();
        data.spin_offs.forEach((so)=>{
            cas.SpinOffs.push(aliasObjectKey(so, spin_off_mapping));
        });
    }
    if (((_l = data.name_changes) === null || _l === void 0 ? void 0 : _l.length) > 0) {
        cas.NameChanges = cas.NameChanges ? cas.NameChanges : Array();
        data.name_changes.forEach((nc)=>{
            cas.NameChanges.push(aliasObjectKey(nc, name_change_mapping));
        });
    }
    if (((_m = data.worthless_removals) === null || _m === void 0 ? void 0 : _m.length) > 0) {
        cas.WorthlessRemovals = cas.WorthlessRemovals ? cas.WorthlessRemovals : Array();
        data.worthless_removals.forEach((wr)=>{
            cas.WorthlessRemovals.push(aliasObjectKey(wr, worthless_removal_mapping));
        });
    }
    if (((_o = data.rights_distributions) === null || _o === void 0 ? void 0 : _o.length) > 0) {
        cas.RightsDistributions = cas.RightsDistributions ? cas.RightsDistributions : Array();
        data.rights_distributions.forEach((rd)=>{
            cas.RightsDistributions.push(aliasObjectKey(rd, rights_distribution_mapping));
        });
    }
    return cas;
}
exports.convertCorporateActions = convertCorporateActions;
function getCorporateActionsSize(cas) {
    let sum = 0;
    for(const key in cas){
        sum += cas[key] ? cas[key].length : 0;
    }
    return sum;
}
exports.getCorporateActionsSize = getCorporateActionsSize;
function mergeCorporateActions(ca1, ca2) {
    return {
        CashDividends: (ca1.CashDividends || []).concat(ca2.CashDividends || []),
        ReverseSplits: (ca1.ReverseSplits || []).concat(ca2.ReverseSplits || []),
        ForwardSplits: (ca1.ForwardSplits || []).concat(ca2.ForwardSplits || []),
        UnitSplits: (ca1.UnitSplits || []).concat(ca2.UnitSplits || []),
        CashMergers: (ca1.CashMergers || []).concat(ca2.CashMergers || []),
        StockMergers: (ca1.StockMergers || []).concat(ca2.StockMergers || []),
        StockAndCashMerger: (ca1.StockAndCashMerger || []).concat(ca2.StockAndCashMerger || []),
        StockDividends: (ca1.StockDividends || []).concat(ca2.StockDividends || []),
        Redemptions: (ca1.Redemptions || []).concat(ca2.Redemptions || []),
        SpinOffs: (ca1.SpinOffs || []).concat(ca2.SpinOffs || []),
        NameChanges: (ca1.NameChanges || []).concat(ca2.NameChanges || []),
        WorthlessRemovals: (ca1.WorthlessRemovals || []).concat(ca2.WorthlessRemovals || []),
        RightsDistributions: (ca1.RightsDistributions || []).concat(ca2.RightsDistributions || [])
    };
}
exports.mergeCorporateActions = mergeCorporateActions;
}),
"[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/datav2/rest_v2.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

var __awaiter = /*TURBOPACK member replacement*/ __turbopack_context__.e && /*TURBOPACK member replacement*/ __turbopack_context__.e.__awaiter || function(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
        });
    }
    return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __await = /*TURBOPACK member replacement*/ __turbopack_context__.e && /*TURBOPACK member replacement*/ __turbopack_context__.e.__await || function(v) {
    return this instanceof __await ? (this.v = v, this) : new __await(v);
};
var __asyncGenerator = /*TURBOPACK member replacement*/ __turbopack_context__.e && /*TURBOPACK member replacement*/ __turbopack_context__.e.__asyncGenerator || function(thisArg, _arguments, generator) {
    if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
    var g = generator.apply(thisArg, _arguments || []), i, q = [];
    return i = {}, verb("next"), verb("throw"), verb("return", awaitReturn), i[Symbol.asyncIterator] = function() {
        return this;
    }, i;
    //TURBOPACK unreachable
    ;
    function awaitReturn(f) {
        return function(v) {
            return Promise.resolve(v).then(f, reject);
        };
    }
    function verb(n, f) {
        if (g[n]) {
            i[n] = function(v) {
                return new Promise(function(a, b) {
                    q.push([
                        n,
                        v,
                        a,
                        b
                    ]) > 1 || resume(n, v);
                });
            };
            if (f) i[n] = f(i[n]);
        }
    }
    function resume(n, v) {
        try {
            step(g[n](v));
        } catch (e) {
            settle(q[0][3], e);
        }
    }
    function step(r) {
        r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);
    }
    function fulfill(value) {
        resume("next", value);
    }
    function reject(value) {
        resume("throw", value);
    }
    function settle(f, v) {
        if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]);
    }
};
var __asyncValues = /*TURBOPACK member replacement*/ __turbopack_context__.e && /*TURBOPACK member replacement*/ __turbopack_context__.e.__asyncValues || function(o) {
    if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
    var m = o[Symbol.asyncIterator], i;
    return m ? m.call(o) : (o = typeof __values === "function" ? __values(o) : o[Symbol.iterator](), i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function() {
        return this;
    }, i);
    //TURBOPACK unreachable
    ;
    function verb(n) {
        i[n] = o[n] && function(v) {
            return new Promise(function(resolve, reject) {
                v = o[n](v), settle(resolve, reject, v.done, v.value);
            });
        };
    }
    function settle(resolve, reject, d, v) {
        Promise.resolve(v).then(function(v) {
            resolve({
                value: v,
                done: d
            });
        }, reject);
    }
};
var __importDefault = /*TURBOPACK member replacement*/ __turbopack_context__.e && /*TURBOPACK member replacement*/ __turbopack_context__.e.__importDefault || function(mod) {
    return mod && mod.__esModule ? mod : {
        "default": mod
    };
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.getCorporateActions = exports.getOptionChain = exports.getOptionSnapshots = exports.getLatestOptionQuotes = exports.getLatestOptionTrades = exports.getMultiOptionTradesAsync = exports.getMultiOptionTrades = exports.getMultiOptionBarsAsync = exports.getMultiOptionBars = exports.getNews = exports.Sort = exports.getLatestCryptoOrderbooks = exports.getCryptoSnapshots = exports.getLatestCryptoQuotes = exports.getLatestCryptoTrades = exports.getLatestCryptoBars = exports.getCryptoBars = exports.getCryptoQuotes = exports.getCryptoTrades = exports.getSnapshots = exports.getSnapshot = exports.getLatestBars = exports.getLatestBar = exports.getLatestQuotes = exports.getLatestQuote = exports.getLatestTrades = exports.getLatestTrade = exports.getMultiBarsAsync = exports.getMultiBars = exports.getBars = exports.getMultiQuotesAsync = exports.getMultiQuotes = exports.getQuotes = exports.getMultiTradesAsync = exports.getMultiTrades = exports.getTrades = exports.getMultiDataV2 = exports.getDataV2 = exports.dataV2HttpRequest = exports.TYPE = exports.Adjustment = void 0;
const axios_1 = __importDefault(__turbopack_context__.r("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/node_modules/axios/index.js [app-route] (ecmascript)"));
const entityv2_1 = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/datav2/entityv2.js [app-route] (ecmascript)");
// Number of data points to return.
const V2_MAX_LIMIT = 10000;
const V2_NEWS_MAX_LIMIT = 50;
const V1_BETA1_MAX_LIMIT = 1000;
var Adjustment;
(function(Adjustment) {
    Adjustment["RAW"] = "raw";
    Adjustment["DIVIDEND"] = "dividend";
    Adjustment["SPLIT"] = "split";
    Adjustment["ALL"] = "all";
})(Adjustment || (exports.Adjustment = Adjustment = {}));
var TYPE;
(function(TYPE) {
    TYPE["TRADES"] = "trades";
    TYPE["QUOTES"] = "quotes";
    TYPE["BARS"] = "bars";
    TYPE["SNAPSHOTS"] = "snapshots";
})(TYPE || (exports.TYPE = TYPE = {}));
function dataV2HttpRequest(url, queryParams, config) {
    const { dataBaseUrl, keyId, secretKey, oauth } = config;
    const headers = {
        "Content-Type": "application/json",
        "Accept-Encoding": "gzip"
    };
    if (oauth == "") {
        headers["APCA-API-KEY-ID"] = keyId;
        headers["APCA-API-SECRET-KEY"] = secretKey;
    } else {
        headers["Authorization"] = "Bearer " + oauth;
    }
    return axios_1.default.get(`${dataBaseUrl}${url}`, {
        params: queryParams,
        headers: headers
    }).catch((err)=>{
        var _a, _b;
        throw new Error(`code: ${((_a = err.response) === null || _a === void 0 ? void 0 : _a.status) || err.statusCode}, message: ${(_b = err.response) === null || _b === void 0 ? void 0 : _b.data.message}`);
    });
}
exports.dataV2HttpRequest = dataV2HttpRequest;
function getQueryLimit(totalLimit, pageLimit, received) {
    let limit = 0;
    if (pageLimit !== 0) {
        limit = pageLimit;
    }
    if (totalLimit !== 0) {
        const remaining = totalLimit - received;
        if (remaining <= 0) {
            // this should never happen
            return -1;
        }
        if (limit == 0 || limit > remaining) {
            limit = remaining;
        }
    }
    return limit;
}
function getDataV2(endpoint, path, options, config) {
    return __asyncGenerator(this, arguments, function* getDataV2_1() {
        var _a;
        let pageToken = null;
        let received = 0;
        const pageLimit = options.pageLimit ? Math.min(options.pageLimit, V2_MAX_LIMIT) : V2_MAX_LIMIT;
        delete options.pageLimit;
        options.limit = (_a = options.limit) !== null && _a !== void 0 ? _a : 0;
        while(options.limit > received || options.limit === 0){
            let limit;
            if (options.limit !== 0) {
                limit = getQueryLimit(options.limit, pageLimit, received);
                if (limit == -1) {
                    break;
                }
            } else {
                limit = null;
            }
            const resp = yield __await(dataV2HttpRequest(path, Object.assign(Object.assign({}, options), {
                limit,
                page_token: pageToken
            }), config));
            const items = resp.data[endpoint] || [];
            for (const item of items){
                yield yield __await(item);
            }
            received += items.length;
            pageToken = resp.data.next_page_token;
            if (!pageToken) {
                break;
            }
        }
    });
}
exports.getDataV2 = getDataV2;
function getMultiDataV2(symbols, url, endpoint, options, config) {
    return __asyncGenerator(this, arguments, function* getMultiDataV2_1() {
        var _a;
        let pageToken = null;
        let received = 0;
        const pageLimit = options.pageLimit ? Math.min(options.pageLimit, V2_MAX_LIMIT) : V2_MAX_LIMIT;
        delete options.pageLimit;
        options.limit = (_a = options.limit) !== null && _a !== void 0 ? _a : 0;
        while(options.limit > received || options.limit === 0){
            const limit = getQueryLimit(options.limit, pageLimit, received);
            if (limit == -1) {
                break;
            }
            const params = Object.assign(Object.assign({}, options), {
                symbols: symbols.join(","),
                limit: limit,
                page_token: pageToken
            });
            const resp = yield __await(dataV2HttpRequest(`${url}${endpoint}`, params, config));
            const items = resp.data[endpoint];
            for(const symbol in items){
                for (const data of items[symbol]){
                    received++;
                    yield yield __await({
                        symbol: symbol,
                        data: data
                    });
                }
            }
            pageToken = resp.data.next_page_token;
            if (!pageToken) {
                break;
            }
        }
    });
}
exports.getMultiDataV2 = getMultiDataV2;
function getTrades(symbol, options, config) {
    return __asyncGenerator(this, arguments, function* getTrades_1() {
        var _a, e_1, _b, _c;
        const trades = getDataV2(TYPE.TRADES, `/v2/stocks/${symbol}/${TYPE.TRADES}`, options, config);
        try {
            for(var _d = true, trades_1 = __asyncValues(trades), trades_1_1; trades_1_1 = yield __await(trades_1.next()), _a = trades_1_1.done, !_a; _d = true){
                _c = trades_1_1.value;
                _d = false;
                const trade = _c;
                yield yield __await((0, entityv2_1.AlpacaTradeV2)(trade));
            }
        } catch (e_1_1) {
            e_1 = {
                error: e_1_1
            };
        } finally{
            try {
                if (!_d && !_a && (_b = trades_1.return)) yield __await(_b.call(trades_1));
            } finally{
                if (e_1) throw e_1.error;
            }
        }
    });
}
exports.getTrades = getTrades;
function getMultiTrades(symbols, options, config) {
    return __awaiter(this, void 0, void 0, function*() {
        var _a, e_2, _b, _c;
        const multiTrades = getMultiTradesAsync(symbols, options, config);
        const trades = new Map();
        try {
            for(var _d = true, multiTrades_1 = __asyncValues(multiTrades), multiTrades_1_1; multiTrades_1_1 = yield multiTrades_1.next(), _a = multiTrades_1_1.done, !_a; _d = true){
                _c = multiTrades_1_1.value;
                _d = false;
                const t = _c;
                const items = trades.get(t.Symbol) || new Array();
                trades.set(t.Symbol, [
                    ...items,
                    t
                ]);
            }
        } catch (e_2_1) {
            e_2 = {
                error: e_2_1
            };
        } finally{
            try {
                if (!_d && !_a && (_b = multiTrades_1.return)) yield _b.call(multiTrades_1);
            } finally{
                if (e_2) throw e_2.error;
            }
        }
        return trades;
    });
}
exports.getMultiTrades = getMultiTrades;
function getMultiTradesAsync(symbols, options, config) {
    return __asyncGenerator(this, arguments, function* getMultiTradesAsync_1() {
        var _a, e_3, _b, _c;
        const multiTrades = getMultiDataV2(symbols, "/v2/stocks/", TYPE.TRADES, options, config);
        try {
            for(var _d = true, multiTrades_2 = __asyncValues(multiTrades), multiTrades_2_1; multiTrades_2_1 = yield __await(multiTrades_2.next()), _a = multiTrades_2_1.done, !_a; _d = true){
                _c = multiTrades_2_1.value;
                _d = false;
                const t = _c;
                t.data = Object.assign(Object.assign({}, t.data), {
                    S: t.symbol
                });
                yield yield __await((0, entityv2_1.AlpacaTradeV2)(t.data));
            }
        } catch (e_3_1) {
            e_3 = {
                error: e_3_1
            };
        } finally{
            try {
                if (!_d && !_a && (_b = multiTrades_2.return)) yield __await(_b.call(multiTrades_2));
            } finally{
                if (e_3) throw e_3.error;
            }
        }
    });
}
exports.getMultiTradesAsync = getMultiTradesAsync;
function getQuotes(symbol, options, config) {
    return __asyncGenerator(this, arguments, function* getQuotes_1() {
        var _a, e_4, _b, _c;
        const quotes = getDataV2(TYPE.QUOTES, `/v2/stocks/${symbol}/${TYPE.QUOTES}`, options, config);
        try {
            for(var _d = true, quotes_1 = __asyncValues(quotes), quotes_1_1; quotes_1_1 = yield __await(quotes_1.next()), _a = quotes_1_1.done, !_a; _d = true){
                _c = quotes_1_1.value;
                _d = false;
                const quote = _c;
                yield yield __await((0, entityv2_1.AlpacaQuoteV2)(quote));
            }
        } catch (e_4_1) {
            e_4 = {
                error: e_4_1
            };
        } finally{
            try {
                if (!_d && !_a && (_b = quotes_1.return)) yield __await(_b.call(quotes_1));
            } finally{
                if (e_4) throw e_4.error;
            }
        }
    });
}
exports.getQuotes = getQuotes;
function getMultiQuotes(symbols, options, config) {
    return __awaiter(this, void 0, void 0, function*() {
        var _a, e_5, _b, _c;
        const multiQuotes = getMultiQuotesAsync(symbols, options, config);
        const quotes = new Map();
        try {
            for(var _d = true, multiQuotes_1 = __asyncValues(multiQuotes), multiQuotes_1_1; multiQuotes_1_1 = yield multiQuotes_1.next(), _a = multiQuotes_1_1.done, !_a; _d = true){
                _c = multiQuotes_1_1.value;
                _d = false;
                const q = _c;
                const items = quotes.get(q.Symbol) || new Array();
                quotes.set(q.Symbol, [
                    ...items,
                    q
                ]);
            }
        } catch (e_5_1) {
            e_5 = {
                error: e_5_1
            };
        } finally{
            try {
                if (!_d && !_a && (_b = multiQuotes_1.return)) yield _b.call(multiQuotes_1);
            } finally{
                if (e_5) throw e_5.error;
            }
        }
        return quotes;
    });
}
exports.getMultiQuotes = getMultiQuotes;
function getMultiQuotesAsync(symbols, options, config) {
    return __asyncGenerator(this, arguments, function* getMultiQuotesAsync_1() {
        var _a, e_6, _b, _c;
        const multiQuotes = getMultiDataV2(symbols, "/v2/stocks/", TYPE.QUOTES, options, config);
        try {
            for(var _d = true, multiQuotes_2 = __asyncValues(multiQuotes), multiQuotes_2_1; multiQuotes_2_1 = yield __await(multiQuotes_2.next()), _a = multiQuotes_2_1.done, !_a; _d = true){
                _c = multiQuotes_2_1.value;
                _d = false;
                const q = _c;
                q.data = Object.assign(Object.assign({}, q.data), {
                    S: q.symbol
                });
                yield yield __await((0, entityv2_1.AlpacaQuoteV2)(q.data));
            }
        } catch (e_6_1) {
            e_6 = {
                error: e_6_1
            };
        } finally{
            try {
                if (!_d && !_a && (_b = multiQuotes_2.return)) yield __await(_b.call(multiQuotes_2));
            } finally{
                if (e_6) throw e_6.error;
            }
        }
    });
}
exports.getMultiQuotesAsync = getMultiQuotesAsync;
function getBars(symbol, options, config) {
    return __asyncGenerator(this, arguments, function* getBars_1() {
        var _a, e_7, _b, _c;
        const bars = getDataV2(TYPE.BARS, `/v2/stocks/${symbol}/${TYPE.BARS}`, options, config);
        try {
            for(var _d = true, _e = __asyncValues(bars || []), _f; _f = yield __await(_e.next()), _a = _f.done, !_a; _d = true){
                _c = _f.value;
                _d = false;
                const bar = _c;
                yield yield __await((0, entityv2_1.AlpacaBarV2)(bar));
            }
        } catch (e_7_1) {
            e_7 = {
                error: e_7_1
            };
        } finally{
            try {
                if (!_d && !_a && (_b = _e.return)) yield __await(_b.call(_e));
            } finally{
                if (e_7) throw e_7.error;
            }
        }
    });
}
exports.getBars = getBars;
function getMultiBars(symbols, options, config) {
    return __awaiter(this, void 0, void 0, function*() {
        var _a, e_8, _b, _c;
        const multiBars = getMultiBarsAsync(symbols, options, config);
        const bars = new Map();
        try {
            for(var _d = true, multiBars_1 = __asyncValues(multiBars), multiBars_1_1; multiBars_1_1 = yield multiBars_1.next(), _a = multiBars_1_1.done, !_a; _d = true){
                _c = multiBars_1_1.value;
                _d = false;
                const b = _c;
                const items = bars.get(b.Symbol) || new Array();
                bars.set(b.Symbol, [
                    ...items,
                    b
                ]);
            }
        } catch (e_8_1) {
            e_8 = {
                error: e_8_1
            };
        } finally{
            try {
                if (!_d && !_a && (_b = multiBars_1.return)) yield _b.call(multiBars_1);
            } finally{
                if (e_8) throw e_8.error;
            }
        }
        return bars;
    });
}
exports.getMultiBars = getMultiBars;
function getMultiBarsAsync(symbols, options, config) {
    return __asyncGenerator(this, arguments, function* getMultiBarsAsync_1() {
        var _a, e_9, _b, _c;
        const multiBars = getMultiDataV2(symbols, "/v2/stocks/", TYPE.BARS, options, config);
        try {
            for(var _d = true, multiBars_2 = __asyncValues(multiBars), multiBars_2_1; multiBars_2_1 = yield __await(multiBars_2.next()), _a = multiBars_2_1.done, !_a; _d = true){
                _c = multiBars_2_1.value;
                _d = false;
                const b = _c;
                b.data = Object.assign(Object.assign({}, b.data), {
                    S: b.symbol
                });
                yield yield __await((0, entityv2_1.AlpacaBarV2)(b.data));
            }
        } catch (e_9_1) {
            e_9 = {
                error: e_9_1
            };
        } finally{
            try {
                if (!_d && !_a && (_b = multiBars_2.return)) yield __await(_b.call(multiBars_2));
            } finally{
                if (e_9) throw e_9.error;
            }
        }
    });
}
exports.getMultiBarsAsync = getMultiBarsAsync;
function getLatestTrade(symbol, config) {
    return __awaiter(this, void 0, void 0, function*() {
        const resp = yield dataV2HttpRequest(`/v2/stocks/${symbol}/trades/latest`, {}, config);
        return (0, entityv2_1.AlpacaTradeV2)(resp.data.trade);
    });
}
exports.getLatestTrade = getLatestTrade;
function getLatestTrades(symbols, config) {
    return __awaiter(this, void 0, void 0, function*() {
        const resp = yield dataV2HttpRequest(`/v2/stocks/${TYPE.TRADES}/latest`, {
            symbols: symbols.join(",")
        }, config);
        const multiLatestTrades = resp.data.trades;
        const multiLatestTradesResp = new Map();
        for(const symbol in multiLatestTrades){
            multiLatestTradesResp.set(symbol, (0, entityv2_1.AlpacaTradeV2)(Object.assign({
                S: symbol
            }, multiLatestTrades[symbol])));
        }
        return multiLatestTradesResp;
    });
}
exports.getLatestTrades = getLatestTrades;
function getLatestQuote(symbol, config) {
    return __awaiter(this, void 0, void 0, function*() {
        const resp = yield dataV2HttpRequest(`/v2/stocks/${symbol}/quotes/latest`, {}, config);
        return (0, entityv2_1.AlpacaQuoteV2)(resp.data.quote);
    });
}
exports.getLatestQuote = getLatestQuote;
function getLatestQuotes(symbols, config) {
    return __awaiter(this, void 0, void 0, function*() {
        const resp = yield dataV2HttpRequest(`/v2/stocks/${TYPE.QUOTES}/latest`, {
            symbols: symbols.join(",")
        }, config);
        const multiLatestQuotes = resp.data.quotes;
        const multiLatestQuotesResp = new Map();
        for(const symbol in multiLatestQuotes){
            multiLatestQuotesResp.set(symbol, (0, entityv2_1.AlpacaQuoteV2)(Object.assign({
                S: symbol
            }, multiLatestQuotes[symbol])));
        }
        return multiLatestQuotesResp;
    });
}
exports.getLatestQuotes = getLatestQuotes;
function getLatestBar(symbol, config) {
    return __awaiter(this, void 0, void 0, function*() {
        const resp = yield dataV2HttpRequest(`/v2/stocks/${symbol}/bars/latest`, {}, config);
        return (0, entityv2_1.AlpacaBarV2)(resp.data.bar);
    });
}
exports.getLatestBar = getLatestBar;
function getLatestBars(symbols, config) {
    return __awaiter(this, void 0, void 0, function*() {
        const resp = yield dataV2HttpRequest(`/v2/stocks/${TYPE.BARS}/latest`, {
            symbols: symbols.join(",")
        }, config);
        const multiLatestBars = resp.data.bars;
        const multiLatestBarsResp = new Map();
        for(const symbol in multiLatestBars){
            multiLatestBarsResp.set(symbol, (0, entityv2_1.AlpacaBarV2)(Object.assign({
                S: symbol
            }, multiLatestBars[symbol])));
        }
        return multiLatestBarsResp;
    });
}
exports.getLatestBars = getLatestBars;
function getSnapshot(symbol, config) {
    return __awaiter(this, void 0, void 0, function*() {
        const resp = yield dataV2HttpRequest(`/v2/stocks/${symbol}/snapshot`, {}, config);
        return (0, entityv2_1.AlpacaSnapshotV2)(resp.data);
    });
}
exports.getSnapshot = getSnapshot;
function getSnapshots(symbols, config) {
    return __awaiter(this, void 0, void 0, function*() {
        const resp = yield dataV2HttpRequest(`/v2/stocks/snapshots?symbols=${symbols.join(",")}`, {}, config);
        const result = Object.entries(resp.data).map(([key, val])=>{
            return (0, entityv2_1.AlpacaSnapshotV2)(Object.assign({
                symbol: key
            }, val));
        });
        return result;
    });
}
exports.getSnapshots = getSnapshots;
function getCryptoTrades(symbols, options, config) {
    return __awaiter(this, void 0, void 0, function*() {
        var _a, e_10, _b, _c;
        const cryptoTrades = getMultiDataV2(symbols, "/v1beta3/crypto/us/", TYPE.TRADES, options, config);
        const trades = new Map();
        try {
            for(var _d = true, cryptoTrades_1 = __asyncValues(cryptoTrades), cryptoTrades_1_1; cryptoTrades_1_1 = yield cryptoTrades_1.next(), _a = cryptoTrades_1_1.done, !_a; _d = true){
                _c = cryptoTrades_1_1.value;
                _d = false;
                const t = _c;
                const items = trades.get(t.symbol) || new Array();
                trades.set(t.symbol, [
                    ...items,
                    (0, entityv2_1.AlpacaCryptoTrade)(t.data)
                ]);
            }
        } catch (e_10_1) {
            e_10 = {
                error: e_10_1
            };
        } finally{
            try {
                if (!_d && !_a && (_b = cryptoTrades_1.return)) yield _b.call(cryptoTrades_1);
            } finally{
                if (e_10) throw e_10.error;
            }
        }
        return trades;
    });
}
exports.getCryptoTrades = getCryptoTrades;
function getCryptoQuotes(symbols, options, config) {
    return __awaiter(this, void 0, void 0, function*() {
        var _a, e_11, _b, _c;
        const cryptoQuotes = getMultiDataV2(symbols, "/v1beta3/crypto/us/", TYPE.QUOTES, options, config);
        const quotes = new Map();
        try {
            for(var _d = true, cryptoQuotes_1 = __asyncValues(cryptoQuotes), cryptoQuotes_1_1; cryptoQuotes_1_1 = yield cryptoQuotes_1.next(), _a = cryptoQuotes_1_1.done, !_a; _d = true){
                _c = cryptoQuotes_1_1.value;
                _d = false;
                const t = _c;
                const items = quotes.get(t.symbol) || new Array();
                quotes.set(t.symbol, [
                    ...items,
                    (0, entityv2_1.AlpacaCryptoQuote)(t.data)
                ]);
            }
        } catch (e_11_1) {
            e_11 = {
                error: e_11_1
            };
        } finally{
            try {
                if (!_d && !_a && (_b = cryptoQuotes_1.return)) yield _b.call(cryptoQuotes_1);
            } finally{
                if (e_11) throw e_11.error;
            }
        }
        return quotes;
    });
}
exports.getCryptoQuotes = getCryptoQuotes;
function getCryptoBars(symbols, options, config) {
    return __awaiter(this, void 0, void 0, function*() {
        var _a, e_12, _b, _c;
        const cryptoBars = getMultiDataV2(symbols, "/v1beta3/crypto/us/", TYPE.BARS, options, config);
        const bars = new Map();
        try {
            for(var _d = true, cryptoBars_1 = __asyncValues(cryptoBars), cryptoBars_1_1; cryptoBars_1_1 = yield cryptoBars_1.next(), _a = cryptoBars_1_1.done, !_a; _d = true){
                _c = cryptoBars_1_1.value;
                _d = false;
                const t = _c;
                const items = bars.get(t.symbol) || new Array();
                bars.set(t.symbol, [
                    ...items,
                    (0, entityv2_1.AlpacaCryptoBar)(t.data)
                ]);
            }
        } catch (e_12_1) {
            e_12 = {
                error: e_12_1
            };
        } finally{
            try {
                if (!_d && !_a && (_b = cryptoBars_1.return)) yield _b.call(cryptoBars_1);
            } finally{
                if (e_12) throw e_12.error;
            }
        }
        return bars;
    });
}
exports.getCryptoBars = getCryptoBars;
function getLatestCryptoBars(symbols, config) {
    return __awaiter(this, void 0, void 0, function*() {
        const params = {
            symbols: symbols.join(",")
        };
        const resp = yield dataV2HttpRequest(`/v1beta3/crypto/us/latest/bars`, params, config);
        const multiLatestCryptoBars = resp.data.bars;
        const result = new Map();
        for(const symbol in multiLatestCryptoBars){
            const bar = multiLatestCryptoBars[symbol];
            result.set(symbol, (0, entityv2_1.AlpacaCryptoBar)(bar));
        }
        return result;
    });
}
exports.getLatestCryptoBars = getLatestCryptoBars;
function getLatestCryptoTrades(symbols, config) {
    return __awaiter(this, void 0, void 0, function*() {
        const params = {
            symbols: symbols.join(",")
        };
        const resp = yield dataV2HttpRequest(`/v1beta3/crypto/us/latest/trades`, params, config);
        const multiLatestCryptoTrades = resp.data.trades;
        const result = new Map();
        for(const symbol in multiLatestCryptoTrades){
            const trade = multiLatestCryptoTrades[symbol];
            result.set(symbol, (0, entityv2_1.AlpacaCryptoTrade)(trade));
        }
        return result;
    });
}
exports.getLatestCryptoTrades = getLatestCryptoTrades;
function getLatestCryptoQuotes(symbols, config) {
    return __awaiter(this, void 0, void 0, function*() {
        const params = {
            symbols: symbols.join(",")
        };
        const resp = yield dataV2HttpRequest(`/v1beta3/crypto/us/latest/quotes`, params, config);
        const multiLatestCryptoQuotes = resp.data.quotes;
        const result = new Map();
        for(const symbol in multiLatestCryptoQuotes){
            const quote = multiLatestCryptoQuotes[symbol];
            result.set(symbol, (0, entityv2_1.AlpacaCryptoQuote)(quote));
        }
        return result;
    });
}
exports.getLatestCryptoQuotes = getLatestCryptoQuotes;
function getCryptoSnapshots(symbols, config) {
    return __awaiter(this, void 0, void 0, function*() {
        const params = {
            symbols: symbols.join(",")
        };
        const resp = yield dataV2HttpRequest(`/v1beta3/crypto/us/snapshots`, params, config);
        const snapshots = resp.data.snapshots;
        const result = new Map();
        for(const symbol in snapshots){
            const snapshot = snapshots[symbol];
            result.set(symbol, (0, entityv2_1.AlpacaCryptoSnapshot)(snapshot));
        }
        return result;
    });
}
exports.getCryptoSnapshots = getCryptoSnapshots;
function getLatestCryptoOrderbooks(symbols, config) {
    return __awaiter(this, void 0, void 0, function*() {
        const params = {
            symbols: symbols.join(",")
        };
        const resp = yield dataV2HttpRequest(`/v1beta3/crypto/us/latest/orderbooks`, params, config);
        const orderbooks = resp.data.orderbooks;
        const result = new Map();
        for(const symbol in orderbooks){
            const orderbook = orderbooks[symbol];
            result.set(symbol, (0, entityv2_1.AlpacaCryptoOrderbook)(orderbook));
        }
        return result;
    });
}
exports.getLatestCryptoOrderbooks = getLatestCryptoOrderbooks;
var Sort;
(function(Sort) {
    Sort["ASC"] = "asc";
    Sort["DESC"] = "desc";
})(Sort || (exports.Sort = Sort = {}));
function getNewsParams(options) {
    var _a;
    const query = {};
    query.symbols = ((_a = options.symbols) === null || _a === void 0 ? void 0 : _a.length) > 0 ? options.symbols.join(",") : null;
    query.start = options.start;
    query.end = options.end;
    query.sort = options.sort;
    query.include_content = options.includeContent;
    query.exclude_contentless = options.excludeContentless;
    return query;
}
function getNews(options, config) {
    return __awaiter(this, void 0, void 0, function*() {
        var _a;
        if (options.totalLimit && options.totalLimit < 0) {
            throw new Error("negative total limit");
        }
        if (options.pageLimit && options.pageLimit < 0) {
            throw new Error("negative page limit");
        }
        let pageToken = null;
        let received = 0;
        const pageLimit = (options === null || options === void 0 ? void 0 : options.pageLimit) ? Math.min(options.pageLimit, V2_NEWS_MAX_LIMIT) : V2_NEWS_MAX_LIMIT;
        options === null || options === void 0 ? true : delete options.pageLimit;
        const totalLimit = (_a = options.totalLimit) !== null && _a !== void 0 ? _a : 10;
        const result = [];
        const params = getNewsParams(options);
        let limit;
        for(;;){
            limit = getQueryLimit(totalLimit, pageLimit, received);
            if (limit < 1) {
                break;
            }
            const resp = yield dataV2HttpRequest("/v1beta1/news", Object.assign(Object.assign({}, params), {
                limit: limit,
                page_token: pageToken
            }), config);
            resp.data.news.forEach((n)=>result.push((0, entityv2_1.AlpacaNews)(n)));
            received += resp.data.news.length;
            pageToken = resp.data.next_page_token;
            if (!pageToken) {
                break;
            }
        }
        return result;
    });
}
exports.getNews = getNews;
function getMultiOptionBars(symbols, options, config) {
    return __awaiter(this, void 0, void 0, function*() {
        var _a, e_13, _b, _c;
        const multiBars = getMultiOptionBarsAsync(symbols, options, config);
        const bars = new Map();
        try {
            for(var _d = true, multiBars_3 = __asyncValues(multiBars), multiBars_3_1; multiBars_3_1 = yield multiBars_3.next(), _a = multiBars_3_1.done, !_a; _d = true){
                _c = multiBars_3_1.value;
                _d = false;
                const b = _c;
                // symbol will always have a value
                let symbol = b.Symbol ? b.Symbol : "";
                delete b.Symbol;
                const items = bars.get(symbol) || new Array();
                bars.set(symbol, [
                    ...items,
                    b
                ]);
            }
        } catch (e_13_1) {
            e_13 = {
                error: e_13_1
            };
        } finally{
            try {
                if (!_d && !_a && (_b = multiBars_3.return)) yield _b.call(multiBars_3);
            } finally{
                if (e_13) throw e_13.error;
            }
        }
        return bars;
    });
}
exports.getMultiOptionBars = getMultiOptionBars;
function getMultiOptionBarsAsync(symbols, options, config) {
    return __asyncGenerator(this, arguments, function* getMultiOptionBarsAsync_1() {
        var _a, e_14, _b, _c;
        const multiBars = getMultiDataV2(symbols, "/v1beta1/options/", TYPE.BARS, options, config);
        try {
            for(var _d = true, multiBars_4 = __asyncValues(multiBars), multiBars_4_1; multiBars_4_1 = yield __await(multiBars_4.next()), _a = multiBars_4_1.done, !_a; _d = true){
                _c = multiBars_4_1.value;
                _d = false;
                const b = _c;
                b.data = Object.assign(Object.assign({}, b.data), {
                    S: b.symbol
                });
                yield yield __await((0, entityv2_1.AlpacaOptionBarV1Beta1)(b.data));
            }
        } catch (e_14_1) {
            e_14 = {
                error: e_14_1
            };
        } finally{
            try {
                if (!_d && !_a && (_b = multiBars_4.return)) yield __await(_b.call(multiBars_4));
            } finally{
                if (e_14) throw e_14.error;
            }
        }
    });
}
exports.getMultiOptionBarsAsync = getMultiOptionBarsAsync;
function getMultiOptionTrades(symbols, options, config) {
    return __awaiter(this, void 0, void 0, function*() {
        var _a, e_15, _b, _c;
        const multiTrades = getMultiOptionTradesAsync(symbols, options, config);
        const trades = new Map();
        try {
            for(var _d = true, multiTrades_3 = __asyncValues(multiTrades), multiTrades_3_1; multiTrades_3_1 = yield multiTrades_3.next(), _a = multiTrades_3_1.done, !_a; _d = true){
                _c = multiTrades_3_1.value;
                _d = false;
                const t = _c;
                // symbol will always have a value
                let symbol = t.Symbol ? t.Symbol : "";
                delete t.Symbol;
                const items = trades.get(symbol) || new Array();
                trades.set(symbol, [
                    ...items,
                    t
                ]);
            }
        } catch (e_15_1) {
            e_15 = {
                error: e_15_1
            };
        } finally{
            try {
                if (!_d && !_a && (_b = multiTrades_3.return)) yield _b.call(multiTrades_3);
            } finally{
                if (e_15) throw e_15.error;
            }
        }
        return trades;
    });
}
exports.getMultiOptionTrades = getMultiOptionTrades;
function getMultiOptionTradesAsync(symbols, options, config) {
    return __asyncGenerator(this, arguments, function* getMultiOptionTradesAsync_1() {
        var _a, e_16, _b, _c;
        const multiBars = getMultiDataV2(symbols, "/v1beta1/options/", TYPE.TRADES, options, config);
        try {
            for(var _d = true, multiBars_5 = __asyncValues(multiBars), multiBars_5_1; multiBars_5_1 = yield __await(multiBars_5.next()), _a = multiBars_5_1.done, !_a; _d = true){
                _c = multiBars_5_1.value;
                _d = false;
                const b = _c;
                b.data = Object.assign(Object.assign({}, b.data), {
                    S: b.symbol
                });
                yield yield __await((0, entityv2_1.AlpacaOptionTradeV1Beta1)(b.data));
            }
        } catch (e_16_1) {
            e_16 = {
                error: e_16_1
            };
        } finally{
            try {
                if (!_d && !_a && (_b = multiBars_5.return)) yield __await(_b.call(multiBars_5));
            } finally{
                if (e_16) throw e_16.error;
            }
        }
    });
}
exports.getMultiOptionTradesAsync = getMultiOptionTradesAsync;
function getLatestOptionTrades(symbols, config) {
    return __awaiter(this, void 0, void 0, function*() {
        const resp = yield dataV2HttpRequest(`/v1beta1/options/${TYPE.TRADES}/latest`, {
            symbols: symbols.join(",")
        }, config);
        const multiLatestTrades = resp.data.trades;
        const multiLatestTradesResp = new Map();
        for(const symbol in multiLatestTrades){
            multiLatestTradesResp.set(symbol, (0, entityv2_1.AlpacaOptionTradeV1Beta1)(Object.assign({}, multiLatestTrades[symbol])));
        }
        return multiLatestTradesResp;
    });
}
exports.getLatestOptionTrades = getLatestOptionTrades;
function getLatestOptionQuotes(symbols, config) {
    return __awaiter(this, void 0, void 0, function*() {
        const resp = yield dataV2HttpRequest(`/v1beta1/options/${TYPE.QUOTES}/latest`, {
            symbols: symbols.join(",")
        }, config);
        const multiLatestQuotes = resp.data.quotes;
        const multiLatestQuotesResp = new Map();
        for(const symbol in multiLatestQuotes){
            multiLatestQuotesResp.set(symbol, (0, entityv2_1.AlpacaOptionQuoteV1Beta1)(Object.assign({}, multiLatestQuotes[symbol])));
        }
        return multiLatestQuotesResp;
    });
}
exports.getLatestOptionQuotes = getLatestOptionQuotes;
function getOptionSnapshots(symbols, config) {
    return __awaiter(this, void 0, void 0, function*() {
        const resp = yield dataV2HttpRequest(`/v1beta1/options/snapshots?symbols=${symbols.join(",")}`, {}, config);
        const result = Object.entries(resp.data.snapshots).map(([key, val])=>{
            return (0, entityv2_1.AlpacaOptionSnapshotV1Beta1)(Object.assign({
                Symbol: key
            }, val));
        });
        return result;
    });
}
exports.getOptionSnapshots = getOptionSnapshots;
function getOptionChain(underlyingSymbol, options, config) {
    return __awaiter(this, void 0, void 0, function*() {
        var _a;
        if (options.totalLimit && options.totalLimit < 0) {
            throw new Error("negative total limit");
        }
        if (options.pageLimit && options.pageLimit < 0) {
            throw new Error("negative page limit");
        }
        let pageToken = null;
        let received = 0;
        const pageLimit = (options === null || options === void 0 ? void 0 : options.pageLimit) ? Math.min(options.pageLimit, V1_BETA1_MAX_LIMIT) : V1_BETA1_MAX_LIMIT;
        delete options.pageLimit;
        const totalLimit = (_a = options === null || options === void 0 ? void 0 : options.totalLimit) !== null && _a !== void 0 ? _a : 10000;
        delete options.totalLimit;
        const result = [];
        let limit;
        for(;;){
            limit = getQueryLimit(totalLimit, pageLimit, received);
            if (limit < 1) {
                break;
            }
            const resp = yield dataV2HttpRequest(`/v1beta1/options/snapshots/${underlyingSymbol}`, Object.assign(Object.assign({}, options), {
                limit: limit,
                page_token: pageToken
            }), config);
            const res = Object.entries(resp.data.snapshots).map(([key, val])=>{
                return (0, entityv2_1.AlpacaOptionSnapshotV1Beta1)(Object.assign({
                    Symbol: key
                }, val));
            });
            received = received + res.length;
            result.push(...res);
            pageToken = resp.data.next_page_token;
            if (!pageToken) {
                break;
            }
        }
        return result;
    });
}
exports.getOptionChain = getOptionChain;
function getCorporateActions(symbols, options, config) {
    return __awaiter(this, void 0, void 0, function*() {
        var _a, _b;
        if (options.totalLimit && options.totalLimit < 0) {
            throw new Error("negative total limit");
        }
        if (options.pageLimit && options.pageLimit < 0) {
            throw new Error("negative page limit");
        }
        let pageToken = null;
        let received = 0;
        const pageLimit = (options === null || options === void 0 ? void 0 : options.pageLimit) ? Math.min(options.pageLimit, V1_BETA1_MAX_LIMIT) : V1_BETA1_MAX_LIMIT;
        options === null || options === void 0 ? true : delete options.pageLimit;
        const totalLimit = (_a = options === null || options === void 0 ? void 0 : options.totalLimit) !== null && _a !== void 0 ? _a : V2_MAX_LIMIT;
        delete options.totalLimit;
        let result = {};
        const types = (_b = options === null || options === void 0 ? void 0 : options.types) === null || _b === void 0 ? void 0 : _b.join(",");
        const params = Object.assign(Object.assign({}, options), {
            symbols,
            types
        });
        let limit;
        for(;;){
            limit = getQueryLimit(totalLimit, pageLimit, received);
            if (limit < 1) {
                break;
            }
            const resp = yield dataV2HttpRequest(`/v1beta1/corporate-actions`, Object.assign(Object.assign({}, params), {
                limit: limit,
                page_token: pageToken
            }), config);
            const cas = (0, entityv2_1.convertCorporateActions)(resp.data.corporate_actions);
            result = (0, entityv2_1.mergeCorporateActions)(result, cas);
            received += (0, entityv2_1.getCorporateActionsSize)(cas);
            pageToken = resp.data.next_page_token;
            if (!pageToken) {
                break;
            }
        }
        return result;
    });
}
exports.getCorporateActions = getCorporateActions;
}),
"[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/datav2/websocket.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

var __importDefault = /*TURBOPACK member replacement*/ __turbopack_context__.e && /*TURBOPACK member replacement*/ __turbopack_context__.e.__importDefault || function(mod) {
    return mod && mod.__esModule ? mod : {
        "default": mod
    };
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.AlpacaWebsocket = exports.ERROR = exports.CONN_ERROR = exports.EVENT = exports.STATE = void 0;
const events_1 = __importDefault(__turbopack_context__.r("[externals]/events [external] (events, cjs)"));
const ws_1 = __importDefault(__turbopack_context__.r("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/node_modules/ws/index.js [app-route] (ecmascript)"));
const msgpack5_1 = __importDefault(__turbopack_context__.r("[project]/swing-trader-ai/node_modules/msgpack5/index.js [app-route] (ecmascript)"));
// Connection states. Each of these will also emit EVENT.STATE_CHANGE
var STATE;
(function(STATE) {
    STATE["AUTHENTICATING"] = "authenticating";
    STATE["AUTHENTICATED"] = "authenticated";
    STATE["CONNECTED"] = "connected";
    STATE["CONNECTING"] = "connecting";
    STATE["DISCONNECTED"] = "disconnected";
    STATE["WAITING_TO_CONNECT"] = "waiting to connect";
    STATE["WAITING_TO_RECONNECT"] = "waiting to reconnect";
})(STATE || (exports.STATE = STATE = {}));
// Client events
var EVENT;
(function(EVENT) {
    EVENT["CLIENT_ERROR"] = "client_error";
    EVENT["STATE_CHANGE"] = "state_change";
    EVENT["AUTHORIZED"] = "authorized";
    EVENT["UNAUTHORIZED"] = "unauthorized";
    EVENT["TRADES"] = "stock_trades";
    EVENT["QUOTES"] = "stock_quotes";
    EVENT["BARS"] = "stock_bars";
    EVENT["UPDATED_BARS"] = "stock_updated_bars";
    EVENT["DAILY_BARS"] = "stock_daily_bars";
    EVENT["TRADING_STATUSES"] = "trading_statuses";
    EVENT["LULDS"] = "lulds";
    EVENT["CANCEL_ERRORS"] = "cancel_errors";
    EVENT["CORRECTIONS"] = "corrections";
    EVENT["ORDERBOOKS"] = "orderbooks";
    EVENT["NEWS"] = "news";
})(EVENT || (exports.EVENT = EVENT = {}));
// Connection errors by code
exports.CONN_ERROR = new Map([
    [
        400,
        "invalid syntax"
    ],
    [
        401,
        "not authenticated"
    ],
    [
        402,
        "auth failed"
    ],
    [
        403,
        "already authenticated"
    ],
    [
        404,
        "auth timeout"
    ],
    [
        405,
        "symbol limit exceeded"
    ],
    [
        406,
        "connection limit exceeded"
    ],
    [
        407,
        "slow client"
    ],
    [
        408,
        "v2 not enabled"
    ],
    [
        409,
        "insufficient subscription"
    ],
    [
        500,
        "internal error"
    ]
]);
// Connection errors without code
var ERROR;
(function(ERROR) {
    ERROR["MISSING_SECERT_KEY"] = "missing secret key";
    ERROR["MISSING_API_KEY"] = "missing api key";
    ERROR["UNEXPECTED_MESSAGE"] = "unexpected message";
})(ERROR || (exports.ERROR = ERROR = {}));
class AlpacaWebsocket extends events_1.default.EventEmitter {
    constructor(options){
        super();
        this.msgpack = (0, msgpack5_1.default)();
        this.session = {
            apiKey: options.apiKey,
            secretKey: options.secretKey,
            subscriptions: options.subscriptions,
            reconnect: true,
            verbose: options.verbose,
            backoff: true,
            reconnectTimeout: 0,
            maxReconnectTimeout: 30,
            backoffIncrement: 0.5,
            url: options.url,
            currentState: STATE.WAITING_TO_CONNECT,
            isReconnected: false,
            pongWait: 5000
        };
        // Register internal event handlers
        // Log and emit every state change
        Object.values(STATE).forEach((s)=>{
            this.on(s, ()=>{
                this.emit(EVENT.STATE_CHANGE, s);
            });
        });
    }
    connect() {
        this.emit(STATE.CONNECTING);
        this.session.currentState = STATE.CONNECTING;
        // Check the credentials
        if (this.session.apiKey.length === 0) {
            throw new Error(ERROR.MISSING_API_KEY);
        }
        if (this.session.secretKey.length === 0) {
            throw new Error(ERROR.MISSING_SECERT_KEY);
        }
        this.resetSession();
        this.conn = new ws_1.default(this.session.url, {
            perMessageDeflate: {
                serverNoContextTakeover: false,
                clientNoContextTakeover: false
            },
            headers: {
                "Content-Type": "application/msgpack"
            }
        });
        this.conn.binaryType = "nodebuffer";
        this.conn.once("open", ()=>this.authenticate());
        this.conn.on("message", (data)=>{
            this.handleMessage(this.msgpack.decode(data));
        });
        this.conn.on("error", (err)=>{
            this.emit(EVENT.CLIENT_ERROR, err.message);
            this.disconnect();
        });
        this.conn.on("close", (code, msg)=>{
            this.log(`connection closed with code: ${code} and message: ${msg}`);
            if (this.session.reconnect) {
                this.reconnect();
            }
        });
        this.conn.on("pong", ()=>{
            if (this.session.pongTimeout) {
                clearTimeout(this.session.pongTimeout);
            }
        });
        this.session.pingInterval = setInterval(()=>{
            this.ping();
        }, 10000);
        this.on(STATE.WAITING_TO_RECONNECT, (ts)=>{
            this.log(`backoff: ${ts}`);
        });
    }
    onConnect(fn) {
        this.on(STATE.AUTHENTICATED, ()=>{
            if (this.session.isReconnected) {
                //if reconnected the user should subscribe to its symbols again
                this.subscribeAll();
            } else {
                fn();
            }
        });
    }
    reconnect() {
        this.log("Reconnecting...");
        this.session.isReconnected = true;
        const { backoff, backoffIncrement, maxReconnectTimeout } = this.session;
        let reconnectTimeout = this.session.reconnectTimeout;
        if (backoff) {
            setTimeout(()=>{
                reconnectTimeout += backoffIncrement;
                if (reconnectTimeout > maxReconnectTimeout) {
                    reconnectTimeout = maxReconnectTimeout;
                }
                this.emit(STATE.WAITING_TO_RECONNECT, reconnectTimeout);
                this.connect();
            }, reconnectTimeout * 1000);
        }
    }
    ping() {
        this.conn.ping();
        this.session.pongTimeout = setTimeout(()=>{
            this.log("no pong received from server, terminating...");
            this.conn.terminate();
        }, this.session.pongWait);
    }
    authenticate() {
        const authMsg = {
            action: "auth",
            key: this.session.apiKey,
            secret: this.session.secretKey
        };
        this.conn.send(this.msgpack.encode(authMsg));
        this.emit(STATE.AUTHENTICATING);
        this.session.currentState = STATE.AUTHENTICATING;
    }
    disconnect() {
        this.emit(STATE.DISCONNECTED);
        this.session.currentState = STATE.DISCONNECTED;
        this.conn.close();
        this.session.reconnect = false;
        if (this.session.pongTimeout) {
            clearTimeout(this.session.pongTimeout);
        }
        if (this.session.pingInterval) {
            clearInterval(this.session.pingInterval);
        }
    }
    onDisconnect(fn) {
        this.on(STATE.DISCONNECTED, ()=>fn());
    }
    onError(fn) {
        this.on(EVENT.CLIENT_ERROR, (err)=>fn(err));
    }
    onStateChange(fn) {
        this.on(EVENT.STATE_CHANGE, (newState)=>fn(newState));
    }
    handleMessage(data) {
        const msgType = (data === null || data === void 0 ? void 0 : data.length) ? data[0].T : "";
        switch(msgType){
            case "success":
                if (data[0].msg === "connected") {
                    this.emit(STATE.CONNECTED);
                    this.session.currentState = STATE.CONNECTED;
                } else if (data[0].msg === "authenticated") {
                    this.emit(STATE.AUTHENTICATED);
                    this.session.currentState = STATE.AUTHENTICATED;
                }
                break;
            case "subscription":
                this.updateSubscriptions(data[0]);
                break;
            case "error":
                this.emit(EVENT.CLIENT_ERROR, exports.CONN_ERROR.get(data[0].code));
                break;
            default:
                this.dataHandler(data);
        }
    }
    log(msg) {
        if (this.session.verbose) {
            // eslint-disable-next-line no-console
            console.log(msg);
        }
    }
    getSubscriptions() {
        return this.session.subscriptions;
    }
    resetSession() {
        this.session.reconnect = true;
        this.session.backoff = true;
        this.session.reconnectTimeout = 0;
        this.session.maxReconnectTimeout = 30;
        this.session.backoffIncrement = 0.5;
        if (this.session.pongTimeout) {
            clearTimeout(this.session.pongTimeout);
        }
        if (this.session.pingInterval) {
            clearInterval(this.session.pingInterval);
        }
    }
}
exports.AlpacaWebsocket = AlpacaWebsocket;
}),
"[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/datav2/crypto_websocket_v1beta3.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.AlpacaCryptoClient = void 0;
const entityv2_1 = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/datav2/entityv2.js [app-route] (ecmascript)");
const websocket_1 = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/datav2/websocket.js [app-route] (ecmascript)");
const eventTypeMap = new Map([
    [
        "t",
        {
            event: websocket_1.EVENT.TRADES,
            parse: entityv2_1.AlpacaCryptoTrade
        }
    ],
    [
        "q",
        {
            event: websocket_1.EVENT.QUOTES,
            parse: entityv2_1.AlpacaCryptoQuote
        }
    ],
    [
        "b",
        {
            event: websocket_1.EVENT.BARS,
            parse: entityv2_1.AlpacaCryptoBar
        }
    ],
    [
        "u",
        {
            event: websocket_1.EVENT.UPDATED_BARS,
            parse: entityv2_1.AlpacaCryptoBar
        }
    ],
    [
        "d",
        {
            event: websocket_1.EVENT.DAILY_BARS,
            parse: entityv2_1.AlpacaCryptoBar
        }
    ],
    [
        "o",
        {
            event: websocket_1.EVENT.ORDERBOOKS,
            parse: entityv2_1.AlpacaCryptoOrderbook
        }
    ]
]);
class AlpacaCryptoClient extends websocket_1.AlpacaWebsocket {
    constructor(options){
        options.url = options.url.replace("https", "wss") + "/v1beta3/crypto/us";
        options.subscriptions = {
            trades: [],
            quotes: [],
            bars: [],
            updatedBars: [],
            dailyBars: [],
            orderbooks: []
        };
        super(options);
    }
    subscribeForTrades(trades) {
        this.session.subscriptions.trades.push(...trades);
        this.subscribe({
            trades
        });
    }
    subscribeForQuotes(quotes) {
        this.session.subscriptions.quotes.push(...quotes);
        this.subscribe({
            quotes
        });
    }
    subscribeForBars(bars) {
        this.session.subscriptions.bars.push(...bars);
        this.subscribe({
            bars
        });
    }
    subscribeForUpdatedBars(updatedBars) {
        this.session.subscriptions.updatedBars.push(...updatedBars);
        this.subscribe({
            updatedBars
        });
    }
    subscribeForDailyBars(dailyBars) {
        this.session.subscriptions.dailyBars.push(...dailyBars);
        this.subscribe({
            dailyBars
        });
    }
    subscribeForOrderbooks(orderbooks) {
        this.session.subscriptions.orderbooks.push(...orderbooks);
        this.subscribe({
            orderbooks
        });
    }
    subscribe(symbols) {
        var _a, _b, _c, _d, _e, _f;
        const subMsg = {
            action: "subscribe",
            trades: (_a = symbols.trades) !== null && _a !== void 0 ? _a : [],
            quotes: (_b = symbols.quotes) !== null && _b !== void 0 ? _b : [],
            bars: (_c = symbols.bars) !== null && _c !== void 0 ? _c : [],
            updatedBars: (_d = symbols.updatedBars) !== null && _d !== void 0 ? _d : [],
            dailyBars: (_e = symbols.dailyBars) !== null && _e !== void 0 ? _e : [],
            orderbooks: (_f = symbols.orderbooks) !== null && _f !== void 0 ? _f : []
        };
        this.conn.send(this.msgpack.encode(subMsg));
    }
    subscribeAll() {
        this.subscribe(this.session.subscriptions);
    }
    unsubscribeFromTrades(trades) {
        this.session.subscriptions.trades = this.session.subscriptions.trades.filter((trade)=>!trades.includes(trade));
        this.unsubscribe({
            trades
        });
    }
    unsubscribeFromQuotes(quotes) {
        this.session.subscriptions.quotes = this.session.subscriptions.quotes.filter((quote)=>!quotes.includes(quote));
        this.unsubscribe({
            quotes
        });
    }
    unsubscribeFromBars(bars) {
        this.session.subscriptions.bars = this.session.subscriptions.bars.filter((bar)=>!bars.includes(bar));
        this.unsubscribe({
            bars
        });
    }
    unsubscribeFromUpdatedBars(updatedBars) {
        this.session.subscriptions.updatedBars = this.session.subscriptions.updatedBars.filter((updatedBar)=>!updatedBars.includes(updatedBar));
        this.unsubscribe({
            updatedBars
        });
    }
    unsubscriceFromDailyBars(dailyBars) {
        this.session.subscriptions.dailyBars = this.session.subscriptions.dailyBars.filter((dailyBar)=>!dailyBars.includes(dailyBar));
        this.unsubscribe({
            dailyBars
        });
    }
    unsubscribeFromOrderbooks(orderbooks) {
        this.session.subscriptions.orderbooks = this.session.subscriptions.orderbooks.filter((orderbook)=>!orderbooks.includes(orderbook));
        this.unsubscribe({
            orderbooks
        });
    }
    unsubscribe(symbols) {
        var _a, _b, _c, _d, _e, _f;
        const unsubMsg = {
            action: "unsubscribe",
            trades: (_a = symbols.trades) !== null && _a !== void 0 ? _a : [],
            quotes: (_b = symbols.quotes) !== null && _b !== void 0 ? _b : [],
            bars: (_c = symbols.bars) !== null && _c !== void 0 ? _c : [],
            updatedBars: (_d = symbols.updatedBars) !== null && _d !== void 0 ? _d : [],
            dailyBars: (_e = symbols.dailyBars) !== null && _e !== void 0 ? _e : [],
            orderbooks: (_f = symbols.orderbooks) !== null && _f !== void 0 ? _f : []
        };
        this.conn.send(this.msgpack.encode(unsubMsg));
    }
    updateSubscriptions(msg) {
        this.session.subscriptions = {
            trades: msg.trades,
            quotes: msg.quotes,
            bars: msg.bars,
            updatedBars: msg.updatedBars,
            dailyBars: msg.dailyBars,
            orderbooks: msg.orderbooks
        };
        this.log(`listening to streams:
        ${JSON.stringify(this.session.subscriptions)}`);
    }
    onCryptoTrade(fn) {
        this.on(websocket_1.EVENT.TRADES, (trade)=>fn(trade));
    }
    onCryptoQuote(fn) {
        this.on(websocket_1.EVENT.QUOTES, (quote)=>fn(quote));
    }
    onCryptoBar(fn) {
        this.on(websocket_1.EVENT.BARS, (bar)=>fn(bar));
    }
    onCryptoUpdatedBar(fn) {
        this.on(websocket_1.EVENT.UPDATED_BARS, (updatedBar)=>fn(updatedBar));
    }
    onCryptoDailyBar(fn) {
        this.on(websocket_1.EVENT.DAILY_BARS, (dailyBar)=>fn(dailyBar));
    }
    onCryptoOrderbook(fn) {
        this.on(websocket_1.EVENT.ORDERBOOKS, (orderbook)=>fn(orderbook));
    }
    dataHandler(data) {
        data.forEach((element)=>{
            if ("T" in element) {
                const eventType = eventTypeMap.get(element.T);
                if (eventType) {
                    this.emit(eventType.event, eventType.parse(element));
                } else {
                    this.emit(websocket_1.EVENT.CLIENT_ERROR, websocket_1.ERROR.UNEXPECTED_MESSAGE);
                }
            }
        });
    }
}
exports.AlpacaCryptoClient = AlpacaCryptoClient;
}),
"[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/datav2/news_websocket.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.AlpacaNewsCLient = void 0;
const websocket_1 = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/datav2/websocket.js [app-route] (ecmascript)");
const entityv2_1 = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/datav2/entityv2.js [app-route] (ecmascript)");
const websocket_2 = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/datav2/websocket.js [app-route] (ecmascript)");
class AlpacaNewsCLient extends websocket_2.AlpacaWebsocket {
    constructor(options){
        const url = "wss" + options.url.substr(options.url.indexOf(":")) + "/v1beta1/news";
        options.url = url;
        options.subscriptions = {
            news: []
        };
        super(options);
    }
    subscribeForNews(news) {
        this.session.subscriptions.news.push(...news);
        this.subscribe(news);
    }
    subscribe(news) {
        const subMsg = {
            action: "subscribe",
            news
        };
        console.log("subscribing", subMsg);
        this.conn.send(this.msgpack.encode(subMsg));
    }
    subscribeAll() {
        if (this.session.subscriptions.news.length > 0) {
            this.subscribe(this.session.subscriptions.news);
        }
    }
    unsubscribeFromNews(news) {
        this.session.subscriptions.news = this.session.subscriptions.news.filter((n)=>!news.includes(n));
        this.unsubscribe(news);
    }
    unsubscribe(news) {
        const unsubMsg = {
            action: "unsubscribe",
            news
        };
        this.conn.send(this.msgpack.encode(unsubMsg));
    }
    updateSubscriptions(msg) {
        this.log(`listening to streams:
        news: ${msg.news}`);
        this.session.subscriptions = {
            news: msg.news
        };
    }
    onNews(fn) {
        this.on(websocket_1.EVENT.NEWS, (n)=>fn(n));
    }
    dataHandler(data) {
        data.forEach((element)=>{
            if ("T" in element) {
                switch(element.T){
                    case "n":
                        this.emit(websocket_1.EVENT.NEWS, (0, entityv2_1.AlpacaNews)(element));
                        break;
                    default:
                        this.emit(websocket_1.EVENT.CLIENT_ERROR, websocket_1.ERROR.UNEXPECTED_MESSAGE);
                }
            } else {
                this.emit(websocket_1.EVENT.CLIENT_ERROR, websocket_1.ERROR.UNEXPECTED_MESSAGE);
            }
        });
    }
}
exports.AlpacaNewsCLient = AlpacaNewsCLient;
}),
"[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/datav2/stock_websocket_v2.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.AlpacaStocksClient = void 0;
const entityv2_1 = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/datav2/entityv2.js [app-route] (ecmascript)");
const websocket_1 = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/datav2/websocket.js [app-route] (ecmascript)");
class AlpacaStocksClient extends websocket_1.AlpacaWebsocket {
    constructor(options){
        const url = "wss" + options.url.substr(options.url.indexOf(":")) + "/v2/" + options.feed;
        options.url = url;
        options.subscriptions = {
            trades: [],
            quotes: [],
            bars: [],
            updatedBars: [],
            dailyBars: [],
            statuses: [],
            lulds: [],
            cancelErrors: [],
            corrections: []
        };
        super(options);
    }
    subscribeForTrades(trades) {
        this.session.subscriptions.trades.push(...trades);
        this.subscribe({
            trades
        });
    }
    subscribeForQuotes(quotes) {
        this.session.subscriptions.quotes.push(...quotes);
        this.subscribe({
            quotes
        });
    }
    subscribeForBars(bars) {
        this.session.subscriptions.bars.push(...bars);
        this.subscribe({
            bars
        });
    }
    subscribeForUpdatedBars(updatedBars) {
        this.session.subscriptions.updatedBars.push(...updatedBars);
        this.subscribe({
            updatedBars
        });
    }
    subscribeForDailyBars(dailyBars) {
        this.session.subscriptions.dailyBars.push(...dailyBars);
        this.subscribe({
            dailyBars
        });
    }
    subscribeForStatuses(statuses) {
        this.session.subscriptions.statuses.push(...statuses);
        this.subscribe({
            statuses
        });
    }
    subscribeForLulds(lulds) {
        this.session.subscriptions.lulds.push(...lulds);
        this.subscribe({
            lulds
        });
    }
    subscribe(symbols) {
        var _a, _b, _c, _d, _e, _f, _g;
        const subMsg = {
            action: "subscribe",
            trades: (_a = symbols.trades) !== null && _a !== void 0 ? _a : [],
            quotes: (_b = symbols.quotes) !== null && _b !== void 0 ? _b : [],
            bars: (_c = symbols.bars) !== null && _c !== void 0 ? _c : [],
            updatedBars: (_d = symbols.updatedBars) !== null && _d !== void 0 ? _d : [],
            dailyBars: (_e = symbols.dailyBars) !== null && _e !== void 0 ? _e : [],
            statuses: (_f = symbols.statuses) !== null && _f !== void 0 ? _f : [],
            lulds: (_g = symbols.lulds) !== null && _g !== void 0 ? _g : []
        };
        this.conn.send(this.msgpack.encode(subMsg));
    }
    subscribeAll() {
        this.subscribe(this.session.subscriptions);
    }
    unsubscribeFromTrades(trades) {
        this.session.subscriptions.trades = this.session.subscriptions.trades.filter((trade)=>!trades.includes(trade));
        this.unsubscribe({
            trades
        });
    }
    unsubscribeFromQuotes(quotes) {
        this.session.subscriptions.quotes = this.session.subscriptions.quotes.filter((quote)=>!quotes.includes(quote));
        this.unsubscribe({
            quotes
        });
    }
    unsubscribeFromBars(bars) {
        this.session.subscriptions.bars = this.session.subscriptions.bars.filter((bar)=>!bars.includes(bar));
        this.unsubscribe({
            bars
        });
    }
    unsubscribeFromUpdatedBars(updatedBars) {
        this.session.subscriptions.updatedBars = this.session.subscriptions.updatedBars.filter((updatedBar)=>!updatedBars.includes(updatedBar));
        this.unsubscribe({
            updatedBars
        });
    }
    unsubscribeFromDailyBars(dailyBars) {
        this.session.subscriptions.dailyBars = this.session.subscriptions.dailyBars.filter((dailyBar)=>!dailyBars.includes(dailyBar));
        this.unsubscribe({
            dailyBars
        });
    }
    unsubscribeFromStatuses(statuses) {
        this.session.subscriptions.statuses = this.session.subscriptions.statuses.filter((status)=>!statuses.includes(status));
        this.unsubscribe({
            statuses
        });
    }
    unsubscribeFromLulds(lulds) {
        this.session.subscriptions.lulds = this.session.subscriptions.lulds.filter((luld)=>!lulds.includes(luld));
        this.unsubscribe({
            lulds
        });
    }
    unsubscribe(symbols) {
        var _a, _b, _c, _d, _e, _f, _g;
        const unsubMsg = {
            action: "unsubscribe",
            trades: (_a = symbols.trades) !== null && _a !== void 0 ? _a : [],
            quotes: (_b = symbols.quotes) !== null && _b !== void 0 ? _b : [],
            bars: (_c = symbols.bars) !== null && _c !== void 0 ? _c : [],
            updatedBars: (_d = symbols.updatedBars) !== null && _d !== void 0 ? _d : [],
            dailyBars: (_e = symbols.dailyBars) !== null && _e !== void 0 ? _e : [],
            statuses: (_f = symbols.statuses) !== null && _f !== void 0 ? _f : [],
            lulds: (_g = symbols.lulds) !== null && _g !== void 0 ? _g : []
        };
        this.conn.send(this.msgpack.encode(unsubMsg));
    }
    updateSubscriptions(msg) {
        this.log(`listening to streams:
        trades: ${msg.trades},
        quotes: ${msg.quotes},
        bars: ${msg.bars},
        updatedBars: ${msg.updatedBars},
        dailyBars: ${msg.dailyBars},
        statuses: ${msg.statuses},
        lulds: ${msg.lulds},
        cancelErrors: ${msg.cancelErrors},
        corrections: ${msg.corrections}`);
        this.session.subscriptions = {
            trades: msg.trades,
            quotes: msg.quotes,
            bars: msg.bars,
            updatedBars: msg.updatedBars,
            dailyBars: msg.dailyBars,
            statuses: msg.statuses,
            lulds: msg.lulds,
            cancelErrors: msg.cancelErrors,
            corrections: msg.corrections
        };
    }
    onStockTrade(fn) {
        this.on(websocket_1.EVENT.TRADES, (trade)=>fn(trade));
    }
    onStockQuote(fn) {
        this.on(websocket_1.EVENT.QUOTES, (quote)=>fn(quote));
    }
    onStockBar(fn) {
        this.on(websocket_1.EVENT.BARS, (bar)=>fn(bar));
    }
    onStockUpdatedBar(fn) {
        this.on(websocket_1.EVENT.UPDATED_BARS, (updatedBar)=>fn(updatedBar));
    }
    onStockDailyBar(fn) {
        this.on(websocket_1.EVENT.DAILY_BARS, (dailyBar)=>fn(dailyBar));
    }
    onStatuses(fn) {
        this.on(websocket_1.EVENT.TRADING_STATUSES, (status)=>fn(status));
    }
    onLulds(fn) {
        this.on(websocket_1.EVENT.LULDS, (luld)=>fn(luld));
    }
    onCancelErrors(fn) {
        this.on(websocket_1.EVENT.CANCEL_ERRORS, (cancelError)=>fn(cancelError));
    }
    onCorrections(fn) {
        this.on(websocket_1.EVENT.CORRECTIONS, (correction)=>fn(correction));
    }
    dataHandler(data) {
        data.forEach((element)=>{
            if ("T" in element) {
                switch(element.T){
                    case "t":
                        this.emit(websocket_1.EVENT.TRADES, (0, entityv2_1.AlpacaTradeV2)(element));
                        break;
                    case "q":
                        this.emit(websocket_1.EVENT.QUOTES, (0, entityv2_1.AlpacaQuoteV2)(element));
                        break;
                    case "b":
                        this.emit(websocket_1.EVENT.BARS, (0, entityv2_1.AlpacaBarV2)(element));
                        break;
                    case "u":
                        this.emit(websocket_1.EVENT.UPDATED_BARS, (0, entityv2_1.AlpacaBarV2)(element));
                        break;
                    case "d":
                        this.emit(websocket_1.EVENT.DAILY_BARS, (0, entityv2_1.AlpacaBarV2)(element));
                        break;
                    case "s":
                        this.emit(websocket_1.EVENT.TRADING_STATUSES, (0, entityv2_1.AlpacaStatusV2)(element));
                        break;
                    case "l":
                        this.emit(websocket_1.EVENT.LULDS, (0, entityv2_1.AlpacaLuldV2)(element));
                        break;
                    case "x":
                        this.emit(websocket_1.EVENT.CANCEL_ERRORS, (0, entityv2_1.AlpacaCancelErrorV2)(element));
                        break;
                    case "c":
                        this.emit(websocket_1.EVENT.CORRECTIONS, (0, entityv2_1.AlpacaCorrectionV2)(element));
                        break;
                    default:
                        this.emit(websocket_1.EVENT.CLIENT_ERROR, websocket_1.ERROR.UNEXPECTED_MESSAGE);
                }
            }
        });
    }
}
exports.AlpacaStocksClient = AlpacaStocksClient;
}),
"[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/datav2/option_websocket_v1beta1.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.AlpacaOptionClient = void 0;
const entityv2_1 = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/datav2/entityv2.js [app-route] (ecmascript)");
const websocket_1 = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/datav2/websocket.js [app-route] (ecmascript)");
class AlpacaOptionClient extends websocket_1.AlpacaWebsocket {
    constructor(options){
        const url = "wss" + options.url.substr(options.url.indexOf(":")) + "/v1beta1/" + options.feed;
        options.url = url;
        options.subscriptions = {
            trades: [],
            quotes: []
        };
        super(options);
    }
    subscribeForTrades(trades) {
        this.session.subscriptions.trades.push(...trades);
        this.subscribe({
            trades
        });
    }
    subscribeForQuotes(quotes) {
        this.session.subscriptions.quotes.push(...quotes);
        this.subscribe({
            quotes
        });
    }
    subscribe(symbols) {
        var _a, _b;
        const subMsg = {
            action: "subscribe",
            trades: (_a = symbols.trades) !== null && _a !== void 0 ? _a : [],
            quotes: (_b = symbols.quotes) !== null && _b !== void 0 ? _b : []
        };
        this.conn.send(this.msgpack.encode(subMsg));
    }
    subscribeAll() {
        this.subscribe(this.session.subscriptions);
    }
    unsubscribeFromTrades(trades) {
        this.session.subscriptions.trades = this.session.subscriptions.trades.filter((trade)=>!trades.includes(trade));
        this.unsubscribe({
            trades
        });
    }
    unsubscribeFromQuotes(quotes) {
        this.session.subscriptions.quotes = this.session.subscriptions.quotes.filter((quote)=>!quotes.includes(quote));
        this.unsubscribe({
            quotes
        });
    }
    unsubscribe(symbols) {
        var _a, _b;
        const unsubMsg = {
            action: "unsubscribe",
            trades: (_a = symbols.trades) !== null && _a !== void 0 ? _a : [],
            quotes: (_b = symbols.quotes) !== null && _b !== void 0 ? _b : []
        };
        this.conn.send(this.msgpack.encode(unsubMsg));
    }
    updateSubscriptions(msg) {
        this.log(`listening to streams:
          trades: ${msg.trades},
          quotes: ${msg.quotes}`);
        this.session.subscriptions = {
            trades: msg.trades,
            quotes: msg.quotes
        };
    }
    onOptionTrade(fn) {
        this.on(websocket_1.EVENT.TRADES, (trade)=>fn(trade));
    }
    onOptionQuote(fn) {
        this.on(websocket_1.EVENT.QUOTES, (quote)=>fn(quote));
    }
    dataHandler(data) {
        data.forEach((element)=>{
            if ("T" in element) {
                switch(element.T){
                    case "t":
                        this.emit(websocket_1.EVENT.TRADES, (0, entityv2_1.AlpacaOptionTradeV1Beta1)(element));
                        break;
                    case "q":
                        this.emit(websocket_1.EVENT.QUOTES, (0, entityv2_1.AlpacaOptionQuoteV1Beta1)(element));
                        break;
                    default:
                        this.emit(websocket_1.EVENT.CLIENT_ERROR, websocket_1.ERROR.UNEXPECTED_MESSAGE);
                }
            }
        });
    }
}
exports.AlpacaOptionClient = AlpacaOptionClient;
}),
"[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/entity.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

let alpaca_quote_mapping = {
    T: "symbol",
    X: "askexchange",
    P: "askprice",
    S: "asksize",
    x: "bidexchange",
    p: "bidprice",
    s: "bidsize",
    c: "conditions",
    t: "timestamp"
};
let alpaca_trade_mapping = {
    T: "symbol",
    i: "tradeID",
    x: "exchange",
    p: "price",
    s: "size",
    t: "timestamp",
    z: "tapeID",
    c: "conditions"
};
// used in websocket with AM.<SYMBOL>
let alpaca_agg_minute_bar_mapping = {
    T: "symbol",
    v: "volume",
    av: "accumulatedVolume",
    op: "officialOpenPrice",
    vw: "vwap",
    o: "openPrice",
    h: "highPrice",
    l: "lowPrice",
    c: "closePrice",
    a: "averagePrice",
    s: "startEpochTime",
    e: "endEpochTime"
};
// used with rest bars endpoint
let alpaca_bar_mapping = {
    t: "startEpochTime",
    o: "openPrice",
    h: "highPrice",
    l: "lowPrice",
    c: "closePrice",
    v: "volume"
};
let polygon_quote_mapping = {
    sym: "symbol",
    ax: "askexchange",
    ap: "askprice",
    as: "asksize",
    bx: "bidexchange",
    bp: "bidprice",
    bs: "bidsize",
    c: "condition",
    t: "timestamp"
};
function AlpacaQuote(data) {
    return convert(data, alpaca_quote_mapping);
}
function AlpacaTrade(data) {
    return convert(data, alpaca_trade_mapping);
}
function AggMinuteBar(data) {
    return convert(data, alpaca_agg_minute_bar_mapping);
}
function Bar(data) {
    return convert(data, alpaca_bar_mapping);
}
function convert(data, mapping) {
    const obj = {};
    for (let [key, value] of Object.entries(data)){
        if (mapping.hasOwnProperty(key)) {
            obj[mapping[key]] = value;
        } else {
            obj[key] = value;
        }
    }
    return obj;
}
module.exports = {
    AlpacaTrade: AlpacaTrade,
    AlpacaQuote: AlpacaQuote,
    AggMinuteBar: AggMinuteBar,
    Bar: Bar
};
}),
"[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/websockets.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
const events = __turbopack_context__.r("[externals]/events [external] (events, cjs)");
const WebSocket = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/node_modules/ws/index.js [app-route] (ecmascript)");
const entity = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/entity.js [app-route] (ecmascript)");
// Listeners
// A client can listen on any of the following events, states, or errors
// Connection states. Each of these will also emit EVENT.STATE_CHANGE
var STATE;
(function(STATE) {
    STATE.AUTHENTICATING = "authenticating";
    STATE.CONNECTED = "connected";
    STATE.CONNECTING = "connecting";
    STATE.DISCONNECTED = "disconnected";
    STATE.WAITING_TO_CONNECT = "waiting to connect";
    STATE.WAITING_TO_RECONNECT = "waiting to reconnect";
})(STATE = exports.STATE || (exports.STATE = {}));
// Client events
var EVENT;
(function(EVENT) {
    EVENT.CLIENT_ERROR = "client_error";
    EVENT.STATE_CHANGE = "state_change";
    EVENT.AUTHORIZED = "authorized";
    EVENT.UNAUTHORIZED = "unauthorized";
    EVENT.ORDER_UPDATE = "trade_updates";
    EVENT.ACCOUNT_UPDATE = "account_updates";
    EVENT.STOCK_TRADES = "stock_trades";
    EVENT.STOCK_QUOTES = "stock_quotes";
    EVENT.STOCK_AGG_SEC = "stock_agg_sec";
    EVENT.STOCK_AGG_MIN = "stock_agg_min";
})(EVENT = exports.EVENT || (exports.EVENT = {}));
// Connection errors Each of these will also emit EVENT.ERROR
var ERROR;
(function(ERROR) {
    ERROR.BAD_KEY_OR_SECRET = "bad key id or secret";
    ERROR.CONNECTION_REFUSED = "connection refused";
    ERROR.MISSING_API_KEY = "missing api key";
    ERROR.MISSING_SECRET_KEY = "missing secret key";
    ERROR.UNKNOWN = "unknown error";
})(ERROR = exports.ERROR || (exports.ERROR = {}));
/**
 * AlpacaStreamClient manages a connection to Alpaca's websocket api
 */ class AlpacaStreamClient extends events.EventEmitter {
    constructor(opts = {}){
        super();
        this.defaultOptions = {
            // A list of subscriptions to subscribe to on connection
            subscriptions: [],
            // Whether the library should reconnect automatically
            reconnect: true,
            // Reconnection backoff: if true, then the reconnection time will be initially
            // reconnectTimeout, then will double with each unsuccessful connection attempt.
            // It will not exceed maxReconnectTimeout
            backoff: true,
            // Initial reconnect timeout (seconds) a minimum of 1 will be used if backoff=false
            reconnectTimeout: 0,
            // The maximum amount of time between reconnect tries (applies to backoff)
            maxReconnectTimeout: 30,
            // The amount of time to increment the delay between each reconnect attempt
            backoffIncrement: 0.5,
            // If true, client outputs detailed log messages
            verbose: false,
            // If true we will use the polygon ws data source, otherwise we use
            // alpaca ws data source
            usePolygon: false
        };
        // Set minimum reconnectTimeout of 1s if backoff=false
        if (!opts.backoff && opts.reconnectTimeout < 1) {
            opts.reconnectTimeout = 1;
        }
        // Merge supplied options with defaults
        this.session = Object.assign(this.defaultOptions, opts);
        this.session.url = this.session.url.replace(/^http/, "ws") + "/stream";
        // Keep track of subscriptions in case we need to reconnect after the client
        // has called subscribe()
        this.subscriptionState = {};
        this.session.subscriptions.forEach((x)=>{
            this.subscriptionState[x] = true;
        });
        this.currentState = STATE.WAITING_TO_CONNECT;
        // Register internal event handlers
        // Log and emit every state change
        Object.keys(STATE).forEach((s)=>{
            this.on(STATE[s], ()=>{
                this.currentState = STATE[s];
                this.log("info", `state change: ${STATE[s]}`);
                this.emit(EVENT.STATE_CHANGE, STATE[s]);
            });
        });
        // Log and emit every error
        Object.keys(ERROR).forEach((e)=>{
            this.on(ERROR[e], ()=>{
                this.log("error", ERROR[e]);
                this.emit(EVENT.CLIENT_ERROR, ERROR[e]);
            });
        });
    }
    connect() {
        // Check the credentials
        if (this.session.apiKey.length === 0 && this.session.oauth.length === 0) {
            throw new Error(ERROR.MISSING_API_KEY);
        }
        if (this.session.secretKey.length === 0 && this.session.oauth.length === 0) {
            throw new Error(ERROR.MISSING_SECRET_KEY);
        }
        // Reset reconnectDisabled since the user called connect() again
        this.reconnectDisabled = false;
        this.emit(STATE.CONNECTING);
        this.conn = new WebSocket(this.session.url);
        this.conn.once("open", ()=>{
            this.authenticate();
        });
        this.conn.on("message", (data)=>this.handleMessage(data));
        this.conn.once("error", (err)=>{
            this.emit(ERROR.CONNECTION_REFUSED);
        });
        this.conn.once("close", ()=>{
            this.emit(STATE.DISCONNECTED);
            if (this.session.reconnect && !this.reconnectDisabled) {
                this.reconnect();
            }
        });
    }
    _ensure_polygon(channels) {
        if (this.polygon.connectCalled) {
            if (channels) {
                this.polygon.subscribe(channels);
            }
            return;
        }
        this.polygon.connect(channels);
    }
    _unsubscribe_polygon(channels) {
        if (this.polygon.connectCalled) {
            if (channels) {
                this.polygon.unsubscribe(channels);
            }
        }
    }
    subscribe(keys) {
        let wsChannels = [];
        let polygonChannels = [];
        keys.forEach((key)=>{
            const poly = [
                "Q.",
                "T.",
                "A.",
                "AM."
            ];
            let found = poly.filter((channel)=>key.startsWith(channel));
            if (found.length > 0) {
                polygonChannels.push(key);
            } else {
                wsChannels.push(key);
            }
        });
        if (wsChannels.length > 0) {
            const subMsg = {
                action: "listen",
                data: {
                    streams: wsChannels
                }
            };
            this.send(JSON.stringify(subMsg));
        }
        if (polygonChannels.length > 0) {
            this._ensure_polygon(polygonChannels);
        }
        keys.forEach((x)=>{
            this.subscriptionState[x] = true;
        });
    }
    unsubscribe(keys) {
        // Currently, only Polygon channels can be unsubscribed from
        let polygonChannels = [];
        keys.forEach((key)=>{
            const poly = [
                "Q.",
                "T.",
                "A.",
                "AM."
            ];
            let found = poly.filter((channel)=>key.startsWith(channel));
            if (found.length > 0) {
                polygonChannels.push(key);
            }
        });
        if (polygonChannels.length > 0) {
            this._unsubscribe_polygon(polygonChannels);
        }
        keys.forEach((x)=>{
            this.subscriptionState[x] = false;
        });
    }
    subscriptions() {
        // if the user unsubscribes from certain equities, they will still be
        // under this.subscriptionState but with value "false", so we need to
        // filter them out
        return Object.keys(this.subscriptionState).filter((x)=>this.subscriptionState[x]);
    }
    onConnect(fn) {
        this.on(STATE.CONNECTED, ()=>fn());
    }
    onDisconnect(fn) {
        this.on(STATE.DISCONNECTED, ()=>fn());
    }
    onStateChange(fn) {
        this.on(EVENT.STATE_CHANGE, (newState)=>fn(newState));
    }
    onError(fn) {
        this.on(EVENT.CLIENT_ERROR, (err)=>fn(err));
    }
    onOrderUpdate(fn) {
        this.on(EVENT.ORDER_UPDATE, (orderUpdate)=>fn(orderUpdate));
    }
    onAccountUpdate(fn) {
        this.on(EVENT.ACCOUNT_UPDATE, (accountUpdate)=>fn(accountUpdate));
    }
    onPolygonConnect(fn) {
        this.polygon.on(STATE.CONNECTED, ()=>fn());
    }
    onPolygonDisconnect(fn) {
        this.polygon.on(STATE.DISCONNECTED, ()=>fn());
    }
    onStockTrades(fn) {
        if (this.session.usePolygon) {
            this.polygon.on(EVENT.STOCK_TRADES, function(subject, data) {
                fn(subject, data);
            });
        } else {
            this.on(EVENT.STOCK_TRADES, function(subject, data) {
                fn(subject, data);
            });
        }
    }
    onStockQuotes(fn) {
        if (this.session.usePolygon) {
            this.polygon.on(EVENT.STOCK_QUOTES, function(subject, data) {
                fn(subject, data);
            });
        } else {
            this.on(EVENT.STOCK_QUOTES, function(subject, data) {
                fn(subject, data);
            });
        }
    }
    onStockAggSec(fn) {
        this.polygon.on(EVENT.STOCK_AGG_SEC, function(subject, data) {
            fn(subject, data);
        });
    }
    onStockAggMin(fn) {
        if (this.session.usePolygon) {
            this.polygon.on(EVENT.STOCK_AGG_MIN, function(subject, data) {
                fn(subject, data);
            });
        } else {
            this.on(EVENT.STOCK_AGG_MIN, function(subject, data) {
                fn(subject, data);
            });
        }
    }
    send(data) {
        this.conn.send(data);
    }
    disconnect() {
        this.reconnectDisabled = true;
        this.conn.close();
        if (this.polygon) {
            this.polygon.close();
        }
    }
    state() {
        return this.currentState;
    }
    get(key) {
        return this.session[key];
    }
    reconnect() {
        setTimeout(()=>{
            if (this.session.backoff) {
                this.session.reconnectTimeout += this.session.backoffIncrement;
                if (this.session.reconnectTimeout > this.session.maxReconnectTimeout) {
                    this.session.reconnectTimeout = this.session.maxReconnectTimeout;
                }
            }
            this.connect();
        }, this.session.reconnectTimeout * 1000);
        this.emit(STATE.WAITING_TO_RECONNECT, this.session.reconnectTimeout);
    }
    authenticate() {
        this.emit(STATE.AUTHENTICATING);
        const authMsg = {
            action: "authenticate",
            data: {
                key_id: this.session.apiKey,
                secret_key: this.session.secretKey
            }
        };
        this.send(JSON.stringify(authMsg));
    }
    handleMessage(data) {
        // Heartbeat
        const bytes = new Uint8Array(data);
        if (bytes.length === 1 && bytes[0] === 1) {
            return;
        }
        let message = JSON.parse(data);
        const subject = message.stream;
        if ("error" in message.data) {
            console.log(message.data.error);
        }
        switch(subject){
            case "authorization":
                this.authResultHandler(message.data.status);
                break;
            case "listening":
                this.log(`listening to the streams: ${message.data.streams}`);
                break;
            case "trade_updates":
                this.emit(EVENT.ORDER_UPDATE, message.data);
                break;
            case "account_updates":
                this.emit(EVENT.ACCOUNT_UPDATE, message.data);
                break;
            default:
                if (message.stream.startsWith("T.")) {
                    this.emit(EVENT.STOCK_TRADES, subject, entity.AlpacaTrade(message.data));
                } else if (message.stream.startsWith("Q.")) {
                    this.emit(EVENT.STOCK_QUOTES, subject, entity.AlpacaQuote(message.data));
                } else if (message.stream.startsWith("AM.")) {
                    this.emit(EVENT.STOCK_AGG_MIN, subject, entity.AggMinuteBar(message.data));
                } else {
                    this.emit(ERROR.PROTOBUF);
                }
        }
    }
    authResultHandler(authResult) {
        switch(authResult){
            case "authorized":
                this.emit(STATE.CONNECTED);
                break;
            case "unauthorized":
                this.emit(ERROR.BAD_KEY_OR_SECRET);
                this.disconnect();
                break;
            default:
                break;
        }
    }
    log(level, ...msg) {
        if (this.session.verbose) {
            console[level](...msg);
        }
    }
}
exports.AlpacaStreamClient = AlpacaStreamClient;
}),
"[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/alpaca-trade-api.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

__turbopack_context__.r("[project]/swing-trader-ai/node_modules/dotenv/lib/main.js [app-route] (ecmascript)").config();
const api = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/api.js [app-route] (ecmascript)");
const account = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/account.js [app-route] (ecmascript)");
const position = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/position.js [app-route] (ecmascript)");
const calendar = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/calendar.js [app-route] (ecmascript)");
const clock = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/clock.js [app-route] (ecmascript)");
const asset = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/asset.js [app-route] (ecmascript)");
const order = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/order.js [app-route] (ecmascript)");
const watchlist = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/watchlist.js [app-route] (ecmascript)");
const dataV2 = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/datav2/rest_v2.js [app-route] (ecmascript)");
const entityV2 = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/datav2/entityv2.js [app-route] (ecmascript)");
const crypto_websocket = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/datav2/crypto_websocket_v1beta3.js [app-route] (ecmascript)");
const news_stream = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/datav2/news_websocket.js [app-route] (ecmascript)");
const websockets_v2 = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/datav2/stock_websocket_v2.js [app-route] (ecmascript)");
const option_stream = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/datav2/option_websocket_v1beta1.js [app-route] (ecmascript)");
const websockets = __turbopack_context__.r("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/resources/websockets.js [app-route] (ecmascript)");
class Alpaca {
    constructor(config = {}){
        // Helper methods
        this.httpRequest = api.httpRequest.bind(this);
        this.dataHttpRequest = api.dataHttpRequest;
        // Account
        this.getAccount = account.get;
        this.updateAccountConfigurations = account.updateConfigs;
        this.getAccountConfigurations = account.getConfigs;
        this.getAccountActivities = account.getActivities;
        this.getPortfolioHistory = account.getPortfolioHistory;
        // Positions
        this.getPositions = position.getAll;
        this.getPosition = position.getOne;
        this.closeAllPositions = position.closeAll;
        this.closePosition = position.closeOne;
        // Calendar
        this.getCalendar = calendar.get;
        // Clock
        this.getClock = clock.get;
        // Asset
        this.getAssets = asset.getAll;
        this.getAsset = asset.getOne;
        // Order
        this.getOrders = order.getAll;
        this.getOrder = order.getOne;
        this.getOrderByClientId = order.getByClientOrderId;
        this.createOrder = order.post;
        this.replaceOrder = order.patchOrder;
        this.cancelOrder = order.cancel;
        this.cancelAllOrders = order.cancelAll;
        // Watchlists
        this.getWatchlists = watchlist.getAll;
        this.getWatchlist = watchlist.getOne;
        this.addWatchlist = watchlist.addWatchlist;
        this.addToWatchlist = watchlist.addToWatchlist;
        this.updateWatchlist = watchlist.updateWatchlist;
        this.deleteWatchlist = watchlist.deleteWatchlist;
        this.deleteFromWatchlist = watchlist.deleteFromWatchlist;
        this.configuration = {
            baseUrl: config.baseUrl || process.env.APCA_API_BASE_URL || (config.paper ? "https://paper-api.alpaca.markets" : "https://api.alpaca.markets"),
            dataBaseUrl: config.dataBaseUrl || process.env.APCA_DATA_BASE_URL || process.env.DATA_PROXY_WS || "https://data.alpaca.markets",
            dataStreamUrl: config.dataStreamUrl || process.env.APCA_API_STREAM_URL || "https://stream.data.alpaca.markets",
            keyId: config.keyId || process.env.APCA_API_KEY_ID || "",
            secretKey: config.secretKey || process.env.APCA_API_SECRET_KEY || "",
            apiVersion: config.apiVersion || process.env.APCA_API_VERSION || "v2",
            oauth: config.oauth || process.env.APCA_API_OAUTH || "",
            feed: config.feed || "iex",
            optionFeed: config.optionFeed || "indicative",
            verbose: config.verbose
        };
        this.data_ws = new websockets.AlpacaStreamClient({
            url: this.configuration.dataBaseUrl,
            apiKey: this.configuration.keyId,
            secretKey: this.configuration.secretKey,
            oauth: this.configuration.oauth
        });
        this.data_ws.STATE = websockets.STATE;
        this.data_ws.EVENT = websockets.EVENT;
        this.data_ws.ERROR = websockets.ERROR;
        this.trade_ws = new websockets.AlpacaStreamClient({
            url: this.configuration.baseUrl,
            apiKey: this.configuration.keyId,
            secretKey: this.configuration.secretKey,
            oauth: this.configuration.oauth
        });
        this.trade_ws.STATE = websockets.STATE;
        this.trade_ws.EVENT = websockets.EVENT;
        this.trade_ws.ERROR = websockets.ERROR;
        this.data_stream_v2 = new websockets_v2.AlpacaStocksClient({
            url: this.configuration.dataStreamUrl,
            feed: this.configuration.feed,
            apiKey: this.configuration.keyId,
            secretKey: this.configuration.secretKey,
            verbose: this.configuration.verbose
        });
        this.adjustment = dataV2.Adjustment;
        this.timeframeUnit = entityV2.TimeFrameUnit;
        this.crypto_stream_v1beta3 = new crypto_websocket.AlpacaCryptoClient({
            url: this.configuration.dataStreamUrl,
            apiKey: this.configuration.keyId,
            secretKey: this.configuration.secretKey,
            verbose: this.configuration.verbose
        });
        this.news_stream = new news_stream.AlpacaNewsCLient({
            url: this.configuration.dataStreamUrl,
            apiKey: this.configuration.keyId,
            secretKey: this.configuration.secretKey,
            verbose: this.configuration.verbose
        });
        this.option_stream = new option_stream.AlpacaOptionClient({
            url: this.configuration.dataStreamUrl,
            feed: this.configuration.optionFeed,
            apiKey: this.configuration.keyId,
            secretKey: this.configuration.secretKey,
            verbose: this.configuration.verbose
        });
    }
    sendRequest(endpoint, queryParams, body, method) {
        return api.sendRequest(this.httpRequest, endpoint, queryParams, body, method);
    }
    //DataV2
    getTradesV2(symbol, options, config = this.configuration) {
        return dataV2.getTrades(symbol, options, config);
    }
    getMultiTradesV2(symbols, options, config = this.configuration) {
        return dataV2.getMultiTrades(symbols, options, config);
    }
    getMultiTradesAsyncV2(symbols, options, config = this.configuration) {
        return dataV2.getMultiTradesAsync(symbols, options, config);
    }
    getQuotesV2(symbol, options, config = this.configuration) {
        return dataV2.getQuotes(symbol, options, config);
    }
    getMultiQuotesV2(symbols, options, config = this.configuration) {
        return dataV2.getMultiQuotes(symbols, options, config);
    }
    getMultiQuotesAsyncV2(symbols, options, config = this.configuration) {
        return dataV2.getMultiQuotesAsync(symbols, options, config);
    }
    getBarsV2(symbol, options, config = this.configuration) {
        return dataV2.getBars(symbol, options, config);
    }
    getMultiBarsV2(symbols, options, config = this.configuration) {
        return dataV2.getMultiBars(symbols, options, config);
    }
    getMultiBarsAsyncV2(symbols, options, config = this.configuration) {
        return dataV2.getMultiBarsAsync(symbols, options, config);
    }
    getLatestTrade(symbol, config = this.configuration) {
        return dataV2.getLatestTrade(symbol, config);
    }
    getLatestTrades(symbols, config = this.configuration) {
        return dataV2.getLatestTrades(symbols, config);
    }
    getLatestQuote(symbol, config = this.configuration) {
        return dataV2.getLatestQuote(symbol, config);
    }
    getLatestQuotes(symbols, config = this.configuration) {
        return dataV2.getLatestQuotes(symbols, config);
    }
    getLatestBar(symbol, config = this.configuration) {
        return dataV2.getLatestBar(symbol, config);
    }
    getLatestBars(symbols, config = this.configuration) {
        return dataV2.getLatestBars(symbols, config);
    }
    getSnapshot(symbol, config = this.configuration) {
        return dataV2.getSnapshot(symbol, config);
    }
    getSnapshots(symbols, config = this.configuration) {
        return dataV2.getSnapshots(symbols, config);
    }
    getCryptoTrades(symbols, options, config = this.configuration) {
        return dataV2.getCryptoTrades(symbols, options, config);
    }
    getCryptoQuotes(symbols, options, config = this.configuration) {
        return dataV2.getCryptoQuotes(symbols, options, config);
    }
    getCryptoBars(symbols, options, config = this.configuration) {
        return dataV2.getCryptoBars(symbols, options, config);
    }
    getLatestCryptoTrades(symbols, config = this.configuration) {
        return dataV2.getLatestCryptoTrades(symbols, config);
    }
    getLatestCryptoQuotes(symbols, config = this.configuration) {
        return dataV2.getLatestCryptoQuotes(symbols, config);
    }
    getLatestCryptoBars(symbols, config = this.configuration) {
        return dataV2.getLatestCryptoBars(symbols, config);
    }
    getCryptoSnapshots(symbols, config = this.configuration) {
        return dataV2.getCryptoSnapshots(symbols, config);
    }
    getCryptoOrderbooks(symbols, config = this.configuration) {
        return dataV2.getLatestCryptoOrderbooks(symbols, config);
    }
    getOptionBars(symbols, options, config = this.configuration) {
        return dataV2.getMultiOptionBars(symbols, options, config);
    }
    getOptionTrades(symbols, options, config = this.configuration) {
        return dataV2.getMultiOptionTrades(symbols, options, config);
    }
    getOptionLatestTrades(symbols, config = this.configuration) {
        return dataV2.getLatestOptionTrades(symbols, config);
    }
    getOptionLatestQuotes(symbols, config = this.configuration) {
        return dataV2.getLatestOptionQuotes(symbols, config);
    }
    getOptionSnapshots(symbols, config = this.configuration) {
        return dataV2.getOptionSnapshots(symbols, config);
    }
    getOptionChain(underlying_symbol, options, config = this.configuration) {
        return dataV2.getOptionChain(underlying_symbol, options, config);
    }
    getCorporateActions(symbols, options, config = this.configuration) {
        return dataV2.getCorporateActions(symbols, options, config);
    }
    getNews(options, config = this.configuration) {
        return dataV2.getNews(options, config);
    }
    newTimeframe(amount, unit) {
        return entityV2.NewTimeframe(amount, unit);
    }
}
module.exports = Alpaca;
}),
"[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/index.js [app-route] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

var __importDefault = /*TURBOPACK member replacement*/ __turbopack_context__.e && /*TURBOPACK member replacement*/ __turbopack_context__.e.__importDefault || function(mod) {
    return mod && mod.__esModule ? mod : {
        "default": mod
    };
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
const alpaca_trade_api_1 = __importDefault(__turbopack_context__.r("[project]/swing-trader-ai/node_modules/@alpacahq/alpaca-trade-api/dist/alpaca-trade-api.js [app-route] (ecmascript)"));
exports.default = alpaca_trade_api_1.default;
module.exports = alpaca_trade_api_1.default;
}),
];

//# sourceMappingURL=6bf44_%40alpacahq_alpaca-trade-api_dist_c2d4f232._.js.map