(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,71710,e=>{"use strict";e.s(["default",()=>t7],71710);var t,s,a=e.i(33606),r=e.i(82568);let l=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},n=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)((e,t)=>{let{color:s="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:c,className:o="",children:d,iconNode:m,...x}=e;return(0,r.createElement)("svg",{ref:t,...i,width:a,height:a,stroke:s,strokeWidth:c?24*Number(l)/Number(a):l,className:n("lucide",o),...!d&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(x)&&{"aria-hidden":"true"},...x},[...m.map(e=>{let[t,s]=e;return(0,r.createElement)(t,s)}),...Array.isArray(d)?d:[d]])}),o=(e,t)=>{let s=(0,r.forwardRef)((s,a)=>{let{className:i,...o}=s;return(0,r.createElement)(c,{ref:a,iconNode:t,className:n("lucide-".concat(l(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),i),...o})});return s.displayName=l(e),s},d=o("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]),m=o("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),x=o("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),h=o("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),u=o("brain",[["path",{d:"M12 18V5",key:"adv99a"}],["path",{d:"M15 13a4.17 4.17 0 0 1-3-4 4.17 4.17 0 0 1-3 4",key:"1e3is1"}],["path",{d:"M17.598 6.5A3 3 0 1 0 12 5a3 3 0 1 0-5.598 1.5",key:"1gqd8o"}],["path",{d:"M17.997 5.125a4 4 0 0 1 2.526 5.77",key:"iwvgf7"}],["path",{d:"M18 18a4 4 0 0 0 2-7.464",key:"efp6ie"}],["path",{d:"M19.967 17.483A4 4 0 1 1 12 18a4 4 0 1 1-7.967-.517",key:"1gq6am"}],["path",{d:"M6 18a4 4 0 0 1-2-7.464",key:"k1g0md"}],["path",{d:"M6.003 5.125a4 4 0 0 0-2.526 5.77",key:"q97ue3"}]]),p=o("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),f=o("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]),g=o("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),b=o("scan",[["path",{d:"M3 7V5a2 2 0 0 1 2-2h2",key:"aa7l1z"}],["path",{d:"M17 3h2a2 2 0 0 1 2 2v2",key:"4qcy5o"}],["path",{d:"M21 17v2a2 2 0 0 1-2 2h-2",key:"6vwrx8"}],["path",{d:"M7 21H5a2 2 0 0 1-2-2v-2",key:"ioqczr"}]]);function j(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function v(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return e=>{let s=!1,a=t.map(t=>{let a=j(t,e);return s||"function"!=typeof a||(s=!0),a});if(s)return()=>{for(let e=0;e<a.length;e++){let s=a[e];"function"==typeof s?s():j(t[e],null)}}}}function y(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return r.useCallback(v(...t),t)}function N(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:s,...a}=e;if(r.isValidElement(s)){var l,n,i;let e,c,o=(c=(e=null==(n=Object.getOwnPropertyDescriptor((l=s).props,"ref"))?void 0:n.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(c=(e=null==(i=Object.getOwnPropertyDescriptor(l,"ref"))?void 0:i.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref,d=function(e,t){let s={...t};for(let a in t){let r=e[a],l=t[a];/^on[A-Z]/.test(a)?r&&l?s[a]=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];let a=l(...t);return r(...t),a}:r&&(s[a]=r):"style"===a?s[a]={...r,...l}:"className"===a&&(s[a]=[r,l].filter(Boolean).join(" "))}return{...e,...s}}(a,s.props);return s.type!==r.Fragment&&(d.ref=t?v(t,o):o),r.cloneElement(s,d)}return r.Children.count(s)>1?r.Children.only(null):null});return t.displayName="".concat(e,".SlotClone"),t}(e),s=r.forwardRef((e,s)=>{let{children:l,...n}=e,i=r.Children.toArray(l),c=i.find(S);if(c){let e=c.props.children,l=i.map(t=>t!==c?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...n,ref:s,children:r.isValidElement(e)?r.cloneElement(e,void 0,l):null})}return(0,a.jsx)(t,{...n,ref:s,children:l})});return s.displayName="".concat(e,".Slot"),s}var w=N("Slot"),k=Symbol("radix.slottable");function S(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===k}function A(){for(var e,t,s=0,a="",r=arguments.length;s<r;s++)(e=arguments[s])&&(t=function e(t){var s,a,r="";if("string"==typeof t||"number"==typeof t)r+=t;else if("object"==typeof t)if(Array.isArray(t)){var l=t.length;for(s=0;s<l;s++)t[s]&&(a=e(t[s]))&&(r&&(r+=" "),r+=a)}else for(a in t)t[a]&&(r&&(r+=" "),r+=a);return r}(e))&&(a&&(a+=" "),a+=t);return a}let C=e=>"boolean"==typeof e?"".concat(e):0===e?"0":e,P=(e,t)=>s=>{var a;if((null==t?void 0:t.variants)==null)return A(e,null==s?void 0:s.class,null==s?void 0:s.className);let{variants:r,defaultVariants:l}=t,n=Object.keys(r).map(e=>{let t=null==s?void 0:s[e],a=null==l?void 0:l[e];if(null===t)return null;let n=C(t)||C(a);return r[e][n]}),i=s&&Object.entries(s).reduce((e,t)=>{let[s,a]=t;return void 0===a||(e[s]=a),e},{});return A(e,n,null==t||null==(a=t.compoundVariants)?void 0:a.reduce((e,t)=>{let{class:s,className:a,...r}=t;return Object.entries(r).every(e=>{let[t,s]=e;return Array.isArray(s)?s.includes({...l,...i}[t]):({...l,...i})[t]===s})?[...e,s,a]:e},[]),null==s?void 0:s.class,null==s?void 0:s.className)},M=(e,t)=>{var s;if(0===e.length)return t.classGroupId;let a=e[0],r=t.nextPart.get(a),l=r?M(e.slice(1),r):void 0;if(l)return l;if(0===t.validators.length)return;let n=e.join("-");return null==(s=t.validators.find(e=>{let{validator:t}=e;return t(n)}))?void 0:s.classGroupId},T=/^\[(.+)\]$/,E=(e,t,s,a)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:z(t,e)).classGroupId=s;return}if("function"==typeof e)return R(e)?void E(e(a),t,s,a):void t.validators.push({validator:e,classGroupId:s});Object.entries(e).forEach(e=>{let[r,l]=e;E(l,z(t,r),s,a)})})},z=(e,t)=>{let s=e;return t.split("-").forEach(e=>{s.nextPart.has(e)||s.nextPart.set(e,{nextPart:new Map,validators:[]}),s=s.nextPart.get(e)}),s},R=e=>e.isThemeGetter,I=/\s+/;function _(){let e,t,s=0,a="";for(;s<arguments.length;)(e=arguments[s++])&&(t=F(e))&&(a&&(a+=" "),a+=t);return a}let F=e=>{let t;if("string"==typeof e)return e;let s="";for(let a=0;a<e.length;a++)e[a]&&(t=F(e[a]))&&(s&&(s+=" "),s+=t);return s},O=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},L=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,D=/^\((?:(\w[\w-]*):)?(.+)\)$/i,G=/^\d+\/\d+$/,U=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,B=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,V=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,q=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,H=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,W=e=>G.test(e),$=e=>!!e&&!Number.isNaN(Number(e)),K=e=>!!e&&Number.isInteger(Number(e)),Q=e=>e.endsWith("%")&&$(e.slice(0,-1)),Y=e=>U.test(e),J=()=>!0,Z=e=>B.test(e)&&!V.test(e),X=()=>!1,ee=e=>q.test(e),et=e=>H.test(e),es=e=>!er(e)&&!ed(e),ea=e=>eg(e,ey,X),er=e=>L.test(e),el=e=>eg(e,eN,Z),en=e=>eg(e,ew,$),ei=e=>eg(e,ej,X),ec=e=>eg(e,ev,et),eo=e=>eg(e,eS,ee),ed=e=>D.test(e),em=e=>eb(e,eN),ex=e=>eb(e,ek),eh=e=>eb(e,ej),eu=e=>eb(e,ey),ep=e=>eb(e,ev),ef=e=>eb(e,eS,!0),eg=(e,t,s)=>{let a=L.exec(e);return!!a&&(a[1]?t(a[1]):s(a[2]))},eb=function(e,t){let s=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=D.exec(e);return!!a&&(a[1]?t(a[1]):s)},ej=e=>"position"===e||"percentage"===e,ev=e=>"image"===e||"url"===e,ey=e=>"length"===e||"size"===e||"bg-size"===e,eN=e=>"length"===e,ew=e=>"number"===e,ek=e=>"family-name"===e,eS=e=>"shadow"===e;Symbol.toStringTag;let eA=function(e){let t,s,a;for(var r=arguments.length,l=Array(r>1?r-1:0),n=1;n<r;n++)l[n-1]=arguments[n];let i=function(r){let n;return s=(t={cache:(e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,s=new Map,a=new Map,r=(r,l)=>{s.set(r,l),++t>e&&(t=0,a=s,s=new Map)};return{get(e){let t=s.get(e);return void 0!==t?t:void 0!==(t=a.get(e))?(r(e,t),t):void 0},set(e,t){s.has(e)?s.set(e,t):r(e,t)}}})((n=l.reduce((e,t)=>t(e),e())).cacheSize),parseClassName:(e=>{let{prefix:t,experimentalParseClassName:s}=e,a=e=>{let t,s,a=[],r=0,l=0,n=0;for(let s=0;s<e.length;s++){let i=e[s];if(0===r&&0===l){if(":"===i){a.push(e.slice(n,s)),n=s+1;continue}if("/"===i){t=s;continue}}"["===i?r++:"]"===i?r--:"("===i?l++:")"===i&&l--}let i=0===a.length?e:e.substring(n),c=(s=i).endsWith("!")?s.substring(0,s.length-1):s.startsWith("!")?s.substring(1):s;return{modifiers:a,hasImportantModifier:c!==i,baseClassName:c,maybePostfixModifierPosition:t&&t>n?t-n:void 0}};if(t){let e=t+":",s=a;a=t=>t.startsWith(e)?s(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(s){let e=a;a=t=>s({className:t,parseClassName:e})}return a})(n),sortModifiers:(e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let s=[],a=[];return e.forEach(e=>{"["===e[0]||t[e]?(s.push(...a.sort(),e),a=[]):a.push(e)}),s.push(...a.sort()),s}})(n),...(e=>{let t=(e=>{let{theme:t,classGroups:s}=e,a={nextPart:new Map,validators:[]};for(let e in s)E(s[e],a,e,t);return a})(e),{conflictingClassGroups:s,conflictingClassGroupModifiers:a}=e;return{getClassGroupId:e=>{let s=e.split("-");return""===s[0]&&1!==s.length&&s.shift(),M(s,t)||(e=>{if(T.test(e)){let t=T.exec(e)[1],s=null==t?void 0:t.substring(0,t.indexOf(":"));if(s)return"arbitrary.."+s}})(e)},getConflictingClassGroupIds:(e,t)=>{let r=s[e]||[];return t&&a[e]?[...r,...a[e]]:r}}})(n)}).cache.get,a=t.cache.set,i=c,c(r)};function c(e){let r=s(e);if(r)return r;let l=((e,t)=>{let{parseClassName:s,getClassGroupId:a,getConflictingClassGroupIds:r,sortModifiers:l}=t,n=[],i=e.trim().split(I),c="";for(let e=i.length-1;e>=0;e-=1){let t=i[e],{isExternal:o,modifiers:d,hasImportantModifier:m,baseClassName:x,maybePostfixModifierPosition:h}=s(t);if(o){c=t+(c.length>0?" "+c:c);continue}let u=!!h,p=a(u?x.substring(0,h):x);if(!p){if(!u||!(p=a(x))){c=t+(c.length>0?" "+c:c);continue}u=!1}let f=l(d).join(":"),g=m?f+"!":f,b=g+p;if(n.includes(b))continue;n.push(b);let j=r(p,u);for(let e=0;e<j.length;++e){let t=j[e];n.push(g+t)}c=t+(c.length>0?" "+c:c)}return c})(e,t);return a(e,l),l}return function(){return i(_.apply(null,arguments))}}(()=>{let e=O("color"),t=O("font"),s=O("text"),a=O("font-weight"),r=O("tracking"),l=O("leading"),n=O("breakpoint"),i=O("container"),c=O("spacing"),o=O("radius"),d=O("shadow"),m=O("inset-shadow"),x=O("text-shadow"),h=O("drop-shadow"),u=O("blur"),p=O("perspective"),f=O("aspect"),g=O("ease"),b=O("animate"),j=()=>["auto","avoid","all","avoid-page","page","left","right","column"],v=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],y=()=>[...v(),ed,er],N=()=>["auto","hidden","clip","visible","scroll"],w=()=>["auto","contain","none"],k=()=>[ed,er,c],S=()=>[W,"full","auto",...k()],A=()=>[K,"none","subgrid",ed,er],C=()=>["auto",{span:["full",K,ed,er]},K,ed,er],P=()=>[K,"auto",ed,er],M=()=>["auto","min","max","fr",ed,er],T=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],E=()=>["start","end","center","stretch","center-safe","end-safe"],z=()=>["auto",...k()],R=()=>[W,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...k()],I=()=>[e,ed,er],_=()=>[...v(),eh,ei,{position:[ed,er]}],F=()=>["no-repeat",{repeat:["","x","y","space","round"]}],L=()=>["auto","cover","contain",eu,ea,{size:[ed,er]}],D=()=>[Q,em,el],G=()=>["","none","full",o,ed,er],U=()=>["",$,em,el],B=()=>["solid","dashed","dotted","double"],V=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],q=()=>[$,Q,eh,ei],H=()=>["","none",u,ed,er],Z=()=>["none",$,ed,er],X=()=>["none",$,ed,er],ee=()=>[$,ed,er],et=()=>[W,"full",...k()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[Y],breakpoint:[Y],color:[J],container:[Y],"drop-shadow":[Y],ease:["in","out","in-out"],font:[es],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[Y],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[Y],shadow:[Y],spacing:["px",$],text:[Y],"text-shadow":[Y],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",W,er,ed,f]}],container:["container"],columns:[{columns:[$,er,ed,i]}],"break-after":[{"break-after":j()}],"break-before":[{"break-before":j()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:y()}],overflow:[{overflow:N()}],"overflow-x":[{"overflow-x":N()}],"overflow-y":[{"overflow-y":N()}],overscroll:[{overscroll:w()}],"overscroll-x":[{"overscroll-x":w()}],"overscroll-y":[{"overscroll-y":w()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:S()}],"inset-x":[{"inset-x":S()}],"inset-y":[{"inset-y":S()}],start:[{start:S()}],end:[{end:S()}],top:[{top:S()}],right:[{right:S()}],bottom:[{bottom:S()}],left:[{left:S()}],visibility:["visible","invisible","collapse"],z:[{z:[K,"auto",ed,er]}],basis:[{basis:[W,"full","auto",i,...k()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[$,W,"auto","initial","none",er]}],grow:[{grow:["",$,ed,er]}],shrink:[{shrink:["",$,ed,er]}],order:[{order:[K,"first","last","none",ed,er]}],"grid-cols":[{"grid-cols":A()}],"col-start-end":[{col:C()}],"col-start":[{"col-start":P()}],"col-end":[{"col-end":P()}],"grid-rows":[{"grid-rows":A()}],"row-start-end":[{row:C()}],"row-start":[{"row-start":P()}],"row-end":[{"row-end":P()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":M()}],"auto-rows":[{"auto-rows":M()}],gap:[{gap:k()}],"gap-x":[{"gap-x":k()}],"gap-y":[{"gap-y":k()}],"justify-content":[{justify:[...T(),"normal"]}],"justify-items":[{"justify-items":[...E(),"normal"]}],"justify-self":[{"justify-self":["auto",...E()]}],"align-content":[{content:["normal",...T()]}],"align-items":[{items:[...E(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...E(),{baseline:["","last"]}]}],"place-content":[{"place-content":T()}],"place-items":[{"place-items":[...E(),"baseline"]}],"place-self":[{"place-self":["auto",...E()]}],p:[{p:k()}],px:[{px:k()}],py:[{py:k()}],ps:[{ps:k()}],pe:[{pe:k()}],pt:[{pt:k()}],pr:[{pr:k()}],pb:[{pb:k()}],pl:[{pl:k()}],m:[{m:z()}],mx:[{mx:z()}],my:[{my:z()}],ms:[{ms:z()}],me:[{me:z()}],mt:[{mt:z()}],mr:[{mr:z()}],mb:[{mb:z()}],ml:[{ml:z()}],"space-x":[{"space-x":k()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":k()}],"space-y-reverse":["space-y-reverse"],size:[{size:R()}],w:[{w:[i,"screen",...R()]}],"min-w":[{"min-w":[i,"screen","none",...R()]}],"max-w":[{"max-w":[i,"screen","none","prose",{screen:[n]},...R()]}],h:[{h:["screen","lh",...R()]}],"min-h":[{"min-h":["screen","lh","none",...R()]}],"max-h":[{"max-h":["screen","lh",...R()]}],"font-size":[{text:["base",s,em,el]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[a,ed,en]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Q,er]}],"font-family":[{font:[ex,er,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[r,ed,er]}],"line-clamp":[{"line-clamp":[$,"none",ed,en]}],leading:[{leading:[l,...k()]}],"list-image":[{"list-image":["none",ed,er]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",ed,er]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:I()}],"text-color":[{text:I()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...B(),"wavy"]}],"text-decoration-thickness":[{decoration:[$,"from-font","auto",ed,el]}],"text-decoration-color":[{decoration:I()}],"underline-offset":[{"underline-offset":[$,"auto",ed,er]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:k()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",ed,er]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",ed,er]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:_()}],"bg-repeat":[{bg:F()}],"bg-size":[{bg:L()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},K,ed,er],radial:["",ed,er],conic:[K,ed,er]},ep,ec]}],"bg-color":[{bg:I()}],"gradient-from-pos":[{from:D()}],"gradient-via-pos":[{via:D()}],"gradient-to-pos":[{to:D()}],"gradient-from":[{from:I()}],"gradient-via":[{via:I()}],"gradient-to":[{to:I()}],rounded:[{rounded:G()}],"rounded-s":[{"rounded-s":G()}],"rounded-e":[{"rounded-e":G()}],"rounded-t":[{"rounded-t":G()}],"rounded-r":[{"rounded-r":G()}],"rounded-b":[{"rounded-b":G()}],"rounded-l":[{"rounded-l":G()}],"rounded-ss":[{"rounded-ss":G()}],"rounded-se":[{"rounded-se":G()}],"rounded-ee":[{"rounded-ee":G()}],"rounded-es":[{"rounded-es":G()}],"rounded-tl":[{"rounded-tl":G()}],"rounded-tr":[{"rounded-tr":G()}],"rounded-br":[{"rounded-br":G()}],"rounded-bl":[{"rounded-bl":G()}],"border-w":[{border:U()}],"border-w-x":[{"border-x":U()}],"border-w-y":[{"border-y":U()}],"border-w-s":[{"border-s":U()}],"border-w-e":[{"border-e":U()}],"border-w-t":[{"border-t":U()}],"border-w-r":[{"border-r":U()}],"border-w-b":[{"border-b":U()}],"border-w-l":[{"border-l":U()}],"divide-x":[{"divide-x":U()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":U()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...B(),"hidden","none"]}],"divide-style":[{divide:[...B(),"hidden","none"]}],"border-color":[{border:I()}],"border-color-x":[{"border-x":I()}],"border-color-y":[{"border-y":I()}],"border-color-s":[{"border-s":I()}],"border-color-e":[{"border-e":I()}],"border-color-t":[{"border-t":I()}],"border-color-r":[{"border-r":I()}],"border-color-b":[{"border-b":I()}],"border-color-l":[{"border-l":I()}],"divide-color":[{divide:I()}],"outline-style":[{outline:[...B(),"none","hidden"]}],"outline-offset":[{"outline-offset":[$,ed,er]}],"outline-w":[{outline:["",$,em,el]}],"outline-color":[{outline:I()}],shadow:[{shadow:["","none",d,ef,eo]}],"shadow-color":[{shadow:I()}],"inset-shadow":[{"inset-shadow":["none",m,ef,eo]}],"inset-shadow-color":[{"inset-shadow":I()}],"ring-w":[{ring:U()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:I()}],"ring-offset-w":[{"ring-offset":[$,el]}],"ring-offset-color":[{"ring-offset":I()}],"inset-ring-w":[{"inset-ring":U()}],"inset-ring-color":[{"inset-ring":I()}],"text-shadow":[{"text-shadow":["none",x,ef,eo]}],"text-shadow-color":[{"text-shadow":I()}],opacity:[{opacity:[$,ed,er]}],"mix-blend":[{"mix-blend":[...V(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":V()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[$]}],"mask-image-linear-from-pos":[{"mask-linear-from":q()}],"mask-image-linear-to-pos":[{"mask-linear-to":q()}],"mask-image-linear-from-color":[{"mask-linear-from":I()}],"mask-image-linear-to-color":[{"mask-linear-to":I()}],"mask-image-t-from-pos":[{"mask-t-from":q()}],"mask-image-t-to-pos":[{"mask-t-to":q()}],"mask-image-t-from-color":[{"mask-t-from":I()}],"mask-image-t-to-color":[{"mask-t-to":I()}],"mask-image-r-from-pos":[{"mask-r-from":q()}],"mask-image-r-to-pos":[{"mask-r-to":q()}],"mask-image-r-from-color":[{"mask-r-from":I()}],"mask-image-r-to-color":[{"mask-r-to":I()}],"mask-image-b-from-pos":[{"mask-b-from":q()}],"mask-image-b-to-pos":[{"mask-b-to":q()}],"mask-image-b-from-color":[{"mask-b-from":I()}],"mask-image-b-to-color":[{"mask-b-to":I()}],"mask-image-l-from-pos":[{"mask-l-from":q()}],"mask-image-l-to-pos":[{"mask-l-to":q()}],"mask-image-l-from-color":[{"mask-l-from":I()}],"mask-image-l-to-color":[{"mask-l-to":I()}],"mask-image-x-from-pos":[{"mask-x-from":q()}],"mask-image-x-to-pos":[{"mask-x-to":q()}],"mask-image-x-from-color":[{"mask-x-from":I()}],"mask-image-x-to-color":[{"mask-x-to":I()}],"mask-image-y-from-pos":[{"mask-y-from":q()}],"mask-image-y-to-pos":[{"mask-y-to":q()}],"mask-image-y-from-color":[{"mask-y-from":I()}],"mask-image-y-to-color":[{"mask-y-to":I()}],"mask-image-radial":[{"mask-radial":[ed,er]}],"mask-image-radial-from-pos":[{"mask-radial-from":q()}],"mask-image-radial-to-pos":[{"mask-radial-to":q()}],"mask-image-radial-from-color":[{"mask-radial-from":I()}],"mask-image-radial-to-color":[{"mask-radial-to":I()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":v()}],"mask-image-conic-pos":[{"mask-conic":[$]}],"mask-image-conic-from-pos":[{"mask-conic-from":q()}],"mask-image-conic-to-pos":[{"mask-conic-to":q()}],"mask-image-conic-from-color":[{"mask-conic-from":I()}],"mask-image-conic-to-color":[{"mask-conic-to":I()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:_()}],"mask-repeat":[{mask:F()}],"mask-size":[{mask:L()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",ed,er]}],filter:[{filter:["","none",ed,er]}],blur:[{blur:H()}],brightness:[{brightness:[$,ed,er]}],contrast:[{contrast:[$,ed,er]}],"drop-shadow":[{"drop-shadow":["","none",h,ef,eo]}],"drop-shadow-color":[{"drop-shadow":I()}],grayscale:[{grayscale:["",$,ed,er]}],"hue-rotate":[{"hue-rotate":[$,ed,er]}],invert:[{invert:["",$,ed,er]}],saturate:[{saturate:[$,ed,er]}],sepia:[{sepia:["",$,ed,er]}],"backdrop-filter":[{"backdrop-filter":["","none",ed,er]}],"backdrop-blur":[{"backdrop-blur":H()}],"backdrop-brightness":[{"backdrop-brightness":[$,ed,er]}],"backdrop-contrast":[{"backdrop-contrast":[$,ed,er]}],"backdrop-grayscale":[{"backdrop-grayscale":["",$,ed,er]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[$,ed,er]}],"backdrop-invert":[{"backdrop-invert":["",$,ed,er]}],"backdrop-opacity":[{"backdrop-opacity":[$,ed,er]}],"backdrop-saturate":[{"backdrop-saturate":[$,ed,er]}],"backdrop-sepia":[{"backdrop-sepia":["",$,ed,er]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":k()}],"border-spacing-x":[{"border-spacing-x":k()}],"border-spacing-y":[{"border-spacing-y":k()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",ed,er]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[$,"initial",ed,er]}],ease:[{ease:["linear","initial",g,ed,er]}],delay:[{delay:[$,ed,er]}],animate:[{animate:["none",b,ed,er]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[p,ed,er]}],"perspective-origin":[{"perspective-origin":y()}],rotate:[{rotate:Z()}],"rotate-x":[{"rotate-x":Z()}],"rotate-y":[{"rotate-y":Z()}],"rotate-z":[{"rotate-z":Z()}],scale:[{scale:X()}],"scale-x":[{"scale-x":X()}],"scale-y":[{"scale-y":X()}],"scale-z":[{"scale-z":X()}],"scale-3d":["scale-3d"],skew:[{skew:ee()}],"skew-x":[{"skew-x":ee()}],"skew-y":[{"skew-y":ee()}],transform:[{transform:[ed,er,"","none","gpu","cpu"]}],"transform-origin":[{origin:y()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:et()}],"translate-x":[{"translate-x":et()}],"translate-y":[{"translate-y":et()}],"translate-z":[{"translate-z":et()}],"translate-none":["translate-none"],accent:[{accent:I()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:I()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",ed,er]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":k()}],"scroll-mx":[{"scroll-mx":k()}],"scroll-my":[{"scroll-my":k()}],"scroll-ms":[{"scroll-ms":k()}],"scroll-me":[{"scroll-me":k()}],"scroll-mt":[{"scroll-mt":k()}],"scroll-mr":[{"scroll-mr":k()}],"scroll-mb":[{"scroll-mb":k()}],"scroll-ml":[{"scroll-ml":k()}],"scroll-p":[{"scroll-p":k()}],"scroll-px":[{"scroll-px":k()}],"scroll-py":[{"scroll-py":k()}],"scroll-ps":[{"scroll-ps":k()}],"scroll-pe":[{"scroll-pe":k()}],"scroll-pt":[{"scroll-pt":k()}],"scroll-pr":[{"scroll-pr":k()}],"scroll-pb":[{"scroll-pb":k()}],"scroll-pl":[{"scroll-pl":k()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",ed,er]}],fill:[{fill:["none",...I()]}],"stroke-w":[{stroke:[$,em,el,en]}],stroke:[{stroke:["none",...I()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function eC(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return eA(A(t))}function eP(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e)}function eM(e){return new Intl.NumberFormat("en-US",{style:"percent",minimumFractionDigits:2,maximumFractionDigits:2}).format(e/100)}let eT=P("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),eE=r.forwardRef((e,t)=>{let{className:s,variant:r,size:l,asChild:n=!1,...i}=e;return(0,a.jsx)(n?w:"button",{className:eC(eT({variant:r,size:l,className:s})),ref:t,...i})});eE.displayName="Button";let ez=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:eC("rounded-lg border bg-card text-card-foreground shadow-sm",s),...r})});ez.displayName="Card";let eR=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:eC("flex flex-col space-y-1.5 p-6",s),...r})});eR.displayName="CardHeader";let eI=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("h3",{ref:t,className:eC("text-2xl font-semibold leading-none tracking-tight",s),...r})});eI.displayName="CardTitle";let e_=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("p",{ref:t,className:eC("text-sm text-muted-foreground",s),...r})});e_.displayName="CardDescription";let eF=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:eC("p-6 pt-0",s),...r})});eF.displayName="CardContent",r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:eC("flex items-center p-6 pt-0",s),...r})}).displayName="CardFooter";let eO=P("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function eL(e){let{className:t,variant:s,...r}=e;return(0,a.jsx)("div",{className:eC(eO({variant:s}),t),...r})}let eD=o("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]]),eG=o("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);function eU(e){let{autoScan:t=!1}=e,[s,l]=(0,r.useState)(!1),[n,i]=(0,r.useState)(null),[c,o]=(0,r.useState)("quick"),[m,x]=(0,r.useState)("Technology"),[h,u]=(0,r.useState)(null);(0,r.useEffect)(()=>{t&&p()},[t]);let p=async()=>{l(!0),u(null),i(null);try{let e=await fetch("/api/scanner/quick?limit=15");if(!e.ok)throw Error("Failed to fetch scan results");let t=await e.json(),s={totalScanned:t.totalScanned,successfulScans:t.results.length,failedScans:t.totalScanned-t.results.length,topOpportunities:t.results,sectorBreakdown:{},scanDuration:0};i(s)}catch(e){u("Failed to perform quick scan. Please try again."),console.error("Quick scan error:",e)}finally{l(!1)}},b=async()=>{l(!0),u(null),i(null);try{let e=await fetch("/api/scanner/full?limit=25&concurrent=3");if(!e.ok)throw Error("Failed to fetch scan results");let t=await e.json();i(t)}catch(e){u("Failed to perform full scan. Please try again."),console.error("Full scan error:",e)}finally{l(!1)}},j=async()=>{l(!0),u(null),i(null);try{let e=await fetch("/api/scanner/sector/".concat(encodeURIComponent(m),"?limit=15"));if(!e.ok)throw Error("Failed to fetch scan results");let t=await e.json();i(t)}catch(e){u("Failed to perform sector scan. Please try again."),console.error("Sector scan error:",e)}finally{l(!1)}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(ez,{className:"bg-slate-800/50 border-slate-700",children:[(0,a.jsxs)(eR,{children:[(0,a.jsxs)(eI,{className:"text-white flex items-center",children:[(0,a.jsx)(f,{className:"mr-2 h-5 w-5 text-blue-400"}),"Swing Trading Scanner"]}),(0,a.jsx)(e_,{className:"text-slate-300",children:"Automatically scan stocks for the best swing trading opportunities"})]}),(0,a.jsxs)(eF,{children:[(0,a.jsxs)("div",{className:"flex flex-wrap gap-4 mb-4",children:[(0,a.jsxs)(eE,{onClick:p,disabled:s,className:"bg-blue-600 hover:bg-blue-700",children:[s&&"quick"===c?(0,a.jsx)(g,{className:"mr-2 h-4 w-4 animate-spin"}):null,"Quick Scan (Top 16)"]}),(0,a.jsxs)(eE,{onClick:b,disabled:s,variant:"outline",className:"border-slate-600 text-slate-300 hover:bg-slate-800",children:[s&&"full"===c?(0,a.jsx)(g,{className:"mr-2 h-4 w-4 animate-spin"}):null,"Full Scan (All 70+ Stocks)"]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("select",{value:m,onChange:e=>x(e.target.value),className:"px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm",disabled:s,children:["Technology","Financial Services","Healthcare","Industrial","Materials","Consumer","Communication Services","Energy"].map(e=>(0,a.jsx)("option",{value:e,children:e},e))}),(0,a.jsxs)(eE,{onClick:j,disabled:s,variant:"outline",className:"border-slate-600 text-slate-300 hover:bg-slate-800",children:[s&&"sector"===c?(0,a.jsx)(g,{className:"mr-2 h-4 w-4 animate-spin"}):null,"Scan Sector"]})]})]}),s&&(0,a.jsxs)("div",{className:"text-center py-4",children:[(0,a.jsx)(g,{className:"mx-auto h-8 w-8 animate-spin text-blue-400"}),(0,a.jsx)("p",{className:"text-slate-300 mt-2",children:"Scanning stocks for swing trading opportunities..."})]})]})]}),h&&(0,a.jsx)(ez,{className:"bg-red-900/20 border-red-500/50",children:(0,a.jsx)(eF,{className:"p-6",children:(0,a.jsx)("p",{className:"text-red-300 text-center",children:h})})}),n&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(ez,{className:"bg-slate-800/50 border-slate-700",children:[(0,a.jsx)(eR,{children:(0,a.jsx)(eI,{className:"text-white",children:"Scan Summary"})}),(0,a.jsx)(eF,{children:(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-white",children:n.totalScanned}),(0,a.jsx)("div",{className:"text-sm text-slate-300",children:"Total Scanned"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-400",children:n.successfulScans}),(0,a.jsx)("div",{className:"text-sm text-slate-300",children:"Successful"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-400",children:n.failedScans}),(0,a.jsx)("div",{className:"text-sm text-slate-300",children:"Failed"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-400",children:n.scanDuration?"".concat((n.scanDuration/1e3).toFixed(1),"s"):"N/A"}),(0,a.jsx)("div",{className:"text-sm text-slate-300",children:"Duration"})]})]})})]}),(0,a.jsxs)(ez,{className:"bg-slate-800/50 border-slate-700",children:[(0,a.jsx)(eR,{children:(0,a.jsxs)(eI,{className:"text-white flex items-center",children:[(0,a.jsx)(d,{className:"mr-2 h-5 w-5 text-green-400"}),"Top Swing Trading Opportunities"]})}),(0,a.jsx)(eF,{children:(0,a.jsx)("div",{className:"space-y-4",children:n.topOpportunities.map((e,t)=>{var s;return(0,a.jsxs)("div",{className:"p-4 bg-slate-700/50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"text-lg font-bold text-white",children:["#",e.rank]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-lg font-semibold text-white",children:e.symbol}),(e=>{switch(e){case"BULLISH":return(0,a.jsx)(d,{className:"h-4 w-4 text-green-400"});case"BEARISH":return(0,a.jsx)(eD,{className:"h-4 w-4 text-red-400"});default:return(0,a.jsx)(eG,{className:"h-4 w-4 text-yellow-400"})}})(e.analysis.trend),(0,a.jsx)(eL,{className:(s=e.analysis.recommendation).includes("BUY")?"bg-green-500/20 text-green-400":s.includes("SELL")?"bg-red-500/20 text-red-400":"bg-yellow-500/20 text-yellow-400",children:e.analysis.recommendation.replace("_"," ")})]}),(0,a.jsx)("div",{className:"text-sm text-slate-300",children:e.name})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-xl font-bold text-white",children:[e.score.toFixed(1),"/100"]}),(0,a.jsx)("div",{className:"text-sm text-slate-300",children:"Score"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-slate-300",children:"Price"}),(0,a.jsx)("div",{className:"text-white font-semibold",children:eP(e.quote.price)}),(0,a.jsx)("div",{className:e.quote.change>=0?"text-green-400":"text-red-400",children:eM(e.quote.changePercent)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-slate-300",children:"Entry"}),(0,a.jsx)("div",{className:"text-blue-400 font-semibold",children:eP(e.analysis.entryPrice)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-slate-300",children:"R/R Ratio"}),(0,a.jsxs)("div",{className:"text-green-400 font-semibold",children:[e.analysis.riskRewardRatio.toFixed(2),":1"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-slate-300",children:"Confidence"}),(0,a.jsxs)("div",{className:"text-white font-semibold",children:[e.analysis.confidence.toFixed(1),"%"]})]})]})]},e.symbol)})})})]})]})]})}let eB=o("moon",[["path",{d:"M20.985 12.486a9 9 0 1 1-9.473-9.472c.405-.022.617.46.402.803a6 6 0 0 0 8.268 8.268c.344-.215.825-.004.803.401",key:"kfwtm"}]]),eV=o("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]),eq=o("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]),eH=o("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]),eW=o("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),e$=o("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),eK=o("play",[["path",{d:"M5 5a2 2 0 0 1 3.008-1.728l11.997 6.998a2 2 0 0 1 .003 3.458l-12 7A2 2 0 0 1 5 19z",key:"10ikf1"}]]),eQ=o("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),eY=o("sparkles",[["path",{d:"M11.017 2.814a1 1 0 0 1 1.966 0l1.051 5.558a2 2 0 0 0 1.594 1.594l5.558 1.051a1 1 0 0 1 0 1.966l-5.558 1.051a2 2 0 0 0-1.594 1.594l-1.051 5.558a1 1 0 0 1-1.966 0l-1.051-5.558a2 2 0 0 0-1.594-1.594l-5.558-1.051a1 1 0 0 1 0-1.966l5.558-1.051a2 2 0 0 0 1.594-1.594z",key:"1s2grr"}],["path",{d:"M20 2v4",key:"1rf3ol"}],["path",{d:"M22 4h-4",key:"gwowj6"}],["circle",{cx:"4",cy:"20",r:"2",key:"6kqj1y"}]]);function eJ(e){var t,s;let{setup:l,onExecuteTrade:n,onViewChart:i}=e,[c,o]=(0,r.useState)(!1),[m,p]=(0,r.useState)(null),[f,b]=(0,r.useState)(!1),[j,v]=(0,r.useState)(!1);(0,r.useEffect)(()=>{y()},[]);let y=async()=>{try{let e=await fetch("/api/ai?action=status"),t=await e.json();v(t.enabled),t.enabled&&N()}catch(e){console.error("Error checking AI status:",e)}},N=async()=>{b(!0);try{let e=await fetch("/api/ai",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"risk-assessment",data:{setup:l}})});if(e.ok){let{riskAssessment:t}=await e.json();p(t)}}catch(e){console.error("Error loading AI risk assessment:",e)}finally{b(!1)}},w=((l.targets[0]-l.entryPrice)/(l.entryPrice-l.stopLoss)).toFixed(1);return(0,a.jsxs)(ez,{className:"bg-slate-800/50 border-slate-700",children:[(0,a.jsx)(eR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(eI,{className:"text-xl text-white",children:l.symbol}),(0,a.jsx)(eL,{variant:"outline",className:"text-blue-400 border-blue-400",children:"overnight_momentum"===l.strategy?"Overnight Momentum":"Technical Breakout"}),(0,a.jsxs)(eL,{className:(t=l.confidence)>=80?"bg-green-500/20 text-green-400":t>=60?"bg-yellow-500/20 text-yellow-400":"bg-red-500/20 text-red-400",children:[l.confidence,"% Confidence"]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(eE,{size:"sm",variant:"outline",onClick:()=>null==i?void 0:i(l.symbol),className:"text-slate-300 hover:text-white",children:[(0,a.jsx)(eQ,{className:"h-4 w-4 mr-1"}),"Chart"]}),(0,a.jsxs)(eE,{size:"sm",onClick:()=>null==n?void 0:n(l),className:"bg-green-600 hover:bg-green-700 text-white",children:[(0,a.jsx)(eK,{className:"h-4 w-4 mr-1"}),"Execute Trade"]})]})]})}),(0,a.jsxs)(eF,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-3 bg-slate-700/30 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-1",children:[(0,a.jsx)(d,{className:"h-4 w-4 text-green-400 mr-1"}),(0,a.jsx)("span",{className:"text-xs text-slate-400",children:"Entry"})]}),(0,a.jsxs)("div",{className:"text-lg font-bold text-white",children:["$",l.preciseEntry.price.toFixed(2)]}),(0,a.jsx)("div",{className:"text-xs text-slate-400",children:l.preciseEntry.orderType})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-slate-700/30 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-1",children:[(0,a.jsx)(h,{className:"h-4 w-4 text-red-400 mr-1"}),(0,a.jsx)("span",{className:"text-xs text-slate-400",children:"Stop Loss"})]}),(0,a.jsxs)("div",{className:"text-lg font-bold text-white",children:["$",l.preciseExit.stopLoss.price.toFixed(2)]}),(0,a.jsx)("div",{className:"text-xs text-slate-400",children:l.preciseExit.stopLoss.orderType})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-slate-700/30 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-1",children:[(0,a.jsx)(x,{className:"h-4 w-4 text-blue-400 mr-1"}),(0,a.jsx)("span",{className:"text-xs text-slate-400",children:"First Target"})]}),(0,a.jsxs)("div",{className:"text-lg font-bold text-white",children:["$",l.targets[0].toFixed(2)]}),(0,a.jsxs)("div",{className:"text-xs text-slate-400",children:["R/R: ",w,":1"]})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-slate-700/30 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center mb-1",children:[(0,a.jsx)(eH,{className:"h-4 w-4 text-yellow-400 mr-1"}),(0,a.jsx)("span",{className:"text-xs text-slate-400",children:"Position Size"})]}),(0,a.jsx)("div",{className:"text-lg font-bold text-white",children:l.positionSize}),(0,a.jsx)("div",{className:"text-xs text-slate-400",children:"shares"})]})]}),(0,a.jsxs)("div",{className:"p-3 bg-slate-700/20 rounded-lg",children:[(0,a.jsxs)("h4",{className:"text-sm font-semibold text-white mb-2 flex items-center",children:[(0,a.jsx)(e$,{className:"h-4 w-4 text-green-400 mr-2"}),"Entry Conditions"]}),(0,a.jsx)("div",{className:"space-y-1",children:l.preciseEntry.conditions.map((e,t)=>(0,a.jsxs)("div",{className:"text-xs text-slate-300 flex items-center",children:[(0,a.jsx)("div",{className:"w-1 h-1 bg-green-400 rounded-full mr-2"}),e]},t))}),(0,a.jsxs)("div",{className:"mt-2 text-xs text-blue-400",children:[(0,a.jsx)(eq,{className:"h-3 w-3 inline mr-1"}),l.preciseEntry.timing]})]}),(0,a.jsxs)("div",{className:"p-3 bg-slate-700/20 rounded-lg",children:[(0,a.jsxs)("h4",{className:"text-sm font-semibold text-white mb-2 flex items-center",children:[(0,a.jsx)(x,{className:"h-4 w-4 text-blue-400 mr-2"}),"Take Profit Plan"]}),(0,a.jsx)("div",{className:"space-y-2",children:l.preciseExit.takeProfits.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,a.jsxs)("div",{className:"text-slate-300",children:[(0,a.jsxs)("span",{className:"font-medium",children:[e.target,":"]})," $",e.price.toFixed(2)]}),(0,a.jsxs)("div",{className:"text-slate-400",children:[e.percentage,"% of position"]})]},t))})]}),(0,a.jsxs)("div",{className:"p-3 bg-slate-700/20 rounded-lg",children:[(0,a.jsxs)("h4",{className:"text-sm font-semibold text-white mb-2 flex items-center",children:[(0,a.jsx)(h,{className:"h-4 w-4 text-red-400 mr-2"}),"Risk Management"]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-xs",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Max Risk:"}),(0,a.jsxs)("span",{className:"text-white ml-2",children:["$",l.riskManagement.maxRiskDollars.toFixed(0)]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Account Risk:"}),(0,a.jsxs)("span",{className:"text-white ml-2",children:[l.riskManagement.accountRiskPercent,"%"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Time Stop:"}),(0,a.jsxs)("span",{className:"text-white ml-2",children:[l.riskManagement.timeStopHours,"h"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Max Drawdown:"}),(0,a.jsxs)("span",{className:"text-white ml-2",children:[l.riskManagement.maxDrawdownPercent,"%"]})]})]})]}),j&&(0,a.jsxs)("div",{className:"p-3 bg-gradient-to-r from-blue-900/20 to-purple-900/20 rounded-lg border border-blue-500/20",children:[(0,a.jsxs)("h4",{className:"text-sm font-semibold text-white mb-2 flex items-center",children:[(0,a.jsx)(u,{className:"h-4 w-4 text-blue-400 mr-2"}),"AI Risk Assessment",(0,a.jsx)(eY,{className:"h-3 w-3 text-blue-400 ml-1"})]}),f?(0,a.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,a.jsx)(g,{className:"h-4 w-4 animate-spin text-blue-400 mr-2"}),(0,a.jsx)("span",{className:"text-xs text-slate-300",children:"Analyzing risk..."})]}):m?(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-xs text-slate-400",children:"Risk Score:"}),(0,a.jsxs)("span",{className:"ml-2 text-sm font-bold ".concat((s=m.riskScore)<=3?"text-green-400":s<=6?"text-yellow-400":"text-red-400"),children:[m.riskScore,"/10"]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-xs text-slate-400",children:"Sentiment:"}),(0,a.jsx)("span",{className:"ml-2 text-sm font-semibold capitalize ".concat((e=>{switch(e){case"bullish":return"text-green-400";case"bearish":return"text-red-400";default:return"text-yellow-400"}})(m.sentiment)),children:m.sentiment})]})]}),m.riskFactors.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-xs text-slate-400 mb-1 block",children:"Key Risk Factors:"}),(0,a.jsx)("div",{className:"space-y-1",children:m.riskFactors.slice(0,2).map((e,t)=>(0,a.jsxs)("div",{className:"text-xs text-slate-300 flex items-start",children:[(0,a.jsx)(eW,{className:"h-3 w-3 text-yellow-400 mr-1 mt-0.5 flex-shrink-0"}),e]},t))})]}),m.recommendations.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-xs text-slate-400 mb-1 block",children:"AI Recommendations:"}),(0,a.jsx)("div",{className:"space-y-1",children:m.recommendations.slice(0,2).map((e,t)=>(0,a.jsxs)("div",{className:"text-xs text-slate-300 flex items-start",children:[(0,a.jsx)(e$,{className:"h-3 w-3 text-blue-400 mr-1 mt-0.5 flex-shrink-0"}),e]},t))})]})]}):(0,a.jsx)("div",{className:"text-xs text-slate-400 text-center py-2",children:"AI risk assessment unavailable"})]}),(0,a.jsxs)(eE,{variant:"outline",size:"sm",onClick:()=>o(!c),className:"w-full text-slate-300 hover:text-white",children:[c?"Hide":"Show"," Execution Details"]}),c&&(0,a.jsxs)("div",{className:"space-y-4 pt-4 border-t border-slate-700",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-sm font-semibold text-green-400 mb-2",children:"Entry Instructions:"}),(0,a.jsx)("div",{className:"space-y-1",children:l.executionPlan.entryInstructions.map((e,t)=>(0,a.jsxs)("div",{className:"text-xs text-slate-300 flex items-start",children:[(0,a.jsx)("div",{className:"w-4 h-4 rounded-full bg-green-500/20 text-green-400 flex items-center justify-center text-[10px] mr-2 mt-0.5 flex-shrink-0",children:t+1}),e]},t))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-sm font-semibold text-red-400 mb-2",children:"Exit Instructions:"}),(0,a.jsx)("div",{className:"space-y-1",children:l.executionPlan.exitInstructions.map((e,t)=>(0,a.jsxs)("div",{className:"text-xs text-slate-300 flex items-start",children:[(0,a.jsx)("div",{className:"w-4 h-4 rounded-full bg-red-500/20 text-red-400 flex items-center justify-center text-[10px] mr-2 mt-0.5 flex-shrink-0",children:t+1}),e]},t))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-sm font-semibold text-blue-400 mb-2",children:"Monitor These:"}),(0,a.jsx)("div",{className:"space-y-1",children:l.executionPlan.monitoringPoints.map((e,t)=>(0,a.jsxs)("div",{className:"text-xs text-slate-300 flex items-center",children:[(0,a.jsx)(eQ,{className:"h-3 w-3 text-blue-400 mr-2"}),e]},t))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-sm font-semibold text-yellow-400 mb-2",children:"Contingency Plans:"}),(0,a.jsx)("div",{className:"space-y-1",children:l.executionPlan.contingencyPlans.map((e,t)=>(0,a.jsxs)("div",{className:"text-xs text-slate-300 flex items-start",children:[(0,a.jsx)(eW,{className:"h-3 w-3 text-yellow-400 mr-2 mt-0.5 flex-shrink-0"}),e]},t))})]}),(0,a.jsxs)("div",{className:"p-3 bg-red-500/10 border border-red-500/20 rounded-lg",children:[(0,a.jsx)("h5",{className:"text-sm font-semibold text-red-400 mb-1",children:"Setup Invalidation:"}),(0,a.jsx)("div",{className:"text-xs text-slate-300",children:l.invalidation})]})]})]})]})}let eZ=o("lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]);function eX(e){let{scanResults:t,marketConditions:s,onRefresh:l}=e,[n,i]=(0,r.useState)(null),[c,o]=(0,r.useState)(""),[h,p]=(0,r.useState)(null),[f,b]=(0,r.useState)(!1),[j,v]=(0,r.useState)(null);(0,r.useEffect)(()=>{y()},[]),(0,r.useEffect)(()=>{t.length>0&&(null==n?void 0:n.enabled)&&N()},[t,n]);let y=async()=>{try{let e=await fetch("/api/ai?action=status"),t=await e.json();i(t)}catch(e){console.error("Error checking AI status:",e),v("Unable to connect to AI service")}},N=async()=>{if((null==n?void 0:n.enabled)&&0!==t.length){b(!0),v(null);try{let e=await fetch("/api/ai",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"market-commentary",data:{scanResults:t,marketConditions:s}})});if(e.ok){let{commentary:t}=await e.json();o(t)}let a=await fetch("/api/ai",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"trading-recommendations",data:{scanResults:t,userPreferences:{riskTolerance:"medium",tradingStyle:"moderate",accountSize:1e5}}})});if(a.ok){let{recommendations:e}=await a.json();p(e)}}catch(e){console.error("Error generating AI insights:",e),v("Failed to generate AI insights")}finally{b(!1)}}};return n?n.enabled?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(ez,{className:"bg-slate-800/50 border-slate-700",children:(0,a.jsx)(eR,{className:"pb-3",children:(0,a.jsxs)(eI,{className:"text-white flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(eY,{className:"mr-2 h-5 w-5 text-blue-400"}),"AI-Powered Insights",(0,a.jsx)(eL,{className:"ml-2 bg-blue-500/20 text-blue-400",children:n.model||"GPT-4o"})]}),(0,a.jsxs)(eE,{size:"sm",variant:"outline",onClick:N,disabled:f,className:"text-slate-300 hover:text-white",children:[f?(0,a.jsx)(g,{className:"h-4 w-4 animate-spin mr-1"}):(0,a.jsx)(u,{className:"h-4 w-4 mr-1"}),f?"Analyzing...":"Refresh Insights"]})]})})}),j&&(0,a.jsx)(ez,{className:"bg-red-900/20 border-red-500/30",children:(0,a.jsx)(eF,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center text-red-400",children:[(0,a.jsx)(eW,{className:"h-4 w-4 mr-2"}),j]})})}),c&&(0,a.jsxs)(ez,{className:"bg-slate-800/50 border-slate-700",children:[(0,a.jsx)(eR,{children:(0,a.jsxs)(eI,{className:"text-white flex items-center",children:[(0,a.jsx)(m,{className:"mr-2 h-5 w-5 text-green-400"}),"Market Commentary"]})}),(0,a.jsx)(eF,{children:(0,a.jsx)("div",{className:"prose prose-invert max-w-none",children:(0,a.jsx)("p",{className:"text-slate-300 leading-relaxed whitespace-pre-line",children:c})})})]}),h&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)(ez,{className:"bg-slate-800/50 border-slate-700",children:[(0,a.jsx)(eR,{children:(0,a.jsxs)(eI,{className:"text-white flex items-center",children:[(0,a.jsx)(d,{className:"mr-2 h-5 w-5 text-green-400"}),"AI Top Picks"]})}),(0,a.jsx)(eF,{children:(0,a.jsx)("div",{className:"space-y-2",children:h.topPicks.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-2 bg-green-900/20 rounded",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-6 h-6 rounded-full bg-green-500/20 text-green-400 flex items-center justify-center text-sm font-bold mr-3",children:t+1}),(0,a.jsx)("span",{className:"text-white font-semibold",children:e})]}),(0,a.jsx)(eL,{className:"bg-green-500/20 text-green-400",children:"Recommended"})]},e))})})]}),(0,a.jsxs)(ez,{className:"bg-slate-800/50 border-slate-700",children:[(0,a.jsx)(eR,{children:(0,a.jsxs)(eI,{className:"text-white flex items-center",children:[(0,a.jsx)(eZ,{className:"mr-2 h-5 w-5 text-yellow-400"}),"Action Items"]})}),(0,a.jsx)(eF,{children:(0,a.jsx)("div",{className:"space-y-2",children:h.actionItems.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-start p-2 bg-yellow-900/20 rounded",children:[(0,a.jsx)(x,{className:"h-4 w-4 text-yellow-400 mr-2 mt-0.5 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-slate-300 text-sm",children:e})]},t))})})]})]}),(null==h?void 0:h.marketOutlook)&&(0,a.jsxs)(ez,{className:"bg-slate-800/50 border-slate-700",children:[(0,a.jsx)(eR,{children:(0,a.jsxs)(eI,{className:"text-white flex items-center",children:[(0,a.jsx)(d,{className:"mr-2 h-5 w-5 text-blue-400"}),"Market Outlook"]})}),(0,a.jsx)(eF,{children:(0,a.jsx)("p",{className:"text-slate-300 leading-relaxed",children:h.marketOutlook})})]}),(null==h?void 0:h.avoidList)&&h.avoidList.length>0&&(0,a.jsxs)(ez,{className:"bg-slate-800/50 border-slate-700",children:[(0,a.jsx)(eR,{children:(0,a.jsxs)(eI,{className:"text-white flex items-center",children:[(0,a.jsx)(eD,{className:"mr-2 h-5 w-5 text-red-400"}),"Caution List"]})}),(0,a.jsx)(eF,{children:(0,a.jsx)("div",{className:"space-y-2",children:h.avoidList.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-2 bg-red-900/20 rounded",children:[(0,a.jsx)("span",{className:"text-white font-semibold",children:e}),(0,a.jsx)(eL,{className:"bg-red-500/20 text-red-400",children:"Avoid"})]},e))})})]}),f&&(0,a.jsx)(ez,{className:"bg-slate-800/50 border-slate-700",children:(0,a.jsx)(eF,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsx)(g,{className:"h-6 w-6 animate-spin text-blue-400 mr-2"}),(0,a.jsx)("span",{className:"text-slate-300",children:"Generating AI insights..."})]})})})]}):(0,a.jsxs)(ez,{className:"bg-slate-800/50 border-slate-700",children:[(0,a.jsx)(eR,{children:(0,a.jsxs)(eI,{className:"text-white flex items-center",children:[(0,a.jsx)(u,{className:"mr-2 h-5 w-5 text-gray-400"}),"AI Insights",(0,a.jsx)(eL,{variant:"outline",className:"ml-2 text-gray-400 border-gray-400",children:"Disabled"})]})}),(0,a.jsx)(eF,{children:(0,a.jsxs)("div",{className:"text-center py-6",children:[(0,a.jsx)(u,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-slate-300 mb-4",children:"AI-powered insights are currently disabled."}),(0,a.jsx)("p",{className:"text-sm text-slate-400 mb-4",children:"Enable OpenAI integration to get intelligent market analysis, risk assessments, and personalized trading recommendations."}),(0,a.jsx)(eE,{variant:"outline",onClick:y,children:"Check Configuration"})]})})]}):(0,a.jsx)(ez,{className:"bg-slate-800/50 border-slate-700",children:(0,a.jsx)(eF,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsx)(g,{className:"h-6 w-6 animate-spin text-blue-400 mr-2"}),(0,a.jsx)("span",{className:"text-slate-300",children:"Checking AI status..."})]})})})}function e0(e){let{autoScan:t=!1,accountSize:s=1e5}=e,[l,n]=(0,r.useState)(!1),[i,c]=(0,r.useState)(null),[o,m]=(0,r.useState)("both"),[h,u]=(0,r.useState)(null),[f,b]=(0,r.useState)(s),j=async e=>{try{alert("Execute Trade for ".concat(e.symbol,":\n\nEntry: $").concat(e.preciseEntry.price.toFixed(2)," (").concat(e.preciseEntry.orderType,")\nStop: $").concat(e.preciseExit.stopLoss.price.toFixed(2),"\nTarget: $").concat(e.targets[0].toFixed(2),"\nShares: ").concat(e.positionSize,"\n\nThis would normally open your trading platform or paper trading interface."))}catch(e){console.error("Error executing trade:",e)}},v=e=>{window.open("https://www.tradingview.com/chart/?symbol=".concat(e),"_blank")};(0,r.useEffect)(()=>{t&&y("quick")},[t]);let y=async e=>{n(!0),u(null),c(null);try{let t=await fetch("/api/scanner/strategies?type=".concat(e,"&accountSize=").concat(f,"&limit=20"));if(!t.ok)throw Error("Failed to fetch strategy scan results");let s=await t.json();c(s)}catch(e){u("Failed to perform strategy scan. Please try again."),console.error("Strategy scan error:",e)}finally{n(!1)}},N=(null==i?void 0:i.topSetups.filter(e=>"both"===o||("overnight"===o?e.overnightSetup:"breakout"!==o||e.breakoutSetup)))||[];return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(ez,{className:"bg-slate-800/50 border-slate-700",children:[(0,a.jsxs)(eR,{children:[(0,a.jsxs)(eI,{className:"text-white flex items-center",children:[(0,a.jsx)(p,{className:"mr-2 h-5 w-5 text-yellow-400"}),"Professional Swing Trading Strategies"]}),(0,a.jsx)(e_,{className:"text-slate-300",children:"Automated scanner implementing proven swing trading methodologies with precise entry/exit rules"})]}),(0,a.jsxs)(eF,{children:[(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-sm text-slate-300 mb-2",children:"Account Size (for position sizing)"}),(0,a.jsx)("input",{type:"number",value:f,onChange:e=>b(parseInt(e.target.value)||1e5),className:"w-32 px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm",min:"10000",step:"10000",disabled:l})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-sm text-slate-300 mb-2",children:"Strategy Filter"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(eE,{variant:"both"===o?"default":"outline",size:"sm",onClick:()=>m("both"),className:"both"===o?"bg-blue-600":"border-slate-600 text-slate-300",children:"All Strategies"}),(0,a.jsxs)(eE,{variant:"overnight"===o?"default":"outline",size:"sm",onClick:()=>m("overnight"),className:"overnight"===o?"bg-purple-600":"border-slate-600 text-slate-300",children:[(0,a.jsx)(eB,{className:"mr-1 h-3 w-3"}),"Overnight"]}),(0,a.jsxs)(eE,{variant:"breakout"===o?"default":"outline",size:"sm",onClick:()=>m("breakout"),className:"breakout"===o?"bg-green-600":"border-slate-600 text-slate-300",children:[(0,a.jsx)(d,{className:"mr-1 h-3 w-3"}),"Breakout"]})]})]}),(0,a.jsxs)("div",{className:"flex gap-4 mb-4",children:[(0,a.jsxs)(eE,{onClick:()=>y("quick"),disabled:l,className:"bg-blue-600 hover:bg-blue-700",children:[l?(0,a.jsx)(g,{className:"mr-2 h-4 w-4 animate-spin"}):(0,a.jsx)(p,{className:"mr-2 h-4 w-4"}),"Quick Strategy Scan"]}),(0,a.jsxs)(eE,{onClick:()=>y("full"),disabled:l,variant:"outline",className:"border-slate-600 text-slate-300 hover:bg-slate-800",children:[l?(0,a.jsx)(g,{className:"mr-2 h-4 w-4 animate-spin"}):(0,a.jsx)(x,{className:"mr-2 h-4 w-4"}),"Full Strategy Scan"]})]}),(null==i?void 0:i.marketConditions)&&(0,a.jsx)("div",{className:"p-3 bg-slate-700/50 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-slate-300",children:"Market Status:"}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("span",{className:"text-white",children:i.marketConditions.timeOfDay}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(eL,{className:i.marketConditions.marketHours?"bg-green-500/20 text-green-400":"bg-red-500/20 text-red-400",children:i.marketConditions.marketHours?"Market Open":"Market Closed"}),(0,a.jsx)(eL,{className:i.marketConditions.isOptimalScanTime?"bg-blue-500/20 text-blue-400":"bg-yellow-500/20 text-yellow-400",children:i.marketConditions.isOptimalScanTime?"Optimal Scan Time":"Outside Optimal Hours"})]})]})]})}),l&&(0,a.jsxs)("div",{className:"text-center py-4",children:[(0,a.jsx)(g,{className:"mx-auto h-8 w-8 animate-spin text-blue-400"}),(0,a.jsx)("p",{className:"text-slate-300 mt-2",children:"Analyzing stocks for professional swing trading setups..."})]})]})]}),h&&(0,a.jsx)(ez,{className:"bg-red-900/20 border-red-500/50",children:(0,a.jsx)(eF,{className:"p-6",children:(0,a.jsx)("p",{className:"text-red-300 text-center",children:h})})}),i&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(ez,{className:"bg-slate-800/50 border-slate-700",children:[(0,a.jsx)(eR,{children:(0,a.jsx)(eI,{className:"text-white",children:"Strategy Scan Summary"})}),(0,a.jsx)(eF,{children:(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-white",children:i.totalScanned}),(0,a.jsx)("div",{className:"text-sm text-slate-300",children:"Stocks Scanned"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-400",children:i.overnightSetups}),(0,a.jsx)("div",{className:"text-sm text-slate-300",children:"Overnight Setups"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-400",children:i.breakoutSetups}),(0,a.jsx)("div",{className:"text-sm text-slate-300",children:"Breakout Setups"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-400",children:i.bothStrategies}),(0,a.jsx)("div",{className:"text-sm text-slate-300",children:"Both Strategies"})]})]})})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold text-white flex items-center",children:[(0,a.jsx)(x,{className:"mr-3 h-6 w-6 text-green-400"}),"Professional Trading Setups (",N.length,")"]}),(0,a.jsxs)(eE,{variant:"outline",size:"sm",onClick:()=>window.open("https://www.tradingview.com","_blank"),className:"text-slate-300 hover:text-white",children:[(0,a.jsx)(eV,{className:"h-4 w-4 mr-2"}),"Open TradingView"]})]}),N.map((e,t)=>{let s="overnight_momentum"===e.bestStrategy?e.overnightSetup:e.breakoutSetup;if(!s)return null;let r={...s,symbol:e.symbol};return(0,a.jsx)(eJ,{setup:r,onExecuteTrade:j,onViewChart:v},e.symbol)})]}),N.length>0&&(0,a.jsx)("div",{className:"mt-8",children:(0,a.jsx)(eX,{scanResults:N,marketConditions:i.marketConditions,onRefresh:()=>runScan()})})]})]})}function e1(e,t){let{checkForDefaultPrevented:s=!0}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return function(a){if(null==e||e(a),!1===s||!a.defaultPrevented)return null==t?void 0:t(a)}}function e2(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],s=[],l=()=>{let t=s.map(e=>r.createContext(e));return function(s){let a=(null==s?void 0:s[e])||t;return r.useMemo(()=>({["__scope".concat(e)]:{...s,[e]:a}}),[s,a])}};return l.scopeName=e,[function(t,l){let n=r.createContext(l),i=s.length;s=[...s,l];let c=t=>{var s;let{scope:l,children:c,...o}=t,d=(null==l||null==(s=l[e])?void 0:s[i])||n,m=r.useMemo(()=>o,Object.values(o));return(0,a.jsx)(d.Provider,{value:m,children:c})};return c.displayName=t+"Provider",[c,function(s,a){var c;let o=(null==a||null==(c=a[e])?void 0:c[i])||n,d=r.useContext(o);if(d)return d;if(void 0!==l)return l;throw Error("`".concat(s,"` must be used within `").concat(t,"`"))}]},function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];let a=t[0];if(1===t.length)return a;let l=()=>{let e=t.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(t){let s=e.reduce((e,s)=>{let{useScope:a,scopeName:r}=s,l=a(t)["__scope".concat(r)];return{...e,...l}},{});return r.useMemo(()=>({["__scope".concat(a.scopeName)]:s}),[s])}};return l.scopeName=a.scopeName,l}(l,...t)]}function e4(e,t,s){if(!t.has(e))throw TypeError("attempted to "+s+" private field on non-instance");return t.get(e)}function e5(e,t){var s=e4(e,t,"get");return s.get?s.get.call(e):s.value}function e3(e,t,s){var a=e4(e,t,"set");if(a.set)a.set.call(e,s);else{if(!a.writable)throw TypeError("attempted to set read only private field");a.value=s}return s}"undefined"!=typeof window&&window.document&&window.document.createElement;var e6=new WeakMap;function e7(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let s=function(e,t){let s=e.length,a=e8(t),r=a>=0?a:s+a;return r<0||r>=s?-1:r}(e,t);return -1===s?void 0:e[s]}function e8(e){return e!=e||0===e?0:Math.trunc(e)}t=new WeakMap,class e extends Map{set(e,s){return e6.get(this)&&(this.has(e)?e5(this,t)[e5(this,t).indexOf(e)]=e:e5(this,t).push(e)),super.set(e,s),this}insert(e,s,a){let r,l=this.has(s),n=e5(this,t).length,i=e8(e),c=i>=0?i:n+i,o=c<0||c>=n?-1:c;if(o===this.size||l&&o===this.size-1||-1===o)return this.set(s,a),this;let d=this.size+ +!l;i<0&&c++;let m=[...e5(this,t)],x=!1;for(let e=c;e<d;e++)if(c===e){let t=m[e];m[e]===s&&(t=m[e+1]),l&&this.delete(s),r=this.get(t),this.set(s,a)}else{x||m[e-1]!==s||(x=!0);let t=m[x?e:e-1],a=r;r=this.get(t),this.delete(t),this.set(t,a)}return this}with(t,s,a){let r=new e(this);return r.insert(t,s,a),r}before(e){let s=e5(this,t).indexOf(e)-1;if(!(s<0))return this.entryAt(s)}setBefore(e,s,a){let r=e5(this,t).indexOf(e);return -1===r?this:this.insert(r,s,a)}after(e){let s=e5(this,t).indexOf(e);if(-1!==(s=-1===s||s===this.size-1?-1:s+1))return this.entryAt(s)}setAfter(e,s,a){let r=e5(this,t).indexOf(e);return -1===r?this:this.insert(r+1,s,a)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return e3(this,t,[]),super.clear()}delete(e){let s=super.delete(e);return s&&e5(this,t).splice(e5(this,t).indexOf(e),1),s}deleteAt(e){let t=this.keyAt(e);return void 0!==t&&this.delete(t)}at(e){let s=e7(e5(this,t),e);if(void 0!==s)return this.get(s)}entryAt(e){let s=e7(e5(this,t),e);if(void 0!==s)return[s,this.get(s)]}indexOf(e){return e5(this,t).indexOf(e)}keyAt(e){return e7(e5(this,t),e)}from(e,t){let s=this.indexOf(e);if(-1===s)return;let a=s+t;return a<0&&(a=0),a>=this.size&&(a=this.size-1),this.at(a)}keyFrom(e,t){let s=this.indexOf(e);if(-1===s)return;let a=s+t;return a<0&&(a=0),a>=this.size&&(a=this.size-1),this.keyAt(a)}find(e,t){let s=0;for(let a of this){if(Reflect.apply(e,t,[a,s,this]))return a;s++}}findIndex(e,t){let s=0;for(let a of this){if(Reflect.apply(e,t,[a,s,this]))return s;s++}return -1}filter(t,s){let a=[],r=0;for(let e of this)Reflect.apply(t,s,[e,r,this])&&a.push(e),r++;return new e(a)}map(t,s){let a=[],r=0;for(let e of this)a.push([e[0],Reflect.apply(t,s,[e,r,this])]),r++;return new e(a)}reduce(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];let[a,r]=t,l=0,n=null!=r?r:this.at(0);for(let e of this)n=0===l&&1===t.length?e:Reflect.apply(a,this,[n,e,l,this]),l++;return n}reduceRight(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];let[a,r]=t,l=null!=r?r:this.at(-1);for(let e=this.size-1;e>=0;e--){let s=this.at(e);l=e===this.size-1&&1===t.length?s:Reflect.apply(a,this,[l,s,e,this])}return l}toSorted(t){return new e([...this.entries()].sort(t))}toReversed(){let t=new e;for(let e=this.size-1;e>=0;e--){let s=this.keyAt(e),a=this.get(s);t.set(s,a)}return t}toSpliced(){for(var t=arguments.length,s=Array(t),a=0;a<t;a++)s[a]=arguments[a];let r=[...this.entries()];return r.splice(...s),new e(r)}slice(t,s){let a=new e,r=this.size-1;if(void 0===t)return a;t<0&&(t+=this.size),void 0!==s&&s>0&&(r=s-1);for(let e=t;e<=r;e++){let t=this.keyAt(e),s=this.get(t);a.set(t,s)}return a}every(e,t){let s=0;for(let a of this){if(!Reflect.apply(e,t,[a,s,this]))return!1;s++}return!0}some(e,t){let s=0;for(let a of this){if(Reflect.apply(e,t,[a,s,this]))return!0;s++}return!1}constructor(e){super(e),function(e,t,s){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object");t.set(e,s)}(this,t,{writable:!0,value:void 0}),e3(this,t,[...super.keys()]),e6.set(this,!0)}};var e9=(null==(s=globalThis)?void 0:s.document)?r.useLayoutEffect:()=>{},te=r[" useId ".trim().toString()]||(()=>void 0),tt=0;function ts(e){let[t,s]=r.useState(te());return e9(()=>{e||s(e=>null!=e?e:String(tt++))},[e]),e||(t?"radix-".concat(t):"")}e.i(48680);var ta=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let s=N("Primitive.".concat(t)),l=r.forwardRef((e,r)=>{let{asChild:l,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(l?s:t,{...n,ref:r})});return l.displayName="Primitive.".concat(t),{...e,[t]:l}},{});r[" useEffectEvent ".trim().toString()],r[" useInsertionEffect ".trim().toString()];var tr=r[" useInsertionEffect ".trim().toString()]||e9;function tl(e){let{prop:t,defaultProp:s,onChange:a=()=>{},caller:l}=e,[n,i,c]=function(e){let{defaultProp:t,onChange:s}=e,[a,l]=r.useState(t),n=r.useRef(a),i=r.useRef(s);return tr(()=>{i.current=s},[s]),r.useEffect(()=>{if(n.current!==a){var e;null==(e=i.current)||e.call(i,a),n.current=a}},[a,n]),[a,l,i]}({defaultProp:s,onChange:a}),o=void 0!==t,d=o?t:n;{let e=r.useRef(void 0!==t);r.useEffect(()=>{let t=e.current;if(t!==o){let e=o?"controlled":"uncontrolled";console.warn("".concat(l," is changing from ").concat(t?"controlled":"uncontrolled"," to ").concat(e,". Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component."))}e.current=o},[o,l])}return[d,r.useCallback(e=>{if(o){let a="function"==typeof e?e(t):e;if(a!==t){var s;null==(s=c.current)||s.call(c,a)}}else i(e)},[o,t,i,c])]}Symbol("RADIX:SYNC_STATE");var tn=r.createContext(void 0);function ti(e){let t=r.useContext(tn);return e||t||"ltr"}var tc="rovingFocusGroup.onEntryFocus",to={bubbles:!1,cancelable:!0},td="RovingFocusGroup",[tm,tx,th]=function(e){let t=e+"CollectionProvider",[s,l]=e2(t),[n,i]=s(t,{collectionRef:{current:null},itemMap:new Map}),c=e=>{let{scope:t,children:s}=e,l=r.default.useRef(null),i=r.default.useRef(new Map).current;return(0,a.jsx)(n,{scope:t,itemMap:i,collectionRef:l,children:s})};c.displayName=t;let o=e+"CollectionSlot",d=N(o),m=r.default.forwardRef((e,t)=>{let{scope:s,children:r}=e,l=y(t,i(o,s).collectionRef);return(0,a.jsx)(d,{ref:l,children:r})});m.displayName=o;let x=e+"CollectionItemSlot",h="data-radix-collection-item",u=N(x),p=r.default.forwardRef((e,t)=>{let{scope:s,children:l,...n}=e,c=r.default.useRef(null),o=y(t,c),d=i(x,s);return r.default.useEffect(()=>(d.itemMap.set(c,{ref:c,...n}),()=>void d.itemMap.delete(c))),(0,a.jsx)(u,{...{[h]:""},ref:o,children:l})});return p.displayName=x,[{Provider:c,Slot:m,ItemSlot:p},function(t){let s=i(e+"CollectionConsumer",t);return r.default.useCallback(()=>{let e=s.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(h,"]")));return Array.from(s.itemMap.values()).sort((e,s)=>t.indexOf(e.ref.current)-t.indexOf(s.ref.current))},[s.collectionRef,s.itemMap])},l]}(td),[tu,tp]=e2(td,[th]),[tf,tg]=tu(td),tb=r.forwardRef((e,t)=>(0,a.jsx)(tm.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,a.jsx)(tm.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,a.jsx)(tj,{...e,ref:t})})}));tb.displayName=td;var tj=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:s,orientation:l,loop:n=!1,dir:i,currentTabStopId:c,defaultCurrentTabStopId:o,onCurrentTabStopIdChange:d,onEntryFocus:m,preventScrollOnEntryFocus:x=!1,...h}=e,u=r.useRef(null),p=y(t,u),f=ti(i),[g,b]=tl({prop:c,defaultProp:null!=o?o:null,onChange:d,caller:td}),[j,v]=r.useState(!1),N=function(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>function(){for(var e,s=arguments.length,a=Array(s),r=0;r<s;r++)a[r]=arguments[r];return null==(e=t.current)?void 0:e.call(t,...a)},[])}(m),w=tx(s),k=r.useRef(!1),[S,A]=r.useState(0);return r.useEffect(()=>{let e=u.current;if(e)return e.addEventListener(tc,N),()=>e.removeEventListener(tc,N)},[N]),(0,a.jsx)(tf,{scope:s,orientation:l,dir:f,loop:n,currentTabStopId:g,onItemFocus:r.useCallback(e=>b(e),[b]),onItemShiftTab:r.useCallback(()=>v(!0),[]),onFocusableItemAdd:r.useCallback(()=>A(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>A(e=>e-1),[]),children:(0,a.jsx)(ta.div,{tabIndex:j||0===S?-1:0,"data-orientation":l,...h,ref:p,style:{outline:"none",...e.style},onMouseDown:e1(e.onMouseDown,()=>{k.current=!0}),onFocus:e1(e.onFocus,e=>{let t=!k.current;if(e.target===e.currentTarget&&t&&!j){let t=new CustomEvent(tc,to);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=w().filter(e=>e.focusable);tw([e.find(e=>e.active),e.find(e=>e.id===g),...e].filter(Boolean).map(e=>e.ref.current),x)}}k.current=!1}),onBlur:e1(e.onBlur,()=>v(!1))})})}),tv="RovingFocusGroupItem",ty=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:s,focusable:l=!0,active:n=!1,tabStopId:i,children:c,...o}=e,d=ts(),m=i||d,x=tg(tv,s),h=x.currentTabStopId===m,u=tx(s),{onFocusableItemAdd:p,onFocusableItemRemove:f,currentTabStopId:g}=x;return r.useEffect(()=>{if(l)return p(),()=>f()},[l,p,f]),(0,a.jsx)(tm.ItemSlot,{scope:s,id:m,focusable:l,active:n,children:(0,a.jsx)(ta.span,{tabIndex:h?0:-1,"data-orientation":x.orientation,...o,ref:t,onMouseDown:e1(e.onMouseDown,e=>{l?x.onItemFocus(m):e.preventDefault()}),onFocus:e1(e.onFocus,()=>x.onItemFocus(m)),onKeyDown:e1(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void x.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,s){var a;let r=(a=e.key,"rtl"!==s?a:"ArrowLeft"===a?"ArrowRight":"ArrowRight"===a?"ArrowLeft":a);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(r))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(r)))return tN[r]}(e,x.orientation,x.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let s=u().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)s.reverse();else if("prev"===t||"next"===t){"prev"===t&&s.reverse();let a=s.indexOf(e.currentTarget);s=x.loop?function(e,t){return e.map((s,a)=>e[(t+a)%e.length])}(s,a+1):s.slice(a+1)}setTimeout(()=>tw(s))}}),children:"function"==typeof c?c({isCurrentTabStop:h,hasTabStop:null!=g}):c})})});ty.displayName=tv;var tN={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function tw(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],s=document.activeElement;for(let a of e)if(a===s||(a.focus({preventScroll:t}),document.activeElement!==s))return}var tk=e=>{let{present:t,children:s}=e,a=function(e){var t,s;let[a,l]=r.useState(),n=r.useRef(null),i=r.useRef(e),c=r.useRef("none"),[o,d]=(t=e?"mounted":"unmounted",s={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let a=s[e][t];return null!=a?a:e},t));return r.useEffect(()=>{let e=tS(n.current);c.current="mounted"===o?e:"none"},[o]),e9(()=>{let t=n.current,s=i.current;if(s!==e){let a=c.current,r=tS(t);e?d("MOUNT"):"none"===r||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):s&&a!==r?d("ANIMATION_OUT"):d("UNMOUNT"),i.current=e}},[e,d]),e9(()=>{if(a){var e;let t,s=null!=(e=a.ownerDocument.defaultView)?e:window,r=e=>{let r=tS(n.current).includes(CSS.escape(e.animationName));if(e.target===a&&r&&(d("ANIMATION_END"),!i.current)){let e=a.style.animationFillMode;a.style.animationFillMode="forwards",t=s.setTimeout(()=>{"forwards"===a.style.animationFillMode&&(a.style.animationFillMode=e)})}},l=e=>{e.target===a&&(c.current=tS(n.current))};return a.addEventListener("animationstart",l),a.addEventListener("animationcancel",r),a.addEventListener("animationend",r),()=>{s.clearTimeout(t),a.removeEventListener("animationstart",l),a.removeEventListener("animationcancel",r),a.removeEventListener("animationend",r)}}d("ANIMATION_END")},[a,d]),{isPresent:["mounted","unmountSuspended"].includes(o),ref:r.useCallback(e=>{n.current=e?getComputedStyle(e):null,l(e)},[])}}(t),l="function"==typeof s?s({present:a.isPresent}):r.Children.only(s),n=y(a.ref,function(e){var t,s;let a=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,r=a&&"isReactWarning"in a&&a.isReactWarning;return r?e.ref:(r=(a=null==(s=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:s.get)&&"isReactWarning"in a&&a.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof s||a.isPresent?r.cloneElement(l,{ref:n}):null};function tS(e){return(null==e?void 0:e.animationName)||"none"}tk.displayName="Presence";var tA="Tabs",[tC,tP]=e2(tA,[tp]),tM=tp(),[tT,tE]=tC(tA),tz=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,onValueChange:l,defaultValue:n,orientation:i="horizontal",dir:c,activationMode:o="automatic",...d}=e,m=ti(c),[x,h]=tl({prop:r,onChange:l,defaultProp:null!=n?n:"",caller:tA});return(0,a.jsx)(tT,{scope:s,baseId:ts(),value:x,onValueChange:h,orientation:i,dir:m,activationMode:o,children:(0,a.jsx)(ta.div,{dir:m,"data-orientation":i,...d,ref:t})})});tz.displayName=tA;var tR="TabsList",tI=r.forwardRef((e,t)=>{let{__scopeTabs:s,loop:r=!0,...l}=e,n=tE(tR,s),i=tM(s);return(0,a.jsx)(tb,{asChild:!0,...i,orientation:n.orientation,dir:n.dir,loop:r,children:(0,a.jsx)(ta.div,{role:"tablist","aria-orientation":n.orientation,...l,ref:t})})});tI.displayName=tR;var t_="TabsTrigger",tF=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,disabled:l=!1,...n}=e,i=tE(t_,s),c=tM(s),o=tD(i.baseId,r),d=tG(i.baseId,r),m=r===i.value;return(0,a.jsx)(ty,{asChild:!0,...c,focusable:!l,active:m,children:(0,a.jsx)(ta.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":d,"data-state":m?"active":"inactive","data-disabled":l?"":void 0,disabled:l,id:o,...n,ref:t,onMouseDown:e1(e.onMouseDown,e=>{l||0!==e.button||!1!==e.ctrlKey?e.preventDefault():i.onValueChange(r)}),onKeyDown:e1(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&i.onValueChange(r)}),onFocus:e1(e.onFocus,()=>{let e="manual"!==i.activationMode;m||l||!e||i.onValueChange(r)})})})});tF.displayName=t_;var tO="TabsContent",tL=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:l,forceMount:n,children:i,...c}=e,o=tE(tO,s),d=tD(o.baseId,l),m=tG(o.baseId,l),x=l===o.value,h=r.useRef(x);return r.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,a.jsx)(tk,{present:n||x,children:s=>{let{present:r}=s;return(0,a.jsx)(ta.div,{"data-state":x?"active":"inactive","data-orientation":o.orientation,role:"tabpanel","aria-labelledby":d,hidden:!r,id:m,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:r&&i})}})});function tD(e,t){return"".concat(e,"-trigger-").concat(t)}function tG(e,t){return"".concat(e,"-content-").concat(t)}tL.displayName=tO;let tU=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(tI,{ref:t,className:eC("inline-flex h-10 items-center justify-center rounded-md bg-slate-100 p-1 text-slate-500 dark:bg-slate-800 dark:text-slate-400",s),...r})});tU.displayName=tI.displayName;let tB=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(tF,{ref:t,className:eC("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-white transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-white data-[state=active]:text-slate-950 data-[state=active]:shadow-sm dark:ring-offset-slate-950 dark:focus-visible:ring-slate-300 dark:data-[state=active]:bg-slate-950 dark:data-[state=active]:text-slate-50",s),...r})});tB.displayName=tF.displayName;let tV=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(tL,{ref:t,className:eC("mt-2 ring-offset-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2 dark:ring-offset-slate-950 dark:focus-visible:ring-slate-300",s),...r})});tV.displayName=tL.displayName;let tq=o("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]),tH=o("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),tW=o("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]),t$=o("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),tK=o("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);function tQ(e){var t;let{setup:s,onExecuteTrade:l,onViewChart:n,onGenerateEntryTrigger:i}=e,[c,o]=(0,r.useState)(!1),m=s.rewardPlanning.riskRewardRatio,h=s.riskManagement.positionSize*s.riskManagement.riskPerShare*m,u=s.riskManagement.positionSize*s.riskManagement.riskPerShare;return(0,a.jsxs)(ez,{className:"hover:shadow-lg transition-all duration-200 border-l-4 border-l-blue-500",children:[(0,a.jsx)(eR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(eI,{className:"text-xl font-bold flex items-center gap-2",children:[s.symbol,(0,a.jsx)(tW,{className:"h-5 w-5 text-yellow-500"})]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:s.name})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(eL,{className:"".concat((t=s.setupGrade).startsWith("A")?"text-green-600 bg-green-50 border-green-200":t.startsWith("B")?"text-blue-600 bg-blue-50 border-blue-200":t.startsWith("C")?"text-yellow-600 bg-yellow-50 border-yellow-200":"text-red-600 bg-red-50 border-red-200"," border"),children:["Grade ",s.setupGrade]}),(0,a.jsxs)(eL,{variant:"outline",className:"font-semibold",children:["Score: ",s.overallScore,"/100"]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:eP(s.gapScan.price)}),(0,a.jsxs)("div",{className:"text-sm font-semibold text-green-600",children:["Gap: +",eM(s.gapScan.gapPercent)]})]})]})}),(0,a.jsxs)(eF,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(p,{className:"h-5 w-5 text-purple-600"}),(0,a.jsx)("span",{className:"font-semibold text-purple-800",children:"Catalyst"}),(e=>{switch(e){case"bullish":return(0,a.jsx)(d,{className:"h-4 w-4 text-green-500"});case"bearish":return(0,a.jsx)(eD,{className:"h-4 w-4 text-red-500"});default:return(0,a.jsx)(tq,{className:"h-4 w-4 text-gray-500"})}})(s.catalyst.impact),(0,a.jsx)(eL,{className:(e=>{switch(e){case"tier_1":return"bg-green-500 text-white";case"tier_2":return"bg-yellow-500 text-white";case"tier_3":return"bg-gray-500 text-white";default:return"bg-gray-400 text-white"}})(s.catalyst.tier),children:s.catalyst.tier.replace("_"," ").toUpperCase()}),(0,a.jsx)(eL,{className:(e=>{switch(e){case"fresh":return"text-green-600 bg-green-50";case"moderate":return"text-yellow-600 bg-yellow-50";case"stale":return"text-red-600 bg-red-50";default:return"text-gray-600 bg-gray-50"}})(s.catalyst.freshness),children:s.catalyst.freshness})]}),(0,a.jsx)("h4",{className:"font-medium text-gray-800 mb-1",children:s.catalyst.title}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:s.catalyst.description}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-xs text-gray-500",children:[(0,a.jsxs)("span",{children:["Quality: ",s.catalyst.qualityScore,"/10"]}),(0,a.jsxs)("span",{children:["Source: ",s.catalyst.source]}),(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)(eq,{className:"h-3 w-3"}),new Date(s.catalyst.announcementTime).toLocaleString()]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-3 bg-blue-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-sm text-blue-600 font-medium",children:"Entry"}),(0,a.jsx)("div",{className:"text-lg font-bold text-blue-800",children:eP(s.riskManagement.entryPrice)})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-red-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-sm text-red-600 font-medium",children:"Stop Loss"}),(0,a.jsx)("div",{className:"text-lg font-bold text-red-800",children:eP(s.riskManagement.stopLoss)})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-green-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-sm text-green-600 font-medium",children:"Target (3R)"}),(0,a.jsx)("div",{className:"text-lg font-bold text-green-800",children:eP(s.rewardPlanning.target3R)})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-purple-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-sm text-purple-600 font-medium",children:"R/R Ratio"}),(0,a.jsxs)("div",{className:"text-lg font-bold text-purple-800",children:[m,":1"]})]})]}),(0,a.jsx)("div",{className:"bg-gray-50 p-4 rounded-lg",children:(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Position Size:"}),(0,a.jsxs)("div",{className:"font-semibold",children:[s.riskManagement.positionSize," shares"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Risk Amount:"}),(0,a.jsx)("div",{className:"font-semibold text-red-600",children:eP(u)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Potential Profit (3R):"}),(0,a.jsx)("div",{className:"font-semibold text-green-600",children:eP(h)})]})]})}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-slate-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(x,{className:"h-4 w-4 text-slate-600"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"Technical Gate:"}),(0,a.jsxs)(eL,{variant:"A"===s.technicalGate.overallGrade?"default":"secondary",children:["Grade ",s.technicalGate.overallGrade]}),(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:["(",s.technicalGate.gateScore,"/100)"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[s.technicalGate.aboveSMA200&&(0,a.jsx)(e$,{className:"h-3 w-3 text-green-500"}),s.technicalGate.aboveEMA8&&(0,a.jsx)(e$,{className:"h-3 w-3 text-green-500"}),s.technicalGate.dailyTrendConfirmed&&(0,a.jsx)(e$,{className:"h-3 w-3 text-green-500"})]})]}),s.entryTrigger&&(0,a.jsxs)("div",{className:"p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)(eK,{className:"h-4 w-4 text-yellow-600"}),(0,a.jsx)("span",{className:"font-medium text-yellow-800",children:"Entry Trigger"}),(0,a.jsx)(eL,{variant:"outline",className:"text-xs",children:s.entryTrigger.urgency.replace("_"," ")})]}),(0,a.jsxs)("p",{className:"text-sm text-yellow-700",children:[s.entryTrigger.entrySignalType.replace("_"," ").toUpperCase()," at ",eP(s.entryTrigger.entryPrice)]})]}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-2 text-xs",children:Object.entries(s.validationChecks).map(e=>{let[t,s]=e;return(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[s?(0,a.jsx)(e$,{className:"h-3 w-3 text-green-500"}):(0,a.jsx)(eW,{className:"h-3 w-3 text-red-500"}),(0,a.jsx)("span",{className:s?"text-green-700":"text-red-700",children:t.replace(/([A-Z])/g," $1").toLowerCase()})]},t)})}),(0,a.jsxs)("div",{className:"flex items-center gap-2 pt-2",children:[(0,a.jsxs)(eE,{onClick:()=>null==l?void 0:l(s),className:"flex-1 bg-blue-600 hover:bg-blue-700",disabled:!s.validationChecks.noExclusionFlags,children:[(0,a.jsx)(eK,{className:"h-4 w-4 mr-2"}),"Execute Trade"]}),(0,a.jsxs)(eE,{variant:"outline",onClick:()=>null==n?void 0:n(s.symbol),className:"flex-1",children:[(0,a.jsx)(eQ,{className:"h-4 w-4 mr-2"}),"View Chart"]}),!s.entryTrigger&&(0,a.jsxs)(eE,{variant:"outline",onClick:()=>null==i?void 0:i(s.symbol,s.gapScan.preMarketHigh),className:"flex-1",children:[(0,a.jsx)(x,{className:"h-4 w-4 mr-2"}),"Entry Trigger"]})]}),(0,a.jsx)(eE,{variant:"ghost",onClick:()=>o(!c),className:"w-full text-sm",children:c?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(tK,{className:"h-4 w-4 mr-2"}),"Hide Details"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(t$,{className:"h-4 w-4 mr-2"}),"Show Details"]})}),c&&(0,a.jsxs)("div",{className:"space-y-3 pt-3 border-t",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-2",children:"Scale-out Plan"}),(0,a.jsx)("div",{className:"space-y-1 text-sm",children:s.rewardPlanning.scaleOutPlan.map((e,t)=>(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("span",{children:["At ",e.level,"R (",eP(s.riskManagement.entryPrice+s.riskManagement.riskPerShare*e.level),"):"]}),(0,a.jsxs)("span",{className:"font-medium",children:[e.percentage,"%"]})]},t))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-2",children:"Key Technical Levels"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[(0,a.jsxs)("div",{children:["SMA200: ",eP(s.technicalGate.keyTechnicalLevels.sma200)]}),(0,a.jsxs)("div",{children:["EMA8: ",eP(s.technicalGate.keyTechnicalLevels.ema8)]}),(0,a.jsxs)("div",{children:["VWAP: ",eP(s.technicalGate.keyTechnicalLevels.vwap)]}),(0,a.jsxs)("div",{children:["Prev High: ",eP(s.technicalGate.keyTechnicalLevels.previousHigh)]})]})]}),s.exclusionReasons.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-2 text-red-600",children:"Exclusion Reasons"}),(0,a.jsx)("ul",{className:"text-sm text-red-600 space-y-1",children:s.exclusionReasons.map((e,t)=>(0,a.jsxs)("li",{className:"flex items-center gap-1",children:[(0,a.jsx)(eW,{className:"h-3 w-3"}),e]},t))})]})]})]})]})}function tY(e,t,s){return t in e?Object.defineProperty(e,t,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[t]=s,e}let tJ=new class{subscribe(e){return this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);t>-1&&this.subscribers.splice(t,1)}}addAlert(e){let t={...e,id:this.generateAlertId(),timestamp:new Date().toISOString(),read:!1};this.alerts.unshift(t),this.alerts.length>100&&(this.alerts=this.alerts.slice(0,100)),("high"===t.priority||"critical"===t.priority)&&this.playAlertSound(),"critical"===t.priority&&this.showBrowserNotification(t),this.notifySubscribers()}markAsRead(e){let t=this.alerts.find(t=>t.id===e);t&&(t.read=!0,this.notifySubscribers())}markAllAsRead(){this.alerts.forEach(e=>e.read=!0),this.notifySubscribers()}removeAlert(e){this.alerts=this.alerts.filter(t=>t.id!==e),this.notifySubscribers()}clearAllAlerts(){this.alerts=[],this.notifySubscribers()}getAlerts(){return[...this.alerts]}getUnreadCount(){return this.alerts.filter(e=>!e.read).length}createCatalystAlert(e){let t="tier_1"===e.tier?"high":"tier_2"===e.tier?"medium":"low";this.addAlert({type:"new_catalyst",symbol:e.symbol,title:"New ".concat(e.tier.replace("_"," ").toUpperCase()," Catalyst: ").concat(e.symbol),message:e.title,priority:t,data:{catalyst:e},actionable:!0,actions:[{id:"view_details",label:"View Details",type:"view_chart",data:{symbol:e.symbol}}]})}createPerfectPickAlert(e){this.addAlert({type:"perfect_pick_found",symbol:e.symbol,title:"Perfect-Pick Setup Found: ".concat(e.symbol),message:"Grade ".concat(e.setupGrade," setup with ").concat(e.catalyst.type.replace(/_/g," ")," catalyst"),priority:e.setupGrade.startsWith("A")?"high":"medium",data:{setup:e},actionable:!0,actions:[{id:"execute_trade",label:"Execute Trade",type:"execute_trade",data:{setup:e}},{id:"view_chart",label:"View Chart",type:"view_chart",data:{symbol:e.symbol}}]})}createGapAlert(e){let t=e.gapPercent>10?"high":e.gapPercent>5?"medium":"low";this.addAlert({type:"pre_market_gap",symbol:e.symbol,title:"Pre-Market Gap: ".concat(e.symbol),message:"".concat(e.gapPercent.toFixed(1),"% gap with ").concat(e.catalyst?"catalyst":"no catalyst"),priority:t,data:{gapScan:e},actionable:e.meetsAllCriteria,actions:e.meetsAllCriteria?[{id:"analyze_setup",label:"Analyze Setup",type:"view_chart",data:{symbol:e.symbol}}]:void 0})}createPMHBreakAlert(e,t,s){this.addAlert({type:"pmh_break",symbol:e,title:"PMH Break: ".concat(e),message:"Price ".concat(t.toFixed(2)," broke above PMH ").concat(s.toFixed(2)),priority:"high",data:{currentPrice:t,pmh:s},actionable:!0,actions:[{id:"execute_entry",label:"Execute Entry",type:"execute_trade",data:{symbol:e,entryPrice:t}}]})}createStopLossAlert(e,t,s){this.addAlert({type:"stop_loss_hit",symbol:e,title:"Stop Loss Hit: ".concat(e),message:"Price ".concat(t.toFixed(2)," hit stop loss ").concat(s.toFixed(2)),priority:"critical",data:{currentPrice:t,stopLoss:s},actionable:!0,actions:[{id:"exit_position",label:"Exit Position",type:"execute_trade",data:{symbol:e,action:"sell",price:t}}]})}createProfitTargetAlert(e,t,s,a){this.addAlert({type:"profit_target_hit",symbol:e,title:"Profit Target Hit: ".concat(e),message:"Price ".concat(t.toFixed(2)," hit ").concat(a,"R target ").concat(s.toFixed(2)),priority:"high",data:{currentPrice:t,target:s,rLevel:a},actionable:!0,actions:[{id:"take_profit",label:"Take Profit",type:"take_profit",data:{symbol:e,price:t,rLevel:a}}]})}createVolumeSpikeAlert(e,t,s){let a=t/s;this.addAlert({type:"volume_spike",symbol:e,title:"Volume Spike: ".concat(e),message:"Volume ".concat(a.toFixed(1),"x above average"),priority:a>5?"high":"medium",data:{currentVolume:t,avgVolume:s,volumeRatio:a},actionable:!1})}monitorPerfectPickSetups(e){e.forEach(e=>{!this.alerts.find(t=>{var s,a;return"perfect_pick_found"===t.type&&t.symbol===e.symbol&&(null==(a=t.data)||null==(s=a.setup)?void 0:s.createdAt)===e.createdAt})&&e.overallScore>=80&&this.createPerfectPickAlert(e)})}monitorGapScans(e){e.forEach(e=>{!this.alerts.find(t=>"pre_market_gap"===t.type&&t.symbol===e.symbol&&6e4>Math.abs(new Date(t.timestamp).getTime()-new Date(e.scanTime).getTime()))&&e.gapPercent>=3&&this.createGapAlert(e)})}generateAlertId(){return"alert_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9))}notifySubscribers(){this.subscribers.forEach(e=>e([...this.alerts]))}playAlertSound(){this.alertSound&&this.alertSound.play().catch(e=>{console.log("Could not play alert sound:",e)})}showBrowserNotification(e){"Notification"in window&&"granted"===Notification.permission?new Notification(e.title,{body:e.message,icon:"/favicon.ico",tag:e.symbol}):"Notification"in window&&"denied"!==Notification.permission&&Notification.requestPermission().then(t=>{"granted"===t&&new Notification(e.title,{body:e.message,icon:"/favicon.ico",tag:e.symbol})})}constructor(){tY(this,"alerts",[]),tY(this,"subscribers",[]),tY(this,"alertSound",null),this.alertSound=new Audio("/alert-sound.mp3"),this.alertSound.volume=.5}};function tZ(e){let{accountSize:t=1e5,riskPercent:s=2}=e,[l,n]=(0,r.useState)([]),[i,c]=(0,r.useState)([]),[o,m]=(0,r.useState)([]),[h,u]=(0,r.useState)(!1),[f,b]=(0,r.useState)("perfect-pick"),[j,v]=(0,r.useState)(""),[y,N]=(0,r.useState)(null);(0,r.useEffect)(()=>{let e=setInterval(()=>{let e=new Date().getHours();e>=4&&e<21&&A()},9e5);return()=>clearInterval(e)},[]);let w=async()=>{u(!0);try{let e=await fetch("/api/scanner/perfect-pick?accountSize=".concat(t,"&riskPercent=").concat(s,"&limit=20")),a=await e.json();a.success&&(n(a.data.setups),N(a.data.summary),v(new Date().toLocaleTimeString()),tJ.monitorPerfectPickSetups(a.data.setups))}catch(e){console.error("Error running Perfect-Pick scan:",e)}finally{u(!1)}},k=async()=>{u(!0);try{let e=await fetch("/api/scanner/gap-scan?minGap=3&maxGap=15&limit=30"),t=await e.json();t.success&&(c(t.data.results),v(new Date().toLocaleTimeString()),tJ.monitorGapScans(t.data.results))}catch(e){console.error("Error running gap scan:",e)}finally{u(!1)}},S=async()=>{u(!0);try{let e=i.slice(0,10).map(e=>e.symbol).join(","),t=await fetch("/api/catalyst/detect?symbols=".concat(e,"&minQuality=6&limit=50")),s=await t.json();s.success&&(m(s.data.catalysts),v(new Date().toLocaleTimeString()),s.data.catalysts.filter(e=>e.qualityScore>=7).forEach(e=>tJ.createCatalystAlert(e)))}catch(e){console.error("Error running catalyst scan:",e)}finally{u(!1)}},A=()=>{switch(f){case"perfect-pick":w();break;case"gap-scan":k();break;case"catalysts":S()}},C=e=>"tier_1"===e.tier?"bg-green-500":"tier_2"===e.tier?"bg-yellow-500":"bg-gray-500";return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold",children:"Event-Driven Scanner"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Perfect-Pick Trading System with Catalyst Detection"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[j&&(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Last scan: ",j]}),(0,a.jsxs)(eE,{onClick:A,disabled:h,variant:"outline",size:"sm",children:[h?(0,a.jsx)(g,{className:"h-4 w-4 animate-spin"}):(0,a.jsx)(tH,{className:"h-4 w-4"}),"Refresh"]})]})]}),y&&(0,a.jsxs)(ez,{children:[(0,a.jsx)(eR,{children:(0,a.jsxs)(eI,{className:"flex items-center gap-2",children:[(0,a.jsx)(tq,{className:"h-5 w-5"}),"Scan Summary"]})}),(0,a.jsx)(eF,{children:(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:y.totalSetups||0}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Total Setups"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:y.avgScore||0}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Avg Score"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:eM(y.avgGap||0)}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Avg Gap"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:Object.keys(y.catalystBreakdown||{}).length}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Catalyst Types"})]})]})})]}),(0,a.jsxs)(tz,{value:f,onValueChange:b,children:[(0,a.jsxs)(tU,{className:"grid w-full grid-cols-3",children:[(0,a.jsxs)(tB,{value:"perfect-pick",className:"flex items-center gap-2",children:[(0,a.jsx)(tW,{className:"h-4 w-4"}),"Perfect-Pick"]}),(0,a.jsxs)(tB,{value:"gap-scan",className:"flex items-center gap-2",children:[(0,a.jsx)(d,{className:"h-4 w-4"}),"Gap Scanner"]}),(0,a.jsxs)(tB,{value:"catalysts",className:"flex items-center gap-2",children:[(0,a.jsx)(p,{className:"h-4 w-4"}),"Catalysts"]})]}),(0,a.jsxs)(tV,{value:"perfect-pick",className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Perfect-Pick Setups"}),(0,a.jsxs)(eE,{onClick:w,disabled:h,children:[h?(0,a.jsx)(g,{className:"h-4 w-4 animate-spin mr-2"}):(0,a.jsx)(x,{className:"h-4 w-4 mr-2"}),"Scan Perfect-Picks"]})]}),(0,a.jsx)("div",{className:"space-y-6",children:l.map(e=>(0,a.jsx)(tQ,{setup:e,onExecuteTrade:e=>{console.log("Execute trade for:",e.symbol)},onViewChart:e=>{console.log("View chart for:",e)},onGenerateEntryTrigger:async(e,t)=>{console.log("Generate entry trigger for:",e);try{let s=await fetch("/api/scanner/perfect-pick",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"generate_entry_trigger",data:{symbol:e,preMarketHigh:t}})}),a=await s.json();a.success&&console.log("Entry trigger generated:",a.data.entryTrigger)}catch(e){console.error("Error generating entry trigger:",e)}}},e.symbol))}),0===l.length&&!h&&(0,a.jsx)(ez,{children:(0,a.jsxs)(eF,{className:"text-center py-8",children:[(0,a.jsx)(x,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:'No Perfect-Pick setups found. Click "Scan Perfect-Picks" to search for opportunities.'})]})})]}),(0,a.jsxs)(tV,{value:"gap-scan",className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Pre-Market Gap Scanner"}),(0,a.jsxs)(eE,{onClick:k,disabled:h,children:[h?(0,a.jsx)(g,{className:"h-4 w-4 animate-spin mr-2"}):(0,a.jsx)(d,{className:"h-4 w-4 mr-2"}),"Scan Gaps"]})]}),(0,a.jsx)("div",{className:"grid gap-3",children:i.map(e=>(0,a.jsx)(ez,{className:"hover:shadow-md transition-shadow",children:(0,a.jsxs)(eF,{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-semibold",children:e.symbol}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:e.name})]}),e.catalyst&&(0,a.jsx)(eL,{className:C(e.catalyst),children:e.catalyst.type.replace(/_/g," ")}),e.meetsAllCriteria&&(0,a.jsx)(eL,{className:"bg-green-500",children:"Perfect-Pick"})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"font-bold",children:eP(e.price)}),(0,a.jsxs)("div",{className:"text-sm text-green-600",children:["+",eM(e.gapPercent)]})]})]}),(0,a.jsxs)("div",{className:"mt-3 grid grid-cols-3 gap-4 text-xs text-muted-foreground",children:[(0,a.jsxs)("div",{children:["Vol: ",(e.preMarketVolume/1e3).toFixed(0),"K"]}),(0,a.jsxs)("div",{children:["MCap: $",(e.marketCap/1e9).toFixed(1),"B"]}),(0,a.jsxs)("div",{children:["Sector: ",e.sector]})]})]})},e.symbol))}),0===i.length&&!h&&(0,a.jsx)(ez,{children:(0,a.jsxs)(eF,{className:"text-center py-8",children:[(0,a.jsx)(d,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:'No gap opportunities found. Click "Scan Gaps" to search for pre-market movers.'})]})})]}),(0,a.jsxs)(tV,{value:"catalysts",className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Real-Time Catalysts"}),(0,a.jsxs)(eE,{onClick:S,disabled:h,children:[h?(0,a.jsx)(g,{className:"h-4 w-4 animate-spin mr-2"}):(0,a.jsx)(p,{className:"h-4 w-4 mr-2"}),"Scan Catalysts"]})]}),(0,a.jsx)("div",{className:"grid gap-3",children:o.map(e=>(0,a.jsx)(ez,{className:"hover:shadow-md transition-shadow",children:(0,a.jsx)(eF,{className:"p-4",children:(0,a.jsx)("div",{className:"flex items-start justify-between",children:(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)("span",{className:"font-semibold",children:e.symbol}),(e=>{switch(e){case"bullish":return(0,a.jsx)(d,{className:"h-4 w-4 text-green-500"});case"bearish":return(0,a.jsx)(eW,{className:"h-4 w-4 text-red-500"});default:return(0,a.jsx)(tq,{className:"h-4 w-4 text-gray-500"})}})(e.impact),(0,a.jsx)(eL,{className:C(e),children:e.tier.replace("_"," ").toUpperCase()}),(0,a.jsx)(eL,{variant:"outline",className:"text-xs",children:e.freshness})]}),(0,a.jsx)("h4",{className:"font-medium mb-1",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-xs text-muted-foreground",children:[(0,a.jsxs)("span",{children:["Quality: ",e.qualityScore,"/10"]}),(0,a.jsxs)("span",{children:["Source: ",e.source]}),(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)(eq,{className:"h-3 w-3"}),new Date(e.announcementTime).toLocaleString()]})]})]})})})},e.id))}),0===o.length&&!h&&(0,a.jsx)(ez,{children:(0,a.jsxs)(eF,{className:"text-center py-8",children:[(0,a.jsx)(p,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:'No catalysts found. Click "Scan Catalysts" to detect market-moving events.'})]})})]})]})]})}let tX=o("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]),t0=o("bell-ring",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M22 8c0-2.3-.8-4.3-2-6",key:"5bb3ad"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}],["path",{d:"M4 2C2.8 3.7 2 5.7 2 8",key:"tap9e0"}]]),t1=o("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function t2(e){let{className:t}=e,[s,l]=(0,r.useState)([]),[n,i]=(0,r.useState)(!1),[c,o]=(0,r.useState)(0);(0,r.useEffect)(()=>{let e=tJ.subscribe(e=>{l(e),o(tJ.getUnreadCount())});return l(tJ.getAlerts()),o(tJ.getUnreadCount()),e},[]);let m=async(e,t)=>{var s;let a=null==(s=e.actions)?void 0:s.find(e=>e.id===t);if(a){switch(a.type){case"execute_trade":console.log("Execute trade:",a.data);break;case"view_chart":console.log("View chart:",a.data);break;case"take_profit":console.log("Take profit:",a.data);break;case"update_stop":console.log("Update stop:",a.data);break;case"dismiss":tJ.removeAlert(e.id)}tJ.markAsRead(e.id)}};return(0,a.jsxs)("div",{className:"relative ".concat(t),children:[(0,a.jsxs)(eE,{variant:"outline",size:"sm",onClick:()=>i(!n),className:"relative",children:[c>0?(0,a.jsx)(t0,{className:"h-4 w-4"}):(0,a.jsx)(tX,{className:"h-4 w-4"}),c>0&&(0,a.jsx)(eL,{className:"absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs bg-red-500",children:c>99?"99+":c})]}),n&&(0,a.jsxs)("div",{className:"absolute right-0 top-full mt-2 w-96 max-h-96 overflow-y-auto bg-white border rounded-lg shadow-lg z-50",children:[(0,a.jsx)("div",{className:"p-4 border-b",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"font-semibold",children:"Alerts"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[c>0&&(0,a.jsx)(eE,{variant:"ghost",size:"sm",onClick:()=>tJ.markAllAsRead(),children:"Mark all read"}),(0,a.jsx)(eE,{variant:"ghost",size:"sm",onClick:()=>i(!1),children:(0,a.jsx)(t1,{className:"h-4 w-4"})})]})]})}),(0,a.jsx)("div",{className:"max-h-80 overflow-y-auto",children:0===s.length?(0,a.jsxs)("div",{className:"p-8 text-center text-muted-foreground",children:[(0,a.jsx)(tX,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{children:"No alerts yet"})]}):s.map(e=>(0,a.jsx)("div",{className:"p-4 border-b hover:bg-gray-50 ".concat(e.read?"":"bg-blue-50/50"),onClick:()=>!e.read&&tJ.markAsRead(e.id),children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"p-2 rounded-full ".concat((e=>{switch(e){case"critical":return"text-red-600 bg-red-50 border-red-200";case"high":return"text-orange-600 bg-orange-50 border-orange-200";case"medium":return"text-yellow-600 bg-yellow-50 border-yellow-200";case"low":return"text-blue-600 bg-blue-50 border-blue-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}})(e.priority)),children:(e=>{switch(e){case"new_catalyst":return(0,a.jsx)(p,{className:"h-4 w-4"});case"perfect_pick_found":return(0,a.jsx)(x,{className:"h-4 w-4"});case"pre_market_gap":return(0,a.jsx)(d,{className:"h-4 w-4"});case"pmh_break":case"volume_spike":return(0,a.jsx)(tq,{className:"h-4 w-4"});case"stop_loss_hit":return(0,a.jsx)(eW,{className:"h-4 w-4"});case"profit_target_hit":return(0,a.jsx)(eH,{className:"h-4 w-4"});case"entry_trigger":return(0,a.jsx)(eK,{className:"h-4 w-4"});default:return(0,a.jsx)(tX,{className:"h-4 w-4"})}})(e.type)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)("h4",{className:"font-medium text-sm truncate",children:e.title}),!e.read&&(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"})]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:e.message}),(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{className:"flex items-center gap-2 text-xs text-muted-foreground",children:[(0,a.jsx)(eq,{className:"h-3 w-3"}),(e=>{let t=new Date,s=new Date(e),a=Math.floor((t.getTime()-s.getTime())/6e4),r=Math.floor(a/60);return a<1?"Just now":a<60?"".concat(a,"m ago"):r<24?"".concat(r,"h ago"):s.toLocaleDateString()})(e.timestamp),(0,a.jsx)(eL,{variant:"outline",className:"text-xs",children:e.priority})]})}),e.actionable&&e.actions&&e.actions.length>0&&(0,a.jsx)("div",{className:"flex items-center gap-2 mt-2",children:e.actions.map(t=>(0,a.jsxs)(eE,{variant:"outline",size:"sm",onClick:s=>{s.stopPropagation(),m(e,t.id)},className:"text-xs",children:["execute_trade"===t.type&&(0,a.jsx)(eK,{className:"h-3 w-3 mr-1"}),"view_chart"===t.type&&(0,a.jsx)(eQ,{className:"h-3 w-3 mr-1"}),"take_profit"===t.type&&(0,a.jsx)(eH,{className:"h-3 w-3 mr-1"}),t.label]},t.id))})]})]})},e.id))}),s.length>0&&(0,a.jsx)("div",{className:"p-4 border-t",children:(0,a.jsx)(eE,{variant:"ghost",size:"sm",onClick:()=>{tJ.clearAllAlerts(),i(!1)},className:"w-full text-sm",children:"Clear all alerts"})})]})]})}let t4=o("settings",[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),t5=o("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),t3=o("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);function t6(){let[e,t]=(0,r.useState)(null),[s,l]=(0,r.useState)(!1),[n,i]=(0,r.useState)(null);(0,r.useEffect)(()=>{c()},[]);let c=async()=>{l(!0),i(null);try{let e=await fetch("/api/ai?action=status"),s=await e.json();t(s)}catch(e){console.error("Error checking AI status:",e),i("Unable to connect to AI service")}finally{l(!1)}},o=async()=>{l(!0),i(null);try{let e=await fetch("/api/ai?action=model");if(e.ok){let{model:s}=await e.json();t(e=>e?{...e,model:s}:null),alert("AI Connection Successful!\nUsing model: ".concat(s))}else throw Error("Failed to connect to AI service")}catch(e){console.error("Error testing AI connection:",e),i("AI connection test failed")}finally{l(!1)}};return s&&!e?(0,a.jsx)(ez,{className:"bg-slate-800/50 border-slate-700",children:(0,a.jsx)(eF,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsx)(g,{className:"h-6 w-6 animate-spin text-blue-400 mr-2"}),(0,a.jsx)("span",{className:"text-slate-300",children:"Checking AI configuration..."})]})})}):(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)(ez,{className:"bg-slate-800/50 border-slate-700",children:[(0,a.jsx)(eR,{children:(0,a.jsxs)(eI,{className:"text-white flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(u,{className:"mr-2 h-5 w-5 text-blue-400"}),"AI Configuration"]}),(0,a.jsxs)(eE,{size:"sm",variant:"outline",onClick:c,disabled:s,className:"text-slate-300 hover:text-white",children:[s?(0,a.jsx)(g,{className:"h-4 w-4 animate-spin mr-1"}):(0,a.jsx)(t4,{className:"h-4 w-4 mr-1"}),"Refresh Status"]})]})}),(0,a.jsxs)(eF,{children:[n&&(0,a.jsx)("div",{className:"mb-4 p-3 bg-red-900/20 border border-red-500/30 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center text-red-400",children:[(0,a.jsx)(eW,{className:"h-4 w-4 mr-2"}),n]})}),e&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-4 bg-slate-700/30 rounded-lg",children:[(0,a.jsx)("div",{className:"flex items-center justify-center mb-2",children:e.enabled?(0,a.jsx)(e$,{className:"h-6 w-6 text-green-400"}):(0,a.jsx)(t5,{className:"h-6 w-6 text-red-400"})}),(0,a.jsx)("div",{className:"text-sm text-slate-400",children:"Status"}),(0,a.jsx)("div",{className:"font-semibold ".concat(e.enabled?"text-green-400":"text-red-400"),children:e.enabled?"Enabled":"Disabled"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-slate-700/30 rounded-lg",children:[(0,a.jsx)("div",{className:"flex items-center justify-center mb-2",children:(0,a.jsx)(eY,{className:"h-6 w-6 text-blue-400"})}),(0,a.jsx)("div",{className:"text-sm text-slate-400",children:"Model"}),(0,a.jsx)("div",{className:"font-semibold text-white",children:e.model||"Not Available"})]}),(0,a.jsx)("div",{className:"text-center p-4 bg-slate-700/30 rounded-lg",children:(0,a.jsxs)(eE,{onClick:o,disabled:!e.enabled||s,className:"w-full bg-blue-600 hover:bg-blue-700",children:[s?(0,a.jsx)(g,{className:"h-4 w-4 animate-spin mr-2"}):(0,a.jsx)(e$,{className:"h-4 w-4 mr-2"}),"Test Connection"]})})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-white mb-3 flex items-center",children:[(0,a.jsx)(t4,{className:"h-5 w-5 mr-2"}),"Available Features"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"p-3 bg-slate-700/20 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-white font-medium",children:"Market Commentary"}),(0,a.jsx)(eL,{className:e.features.marketCommentary&&e.enabled?"bg-green-500/20 text-green-400":"bg-gray-500/20 text-gray-400",children:e.features.marketCommentary&&e.enabled?"Active":"Inactive"})]}),(0,a.jsx)("p",{className:"text-xs text-slate-400",children:"AI-powered market analysis and commentary on scan results"})]}),(0,a.jsxs)("div",{className:"p-3 bg-slate-700/20 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-white font-medium",children:"Risk Assessment"}),(0,a.jsx)(eL,{className:e.features.riskAssessment&&e.enabled?"bg-green-500/20 text-green-400":"bg-gray-500/20 text-gray-400",children:e.features.riskAssessment&&e.enabled?"Active":"Inactive"})]}),(0,a.jsx)("p",{className:"text-xs text-slate-400",children:"Individual setup risk analysis with AI-generated recommendations"})]}),(0,a.jsxs)("div",{className:"p-3 bg-slate-700/20 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-white font-medium",children:"Trading Recommendations"}),(0,a.jsx)(eL,{className:e.features.tradingRecommendations&&e.enabled?"bg-green-500/20 text-green-400":"bg-gray-500/20 text-gray-400",children:e.features.tradingRecommendations&&e.enabled?"Active":"Inactive"})]}),(0,a.jsx)("p",{className:"text-xs text-slate-400",children:"Personalized trading recommendations based on your preferences"})]})]})]}),!e.enabled&&(0,a.jsxs)("div",{className:"p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg",children:[(0,a.jsxs)("h4",{className:"text-white font-semibold mb-2 flex items-center",children:[(0,a.jsx)(t3,{className:"h-4 w-4 mr-2"}),"Enable AI Features"]}),(0,a.jsxs)("div",{className:"text-sm text-slate-300 space-y-2",children:[(0,a.jsx)("p",{children:"To enable AI-powered insights:"}),(0,a.jsxs)("ol",{className:"list-decimal list-inside space-y-1 ml-4",children:[(0,a.jsx)("li",{children:"Ensure OPENAI_API_KEY is set in your .env.local file"}),(0,a.jsx)("li",{children:"Set OPENAI_ENABLED=true in your environment variables"}),(0,a.jsx)("li",{children:"Restart the application"}),(0,a.jsx)("li",{children:'Click "Refresh Status" to verify the configuration'})]})]})]}),e.enabled&&(0,a.jsxs)("div",{className:"p-4 bg-green-900/20 border border-green-500/30 rounded-lg",children:[(0,a.jsxs)("h4",{className:"text-green-400 font-semibold mb-2 flex items-center",children:[(0,a.jsx)(e$,{className:"h-4 w-4 mr-2"}),"AI Features Active"]}),(0,a.jsx)("p",{className:"text-sm text-slate-300",children:"Your SwingTrader AI is enhanced with GPT-4o intelligence. You'll see AI-powered insights in your trading setup cards and market analysis sections."})]})]})]})]})})}function t7(){let[e,t]=(0,r.useState)("SPY"),[s,l]=(0,r.useState)(""),[n,i]=(0,r.useState)(!1),[c,o]=(0,r.useState)(null),[j,v]=(0,r.useState)(null),[y,N]=(0,r.useState)(null),[w,k]=(0,r.useState)("event-driven"),S=async e=>{i(!0),N(null),o(null),v(null);try{let[t,s]=await Promise.all([fetch("/api/stocks/quote/".concat(e)),fetch("/api/analysis/swing/".concat(e))]);if(!t.ok||!s.ok)throw Error("Failed to fetch data");let[a,r]=await Promise.all([t.json(),s.json()]);v(a),o(r)}catch(e){N("Failed to analyze stock. Please try again."),console.error("Analysis error:",e)}finally{i(!1)}};return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900",children:[(0,a.jsx)("header",{className:"border-b border-slate-800 bg-slate-900/50 backdrop-blur-sm",children:(0,a.jsx)("div",{className:"container mx-auto px-4 py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(u,{className:"h-8 w-8 text-blue-400"}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-white",children:"SwingTrader AI"})]}),(0,a.jsxs)("nav",{className:"hidden md:flex items-center space-x-6",children:[(0,a.jsx)("button",{onClick:()=>k("strategies"),className:"transition-colors ".concat("strategies"===w?"text-white":"text-slate-300 hover:text-white"),children:"Pro Strategies"}),(0,a.jsx)("button",{onClick:()=>k("scanner"),className:"transition-colors ".concat("scanner"===w?"text-white":"text-slate-300 hover:text-white"),children:"Basic Scanner"}),(0,a.jsx)("button",{onClick:()=>k("individual"),className:"transition-colors ".concat("individual"===w?"text-white":"text-slate-300 hover:text-white"),children:"Individual Analysis"}),(0,a.jsx)("button",{onClick:()=>k("trading"),className:"transition-colors ".concat("trading"===w?"text-white":"text-slate-300 hover:text-white"),children:"Paper Trading"}),(0,a.jsx)(t2,{}),(0,a.jsx)(eE,{variant:"outline",className:"border-blue-400 text-blue-400 hover:bg-blue-400 hover:text-white",children:"Sign In"})]})]})})}),(0,a.jsx)("section",{className:"py-20 px-4",children:(0,a.jsxs)("div",{className:"container mx-auto text-center",children:[(0,a.jsx)("h2",{className:"text-5xl font-bold text-white mb-6",children:"AI-Powered Swing Trading Analysis"}),(0,a.jsx)("p",{className:"text-xl text-slate-300 mb-8 max-w-3xl mx-auto",children:"Professional swing trading strategies with automated scanning, precise entry/exit rules, and risk management based on proven methodologies."}),(0,a.jsx)("div",{className:"flex justify-center mb-8",children:(0,a.jsxs)("div",{className:"bg-slate-800/50 rounded-lg p-1 flex",children:[(0,a.jsxs)("button",{onClick:()=>k("event-driven"),className:"px-6 py-3 rounded-md transition-all ".concat("event-driven"===w?"bg-blue-600 text-white":"text-slate-300 hover:text-white hover:bg-slate-700/50"),children:[(0,a.jsx)(x,{className:"inline mr-2 h-4 w-4"}),"Event-Driven"]}),(0,a.jsxs)("button",{onClick:()=>k("strategies"),className:"px-6 py-3 rounded-md transition-all ".concat("strategies"===w?"bg-blue-600 text-white":"text-slate-300 hover:text-white hover:bg-slate-700/50"),children:[(0,a.jsx)(p,{className:"inline mr-2 h-4 w-4"}),"Pro Strategies"]}),(0,a.jsxs)("button",{onClick:()=>k("scanner"),className:"px-6 py-3 rounded-md transition-all ".concat("scanner"===w?"bg-blue-600 text-white":"text-slate-300 hover:text-white hover:bg-slate-700/50"),children:[(0,a.jsx)(b,{className:"inline mr-2 h-4 w-4"}),"Basic Scanner"]}),(0,a.jsxs)("button",{onClick:()=>k("individual"),className:"px-6 py-3 rounded-md transition-all ".concat("individual"===w?"bg-blue-600 text-white":"text-slate-300 hover:text-white hover:bg-slate-700/50"),children:[(0,a.jsx)(f,{className:"inline mr-2 h-4 w-4"}),"Individual Analysis"]}),(0,a.jsxs)("button",{onClick:()=>k("trading"),className:"px-6 py-3 rounded-md transition-all ".concat("trading"===w?"bg-blue-600 text-white":"text-slate-300 hover:text-white hover:bg-slate-700/50"),children:[(0,a.jsx)(d,{className:"inline mr-2 h-4 w-4"}),"Trading"]}),(0,a.jsxs)("button",{onClick:()=>k("ai"),className:"px-6 py-3 rounded-md transition-all ".concat("ai"===w?"bg-blue-600 text-white":"text-slate-300 hover:text-white hover:bg-slate-700/50"),children:[(0,a.jsx)(u,{className:"inline mr-2 h-4 w-4"}),"AI Config"]})]})}),"event-driven"===w?(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h3",{className:"text-lg text-slate-300 mb-4",children:"Event-Driven Catalyst Detection & Perfect-Pick Trading"}),(0,a.jsx)("p",{className:"text-slate-400 mb-6",children:"Advanced catalyst detection with pre-market gap scanning, technical gate analysis, and Perfect-Pick setups"})]}):"strategies"===w?(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h3",{className:"text-lg text-slate-300 mb-4",children:"Professional Swing Trading Strategies"}),(0,a.jsx)("p",{className:"text-slate-400 mb-6",children:"Overnight Momentum & Technical Breakout strategies with precise entry/exit rules and position sizing"})]}):"scanner"===w?(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h3",{className:"text-lg text-slate-300 mb-4",children:"Basic Swing Trading Scanner"}),(0,a.jsx)("p",{className:"text-slate-400 mb-6",children:"General swing trading analysis with technical indicators and trend detection"})]}):(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h3",{className:"text-lg text-slate-300 mb-4",children:"Individual Stock Analysis"}),(0,a.jsx)("div",{className:"flex flex-wrap justify-center gap-2 mb-6",children:["SPY","QQQ","AAPL","TSLA","NVDA","MSFT","AMZN","GOOGL"].map(s=>(0,a.jsx)(eE,{variant:e===s?"default":"outline",onClick:()=>{t(s),S(s)},disabled:n,className:e===s?"bg-blue-600 hover:bg-blue-700":"border-slate-600 text-slate-300 hover:bg-slate-800",children:s},s))}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),s.trim()&&(S(s.toUpperCase()),t(s.toUpperCase()))},className:"flex justify-center gap-2 mb-6",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(f,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)("input",{type:"text",placeholder:"Enter symbol (e.g., AAPL)",value:s,onChange:e=>l(e.target.value),className:"pl-10 pr-4 py-2 bg-slate-800 border border-slate-600 rounded-md text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500",disabled:n})]}),(0,a.jsx)(eE,{type:"submit",disabled:n||!s.trim(),className:"bg-blue-600 hover:bg-blue-700",children:"Analyze"})]}),(0,a.jsx)(eE,{size:"lg",onClick:()=>S(e),disabled:n,className:"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3",children:n?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g,{className:"mr-2 h-5 w-5 animate-spin"}),"Analyzing ",e,"..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p,{className:"mr-2 h-5 w-5"}),"Get AI Analysis for ",e]})})]})]})}),"event-driven"===w&&(0,a.jsx)("section",{className:"py-12 px-4",children:(0,a.jsx)("div",{className:"container mx-auto",children:(0,a.jsx)(tZ,{accountSize:1e5,riskPercent:2})})}),"strategies"===w&&(0,a.jsx)("section",{className:"py-12 px-4",children:(0,a.jsx)("div",{className:"container mx-auto",children:(0,a.jsx)(e0,{autoScan:!0,accountSize:1e5})})}),"scanner"===w&&(0,a.jsx)("section",{className:"py-12 px-4",children:(0,a.jsx)("div",{className:"container mx-auto",children:(0,a.jsx)(eU,{autoScan:!1})})}),"trading"===w&&(0,a.jsx)("section",{className:"py-12 px-4",children:(0,a.jsxs)("div",{className:"container mx-auto",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-white mb-4",children:"Paper Trading"}),(0,a.jsx)("p",{className:"text-slate-400 mb-6",children:"Execute swing trades with Alpaca or Interactive Brokers paper trading accounts"})]}),(0,a.jsxs)("div",{className:"text-center text-slate-400",children:[(0,a.jsx)("h3",{className:"text-xl mb-4",children:"Paper Trading Interface"}),(0,a.jsx)("p",{children:"IBKR and Alpaca integration is ready!"}),(0,a.jsx)("p",{className:"mt-2",children:"Trading interface temporarily disabled due to component loading issue."}),(0,a.jsxs)("p",{className:"mt-2",children:["API endpoints are working: ",(0,a.jsx)("code",{children:"/api/trading"})]})]})]})}),"ai"===w&&(0,a.jsx)("section",{className:"py-12 px-4",children:(0,a.jsxs)("div",{className:"container mx-auto",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-white mb-4",children:"AI Configuration"}),(0,a.jsx)("p",{className:"text-slate-400 mb-6",children:"Configure and manage AI-powered features for enhanced trading analysis"})]}),(0,a.jsx)(t6,{})]})}),y&&(0,a.jsx)("section",{className:"py-8 px-4",children:(0,a.jsx)("div",{className:"container mx-auto",children:(0,a.jsx)(ez,{className:"bg-red-900/20 border-red-500/50",children:(0,a.jsx)(eF,{className:"p-6",children:(0,a.jsx)("p",{className:"text-red-300 text-center",children:y})})})})}),"individual"===w&&(j||c)&&(0,a.jsx)("section",{className:"py-12 px-4",children:(0,a.jsxs)("div",{className:"container mx-auto",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[j&&(0,a.jsxs)(ez,{className:"bg-slate-800/50 border-slate-700",children:[(0,a.jsx)(eR,{children:(0,a.jsxs)(eI,{className:"text-white flex items-center",children:[(0,a.jsx)(m,{className:"mr-2 h-5 w-5 text-blue-400"}),j.symbol," Quote"]})}),(0,a.jsx)(eF,{children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-slate-300",children:"Price:"}),(0,a.jsx)("span",{className:"text-white font-semibold",children:eP(j.price)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-slate-300",children:"Change:"}),(0,a.jsxs)("span",{className:j.change>=0?"text-green-400":"text-red-400",children:[eP(j.change)," (",eM(j.changePercent),")"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-slate-300",children:"Volume:"}),(0,a.jsx)("span",{className:"text-white",children:j.volume.toLocaleString()})]})]})})]}),c&&(0,a.jsxs)(ez,{className:"bg-slate-800/50 border-slate-700",children:[(0,a.jsx)(eR,{children:(0,a.jsxs)(eI,{className:"text-white flex items-center",children:[(0,a.jsx)(x,{className:"mr-2 h-5 w-5 text-green-400"}),"Trading Levels"]})}),(0,a.jsx)(eF,{children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-slate-300",children:"Entry:"}),(0,a.jsx)("span",{className:"text-white font-semibold",children:eP(c.entryPrice)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-slate-300",children:"Stop Loss:"}),(0,a.jsx)("span",{className:"text-red-400",children:eP(c.stopLoss)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-slate-300",children:"Take Profit:"}),(0,a.jsx)("span",{className:"text-green-400",children:eP(c.takeProfit)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-slate-300",children:"Risk/Reward:"}),(0,a.jsxs)("span",{className:"text-blue-400 font-semibold",children:[c.riskRewardRatio.toFixed(2),":1"]})]})]})})]}),c&&(0,a.jsxs)(ez,{className:"bg-slate-800/50 border-slate-700",children:[(0,a.jsx)(eR,{children:(0,a.jsxs)(eI,{className:"text-white flex items-center",children:[(0,a.jsx)(u,{className:"mr-2 h-5 w-5 text-purple-400"}),"AI Analysis"]})}),(0,a.jsx)(eF,{children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-slate-300",children:"Trend:"}),(0,a.jsx)("span",{className:"font-semibold ".concat("BULLISH"===c.trend?"text-green-400":"BEARISH"===c.trend?"text-red-400":"text-yellow-400"),children:c.trend})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-slate-300",children:"Confidence:"}),(0,a.jsxs)("span",{className:"text-white font-semibold",children:[c.confidence.toFixed(1),"%"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-slate-300",children:"Recommendation:"}),(0,a.jsx)("span",{className:"font-semibold ".concat(c.recommendation.includes("BUY")?"text-green-400":c.recommendation.includes("SELL")?"text-red-400":"text-yellow-400"),children:c.recommendation.replace("_"," ")})]})]})})]})]}),c&&(0,a.jsxs)(ez,{className:"mt-6 bg-slate-800/50 border-slate-700",children:[(0,a.jsx)(eR,{children:(0,a.jsxs)(eI,{className:"text-white flex items-center",children:[(0,a.jsx)(d,{className:"mr-2 h-5 w-5 text-orange-400"}),"Technical Indicators"]})}),(0,a.jsx)(eF,{children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:c.indicators.map((e,t)=>(0,a.jsxs)("div",{className:"p-4 bg-slate-700/50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h4",{className:"text-white font-medium",children:e.name}),(0,a.jsx)("span",{className:"px-2 py-1 rounded text-xs font-semibold ".concat("BUY"===e.signal?"bg-green-500/20 text-green-400":"SELL"===e.signal?"bg-red-500/20 text-red-400":"bg-yellow-500/20 text-yellow-400"),children:e.signal})]}),(0,a.jsx)("p",{className:"text-slate-300 text-sm",children:e.description})]},t))})})]}),c&&(c.supportLevels.length>0||c.resistanceLevels.length>0)&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mt-6",children:[c.supportLevels.length>0&&(0,a.jsxs)(ez,{className:"bg-slate-800/50 border-slate-700",children:[(0,a.jsx)(eR,{children:(0,a.jsxs)(eI,{className:"text-white flex items-center",children:[(0,a.jsx)(h,{className:"mr-2 h-5 w-5 text-green-400"}),"Support Levels"]})}),(0,a.jsx)(eF,{children:(0,a.jsx)("div",{className:"space-y-2",children:c.supportLevels.map((e,t)=>(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("span",{className:"text-slate-300",children:["Support ",t+1,":"]}),(0,a.jsx)("span",{className:"text-green-400 font-semibold",children:eP(e)})]},t))})})]}),c.resistanceLevels.length>0&&(0,a.jsxs)(ez,{className:"bg-slate-800/50 border-slate-700",children:[(0,a.jsx)(eR,{children:(0,a.jsxs)(eI,{className:"text-white flex items-center",children:[(0,a.jsx)(h,{className:"mr-2 h-5 w-5 text-red-400"}),"Resistance Levels"]})}),(0,a.jsx)(eF,{children:(0,a.jsx)("div",{className:"space-y-2",children:c.resistanceLevels.map((e,t)=>(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("span",{className:"text-slate-300",children:["Resistance ",t+1,":"]}),(0,a.jsx)("span",{className:"text-red-400 font-semibold",children:eP(e)})]},t))})})]})]})]})})]})}}]);