var R=require("../../../../chunks/[turbopack]_runtime.js")("server/app/api/scanner/perfect-pick/route.js")
R.c("server/chunks/6bf44_599abb6d._.js")
R.c("server/chunks/[root-of-the-server]__e67f8511._.js")
R.m("[project]/swing-trader-ai/.next-internal/server/app/api/scanner/perfect-pick/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/swing-trader-ai/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/swing-trader-ai/src/app/api/scanner/perfect-pick/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/swing-trader-ai/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/swing-trader-ai/src/app/api/scanner/perfect-pick/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
