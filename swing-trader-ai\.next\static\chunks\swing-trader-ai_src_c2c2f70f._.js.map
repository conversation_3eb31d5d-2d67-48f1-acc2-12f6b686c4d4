{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(value: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(value)\n}\n\nexport function formatPercentage(value: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'percent',\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2,\n  }).format(value / 100)\n}\n\nexport function calculateRiskReward(\n  entryPrice: number,\n  stopLoss: number,\n  takeProfit: number\n): number {\n  const risk = Math.abs(entryPrice - stopLoss)\n  const reward = Math.abs(takeProfit - entryPrice)\n  return reward / risk\n}\n\nexport function calculatePositionSize(\n  accountSize: number,\n  riskPercentage: number,\n  entryPrice: number,\n  stopLoss: number\n): number {\n  const riskAmount = accountSize * (riskPercentage / 100)\n  const riskPerShare = Math.abs(entryPrice - stopLoss)\n  return Math.floor(riskAmount / riskPerShare)\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,kMAAO,EAAC,IAAA,yKAAI,EAAC;AACtB;AAEO,SAAS,eAAe,KAAa;IAC1C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,iBAAiB,KAAa;IAC5C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC,QAAQ;AACpB;AAEO,SAAS,oBACd,UAAkB,EAClB,QAAgB,EAChB,UAAkB;IAElB,MAAM,OAAO,KAAK,GAAG,CAAC,aAAa;IACnC,MAAM,SAAS,KAAK,GAAG,CAAC,aAAa;IACrC,OAAO,SAAS;AAClB;AAEO,SAAS,sBACd,WAAmB,EACnB,cAAsB,EACtB,UAAkB,EAClB,QAAgB;IAEhB,MAAM,aAAa,cAAc,CAAC,iBAAiB,GAAG;IACtD,MAAM,eAAe,KAAK,GAAG,CAAC,aAAa;IAC3C,OAAO,KAAK,KAAK,CAAC,aAAa;AACjC", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,IAAA,mMAAG,EACxB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,oMAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,MAAM,OAAO,UAAU,oMAAI,GAAG;IAC9B,qBACE,sNAAC;QACC,WAAW,IAAA,qJAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,oMAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,sNAAC;QACC,KAAK;QACL,WAAW,IAAA,qJAAE,EACX,4DACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,oMAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,sNAAC;QACC,KAAK;QACL,WAAW,IAAA,qJAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,oMAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,sNAAC;QACC,KAAK;QACL,WAAW,IAAA,qJAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,oMAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,sNAAC;QACC,KAAK;QACL,WAAW,IAAA,qJAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,oMAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,sNAAC;QAAI,KAAK;QAAK,WAAW,IAAA,qJAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,oMAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,sNAAC;QACC,KAAK;QACL,WAAW,IAAA,qJAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 249, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,IAAA,mMAAG,EACvB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,KAA4C;QAA5C,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB,GAA5C;IACb,qBACE,sNAAC;QAAI,WAAW,IAAA,qJAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/components/SwingScanner.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Loader2, Search, TrendingUp, TrendingDown, Minus, Target, Shield, BarChart3 } from 'lucide-react'\nimport { ScanResult, ScanSummary } from '@/lib/swingScanner'\nimport { formatCurrency, formatPercentage } from '@/lib/utils'\n\ninterface ScannerProps {\n  autoScan?: boolean\n}\n\nexport function SwingScanner({ autoScan = false }: ScannerProps) {\n  const [isScanning, setIsScanning] = useState(false)\n  const [scanResults, setScanResults] = useState<ScanSummary | null>(null)\n  const [selectedScan, setSelectedScan] = useState<'quick' | 'full' | 'sector'>('quick')\n  const [selectedSector, setSelectedSector] = useState<string>('Technology')\n  const [error, setError] = useState<string | null>(null)\n\n  const sectors = [\n    'Technology', 'Financial Services', 'Healthcare', 'Industrial', \n    'Materials', 'Consumer', 'Communication Services', 'Energy'\n  ]\n\n  // Auto-scan on component mount if enabled\n  useEffect(() => {\n    if (autoScan) {\n      handleQuickScan()\n    }\n  }, [autoScan])\n\n  const handleQuickScan = async () => {\n    setIsScanning(true)\n    setError(null)\n    setScanResults(null)\n\n    try {\n      const response = await fetch('/api/scanner/quick?limit=15')\n      if (!response.ok) throw new Error('Failed to fetch scan results')\n      \n      const data = await response.json()\n      \n      // Convert to ScanSummary format\n      const summary: ScanSummary = {\n        totalScanned: data.totalScanned,\n        successfulScans: data.results.length,\n        failedScans: data.totalScanned - data.results.length,\n        topOpportunities: data.results,\n        sectorBreakdown: {},\n        scanDuration: 0\n      }\n      \n      setScanResults(summary)\n    } catch (err) {\n      setError('Failed to perform quick scan. Please try again.')\n      console.error('Quick scan error:', err)\n    } finally {\n      setIsScanning(false)\n    }\n  }\n\n  const handleFullScan = async () => {\n    setIsScanning(true)\n    setError(null)\n    setScanResults(null)\n\n    try {\n      const response = await fetch('/api/scanner/full?limit=25&concurrent=3')\n      if (!response.ok) throw new Error('Failed to fetch scan results')\n      \n      const data = await response.json()\n      setScanResults(data)\n    } catch (err) {\n      setError('Failed to perform full scan. Please try again.')\n      console.error('Full scan error:', err)\n    } finally {\n      setIsScanning(false)\n    }\n  }\n\n  const handleSectorScan = async () => {\n    setIsScanning(true)\n    setError(null)\n    setScanResults(null)\n\n    try {\n      const response = await fetch(`/api/scanner/sector/${encodeURIComponent(selectedSector)}?limit=15`)\n      if (!response.ok) throw new Error('Failed to fetch scan results')\n      \n      const data = await response.json()\n      setScanResults(data)\n    } catch (err) {\n      setError('Failed to perform sector scan. Please try again.')\n      console.error('Sector scan error:', err)\n    } finally {\n      setIsScanning(false)\n    }\n  }\n\n  const getTrendIcon = (trend: string) => {\n    switch (trend) {\n      case 'BULLISH':\n        return <TrendingUp className=\"h-4 w-4 text-green-400\" />\n      case 'BEARISH':\n        return <TrendingDown className=\"h-4 w-4 text-red-400\" />\n      default:\n        return <Minus className=\"h-4 w-4 text-yellow-400\" />\n    }\n  }\n\n  const getRecommendationColor = (recommendation: string) => {\n    if (recommendation.includes('BUY')) return 'bg-green-500/20 text-green-400'\n    if (recommendation.includes('SELL')) return 'bg-red-500/20 text-red-400'\n    return 'bg-yellow-500/20 text-yellow-400'\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Scanner Controls */}\n      <Card className=\"bg-slate-800/50 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"text-white flex items-center\">\n            <Search className=\"mr-2 h-5 w-5 text-blue-400\" />\n            Swing Trading Scanner\n          </CardTitle>\n          <CardDescription className=\"text-slate-300\">\n            Automatically scan stocks for the best swing trading opportunities\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"flex flex-wrap gap-4 mb-4\">\n            <Button\n              onClick={handleQuickScan}\n              disabled={isScanning}\n              className=\"bg-blue-600 hover:bg-blue-700\"\n            >\n              {isScanning && selectedScan === 'quick' ? (\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n              ) : null}\n              Quick Scan (Top 16)\n            </Button>\n            \n            <Button\n              onClick={handleFullScan}\n              disabled={isScanning}\n              variant=\"outline\"\n              className=\"border-slate-600 text-slate-300 hover:bg-slate-800\"\n            >\n              {isScanning && selectedScan === 'full' ? (\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n              ) : null}\n              Full Scan (All 70+ Stocks)\n            </Button>\n\n            <div className=\"flex gap-2\">\n              <select\n                value={selectedSector}\n                onChange={(e) => setSelectedSector(e.target.value)}\n                className=\"px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm\"\n                disabled={isScanning}\n              >\n                {sectors.map(sector => (\n                  <option key={sector} value={sector}>{sector}</option>\n                ))}\n              </select>\n              <Button\n                onClick={handleSectorScan}\n                disabled={isScanning}\n                variant=\"outline\"\n                className=\"border-slate-600 text-slate-300 hover:bg-slate-800\"\n              >\n                {isScanning && selectedScan === 'sector' ? (\n                  <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                ) : null}\n                Scan Sector\n              </Button>\n            </div>\n          </div>\n\n          {isScanning && (\n            <div className=\"text-center py-4\">\n              <Loader2 className=\"mx-auto h-8 w-8 animate-spin text-blue-400\" />\n              <p className=\"text-slate-300 mt-2\">\n                Scanning stocks for swing trading opportunities...\n              </p>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Error Display */}\n      {error && (\n        <Card className=\"bg-red-900/20 border-red-500/50\">\n          <CardContent className=\"p-6\">\n            <p className=\"text-red-300 text-center\">{error}</p>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Scan Results */}\n      {scanResults && (\n        <div className=\"space-y-6\">\n          {/* Scan Summary */}\n          <Card className=\"bg-slate-800/50 border-slate-700\">\n            <CardHeader>\n              <CardTitle className=\"text-white\">Scan Summary</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-white\">{scanResults.totalScanned}</div>\n                  <div className=\"text-sm text-slate-300\">Total Scanned</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-green-400\">{scanResults.successfulScans}</div>\n                  <div className=\"text-sm text-slate-300\">Successful</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-red-400\">{scanResults.failedScans}</div>\n                  <div className=\"text-sm text-slate-300\">Failed</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-blue-400\">\n                    {scanResults.scanDuration ? `${(scanResults.scanDuration / 1000).toFixed(1)}s` : 'N/A'}\n                  </div>\n                  <div className=\"text-sm text-slate-300\">Duration</div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Top Opportunities */}\n          <Card className=\"bg-slate-800/50 border-slate-700\">\n            <CardHeader>\n              <CardTitle className=\"text-white flex items-center\">\n                <TrendingUp className=\"mr-2 h-5 w-5 text-green-400\" />\n                Top Swing Trading Opportunities\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {scanResults.topOpportunities.map((result, index) => (\n                  <div key={result.symbol} className=\"p-4 bg-slate-700/50 rounded-lg\">\n                    <div className=\"flex items-center justify-between mb-3\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"text-lg font-bold text-white\">#{result.rank}</div>\n                        <div>\n                          <div className=\"flex items-center space-x-2\">\n                            <span className=\"text-lg font-semibold text-white\">{result.symbol}</span>\n                            {getTrendIcon(result.analysis.trend)}\n                            <Badge className={getRecommendationColor(result.analysis.recommendation)}>\n                              {result.analysis.recommendation.replace('_', ' ')}\n                            </Badge>\n                          </div>\n                          <div className=\"text-sm text-slate-300\">{result.name}</div>\n                        </div>\n                      </div>\n                      <div className=\"text-right\">\n                        <div className=\"text-xl font-bold text-white\">\n                          {result.score.toFixed(1)}/100\n                        </div>\n                        <div className=\"text-sm text-slate-300\">Score</div>\n                      </div>\n                    </div>\n\n                    <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\">\n                      <div>\n                        <div className=\"text-slate-300\">Price</div>\n                        <div className=\"text-white font-semibold\">\n                          {formatCurrency(result.quote.price)}\n                        </div>\n                        <div className={result.quote.change >= 0 ? \"text-green-400\" : \"text-red-400\"}>\n                          {formatPercentage(result.quote.changePercent)}\n                        </div>\n                      </div>\n                      <div>\n                        <div className=\"text-slate-300\">Entry</div>\n                        <div className=\"text-blue-400 font-semibold\">\n                          {formatCurrency(result.analysis.entryPrice)}\n                        </div>\n                      </div>\n                      <div>\n                        <div className=\"text-slate-300\">R/R Ratio</div>\n                        <div className=\"text-green-400 font-semibold\">\n                          {result.analysis.riskRewardRatio.toFixed(2)}:1\n                        </div>\n                      </div>\n                      <div>\n                        <div className=\"text-slate-300\">Confidence</div>\n                        <div className=\"text-white font-semibold\">\n                          {result.analysis.confidence.toFixed(1)}%\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;;;AARA;;;;;;;AAcO,SAAS,aAAa,KAAkC;QAAlC,EAAE,WAAW,KAAK,EAAgB,GAAlC;;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,kMAAQ,EAAC;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,kMAAQ,EAAqB;IACnE,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,kMAAQ,EAA8B;IAC9E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,kMAAQ,EAAS;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,kMAAQ,EAAgB;IAElD,MAAM,UAAU;QACd;QAAc;QAAsB;QAAc;QAClD;QAAa;QAAY;QAA0B;KACpD;IAED,0CAA0C;IAC1C,IAAA,mMAAS;kCAAC;YACR,IAAI,UAAU;gBACZ;YACF;QACF;iCAAG;QAAC;KAAS;IAEb,MAAM,kBAAkB;QACtB,cAAc;QACd,SAAS;QACT,eAAe;QAEf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,gCAAgC;YAChC,MAAM,UAAuB;gBAC3B,cAAc,KAAK,YAAY;gBAC/B,iBAAiB,KAAK,OAAO,CAAC,MAAM;gBACpC,aAAa,KAAK,YAAY,GAAG,KAAK,OAAO,CAAC,MAAM;gBACpD,kBAAkB,KAAK,OAAO;gBAC9B,iBAAiB,CAAC;gBAClB,cAAc;YAChB;YAEA,eAAe;QACjB,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,qBAAqB;QACrC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,iBAAiB;QACrB,cAAc;QACd,SAAS;QACT,eAAe;QAEf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,eAAe;QACjB,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,oBAAoB;QACpC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,mBAAmB;QACvB,cAAc;QACd,SAAS;QACT,eAAe;QAEf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,uBAAyD,OAAnC,mBAAmB,iBAAgB;YACvF,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,eAAe;QACjB,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,sBAAsB;QACtC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,sNAAC,4PAAU;oBAAC,WAAU;;;;;;YAC/B,KAAK;gBACH,qBAAO,sNAAC,kQAAY;oBAAC,WAAU;;;;;;YACjC;gBACE,qBAAO,sNAAC,yOAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,IAAI,eAAe,QAAQ,CAAC,QAAQ,OAAO;QAC3C,IAAI,eAAe,QAAQ,CAAC,SAAS,OAAO;QAC5C,OAAO;IACT;IAEA,qBACE,sNAAC;QAAI,WAAU;;0BAEb,sNAAC,oKAAI;gBAAC,WAAU;;kCACd,sNAAC,0KAAU;;0CACT,sNAAC,yKAAS;gCAAC,WAAU;;kDACnB,sNAAC,4OAAM;wCAAC,WAAU;;;;;;oCAA+B;;;;;;;0CAGnD,sNAAC,+KAAe;gCAAC,WAAU;0CAAiB;;;;;;;;;;;;kCAI9C,sNAAC,2KAAW;;0CACV,sNAAC;gCAAI,WAAU;;kDACb,sNAAC,wKAAM;wCACL,SAAS;wCACT,UAAU;wCACV,WAAU;;4CAET,cAAc,iBAAiB,wBAC9B,sNAAC,wPAAO;gDAAC,WAAU;;;;;uDACjB;4CAAK;;;;;;;kDAIX,sNAAC,wKAAM;wCACL,SAAS;wCACT,UAAU;wCACV,SAAQ;wCACR,WAAU;;4CAET,cAAc,iBAAiB,uBAC9B,sNAAC,wPAAO;gDAAC,WAAU;;;;;uDACjB;4CAAK;;;;;;;kDAIX,sNAAC;wCAAI,WAAU;;0DACb,sNAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gDACjD,WAAU;gDACV,UAAU;0DAET,QAAQ,GAAG,CAAC,CAAA,uBACX,sNAAC;wDAAoB,OAAO;kEAAS;uDAAxB;;;;;;;;;;0DAGjB,sNAAC,wKAAM;gDACL,SAAS;gDACT,UAAU;gDACV,SAAQ;gDACR,WAAU;;oDAET,cAAc,iBAAiB,yBAC9B,sNAAC,wPAAO;wDAAC,WAAU;;;;;+DACjB;oDAAK;;;;;;;;;;;;;;;;;;;4BAMd,4BACC,sNAAC;gCAAI,WAAU;;kDACb,sNAAC,wPAAO;wCAAC,WAAU;;;;;;kDACnB,sNAAC;wCAAE,WAAU;kDAAsB;;;;;;;;;;;;;;;;;;;;;;;;YAS1C,uBACC,sNAAC,oKAAI;gBAAC,WAAU;0BACd,cAAA,sNAAC,2KAAW;oBAAC,WAAU;8BACrB,cAAA,sNAAC;wBAAE,WAAU;kCAA4B;;;;;;;;;;;;;;;;YAM9C,6BACC,sNAAC;gBAAI,WAAU;;kCAEb,sNAAC,oKAAI;wBAAC,WAAU;;0CACd,sNAAC,0KAAU;0CACT,cAAA,sNAAC,yKAAS;oCAAC,WAAU;8CAAa;;;;;;;;;;;0CAEpC,sNAAC,2KAAW;0CACV,cAAA,sNAAC;oCAAI,WAAU;;sDACb,sNAAC;4CAAI,WAAU;;8DACb,sNAAC;oDAAI,WAAU;8DAAiC,YAAY,YAAY;;;;;;8DACxE,sNAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;sDAE1C,sNAAC;4CAAI,WAAU;;8DACb,sNAAC;oDAAI,WAAU;8DAAqC,YAAY,eAAe;;;;;;8DAC/E,sNAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;sDAE1C,sNAAC;4CAAI,WAAU;;8DACb,sNAAC;oDAAI,WAAU;8DAAmC,YAAY,WAAW;;;;;;8DACzE,sNAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;sDAE1C,sNAAC;4CAAI,WAAU;;8DACb,sNAAC;oDAAI,WAAU;8DACZ,YAAY,YAAY,GAAG,AAAC,GAA+C,OAA7C,CAAC,YAAY,YAAY,GAAG,IAAI,EAAE,OAAO,CAAC,IAAG,OAAK;;;;;;8DAEnF,sNAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOhD,sNAAC,oKAAI;wBAAC,WAAU;;0CACd,sNAAC,0KAAU;0CACT,cAAA,sNAAC,yKAAS;oCAAC,WAAU;;sDACnB,sNAAC,4PAAU;4CAAC,WAAU;;;;;;wCAAgC;;;;;;;;;;;;0CAI1D,sNAAC,2KAAW;0CACV,cAAA,sNAAC;oCAAI,WAAU;8CACZ,YAAY,gBAAgB,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACzC,sNAAC;4CAAwB,WAAU;;8DACjC,sNAAC;oDAAI,WAAU;;sEACb,sNAAC;4DAAI,WAAU;;8EACb,sNAAC;oEAAI,WAAU;;wEAA+B;wEAAE,OAAO,IAAI;;;;;;;8EAC3D,sNAAC;;sFACC,sNAAC;4EAAI,WAAU;;8FACb,sNAAC;oFAAK,WAAU;8FAAoC,OAAO,MAAM;;;;;;gFAChE,aAAa,OAAO,QAAQ,CAAC,KAAK;8FACnC,sNAAC,sKAAK;oFAAC,WAAW,uBAAuB,OAAO,QAAQ,CAAC,cAAc;8FACpE,OAAO,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;sFAGjD,sNAAC;4EAAI,WAAU;sFAA0B,OAAO,IAAI;;;;;;;;;;;;;;;;;;sEAGxD,sNAAC;4DAAI,WAAU;;8EACb,sNAAC;oEAAI,WAAU;;wEACZ,OAAO,KAAK,CAAC,OAAO,CAAC;wEAAG;;;;;;;8EAE3B,sNAAC;oEAAI,WAAU;8EAAyB;;;;;;;;;;;;;;;;;;8DAI5C,sNAAC;oDAAI,WAAU;;sEACb,sNAAC;;8EACC,sNAAC;oEAAI,WAAU;8EAAiB;;;;;;8EAChC,sNAAC;oEAAI,WAAU;8EACZ,IAAA,iKAAc,EAAC,OAAO,KAAK,CAAC,KAAK;;;;;;8EAEpC,sNAAC;oEAAI,WAAW,OAAO,KAAK,CAAC,MAAM,IAAI,IAAI,mBAAmB;8EAC3D,IAAA,mKAAgB,EAAC,OAAO,KAAK,CAAC,aAAa;;;;;;;;;;;;sEAGhD,sNAAC;;8EACC,sNAAC;oEAAI,WAAU;8EAAiB;;;;;;8EAChC,sNAAC;oEAAI,WAAU;8EACZ,IAAA,iKAAc,EAAC,OAAO,QAAQ,CAAC,UAAU;;;;;;;;;;;;sEAG9C,sNAAC;;8EACC,sNAAC;oEAAI,WAAU;8EAAiB;;;;;;8EAChC,sNAAC;oEAAI,WAAU;;wEACZ,OAAO,QAAQ,CAAC,eAAe,CAAC,OAAO,CAAC;wEAAG;;;;;;;;;;;;;sEAGhD,sNAAC;;8EACC,sNAAC;oEAAI,WAAU;8EAAiB;;;;;;8EAChC,sNAAC;oEAAI,WAAU;;wEACZ,OAAO,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC;wEAAG;;;;;;;;;;;;;;;;;;;;2CAhDrC,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6DzC;GAnSgB;KAAA", "debugId": null}}, {"offset": {"line": 1071, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/components/TradingSetupCard.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport {\n  TrendingUp,\n  TrendingDown,\n  Target,\n  Shield,\n  Clock,\n  DollarSign,\n  AlertTriangle,\n  CheckCircle,\n  Play,\n  Eye,\n  Brain,\n  Loader2,\n  Sparkles\n} from 'lucide-react';\nimport { StrategySetup } from '@/lib/swingStrategies';\n\ninterface TradingSetupCardProps {\n  setup: StrategySetup & { symbol: string };\n  onExecuteTrade?: (setup: StrategySetup & { symbol: string }) => void;\n  onViewChart?: (symbol: string) => void;\n}\n\ninterface AIRiskAssessment {\n  riskScore: number;\n  riskFactors: string[];\n  recommendations: string[];\n  sentiment: 'bullish' | 'bearish' | 'neutral';\n}\n\nexport default function TradingSetupCard({ setup, onExecuteTrade, onViewChart }: TradingSetupCardProps) {\n  const [showDetails, setShowDetails] = useState(false);\n  const [aiRiskAssessment, setAiRiskAssessment] = useState<AIRiskAssessment | null>(null);\n  const [isLoadingAI, setIsLoadingAI] = useState(false);\n  const [aiEnabled, setAiEnabled] = useState(false);\n\n  // Check AI status and load risk assessment\n  useEffect(() => {\n    checkAIStatus();\n  }, []);\n\n  const checkAIStatus = async () => {\n    try {\n      const response = await fetch('/api/ai?action=status');\n      const status = await response.json();\n      setAiEnabled(status.enabled);\n\n      if (status.enabled) {\n        loadRiskAssessment();\n      }\n    } catch (error) {\n      console.error('Error checking AI status:', error);\n    }\n  };\n\n  const loadRiskAssessment = async () => {\n    setIsLoadingAI(true);\n    try {\n      const response = await fetch('/api/ai', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          action: 'risk-assessment',\n          data: { setup }\n        })\n      });\n\n      if (response.ok) {\n        const { riskAssessment } = await response.json();\n        setAiRiskAssessment(riskAssessment);\n      }\n    } catch (error) {\n      console.error('Error loading AI risk assessment:', error);\n    } finally {\n      setIsLoadingAI(false);\n    }\n  };\n\n  const getConfidenceColor = (confidence: number) => {\n    if (confidence >= 80) return 'bg-green-500/20 text-green-400';\n    if (confidence >= 60) return 'bg-yellow-500/20 text-yellow-400';\n    return 'bg-red-500/20 text-red-400';\n  };\n\n  const getStrategyName = (strategy: string) => {\n    return strategy === 'overnight_momentum' \n      ? 'Overnight Momentum' \n      : 'Technical Breakout';\n  };\n\n  const riskReward = ((setup.targets[0] - setup.entryPrice) / (setup.entryPrice - setup.stopLoss)).toFixed(1);\n\n  const getSentimentColor = (sentiment: string) => {\n    switch (sentiment) {\n      case 'bullish': return 'text-green-400';\n      case 'bearish': return 'text-red-400';\n      default: return 'text-yellow-400';\n    }\n  };\n\n  const getRiskScoreColor = (score: number) => {\n    if (score <= 3) return 'text-green-400';\n    if (score <= 6) return 'text-yellow-400';\n    return 'text-red-400';\n  };\n\n  return (\n    <Card className=\"bg-slate-800/50 border-slate-700\">\n      <CardHeader className=\"pb-3\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-3\">\n            <CardTitle className=\"text-xl text-white\">{setup.symbol}</CardTitle>\n            <Badge variant=\"outline\" className=\"text-blue-400 border-blue-400\">\n              {getStrategyName(setup.strategy)}\n            </Badge>\n            <Badge className={getConfidenceColor(setup.confidence)}>\n              {setup.confidence}% Confidence\n            </Badge>\n          </div>\n          <div className=\"flex gap-2\">\n            <Button\n              size=\"sm\"\n              variant=\"outline\"\n              onClick={() => onViewChart?.(setup.symbol)}\n              className=\"text-slate-300 hover:text-white\"\n            >\n              <Eye className=\"h-4 w-4 mr-1\" />\n              Chart\n            </Button>\n            <Button\n              size=\"sm\"\n              onClick={() => onExecuteTrade?.(setup)}\n              className=\"bg-green-600 hover:bg-green-700 text-white\"\n            >\n              <Play className=\"h-4 w-4 mr-1\" />\n              Execute Trade\n            </Button>\n          </div>\n        </div>\n      </CardHeader>\n\n      <CardContent className=\"space-y-4\">\n        {/* Key Metrics */}\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n          <div className=\"text-center p-3 bg-slate-700/30 rounded-lg\">\n            <div className=\"flex items-center justify-center mb-1\">\n              <TrendingUp className=\"h-4 w-4 text-green-400 mr-1\" />\n              <span className=\"text-xs text-slate-400\">Entry</span>\n            </div>\n            <div className=\"text-lg font-bold text-white\">${setup.preciseEntry.price.toFixed(2)}</div>\n            <div className=\"text-xs text-slate-400\">{setup.preciseEntry.orderType}</div>\n          </div>\n\n          <div className=\"text-center p-3 bg-slate-700/30 rounded-lg\">\n            <div className=\"flex items-center justify-center mb-1\">\n              <Shield className=\"h-4 w-4 text-red-400 mr-1\" />\n              <span className=\"text-xs text-slate-400\">Stop Loss</span>\n            </div>\n            <div className=\"text-lg font-bold text-white\">${setup.preciseExit.stopLoss.price.toFixed(2)}</div>\n            <div className=\"text-xs text-slate-400\">{setup.preciseExit.stopLoss.orderType}</div>\n          </div>\n\n          <div className=\"text-center p-3 bg-slate-700/30 rounded-lg\">\n            <div className=\"flex items-center justify-center mb-1\">\n              <Target className=\"h-4 w-4 text-blue-400 mr-1\" />\n              <span className=\"text-xs text-slate-400\">First Target</span>\n            </div>\n            <div className=\"text-lg font-bold text-white\">${setup.targets[0].toFixed(2)}</div>\n            <div className=\"text-xs text-slate-400\">R/R: {riskReward}:1</div>\n          </div>\n\n          <div className=\"text-center p-3 bg-slate-700/30 rounded-lg\">\n            <div className=\"flex items-center justify-center mb-1\">\n              <DollarSign className=\"h-4 w-4 text-yellow-400 mr-1\" />\n              <span className=\"text-xs text-slate-400\">Position Size</span>\n            </div>\n            <div className=\"text-lg font-bold text-white\">{setup.positionSize}</div>\n            <div className=\"text-xs text-slate-400\">shares</div>\n          </div>\n        </div>\n\n        {/* Entry Conditions */}\n        <div className=\"p-3 bg-slate-700/20 rounded-lg\">\n          <h4 className=\"text-sm font-semibold text-white mb-2 flex items-center\">\n            <CheckCircle className=\"h-4 w-4 text-green-400 mr-2\" />\n            Entry Conditions\n          </h4>\n          <div className=\"space-y-1\">\n            {setup.preciseEntry.conditions.map((condition, index) => (\n              <div key={index} className=\"text-xs text-slate-300 flex items-center\">\n                <div className=\"w-1 h-1 bg-green-400 rounded-full mr-2\"></div>\n                {condition}\n              </div>\n            ))}\n          </div>\n          <div className=\"mt-2 text-xs text-blue-400\">\n            <Clock className=\"h-3 w-3 inline mr-1\" />\n            {setup.preciseEntry.timing}\n          </div>\n        </div>\n\n        {/* Take Profit Levels */}\n        <div className=\"p-3 bg-slate-700/20 rounded-lg\">\n          <h4 className=\"text-sm font-semibold text-white mb-2 flex items-center\">\n            <Target className=\"h-4 w-4 text-blue-400 mr-2\" />\n            Take Profit Plan\n          </h4>\n          <div className=\"space-y-2\">\n            {setup.preciseExit.takeProfits.map((tp, index) => (\n              <div key={index} className=\"flex items-center justify-between text-xs\">\n                <div className=\"text-slate-300\">\n                  <span className=\"font-medium\">{tp.target}:</span> ${tp.price.toFixed(2)}\n                </div>\n                <div className=\"text-slate-400\">\n                  {tp.percentage}% of position\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Risk Management */}\n        <div className=\"p-3 bg-slate-700/20 rounded-lg\">\n          <h4 className=\"text-sm font-semibold text-white mb-2 flex items-center\">\n            <Shield className=\"h-4 w-4 text-red-400 mr-2\" />\n            Risk Management\n          </h4>\n          <div className=\"grid grid-cols-2 gap-4 text-xs\">\n            <div>\n              <span className=\"text-slate-400\">Max Risk:</span>\n              <span className=\"text-white ml-2\">${setup.riskManagement.maxRiskDollars.toFixed(0)}</span>\n            </div>\n            <div>\n              <span className=\"text-slate-400\">Account Risk:</span>\n              <span className=\"text-white ml-2\">{setup.riskManagement.accountRiskPercent}%</span>\n            </div>\n            <div>\n              <span className=\"text-slate-400\">Time Stop:</span>\n              <span className=\"text-white ml-2\">{setup.riskManagement.timeStopHours}h</span>\n            </div>\n            <div>\n              <span className=\"text-slate-400\">Max Drawdown:</span>\n              <span className=\"text-white ml-2\">{setup.riskManagement.maxDrawdownPercent}%</span>\n            </div>\n          </div>\n        </div>\n\n        {/* AI Risk Assessment */}\n        {aiEnabled && (\n          <div className=\"p-3 bg-gradient-to-r from-blue-900/20 to-purple-900/20 rounded-lg border border-blue-500/20\">\n            <h4 className=\"text-sm font-semibold text-white mb-2 flex items-center\">\n              <Brain className=\"h-4 w-4 text-blue-400 mr-2\" />\n              AI Risk Assessment\n              <Sparkles className=\"h-3 w-3 text-blue-400 ml-1\" />\n            </h4>\n\n            {isLoadingAI ? (\n              <div className=\"flex items-center justify-center py-4\">\n                <Loader2 className=\"h-4 w-4 animate-spin text-blue-400 mr-2\" />\n                <span className=\"text-xs text-slate-300\">Analyzing risk...</span>\n              </div>\n            ) : aiRiskAssessment ? (\n              <div className=\"space-y-3\">\n                {/* Risk Score and Sentiment */}\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center\">\n                    <span className=\"text-xs text-slate-400\">Risk Score:</span>\n                    <span className={`ml-2 text-sm font-bold ${getRiskScoreColor(aiRiskAssessment.riskScore)}`}>\n                      {aiRiskAssessment.riskScore}/10\n                    </span>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <span className=\"text-xs text-slate-400\">Sentiment:</span>\n                    <span className={`ml-2 text-sm font-semibold capitalize ${getSentimentColor(aiRiskAssessment.sentiment)}`}>\n                      {aiRiskAssessment.sentiment}\n                    </span>\n                  </div>\n                </div>\n\n                {/* Risk Factors */}\n                {aiRiskAssessment.riskFactors.length > 0 && (\n                  <div>\n                    <span className=\"text-xs text-slate-400 mb-1 block\">Key Risk Factors:</span>\n                    <div className=\"space-y-1\">\n                      {aiRiskAssessment.riskFactors.slice(0, 2).map((factor, index) => (\n                        <div key={index} className=\"text-xs text-slate-300 flex items-start\">\n                          <AlertTriangle className=\"h-3 w-3 text-yellow-400 mr-1 mt-0.5 flex-shrink-0\" />\n                          {factor}\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                )}\n\n                {/* AI Recommendations */}\n                {aiRiskAssessment.recommendations.length > 0 && (\n                  <div>\n                    <span className=\"text-xs text-slate-400 mb-1 block\">AI Recommendations:</span>\n                    <div className=\"space-y-1\">\n                      {aiRiskAssessment.recommendations.slice(0, 2).map((rec, index) => (\n                        <div key={index} className=\"text-xs text-slate-300 flex items-start\">\n                          <CheckCircle className=\"h-3 w-3 text-blue-400 mr-1 mt-0.5 flex-shrink-0\" />\n                          {rec}\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                )}\n              </div>\n            ) : (\n              <div className=\"text-xs text-slate-400 text-center py-2\">\n                AI risk assessment unavailable\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Toggle Details */}\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={() => setShowDetails(!showDetails)}\n          className=\"w-full text-slate-300 hover:text-white\"\n        >\n          {showDetails ? 'Hide' : 'Show'} Execution Details\n        </Button>\n\n        {/* Detailed Execution Plan */}\n        {showDetails && (\n          <div className=\"space-y-4 pt-4 border-t border-slate-700\">\n            {/* Entry Instructions */}\n            <div>\n              <h5 className=\"text-sm font-semibold text-green-400 mb-2\">Entry Instructions:</h5>\n              <div className=\"space-y-1\">\n                {setup.executionPlan.entryInstructions.map((instruction, index) => (\n                  <div key={index} className=\"text-xs text-slate-300 flex items-start\">\n                    <div className=\"w-4 h-4 rounded-full bg-green-500/20 text-green-400 flex items-center justify-center text-[10px] mr-2 mt-0.5 flex-shrink-0\">\n                      {index + 1}\n                    </div>\n                    {instruction}\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Exit Instructions */}\n            <div>\n              <h5 className=\"text-sm font-semibold text-red-400 mb-2\">Exit Instructions:</h5>\n              <div className=\"space-y-1\">\n                {setup.executionPlan.exitInstructions.map((instruction, index) => (\n                  <div key={index} className=\"text-xs text-slate-300 flex items-start\">\n                    <div className=\"w-4 h-4 rounded-full bg-red-500/20 text-red-400 flex items-center justify-center text-[10px] mr-2 mt-0.5 flex-shrink-0\">\n                      {index + 1}\n                    </div>\n                    {instruction}\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Monitoring Points */}\n            <div>\n              <h5 className=\"text-sm font-semibold text-blue-400 mb-2\">Monitor These:</h5>\n              <div className=\"space-y-1\">\n                {setup.executionPlan.monitoringPoints.map((point, index) => (\n                  <div key={index} className=\"text-xs text-slate-300 flex items-center\">\n                    <Eye className=\"h-3 w-3 text-blue-400 mr-2\" />\n                    {point}\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Contingency Plans */}\n            <div>\n              <h5 className=\"text-sm font-semibold text-yellow-400 mb-2\">Contingency Plans:</h5>\n              <div className=\"space-y-1\">\n                {setup.executionPlan.contingencyPlans.map((plan, index) => (\n                  <div key={index} className=\"text-xs text-slate-300 flex items-start\">\n                    <AlertTriangle className=\"h-3 w-3 text-yellow-400 mr-2 mt-0.5 flex-shrink-0\" />\n                    {plan}\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Invalidation */}\n            <div className=\"p-3 bg-red-500/10 border border-red-500/20 rounded-lg\">\n              <h5 className=\"text-sm font-semibold text-red-400 mb-1\">Setup Invalidation:</h5>\n              <div className=\"text-xs text-slate-300\">{setup.invalidation}</div>\n            </div>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAoCe,SAAS,iBAAiB,KAA6D;QAA7D,EAAE,KAAK,EAAE,cAAc,EAAE,WAAW,EAAyB,GAA7D;;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,kMAAQ,EAAC;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,kMAAQ,EAA0B;IAClF,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,kMAAQ,EAAC;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,kMAAQ,EAAC;IAE3C,2CAA2C;IAC3C,IAAA,mMAAS;sCAAC;YACR;QACF;qCAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,aAAa,OAAO,OAAO;YAE3B,IAAI,OAAO,OAAO,EAAE;gBAClB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,MAAM,qBAAqB;QACzB,eAAe;QACf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,WAAW;gBACtC,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,MAAM;wBAAE;oBAAM;gBAChB;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,EAAE,cAAc,EAAE,GAAG,MAAM,SAAS,IAAI;gBAC9C,oBAAoB;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,cAAc,IAAI,OAAO;QAC7B,IAAI,cAAc,IAAI,OAAO;QAC7B,OAAO;IACT;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAO,aAAa,uBAChB,uBACA;IACN;IAEA,MAAM,aAAa,CAAC,CAAC,MAAM,OAAO,CAAC,EAAE,GAAG,MAAM,UAAU,IAAI,CAAC,MAAM,UAAU,GAAG,MAAM,QAAQ,CAAC,EAAE,OAAO,CAAC;IAEzG,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,SAAS,GAAG,OAAO;QACvB,IAAI,SAAS,GAAG,OAAO;QACvB,OAAO;IACT;IAEA,qBACE,sNAAC,oKAAI;QAAC,WAAU;;0BACd,sNAAC,0KAAU;gBAAC,WAAU;0BACpB,cAAA,sNAAC;oBAAI,WAAU;;sCACb,sNAAC;4BAAI,WAAU;;8CACb,sNAAC,yKAAS;oCAAC,WAAU;8CAAsB,MAAM,MAAM;;;;;;8CACvD,sNAAC,sKAAK;oCAAC,SAAQ;oCAAU,WAAU;8CAChC,gBAAgB,MAAM,QAAQ;;;;;;8CAEjC,sNAAC,sKAAK;oCAAC,WAAW,mBAAmB,MAAM,UAAU;;wCAClD,MAAM,UAAU;wCAAC;;;;;;;;;;;;;sCAGtB,sNAAC;4BAAI,WAAU;;8CACb,sNAAC,wKAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS,IAAM,wBAAA,kCAAA,YAAc,MAAM,MAAM;oCACzC,WAAU;;sDAEV,sNAAC,mOAAG;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGlC,sNAAC,wKAAM;oCACL,MAAK;oCACL,SAAS,IAAM,2BAAA,qCAAA,eAAiB;oCAChC,WAAU;;sDAEV,sNAAC,sOAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAOzC,sNAAC,2KAAW;gBAAC,WAAU;;kCAErB,sNAAC;wBAAI,WAAU;;0CACb,sNAAC;gCAAI,WAAU;;kDACb,sNAAC;wCAAI,WAAU;;0DACb,sNAAC,4PAAU;gDAAC,WAAU;;;;;;0DACtB,sNAAC;gDAAK,WAAU;0DAAyB;;;;;;;;;;;;kDAE3C,sNAAC;wCAAI,WAAU;;4CAA+B;4CAAE,MAAM,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC;;;;;;;kDACjF,sNAAC;wCAAI,WAAU;kDAA0B,MAAM,YAAY,CAAC,SAAS;;;;;;;;;;;;0CAGvE,sNAAC;gCAAI,WAAU;;kDACb,sNAAC;wCAAI,WAAU;;0DACb,sNAAC,4OAAM;gDAAC,WAAU;;;;;;0DAClB,sNAAC;gDAAK,WAAU;0DAAyB;;;;;;;;;;;;kDAE3C,sNAAC;wCAAI,WAAU;;4CAA+B;4CAAE,MAAM,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC;;;;;;;kDACzF,sNAAC;wCAAI,WAAU;kDAA0B,MAAM,WAAW,CAAC,QAAQ,CAAC,SAAS;;;;;;;;;;;;0CAG/E,sNAAC;gCAAI,WAAU;;kDACb,sNAAC;wCAAI,WAAU;;0DACb,sNAAC,4OAAM;gDAAC,WAAU;;;;;;0DAClB,sNAAC;gDAAK,WAAU;0DAAyB;;;;;;;;;;;;kDAE3C,sNAAC;wCAAI,WAAU;;4CAA+B;4CAAE,MAAM,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC;;;;;;;kDACzE,sNAAC;wCAAI,WAAU;;4CAAyB;4CAAM;4CAAW;;;;;;;;;;;;;0CAG3D,sNAAC;gCAAI,WAAU;;kDACb,sNAAC;wCAAI,WAAU;;0DACb,sNAAC,4PAAU;gDAAC,WAAU;;;;;;0DACtB,sNAAC;gDAAK,WAAU;0DAAyB;;;;;;;;;;;;kDAE3C,sNAAC;wCAAI,WAAU;kDAAgC,MAAM,YAAY;;;;;;kDACjE,sNAAC;wCAAI,WAAU;kDAAyB;;;;;;;;;;;;;;;;;;kCAK5C,sNAAC;wBAAI,WAAU;;0CACb,sNAAC;gCAAG,WAAU;;kDACZ,sNAAC,sQAAW;wCAAC,WAAU;;;;;;oCAAgC;;;;;;;0CAGzD,sNAAC;gCAAI,WAAU;0CACZ,MAAM,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,sBAC7C,sNAAC;wCAAgB,WAAU;;0DACzB,sNAAC;gDAAI,WAAU;;;;;;4CACd;;uCAFO;;;;;;;;;;0CAMd,sNAAC;gCAAI,WAAU;;kDACb,sNAAC,yOAAK;wCAAC,WAAU;;;;;;oCAChB,MAAM,YAAY,CAAC,MAAM;;;;;;;;;;;;;kCAK9B,sNAAC;wBAAI,WAAU;;0CACb,sNAAC;gCAAG,WAAU;;kDACZ,sNAAC,4OAAM;wCAAC,WAAU;;;;;;oCAA+B;;;;;;;0CAGnD,sNAAC;gCAAI,WAAU;0CACZ,MAAM,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI,sBACtC,sNAAC;wCAAgB,WAAU;;0DACzB,sNAAC;gDAAI,WAAU;;kEACb,sNAAC;wDAAK,WAAU;;4DAAe,GAAG,MAAM;4DAAC;;;;;;;oDAAQ;oDAAG,GAAG,KAAK,CAAC,OAAO,CAAC;;;;;;;0DAEvE,sNAAC;gDAAI,WAAU;;oDACZ,GAAG,UAAU;oDAAC;;;;;;;;uCALT;;;;;;;;;;;;;;;;kCAahB,sNAAC;wBAAI,WAAU;;0CACb,sNAAC;gCAAG,WAAU;;kDACZ,sNAAC,4OAAM;wCAAC,WAAU;;;;;;oCAA8B;;;;;;;0CAGlD,sNAAC;gCAAI,WAAU;;kDACb,sNAAC;;0DACC,sNAAC;gDAAK,WAAU;0DAAiB;;;;;;0DACjC,sNAAC;gDAAK,WAAU;;oDAAkB;oDAAE,MAAM,cAAc,CAAC,cAAc,CAAC,OAAO,CAAC;;;;;;;;;;;;;kDAElF,sNAAC;;0DACC,sNAAC;gDAAK,WAAU;0DAAiB;;;;;;0DACjC,sNAAC;gDAAK,WAAU;;oDAAmB,MAAM,cAAc,CAAC,kBAAkB;oDAAC;;;;;;;;;;;;;kDAE7E,sNAAC;;0DACC,sNAAC;gDAAK,WAAU;0DAAiB;;;;;;0DACjC,sNAAC;gDAAK,WAAU;;oDAAmB,MAAM,cAAc,CAAC,aAAa;oDAAC;;;;;;;;;;;;;kDAExE,sNAAC;;0DACC,sNAAC;gDAAK,WAAU;0DAAiB;;;;;;0DACjC,sNAAC;gDAAK,WAAU;;oDAAmB,MAAM,cAAc,CAAC,kBAAkB;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;;oBAMhF,2BACC,sNAAC;wBAAI,WAAU;;0CACb,sNAAC;gCAAG,WAAU;;kDACZ,sNAAC,yOAAK;wCAAC,WAAU;;;;;;oCAA+B;kDAEhD,sNAAC,kPAAQ;wCAAC,WAAU;;;;;;;;;;;;4BAGrB,4BACC,sNAAC;gCAAI,WAAU;;kDACb,sNAAC,wPAAO;wCAAC,WAAU;;;;;;kDACnB,sNAAC;wCAAK,WAAU;kDAAyB;;;;;;;;;;;uCAEzC,iCACF,sNAAC;gCAAI,WAAU;;kDAEb,sNAAC;wCAAI,WAAU;;0DACb,sNAAC;gDAAI,WAAU;;kEACb,sNAAC;wDAAK,WAAU;kEAAyB;;;;;;kEACzC,sNAAC;wDAAK,WAAW,AAAC,0BAAuE,OAA9C,kBAAkB,iBAAiB,SAAS;;4DACpF,iBAAiB,SAAS;4DAAC;;;;;;;;;;;;;0DAGhC,sNAAC;gDAAI,WAAU;;kEACb,sNAAC;wDAAK,WAAU;kEAAyB;;;;;;kEACzC,sNAAC;wDAAK,WAAW,AAAC,yCAAsF,OAA9C,kBAAkB,iBAAiB,SAAS;kEACnG,iBAAiB,SAAS;;;;;;;;;;;;;;;;;;oCAMhC,iBAAiB,WAAW,CAAC,MAAM,GAAG,mBACrC,sNAAC;;0DACC,sNAAC;gDAAK,WAAU;0DAAoC;;;;;;0DACpD,sNAAC;gDAAI,WAAU;0DACZ,iBAAiB,WAAW,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,sBACrD,sNAAC;wDAAgB,WAAU;;0EACzB,sNAAC,qQAAa;gEAAC,WAAU;;;;;;4DACxB;;uDAFO;;;;;;;;;;;;;;;;oCAUjB,iBAAiB,eAAe,CAAC,MAAM,GAAG,mBACzC,sNAAC;;0DACC,sNAAC;gDAAK,WAAU;0DAAoC;;;;;;0DACpD,sNAAC;gDAAI,WAAU;0DACZ,iBAAiB,eAAe,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBACtD,sNAAC;wDAAgB,WAAU;;0EACzB,sNAAC,sQAAW;gEAAC,WAAU;;;;;;4DACtB;;uDAFO;;;;;;;;;;;;;;;;;;;;;qDAUpB,sNAAC;gCAAI,WAAU;0CAA0C;;;;;;;;;;;;kCAQ/D,sNAAC,wKAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,eAAe,CAAC;wBAC/B,WAAU;;4BAET,cAAc,SAAS;4BAAO;;;;;;;oBAIhC,6BACC,sNAAC;wBAAI,WAAU;;0CAEb,sNAAC;;kDACC,sNAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,sNAAC;wCAAI,WAAU;kDACZ,MAAM,aAAa,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,aAAa,sBACvD,sNAAC;gDAAgB,WAAU;;kEACzB,sNAAC;wDAAI,WAAU;kEACZ,QAAQ;;;;;;oDAEV;;+CAJO;;;;;;;;;;;;;;;;0CAWhB,sNAAC;;kDACC,sNAAC;wCAAG,WAAU;kDAA0C;;;;;;kDACxD,sNAAC;wCAAI,WAAU;kDACZ,MAAM,aAAa,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,aAAa,sBACtD,sNAAC;gDAAgB,WAAU;;kEACzB,sNAAC;wDAAI,WAAU;kEACZ,QAAQ;;;;;;oDAEV;;+CAJO;;;;;;;;;;;;;;;;0CAWhB,sNAAC;;kDACC,sNAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,sNAAC;wCAAI,WAAU;kDACZ,MAAM,aAAa,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,OAAO,sBAChD,sNAAC;gDAAgB,WAAU;;kEACzB,sNAAC,mOAAG;wDAAC,WAAU;;;;;;oDACd;;+CAFO;;;;;;;;;;;;;;;;0CAShB,sNAAC;;kDACC,sNAAC;wCAAG,WAAU;kDAA6C;;;;;;kDAC3D,sNAAC;wCAAI,WAAU;kDACZ,MAAM,aAAa,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC/C,sNAAC;gDAAgB,WAAU;;kEACzB,sNAAC,qQAAa;wDAAC,WAAU;;;;;;oDACxB;;+CAFO;;;;;;;;;;;;;;;;0CAShB,sNAAC;gCAAI,WAAU;;kDACb,sNAAC;wCAAG,WAAU;kDAA0C;;;;;;kDACxD,sNAAC;wCAAI,WAAU;kDAA0B,MAAM,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzE;GA9WwB;KAAA", "debugId": null}}, {"offset": {"line": 2232, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/components/AIInsights.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { \n  Brain, \n  TrendingUp, \n  TrendingDown, \n  AlertTriangle, \n  Target, \n  Lightbulb,\n  Loader2,\n  Sparkles,\n  BarChart3\n} from 'lucide-react';\nimport { EnhancedScanResult } from '@/lib/enhancedSwingScanner';\n\ninterface AIInsightsProps {\n  scanResults: EnhancedScanResult[];\n  marketConditions: any;\n  onRefresh?: () => void;\n}\n\ninterface AIStatus {\n  enabled: boolean;\n  model: string | null;\n  features: {\n    marketCommentary: boolean;\n    riskAssessment: boolean;\n    tradingRecommendations: boolean;\n  };\n}\n\ninterface TradingRecommendations {\n  topPicks: string[];\n  avoidList: string[];\n  marketOutlook: string;\n  actionItems: string[];\n}\n\nexport default function AIInsights({ scanResults, marketConditions, onRefresh }: AIInsightsProps) {\n  const [aiStatus, setAiStatus] = useState<AIStatus | null>(null);\n  const [marketCommentary, setMarketCommentary] = useState<string>('');\n  const [recommendations, setRecommendations] = useState<TradingRecommendations | null>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  // Check AI status on component mount\n  useEffect(() => {\n    checkAIStatus();\n  }, []);\n\n  // Generate insights when scan results change\n  useEffect(() => {\n    if (scanResults.length > 0 && aiStatus?.enabled) {\n      generateInsights();\n    }\n  }, [scanResults, aiStatus]);\n\n  const checkAIStatus = async () => {\n    try {\n      const response = await fetch('/api/ai?action=status');\n      const status = await response.json();\n      setAiStatus(status);\n    } catch (error) {\n      console.error('Error checking AI status:', error);\n      setError('Unable to connect to AI service');\n    }\n  };\n\n  const generateInsights = async () => {\n    if (!aiStatus?.enabled || scanResults.length === 0) return;\n\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      // Generate market commentary\n      const commentaryResponse = await fetch('/api/ai', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          action: 'market-commentary',\n          data: { scanResults, marketConditions }\n        })\n      });\n      \n      if (commentaryResponse.ok) {\n        const { commentary } = await commentaryResponse.json();\n        setMarketCommentary(commentary);\n      }\n\n      // Generate trading recommendations\n      const recommendationsResponse = await fetch('/api/ai', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          action: 'trading-recommendations',\n          data: { \n            scanResults,\n            userPreferences: {\n              riskTolerance: 'medium',\n              tradingStyle: 'moderate',\n              accountSize: 100000\n            }\n          }\n        })\n      });\n\n      if (recommendationsResponse.ok) {\n        const { recommendations: recs } = await recommendationsResponse.json();\n        setRecommendations(recs);\n      }\n\n    } catch (error) {\n      console.error('Error generating AI insights:', error);\n      setError('Failed to generate AI insights');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  if (!aiStatus) {\n    return (\n      <Card className=\"bg-slate-800/50 border-slate-700\">\n        <CardContent className=\"p-6\">\n          <div className=\"flex items-center justify-center\">\n            <Loader2 className=\"h-6 w-6 animate-spin text-blue-400 mr-2\" />\n            <span className=\"text-slate-300\">Checking AI status...</span>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  if (!aiStatus.enabled) {\n    return (\n      <Card className=\"bg-slate-800/50 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"text-white flex items-center\">\n            <Brain className=\"mr-2 h-5 w-5 text-gray-400\" />\n            AI Insights\n            <Badge variant=\"outline\" className=\"ml-2 text-gray-400 border-gray-400\">\n              Disabled\n            </Badge>\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"text-center py-6\">\n            <Brain className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n            <p className=\"text-slate-300 mb-4\">\n              AI-powered insights are currently disabled.\n            </p>\n            <p className=\"text-sm text-slate-400 mb-4\">\n              Enable OpenAI integration to get intelligent market analysis, risk assessments, and personalized trading recommendations.\n            </p>\n            <Button variant=\"outline\" onClick={checkAIStatus}>\n              Check Configuration\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* AI Status Header */}\n      <Card className=\"bg-slate-800/50 border-slate-700\">\n        <CardHeader className=\"pb-3\">\n          <CardTitle className=\"text-white flex items-center justify-between\">\n            <div className=\"flex items-center\">\n              <Sparkles className=\"mr-2 h-5 w-5 text-blue-400\" />\n              AI-Powered Insights\n              <Badge className=\"ml-2 bg-blue-500/20 text-blue-400\">\n                {aiStatus.model || 'GPT-4o'}\n              </Badge>\n            </div>\n            <Button\n              size=\"sm\"\n              variant=\"outline\"\n              onClick={generateInsights}\n              disabled={isLoading}\n              className=\"text-slate-300 hover:text-white\"\n            >\n              {isLoading ? (\n                <Loader2 className=\"h-4 w-4 animate-spin mr-1\" />\n              ) : (\n                <Brain className=\"h-4 w-4 mr-1\" />\n              )}\n              {isLoading ? 'Analyzing...' : 'Refresh Insights'}\n            </Button>\n          </CardTitle>\n        </CardHeader>\n      </Card>\n\n      {error && (\n        <Card className=\"bg-red-900/20 border-red-500/30\">\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center text-red-400\">\n              <AlertTriangle className=\"h-4 w-4 mr-2\" />\n              {error}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Market Commentary */}\n      {marketCommentary && (\n        <Card className=\"bg-slate-800/50 border-slate-700\">\n          <CardHeader>\n            <CardTitle className=\"text-white flex items-center\">\n              <BarChart3 className=\"mr-2 h-5 w-5 text-green-400\" />\n              Market Commentary\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"prose prose-invert max-w-none\">\n              <p className=\"text-slate-300 leading-relaxed whitespace-pre-line\">\n                {marketCommentary}\n              </p>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Trading Recommendations */}\n      {recommendations && (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          {/* Top Picks */}\n          <Card className=\"bg-slate-800/50 border-slate-700\">\n            <CardHeader>\n              <CardTitle className=\"text-white flex items-center\">\n                <TrendingUp className=\"mr-2 h-5 w-5 text-green-400\" />\n                AI Top Picks\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-2\">\n                {recommendations.topPicks.map((symbol, index) => (\n                  <div key={symbol} className=\"flex items-center justify-between p-2 bg-green-900/20 rounded\">\n                    <div className=\"flex items-center\">\n                      <div className=\"w-6 h-6 rounded-full bg-green-500/20 text-green-400 flex items-center justify-center text-sm font-bold mr-3\">\n                        {index + 1}\n                      </div>\n                      <span className=\"text-white font-semibold\">{symbol}</span>\n                    </div>\n                    <Badge className=\"bg-green-500/20 text-green-400\">\n                      Recommended\n                    </Badge>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Action Items */}\n          <Card className=\"bg-slate-800/50 border-slate-700\">\n            <CardHeader>\n              <CardTitle className=\"text-white flex items-center\">\n                <Lightbulb className=\"mr-2 h-5 w-5 text-yellow-400\" />\n                Action Items\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-2\">\n                {recommendations.actionItems.map((item, index) => (\n                  <div key={index} className=\"flex items-start p-2 bg-yellow-900/20 rounded\">\n                    <Target className=\"h-4 w-4 text-yellow-400 mr-2 mt-0.5 flex-shrink-0\" />\n                    <span className=\"text-slate-300 text-sm\">{item}</span>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      )}\n\n      {/* Market Outlook */}\n      {recommendations?.marketOutlook && (\n        <Card className=\"bg-slate-800/50 border-slate-700\">\n          <CardHeader>\n            <CardTitle className=\"text-white flex items-center\">\n              <TrendingUp className=\"mr-2 h-5 w-5 text-blue-400\" />\n              Market Outlook\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <p className=\"text-slate-300 leading-relaxed\">\n              {recommendations.marketOutlook}\n            </p>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Avoid List */}\n      {recommendations?.avoidList && recommendations.avoidList.length > 0 && (\n        <Card className=\"bg-slate-800/50 border-slate-700\">\n          <CardHeader>\n            <CardTitle className=\"text-white flex items-center\">\n              <TrendingDown className=\"mr-2 h-5 w-5 text-red-400\" />\n              Caution List\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-2\">\n              {recommendations.avoidList.map((symbol, index) => (\n                <div key={symbol} className=\"flex items-center justify-between p-2 bg-red-900/20 rounded\">\n                  <span className=\"text-white font-semibold\">{symbol}</span>\n                  <Badge className=\"bg-red-500/20 text-red-400\">\n                    Avoid\n                  </Badge>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {isLoading && (\n        <Card className=\"bg-slate-800/50 border-slate-700\">\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-center\">\n              <Loader2 className=\"h-6 w-6 animate-spin text-blue-400 mr-2\" />\n              <span className=\"text-slate-300\">Generating AI insights...</span>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AA0Ce,SAAS,WAAW,KAA6D;QAA7D,EAAE,WAAW,EAAE,gBAAgB,EAAE,SAAS,EAAmB,GAA7D;;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,kMAAQ,EAAkB;IAC1D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,kMAAQ,EAAS;IACjE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,kMAAQ,EAAgC;IACtF,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,kMAAQ,EAAC;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,kMAAQ,EAAgB;IAElD,qCAAqC;IACrC,IAAA,mMAAS;gCAAC;YACR;QACF;+BAAG,EAAE;IAEL,6CAA6C;IAC7C,IAAA,mMAAS;gCAAC;YACR,IAAI,YAAY,MAAM,GAAG,MAAK,qBAAA,+BAAA,SAAU,OAAO,GAAE;gBAC/C;YACF;QACF;+BAAG;QAAC;QAAa;KAAS;IAE1B,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS;QACX;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,EAAC,qBAAA,+BAAA,SAAU,OAAO,KAAI,YAAY,MAAM,KAAK,GAAG;QAEpD,aAAa;QACb,SAAS;QAET,IAAI;YACF,6BAA6B;YAC7B,MAAM,qBAAqB,MAAM,MAAM,WAAW;gBAChD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,MAAM;wBAAE;wBAAa;oBAAiB;gBACxC;YACF;YAEA,IAAI,mBAAmB,EAAE,EAAE;gBACzB,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,mBAAmB,IAAI;gBACpD,oBAAoB;YACtB;YAEA,mCAAmC;YACnC,MAAM,0BAA0B,MAAM,MAAM,WAAW;gBACrD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,MAAM;wBACJ;wBACA,iBAAiB;4BACf,eAAe;4BACf,cAAc;4BACd,aAAa;wBACf;oBACF;gBACF;YACF;YAEA,IAAI,wBAAwB,EAAE,EAAE;gBAC9B,MAAM,EAAE,iBAAiB,IAAI,EAAE,GAAG,MAAM,wBAAwB,IAAI;gBACpE,mBAAmB;YACrB;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,CAAC,UAAU;QACb,qBACE,sNAAC,oKAAI;YAAC,WAAU;sBACd,cAAA,sNAAC,2KAAW;gBAAC,WAAU;0BACrB,cAAA,sNAAC;oBAAI,WAAU;;sCACb,sNAAC,wPAAO;4BAAC,WAAU;;;;;;sCACnB,sNAAC;4BAAK,WAAU;sCAAiB;;;;;;;;;;;;;;;;;;;;;;IAK3C;IAEA,IAAI,CAAC,SAAS,OAAO,EAAE;QACrB,qBACE,sNAAC,oKAAI;YAAC,WAAU;;8BACd,sNAAC,0KAAU;8BACT,cAAA,sNAAC,yKAAS;wBAAC,WAAU;;0CACnB,sNAAC,yOAAK;gCAAC,WAAU;;;;;;4BAA+B;0CAEhD,sNAAC,sKAAK;gCAAC,SAAQ;gCAAU,WAAU;0CAAqC;;;;;;;;;;;;;;;;;8BAK5E,sNAAC,2KAAW;8BACV,cAAA,sNAAC;wBAAI,WAAU;;0CACb,sNAAC,yOAAK;gCAAC,WAAU;;;;;;0CACjB,sNAAC;gCAAE,WAAU;0CAAsB;;;;;;0CAGnC,sNAAC;gCAAE,WAAU;0CAA8B;;;;;;0CAG3C,sNAAC,wKAAM;gCAAC,SAAQ;gCAAU,SAAS;0CAAe;;;;;;;;;;;;;;;;;;;;;;;IAO5D;IAEA,qBACE,sNAAC;QAAI,WAAU;;0BAEb,sNAAC,oKAAI;gBAAC,WAAU;0BACd,cAAA,sNAAC,0KAAU;oBAAC,WAAU;8BACpB,cAAA,sNAAC,yKAAS;wBAAC,WAAU;;0CACnB,sNAAC;gCAAI,WAAU;;kDACb,sNAAC,kPAAQ;wCAAC,WAAU;;;;;;oCAA+B;kDAEnD,sNAAC,sKAAK;wCAAC,WAAU;kDACd,SAAS,KAAK,IAAI;;;;;;;;;;;;0CAGvB,sNAAC,wKAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU;gCACV,WAAU;;oCAET,0BACC,sNAAC,wPAAO;wCAAC,WAAU;;;;;6DAEnB,sNAAC,yOAAK;wCAAC,WAAU;;;;;;oCAElB,YAAY,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;YAMrC,uBACC,sNAAC,oKAAI;gBAAC,WAAU;0BACd,cAAA,sNAAC,2KAAW;oBAAC,WAAU;8BACrB,cAAA,sNAAC;wBAAI,WAAU;;0CACb,sNAAC,qQAAa;gCAAC,WAAU;;;;;;4BACxB;;;;;;;;;;;;;;;;;YAOR,kCACC,sNAAC,oKAAI;gBAAC,WAAU;;kCACd,sNAAC,0KAAU;kCACT,cAAA,sNAAC,yKAAS;4BAAC,WAAU;;8CACnB,sNAAC,2PAAS;oCAAC,WAAU;;;;;;gCAAgC;;;;;;;;;;;;kCAIzD,sNAAC,2KAAW;kCACV,cAAA,sNAAC;4BAAI,WAAU;sCACb,cAAA,sNAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;;;;;;;;;;;YAQV,iCACC,sNAAC;gBAAI,WAAU;;kCAEb,sNAAC,oKAAI;wBAAC,WAAU;;0CACd,sNAAC,0KAAU;0CACT,cAAA,sNAAC,yKAAS;oCAAC,WAAU;;sDACnB,sNAAC,4PAAU;4CAAC,WAAU;;;;;;wCAAgC;;;;;;;;;;;;0CAI1D,sNAAC,2KAAW;0CACV,cAAA,sNAAC;oCAAI,WAAU;8CACZ,gBAAgB,QAAQ,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACrC,sNAAC;4CAAiB,WAAU;;8DAC1B,sNAAC;oDAAI,WAAU;;sEACb,sNAAC;4DAAI,WAAU;sEACZ,QAAQ;;;;;;sEAEX,sNAAC;4DAAK,WAAU;sEAA4B;;;;;;;;;;;;8DAE9C,sNAAC,sKAAK;oDAAC,WAAU;8DAAiC;;;;;;;2CAP1C;;;;;;;;;;;;;;;;;;;;;kCAiBlB,sNAAC,oKAAI;wBAAC,WAAU;;0CACd,sNAAC,0KAAU;0CACT,cAAA,sNAAC,yKAAS;oCAAC,WAAU;;sDACnB,sNAAC,qPAAS;4CAAC,WAAU;;;;;;wCAAiC;;;;;;;;;;;;0CAI1D,sNAAC,2KAAW;0CACV,cAAA,sNAAC;oCAAI,WAAU;8CACZ,gBAAgB,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,sBACtC,sNAAC;4CAAgB,WAAU;;8DACzB,sNAAC,4OAAM;oDAAC,WAAU;;;;;;8DAClB,sNAAC;oDAAK,WAAU;8DAA0B;;;;;;;2CAFlC;;;;;;;;;;;;;;;;;;;;;;;;;;;YAYrB,CAAA,4BAAA,sCAAA,gBAAiB,aAAa,mBAC7B,sNAAC,oKAAI;gBAAC,WAAU;;kCACd,sNAAC,0KAAU;kCACT,cAAA,sNAAC,yKAAS;4BAAC,WAAU;;8CACnB,sNAAC,4PAAU;oCAAC,WAAU;;;;;;gCAA+B;;;;;;;;;;;;kCAIzD,sNAAC,2KAAW;kCACV,cAAA,sNAAC;4BAAE,WAAU;sCACV,gBAAgB,aAAa;;;;;;;;;;;;;;;;;YAOrC,CAAA,4BAAA,sCAAA,gBAAiB,SAAS,KAAI,gBAAgB,SAAS,CAAC,MAAM,GAAG,mBAChE,sNAAC,oKAAI;gBAAC,WAAU;;kCACd,sNAAC,0KAAU;kCACT,cAAA,sNAAC,yKAAS;4BAAC,WAAU;;8CACnB,sNAAC,kQAAY;oCAAC,WAAU;;;;;;gCAA8B;;;;;;;;;;;;kCAI1D,sNAAC,2KAAW;kCACV,cAAA,sNAAC;4BAAI,WAAU;sCACZ,gBAAgB,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACtC,sNAAC;oCAAiB,WAAU;;sDAC1B,sNAAC;4CAAK,WAAU;sDAA4B;;;;;;sDAC5C,sNAAC,sKAAK;4CAAC,WAAU;sDAA6B;;;;;;;mCAFtC;;;;;;;;;;;;;;;;;;;;;YAYnB,2BACC,sNAAC,oKAAI;gBAAC,WAAU;0BACd,cAAA,sNAAC,2KAAW;oBAAC,WAAU;8BACrB,cAAA,sNAAC;wBAAI,WAAU;;0CACb,sNAAC,wPAAO;gCAAC,WAAU;;;;;;0CACnB,sNAAC;gCAAK,WAAU;0CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C;GAnSwB;KAAA", "debugId": null}}, {"offset": {"line": 2974, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/components/StrategyScanner.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Loader2, Zap, Target, Clock, AlertTriangle, TrendingUp, Moon, BarChart3, ExternalLink } from 'lucide-react'\nimport { EnhancedScanResult, StrategyScanSummary } from '@/lib/enhancedSwingScanner'\nimport { formatCurrency, formatPercentage } from '@/lib/utils'\nimport TradingSetupCard from './TradingSetupCard'\nimport AIInsights from './AIInsights'\n\ninterface StrategyScannerProps {\n  autoScan?: boolean\n  accountSize?: number\n}\n\nexport function StrategyScanner({ autoScan = false, accountSize = 100000 }: StrategyScannerProps) {\n  const [isScanning, setIsScanning] = useState(false)\n  const [scanResults, setScanResults] = useState<StrategyScanSummary | null>(null)\n  const [selectedStrategy, setSelectedStrategy] = useState<'both' | 'overnight' | 'breakout'>('both')\n  const [error, setError] = useState<string | null>(null)\n  const [userAccountSize, setUserAccountSize] = useState(accountSize)\n\n  // Handle manual trade execution\n  const handleExecuteTrade = async (setup: any) => {\n    try {\n      // Open trading interface or redirect to paper trading\n      alert(`Execute Trade for ${setup.symbol}:\\n\\nEntry: $${setup.preciseEntry.price.toFixed(2)} (${setup.preciseEntry.orderType})\\nStop: $${setup.preciseExit.stopLoss.price.toFixed(2)}\\nTarget: $${setup.targets[0].toFixed(2)}\\nShares: ${setup.positionSize}\\n\\nThis would normally open your trading platform or paper trading interface.`)\n    } catch (error) {\n      console.error('Error executing trade:', error)\n    }\n  }\n\n  // Handle chart viewing\n  const handleViewChart = (symbol: string) => {\n    // Open chart in new tab (TradingView or similar)\n    const chartUrl = `https://www.tradingview.com/chart/?symbol=${symbol}`\n    window.open(chartUrl, '_blank')\n  }\n\n  // Auto-scan on component mount if enabled\n  useEffect(() => {\n    if (autoScan) {\n      handleStrategyScan('quick')\n    }\n  }, [autoScan])\n\n  const handleStrategyScan = async (scanType: 'quick' | 'full') => {\n    setIsScanning(true)\n    setError(null)\n    setScanResults(null)\n\n    try {\n      const response = await fetch(\n        `/api/scanner/strategies?type=${scanType}&accountSize=${userAccountSize}&limit=20`\n      )\n      \n      if (!response.ok) throw new Error('Failed to fetch strategy scan results')\n      \n      const data = await response.json()\n      setScanResults(data)\n    } catch (err) {\n      setError('Failed to perform strategy scan. Please try again.')\n      console.error('Strategy scan error:', err)\n    } finally {\n      setIsScanning(false)\n    }\n  }\n\n  const getStrategyIcon = (strategy: string) => {\n    switch (strategy) {\n      case 'overnight_momentum':\n        return <Moon className=\"h-4 w-4 text-purple-400\" />\n      case 'technical_breakout':\n        return <TrendingUp className=\"h-4 w-4 text-green-400\" />\n      default:\n        return <BarChart3 className=\"h-4 w-4 text-blue-400\" />\n    }\n  }\n\n  const getStrategyName = (strategy: string) => {\n    switch (strategy) {\n      case 'overnight_momentum':\n        return 'Overnight Momentum'\n      case 'technical_breakout':\n        return 'Technical Breakout'\n      default:\n        return 'Mixed Strategy'\n    }\n  }\n\n  const getStrategyColor = (strategy: string) => {\n    switch (strategy) {\n      case 'overnight_momentum':\n        return 'bg-purple-500/20 text-purple-400'\n      case 'technical_breakout':\n        return 'bg-green-500/20 text-green-400'\n      default:\n        return 'bg-blue-500/20 text-blue-400'\n    }\n  }\n\n  const filteredResults = scanResults?.topSetups.filter(result => {\n    if (selectedStrategy === 'both') return true\n    if (selectedStrategy === 'overnight') return result.overnightSetup\n    if (selectedStrategy === 'breakout') return result.breakoutSetup\n    return true\n  }) || []\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Strategy Scanner Controls */}\n      <Card className=\"bg-slate-800/50 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"text-white flex items-center\">\n            <Zap className=\"mr-2 h-5 w-5 text-yellow-400\" />\n            Professional Swing Trading Strategies\n          </CardTitle>\n          <CardDescription className=\"text-slate-300\">\n            Automated scanner implementing proven swing trading methodologies with precise entry/exit rules\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          {/* Account Size Input */}\n          <div className=\"mb-4\">\n            <label className=\"block text-sm text-slate-300 mb-2\">Account Size (for position sizing)</label>\n            <input\n              type=\"number\"\n              value={userAccountSize}\n              onChange={(e) => setUserAccountSize(parseInt(e.target.value) || 100000)}\n              className=\"w-32 px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm\"\n              min=\"10000\"\n              step=\"10000\"\n              disabled={isScanning}\n            />\n          </div>\n\n          {/* Strategy Filter */}\n          <div className=\"mb-4\">\n            <label className=\"block text-sm text-slate-300 mb-2\">Strategy Filter</label>\n            <div className=\"flex gap-2\">\n              <Button\n                variant={selectedStrategy === 'both' ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => setSelectedStrategy('both')}\n                className={selectedStrategy === 'both' ? 'bg-blue-600' : 'border-slate-600 text-slate-300'}\n              >\n                All Strategies\n              </Button>\n              <Button\n                variant={selectedStrategy === 'overnight' ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => setSelectedStrategy('overnight')}\n                className={selectedStrategy === 'overnight' ? 'bg-purple-600' : 'border-slate-600 text-slate-300'}\n              >\n                <Moon className=\"mr-1 h-3 w-3\" />\n                Overnight\n              </Button>\n              <Button\n                variant={selectedStrategy === 'breakout' ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => setSelectedStrategy('breakout')}\n                className={selectedStrategy === 'breakout' ? 'bg-green-600' : 'border-slate-600 text-slate-300'}\n              >\n                <TrendingUp className=\"mr-1 h-3 w-3\" />\n                Breakout\n              </Button>\n            </div>\n          </div>\n\n          {/* Scan Buttons */}\n          <div className=\"flex gap-4 mb-4\">\n            <Button\n              onClick={() => handleStrategyScan('quick')}\n              disabled={isScanning}\n              className=\"bg-blue-600 hover:bg-blue-700\"\n            >\n              {isScanning ? (\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n              ) : (\n                <Zap className=\"mr-2 h-4 w-4\" />\n              )}\n              Quick Strategy Scan\n            </Button>\n            \n            <Button\n              onClick={() => handleStrategyScan('full')}\n              disabled={isScanning}\n              variant=\"outline\"\n              className=\"border-slate-600 text-slate-300 hover:bg-slate-800\"\n            >\n              {isScanning ? (\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n              ) : (\n                <Target className=\"mr-2 h-4 w-4\" />\n              )}\n              Full Strategy Scan\n            </Button>\n          </div>\n\n          {/* Market Conditions */}\n          {scanResults?.marketConditions && (\n            <div className=\"p-3 bg-slate-700/50 rounded-lg\">\n              <div className=\"flex items-center justify-between text-sm\">\n                <span className=\"text-slate-300\">Market Status:</span>\n                <div className=\"flex items-center gap-4\">\n                  <span className=\"text-white\">{scanResults.marketConditions.timeOfDay}</span>\n                  <div className=\"flex gap-2\">\n                    <Badge className={scanResults.marketConditions.marketHours\n                      ? 'bg-green-500/20 text-green-400'\n                      : 'bg-red-500/20 text-red-400'\n                    }>\n                      {scanResults.marketConditions.marketHours ? 'Market Open' : 'Market Closed'}\n                    </Badge>\n                    <Badge className={scanResults.marketConditions.isOptimalScanTime\n                      ? 'bg-blue-500/20 text-blue-400'\n                      : 'bg-yellow-500/20 text-yellow-400'\n                    }>\n                      {scanResults.marketConditions.isOptimalScanTime ? 'Optimal Scan Time' : 'Outside Optimal Hours'}\n                    </Badge>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {isScanning && (\n            <div className=\"text-center py-4\">\n              <Loader2 className=\"mx-auto h-8 w-8 animate-spin text-blue-400\" />\n              <p className=\"text-slate-300 mt-2\">\n                Analyzing stocks for professional swing trading setups...\n              </p>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Error Display */}\n      {error && (\n        <Card className=\"bg-red-900/20 border-red-500/50\">\n          <CardContent className=\"p-6\">\n            <p className=\"text-red-300 text-center\">{error}</p>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Strategy Scan Results */}\n      {scanResults && (\n        <div className=\"space-y-6\">\n          {/* Scan Summary */}\n          <Card className=\"bg-slate-800/50 border-slate-700\">\n            <CardHeader>\n              <CardTitle className=\"text-white\">Strategy Scan Summary</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-white\">{scanResults.totalScanned}</div>\n                  <div className=\"text-sm text-slate-300\">Stocks Scanned</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-purple-400\">{scanResults.overnightSetups}</div>\n                  <div className=\"text-sm text-slate-300\">Overnight Setups</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-green-400\">{scanResults.breakoutSetups}</div>\n                  <div className=\"text-sm text-slate-300\">Breakout Setups</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-blue-400\">{scanResults.bothStrategies}</div>\n                  <div className=\"text-sm text-slate-300\">Both Strategies</div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Professional Trading Setups */}\n          <div className=\"space-y-6\">\n            <div className=\"flex items-center justify-between\">\n              <h2 className=\"text-2xl font-bold text-white flex items-center\">\n                <Target className=\"mr-3 h-6 w-6 text-green-400\" />\n                Professional Trading Setups ({filteredResults.length})\n              </h2>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => window.open('https://www.tradingview.com', '_blank')}\n                className=\"text-slate-300 hover:text-white\"\n              >\n                <ExternalLink className=\"h-4 w-4 mr-2\" />\n                Open TradingView\n              </Button>\n            </div>\n\n            {filteredResults.map((result, index) => {\n              // Convert to TradingSetupCard format - prioritize best strategy\n              const bestSetup = result.bestStrategy === 'overnight_momentum'\n                ? result.overnightSetup\n                : result.breakoutSetup;\n\n              if (!bestSetup) return null;\n\n              const setupWithSymbol = {\n                ...bestSetup,\n                symbol: result.symbol\n              };\n\n              return (\n                <TradingSetupCard\n                  key={result.symbol}\n                  setup={setupWithSymbol}\n                  onExecuteTrade={handleExecuteTrade}\n                  onViewChart={handleViewChart}\n                />\n              );\n            })}\n          </div>\n\n          {/* AI Insights Section */}\n          {filteredResults.length > 0 && (\n            <div className=\"mt-8\">\n              <AIInsights\n                scanResults={filteredResults}\n                marketConditions={scanResults.marketConditions}\n                onRefresh={() => runScan()}\n              />\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AACA;;;AAVA;;;;;;;;AAiBO,SAAS,gBAAgB,KAAgE;QAAhE,EAAE,WAAW,KAAK,EAAE,cAAc,MAAM,EAAwB,GAAhE;;IAC9B,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,kMAAQ,EAAC;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,kMAAQ,EAA6B;IAC3E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,kMAAQ,EAAoC;IAC5F,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,kMAAQ,EAAgB;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,kMAAQ,EAAC;IAEvD,gCAAgC;IAChC,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,sDAAsD;YACtD,MAAM,AAAC,qBAAgD,OAA5B,MAAM,MAAM,EAAC,iBAAuD,OAAxC,MAAM,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,IAAG,MAA6C,OAAzC,MAAM,YAAY,CAAC,SAAS,EAAC,cAAqE,OAAzD,MAAM,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,IAAG,eAAqD,OAAxC,MAAM,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAG,cAA+B,OAAnB,MAAM,YAAY,EAAC;QAC9P,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,uBAAuB;IACvB,MAAM,kBAAkB,CAAC;QACvB,iDAAiD;QACjD,MAAM,WAAW,AAAC,6CAAmD,OAAP;QAC9D,OAAO,IAAI,CAAC,UAAU;IACxB;IAEA,0CAA0C;IAC1C,IAAA,mMAAS;qCAAC;YACR,IAAI,UAAU;gBACZ,mBAAmB;YACrB;QACF;oCAAG;QAAC;KAAS;IAEb,MAAM,qBAAqB,OAAO;QAChC,cAAc;QACd,SAAS;QACT,eAAe;QAEf,IAAI;YACF,MAAM,WAAW,MAAM,MACrB,AAAC,gCAAuD,OAAxB,UAAS,iBAA+B,OAAhB,iBAAgB;YAG1E,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,eAAe;QACjB,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,qBAAO,sNAAC,sOAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,sNAAC,4PAAU;oBAAC,WAAU;;;;;;YAC/B;gBACE,qBAAO,sNAAC,2PAAS;oBAAC,WAAU;;;;;;QAChC;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB,CAAA,wBAAA,kCAAA,YAAa,SAAS,CAAC,MAAM,CAAC,CAAA;QACpD,IAAI,qBAAqB,QAAQ,OAAO;QACxC,IAAI,qBAAqB,aAAa,OAAO,OAAO,cAAc;QAClE,IAAI,qBAAqB,YAAY,OAAO,OAAO,aAAa;QAChE,OAAO;IACT,OAAM,EAAE;IAER,qBACE,sNAAC;QAAI,WAAU;;0BAEb,sNAAC,oKAAI;gBAAC,WAAU;;kCACd,sNAAC,0KAAU;;0CACT,sNAAC,yKAAS;gCAAC,WAAU;;kDACnB,sNAAC,mOAAG;wCAAC,WAAU;;;;;;oCAAiC;;;;;;;0CAGlD,sNAAC,+KAAe;gCAAC,WAAU;0CAAiB;;;;;;;;;;;;kCAI9C,sNAAC,2KAAW;;0CAEV,sNAAC;gCAAI,WAAU;;kDACb,sNAAC;wCAAM,WAAU;kDAAoC;;;;;;kDACrD,sNAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,mBAAmB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;wCAChE,WAAU;wCACV,KAAI;wCACJ,MAAK;wCACL,UAAU;;;;;;;;;;;;0CAKd,sNAAC;gCAAI,WAAU;;kDACb,sNAAC;wCAAM,WAAU;kDAAoC;;;;;;kDACrD,sNAAC;wCAAI,WAAU;;0DACb,sNAAC,wKAAM;gDACL,SAAS,qBAAqB,SAAS,YAAY;gDACnD,MAAK;gDACL,SAAS,IAAM,oBAAoB;gDACnC,WAAW,qBAAqB,SAAS,gBAAgB;0DAC1D;;;;;;0DAGD,sNAAC,wKAAM;gDACL,SAAS,qBAAqB,cAAc,YAAY;gDACxD,MAAK;gDACL,SAAS,IAAM,oBAAoB;gDACnC,WAAW,qBAAqB,cAAc,kBAAkB;;kEAEhE,sNAAC,sOAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,sNAAC,wKAAM;gDACL,SAAS,qBAAqB,aAAa,YAAY;gDACvD,MAAK;gDACL,SAAS,IAAM,oBAAoB;gDACnC,WAAW,qBAAqB,aAAa,iBAAiB;;kEAE9D,sNAAC,4PAAU;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;0CAO7C,sNAAC;gCAAI,WAAU;;kDACb,sNAAC,wKAAM;wCACL,SAAS,IAAM,mBAAmB;wCAClC,UAAU;wCACV,WAAU;;4CAET,2BACC,sNAAC,wPAAO;gDAAC,WAAU;;;;;qEAEnB,sNAAC,mOAAG;gDAAC,WAAU;;;;;;4CACf;;;;;;;kDAIJ,sNAAC,wKAAM;wCACL,SAAS,IAAM,mBAAmB;wCAClC,UAAU;wCACV,SAAQ;wCACR,WAAU;;4CAET,2BACC,sNAAC,wPAAO;gDAAC,WAAU;;;;;qEAEnB,sNAAC,4OAAM;gDAAC,WAAU;;;;;;4CAClB;;;;;;;;;;;;;4BAML,CAAA,wBAAA,kCAAA,YAAa,gBAAgB,mBAC5B,sNAAC;gCAAI,WAAU;0CACb,cAAA,sNAAC;oCAAI,WAAU;;sDACb,sNAAC;4CAAK,WAAU;sDAAiB;;;;;;sDACjC,sNAAC;4CAAI,WAAU;;8DACb,sNAAC;oDAAK,WAAU;8DAAc,YAAY,gBAAgB,CAAC,SAAS;;;;;;8DACpE,sNAAC;oDAAI,WAAU;;sEACb,sNAAC,sKAAK;4DAAC,WAAW,YAAY,gBAAgB,CAAC,WAAW,GACtD,mCACA;sEAED,YAAY,gBAAgB,CAAC,WAAW,GAAG,gBAAgB;;;;;;sEAE9D,sNAAC,sKAAK;4DAAC,WAAW,YAAY,gBAAgB,CAAC,iBAAiB,GAC5D,iCACA;sEAED,YAAY,gBAAgB,CAAC,iBAAiB,GAAG,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAQnF,4BACC,sNAAC;gCAAI,WAAU;;kDACb,sNAAC,wPAAO;wCAAC,WAAU;;;;;;kDACnB,sNAAC;wCAAE,WAAU;kDAAsB;;;;;;;;;;;;;;;;;;;;;;;;YAS1C,uBACC,sNAAC,oKAAI;gBAAC,WAAU;0BACd,cAAA,sNAAC,2KAAW;oBAAC,WAAU;8BACrB,cAAA,sNAAC;wBAAE,WAAU;kCAA4B;;;;;;;;;;;;;;;;YAM9C,6BACC,sNAAC;gBAAI,WAAU;;kCAEb,sNAAC,oKAAI;wBAAC,WAAU;;0CACd,sNAAC,0KAAU;0CACT,cAAA,sNAAC,yKAAS;oCAAC,WAAU;8CAAa;;;;;;;;;;;0CAEpC,sNAAC,2KAAW;0CACV,cAAA,sNAAC;oCAAI,WAAU;;sDACb,sNAAC;4CAAI,WAAU;;8DACb,sNAAC;oDAAI,WAAU;8DAAiC,YAAY,YAAY;;;;;;8DACxE,sNAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;sDAE1C,sNAAC;4CAAI,WAAU;;8DACb,sNAAC;oDAAI,WAAU;8DAAsC,YAAY,eAAe;;;;;;8DAChF,sNAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;sDAE1C,sNAAC;4CAAI,WAAU;;8DACb,sNAAC;oDAAI,WAAU;8DAAqC,YAAY,cAAc;;;;;;8DAC9E,sNAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;sDAE1C,sNAAC;4CAAI,WAAU;;8DACb,sNAAC;oDAAI,WAAU;8DAAoC,YAAY,cAAc;;;;;;8DAC7E,sNAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOhD,sNAAC;wBAAI,WAAU;;0CACb,sNAAC;gCAAI,WAAU;;kDACb,sNAAC;wCAAG,WAAU;;0DACZ,sNAAC,4OAAM;gDAAC,WAAU;;;;;;4CAAgC;4CACpB,gBAAgB,MAAM;4CAAC;;;;;;;kDAEvD,sNAAC,wKAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC,+BAA+B;wCAC1D,WAAU;;0DAEV,sNAAC,kQAAY;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;4BAK5C,gBAAgB,GAAG,CAAC,CAAC,QAAQ;gCAC5B,gEAAgE;gCAChE,MAAM,YAAY,OAAO,YAAY,KAAK,uBACtC,OAAO,cAAc,GACrB,OAAO,aAAa;gCAExB,IAAI,CAAC,WAAW,OAAO;gCAEvB,MAAM,kBAAkB;oCACtB,GAAG,SAAS;oCACZ,QAAQ,OAAO,MAAM;gCACvB;gCAEA,qBACE,sNAAC,6KAAgB;oCAEf,OAAO;oCACP,gBAAgB;oCAChB,aAAa;mCAHR,OAAO,MAAM;;;;;4BAMxB;;;;;;;oBAID,gBAAgB,MAAM,GAAG,mBACxB,sNAAC;wBAAI,WAAU;kCACb,cAAA,sNAAC,uKAAU;4BACT,aAAa;4BACb,kBAAkB,YAAY,gBAAgB;4BAC9C,WAAW,IAAM;;;;;;;;;;;;;;;;;;;;;;;AAQjC;GA5TgB;KAAA", "debugId": null}}, {"offset": {"line": 3693, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/components/ui/tabs.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-slate-100 p-1 text-slate-500 dark:bg-slate-800 dark:text-slate-400\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-white transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-white data-[state=active]:text-slate-950 data-[state=active]:shadow-sm dark:ring-offset-slate-950 dark:focus-visible:ring-slate-300 dark:data-[state=active]:bg-slate-950 dark:data-[state=active]:text-slate-50\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2 dark:ring-offset-slate-950 dark:focus-visible:ring-slate-300\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,OAAO,oMAAkB;AAE/B,MAAM,yBAAW,oMAAgB,MAG/B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,sNAAC,oMAAkB;QACjB,KAAK;QACL,WAAW,IAAA,qJAAE,EACX,iIACA;QAED,GAAG,KAAK;;;;;;;;AAGb,SAAS,WAAW,GAAG,oMAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,oMAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,sNAAC,uMAAqB;QACpB,KAAK;QACL,WAAW,IAAA,qJAAE,EACX,2gBACA;QAED,GAAG,KAAK;;;;;;;;AAGb,YAAY,WAAW,GAAG,uMAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,oMAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,sNAAC,uMAAqB;QACpB,KAAK;QACL,WAAW,IAAA,qJAAE,EACX,gMACA;QAED,GAAG,KAAK;;;;;;;;AAGb,YAAY,WAAW,GAAG,uMAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3769, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/components/PerfectPickSetupCard.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  TrendingUp,\n  TrendingDown,\n  Target,\n  Shield,\n  Clock,\n  DollarSign,\n  AlertTriangle,\n  CheckCircle,\n  Play,\n  Eye,\n  Zap,\n  Activity,\n  Star,\n  ChevronDown,\n  ChevronUp\n} from 'lucide-react'\nimport { PerfectPickSetup } from '@/types/trading'\nimport { formatCurrency, formatPercentage } from '@/lib/utils'\n\ninterface PerfectPickSetupCardProps {\n  setup: PerfectPickSetup\n  onExecuteTrade?: (setup: PerfectPickSetup) => void\n  onViewChart?: (symbol: string) => void\n  onGenerateEntryTrigger?: (symbol: string, preMarketHigh: number) => void\n}\n\nexport function PerfectPickSetupCard({ \n  setup, \n  onExecuteTrade, \n  onViewChart,\n  onGenerateEntryTrigger \n}: PerfectPickSetupCardProps) {\n  const [showDetails, setShowDetails] = useState(false)\n\n  const getCatalystBadgeColor = (tier: string) => {\n    switch (tier) {\n      case 'tier_1': return 'bg-green-500 text-white'\n      case 'tier_2': return 'bg-yellow-500 text-white'\n      case 'tier_3': return 'bg-gray-500 text-white'\n      default: return 'bg-gray-400 text-white'\n    }\n  }\n\n  const getCatalystImpactIcon = (impact: string) => {\n    switch (impact) {\n      case 'bullish': return <TrendingUp className=\"h-4 w-4 text-green-500\" />\n      case 'bearish': return <TrendingDown className=\"h-4 w-4 text-red-500\" />\n      default: return <Activity className=\"h-4 w-4 text-gray-500\" />\n    }\n  }\n\n  const getSetupGradeColor = (grade: string) => {\n    if (grade.startsWith('A')) return 'text-green-600 bg-green-50 border-green-200'\n    if (grade.startsWith('B')) return 'text-blue-600 bg-blue-50 border-blue-200'\n    if (grade.startsWith('C')) return 'text-yellow-600 bg-yellow-50 border-yellow-200'\n    return 'text-red-600 bg-red-50 border-red-200'\n  }\n\n  const getFreshnessColor = (freshness: string) => {\n    switch (freshness) {\n      case 'fresh': return 'text-green-600 bg-green-50'\n      case 'moderate': return 'text-yellow-600 bg-yellow-50'\n      case 'stale': return 'text-red-600 bg-red-50'\n      default: return 'text-gray-600 bg-gray-50'\n    }\n  }\n\n  const riskRewardRatio = setup.rewardPlanning.riskRewardRatio\n  const potentialProfit = setup.riskManagement.positionSize * setup.riskManagement.riskPerShare * riskRewardRatio\n  const riskAmount = setup.riskManagement.positionSize * setup.riskManagement.riskPerShare\n\n  return (\n    <Card className=\"hover:shadow-lg transition-all duration-200 border-l-4 border-l-blue-500\">\n      <CardHeader className=\"pb-3\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-3\">\n            <div>\n              <CardTitle className=\"text-xl font-bold flex items-center gap-2\">\n                {setup.symbol}\n                <Star className=\"h-5 w-5 text-yellow-500\" />\n              </CardTitle>\n              <p className=\"text-sm text-muted-foreground\">{setup.name}</p>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              <Badge className={`${getSetupGradeColor(setup.setupGrade)} border`}>\n                Grade {setup.setupGrade}\n              </Badge>\n              <Badge variant=\"outline\" className=\"font-semibold\">\n                Score: {setup.overallScore}/100\n              </Badge>\n            </div>\n          </div>\n          <div className=\"text-right\">\n            <div className=\"text-2xl font-bold text-blue-600\">\n              {formatCurrency(setup.gapScan.price)}\n            </div>\n            <div className=\"text-sm font-semibold text-green-600\">\n              Gap: +{formatPercentage(setup.gapScan.gapPercent)}\n            </div>\n          </div>\n        </div>\n      </CardHeader>\n\n      <CardContent className=\"space-y-4\">\n        {/* Catalyst Information */}\n        <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border\">\n          <div className=\"flex items-center gap-2 mb-2\">\n            <Zap className=\"h-5 w-5 text-purple-600\" />\n            <span className=\"font-semibold text-purple-800\">Catalyst</span>\n            {getCatalystImpactIcon(setup.catalyst.impact)}\n            <Badge className={getCatalystBadgeColor(setup.catalyst.tier)}>\n              {setup.catalyst.tier.replace('_', ' ').toUpperCase()}\n            </Badge>\n            <Badge className={getFreshnessColor(setup.catalyst.freshness)}>\n              {setup.catalyst.freshness}\n            </Badge>\n          </div>\n          <h4 className=\"font-medium text-gray-800 mb-1\">{setup.catalyst.title}</h4>\n          <p className=\"text-sm text-gray-600 mb-2\">{setup.catalyst.description}</p>\n          <div className=\"flex items-center gap-4 text-xs text-gray-500\">\n            <span>Quality: {setup.catalyst.qualityScore}/10</span>\n            <span>Source: {setup.catalyst.source}</span>\n            <span className=\"flex items-center gap-1\">\n              <Clock className=\"h-3 w-3\" />\n              {new Date(setup.catalyst.announcementTime).toLocaleString()}\n            </span>\n          </div>\n        </div>\n\n        {/* Risk/Reward Summary */}\n        <div className=\"grid grid-cols-4 gap-4\">\n          <div className=\"text-center p-3 bg-blue-50 rounded-lg\">\n            <div className=\"text-sm text-blue-600 font-medium\">Entry</div>\n            <div className=\"text-lg font-bold text-blue-800\">\n              {formatCurrency(setup.riskManagement.entryPrice)}\n            </div>\n          </div>\n          <div className=\"text-center p-3 bg-red-50 rounded-lg\">\n            <div className=\"text-sm text-red-600 font-medium\">Stop Loss</div>\n            <div className=\"text-lg font-bold text-red-800\">\n              {formatCurrency(setup.riskManagement.stopLoss)}\n            </div>\n          </div>\n          <div className=\"text-center p-3 bg-green-50 rounded-lg\">\n            <div className=\"text-sm text-green-600 font-medium\">Target (3R)</div>\n            <div className=\"text-lg font-bold text-green-800\">\n              {formatCurrency(setup.rewardPlanning.target3R)}\n            </div>\n          </div>\n          <div className=\"text-center p-3 bg-purple-50 rounded-lg\">\n            <div className=\"text-sm text-purple-600 font-medium\">R/R Ratio</div>\n            <div className=\"text-lg font-bold text-purple-800\">\n              {riskRewardRatio}:1\n            </div>\n          </div>\n        </div>\n\n        {/* Position Sizing */}\n        <div className=\"bg-gray-50 p-4 rounded-lg\">\n          <div className=\"grid grid-cols-3 gap-4 text-sm\">\n            <div>\n              <span className=\"text-gray-600\">Position Size:</span>\n              <div className=\"font-semibold\">{setup.riskManagement.positionSize} shares</div>\n            </div>\n            <div>\n              <span className=\"text-gray-600\">Risk Amount:</span>\n              <div className=\"font-semibold text-red-600\">{formatCurrency(riskAmount)}</div>\n            </div>\n            <div>\n              <span className=\"text-gray-600\">Potential Profit (3R):</span>\n              <div className=\"font-semibold text-green-600\">{formatCurrency(potentialProfit)}</div>\n            </div>\n          </div>\n        </div>\n\n        {/* Technical Gate Summary */}\n        <div className=\"flex items-center justify-between p-3 bg-slate-50 rounded-lg\">\n          <div className=\"flex items-center gap-2\">\n            <Target className=\"h-4 w-4 text-slate-600\" />\n            <span className=\"text-sm font-medium\">Technical Gate:</span>\n            <Badge variant={setup.technicalGate.overallGrade === 'A' ? 'default' : 'secondary'}>\n              Grade {setup.technicalGate.overallGrade}\n            </Badge>\n            <span className=\"text-xs text-muted-foreground\">\n              ({setup.technicalGate.gateScore}/100)\n            </span>\n          </div>\n          <div className=\"flex items-center gap-1 text-xs text-muted-foreground\">\n            {setup.technicalGate.aboveSMA200 && <CheckCircle className=\"h-3 w-3 text-green-500\" />}\n            {setup.technicalGate.aboveEMA8 && <CheckCircle className=\"h-3 w-3 text-green-500\" />}\n            {setup.technicalGate.dailyTrendConfirmed && <CheckCircle className=\"h-3 w-3 text-green-500\" />}\n          </div>\n        </div>\n\n        {/* Entry Trigger Status */}\n        {setup.entryTrigger && (\n          <div className=\"p-3 bg-yellow-50 border border-yellow-200 rounded-lg\">\n            <div className=\"flex items-center gap-2 mb-1\">\n              <Play className=\"h-4 w-4 text-yellow-600\" />\n              <span className=\"font-medium text-yellow-800\">Entry Trigger</span>\n              <Badge variant=\"outline\" className=\"text-xs\">\n                {setup.entryTrigger.urgency.replace('_', ' ')}\n              </Badge>\n            </div>\n            <p className=\"text-sm text-yellow-700\">\n              {setup.entryTrigger.entrySignalType.replace('_', ' ').toUpperCase()} at {formatCurrency(setup.entryTrigger.entryPrice)}\n            </p>\n          </div>\n        )}\n\n        {/* Validation Checks */}\n        <div className=\"grid grid-cols-2 gap-2 text-xs\">\n          {Object.entries(setup.validationChecks).map(([key, value]) => (\n            <div key={key} className=\"flex items-center gap-1\">\n              {value ? (\n                <CheckCircle className=\"h-3 w-3 text-green-500\" />\n              ) : (\n                <AlertTriangle className=\"h-3 w-3 text-red-500\" />\n              )}\n              <span className={value ? 'text-green-700' : 'text-red-700'}>\n                {key.replace(/([A-Z])/g, ' $1').toLowerCase()}\n              </span>\n            </div>\n          ))}\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex items-center gap-2 pt-2\">\n          <Button \n            onClick={() => onExecuteTrade?.(setup)}\n            className=\"flex-1 bg-blue-600 hover:bg-blue-700\"\n            disabled={!setup.validationChecks.noExclusionFlags}\n          >\n            <Play className=\"h-4 w-4 mr-2\" />\n            Execute Trade\n          </Button>\n          <Button \n            variant=\"outline\" \n            onClick={() => onViewChart?.(setup.symbol)}\n            className=\"flex-1\"\n          >\n            <Eye className=\"h-4 w-4 mr-2\" />\n            View Chart\n          </Button>\n          {!setup.entryTrigger && (\n            <Button \n              variant=\"outline\" \n              onClick={() => onGenerateEntryTrigger?.(setup.symbol, setup.gapScan.preMarketHigh)}\n              className=\"flex-1\"\n            >\n              <Target className=\"h-4 w-4 mr-2\" />\n              Entry Trigger\n            </Button>\n          )}\n        </div>\n\n        {/* Expandable Details */}\n        <Button\n          variant=\"ghost\"\n          onClick={() => setShowDetails(!showDetails)}\n          className=\"w-full text-sm\"\n        >\n          {showDetails ? (\n            <>\n              <ChevronUp className=\"h-4 w-4 mr-2\" />\n              Hide Details\n            </>\n          ) : (\n            <>\n              <ChevronDown className=\"h-4 w-4 mr-2\" />\n              Show Details\n            </>\n          )}\n        </Button>\n\n        {/* Detailed Information */}\n        {showDetails && (\n          <div className=\"space-y-3 pt-3 border-t\">\n            {/* Scale-out Plan */}\n            <div>\n              <h4 className=\"font-medium mb-2\">Scale-out Plan</h4>\n              <div className=\"space-y-1 text-sm\">\n                {setup.rewardPlanning.scaleOutPlan.map((plan, index) => (\n                  <div key={index} className=\"flex justify-between\">\n                    <span>At {plan.level}R ({formatCurrency(setup.riskManagement.entryPrice + (setup.riskManagement.riskPerShare * plan.level))}):</span>\n                    <span className=\"font-medium\">{plan.percentage}%</span>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Technical Levels */}\n            <div>\n              <h4 className=\"font-medium mb-2\">Key Technical Levels</h4>\n              <div className=\"grid grid-cols-2 gap-2 text-sm\">\n                <div>SMA200: {formatCurrency(setup.technicalGate.keyTechnicalLevels.sma200)}</div>\n                <div>EMA8: {formatCurrency(setup.technicalGate.keyTechnicalLevels.ema8)}</div>\n                <div>VWAP: {formatCurrency(setup.technicalGate.keyTechnicalLevels.vwap)}</div>\n                <div>Prev High: {formatCurrency(setup.technicalGate.keyTechnicalLevels.previousHigh)}</div>\n              </div>\n            </div>\n\n            {/* Exclusion Reasons */}\n            {setup.exclusionReasons.length > 0 && (\n              <div>\n                <h4 className=\"font-medium mb-2 text-red-600\">Exclusion Reasons</h4>\n                <ul className=\"text-sm text-red-600 space-y-1\">\n                  {setup.exclusionReasons.map((reason, index) => (\n                    <li key={index} className=\"flex items-center gap-1\">\n                      <AlertTriangle className=\"h-3 w-3\" />\n                      {reason}\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            )}\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA;;;AAxBA;;;;;;;AAiCO,SAAS,qBAAqB,KAKT;QALS,EACnC,KAAK,EACL,cAAc,EACd,WAAW,EACX,sBAAsB,EACI,GALS;;IAMnC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,kMAAQ,EAAC;IAE/C,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBAAW,qBAAO,sNAAC,4PAAU;oBAAC,WAAU;;;;;;YAC7C,KAAK;gBAAW,qBAAO,sNAAC,kQAAY;oBAAC,WAAU;;;;;;YAC/C;gBAAS,qBAAO,sNAAC,kPAAQ;oBAAC,WAAU;;;;;;QACtC;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,MAAM,UAAU,CAAC,MAAM,OAAO;QAClC,IAAI,MAAM,UAAU,CAAC,MAAM,OAAO;QAClC,IAAI,MAAM,UAAU,CAAC,MAAM,OAAO;QAClC,OAAO;IACT;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,kBAAkB,MAAM,cAAc,CAAC,eAAe;IAC5D,MAAM,kBAAkB,MAAM,cAAc,CAAC,YAAY,GAAG,MAAM,cAAc,CAAC,YAAY,GAAG;IAChG,MAAM,aAAa,MAAM,cAAc,CAAC,YAAY,GAAG,MAAM,cAAc,CAAC,YAAY;IAExF,qBACE,sNAAC,oKAAI;QAAC,WAAU;;0BACd,sNAAC,0KAAU;gBAAC,WAAU;0BACpB,cAAA,sNAAC;oBAAI,WAAU;;sCACb,sNAAC;4BAAI,WAAU;;8CACb,sNAAC;;sDACC,sNAAC,yKAAS;4CAAC,WAAU;;gDAClB,MAAM,MAAM;8DACb,sNAAC,sOAAI;oDAAC,WAAU;;;;;;;;;;;;sDAElB,sNAAC;4CAAE,WAAU;sDAAiC,MAAM,IAAI;;;;;;;;;;;;8CAE1D,sNAAC;oCAAI,WAAU;;sDACb,sNAAC,sKAAK;4CAAC,WAAW,AAAC,GAAuC,OAArC,mBAAmB,MAAM,UAAU,GAAE;;gDAAU;gDAC3D,MAAM,UAAU;;;;;;;sDAEzB,sNAAC,sKAAK;4CAAC,SAAQ;4CAAU,WAAU;;gDAAgB;gDACzC,MAAM,YAAY;gDAAC;;;;;;;;;;;;;;;;;;;sCAIjC,sNAAC;4BAAI,WAAU;;8CACb,sNAAC;oCAAI,WAAU;8CACZ,IAAA,iKAAc,EAAC,MAAM,OAAO,CAAC,KAAK;;;;;;8CAErC,sNAAC;oCAAI,WAAU;;wCAAuC;wCAC7C,IAAA,mKAAgB,EAAC,MAAM,OAAO,CAAC,UAAU;;;;;;;;;;;;;;;;;;;;;;;;0BAMxD,sNAAC,2KAAW;gBAAC,WAAU;;kCAErB,sNAAC;wBAAI,WAAU;;0CACb,sNAAC;gCAAI,WAAU;;kDACb,sNAAC,mOAAG;wCAAC,WAAU;;;;;;kDACf,sNAAC;wCAAK,WAAU;kDAAgC;;;;;;oCAC/C,sBAAsB,MAAM,QAAQ,CAAC,MAAM;kDAC5C,sNAAC,sKAAK;wCAAC,WAAW,sBAAsB,MAAM,QAAQ,CAAC,IAAI;kDACxD,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;kDAEpD,sNAAC,sKAAK;wCAAC,WAAW,kBAAkB,MAAM,QAAQ,CAAC,SAAS;kDACzD,MAAM,QAAQ,CAAC,SAAS;;;;;;;;;;;;0CAG7B,sNAAC;gCAAG,WAAU;0CAAkC,MAAM,QAAQ,CAAC,KAAK;;;;;;0CACpE,sNAAC;gCAAE,WAAU;0CAA8B,MAAM,QAAQ,CAAC,WAAW;;;;;;0CACrE,sNAAC;gCAAI,WAAU;;kDACb,sNAAC;;4CAAK;4CAAU,MAAM,QAAQ,CAAC,YAAY;4CAAC;;;;;;;kDAC5C,sNAAC;;4CAAK;4CAAS,MAAM,QAAQ,CAAC,MAAM;;;;;;;kDACpC,sNAAC;wCAAK,WAAU;;0DACd,sNAAC,yOAAK;gDAAC,WAAU;;;;;;4CAChB,IAAI,KAAK,MAAM,QAAQ,CAAC,gBAAgB,EAAE,cAAc;;;;;;;;;;;;;;;;;;;kCAM/D,sNAAC;wBAAI,WAAU;;0CACb,sNAAC;gCAAI,WAAU;;kDACb,sNAAC;wCAAI,WAAU;kDAAoC;;;;;;kDACnD,sNAAC;wCAAI,WAAU;kDACZ,IAAA,iKAAc,EAAC,MAAM,cAAc,CAAC,UAAU;;;;;;;;;;;;0CAGnD,sNAAC;gCAAI,WAAU;;kDACb,sNAAC;wCAAI,WAAU;kDAAmC;;;;;;kDAClD,sNAAC;wCAAI,WAAU;kDACZ,IAAA,iKAAc,EAAC,MAAM,cAAc,CAAC,QAAQ;;;;;;;;;;;;0CAGjD,sNAAC;gCAAI,WAAU;;kDACb,sNAAC;wCAAI,WAAU;kDAAqC;;;;;;kDACpD,sNAAC;wCAAI,WAAU;kDACZ,IAAA,iKAAc,EAAC,MAAM,cAAc,CAAC,QAAQ;;;;;;;;;;;;0CAGjD,sNAAC;gCAAI,WAAU;;kDACb,sNAAC;wCAAI,WAAU;kDAAsC;;;;;;kDACrD,sNAAC;wCAAI,WAAU;;4CACZ;4CAAgB;;;;;;;;;;;;;;;;;;;kCAMvB,sNAAC;wBAAI,WAAU;kCACb,cAAA,sNAAC;4BAAI,WAAU;;8CACb,sNAAC;;sDACC,sNAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,sNAAC;4CAAI,WAAU;;gDAAiB,MAAM,cAAc,CAAC,YAAY;gDAAC;;;;;;;;;;;;;8CAEpE,sNAAC;;sDACC,sNAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,sNAAC;4CAAI,WAAU;sDAA8B,IAAA,iKAAc,EAAC;;;;;;;;;;;;8CAE9D,sNAAC;;sDACC,sNAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,sNAAC;4CAAI,WAAU;sDAAgC,IAAA,iKAAc,EAAC;;;;;;;;;;;;;;;;;;;;;;;kCAMpE,sNAAC;wBAAI,WAAU;;0CACb,sNAAC;gCAAI,WAAU;;kDACb,sNAAC,4OAAM;wCAAC,WAAU;;;;;;kDAClB,sNAAC;wCAAK,WAAU;kDAAsB;;;;;;kDACtC,sNAAC,sKAAK;wCAAC,SAAS,MAAM,aAAa,CAAC,YAAY,KAAK,MAAM,YAAY;;4CAAa;4CAC3E,MAAM,aAAa,CAAC,YAAY;;;;;;;kDAEzC,sNAAC;wCAAK,WAAU;;4CAAgC;4CAC5C,MAAM,aAAa,CAAC,SAAS;4CAAC;;;;;;;;;;;;;0CAGpC,sNAAC;gCAAI,WAAU;;oCACZ,MAAM,aAAa,CAAC,WAAW,kBAAI,sNAAC,sQAAW;wCAAC,WAAU;;;;;;oCAC1D,MAAM,aAAa,CAAC,SAAS,kBAAI,sNAAC,sQAAW;wCAAC,WAAU;;;;;;oCACxD,MAAM,aAAa,CAAC,mBAAmB,kBAAI,sNAAC,sQAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;oBAKtE,MAAM,YAAY,kBACjB,sNAAC;wBAAI,WAAU;;0CACb,sNAAC;gCAAI,WAAU;;kDACb,sNAAC,sOAAI;wCAAC,WAAU;;;;;;kDAChB,sNAAC;wCAAK,WAAU;kDAA8B;;;;;;kDAC9C,sNAAC,sKAAK;wCAAC,SAAQ;wCAAU,WAAU;kDAChC,MAAM,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;0CAG7C,sNAAC;gCAAE,WAAU;;oCACV,MAAM,YAAY,CAAC,eAAe,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW;oCAAG;oCAAK,IAAA,iKAAc,EAAC,MAAM,YAAY,CAAC,UAAU;;;;;;;;;;;;;kCAM3H,sNAAC;wBAAI,WAAU;kCACZ,OAAO,OAAO,CAAC,MAAM,gBAAgB,EAAE,GAAG,CAAC;gCAAC,CAAC,KAAK,MAAM;iDACvD,sNAAC;gCAAc,WAAU;;oCACtB,sBACC,sNAAC,sQAAW;wCAAC,WAAU;;;;;6DAEvB,sNAAC,qQAAa;wCAAC,WAAU;;;;;;kDAE3B,sNAAC;wCAAK,WAAW,QAAQ,mBAAmB;kDACzC,IAAI,OAAO,CAAC,YAAY,OAAO,WAAW;;;;;;;+BAPrC;;;;;;;;;;;kCAcd,sNAAC;wBAAI,WAAU;;0CACb,sNAAC,wKAAM;gCACL,SAAS,IAAM,2BAAA,qCAAA,eAAiB;gCAChC,WAAU;gCACV,UAAU,CAAC,MAAM,gBAAgB,CAAC,gBAAgB;;kDAElD,sNAAC,sOAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGnC,sNAAC,wKAAM;gCACL,SAAQ;gCACR,SAAS,IAAM,wBAAA,kCAAA,YAAc,MAAM,MAAM;gCACzC,WAAU;;kDAEV,sNAAC,mOAAG;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;4BAGjC,CAAC,MAAM,YAAY,kBAClB,sNAAC,wKAAM;gCACL,SAAQ;gCACR,SAAS,IAAM,mCAAA,6CAAA,uBAAyB,MAAM,MAAM,EAAE,MAAM,OAAO,CAAC,aAAa;gCACjF,WAAU;;kDAEV,sNAAC,4OAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAOzC,sNAAC,wKAAM;wBACL,SAAQ;wBACR,SAAS,IAAM,eAAe,CAAC;wBAC/B,WAAU;kCAET,4BACC;;8CACE,sNAAC,yPAAS;oCAAC,WAAU;;;;;;gCAAiB;;yDAIxC;;8CACE,sNAAC,+PAAW;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;oBAO7C,6BACC,sNAAC;wBAAI,WAAU;;0CAEb,sNAAC;;kDACC,sNAAC;wCAAG,WAAU;kDAAmB;;;;;;kDACjC,sNAAC;wCAAI,WAAU;kDACZ,MAAM,cAAc,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC5C,sNAAC;gDAAgB,WAAU;;kEACzB,sNAAC;;4DAAK;4DAAI,KAAK,KAAK;4DAAC;4DAAI,IAAA,iKAAc,EAAC,MAAM,cAAc,CAAC,UAAU,GAAI,MAAM,cAAc,CAAC,YAAY,GAAG,KAAK,KAAK;4DAAG;;;;;;;kEAC5H,sNAAC;wDAAK,WAAU;;4DAAe,KAAK,UAAU;4DAAC;;;;;;;;+CAFvC;;;;;;;;;;;;;;;;0CAShB,sNAAC;;kDACC,sNAAC;wCAAG,WAAU;kDAAmB;;;;;;kDACjC,sNAAC;wCAAI,WAAU;;0DACb,sNAAC;;oDAAI;oDAAS,IAAA,iKAAc,EAAC,MAAM,aAAa,CAAC,kBAAkB,CAAC,MAAM;;;;;;;0DAC1E,sNAAC;;oDAAI;oDAAO,IAAA,iKAAc,EAAC,MAAM,aAAa,CAAC,kBAAkB,CAAC,IAAI;;;;;;;0DACtE,sNAAC;;oDAAI;oDAAO,IAAA,iKAAc,EAAC,MAAM,aAAa,CAAC,kBAAkB,CAAC,IAAI;;;;;;;0DACtE,sNAAC;;oDAAI;oDAAY,IAAA,iKAAc,EAAC,MAAM,aAAa,CAAC,kBAAkB,CAAC,YAAY;;;;;;;;;;;;;;;;;;;4BAKtF,MAAM,gBAAgB,CAAC,MAAM,GAAG,mBAC/B,sNAAC;;kDACC,sNAAC;wCAAG,WAAU;kDAAgC;;;;;;kDAC9C,sNAAC;wCAAG,WAAU;kDACX,MAAM,gBAAgB,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACnC,sNAAC;gDAAe,WAAU;;kEACxB,sNAAC,qQAAa;wDAAC,WAAU;;;;;;oDACxB;;+CAFM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAa7B;GAvSgB;KAAA", "debugId": null}}, {"offset": {"line": 4779, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/lib/alertSystem.ts"], "sourcesContent": ["import { PerfectPickSetup, PreMarketGapScan, Catalyst } from '@/types/trading'\n\nexport type AlertType = \n  | 'new_catalyst'\n  | 'perfect_pick_found'\n  | 'pre_market_gap'\n  | 'pmh_break'\n  | 'stop_loss_hit'\n  | 'profit_target_hit'\n  | 'entry_trigger'\n  | 'volume_spike'\n\nexport interface Alert {\n  id: string\n  type: AlertType\n  symbol: string\n  title: string\n  message: string\n  priority: 'low' | 'medium' | 'high' | 'critical'\n  timestamp: string\n  data?: any\n  read: boolean\n  actionable: boolean\n  actions?: AlertAction[]\n}\n\nexport interface AlertAction {\n  id: string\n  label: string\n  type: 'execute_trade' | 'view_chart' | 'update_stop' | 'take_profit' | 'dismiss'\n  data?: any\n}\n\nexport class AlertSystem {\n  private alerts: Alert[] = []\n  private subscribers: ((alerts: Alert[]) => void)[] = []\n  private alertSound: HTMLAudioElement | null = null\n\n  constructor() {\n    // Initialize alert sound (optional)\n    if (typeof window !== 'undefined') {\n      this.alertSound = new Audio('/alert-sound.mp3') // Add alert sound file to public folder\n      this.alertSound.volume = 0.5\n    }\n  }\n\n  /**\n   * Subscribe to alert updates\n   */\n  subscribe(callback: (alerts: Alert[]) => void): () => void {\n    this.subscribers.push(callback)\n    \n    // Return unsubscribe function\n    return () => {\n      const index = this.subscribers.indexOf(callback)\n      if (index > -1) {\n        this.subscribers.splice(index, 1)\n      }\n    }\n  }\n\n  /**\n   * Add a new alert\n   */\n  addAlert(alert: Omit<Alert, 'id' | 'timestamp' | 'read'>): void {\n    const newAlert: Alert = {\n      ...alert,\n      id: this.generateAlertId(),\n      timestamp: new Date().toISOString(),\n      read: false\n    }\n\n    this.alerts.unshift(newAlert) // Add to beginning of array\n    \n    // Keep only last 100 alerts\n    if (this.alerts.length > 100) {\n      this.alerts = this.alerts.slice(0, 100)\n    }\n\n    // Play sound for high/critical priority alerts\n    if (newAlert.priority === 'high' || newAlert.priority === 'critical') {\n      this.playAlertSound()\n    }\n\n    // Show browser notification for critical alerts\n    if (newAlert.priority === 'critical') {\n      this.showBrowserNotification(newAlert)\n    }\n\n    this.notifySubscribers()\n  }\n\n  /**\n   * Mark alert as read\n   */\n  markAsRead(alertId: string): void {\n    const alert = this.alerts.find(a => a.id === alertId)\n    if (alert) {\n      alert.read = true\n      this.notifySubscribers()\n    }\n  }\n\n  /**\n   * Mark all alerts as read\n   */\n  markAllAsRead(): void {\n    this.alerts.forEach(alert => alert.read = true)\n    this.notifySubscribers()\n  }\n\n  /**\n   * Remove alert\n   */\n  removeAlert(alertId: string): void {\n    this.alerts = this.alerts.filter(a => a.id !== alertId)\n    this.notifySubscribers()\n  }\n\n  /**\n   * Clear all alerts\n   */\n  clearAllAlerts(): void {\n    this.alerts = []\n    this.notifySubscribers()\n  }\n\n  /**\n   * Get all alerts\n   */\n  getAlerts(): Alert[] {\n    return [...this.alerts]\n  }\n\n  /**\n   * Get unread alerts count\n   */\n  getUnreadCount(): number {\n    return this.alerts.filter(a => !a.read).length\n  }\n\n  /**\n   * Create catalyst alert\n   */\n  createCatalystAlert(catalyst: Catalyst): void {\n    const priority = catalyst.tier === 'tier_1' ? 'high' : \n                    catalyst.tier === 'tier_2' ? 'medium' : 'low'\n\n    this.addAlert({\n      type: 'new_catalyst',\n      symbol: catalyst.symbol,\n      title: `New ${catalyst.tier.replace('_', ' ').toUpperCase()} Catalyst: ${catalyst.symbol}`,\n      message: catalyst.title,\n      priority,\n      data: { catalyst },\n      actionable: true,\n      actions: [\n        {\n          id: 'view_details',\n          label: 'View Details',\n          type: 'view_chart',\n          data: { symbol: catalyst.symbol }\n        }\n      ]\n    })\n  }\n\n  /**\n   * Create Perfect-Pick alert\n   */\n  createPerfectPickAlert(setup: PerfectPickSetup): void {\n    this.addAlert({\n      type: 'perfect_pick_found',\n      symbol: setup.symbol,\n      title: `Perfect-Pick Setup Found: ${setup.symbol}`,\n      message: `Grade ${setup.setupGrade} setup with ${setup.catalyst.type.replace(/_/g, ' ')} catalyst`,\n      priority: setup.setupGrade.startsWith('A') ? 'high' : 'medium',\n      data: { setup },\n      actionable: true,\n      actions: [\n        {\n          id: 'execute_trade',\n          label: 'Execute Trade',\n          type: 'execute_trade',\n          data: { setup }\n        },\n        {\n          id: 'view_chart',\n          label: 'View Chart',\n          type: 'view_chart',\n          data: { symbol: setup.symbol }\n        }\n      ]\n    })\n  }\n\n  /**\n   * Create pre-market gap alert\n   */\n  createGapAlert(gapScan: PreMarketGapScan): void {\n    const priority = gapScan.gapPercent > 10 ? 'high' : \n                    gapScan.gapPercent > 5 ? 'medium' : 'low'\n\n    this.addAlert({\n      type: 'pre_market_gap',\n      symbol: gapScan.symbol,\n      title: `Pre-Market Gap: ${gapScan.symbol}`,\n      message: `${gapScan.gapPercent.toFixed(1)}% gap with ${gapScan.catalyst ? 'catalyst' : 'no catalyst'}`,\n      priority,\n      data: { gapScan },\n      actionable: gapScan.meetsAllCriteria,\n      actions: gapScan.meetsAllCriteria ? [\n        {\n          id: 'analyze_setup',\n          label: 'Analyze Setup',\n          type: 'view_chart',\n          data: { symbol: gapScan.symbol }\n        }\n      ] : undefined\n    })\n  }\n\n  /**\n   * Create PMH break alert\n   */\n  createPMHBreakAlert(symbol: string, currentPrice: number, pmh: number): void {\n    this.addAlert({\n      type: 'pmh_break',\n      symbol,\n      title: `PMH Break: ${symbol}`,\n      message: `Price ${currentPrice.toFixed(2)} broke above PMH ${pmh.toFixed(2)}`,\n      priority: 'high',\n      data: { currentPrice, pmh },\n      actionable: true,\n      actions: [\n        {\n          id: 'execute_entry',\n          label: 'Execute Entry',\n          type: 'execute_trade',\n          data: { symbol, entryPrice: currentPrice }\n        }\n      ]\n    })\n  }\n\n  /**\n   * Create stop loss alert\n   */\n  createStopLossAlert(symbol: string, currentPrice: number, stopLoss: number): void {\n    this.addAlert({\n      type: 'stop_loss_hit',\n      symbol,\n      title: `Stop Loss Hit: ${symbol}`,\n      message: `Price ${currentPrice.toFixed(2)} hit stop loss ${stopLoss.toFixed(2)}`,\n      priority: 'critical',\n      data: { currentPrice, stopLoss },\n      actionable: true,\n      actions: [\n        {\n          id: 'exit_position',\n          label: 'Exit Position',\n          type: 'execute_trade',\n          data: { symbol, action: 'sell', price: currentPrice }\n        }\n      ]\n    })\n  }\n\n  /**\n   * Create profit target alert\n   */\n  createProfitTargetAlert(symbol: string, currentPrice: number, target: number, rLevel: number): void {\n    this.addAlert({\n      type: 'profit_target_hit',\n      symbol,\n      title: `Profit Target Hit: ${symbol}`,\n      message: `Price ${currentPrice.toFixed(2)} hit ${rLevel}R target ${target.toFixed(2)}`,\n      priority: 'high',\n      data: { currentPrice, target, rLevel },\n      actionable: true,\n      actions: [\n        {\n          id: 'take_profit',\n          label: 'Take Profit',\n          type: 'take_profit',\n          data: { symbol, price: currentPrice, rLevel }\n        }\n      ]\n    })\n  }\n\n  /**\n   * Create volume spike alert\n   */\n  createVolumeSpikeAlert(symbol: string, currentVolume: number, avgVolume: number): void {\n    const volumeRatio = currentVolume / avgVolume\n    \n    this.addAlert({\n      type: 'volume_spike',\n      symbol,\n      title: `Volume Spike: ${symbol}`,\n      message: `Volume ${volumeRatio.toFixed(1)}x above average`,\n      priority: volumeRatio > 5 ? 'high' : 'medium',\n      data: { currentVolume, avgVolume, volumeRatio },\n      actionable: false\n    })\n  }\n\n  /**\n   * Monitor Perfect-Pick setups for alerts\n   */\n  monitorPerfectPickSetups(setups: PerfectPickSetup[]): void {\n    setups.forEach(setup => {\n      // Check if this is a new setup (not already alerted)\n      const existingAlert = this.alerts.find(\n        a => a.type === 'perfect_pick_found' && \n             a.symbol === setup.symbol && \n             a.data?.setup?.createdAt === setup.createdAt\n      )\n\n      if (!existingAlert && setup.overallScore >= 80) {\n        this.createPerfectPickAlert(setup)\n      }\n    })\n  }\n\n  /**\n   * Monitor gap scans for alerts\n   */\n  monitorGapScans(gapScans: PreMarketGapScan[]): void {\n    gapScans.forEach(gapScan => {\n      // Check if this is a new gap (not already alerted)\n      const existingAlert = this.alerts.find(\n        a => a.type === 'pre_market_gap' && \n             a.symbol === gapScan.symbol &&\n             Math.abs(new Date(a.timestamp).getTime() - new Date(gapScan.scanTime).getTime()) < 60000 // Within 1 minute\n      )\n\n      if (!existingAlert && gapScan.gapPercent >= 3) {\n        this.createGapAlert(gapScan)\n      }\n    })\n  }\n\n  private generateAlertId(): string {\n    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n  }\n\n  private notifySubscribers(): void {\n    this.subscribers.forEach(callback => callback([...this.alerts]))\n  }\n\n  private playAlertSound(): void {\n    if (this.alertSound) {\n      this.alertSound.play().catch(error => {\n        console.log('Could not play alert sound:', error)\n      })\n    }\n  }\n\n  private showBrowserNotification(alert: Alert): void {\n    if ('Notification' in window && Notification.permission === 'granted') {\n      new Notification(alert.title, {\n        body: alert.message,\n        icon: '/favicon.ico',\n        tag: alert.symbol // Prevent duplicate notifications for same symbol\n      })\n    } else if ('Notification' in window && Notification.permission !== 'denied') {\n      Notification.requestPermission().then(permission => {\n        if (permission === 'granted') {\n          new Notification(alert.title, {\n            body: alert.message,\n            icon: '/favicon.ico',\n            tag: alert.symbol\n          })\n        }\n      })\n    }\n  }\n}\n\n// Create singleton instance\nexport const alertSystem = new AlertSystem()\n"], "names": [], "mappings": ";;;;;;;;AAiCO,MAAM;IAaX;;GAEC,GACD,UAAU,QAAmC,EAAc;QACzD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QAEtB,8BAA8B;QAC9B,OAAO;YACL,MAAM,QAAQ,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;YACvC,IAAI,QAAQ,CAAC,GAAG;gBACd,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO;YACjC;QACF;IACF;IAEA;;GAEC,GACD,SAAS,KAA+C,EAAQ;QAC9D,MAAM,WAAkB;YACtB,GAAG,KAAK;YACR,IAAI,IAAI,CAAC,eAAe;YACxB,WAAW,IAAI,OAAO,WAAW;YACjC,MAAM;QACR;QAEA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAU,4BAA4B;QAE1D,4BAA4B;QAC5B,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,KAAK;YAC5B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG;QACrC;QAEA,+CAA+C;QAC/C,IAAI,SAAS,QAAQ,KAAK,UAAU,SAAS,QAAQ,KAAK,YAAY;YACpE,IAAI,CAAC,cAAc;QACrB;QAEA,gDAAgD;QAChD,IAAI,SAAS,QAAQ,KAAK,YAAY;YACpC,IAAI,CAAC,uBAAuB,CAAC;QAC/B;QAEA,IAAI,CAAC,iBAAiB;IACxB;IAEA;;GAEC,GACD,WAAW,OAAe,EAAQ;QAChC,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC7C,IAAI,OAAO;YACT,MAAM,IAAI,GAAG;YACb,IAAI,CAAC,iBAAiB;QACxB;IACF;IAEA;;GAEC,GACD,gBAAsB;QACpB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA,QAAS,MAAM,IAAI,GAAG;QAC1C,IAAI,CAAC,iBAAiB;IACxB;IAEA;;GAEC,GACD,YAAY,OAAe,EAAQ;QACjC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC/C,IAAI,CAAC,iBAAiB;IACxB;IAEA;;GAEC,GACD,iBAAuB;QACrB,IAAI,CAAC,MAAM,GAAG,EAAE;QAChB,IAAI,CAAC,iBAAiB;IACxB;IAEA;;GAEC,GACD,YAAqB;QACnB,OAAO;eAAI,IAAI,CAAC,MAAM;SAAC;IACzB;IAEA;;GAEC,GACD,iBAAyB;QACvB,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,IAAI,EAAE,MAAM;IAChD;IAEA;;GAEC,GACD,oBAAoB,QAAkB,EAAQ;QAC5C,MAAM,WAAW,SAAS,IAAI,KAAK,WAAW,SAC9B,SAAS,IAAI,KAAK,WAAW,WAAW;QAExD,IAAI,CAAC,QAAQ,CAAC;YACZ,MAAM;YACN,QAAQ,SAAS,MAAM;YACvB,OAAO,AAAC,OAAiE,OAA3D,SAAS,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW,IAAG,eAA6B,OAAhB,SAAS,MAAM;YACxF,SAAS,SAAS,KAAK;YACvB;YACA,MAAM;gBAAE;YAAS;YACjB,YAAY;YACZ,SAAS;gBACP;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;wBAAE,QAAQ,SAAS,MAAM;oBAAC;gBAClC;aACD;QACH;IACF;IAEA;;GAEC,GACD,uBAAuB,KAAuB,EAAQ;QACpD,IAAI,CAAC,QAAQ,CAAC;YACZ,MAAM;YACN,QAAQ,MAAM,MAAM;YACpB,OAAO,AAAC,6BAAyC,OAAb,MAAM,MAAM;YAChD,SAAS,AAAC,SAAuC,OAA/B,MAAM,UAAU,EAAC,gBAAqD,OAAvC,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,MAAK;YACxF,UAAU,MAAM,UAAU,CAAC,UAAU,CAAC,OAAO,SAAS;YACtD,MAAM;gBAAE;YAAM;YACd,YAAY;YACZ,SAAS;gBACP;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;wBAAE;oBAAM;gBAChB;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;wBAAE,QAAQ,MAAM,MAAM;oBAAC;gBAC/B;aACD;QACH;IACF;IAEA;;GAEC,GACD,eAAe,OAAyB,EAAQ;QAC9C,MAAM,WAAW,QAAQ,UAAU,GAAG,KAAK,SAC3B,QAAQ,UAAU,GAAG,IAAI,WAAW;QAEpD,IAAI,CAAC,QAAQ,CAAC;YACZ,MAAM;YACN,QAAQ,QAAQ,MAAM;YACtB,OAAO,AAAC,mBAAiC,OAAf,QAAQ,MAAM;YACxC,SAAS,AAAC,GAA6C,OAA3C,QAAQ,UAAU,CAAC,OAAO,CAAC,IAAG,eAA2D,OAA9C,QAAQ,QAAQ,GAAG,aAAa;YACvF;YACA,MAAM;gBAAE;YAAQ;YAChB,YAAY,QAAQ,gBAAgB;YACpC,SAAS,QAAQ,gBAAgB,GAAG;gBAClC;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;wBAAE,QAAQ,QAAQ,MAAM;oBAAC;gBACjC;aACD,GAAG;QACN;IACF;IAEA;;GAEC,GACD,oBAAoB,MAAc,EAAE,YAAoB,EAAE,GAAW,EAAQ;QAC3E,IAAI,CAAC,QAAQ,CAAC;YACZ,MAAM;YACN;YACA,OAAO,AAAC,cAAoB,OAAP;YACrB,SAAS,AAAC,SAAmD,OAA3C,aAAa,OAAO,CAAC,IAAG,qBAAkC,OAAf,IAAI,OAAO,CAAC;YACzE,UAAU;YACV,MAAM;gBAAE;gBAAc;YAAI;YAC1B,YAAY;YACZ,SAAS;gBACP;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;wBAAE;wBAAQ,YAAY;oBAAa;gBAC3C;aACD;QACH;IACF;IAEA;;GAEC,GACD,oBAAoB,MAAc,EAAE,YAAoB,EAAE,QAAgB,EAAQ;QAChF,IAAI,CAAC,QAAQ,CAAC;YACZ,MAAM;YACN;YACA,OAAO,AAAC,kBAAwB,OAAP;YACzB,SAAS,AAAC,SAAiD,OAAzC,aAAa,OAAO,CAAC,IAAG,mBAAqC,OAApB,SAAS,OAAO,CAAC;YAC5E,UAAU;YACV,MAAM;gBAAE;gBAAc;YAAS;YAC/B,YAAY;YACZ,SAAS;gBACP;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;wBAAE;wBAAQ,QAAQ;wBAAQ,OAAO;oBAAa;gBACtD;aACD;QACH;IACF;IAEA;;GAEC,GACD,wBAAwB,MAAc,EAAE,YAAoB,EAAE,MAAc,EAAE,MAAc,EAAQ;QAClG,IAAI,CAAC,QAAQ,CAAC;YACZ,MAAM;YACN;YACA,OAAO,AAAC,sBAA4B,OAAP;YAC7B,SAAS,AAAC,SAAuC,OAA/B,aAAa,OAAO,CAAC,IAAG,SAAyB,OAAlB,QAAO,aAA6B,OAAlB,OAAO,OAAO,CAAC;YAClF,UAAU;YACV,MAAM;gBAAE;gBAAc;gBAAQ;YAAO;YACrC,YAAY;YACZ,SAAS;gBACP;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;wBAAE;wBAAQ,OAAO;wBAAc;oBAAO;gBAC9C;aACD;QACH;IACF;IAEA;;GAEC,GACD,uBAAuB,MAAc,EAAE,aAAqB,EAAE,SAAiB,EAAQ;QACrF,MAAM,cAAc,gBAAgB;QAEpC,IAAI,CAAC,QAAQ,CAAC;YACZ,MAAM;YACN;YACA,OAAO,AAAC,iBAAuB,OAAP;YACxB,SAAS,AAAC,UAAgC,OAAvB,YAAY,OAAO,CAAC,IAAG;YAC1C,UAAU,cAAc,IAAI,SAAS;YACrC,MAAM;gBAAE;gBAAe;gBAAW;YAAY;YAC9C,YAAY;QACd;IACF;IAEA;;GAEC,GACD,yBAAyB,MAA0B,EAAQ;QACzD,OAAO,OAAO,CAAC,CAAA;YACb,qDAAqD;YACrD,MAAM,gBAAgB,IAAI,CAAC,MAAM,CAAC,IAAI,CACpC,CAAA;oBAEK,eAAA;uBAFA,EAAE,IAAI,KAAK,wBACX,EAAE,MAAM,KAAK,MAAM,MAAM,IACzB,EAAA,UAAA,EAAE,IAAI,cAAN,+BAAA,gBAAA,QAAQ,KAAK,cAAb,oCAAA,cAAe,SAAS,MAAK,MAAM,SAAS;;YAGnD,IAAI,CAAC,iBAAiB,MAAM,YAAY,IAAI,IAAI;gBAC9C,IAAI,CAAC,sBAAsB,CAAC;YAC9B;QACF;IACF;IAEA;;GAEC,GACD,gBAAgB,QAA4B,EAAQ;QAClD,SAAS,OAAO,CAAC,CAAA;YACf,mDAAmD;YACnD,MAAM,gBAAgB,IAAI,CAAC,MAAM,CAAC,IAAI,CACpC,CAAA,IAAK,EAAE,IAAI,KAAK,oBACX,EAAE,MAAM,KAAK,QAAQ,MAAM,IAC3B,KAAK,GAAG,CAAC,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,QAAQ,QAAQ,EAAE,OAAO,MAAM,MAAM,kBAAkB;;YAGlH,IAAI,CAAC,iBAAiB,QAAQ,UAAU,IAAI,GAAG;gBAC7C,IAAI,CAAC,cAAc,CAAC;YACtB;QACF;IACF;IAEQ,kBAA0B;QAChC,OAAO,AAAC,SAAsB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;IACrE;IAEQ,oBAA0B;QAChC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA,WAAY,SAAS;mBAAI,IAAI,CAAC,MAAM;aAAC;IAChE;IAEQ,iBAAuB;QAC7B,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,KAAK,CAAC,CAAA;gBAC3B,QAAQ,GAAG,CAAC,+BAA+B;YAC7C;QACF;IACF;IAEQ,wBAAwB,KAAY,EAAQ;QAClD,IAAI,kBAAkB,UAAU,aAAa,UAAU,KAAK,WAAW;YACrE,IAAI,aAAa,MAAM,KAAK,EAAE;gBAC5B,MAAM,MAAM,OAAO;gBACnB,MAAM;gBACN,KAAK,MAAM,MAAM,CAAC,kDAAkD;YACtE;QACF,OAAO,IAAI,kBAAkB,UAAU,aAAa,UAAU,KAAK,UAAU;YAC3E,aAAa,iBAAiB,GAAG,IAAI,CAAC,CAAA;gBACpC,IAAI,eAAe,WAAW;oBAC5B,IAAI,aAAa,MAAM,KAAK,EAAE;wBAC5B,MAAM,MAAM,OAAO;wBACnB,MAAM;wBACN,KAAK,MAAM,MAAM;oBACnB;gBACF;YACF;QACF;IACF;IApVA,aAAc;QAJd,wMAAQ,UAAkB,EAAE;QAC5B,wMAAQ,eAA6C,EAAE;QACvD,wMAAQ,cAAsC;QAG5C,oCAAoC;QACpC,wCAAmC;YACjC,IAAI,CAAC,UAAU,GAAG,IAAI,MAAM,qBAAoB,wCAAwC;YACxF,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG;QAC3B;IACF;AA+UF;AAGO,MAAM,cAAc,IAAI", "debugId": null}}, {"offset": {"line": 5129, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/components/EventDrivenScanner.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'\nimport { \n  TrendingUp, \n  Zap, \n  Target, \n  AlertTriangle, \n  Clock, \n  DollarSign,\n  Activity,\n  Loader2,\n  RefreshCw,\n  Filter,\n  Star\n} from 'lucide-react'\nimport { PerfectPickSetup, PreMarketGapScan, Catalyst } from '@/types/trading'\nimport { PerfectPickSetupCard } from './PerfectPickSetupCard'\nimport { alertSystem } from '@/lib/alertSystem'\nimport { formatCurrency, formatPercentage } from '@/lib/utils'\n\ninterface EventDrivenScannerProps {\n  accountSize?: number\n  riskPercent?: number\n}\n\nexport function EventDrivenScanner({ \n  accountSize = 100000, \n  riskPercent = 2 \n}: EventDrivenScannerProps) {\n  const [perfectPickSetups, setPerfectPickSetups] = useState<PerfectPickSetup[]>([])\n  const [gapScanResults, setGapScanResults] = useState<PreMarketGapScan[]>([])\n  const [catalysts, setCatalysts] = useState<Catalyst[]>([])\n  const [isLoading, setIsLoading] = useState(false)\n  const [activeTab, setActiveTab] = useState('perfect-pick')\n  const [lastScanTime, setLastScanTime] = useState<string>('')\n  const [scanSummary, setScanSummary] = useState<any>(null)\n\n  // Auto-refresh every 15 minutes during market hours\n  useEffect(() => {\n    const interval = setInterval(() => {\n      const now = new Date()\n      const hour = now.getHours()\n      // Refresh during pre-market (4-9:30 AM EST) and market hours (9:30 AM - 4 PM EST)\n      if ((hour >= 4 && hour < 21)) {\n        handleRefreshScan()\n      }\n    }, 15 * 60 * 1000) // 15 minutes\n\n    return () => clearInterval(interval)\n  }, [])\n\n  const handlePerfectPickScan = async () => {\n    setIsLoading(true)\n    try {\n      const response = await fetch(`/api/scanner/perfect-pick?accountSize=${accountSize}&riskPercent=${riskPercent}&limit=20`)\n      const data = await response.json()\n      \n      if (data.success) {\n        setPerfectPickSetups(data.data.setups)\n        setScanSummary(data.data.summary)\n        setLastScanTime(new Date().toLocaleTimeString())\n\n        // Generate alerts for new Perfect-Pick setups\n        alertSystem.monitorPerfectPickSetups(data.data.setups)\n      }\n    } catch (error) {\n      console.error('Error running Perfect-Pick scan:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleGapScan = async () => {\n    setIsLoading(true)\n    try {\n      const response = await fetch('/api/scanner/gap-scan?minGap=3&maxGap=15&limit=30')\n      const data = await response.json()\n      \n      if (data.success) {\n        setGapScanResults(data.data.results)\n        setLastScanTime(new Date().toLocaleTimeString())\n\n        // Generate alerts for significant gaps\n        alertSystem.monitorGapScans(data.data.results)\n      }\n    } catch (error) {\n      console.error('Error running gap scan:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleCatalystScan = async () => {\n    setIsLoading(true)\n    try {\n      // Get catalysts for top gap stocks\n      const symbols = gapScanResults.slice(0, 10).map(result => result.symbol).join(',')\n      const response = await fetch(`/api/catalyst/detect?symbols=${symbols}&minQuality=6&limit=50`)\n      const data = await response.json()\n      \n      if (data.success) {\n        setCatalysts(data.data.catalysts)\n        setLastScanTime(new Date().toLocaleTimeString())\n\n        // Generate alerts for high-quality catalysts\n        data.data.catalysts\n          .filter((catalyst: Catalyst) => catalyst.qualityScore >= 7)\n          .forEach((catalyst: Catalyst) => alertSystem.createCatalystAlert(catalyst))\n      }\n    } catch (error) {\n      console.error('Error running catalyst scan:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleRefreshScan = () => {\n    switch (activeTab) {\n      case 'perfect-pick':\n        handlePerfectPickScan()\n        break\n      case 'gap-scan':\n        handleGapScan()\n        break\n      case 'catalysts':\n        handleCatalystScan()\n        break\n    }\n  }\n\n  const getCatalystBadgeColor = (catalyst: Catalyst) => {\n    if (catalyst.tier === 'tier_1') return 'bg-green-500'\n    if (catalyst.tier === 'tier_2') return 'bg-yellow-500'\n    return 'bg-gray-500'\n  }\n\n  const getCatalystImpactIcon = (impact: string) => {\n    switch (impact) {\n      case 'bullish': return <TrendingUp className=\"h-4 w-4 text-green-500\" />\n      case 'bearish': return <AlertTriangle className=\"h-4 w-4 text-red-500\" />\n      default: return <Activity className=\"h-4 w-4 text-gray-500\" />\n    }\n  }\n\n  const getSetupGradeColor = (grade: string) => {\n    if (grade.startsWith('A')) return 'text-green-600 bg-green-50'\n    if (grade.startsWith('B')) return 'text-blue-600 bg-blue-50'\n    if (grade.startsWith('C')) return 'text-yellow-600 bg-yellow-50'\n    return 'text-red-600 bg-red-50'\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h2 className=\"text-2xl font-bold\">Event-Driven Scanner</h2>\n          <p className=\"text-muted-foreground\">\n            Perfect-Pick Trading System with Catalyst Detection\n          </p>\n        </div>\n        <div className=\"flex items-center gap-2\">\n          {lastScanTime && (\n            <span className=\"text-sm text-muted-foreground\">\n              Last scan: {lastScanTime}\n            </span>\n          )}\n          <Button \n            onClick={handleRefreshScan} \n            disabled={isLoading}\n            variant=\"outline\"\n            size=\"sm\"\n          >\n            {isLoading ? (\n              <Loader2 className=\"h-4 w-4 animate-spin\" />\n            ) : (\n              <RefreshCw className=\"h-4 w-4\" />\n            )}\n            Refresh\n          </Button>\n        </div>\n      </div>\n\n      {/* Scan Summary */}\n      {scanSummary && (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Activity className=\"h-5 w-5\" />\n              Scan Summary\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-blue-600\">{scanSummary.totalSetups || 0}</div>\n                <div className=\"text-sm text-muted-foreground\">Total Setups</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-green-600\">{scanSummary.avgScore || 0}</div>\n                <div className=\"text-sm text-muted-foreground\">Avg Score</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-purple-600\">{formatPercentage(scanSummary.avgGap || 0)}</div>\n                <div className=\"text-sm text-muted-foreground\">Avg Gap</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-orange-600\">\n                  {Object.keys(scanSummary.catalystBreakdown || {}).length}\n                </div>\n                <div className=\"text-sm text-muted-foreground\">Catalyst Types</div>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Main Scanner Tabs */}\n      <Tabs value={activeTab} onValueChange={setActiveTab}>\n        <TabsList className=\"grid w-full grid-cols-3\">\n          <TabsTrigger value=\"perfect-pick\" className=\"flex items-center gap-2\">\n            <Star className=\"h-4 w-4\" />\n            Perfect-Pick\n          </TabsTrigger>\n          <TabsTrigger value=\"gap-scan\" className=\"flex items-center gap-2\">\n            <TrendingUp className=\"h-4 w-4\" />\n            Gap Scanner\n          </TabsTrigger>\n          <TabsTrigger value=\"catalysts\" className=\"flex items-center gap-2\">\n            <Zap className=\"h-4 w-4\" />\n            Catalysts\n          </TabsTrigger>\n        </TabsList>\n\n        {/* Perfect-Pick Tab */}\n        <TabsContent value=\"perfect-pick\" className=\"space-y-4\">\n          <div className=\"flex items-center justify-between\">\n            <h3 className=\"text-lg font-semibold\">Perfect-Pick Setups</h3>\n            <Button onClick={handlePerfectPickScan} disabled={isLoading}>\n              {isLoading ? (\n                <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\n              ) : (\n                <Target className=\"h-4 w-4 mr-2\" />\n              )}\n              Scan Perfect-Picks\n            </Button>\n          </div>\n\n          <div className=\"space-y-6\">\n            {perfectPickSetups.map((setup) => (\n              <PerfectPickSetupCard\n                key={setup.symbol}\n                setup={setup}\n                onExecuteTrade={(setup) => {\n                  console.log('Execute trade for:', setup.symbol)\n                  // TODO: Implement trade execution\n                }}\n                onViewChart={(symbol) => {\n                  console.log('View chart for:', symbol)\n                  // TODO: Implement chart viewing\n                }}\n                onGenerateEntryTrigger={async (symbol, preMarketHigh) => {\n                  console.log('Generate entry trigger for:', symbol)\n                  try {\n                    const response = await fetch('/api/scanner/perfect-pick', {\n                      method: 'POST',\n                      headers: { 'Content-Type': 'application/json' },\n                      body: JSON.stringify({\n                        action: 'generate_entry_trigger',\n                        data: { symbol, preMarketHigh }\n                      })\n                    })\n                    const data = await response.json()\n                    if (data.success) {\n                      // Update the setup with the new entry trigger\n                      console.log('Entry trigger generated:', data.data.entryTrigger)\n                    }\n                  } catch (error) {\n                    console.error('Error generating entry trigger:', error)\n                  }\n                }}\n              />\n            ))}\n          </div>\n\n          {perfectPickSetups.length === 0 && !isLoading && (\n            <Card>\n              <CardContent className=\"text-center py-8\">\n                <Target className=\"h-12 w-12 mx-auto text-muted-foreground mb-4\" />\n                <p className=\"text-muted-foreground\">\n                  No Perfect-Pick setups found. Click \"Scan Perfect-Picks\" to search for opportunities.\n                </p>\n              </CardContent>\n            </Card>\n          )}\n        </TabsContent>\n\n        {/* Gap Scanner Tab */}\n        <TabsContent value=\"gap-scan\" className=\"space-y-4\">\n          <div className=\"flex items-center justify-between\">\n            <h3 className=\"text-lg font-semibold\">Pre-Market Gap Scanner</h3>\n            <Button onClick={handleGapScan} disabled={isLoading}>\n              {isLoading ? (\n                <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\n              ) : (\n                <TrendingUp className=\"h-4 w-4 mr-2\" />\n              )}\n              Scan Gaps\n            </Button>\n          </div>\n\n          <div className=\"grid gap-3\">\n            {gapScanResults.map((result) => (\n              <Card key={result.symbol} className=\"hover:shadow-md transition-shadow\">\n                <CardContent className=\"p-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center gap-3\">\n                      <div>\n                        <div className=\"font-semibold\">{result.symbol}</div>\n                        <div className=\"text-sm text-muted-foreground\">{result.name}</div>\n                      </div>\n                      {result.catalyst && (\n                        <Badge className={getCatalystBadgeColor(result.catalyst)}>\n                          {result.catalyst.type.replace(/_/g, ' ')}\n                        </Badge>\n                      )}\n                      {result.meetsAllCriteria && (\n                        <Badge className=\"bg-green-500\">Perfect-Pick</Badge>\n                      )}\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"font-bold\">{formatCurrency(result.price)}</div>\n                      <div className=\"text-sm text-green-600\">\n                        +{formatPercentage(result.gapPercent)}\n                      </div>\n                    </div>\n                  </div>\n                  \n                  <div className=\"mt-3 grid grid-cols-3 gap-4 text-xs text-muted-foreground\">\n                    <div>Vol: {(result.preMarketVolume / 1000).toFixed(0)}K</div>\n                    <div>MCap: ${(result.marketCap / 1e9).toFixed(1)}B</div>\n                    <div>Sector: {result.sector}</div>\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n\n          {gapScanResults.length === 0 && !isLoading && (\n            <Card>\n              <CardContent className=\"text-center py-8\">\n                <TrendingUp className=\"h-12 w-12 mx-auto text-muted-foreground mb-4\" />\n                <p className=\"text-muted-foreground\">\n                  No gap opportunities found. Click \"Scan Gaps\" to search for pre-market movers.\n                </p>\n              </CardContent>\n            </Card>\n          )}\n        </TabsContent>\n\n        {/* Catalysts Tab */}\n        <TabsContent value=\"catalysts\" className=\"space-y-4\">\n          <div className=\"flex items-center justify-between\">\n            <h3 className=\"text-lg font-semibold\">Real-Time Catalysts</h3>\n            <Button onClick={handleCatalystScan} disabled={isLoading}>\n              {isLoading ? (\n                <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\n              ) : (\n                <Zap className=\"h-4 w-4 mr-2\" />\n              )}\n              Scan Catalysts\n            </Button>\n          </div>\n\n          <div className=\"grid gap-3\">\n            {catalysts.map((catalyst) => (\n              <Card key={catalyst.id} className=\"hover:shadow-md transition-shadow\">\n                <CardContent className=\"p-4\">\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex-1\">\n                      <div className=\"flex items-center gap-2 mb-2\">\n                        <span className=\"font-semibold\">{catalyst.symbol}</span>\n                        {getCatalystImpactIcon(catalyst.impact)}\n                        <Badge className={getCatalystBadgeColor(catalyst)}>\n                          {catalyst.tier.replace('_', ' ').toUpperCase()}\n                        </Badge>\n                        <Badge variant=\"outline\" className=\"text-xs\">\n                          {catalyst.freshness}\n                        </Badge>\n                      </div>\n                      <h4 className=\"font-medium mb-1\">{catalyst.title}</h4>\n                      <p className=\"text-sm text-muted-foreground mb-2\">\n                        {catalyst.description}\n                      </p>\n                      <div className=\"flex items-center gap-4 text-xs text-muted-foreground\">\n                        <span>Quality: {catalyst.qualityScore}/10</span>\n                        <span>Source: {catalyst.source}</span>\n                        <span className=\"flex items-center gap-1\">\n                          <Clock className=\"h-3 w-3\" />\n                          {new Date(catalyst.announcementTime).toLocaleString()}\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n\n          {catalysts.length === 0 && !isLoading && (\n            <Card>\n              <CardContent className=\"text-center py-8\">\n                <Zap className=\"h-12 w-12 mx-auto text-muted-foreground mb-4\" />\n                <p className=\"text-muted-foreground\">\n                  No catalysts found. Click \"Scan Catalysts\" to detect market-moving events.\n                </p>\n              </CardContent>\n            </Card>\n          )}\n        </TabsContent>\n      </Tabs>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;AACA;;;AAvBA;;;;;;;;;;AA8BO,SAAS,mBAAmB,KAGT;QAHS,EACjC,cAAc,MAAM,EACpB,cAAc,CAAC,EACS,GAHS;;IAIjC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,IAAA,kMAAQ,EAAqB,EAAE;IACjF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,kMAAQ,EAAqB,EAAE;IAC3E,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,kMAAQ,EAAa,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,kMAAQ,EAAC;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,kMAAQ,EAAC;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,kMAAQ,EAAS;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,kMAAQ,EAAM;IAEpD,oDAAoD;IACpD,IAAA,mMAAS;wCAAC;YACR,MAAM,WAAW;yDAAY;oBAC3B,MAAM,MAAM,IAAI;oBAChB,MAAM,OAAO,IAAI,QAAQ;oBACzB,kFAAkF;oBAClF,IAAK,QAAQ,KAAK,OAAO,IAAK;wBAC5B;oBACF;gBACF;wDAAG,KAAK,KAAK,MAAM,aAAa;;YAEhC;gDAAO,IAAM,cAAc;;QAC7B;uCAAG,EAAE;IAEL,MAAM,wBAAwB;QAC5B,aAAa;QACb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,yCAAmE,OAA3B,aAAY,iBAA2B,OAAZ,aAAY;YAC7G,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,qBAAqB,KAAK,IAAI,CAAC,MAAM;gBACrC,eAAe,KAAK,IAAI,CAAC,OAAO;gBAChC,gBAAgB,IAAI,OAAO,kBAAkB;gBAE7C,8CAA8C;gBAC9C,oKAAW,CAAC,wBAAwB,CAAC,KAAK,IAAI,CAAC,MAAM;YACvD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB;QACpB,aAAa;QACb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,kBAAkB,KAAK,IAAI,CAAC,OAAO;gBACnC,gBAAgB,IAAI,OAAO,kBAAkB;gBAE7C,uCAAuC;gBACvC,oKAAW,CAAC,eAAe,CAAC,KAAK,IAAI,CAAC,OAAO;YAC/C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB;QACzB,aAAa;QACb,IAAI;YACF,mCAAmC;YACnC,MAAM,UAAU,eAAe,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAA,SAAU,OAAO,MAAM,EAAE,IAAI,CAAC;YAC9E,MAAM,WAAW,MAAM,MAAM,AAAC,gCAAuC,OAAR,SAAQ;YACrE,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,aAAa,KAAK,IAAI,CAAC,SAAS;gBAChC,gBAAgB,IAAI,OAAO,kBAAkB;gBAE7C,6CAA6C;gBAC7C,KAAK,IAAI,CAAC,SAAS,CAChB,MAAM,CAAC,CAAC,WAAuB,SAAS,YAAY,IAAI,GACxD,OAAO,CAAC,CAAC,WAAuB,oKAAW,CAAC,mBAAmB,CAAC;YACrE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH;gBACA;YACF,KAAK;gBACH;gBACA;YACF,KAAK;gBACH;gBACA;QACJ;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,IAAI,SAAS,IAAI,KAAK,UAAU,OAAO;QACvC,IAAI,SAAS,IAAI,KAAK,UAAU,OAAO;QACvC,OAAO;IACT;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBAAW,qBAAO,sNAAC,4PAAU;oBAAC,WAAU;;;;;;YAC7C,KAAK;gBAAW,qBAAO,sNAAC,qQAAa;oBAAC,WAAU;;;;;;YAChD;gBAAS,qBAAO,sNAAC,kPAAQ;oBAAC,WAAU;;;;;;QACtC;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,MAAM,UAAU,CAAC,MAAM,OAAO;QAClC,IAAI,MAAM,UAAU,CAAC,MAAM,OAAO;QAClC,IAAI,MAAM,UAAU,CAAC,MAAM,OAAO;QAClC,OAAO;IACT;IAEA,qBACE,sNAAC;QAAI,WAAU;;0BAEb,sNAAC;gBAAI,WAAU;;kCACb,sNAAC;;0CACC,sNAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,sNAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,sNAAC;wBAAI,WAAU;;4BACZ,8BACC,sNAAC;gCAAK,WAAU;;oCAAgC;oCAClC;;;;;;;0CAGhB,sNAAC,wKAAM;gCACL,SAAS;gCACT,UAAU;gCACV,SAAQ;gCACR,MAAK;;oCAEJ,0BACC,sNAAC,wPAAO;wCAAC,WAAU;;;;;6DAEnB,sNAAC,yPAAS;wCAAC,WAAU;;;;;;oCACrB;;;;;;;;;;;;;;;;;;;YAOP,6BACC,sNAAC,oKAAI;;kCACH,sNAAC,0KAAU;kCACT,cAAA,sNAAC,yKAAS;4BAAC,WAAU;;8CACnB,sNAAC,kPAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIpC,sNAAC,2KAAW;kCACV,cAAA,sNAAC;4BAAI,WAAU;;8CACb,sNAAC;oCAAI,WAAU;;sDACb,sNAAC;4CAAI,WAAU;sDAAoC,YAAY,WAAW,IAAI;;;;;;sDAC9E,sNAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;8CAEjD,sNAAC;oCAAI,WAAU;;sDACb,sNAAC;4CAAI,WAAU;sDAAqC,YAAY,QAAQ,IAAI;;;;;;sDAC5E,sNAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;8CAEjD,sNAAC;oCAAI,WAAU;;sDACb,sNAAC;4CAAI,WAAU;sDAAsC,IAAA,mKAAgB,EAAC,YAAY,MAAM,IAAI;;;;;;sDAC5F,sNAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;8CAEjD,sNAAC;oCAAI,WAAU;;sDACb,sNAAC;4CAAI,WAAU;sDACZ,OAAO,IAAI,CAAC,YAAY,iBAAiB,IAAI,CAAC,GAAG,MAAM;;;;;;sDAE1D,sNAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQzD,sNAAC,oKAAI;gBAAC,OAAO;gBAAW,eAAe;;kCACrC,sNAAC,wKAAQ;wBAAC,WAAU;;0CAClB,sNAAC,2KAAW;gCAAC,OAAM;gCAAe,WAAU;;kDAC1C,sNAAC,sOAAI;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAG9B,sNAAC,2KAAW;gCAAC,OAAM;gCAAW,WAAU;;kDACtC,sNAAC,4PAAU;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGpC,sNAAC,2KAAW;gCAAC,OAAM;gCAAY,WAAU;;kDACvC,sNAAC,mOAAG;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;kCAM/B,sNAAC,2KAAW;wBAAC,OAAM;wBAAe,WAAU;;0CAC1C,sNAAC;gCAAI,WAAU;;kDACb,sNAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,sNAAC,wKAAM;wCAAC,SAAS;wCAAuB,UAAU;;4CAC/C,0BACC,sNAAC,wPAAO;gDAAC,WAAU;;;;;qEAEnB,sNAAC,4OAAM;gDAAC,WAAU;;;;;;4CAClB;;;;;;;;;;;;;0CAKN,sNAAC;gCAAI,WAAU;0CACZ,kBAAkB,GAAG,CAAC,CAAC,sBACtB,sNAAC,8LAAoB;wCAEnB,OAAO;wCACP,gBAAgB,CAAC;4CACf,QAAQ,GAAG,CAAC,sBAAsB,MAAM,MAAM;wCAC9C,kCAAkC;wCACpC;wCACA,aAAa,CAAC;4CACZ,QAAQ,GAAG,CAAC,mBAAmB;wCAC/B,gCAAgC;wCAClC;wCACA,wBAAwB,OAAO,QAAQ;4CACrC,QAAQ,GAAG,CAAC,+BAA+B;4CAC3C,IAAI;gDACF,MAAM,WAAW,MAAM,MAAM,6BAA6B;oDACxD,QAAQ;oDACR,SAAS;wDAAE,gBAAgB;oDAAmB;oDAC9C,MAAM,KAAK,SAAS,CAAC;wDACnB,QAAQ;wDACR,MAAM;4DAAE;4DAAQ;wDAAc;oDAChC;gDACF;gDACA,MAAM,OAAO,MAAM,SAAS,IAAI;gDAChC,IAAI,KAAK,OAAO,EAAE;oDAChB,8CAA8C;oDAC9C,QAAQ,GAAG,CAAC,4BAA4B,KAAK,IAAI,CAAC,YAAY;gDAChE;4CACF,EAAE,OAAO,OAAO;gDACd,QAAQ,KAAK,CAAC,mCAAmC;4CACnD;wCACF;uCA7BK,MAAM,MAAM;;;;;;;;;;4BAkCtB,kBAAkB,MAAM,KAAK,KAAK,CAAC,2BAClC,sNAAC,oKAAI;0CACH,cAAA,sNAAC,2KAAW;oCAAC,WAAU;;sDACrB,sNAAC,4OAAM;4CAAC,WAAU;;;;;;sDAClB,sNAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;kCAS7C,sNAAC,2KAAW;wBAAC,OAAM;wBAAW,WAAU;;0CACtC,sNAAC;gCAAI,WAAU;;kDACb,sNAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,sNAAC,wKAAM;wCAAC,SAAS;wCAAe,UAAU;;4CACvC,0BACC,sNAAC,wPAAO;gDAAC,WAAU;;;;;qEAEnB,sNAAC,4PAAU;gDAAC,WAAU;;;;;;4CACtB;;;;;;;;;;;;;0CAKN,sNAAC;gCAAI,WAAU;0CACZ,eAAe,GAAG,CAAC,CAAC,uBACnB,sNAAC,oKAAI;wCAAqB,WAAU;kDAClC,cAAA,sNAAC,2KAAW;4CAAC,WAAU;;8DACrB,sNAAC;oDAAI,WAAU;;sEACb,sNAAC;4DAAI,WAAU;;8EACb,sNAAC;;sFACC,sNAAC;4EAAI,WAAU;sFAAiB,OAAO,MAAM;;;;;;sFAC7C,sNAAC;4EAAI,WAAU;sFAAiC,OAAO,IAAI;;;;;;;;;;;;gEAE5D,OAAO,QAAQ,kBACd,sNAAC,sKAAK;oEAAC,WAAW,sBAAsB,OAAO,QAAQ;8EACpD,OAAO,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM;;;;;;gEAGvC,OAAO,gBAAgB,kBACtB,sNAAC,sKAAK;oEAAC,WAAU;8EAAe;;;;;;;;;;;;sEAGpC,sNAAC;4DAAI,WAAU;;8EACb,sNAAC;oEAAI,WAAU;8EAAa,IAAA,iKAAc,EAAC,OAAO,KAAK;;;;;;8EACvD,sNAAC;oEAAI,WAAU;;wEAAyB;wEACpC,IAAA,mKAAgB,EAAC,OAAO,UAAU;;;;;;;;;;;;;;;;;;;8DAK1C,sNAAC;oDAAI,WAAU;;sEACb,sNAAC;;gEAAI;gEAAM,CAAC,OAAO,eAAe,GAAG,IAAI,EAAE,OAAO,CAAC;gEAAG;;;;;;;sEACtD,sNAAC;;gEAAI;gEAAQ,CAAC,OAAO,SAAS,GAAG,GAAG,EAAE,OAAO,CAAC;gEAAG;;;;;;;sEACjD,sNAAC;;gEAAI;gEAAS,OAAO,MAAM;;;;;;;;;;;;;;;;;;;uCA5BtB,OAAO,MAAM;;;;;;;;;;4BAmC3B,eAAe,MAAM,KAAK,KAAK,CAAC,2BAC/B,sNAAC,oKAAI;0CACH,cAAA,sNAAC,2KAAW;oCAAC,WAAU;;sDACrB,sNAAC,4PAAU;4CAAC,WAAU;;;;;;sDACtB,sNAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;kCAS7C,sNAAC,2KAAW;wBAAC,OAAM;wBAAY,WAAU;;0CACvC,sNAAC;gCAAI,WAAU;;kDACb,sNAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,sNAAC,wKAAM;wCAAC,SAAS;wCAAoB,UAAU;;4CAC5C,0BACC,sNAAC,wPAAO;gDAAC,WAAU;;;;;qEAEnB,sNAAC,mOAAG;gDAAC,WAAU;;;;;;4CACf;;;;;;;;;;;;;0CAKN,sNAAC;gCAAI,WAAU;0CACZ,UAAU,GAAG,CAAC,CAAC,yBACd,sNAAC,oKAAI;wCAAmB,WAAU;kDAChC,cAAA,sNAAC,2KAAW;4CAAC,WAAU;sDACrB,cAAA,sNAAC;gDAAI,WAAU;0DACb,cAAA,sNAAC;oDAAI,WAAU;;sEACb,sNAAC;4DAAI,WAAU;;8EACb,sNAAC;oEAAK,WAAU;8EAAiB,SAAS,MAAM;;;;;;gEAC/C,sBAAsB,SAAS,MAAM;8EACtC,sNAAC,sKAAK;oEAAC,WAAW,sBAAsB;8EACrC,SAAS,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;8EAE9C,sNAAC,sKAAK;oEAAC,SAAQ;oEAAU,WAAU;8EAChC,SAAS,SAAS;;;;;;;;;;;;sEAGvB,sNAAC;4DAAG,WAAU;sEAAoB,SAAS,KAAK;;;;;;sEAChD,sNAAC;4DAAE,WAAU;sEACV,SAAS,WAAW;;;;;;sEAEvB,sNAAC;4DAAI,WAAU;;8EACb,sNAAC;;wEAAK;wEAAU,SAAS,YAAY;wEAAC;;;;;;;8EACtC,sNAAC;;wEAAK;wEAAS,SAAS,MAAM;;;;;;;8EAC9B,sNAAC;oEAAK,WAAU;;sFACd,sNAAC,yOAAK;4EAAC,WAAU;;;;;;wEAChB,IAAI,KAAK,SAAS,gBAAgB,EAAE,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCAvBpD,SAAS,EAAE;;;;;;;;;;4BAiCzB,UAAU,MAAM,KAAK,KAAK,CAAC,2BAC1B,sNAAC,oKAAI;0CACH,cAAA,sNAAC,2KAAW;oCAAC,WAAU;;sDACrB,sNAAC,mOAAG;4CAAC,WAAU;;;;;;sDACf,sNAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD;GA9YgB;KAAA", "debugId": null}}, {"offset": {"line": 6192, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/components/AlertCenter.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  Bell,\n  BellRing,\n  X,\n  Eye,\n  Play,\n  TrendingUp,\n  Zap,\n  Target,\n  AlertTriangle,\n  DollarSign,\n  Activity,\n  Clock,\n  CheckCircle\n} from 'lucide-react'\nimport { Alert, AlertType, alertSystem } from '@/lib/alertSystem'\nimport { formatCurrency, formatPercentage } from '@/lib/utils'\n\ninterface AlertCenterProps {\n  className?: string\n}\n\nexport function AlertCenter({ className }: AlertCenterProps) {\n  const [alerts, setAlerts] = useState<Alert[]>([])\n  const [isOpen, setIsOpen] = useState(false)\n  const [unreadCount, setUnreadCount] = useState(0)\n\n  useEffect(() => {\n    // Subscribe to alert updates\n    const unsubscribe = alertSystem.subscribe((updatedAlerts) => {\n      setAlerts(updatedAlerts)\n      setUnreadCount(alertSystem.getUnreadCount())\n    })\n\n    // Initial load\n    setAlerts(alertSystem.getAlerts())\n    setUnreadCount(alertSystem.getUnreadCount())\n\n    return unsubscribe\n  }, [])\n\n  const getAlertIcon = (type: AlertType) => {\n    switch (type) {\n      case 'new_catalyst': return <Zap className=\"h-4 w-4\" />\n      case 'perfect_pick_found': return <Target className=\"h-4 w-4\" />\n      case 'pre_market_gap': return <TrendingUp className=\"h-4 w-4\" />\n      case 'pmh_break': return <Activity className=\"h-4 w-4\" />\n      case 'stop_loss_hit': return <AlertTriangle className=\"h-4 w-4\" />\n      case 'profit_target_hit': return <DollarSign className=\"h-4 w-4\" />\n      case 'entry_trigger': return <Play className=\"h-4 w-4\" />\n      case 'volume_spike': return <Activity className=\"h-4 w-4\" />\n      default: return <Bell className=\"h-4 w-4\" />\n    }\n  }\n\n  const getAlertColor = (priority: string) => {\n    switch (priority) {\n      case 'critical': return 'text-red-600 bg-red-50 border-red-200'\n      case 'high': return 'text-orange-600 bg-orange-50 border-orange-200'\n      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200'\n      case 'low': return 'text-blue-600 bg-blue-50 border-blue-200'\n      default: return 'text-gray-600 bg-gray-50 border-gray-200'\n    }\n  }\n\n  const handleAlertAction = async (alert: Alert, actionId: string) => {\n    const action = alert.actions?.find(a => a.id === actionId)\n    if (!action) return\n\n    switch (action.type) {\n      case 'execute_trade':\n        console.log('Execute trade:', action.data)\n        // TODO: Implement trade execution\n        break\n      case 'view_chart':\n        console.log('View chart:', action.data)\n        // TODO: Implement chart viewing\n        break\n      case 'take_profit':\n        console.log('Take profit:', action.data)\n        // TODO: Implement profit taking\n        break\n      case 'update_stop':\n        console.log('Update stop:', action.data)\n        // TODO: Implement stop loss update\n        break\n      case 'dismiss':\n        alertSystem.removeAlert(alert.id)\n        break\n    }\n\n    // Mark alert as read after action\n    alertSystem.markAsRead(alert.id)\n  }\n\n  const formatTimeAgo = (timestamp: string) => {\n    const now = new Date()\n    const alertTime = new Date(timestamp)\n    const diffMs = now.getTime() - alertTime.getTime()\n    const diffMins = Math.floor(diffMs / 60000)\n    const diffHours = Math.floor(diffMins / 60)\n\n    if (diffMins < 1) return 'Just now'\n    if (diffMins < 60) return `${diffMins}m ago`\n    if (diffHours < 24) return `${diffHours}h ago`\n    return alertTime.toLocaleDateString()\n  }\n\n  return (\n    <div className={`relative ${className}`}>\n      {/* Alert Bell Button */}\n      <Button\n        variant=\"outline\"\n        size=\"sm\"\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"relative\"\n      >\n        {unreadCount > 0 ? (\n          <BellRing className=\"h-4 w-4\" />\n        ) : (\n          <Bell className=\"h-4 w-4\" />\n        )}\n        {unreadCount > 0 && (\n          <Badge className=\"absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs bg-red-500\">\n            {unreadCount > 99 ? '99+' : unreadCount}\n          </Badge>\n        )}\n      </Button>\n\n      {/* Alert Dropdown */}\n      {isOpen && (\n        <div className=\"absolute right-0 top-full mt-2 w-96 max-h-96 overflow-y-auto bg-white border rounded-lg shadow-lg z-50\">\n          <div className=\"p-4 border-b\">\n            <div className=\"flex items-center justify-between\">\n              <h3 className=\"font-semibold\">Alerts</h3>\n              <div className=\"flex items-center gap-2\">\n                {unreadCount > 0 && (\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={() => alertSystem.markAllAsRead()}\n                  >\n                    Mark all read\n                  </Button>\n                )}\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={() => setIsOpen(false)}\n                >\n                  <X className=\"h-4 w-4\" />\n                </Button>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"max-h-80 overflow-y-auto\">\n            {alerts.length === 0 ? (\n              <div className=\"p-8 text-center text-muted-foreground\">\n                <Bell className=\"h-8 w-8 mx-auto mb-2 opacity-50\" />\n                <p>No alerts yet</p>\n              </div>\n            ) : (\n              alerts.map((alert) => (\n                <div\n                  key={alert.id}\n                  className={`p-4 border-b hover:bg-gray-50 ${\n                    !alert.read ? 'bg-blue-50/50' : ''\n                  }`}\n                  onClick={() => !alert.read && alertSystem.markAsRead(alert.id)}\n                >\n                  <div className=\"flex items-start gap-3\">\n                    <div className={`p-2 rounded-full ${getAlertColor(alert.priority)}`}>\n                      {getAlertIcon(alert.type)}\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"flex items-center gap-2 mb-1\">\n                        <h4 className=\"font-medium text-sm truncate\">\n                          {alert.title}\n                        </h4>\n                        {!alert.read && (\n                          <div className=\"w-2 h-2 bg-blue-500 rounded-full flex-shrink-0\" />\n                        )}\n                      </div>\n                      <p className=\"text-sm text-muted-foreground mb-2\">\n                        {alert.message}\n                      </p>\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center gap-2 text-xs text-muted-foreground\">\n                          <Clock className=\"h-3 w-3\" />\n                          {formatTimeAgo(alert.timestamp)}\n                          <Badge variant=\"outline\" className=\"text-xs\">\n                            {alert.priority}\n                          </Badge>\n                        </div>\n                      </div>\n                      \n                      {/* Alert Actions */}\n                      {alert.actionable && alert.actions && alert.actions.length > 0 && (\n                        <div className=\"flex items-center gap-2 mt-2\">\n                          {alert.actions.map((action) => (\n                            <Button\n                              key={action.id}\n                              variant=\"outline\"\n                              size=\"sm\"\n                              onClick={(e) => {\n                                e.stopPropagation()\n                                handleAlertAction(alert, action.id)\n                              }}\n                              className=\"text-xs\"\n                            >\n                              {action.type === 'execute_trade' && <Play className=\"h-3 w-3 mr-1\" />}\n                              {action.type === 'view_chart' && <Eye className=\"h-3 w-3 mr-1\" />}\n                              {action.type === 'take_profit' && <DollarSign className=\"h-3 w-3 mr-1\" />}\n                              {action.label}\n                            </Button>\n                          ))}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ))\n            )}\n          </div>\n\n          {alerts.length > 0 && (\n            <div className=\"p-4 border-t\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => {\n                  alertSystem.clearAllAlerts()\n                  setIsOpen(false)\n                }}\n                className=\"w-full text-sm\"\n              >\n                Clear all alerts\n              </Button>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  )\n}\n\n// Alert Toast Component for critical alerts\nexport function AlertToast({ alert, onDismiss }: { alert: Alert; onDismiss: () => void }) {\n  useEffect(() => {\n    // Auto-dismiss after 10 seconds for non-critical alerts\n    if (alert.priority !== 'critical') {\n      const timer = setTimeout(onDismiss, 10000)\n      return () => clearTimeout(timer)\n    }\n  }, [alert.priority, onDismiss])\n\n  return (\n    <Card className={`fixed top-4 right-4 w-80 z-50 shadow-lg border-l-4 ${\n      alert.priority === 'critical' ? 'border-l-red-500' : \n      alert.priority === 'high' ? 'border-l-orange-500' : \n      'border-l-blue-500'\n    }`}>\n      <CardHeader className=\"pb-2\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-2\">\n            <div className={`p-1 rounded-full ${getAlertColor(alert.priority)}`}>\n              {getAlertIcon(alert.type)}\n            </div>\n            <CardTitle className=\"text-sm\">{alert.title}</CardTitle>\n          </div>\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={onDismiss}\n            className=\"h-6 w-6 p-0\"\n          >\n            <X className=\"h-3 w-3\" />\n          </Button>\n        </div>\n      </CardHeader>\n      <CardContent className=\"pt-0\">\n        <p className=\"text-sm text-muted-foreground mb-3\">\n          {alert.message}\n        </p>\n        {alert.actionable && alert.actions && (\n          <div className=\"flex gap-2\">\n            {alert.actions.slice(0, 2).map((action) => (\n              <Button\n                key={action.id}\n                variant={action.type === 'execute_trade' ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => {\n                  // Handle action\n                  console.log('Toast action:', action)\n                  onDismiss()\n                }}\n                className=\"text-xs\"\n              >\n                {action.label}\n              </Button>\n            ))}\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n\n  function getAlertColor(priority: string) {\n    switch (priority) {\n      case 'critical': return 'text-red-600 bg-red-50'\n      case 'high': return 'text-orange-600 bg-orange-50'\n      case 'medium': return 'text-yellow-600 bg-yellow-50'\n      case 'low': return 'text-blue-600 bg-blue-50'\n      default: return 'text-gray-600 bg-gray-50'\n    }\n  }\n\n  function getAlertIcon(type: AlertType) {\n    switch (type) {\n      case 'new_catalyst': return <Zap className=\"h-4 w-4\" />\n      case 'perfect_pick_found': return <Target className=\"h-4 w-4\" />\n      case 'pre_market_gap': return <TrendingUp className=\"h-4 w-4\" />\n      case 'pmh_break': return <Activity className=\"h-4 w-4\" />\n      case 'stop_loss_hit': return <AlertTriangle className=\"h-4 w-4\" />\n      case 'profit_target_hit': return <DollarSign className=\"h-4 w-4\" />\n      case 'entry_trigger': return <Play className=\"h-4 w-4\" />\n      case 'volume_spike': return <Activity className=\"h-4 w-4\" />\n      default: return <Bell className=\"h-4 w-4\" />\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;;;AArBA;;;;;;;AA4BO,SAAS,YAAY,KAA+B;QAA/B,EAAE,SAAS,EAAoB,GAA/B;;IAC1B,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,kMAAQ,EAAU,EAAE;IAChD,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,kMAAQ,EAAC;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,kMAAQ,EAAC;IAE/C,IAAA,mMAAS;iCAAC;YACR,6BAA6B;YAC7B,MAAM,cAAc,oKAAW,CAAC,SAAS;qDAAC,CAAC;oBACzC,UAAU;oBACV,eAAe,oKAAW,CAAC,cAAc;gBAC3C;;YAEA,eAAe;YACf,UAAU,oKAAW,CAAC,SAAS;YAC/B,eAAe,oKAAW,CAAC,cAAc;YAEzC,OAAO;QACT;gCAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBAAgB,qBAAO,sNAAC,mOAAG;oBAAC,WAAU;;;;;;YAC3C,KAAK;gBAAsB,qBAAO,sNAAC,4OAAM;oBAAC,WAAU;;;;;;YACpD,KAAK;gBAAkB,qBAAO,sNAAC,4PAAU;oBAAC,WAAU;;;;;;YACpD,KAAK;gBAAa,qBAAO,sNAAC,kPAAQ;oBAAC,WAAU;;;;;;YAC7C,KAAK;gBAAiB,qBAAO,sNAAC,qQAAa;oBAAC,WAAU;;;;;;YACtD,KAAK;gBAAqB,qBAAO,sNAAC,4PAAU;oBAAC,WAAU;;;;;;YACvD,KAAK;gBAAiB,qBAAO,sNAAC,sOAAI;oBAAC,WAAU;;;;;;YAC7C,KAAK;gBAAgB,qBAAO,sNAAC,kPAAQ;oBAAC,WAAU;;;;;;YAChD;gBAAS,qBAAO,sNAAC,sOAAI;oBAAC,WAAU;;;;;;QAClC;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,oBAAoB,OAAO,OAAc;YAC9B;QAAf,MAAM,UAAS,iBAAA,MAAM,OAAO,cAAb,qCAAA,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACjD,IAAI,CAAC,QAAQ;QAEb,OAAQ,OAAO,IAAI;YACjB,KAAK;gBACH,QAAQ,GAAG,CAAC,kBAAkB,OAAO,IAAI;gBAEzC;YACF,KAAK;gBACH,QAAQ,GAAG,CAAC,eAAe,OAAO,IAAI;gBAEtC;YACF,KAAK;gBACH,QAAQ,GAAG,CAAC,gBAAgB,OAAO,IAAI;gBAEvC;YACF,KAAK;gBACH,QAAQ,GAAG,CAAC,gBAAgB,OAAO,IAAI;gBAEvC;YACF,KAAK;gBACH,oKAAW,CAAC,WAAW,CAAC,MAAM,EAAE;gBAChC;QACJ;QAEA,kCAAkC;QAClC,oKAAW,CAAC,UAAU,CAAC,MAAM,EAAE;IACjC;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,MAAM,IAAI;QAChB,MAAM,YAAY,IAAI,KAAK;QAC3B,MAAM,SAAS,IAAI,OAAO,KAAK,UAAU,OAAO;QAChD,MAAM,WAAW,KAAK,KAAK,CAAC,SAAS;QACrC,MAAM,YAAY,KAAK,KAAK,CAAC,WAAW;QAExC,IAAI,WAAW,GAAG,OAAO;QACzB,IAAI,WAAW,IAAI,OAAO,AAAC,GAAW,OAAT,UAAS;QACtC,IAAI,YAAY,IAAI,OAAO,AAAC,GAAY,OAAV,WAAU;QACxC,OAAO,UAAU,kBAAkB;IACrC;IAEA,qBACE,sNAAC;QAAI,WAAW,AAAC,YAAqB,OAAV;;0BAE1B,sNAAC,wKAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;oBAET,cAAc,kBACb,sNAAC,sPAAQ;wBAAC,WAAU;;;;;6CAEpB,sNAAC,sOAAI;wBAAC,WAAU;;;;;;oBAEjB,cAAc,mBACb,sNAAC,sKAAK;wBAAC,WAAU;kCACd,cAAc,KAAK,QAAQ;;;;;;;;;;;;YAMjC,wBACC,sNAAC;gBAAI,WAAU;;kCACb,sNAAC;wBAAI,WAAU;kCACb,cAAA,sNAAC;4BAAI,WAAU;;8CACb,sNAAC;oCAAG,WAAU;8CAAgB;;;;;;8CAC9B,sNAAC;oCAAI,WAAU;;wCACZ,cAAc,mBACb,sNAAC,wKAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,oKAAW,CAAC,aAAa;sDACzC;;;;;;sDAIH,sNAAC,wKAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,UAAU;sDAEzB,cAAA,sNAAC,6NAAC;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMrB,sNAAC;wBAAI,WAAU;kCACZ,OAAO,MAAM,KAAK,kBACjB,sNAAC;4BAAI,WAAU;;8CACb,sNAAC,sOAAI;oCAAC,WAAU;;;;;;8CAChB,sNAAC;8CAAE;;;;;;;;;;;mCAGL,OAAO,GAAG,CAAC,CAAC,sBACV,sNAAC;gCAEC,WAAW,AAAC,iCAEX,OADC,CAAC,MAAM,IAAI,GAAG,kBAAkB;gCAElC,SAAS,IAAM,CAAC,MAAM,IAAI,IAAI,oKAAW,CAAC,UAAU,CAAC,MAAM,EAAE;0CAE7D,cAAA,sNAAC;oCAAI,WAAU;;sDACb,sNAAC;4CAAI,WAAW,AAAC,oBAAiD,OAA9B,cAAc,MAAM,QAAQ;sDAC7D,aAAa,MAAM,IAAI;;;;;;sDAE1B,sNAAC;4CAAI,WAAU;;8DACb,sNAAC;oDAAI,WAAU;;sEACb,sNAAC;4DAAG,WAAU;sEACX,MAAM,KAAK;;;;;;wDAEb,CAAC,MAAM,IAAI,kBACV,sNAAC;4DAAI,WAAU;;;;;;;;;;;;8DAGnB,sNAAC;oDAAE,WAAU;8DACV,MAAM,OAAO;;;;;;8DAEhB,sNAAC;oDAAI,WAAU;8DACb,cAAA,sNAAC;wDAAI,WAAU;;0EACb,sNAAC,yOAAK;gEAAC,WAAU;;;;;;4DAChB,cAAc,MAAM,SAAS;0EAC9B,sNAAC,sKAAK;gEAAC,SAAQ;gEAAU,WAAU;0EAChC,MAAM,QAAQ;;;;;;;;;;;;;;;;;gDAMpB,MAAM,UAAU,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,MAAM,GAAG,mBAC3D,sNAAC;oDAAI,WAAU;8DACZ,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,uBAClB,sNAAC,wKAAM;4DAEL,SAAQ;4DACR,MAAK;4DACL,SAAS,CAAC;gEACR,EAAE,eAAe;gEACjB,kBAAkB,OAAO,OAAO,EAAE;4DACpC;4DACA,WAAU;;gEAET,OAAO,IAAI,KAAK,iCAAmB,sNAAC,sOAAI;oEAAC,WAAU;;;;;;gEACnD,OAAO,IAAI,KAAK,8BAAgB,sNAAC,mOAAG;oEAAC,WAAU;;;;;;gEAC/C,OAAO,IAAI,KAAK,+BAAiB,sNAAC,4PAAU;oEAAC,WAAU;;;;;;gEACvD,OAAO,KAAK;;2DAZR,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;+BArCrB,MAAM,EAAE;;;;;;;;;;oBA6DpB,OAAO,MAAM,GAAG,mBACf,sNAAC;wBAAI,WAAU;kCACb,cAAA,sNAAC,wKAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;gCACP,oKAAW,CAAC,cAAc;gCAC1B,UAAU;4BACZ;4BACA,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AASf;GA/NgB;KAAA;AAkOT,SAAS,WAAW,KAA6D;QAA7D,EAAE,KAAK,EAAE,SAAS,EAA2C,GAA7D;;IACzB,IAAA,mMAAS;gCAAC;YACR,wDAAwD;YACxD,IAAI,MAAM,QAAQ,KAAK,YAAY;gBACjC,MAAM,QAAQ,WAAW,WAAW;gBACpC;4CAAO,IAAM,aAAa;;YAC5B;QACF;+BAAG;QAAC,MAAM,QAAQ;QAAE;KAAU;IAE9B,qBACE,sNAAC,oKAAI;QAAC,WAAW,AAAC,sDAIjB,OAHC,MAAM,QAAQ,KAAK,aAAa,qBAChC,MAAM,QAAQ,KAAK,SAAS,wBAC5B;;0BAEA,sNAAC,0KAAU;gBAAC,WAAU;0BACpB,cAAA,sNAAC;oBAAI,WAAU;;sCACb,sNAAC;4BAAI,WAAU;;8CACb,sNAAC;oCAAI,WAAW,AAAC,oBAAiD,OAA9B,cAAc,MAAM,QAAQ;8CAC7D,aAAa,MAAM,IAAI;;;;;;8CAE1B,sNAAC,yKAAS;oCAAC,WAAU;8CAAW,MAAM,KAAK;;;;;;;;;;;;sCAE7C,sNAAC,wKAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,sNAAC,6NAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAInB,sNAAC,2KAAW;gBAAC,WAAU;;kCACrB,sNAAC;wBAAE,WAAU;kCACV,MAAM,OAAO;;;;;;oBAEf,MAAM,UAAU,IAAI,MAAM,OAAO,kBAChC,sNAAC;wBAAI,WAAU;kCACZ,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,uBAC9B,sNAAC,wKAAM;gCAEL,SAAS,OAAO,IAAI,KAAK,kBAAkB,YAAY;gCACvD,MAAK;gCACL,SAAS;oCACP,gBAAgB;oCAChB,QAAQ,GAAG,CAAC,iBAAiB;oCAC7B;gCACF;gCACA,WAAU;0CAET,OAAO,KAAK;+BAVR,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;IAmB5B,SAAS,cAAc,QAAgB;QACrC,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,SAAS,aAAa,IAAe;QACnC,OAAQ;YACN,KAAK;gBAAgB,qBAAO,sNAAC,mOAAG;oBAAC,WAAU;;;;;;YAC3C,KAAK;gBAAsB,qBAAO,sNAAC,4OAAM;oBAAC,WAAU;;;;;;YACpD,KAAK;gBAAkB,qBAAO,sNAAC,4PAAU;oBAAC,WAAU;;;;;;YACpD,KAAK;gBAAa,qBAAO,sNAAC,kPAAQ;oBAAC,WAAU;;;;;;YAC7C,KAAK;gBAAiB,qBAAO,sNAAC,qQAAa;oBAAC,WAAU;;;;;;YACtD,KAAK;gBAAqB,qBAAO,sNAAC,4PAAU;oBAAC,WAAU;;;;;;YACvD,KAAK;gBAAiB,qBAAO,sNAAC,sOAAI;oBAAC,WAAU;;;;;;YAC7C,KAAK;gBAAgB,qBAAO,sNAAC,kPAAQ;oBAAC,WAAU;;;;;;YAChD;gBAAS,qBAAO,sNAAC,sOAAI;oBAAC,WAAU;;;;;;QAClC;IACF;AACF;IAnFgB;MAAA", "debugId": null}}, {"offset": {"line": 6905, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/components/AIConfiguration.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { \n  Brain, \n  Settings, \n  CheckCircle, \n  XCircle, \n  Loader2,\n  Sparkles,\n  AlertTriangle,\n  Info\n} from 'lucide-react';\n\ninterface AIStatus {\n  enabled: boolean;\n  model: string | null;\n  features: {\n    marketCommentary: boolean;\n    riskAssessment: boolean;\n    tradingRecommendations: boolean;\n  };\n}\n\nexport default function AIConfiguration() {\n  const [aiStatus, setAiStatus] = useState<AIStatus | null>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    checkAIStatus();\n  }, []);\n\n  const checkAIStatus = async () => {\n    setIsLoading(true);\n    setError(null);\n    \n    try {\n      const response = await fetch('/api/ai?action=status');\n      const status = await response.json();\n      setAiStatus(status);\n    } catch (error) {\n      console.error('Error checking AI status:', error);\n      setError('Unable to connect to AI service');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const testAIConnection = async () => {\n    setIsLoading(true);\n    setError(null);\n    \n    try {\n      const response = await fetch('/api/ai?action=model');\n      if (response.ok) {\n        const { model } = await response.json();\n        setAiStatus(prev => prev ? { ...prev, model } : null);\n        alert(`AI Connection Successful!\\nUsing model: ${model}`);\n      } else {\n        throw new Error('Failed to connect to AI service');\n      }\n    } catch (error) {\n      console.error('Error testing AI connection:', error);\n      setError('AI connection test failed');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  if (isLoading && !aiStatus) {\n    return (\n      <Card className=\"bg-slate-800/50 border-slate-700\">\n        <CardContent className=\"p-6\">\n          <div className=\"flex items-center justify-center\">\n            <Loader2 className=\"h-6 w-6 animate-spin text-blue-400 mr-2\" />\n            <span className=\"text-slate-300\">Checking AI configuration...</span>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* AI Status Overview */}\n      <Card className=\"bg-slate-800/50 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"text-white flex items-center justify-between\">\n            <div className=\"flex items-center\">\n              <Brain className=\"mr-2 h-5 w-5 text-blue-400\" />\n              AI Configuration\n            </div>\n            <Button\n              size=\"sm\"\n              variant=\"outline\"\n              onClick={checkAIStatus}\n              disabled={isLoading}\n              className=\"text-slate-300 hover:text-white\"\n            >\n              {isLoading ? (\n                <Loader2 className=\"h-4 w-4 animate-spin mr-1\" />\n              ) : (\n                <Settings className=\"h-4 w-4 mr-1\" />\n              )}\n              Refresh Status\n            </Button>\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          {error && (\n            <div className=\"mb-4 p-3 bg-red-900/20 border border-red-500/30 rounded-lg\">\n              <div className=\"flex items-center text-red-400\">\n                <AlertTriangle className=\"h-4 w-4 mr-2\" />\n                {error}\n              </div>\n            </div>\n          )}\n\n          {aiStatus && (\n            <div className=\"space-y-4\">\n              {/* Status Overview */}\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                <div className=\"text-center p-4 bg-slate-700/30 rounded-lg\">\n                  <div className=\"flex items-center justify-center mb-2\">\n                    {aiStatus.enabled ? (\n                      <CheckCircle className=\"h-6 w-6 text-green-400\" />\n                    ) : (\n                      <XCircle className=\"h-6 w-6 text-red-400\" />\n                    )}\n                  </div>\n                  <div className=\"text-sm text-slate-400\">Status</div>\n                  <div className={`font-semibold ${aiStatus.enabled ? 'text-green-400' : 'text-red-400'}`}>\n                    {aiStatus.enabled ? 'Enabled' : 'Disabled'}\n                  </div>\n                </div>\n\n                <div className=\"text-center p-4 bg-slate-700/30 rounded-lg\">\n                  <div className=\"flex items-center justify-center mb-2\">\n                    <Sparkles className=\"h-6 w-6 text-blue-400\" />\n                  </div>\n                  <div className=\"text-sm text-slate-400\">Model</div>\n                  <div className=\"font-semibold text-white\">\n                    {aiStatus.model || 'Not Available'}\n                  </div>\n                </div>\n\n                <div className=\"text-center p-4 bg-slate-700/30 rounded-lg\">\n                  <Button\n                    onClick={testAIConnection}\n                    disabled={!aiStatus.enabled || isLoading}\n                    className=\"w-full bg-blue-600 hover:bg-blue-700\"\n                  >\n                    {isLoading ? (\n                      <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\n                    ) : (\n                      <CheckCircle className=\"h-4 w-4 mr-2\" />\n                    )}\n                    Test Connection\n                  </Button>\n                </div>\n              </div>\n\n              {/* Feature Status */}\n              <div>\n                <h3 className=\"text-lg font-semibold text-white mb-3 flex items-center\">\n                  <Settings className=\"h-5 w-5 mr-2\" />\n                  Available Features\n                </h3>\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  <div className=\"p-3 bg-slate-700/20 rounded-lg\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <span className=\"text-white font-medium\">Market Commentary</span>\n                      <Badge className={aiStatus.features.marketCommentary && aiStatus.enabled \n                        ? 'bg-green-500/20 text-green-400' \n                        : 'bg-gray-500/20 text-gray-400'\n                      }>\n                        {aiStatus.features.marketCommentary && aiStatus.enabled ? 'Active' : 'Inactive'}\n                      </Badge>\n                    </div>\n                    <p className=\"text-xs text-slate-400\">\n                      AI-powered market analysis and commentary on scan results\n                    </p>\n                  </div>\n\n                  <div className=\"p-3 bg-slate-700/20 rounded-lg\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <span className=\"text-white font-medium\">Risk Assessment</span>\n                      <Badge className={aiStatus.features.riskAssessment && aiStatus.enabled \n                        ? 'bg-green-500/20 text-green-400' \n                        : 'bg-gray-500/20 text-gray-400'\n                      }>\n                        {aiStatus.features.riskAssessment && aiStatus.enabled ? 'Active' : 'Inactive'}\n                      </Badge>\n                    </div>\n                    <p className=\"text-xs text-slate-400\">\n                      Individual setup risk analysis with AI-generated recommendations\n                    </p>\n                  </div>\n\n                  <div className=\"p-3 bg-slate-700/20 rounded-lg\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <span className=\"text-white font-medium\">Trading Recommendations</span>\n                      <Badge className={aiStatus.features.tradingRecommendations && aiStatus.enabled \n                        ? 'bg-green-500/20 text-green-400' \n                        : 'bg-gray-500/20 text-gray-400'\n                      }>\n                        {aiStatus.features.tradingRecommendations && aiStatus.enabled ? 'Active' : 'Inactive'}\n                      </Badge>\n                    </div>\n                    <p className=\"text-xs text-slate-400\">\n                      Personalized trading recommendations based on your preferences\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              {/* Configuration Instructions */}\n              {!aiStatus.enabled && (\n                <div className=\"p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg\">\n                  <h4 className=\"text-white font-semibold mb-2 flex items-center\">\n                    <Info className=\"h-4 w-4 mr-2\" />\n                    Enable AI Features\n                  </h4>\n                  <div className=\"text-sm text-slate-300 space-y-2\">\n                    <p>To enable AI-powered insights:</p>\n                    <ol className=\"list-decimal list-inside space-y-1 ml-4\">\n                      <li>Ensure OPENAI_API_KEY is set in your .env.local file</li>\n                      <li>Set OPENAI_ENABLED=true in your environment variables</li>\n                      <li>Restart the application</li>\n                      <li>Click \"Refresh Status\" to verify the configuration</li>\n                    </ol>\n                  </div>\n                </div>\n              )}\n\n              {/* Success Message */}\n              {aiStatus.enabled && (\n                <div className=\"p-4 bg-green-900/20 border border-green-500/30 rounded-lg\">\n                  <h4 className=\"text-green-400 font-semibold mb-2 flex items-center\">\n                    <CheckCircle className=\"h-4 w-4 mr-2\" />\n                    AI Features Active\n                  </h4>\n                  <p className=\"text-sm text-slate-300\">\n                    Your SwingTrader AI is enhanced with GPT-4o intelligence. You'll see AI-powered insights \n                    in your trading setup cards and market analysis sections.\n                  </p>\n                </div>\n              )}\n            </div>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AA2Be,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,kMAAQ,EAAkB;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,kMAAQ,EAAC;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,kMAAQ,EAAgB;IAElD,IAAA,mMAAS;qCAAC;YACR;QACF;oCAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB;QACvB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI;gBACrC,YAAY,CAAA,OAAQ,OAAO;wBAAE,GAAG,IAAI;wBAAE;oBAAM,IAAI;gBAChD,MAAM,AAAC,2CAAgD,OAAN;YACnD,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,aAAa,CAAC,UAAU;QAC1B,qBACE,sNAAC,oKAAI;YAAC,WAAU;sBACd,cAAA,sNAAC,2KAAW;gBAAC,WAAU;0BACrB,cAAA,sNAAC;oBAAI,WAAU;;sCACb,sNAAC,wPAAO;4BAAC,WAAU;;;;;;sCACnB,sNAAC;4BAAK,WAAU;sCAAiB;;;;;;;;;;;;;;;;;;;;;;IAK3C;IAEA,qBACE,sNAAC;QAAI,WAAU;kBAEb,cAAA,sNAAC,oKAAI;YAAC,WAAU;;8BACd,sNAAC,0KAAU;8BACT,cAAA,sNAAC,yKAAS;wBAAC,WAAU;;0CACnB,sNAAC;gCAAI,WAAU;;kDACb,sNAAC,yOAAK;wCAAC,WAAU;;;;;;oCAA+B;;;;;;;0CAGlD,sNAAC,wKAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU;gCACV,WAAU;;oCAET,0BACC,sNAAC,wPAAO;wCAAC,WAAU;;;;;6DAEnB,sNAAC,kPAAQ;wCAAC,WAAU;;;;;;oCACpB;;;;;;;;;;;;;;;;;;8BAKR,sNAAC,2KAAW;;wBACT,uBACC,sNAAC;4BAAI,WAAU;sCACb,cAAA,sNAAC;gCAAI,WAAU;;kDACb,sNAAC,qQAAa;wCAAC,WAAU;;;;;;oCACxB;;;;;;;;;;;;wBAKN,0BACC,sNAAC;4BAAI,WAAU;;8CAEb,sNAAC;oCAAI,WAAU;;sDACb,sNAAC;4CAAI,WAAU;;8DACb,sNAAC;oDAAI,WAAU;8DACZ,SAAS,OAAO,iBACf,sNAAC,sQAAW;wDAAC,WAAU;;;;;6EAEvB,sNAAC,mPAAO;wDAAC,WAAU;;;;;;;;;;;8DAGvB,sNAAC;oDAAI,WAAU;8DAAyB;;;;;;8DACxC,sNAAC;oDAAI,WAAW,AAAC,iBAAqE,OAArD,SAAS,OAAO,GAAG,mBAAmB;8DACpE,SAAS,OAAO,GAAG,YAAY;;;;;;;;;;;;sDAIpC,sNAAC;4CAAI,WAAU;;8DACb,sNAAC;oDAAI,WAAU;8DACb,cAAA,sNAAC,kPAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,sNAAC;oDAAI,WAAU;8DAAyB;;;;;;8DACxC,sNAAC;oDAAI,WAAU;8DACZ,SAAS,KAAK,IAAI;;;;;;;;;;;;sDAIvB,sNAAC;4CAAI,WAAU;sDACb,cAAA,sNAAC,wKAAM;gDACL,SAAS;gDACT,UAAU,CAAC,SAAS,OAAO,IAAI;gDAC/B,WAAU;;oDAET,0BACC,sNAAC,wPAAO;wDAAC,WAAU;;;;;6EAEnB,sNAAC,sQAAW;wDAAC,WAAU;;;;;;oDACvB;;;;;;;;;;;;;;;;;;8CAOR,sNAAC;;sDACC,sNAAC;4CAAG,WAAU;;8DACZ,sNAAC,kPAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,sNAAC;4CAAI,WAAU;;8DACb,sNAAC;oDAAI,WAAU;;sEACb,sNAAC;4DAAI,WAAU;;8EACb,sNAAC;oEAAK,WAAU;8EAAyB;;;;;;8EACzC,sNAAC,sKAAK;oEAAC,WAAW,SAAS,QAAQ,CAAC,gBAAgB,IAAI,SAAS,OAAO,GACpE,mCACA;8EAED,SAAS,QAAQ,CAAC,gBAAgB,IAAI,SAAS,OAAO,GAAG,WAAW;;;;;;;;;;;;sEAGzE,sNAAC;4DAAE,WAAU;sEAAyB;;;;;;;;;;;;8DAKxC,sNAAC;oDAAI,WAAU;;sEACb,sNAAC;4DAAI,WAAU;;8EACb,sNAAC;oEAAK,WAAU;8EAAyB;;;;;;8EACzC,sNAAC,sKAAK;oEAAC,WAAW,SAAS,QAAQ,CAAC,cAAc,IAAI,SAAS,OAAO,GAClE,mCACA;8EAED,SAAS,QAAQ,CAAC,cAAc,IAAI,SAAS,OAAO,GAAG,WAAW;;;;;;;;;;;;sEAGvE,sNAAC;4DAAE,WAAU;sEAAyB;;;;;;;;;;;;8DAKxC,sNAAC;oDAAI,WAAU;;sEACb,sNAAC;4DAAI,WAAU;;8EACb,sNAAC;oEAAK,WAAU;8EAAyB;;;;;;8EACzC,sNAAC,sKAAK;oEAAC,WAAW,SAAS,QAAQ,CAAC,sBAAsB,IAAI,SAAS,OAAO,GAC1E,mCACA;8EAED,SAAS,QAAQ,CAAC,sBAAsB,IAAI,SAAS,OAAO,GAAG,WAAW;;;;;;;;;;;;sEAG/E,sNAAC;4DAAE,WAAU;sEAAyB;;;;;;;;;;;;;;;;;;;;;;;;gCAQ3C,CAAC,SAAS,OAAO,kBAChB,sNAAC;oCAAI,WAAU;;sDACb,sNAAC;4CAAG,WAAU;;8DACZ,sNAAC,sOAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,sNAAC;4CAAI,WAAU;;8DACb,sNAAC;8DAAE;;;;;;8DACH,sNAAC;oDAAG,WAAU;;sEACZ,sNAAC;sEAAG;;;;;;sEACJ,sNAAC;sEAAG;;;;;;sEACJ,sNAAC;sEAAG;;;;;;sEACJ,sNAAC;sEAAG;;;;;;;;;;;;;;;;;;;;;;;;gCAOX,SAAS,OAAO,kBACf,sNAAC;oCAAI,WAAU;;sDACb,sNAAC;4CAAG,WAAU;;8DACZ,sNAAC,sQAAW;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAG1C,sNAAC;4CAAE,WAAU;sDAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYxD;GAvOwB;KAAA", "debugId": null}}, {"offset": {"line": 7542, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { <PERSON><PERSON>dingUp, BarChart3, Target, Shield, Brain, Zap, Search, Loader2, Scan } from 'lucide-react'\nimport { <PERSON><PERSON> } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { SwingTradingAnalysis, StockData } from '@/types/trading'\nimport { formatCurrency, formatPercentage } from '@/lib/utils'\nimport { SwingScanner } from '@/components/SwingScanner'\nimport { StrategyScanner } from '@/components/StrategyScanner'\nimport { EventDrivenScanner } from '@/components/EventDrivenScanner'\nimport { AlertCenter } from '@/components/AlertCenter'\nimport AIConfiguration from '@/components/AIConfiguration'\n// import TradingInterface from '@/components/TradingInterface'\n\nexport default function Home() {\n  const [selectedSymbol, setSelectedSymbol] = useState('SPY')\n  const [customSymbol, setCustomSymbol] = useState('')\n  const [isAnalyzing, setIsAnalyzing] = useState(false)\n  const [analysis, setAnalysis] = useState<SwingTradingAnalysis | null>(null)\n  const [stockData, setStockData] = useState<StockData | null>(null)\n  const [error, setError] = useState<string | null>(null)\n  const [activeTab, setActiveTab] = useState<'individual' | 'scanner' | 'strategies' | 'event-driven' | 'trading' | 'ai'>('event-driven')\n\n  const popularSymbols = ['SPY', 'QQQ', 'AAPL', 'TSLA', 'NVDA', 'MSFT', 'AMZN', 'GOOGL']\n\n  const handleAnalysis = async (symbol: string) => {\n    setIsAnalyzing(true)\n    setError(null)\n    setAnalysis(null)\n    setStockData(null)\n\n    try {\n      // Fetch stock quote and analysis in parallel\n      const [quoteResponse, analysisResponse] = await Promise.all([\n        fetch(`/api/stocks/quote/${symbol}`),\n        fetch(`/api/analysis/swing/${symbol}`)\n      ])\n\n      if (!quoteResponse.ok || !analysisResponse.ok) {\n        throw new Error('Failed to fetch data')\n      }\n\n      const [quoteData, analysisData] = await Promise.all([\n        quoteResponse.json(),\n        analysisResponse.json()\n      ])\n\n      setStockData(quoteData)\n      setAnalysis(analysisData)\n    } catch (err) {\n      setError('Failed to analyze stock. Please try again.')\n      console.error('Analysis error:', err)\n    } finally {\n      setIsAnalyzing(false)\n    }\n  }\n\n  const handleCustomSymbolSubmit = (e: React.FormEvent) => {\n    e.preventDefault()\n    if (customSymbol.trim()) {\n      handleAnalysis(customSymbol.toUpperCase())\n      setSelectedSymbol(customSymbol.toUpperCase())\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900\">\n      {/* Header */}\n      <header className=\"border-b border-slate-800 bg-slate-900/50 backdrop-blur-sm\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-2\">\n              <Brain className=\"h-8 w-8 text-blue-400\" />\n              <h1 className=\"text-2xl font-bold text-white\">SwingTrader AI</h1>\n            </div>\n            <nav className=\"hidden md:flex items-center space-x-6\">\n              <button\n                onClick={() => setActiveTab('strategies')}\n                className={`transition-colors ${activeTab === 'strategies' ? 'text-white' : 'text-slate-300 hover:text-white'}`}\n              >\n                Pro Strategies\n              </button>\n              <button\n                onClick={() => setActiveTab('scanner')}\n                className={`transition-colors ${activeTab === 'scanner' ? 'text-white' : 'text-slate-300 hover:text-white'}`}\n              >\n                Basic Scanner\n              </button>\n              <button\n                onClick={() => setActiveTab('individual')}\n                className={`transition-colors ${activeTab === 'individual' ? 'text-white' : 'text-slate-300 hover:text-white'}`}\n              >\n                Individual Analysis\n              </button>\n              <button\n                onClick={() => setActiveTab('trading')}\n                className={`transition-colors ${activeTab === 'trading' ? 'text-white' : 'text-slate-300 hover:text-white'}`}\n              >\n                Paper Trading\n              </button>\n              <AlertCenter />\n              <Button variant=\"outline\" className=\"border-blue-400 text-blue-400 hover:bg-blue-400 hover:text-white\">\n                Sign In\n              </Button>\n            </nav>\n          </div>\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <section className=\"py-20 px-4\">\n        <div className=\"container mx-auto text-center\">\n          <h2 className=\"text-5xl font-bold text-white mb-6\">\n            AI-Powered Swing Trading Analysis\n          </h2>\n          <p className=\"text-xl text-slate-300 mb-8 max-w-3xl mx-auto\">\n            Professional swing trading strategies with automated scanning, precise entry/exit rules,\n            and risk management based on proven methodologies.\n          </p>\n\n          {/* Tab Navigation */}\n          <div className=\"flex justify-center mb-8\">\n            <div className=\"bg-slate-800/50 rounded-lg p-1 flex\">\n              <button\n                onClick={() => setActiveTab('event-driven')}\n                className={`px-6 py-3 rounded-md transition-all ${\n                  activeTab === 'event-driven'\n                    ? 'bg-blue-600 text-white'\n                    : 'text-slate-300 hover:text-white hover:bg-slate-700/50'\n                }`}\n              >\n                <Target className=\"inline mr-2 h-4 w-4\" />\n                Event-Driven\n              </button>\n              <button\n                onClick={() => setActiveTab('strategies')}\n                className={`px-6 py-3 rounded-md transition-all ${\n                  activeTab === 'strategies'\n                    ? 'bg-blue-600 text-white'\n                    : 'text-slate-300 hover:text-white hover:bg-slate-700/50'\n                }`}\n              >\n                <Zap className=\"inline mr-2 h-4 w-4\" />\n                Pro Strategies\n              </button>\n              <button\n                onClick={() => setActiveTab('scanner')}\n                className={`px-6 py-3 rounded-md transition-all ${\n                  activeTab === 'scanner'\n                    ? 'bg-blue-600 text-white'\n                    : 'text-slate-300 hover:text-white hover:bg-slate-700/50'\n                }`}\n              >\n                <Scan className=\"inline mr-2 h-4 w-4\" />\n                Basic Scanner\n              </button>\n              <button\n                onClick={() => setActiveTab('individual')}\n                className={`px-6 py-3 rounded-md transition-all ${\n                  activeTab === 'individual'\n                    ? 'bg-blue-600 text-white'\n                    : 'text-slate-300 hover:text-white hover:bg-slate-700/50'\n                }`}\n              >\n                <Search className=\"inline mr-2 h-4 w-4\" />\n                Individual Analysis\n              </button>\n              <button\n                onClick={() => setActiveTab('trading')}\n                className={`px-6 py-3 rounded-md transition-all ${\n                  activeTab === 'trading'\n                    ? 'bg-blue-600 text-white'\n                    : 'text-slate-300 hover:text-white hover:bg-slate-700/50'\n                }`}\n              >\n                <TrendingUp className=\"inline mr-2 h-4 w-4\" />\n                Trading\n              </button>\n              <button\n                onClick={() => setActiveTab('ai')}\n                className={`px-6 py-3 rounded-md transition-all ${\n                  activeTab === 'ai'\n                    ? 'bg-blue-600 text-white'\n                    : 'text-slate-300 hover:text-white hover:bg-slate-700/50'\n                }`}\n              >\n                <Brain className=\"inline mr-2 h-4 w-4\" />\n                AI Config\n              </button>\n            </div>\n          </div>\n\n          {/* Content based on active tab */}\n          {activeTab === 'event-driven' ? (\n            <div className=\"mb-8\">\n              <h3 className=\"text-lg text-slate-300 mb-4\">Event-Driven Catalyst Detection & Perfect-Pick Trading</h3>\n              <p className=\"text-slate-400 mb-6\">\n                Advanced catalyst detection with pre-market gap scanning, technical gate analysis, and Perfect-Pick setups\n              </p>\n            </div>\n          ) : activeTab === 'strategies' ? (\n            <div className=\"mb-8\">\n              <h3 className=\"text-lg text-slate-300 mb-4\">Professional Swing Trading Strategies</h3>\n              <p className=\"text-slate-400 mb-6\">\n                Overnight Momentum & Technical Breakout strategies with precise entry/exit rules and position sizing\n              </p>\n            </div>\n          ) : activeTab === 'scanner' ? (\n            <div className=\"mb-8\">\n              <h3 className=\"text-lg text-slate-300 mb-4\">Basic Swing Trading Scanner</h3>\n              <p className=\"text-slate-400 mb-6\">\n                General swing trading analysis with technical indicators and trend detection\n              </p>\n            </div>\n          ) : (\n            <div className=\"mb-8\">\n              <h3 className=\"text-lg text-slate-300 mb-4\">Individual Stock Analysis</h3>\n              <div className=\"flex flex-wrap justify-center gap-2 mb-6\">\n                {popularSymbols.map((symbol) => (\n                  <Button\n                    key={symbol}\n                    variant={selectedSymbol === symbol ? \"default\" : \"outline\"}\n                    onClick={() => {\n                      setSelectedSymbol(symbol)\n                      handleAnalysis(symbol)\n                    }}\n                    disabled={isAnalyzing}\n                    className={selectedSymbol === symbol\n                      ? \"bg-blue-600 hover:bg-blue-700\"\n                      : \"border-slate-600 text-slate-300 hover:bg-slate-800\"\n                    }\n                  >\n                    {symbol}\n                  </Button>\n                ))}\n              </div>\n\n              {/* Custom Symbol Input */}\n              <form onSubmit={handleCustomSymbolSubmit} className=\"flex justify-center gap-2 mb-6\">\n                <div className=\"relative\">\n                  <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                  <input\n                    type=\"text\"\n                    placeholder=\"Enter symbol (e.g., AAPL)\"\n                    value={customSymbol}\n                    onChange={(e) => setCustomSymbol(e.target.value)}\n                    className=\"pl-10 pr-4 py-2 bg-slate-800 border border-slate-600 rounded-md text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    disabled={isAnalyzing}\n                  />\n                </div>\n                <Button\n                  type=\"submit\"\n                  disabled={isAnalyzing || !customSymbol.trim()}\n                  className=\"bg-blue-600 hover:bg-blue-700\"\n                >\n                  Analyze\n                </Button>\n              </form>\n\n              <Button\n                size=\"lg\"\n                onClick={() => handleAnalysis(selectedSymbol)}\n                disabled={isAnalyzing}\n                className=\"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3\"\n              >\n                {isAnalyzing ? (\n                  <>\n                    <Loader2 className=\"mr-2 h-5 w-5 animate-spin\" />\n                    Analyzing {selectedSymbol}...\n                  </>\n                ) : (\n                  <>\n                    <Zap className=\"mr-2 h-5 w-5\" />\n                    Get AI Analysis for {selectedSymbol}\n                  </>\n                )}\n              </Button>\n            </div>\n          )}\n        </div>\n      </section>\n\n      {/* Event-Driven Scanner Section */}\n      {activeTab === 'event-driven' && (\n        <section className=\"py-12 px-4\">\n          <div className=\"container mx-auto\">\n            <EventDrivenScanner accountSize={100000} riskPercent={2} />\n          </div>\n        </section>\n      )}\n\n      {/* Strategy Scanner Section */}\n      {activeTab === 'strategies' && (\n        <section className=\"py-12 px-4\">\n          <div className=\"container mx-auto\">\n            <StrategyScanner autoScan={true} accountSize={100000} />\n          </div>\n        </section>\n      )}\n\n      {/* Basic Scanner Section */}\n      {activeTab === 'scanner' && (\n        <section className=\"py-12 px-4\">\n          <div className=\"container mx-auto\">\n            <SwingScanner autoScan={false} />\n          </div>\n        </section>\n      )}\n\n      {/* Trading Section */}\n      {activeTab === 'trading' && (\n        <section className=\"py-12 px-4\">\n          <div className=\"container mx-auto\">\n            <div className=\"mb-8\">\n              <h2 className=\"text-3xl font-bold text-white mb-4\">Paper Trading</h2>\n              <p className=\"text-slate-400 mb-6\">\n                Execute swing trades with Alpaca or Interactive Brokers paper trading accounts\n              </p>\n            </div>\n            <div className=\"text-center text-slate-400\">\n              <h3 className=\"text-xl mb-4\">Paper Trading Interface</h3>\n              <p>IBKR and Alpaca integration is ready!</p>\n              <p className=\"mt-2\">Trading interface temporarily disabled due to component loading issue.</p>\n              <p className=\"mt-2\">API endpoints are working: <code>/api/trading</code></p>\n            </div>\n          </div>\n        </section>\n      )}\n\n      {/* AI Configuration Section */}\n      {activeTab === 'ai' && (\n        <section className=\"py-12 px-4\">\n          <div className=\"container mx-auto\">\n            <div className=\"mb-8\">\n              <h2 className=\"text-3xl font-bold text-white mb-4\">AI Configuration</h2>\n              <p className=\"text-slate-400 mb-6\">\n                Configure and manage AI-powered features for enhanced trading analysis\n              </p>\n            </div>\n            <AIConfiguration />\n          </div>\n        </section>\n      )}\n\n      {/* Error Display */}\n      {error && (\n        <section className=\"py-8 px-4\">\n          <div className=\"container mx-auto\">\n            <Card className=\"bg-red-900/20 border-red-500/50\">\n              <CardContent className=\"p-6\">\n                <p className=\"text-red-300 text-center\">{error}</p>\n              </CardContent>\n            </Card>\n          </div>\n        </section>\n      )}\n\n      {/* Analysis Results */}\n      {activeTab === 'individual' && (stockData || analysis) && (\n        <section className=\"py-12 px-4\">\n          <div className=\"container mx-auto\">\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n\n              {/* Stock Quote Card */}\n              {stockData && (\n                <Card className=\"bg-slate-800/50 border-slate-700\">\n                  <CardHeader>\n                    <CardTitle className=\"text-white flex items-center\">\n                      <BarChart3 className=\"mr-2 h-5 w-5 text-blue-400\" />\n                      {stockData.symbol} Quote\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-3\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Price:</span>\n                        <span className=\"text-white font-semibold\">{formatCurrency(stockData.price)}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Change:</span>\n                        <span className={stockData.change >= 0 ? \"text-green-400\" : \"text-red-400\"}>\n                          {formatCurrency(stockData.change)} ({formatPercentage(stockData.changePercent)})\n                        </span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Volume:</span>\n                        <span className=\"text-white\">{stockData.volume.toLocaleString()}</span>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n\n              {/* Trading Levels Card */}\n              {analysis && (\n                <Card className=\"bg-slate-800/50 border-slate-700\">\n                  <CardHeader>\n                    <CardTitle className=\"text-white flex items-center\">\n                      <Target className=\"mr-2 h-5 w-5 text-green-400\" />\n                      Trading Levels\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-3\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Entry:</span>\n                        <span className=\"text-white font-semibold\">{formatCurrency(analysis.entryPrice)}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Stop Loss:</span>\n                        <span className=\"text-red-400\">{formatCurrency(analysis.stopLoss)}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Take Profit:</span>\n                        <span className=\"text-green-400\">{formatCurrency(analysis.takeProfit)}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Risk/Reward:</span>\n                        <span className=\"text-blue-400 font-semibold\">{analysis.riskRewardRatio.toFixed(2)}:1</span>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n\n              {/* Analysis Summary Card */}\n              {analysis && (\n                <Card className=\"bg-slate-800/50 border-slate-700\">\n                  <CardHeader>\n                    <CardTitle className=\"text-white flex items-center\">\n                      <Brain className=\"mr-2 h-5 w-5 text-purple-400\" />\n                      AI Analysis\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-3\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Trend:</span>\n                        <span className={`font-semibold ${\n                          analysis.trend === 'BULLISH' ? 'text-green-400' :\n                          analysis.trend === 'BEARISH' ? 'text-red-400' : 'text-yellow-400'\n                        }`}>\n                          {analysis.trend}\n                        </span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Confidence:</span>\n                        <span className=\"text-white font-semibold\">{analysis.confidence.toFixed(1)}%</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Recommendation:</span>\n                        <span className={`font-semibold ${\n                          analysis.recommendation.includes('BUY') ? 'text-green-400' :\n                          analysis.recommendation.includes('SELL') ? 'text-red-400' : 'text-yellow-400'\n                        }`}>\n                          {analysis.recommendation.replace('_', ' ')}\n                        </span>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n            </div>\n\n            {/* Technical Indicators */}\n            {analysis && (\n              <Card className=\"mt-6 bg-slate-800/50 border-slate-700\">\n                <CardHeader>\n                  <CardTitle className=\"text-white flex items-center\">\n                    <TrendingUp className=\"mr-2 h-5 w-5 text-orange-400\" />\n                    Technical Indicators\n                  </CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                    {analysis.indicators.map((indicator, index) => (\n                      <div key={index} className=\"p-4 bg-slate-700/50 rounded-lg\">\n                        <div className=\"flex items-center justify-between mb-2\">\n                          <h4 className=\"text-white font-medium\">{indicator.name}</h4>\n                          <span className={`px-2 py-1 rounded text-xs font-semibold ${\n                            indicator.signal === 'BUY' ? 'bg-green-500/20 text-green-400' :\n                            indicator.signal === 'SELL' ? 'bg-red-500/20 text-red-400' :\n                            'bg-yellow-500/20 text-yellow-400'\n                          }`}>\n                            {indicator.signal}\n                          </span>\n                        </div>\n                        <p className=\"text-slate-300 text-sm\">{indicator.description}</p>\n                      </div>\n                    ))}\n                  </div>\n                </CardContent>\n              </Card>\n            )}\n\n            {/* Support and Resistance Levels */}\n            {analysis && (analysis.supportLevels.length > 0 || analysis.resistanceLevels.length > 0) && (\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mt-6\">\n                {analysis.supportLevels.length > 0 && (\n                  <Card className=\"bg-slate-800/50 border-slate-700\">\n                    <CardHeader>\n                      <CardTitle className=\"text-white flex items-center\">\n                        <Shield className=\"mr-2 h-5 w-5 text-green-400\" />\n                        Support Levels\n                      </CardTitle>\n                    </CardHeader>\n                    <CardContent>\n                      <div className=\"space-y-2\">\n                        {analysis.supportLevels.map((level, index) => (\n                          <div key={index} className=\"flex justify-between\">\n                            <span className=\"text-slate-300\">Support {index + 1}:</span>\n                            <span className=\"text-green-400 font-semibold\">{formatCurrency(level)}</span>\n                          </div>\n                        ))}\n                      </div>\n                    </CardContent>\n                  </Card>\n                )}\n\n                {analysis.resistanceLevels.length > 0 && (\n                  <Card className=\"bg-slate-800/50 border-slate-700\">\n                    <CardHeader>\n                      <CardTitle className=\"text-white flex items-center\">\n                        <Shield className=\"mr-2 h-5 w-5 text-red-400\" />\n                        Resistance Levels\n                      </CardTitle>\n                    </CardHeader>\n                    <CardContent>\n                      <div className=\"space-y-2\">\n                        {analysis.resistanceLevels.map((level, index) => (\n                          <div key={index} className=\"flex justify-between\">\n                            <span className=\"text-slate-300\">Resistance {index + 1}:</span>\n                            <span className=\"text-red-400 font-semibold\">{formatCurrency(level)}</span>\n                          </div>\n                        ))}\n                      </div>\n                    </CardContent>\n                  </Card>\n                )}\n              </div>\n            )}\n          </div>\n        </section>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAZA;;;;;;;;;;;AAee,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,kMAAQ,EAAC;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,kMAAQ,EAAC;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,kMAAQ,EAAC;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,kMAAQ,EAA8B;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,kMAAQ,EAAmB;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,kMAAQ,EAAgB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,kMAAQ,EAA8E;IAExH,MAAM,iBAAiB;QAAC;QAAO;QAAO;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;KAAQ;IAEtF,MAAM,iBAAiB,OAAO;QAC5B,eAAe;QACf,SAAS;QACT,YAAY;QACZ,aAAa;QAEb,IAAI;YACF,6CAA6C;YAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC1D,MAAM,AAAC,qBAA2B,OAAP;gBAC3B,MAAM,AAAC,uBAA6B,OAAP;aAC9B;YAED,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,iBAAiB,EAAE,EAAE;gBAC7C,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,CAAC,WAAW,aAAa,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAClD,cAAc,IAAI;gBAClB,iBAAiB,IAAI;aACtB;YAED,aAAa;YACb,YAAY;QACd,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,mBAAmB;QACnC,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,2BAA2B,CAAC;QAChC,EAAE,cAAc;QAChB,IAAI,aAAa,IAAI,IAAI;YACvB,eAAe,aAAa,WAAW;YACvC,kBAAkB,aAAa,WAAW;QAC5C;IACF;IAEA,qBACE,sNAAC;QAAI,WAAU;;0BAEb,sNAAC;gBAAO,WAAU;0BAChB,cAAA,sNAAC;oBAAI,WAAU;8BACb,cAAA,sNAAC;wBAAI,WAAU;;0CACb,sNAAC;gCAAI,WAAU;;kDACb,sNAAC,yOAAK;wCAAC,WAAU;;;;;;kDACjB,sNAAC;wCAAG,WAAU;kDAAgC;;;;;;;;;;;;0CAEhD,sNAAC;gCAAI,WAAU;;kDACb,sNAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,qBAAkG,OAA9E,cAAc,eAAe,eAAe;kDAC7E;;;;;;kDAGD,sNAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,qBAA+F,OAA3E,cAAc,YAAY,eAAe;kDAC1E;;;;;;kDAGD,sNAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,qBAAkG,OAA9E,cAAc,eAAe,eAAe;kDAC7E;;;;;;kDAGD,sNAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,qBAA+F,OAA3E,cAAc,YAAY,eAAe;kDAC1E;;;;;;kDAGD,sNAAC,4KAAW;;;;;kDACZ,sNAAC,wKAAM;wCAAC,SAAQ;wCAAU,WAAU;kDAAmE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS/G,sNAAC;gBAAQ,WAAU;0BACjB,cAAA,sNAAC;oBAAI,WAAU;;sCACb,sNAAC;4BAAG,WAAU;sCAAqC;;;;;;sCAGnD,sNAAC;4BAAE,WAAU;sCAAgD;;;;;;sCAM7D,sNAAC;4BAAI,WAAU;sCACb,cAAA,sNAAC;gCAAI,WAAU;;kDACb,sNAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,uCAIX,OAHC,cAAc,iBACV,2BACA;;0DAGN,sNAAC,4OAAM;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;kDAG5C,sNAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,uCAIX,OAHC,cAAc,eACV,2BACA;;0DAGN,sNAAC,mOAAG;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;kDAGzC,sNAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,uCAIX,OAHC,cAAc,YACV,2BACA;;0DAGN,sNAAC,sOAAI;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;kDAG1C,sNAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,uCAIX,OAHC,cAAc,eACV,2BACA;;0DAGN,sNAAC,4OAAM;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;kDAG5C,sNAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,uCAIX,OAHC,cAAc,YACV,2BACA;;0DAGN,sNAAC,4PAAU;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;kDAGhD,sNAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,uCAIX,OAHC,cAAc,OACV,2BACA;;0DAGN,sNAAC,yOAAK;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;;;;;;;;;;;;wBAO9C,cAAc,+BACb,sNAAC;4BAAI,WAAU;;8CACb,sNAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,sNAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;mCAInC,cAAc,6BAChB,sNAAC;4BAAI,WAAU;;8CACb,sNAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,sNAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;mCAInC,cAAc,0BAChB,sNAAC;4BAAI,WAAU;;8CACb,sNAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,sNAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;iDAKrC,sNAAC;4BAAI,WAAU;;8CACb,sNAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,sNAAC;oCAAI,WAAU;8CACZ,eAAe,GAAG,CAAC,CAAC,uBACnB,sNAAC,wKAAM;4CAEL,SAAS,mBAAmB,SAAS,YAAY;4CACjD,SAAS;gDACP,kBAAkB;gDAClB,eAAe;4CACjB;4CACA,UAAU;4CACV,WAAW,mBAAmB,SAC1B,kCACA;sDAGH;2CAZI;;;;;;;;;;8CAkBX,sNAAC;oCAAK,UAAU;oCAA0B,WAAU;;sDAClD,sNAAC;4CAAI,WAAU;;8DACb,sNAAC,4OAAM;oDAAC,WAAU;;;;;;8DAClB,sNAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oDAC/C,WAAU;oDACV,UAAU;;;;;;;;;;;;sDAGd,sNAAC,wKAAM;4CACL,MAAK;4CACL,UAAU,eAAe,CAAC,aAAa,IAAI;4CAC3C,WAAU;sDACX;;;;;;;;;;;;8CAKH,sNAAC,wKAAM;oCACL,MAAK;oCACL,SAAS,IAAM,eAAe;oCAC9B,UAAU;oCACV,WAAU;8CAET,4BACC;;0DACE,sNAAC,wPAAO;gDAAC,WAAU;;;;;;4CAA8B;4CACtC;4CAAe;;qEAG5B;;0DACE,sNAAC,mOAAG;gDAAC,WAAU;;;;;;4CAAiB;4CACX;;;;;;;;;;;;;;;;;;;;;;;;;YAUlC,cAAc,gCACb,sNAAC;gBAAQ,WAAU;0BACjB,cAAA,sNAAC;oBAAI,WAAU;8BACb,cAAA,sNAAC,0LAAkB;wBAAC,aAAa;wBAAQ,aAAa;;;;;;;;;;;;;;;;YAM3D,cAAc,8BACb,sNAAC;gBAAQ,WAAU;0BACjB,cAAA,sNAAC;oBAAI,WAAU;8BACb,cAAA,sNAAC,oLAAe;wBAAC,UAAU;wBAAM,aAAa;;;;;;;;;;;;;;;;YAMnD,cAAc,2BACb,sNAAC;gBAAQ,WAAU;0BACjB,cAAA,sNAAC;oBAAI,WAAU;8BACb,cAAA,sNAAC,8KAAY;wBAAC,UAAU;;;;;;;;;;;;;;;;YAM7B,cAAc,2BACb,sNAAC;gBAAQ,WAAU;0BACjB,cAAA,sNAAC;oBAAI,WAAU;;sCACb,sNAAC;4BAAI,WAAU;;8CACb,sNAAC;oCAAG,WAAU;8CAAqC;;;;;;8CACnD,sNAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;;sCAIrC,sNAAC;4BAAI,WAAU;;8CACb,sNAAC;oCAAG,WAAU;8CAAe;;;;;;8CAC7B,sNAAC;8CAAE;;;;;;8CACH,sNAAC;oCAAE,WAAU;8CAAO;;;;;;8CACpB,sNAAC;oCAAE,WAAU;;wCAAO;sDAA2B,sNAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAO5D,cAAc,sBACb,sNAAC;gBAAQ,WAAU;0BACjB,cAAA,sNAAC;oBAAI,WAAU;;sCACb,sNAAC;4BAAI,WAAU;;8CACb,sNAAC;oCAAG,WAAU;8CAAqC;;;;;;8CACnD,sNAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;;sCAIrC,sNAAC,4KAAe;;;;;;;;;;;;;;;;YAMrB,uBACC,sNAAC;gBAAQ,WAAU;0BACjB,cAAA,sNAAC;oBAAI,WAAU;8BACb,cAAA,sNAAC,oKAAI;wBAAC,WAAU;kCACd,cAAA,sNAAC,2KAAW;4BAAC,WAAU;sCACrB,cAAA,sNAAC;gCAAE,WAAU;0CAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;YAQlD,cAAc,gBAAgB,CAAC,aAAa,QAAQ,mBACnD,sNAAC;gBAAQ,WAAU;0BACjB,cAAA,sNAAC;oBAAI,WAAU;;sCACb,sNAAC;4BAAI,WAAU;;gCAGZ,2BACC,sNAAC,oKAAI;oCAAC,WAAU;;sDACd,sNAAC,0KAAU;sDACT,cAAA,sNAAC,yKAAS;gDAAC,WAAU;;kEACnB,sNAAC,2PAAS;wDAAC,WAAU;;;;;;oDACpB,UAAU,MAAM;oDAAC;;;;;;;;;;;;sDAGtB,sNAAC,2KAAW;sDACV,cAAA,sNAAC;gDAAI,WAAU;;kEACb,sNAAC;wDAAI,WAAU;;0EACb,sNAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,sNAAC;gEAAK,WAAU;0EAA4B,IAAA,iKAAc,EAAC,UAAU,KAAK;;;;;;;;;;;;kEAE5E,sNAAC;wDAAI,WAAU;;0EACb,sNAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,sNAAC;gEAAK,WAAW,UAAU,MAAM,IAAI,IAAI,mBAAmB;;oEACzD,IAAA,iKAAc,EAAC,UAAU,MAAM;oEAAE;oEAAG,IAAA,mKAAgB,EAAC,UAAU,aAAa;oEAAE;;;;;;;;;;;;;kEAGnF,sNAAC;wDAAI,WAAU;;0EACb,sNAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,sNAAC;gEAAK,WAAU;0EAAc,UAAU,MAAM,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAQtE,0BACC,sNAAC,oKAAI;oCAAC,WAAU;;sDACd,sNAAC,0KAAU;sDACT,cAAA,sNAAC,yKAAS;gDAAC,WAAU;;kEACnB,sNAAC,4OAAM;wDAAC,WAAU;;;;;;oDAAgC;;;;;;;;;;;;sDAItD,sNAAC,2KAAW;sDACV,cAAA,sNAAC;gDAAI,WAAU;;kEACb,sNAAC;wDAAI,WAAU;;0EACb,sNAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,sNAAC;gEAAK,WAAU;0EAA4B,IAAA,iKAAc,EAAC,SAAS,UAAU;;;;;;;;;;;;kEAEhF,sNAAC;wDAAI,WAAU;;0EACb,sNAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,sNAAC;gEAAK,WAAU;0EAAgB,IAAA,iKAAc,EAAC,SAAS,QAAQ;;;;;;;;;;;;kEAElE,sNAAC;wDAAI,WAAU;;0EACb,sNAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,sNAAC;gEAAK,WAAU;0EAAkB,IAAA,iKAAc,EAAC,SAAS,UAAU;;;;;;;;;;;;kEAEtE,sNAAC;wDAAI,WAAU;;0EACb,sNAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,sNAAC;gEAAK,WAAU;;oEAA+B,SAAS,eAAe,CAAC,OAAO,CAAC;oEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAQ5F,0BACC,sNAAC,oKAAI;oCAAC,WAAU;;sDACd,sNAAC,0KAAU;sDACT,cAAA,sNAAC,yKAAS;gDAAC,WAAU;;kEACnB,sNAAC,yOAAK;wDAAC,WAAU;;;;;;oDAAiC;;;;;;;;;;;;sDAItD,sNAAC,2KAAW;sDACV,cAAA,sNAAC;gDAAI,WAAU;;kEACb,sNAAC;wDAAI,WAAU;;0EACb,sNAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,sNAAC;gEAAK,WAAW,AAAC,iBAGjB,OAFC,SAAS,KAAK,KAAK,YAAY,mBAC/B,SAAS,KAAK,KAAK,YAAY,iBAAiB;0EAE/C,SAAS,KAAK;;;;;;;;;;;;kEAGnB,sNAAC;wDAAI,WAAU;;0EACb,sNAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,sNAAC;gEAAK,WAAU;;oEAA4B,SAAS,UAAU,CAAC,OAAO,CAAC;oEAAG;;;;;;;;;;;;;kEAE7E,sNAAC;wDAAI,WAAU;;0EACb,sNAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,sNAAC;gEAAK,WAAW,AAAC,iBAGjB,OAFC,SAAS,cAAc,CAAC,QAAQ,CAAC,SAAS,mBAC1C,SAAS,cAAc,CAAC,QAAQ,CAAC,UAAU,iBAAiB;0EAE3D,SAAS,cAAc,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAUnD,0BACC,sNAAC,oKAAI;4BAAC,WAAU;;8CACd,sNAAC,0KAAU;8CACT,cAAA,sNAAC,yKAAS;wCAAC,WAAU;;0DACnB,sNAAC,4PAAU;gDAAC,WAAU;;;;;;4CAAiC;;;;;;;;;;;;8CAI3D,sNAAC,2KAAW;8CACV,cAAA,sNAAC;wCAAI,WAAU;kDACZ,SAAS,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,sBACnC,sNAAC;gDAAgB,WAAU;;kEACzB,sNAAC;wDAAI,WAAU;;0EACb,sNAAC;gEAAG,WAAU;0EAA0B,UAAU,IAAI;;;;;;0EACtD,sNAAC;gEAAK,WAAW,AAAC,2CAIjB,OAHC,UAAU,MAAM,KAAK,QAAQ,mCAC7B,UAAU,MAAM,KAAK,SAAS,+BAC9B;0EAEC,UAAU,MAAM;;;;;;;;;;;;kEAGrB,sNAAC;wDAAE,WAAU;kEAA0B,UAAU,WAAW;;;;;;;+CAXpD;;;;;;;;;;;;;;;;;;;;;wBAoBnB,YAAY,CAAC,SAAS,aAAa,CAAC,MAAM,GAAG,KAAK,SAAS,gBAAgB,CAAC,MAAM,GAAG,CAAC,mBACrF,sNAAC;4BAAI,WAAU;;gCACZ,SAAS,aAAa,CAAC,MAAM,GAAG,mBAC/B,sNAAC,oKAAI;oCAAC,WAAU;;sDACd,sNAAC,0KAAU;sDACT,cAAA,sNAAC,yKAAS;gDAAC,WAAU;;kEACnB,sNAAC,4OAAM;wDAAC,WAAU;;;;;;oDAAgC;;;;;;;;;;;;sDAItD,sNAAC,2KAAW;sDACV,cAAA,sNAAC;gDAAI,WAAU;0DACZ,SAAS,aAAa,CAAC,GAAG,CAAC,CAAC,OAAO,sBAClC,sNAAC;wDAAgB,WAAU;;0EACzB,sNAAC;gEAAK,WAAU;;oEAAiB;oEAAS,QAAQ;oEAAE;;;;;;;0EACpD,sNAAC;gEAAK,WAAU;0EAAgC,IAAA,iKAAc,EAAC;;;;;;;uDAFvD;;;;;;;;;;;;;;;;;;;;;gCAUnB,SAAS,gBAAgB,CAAC,MAAM,GAAG,mBAClC,sNAAC,oKAAI;oCAAC,WAAU;;sDACd,sNAAC,0KAAU;sDACT,cAAA,sNAAC,yKAAS;gDAAC,WAAU;;kEACnB,sNAAC,4OAAM;wDAAC,WAAU;;;;;;oDAA8B;;;;;;;;;;;;sDAIpD,sNAAC,2KAAW;sDACV,cAAA,sNAAC;gDAAI,WAAU;0DACZ,SAAS,gBAAgB,CAAC,GAAG,CAAC,CAAC,OAAO,sBACrC,sNAAC;wDAAgB,WAAU;;0EACzB,sNAAC;gEAAK,WAAU;;oEAAiB;oEAAY,QAAQ;oEAAE;;;;;;;0EACvD,sNAAC;gEAAK,WAAU;0EAA8B,IAAA,iKAAc,EAAC;;;;;;;uDAFrD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBpC;GAphBwB;KAAA", "debugId": null}}]}