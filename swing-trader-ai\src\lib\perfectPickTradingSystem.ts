import { 
  PerfectPickSetup, 
  PreMarketGapScan, 
  TechnicalGateAnalysis, 
  IntradayEntryTrigger,
  Catalyst,
  CandlestickData
} from '@/types/trading'
import { PreMarketGapScanner } from './preMarketGapScanner'
import { TechnicalGateAnalysis as TechnicalGateAnalyzer } from './technicalGateAnalysis'
import { CatalystDetectionEngine } from './catalystDetection'
import { PolygonAPI } from './polygon'

export class PerfectPickTradingSystem {
  private gapScanner: PreMarketGapScanner
  private technicalAnalyzer: TechnicalGateAnalyzer
  private catalystEngine: CatalystDetectionEngine
  private polygonAPI: PolygonAPI

  constructor(polygonApiKey?: string, useIBKR: boolean = true) {
    this.gapScanner = new PreMarketGapScanner(polygonApiKey, useIBKR)
    this.technicalAnalyzer = new TechnicalGateAnalyzer(polygonApiKey)
    this.catalystEngine = new CatalystDetectionEngine(polygonApiKey)
    this.polygonAPI = new PolygonAPI(polygonApiKey)
  }

  /**
   * Run complete Perfect-Pick analysis pipeline
   */
  async runPerfectPickScan(
    accountSize: number = 100000,
    riskPercent: number = 2,
    customUniverse?: string[]
  ): Promise<PerfectPickSetup[]> {
    console.log('🎯 Starting Perfect-Pick Trading System scan...')

    try {
      // Step 1: Pre-Market Gap Scan
      console.log('📊 Running pre-market gap scan...')
      const gapResults = await this.gapScanner.runGapScan(customUniverse)
      
      // Filter for basic criteria first
      const qualifiedGaps = gapResults.filter(gap => 
        gap.gapPercent >= 3.0 && 
        gap.gapPercent <= 15.0 && // Exclude over-extended gaps
        gap.marketCap >= ********* &&
        gap.price > 1.0
      )

      console.log(`✅ Found ${qualifiedGaps.length} qualified gap candidates`)

      if (qualifiedGaps.length === 0) {
        return []
      }

      // Step 2: Technical Gate Analysis
      console.log('🔍 Running technical gate analysis...')
      const symbols = qualifiedGaps.map(gap => gap.symbol)
      const technicalAnalyses = await this.technicalAnalyzer.batchAnalyzeTechnicalGates(symbols)
      
      // Filter for passing technical gates (Grade B or better)
      const passingTechnical = technicalAnalyses.filter(analysis => 
        ['A', 'B'].includes(analysis.overallGrade) &&
        analysis.aboveSMA200 &&
        analysis.aboveEMA8
      )

      console.log(`✅ ${passingTechnical.length} stocks passed technical gate`)

      // Step 3: Combine and create Perfect-Pick setups
      const perfectPickSetups: PerfectPickSetup[] = []

      for (const gapResult of qualifiedGaps) {
        const technicalAnalysis = passingTechnical.find(t => t.symbol === gapResult.symbol)
        if (!technicalAnalysis) continue

        const catalyst = gapResult.catalyst
        if (!catalyst || !this.isValidCatalyst(catalyst)) continue

        // Create Perfect-Pick setup
        const setup = await this.createPerfectPickSetup(
          gapResult,
          technicalAnalysis,
          catalyst,
          accountSize,
          riskPercent
        )

        if (setup && this.validatePerfectPickSetup(setup)) {
          perfectPickSetups.push(setup)
        }
      }

      // Sort by overall score
      perfectPickSetups.sort((a, b) => b.overallScore - a.overallScore)

      console.log(`🎯 Generated ${perfectPickSetups.length} Perfect-Pick setups`)
      
      return perfectPickSetups
    } catch (error) {
      console.error('Error running Perfect-Pick scan:', error)
      return []
    }
  }

  /**
   * Create a complete Perfect-Pick setup
   */
  private async createPerfectPickSetup(
    gapScan: PreMarketGapScan,
    technicalGate: TechnicalGateAnalysis,
    catalyst: Catalyst,
    accountSize: number,
    riskPercent: number
  ): Promise<PerfectPickSetup | null> {
    try {
      const symbol = gapScan.symbol
      const currentPrice = gapScan.price
      
      // Calculate risk management
      const preMarketLow = gapScan.preMarketLow
      const stopLoss = preMarketLow * 0.99 // Slightly below PML for safety
      const riskPerShare = currentPrice - stopLoss
      
      if (riskPerShare <= 0) {
        return null // Invalid risk setup
      }

      const maxRiskAmount = accountSize * (riskPercent / 100)
      const positionSize = Math.floor(maxRiskAmount / riskPerShare)
      const maxPositionValue = accountSize * 0.05 // 5% max position size
      const maxShares = Math.floor(maxPositionValue / currentPrice)
      
      const finalPositionSize = Math.min(positionSize, maxShares)
      const actualRiskAmount = finalPositionSize * riskPerShare

      // Calculate reward targets (minimum 3:1 R/R required)
      const target3R = currentPrice + (riskPerShare * 3)
      const target4R = currentPrice + (riskPerShare * 4)
      const target5R = currentPrice + (riskPerShare * 5)
      const riskRewardRatio = 3 // Minimum required

      // Check for exclusion reasons
      const exclusionReasons = this.checkExclusionCriteria(gapScan, technicalGate, catalyst)

      // Validation checks
      const validationChecks = {
        hasValidCatalyst: this.isValidCatalyst(catalyst),
        meetsGapCriteria: gapScan.meetsAllCriteria,
        passesTechnicalGate: ['A', 'B'].includes(technicalGate.overallGrade),
        hasEntryTrigger: true, // Will be determined intraday
        meetsRiskReward: riskRewardRatio >= 3,
        noExclusionFlags: exclusionReasons.length === 0
      }

      // Calculate overall score
      const overallScore = this.calculateOverallScore(gapScan, technicalGate, catalyst, validationChecks)
      const setupGrade = this.calculateSetupGrade(overallScore)

      const setup: PerfectPickSetup = {
        symbol,
        name: gapScan.name,
        catalyst,
        gapScan,
        technicalGate,
        riskManagement: {
          entryPrice: currentPrice,
          stopLoss,
          stopLossType: 'pre_market_low',
          riskPerShare,
          positionSize: finalPositionSize,
          accountRiskPercent: riskPercent,
          maxPositionPercent: 5
        },
        rewardPlanning: {
          riskRewardRatio,
          target3R,
          target4R,
          target5R,
          scaleOutPlan: [
            { level: 3, percentage: 25 }, // Take 25% at 3R
            { level: 4, percentage: 25 }, // Take 25% at 4R
            { level: 5, percentage: 25 }  // Take 25% at 5R, hold 25% for trend
          ]
        },
        overallScore,
        setupGrade,
        exclusionReasons,
        validationChecks,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      return setup
    } catch (error) {
      console.error(`Error creating Perfect-Pick setup for ${gapScan.symbol}:`, error)
      return null
    }
  }

  /**
   * Generate intraday entry trigger
   */
  async generateEntryTrigger(symbol: string, preMarketHigh: number): Promise<IntradayEntryTrigger | null> {
    try {
      // Get current intraday data
      const currentQuote = await this.fmpAPI.getStockQuote(symbol)
      const currentPrice = currentQuote.price
      
      // Calculate VWAP (simplified - would need intraday data for accurate VWAP)
      const vwap = currentPrice * 0.995 // Approximation
      
      // Determine entry signal type
      let entrySignalType: 'pmh_break' | 'vwap_pullback' | 'first_candle_close'
      let urgency: 'immediate' | 'wait_for_pullback' | 'breakout_confirmation'
      let conditions: string[] = []

      if (currentPrice > preMarketHigh) {
        entrySignalType = 'pmh_break'
        urgency = 'immediate'
        conditions.push('Clean break above pre-market high')
      } else if (currentPrice <= vwap && currentPrice > vwap * 0.98) {
        entrySignalType = 'vwap_pullback'
        urgency = 'wait_for_pullback'
        conditions.push('Pullback to VWAP support')
      } else {
        entrySignalType = 'first_candle_close'
        urgency = 'breakout_confirmation'
        conditions.push('Wait for first 5-min candle close above PMH')
      }

      // Check volume confirmation (simplified)
      const volumeConfirmation = currentQuote.volume > (currentQuote.volume || 0) * 1.5
      const vwapRising = true // Would need historical VWAP data to determine
      const noMajorResistance = true // Would need to check against resistance levels

      const trigger: IntradayEntryTrigger = {
        symbol,
        preMarketHigh,
        preMarketLow: preMarketHigh * 0.95, // Approximation
        vwap,
        entrySignalType,
        entryPrice: currentPrice,
        entryTime: new Date().toISOString(),
        volumeConfirmation,
        vwapRising,
        noMajorResistance,
        triggerValid: volumeConfirmation && vwapRising && noMajorResistance,
        urgency,
        conditions
      }

      return trigger
    } catch (error) {
      console.error(`Error generating entry trigger for ${symbol}:`, error)
      return null
    }
  }

  /**
   * Validate catalyst quality and tier
   */
  private isValidCatalyst(catalyst: Catalyst): boolean {
    // Tier 1 catalysts (highest priority)
    const tier1Types = [
      'earnings_beat_guidance',
      'fda_approval',
      'drug_trial_results',
      'contract_win',
      'partnership',
      'merger_acquisition'
    ]

    // Tier 2 catalysts (secondary)
    const tier2Types = [
      'analyst_upgrade',
      'stock_split',
      'sector_rotation'
    ]

    const isValidType = tier1Types.includes(catalyst.type) || tier2Types.includes(catalyst.type)
    const isFresh = catalyst.freshness === 'fresh' || catalyst.freshness === 'moderate'
    const hasQuality = catalyst.qualityScore >= 6
    const isVerified = catalyst.verified

    return isValidType && isFresh && hasQuality && isVerified
  }

  /**
   * Check for exclusion criteria
   */
  private checkExclusionCriteria(
    gapScan: PreMarketGapScan,
    technicalGate: TechnicalGateAnalysis,
    catalyst: Catalyst
  ): string[] {
    const exclusions: string[] = []

    // Anti-pattern filters
    if (!technicalGate.dailyTrendConfirmed) {
      exclusions.push('Stock not in confirmed daily uptrend')
    }

    if (!technicalGate.aboveSMA200) {
      exclusions.push('Stock below 200-day SMA')
    }

    if (gapScan.gapPercent > 15) {
      exclusions.push('Gap too extended (>15%)')
    }

    if (gapScan.averageDailyVolume < 500000) {
      exclusions.push('Low liquidity (avg daily volume <500K)')
    }

    if (catalyst.impact === 'bearish') {
      exclusions.push('Negative catalyst detected')
    }

    if (catalyst.freshness === 'stale') {
      exclusions.push('Catalyst is stale (>72 hours old)')
    }

    return exclusions
  }

  /**
   * Calculate overall setup score (0-100)
   */
  private calculateOverallScore(
    gapScan: PreMarketGapScan,
    technicalGate: TechnicalGateAnalysis,
    catalyst: Catalyst,
    validationChecks: any
  ): number {
    let score = 0

    // Gap quality (25 points max)
    score += Math.min(25, gapScan.gapPercent * 2) // 3% gap = 6 points, 10% gap = 20 points

    // Technical gate score (35 points max)
    score += (technicalGate.gateScore / 100) * 35

    // Catalyst quality (25 points max)
    score += (catalyst.qualityScore / 10) * 25

    // Validation bonus (15 points max)
    const validationCount = Object.values(validationChecks).filter(Boolean).length
    score += (validationCount / Object.keys(validationChecks).length) * 15

    return Math.min(100, Math.round(score))
  }

  /**
   * Calculate setup grade
   */
  private calculateSetupGrade(score: number): 'A+' | 'A' | 'B+' | 'B' | 'C+' | 'C' | 'D' | 'F' {
    if (score >= 95) return 'A+'
    if (score >= 90) return 'A'
    if (score >= 85) return 'B+'
    if (score >= 80) return 'B'
    if (score >= 75) return 'C+'
    if (score >= 70) return 'C'
    if (score >= 60) return 'D'
    return 'F'
  }

  /**
   * Validate complete Perfect-Pick setup
   */
  private validatePerfectPickSetup(setup: PerfectPickSetup): boolean {
    const checks = setup.validationChecks
    
    // Must pass all critical checks
    const criticalChecks = [
      checks.hasValidCatalyst,
      checks.meetsGapCriteria,
      checks.passesTechnicalGate,
      checks.meetsRiskReward,
      checks.noExclusionFlags
    ]

    return criticalChecks.every(check => check) && setup.overallScore >= 70
  }

  /**
   * Get setup summary statistics
   */
  getSetupSummary(setups: PerfectPickSetup[]) {
    const totalSetups = setups.length
    const gradeBreakdown = setups.reduce((acc, setup) => {
      acc[setup.setupGrade] = (acc[setup.setupGrade] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const catalystBreakdown = setups.reduce((acc, setup) => {
      acc[setup.catalyst.type] = (acc[setup.catalyst.type] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const avgScore = totalSetups > 0 
      ? setups.reduce((sum, setup) => sum + setup.overallScore, 0) / totalSetups 
      : 0

    const avgGap = totalSetups > 0
      ? setups.reduce((sum, setup) => sum + setup.gapScan.gapPercent, 0) / totalSetups
      : 0

    return {
      totalSetups,
      avgScore: Math.round(avgScore * 100) / 100,
      avgGap: Math.round(avgGap * 100) / 100,
      gradeBreakdown,
      catalystBreakdown,
      generatedAt: new Date().toISOString()
    }
  }

  /**
   * Update existing setups with current market data
   */
  async updatePerfectPickSetups(setups: PerfectPickSetup[]): Promise<PerfectPickSetup[]> {
    const updatedSetups: PerfectPickSetup[] = []

    for (const setup of setups) {
      try {
        // Get current quote
        const currentQuote = await this.fmpAPI.getStockQuote(setup.symbol)
        const currentPrice = currentQuote.price

        // Update entry trigger if needed
        const entryTrigger = await this.generateEntryTrigger(setup.symbol, setup.gapScan.preMarketHigh)

        // Update the setup
        const updatedSetup: PerfectPickSetup = {
          ...setup,
          entryTrigger,
          riskManagement: {
            ...setup.riskManagement,
            entryPrice: currentPrice
          },
          updatedAt: new Date().toISOString()
        }

        updatedSetups.push(updatedSetup)
      } catch (error) {
        console.error(`Error updating setup for ${setup.symbol}:`, error)
        updatedSetups.push(setup) // Keep original if update fails
      }
    }

    return updatedSetups
  }
}
