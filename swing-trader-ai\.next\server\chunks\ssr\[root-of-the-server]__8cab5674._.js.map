{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(value: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(value)\n}\n\nexport function formatPercentage(value: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'percent',\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2,\n  }).format(value / 100)\n}\n\nexport function calculateRiskReward(\n  entryPrice: number,\n  stopLoss: number,\n  takeProfit: number\n): number {\n  const risk = Math.abs(entryPrice - stopLoss)\n  const reward = Math.abs(takeProfit - entryPrice)\n  return reward / risk\n}\n\nexport function calculatePositionSize(\n  accountSize: number,\n  riskPercentage: number,\n  entryPrice: number,\n  stopLoss: number\n): number {\n  const riskAmount = accountSize * (riskPercentage / 100)\n  const riskPerShare = Math.abs(entryPrice - stopLoss)\n  return Math.floor(riskAmount / riskPerShare)\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,+LAAO,EAAC,IAAA,sKAAI,EAAC;AACtB;AAEO,SAAS,eAAe,KAAa;IAC1C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,iBAAiB,KAAa;IAC5C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC,QAAQ;AACpB;AAEO,SAAS,oBACd,UAAkB,EAClB,QAAgB,EAChB,UAAkB;IAElB,MAAM,OAAO,KAAK,GAAG,CAAC,aAAa;IACnC,MAAM,SAAS,KAAK,GAAG,CAAC,aAAa;IACrC,OAAO,SAAS;AAClB;AAEO,SAAS,sBACd,WAAmB,EACnB,cAAsB,EACtB,UAAkB,EAClB,QAAgB;IAEhB,MAAM,aAAa,cAAc,CAAC,iBAAiB,GAAG;IACtD,MAAM,eAAe,KAAK,GAAG,CAAC,aAAa;IAC3C,OAAO,KAAK,KAAK,CAAC,aAAa;AACjC", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,IAAA,gMAAG,EACxB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,4OAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,iMAAI,GAAG;IAC9B,qBACE,uQAAC;QACC,WAAW,IAAA,kJAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,4OAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uQAAC;QACC,KAAK;QACL,WAAW,IAAA,kJAAE,EACX,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,4OAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uQAAC;QACC,KAAK;QACL,WAAW,IAAA,kJAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,4OAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uQAAC;QACC,KAAK;QACL,WAAW,IAAA,kJAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,4OAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uQAAC;QACC,KAAK;QACL,WAAW,IAAA,kJAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,4OAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uQAAC;QAAI,KAAK;QAAK,WAAW,IAAA,kJAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,4OAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uQAAC;QACC,KAAK;QACL,WAAW,IAAA,kJAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,IAAA,gMAAG,EACvB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,uQAAC;QAAI,WAAW,IAAA,kJAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 243, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/components/SwingScanner.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Loader2, Search, TrendingUp, TrendingDown, Minus, Target, Shield, BarChart3 } from 'lucide-react'\nimport { ScanResult, ScanSummary } from '@/lib/swingScanner'\nimport { formatCurrency, formatPercentage } from '@/lib/utils'\n\ninterface ScannerProps {\n  autoScan?: boolean\n}\n\nexport function SwingScanner({ autoScan = false }: ScannerProps) {\n  const [isScanning, setIsScanning] = useState(false)\n  const [scanResults, setScanResults] = useState<ScanSummary | null>(null)\n  const [selectedScan, setSelectedScan] = useState<'quick' | 'full' | 'sector'>('quick')\n  const [selectedSector, setSelectedSector] = useState<string>('Technology')\n  const [error, setError] = useState<string | null>(null)\n\n  const sectors = [\n    'Technology', 'Financial Services', 'Healthcare', 'Industrial', \n    'Materials', 'Consumer', 'Communication Services', 'Energy'\n  ]\n\n  // Auto-scan on component mount if enabled\n  useEffect(() => {\n    if (autoScan) {\n      handleQuickScan()\n    }\n  }, [autoScan])\n\n  const handleQuickScan = async () => {\n    setIsScanning(true)\n    setError(null)\n    setScanResults(null)\n\n    try {\n      const response = await fetch('/api/scanner/quick?limit=15')\n      if (!response.ok) throw new Error('Failed to fetch scan results')\n      \n      const data = await response.json()\n      \n      // Convert to ScanSummary format\n      const summary: ScanSummary = {\n        totalScanned: data.totalScanned,\n        successfulScans: data.results.length,\n        failedScans: data.totalScanned - data.results.length,\n        topOpportunities: data.results,\n        sectorBreakdown: {},\n        scanDuration: 0\n      }\n      \n      setScanResults(summary)\n    } catch (err) {\n      setError('Failed to perform quick scan. Please try again.')\n      console.error('Quick scan error:', err)\n    } finally {\n      setIsScanning(false)\n    }\n  }\n\n  const handleFullScan = async () => {\n    setIsScanning(true)\n    setError(null)\n    setScanResults(null)\n\n    try {\n      const response = await fetch('/api/scanner/full?limit=25&concurrent=3')\n      if (!response.ok) throw new Error('Failed to fetch scan results')\n      \n      const data = await response.json()\n      setScanResults(data)\n    } catch (err) {\n      setError('Failed to perform full scan. Please try again.')\n      console.error('Full scan error:', err)\n    } finally {\n      setIsScanning(false)\n    }\n  }\n\n  const handleSectorScan = async () => {\n    setIsScanning(true)\n    setError(null)\n    setScanResults(null)\n\n    try {\n      const response = await fetch(`/api/scanner/sector/${encodeURIComponent(selectedSector)}?limit=15`)\n      if (!response.ok) throw new Error('Failed to fetch scan results')\n      \n      const data = await response.json()\n      setScanResults(data)\n    } catch (err) {\n      setError('Failed to perform sector scan. Please try again.')\n      console.error('Sector scan error:', err)\n    } finally {\n      setIsScanning(false)\n    }\n  }\n\n  const getTrendIcon = (trend: string) => {\n    switch (trend) {\n      case 'BULLISH':\n        return <TrendingUp className=\"h-4 w-4 text-green-400\" />\n      case 'BEARISH':\n        return <TrendingDown className=\"h-4 w-4 text-red-400\" />\n      default:\n        return <Minus className=\"h-4 w-4 text-yellow-400\" />\n    }\n  }\n\n  const getRecommendationColor = (recommendation: string) => {\n    if (recommendation.includes('BUY')) return 'bg-green-500/20 text-green-400'\n    if (recommendation.includes('SELL')) return 'bg-red-500/20 text-red-400'\n    return 'bg-yellow-500/20 text-yellow-400'\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Scanner Controls */}\n      <Card className=\"bg-slate-800/50 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"text-white flex items-center\">\n            <Search className=\"mr-2 h-5 w-5 text-blue-400\" />\n            Swing Trading Scanner\n          </CardTitle>\n          <CardDescription className=\"text-slate-300\">\n            Automatically scan stocks for the best swing trading opportunities\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"flex flex-wrap gap-4 mb-4\">\n            <Button\n              onClick={handleQuickScan}\n              disabled={isScanning}\n              className=\"bg-blue-600 hover:bg-blue-700\"\n            >\n              {isScanning && selectedScan === 'quick' ? (\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n              ) : null}\n              Quick Scan (Top 16)\n            </Button>\n            \n            <Button\n              onClick={handleFullScan}\n              disabled={isScanning}\n              variant=\"outline\"\n              className=\"border-slate-600 text-slate-300 hover:bg-slate-800\"\n            >\n              {isScanning && selectedScan === 'full' ? (\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n              ) : null}\n              Full Scan (All 70+ Stocks)\n            </Button>\n\n            <div className=\"flex gap-2\">\n              <select\n                value={selectedSector}\n                onChange={(e) => setSelectedSector(e.target.value)}\n                className=\"px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm\"\n                disabled={isScanning}\n              >\n                {sectors.map(sector => (\n                  <option key={sector} value={sector}>{sector}</option>\n                ))}\n              </select>\n              <Button\n                onClick={handleSectorScan}\n                disabled={isScanning}\n                variant=\"outline\"\n                className=\"border-slate-600 text-slate-300 hover:bg-slate-800\"\n              >\n                {isScanning && selectedScan === 'sector' ? (\n                  <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                ) : null}\n                Scan Sector\n              </Button>\n            </div>\n          </div>\n\n          {isScanning && (\n            <div className=\"text-center py-4\">\n              <Loader2 className=\"mx-auto h-8 w-8 animate-spin text-blue-400\" />\n              <p className=\"text-slate-300 mt-2\">\n                Scanning stocks for swing trading opportunities...\n              </p>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Error Display */}\n      {error && (\n        <Card className=\"bg-red-900/20 border-red-500/50\">\n          <CardContent className=\"p-6\">\n            <p className=\"text-red-300 text-center\">{error}</p>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Scan Results */}\n      {scanResults && (\n        <div className=\"space-y-6\">\n          {/* Scan Summary */}\n          <Card className=\"bg-slate-800/50 border-slate-700\">\n            <CardHeader>\n              <CardTitle className=\"text-white\">Scan Summary</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-white\">{scanResults.totalScanned}</div>\n                  <div className=\"text-sm text-slate-300\">Total Scanned</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-green-400\">{scanResults.successfulScans}</div>\n                  <div className=\"text-sm text-slate-300\">Successful</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-red-400\">{scanResults.failedScans}</div>\n                  <div className=\"text-sm text-slate-300\">Failed</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-blue-400\">\n                    {scanResults.scanDuration ? `${(scanResults.scanDuration / 1000).toFixed(1)}s` : 'N/A'}\n                  </div>\n                  <div className=\"text-sm text-slate-300\">Duration</div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Top Opportunities */}\n          <Card className=\"bg-slate-800/50 border-slate-700\">\n            <CardHeader>\n              <CardTitle className=\"text-white flex items-center\">\n                <TrendingUp className=\"mr-2 h-5 w-5 text-green-400\" />\n                Top Swing Trading Opportunities\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {scanResults.topOpportunities.map((result, index) => (\n                  <div key={result.symbol} className=\"p-4 bg-slate-700/50 rounded-lg\">\n                    <div className=\"flex items-center justify-between mb-3\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"text-lg font-bold text-white\">#{result.rank}</div>\n                        <div>\n                          <div className=\"flex items-center space-x-2\">\n                            <span className=\"text-lg font-semibold text-white\">{result.symbol}</span>\n                            {getTrendIcon(result.analysis.trend)}\n                            <Badge className={getRecommendationColor(result.analysis.recommendation)}>\n                              {result.analysis.recommendation.replace('_', ' ')}\n                            </Badge>\n                          </div>\n                          <div className=\"text-sm text-slate-300\">{result.name}</div>\n                        </div>\n                      </div>\n                      <div className=\"text-right\">\n                        <div className=\"text-xl font-bold text-white\">\n                          {result.score.toFixed(1)}/100\n                        </div>\n                        <div className=\"text-sm text-slate-300\">Score</div>\n                      </div>\n                    </div>\n\n                    <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\">\n                      <div>\n                        <div className=\"text-slate-300\">Price</div>\n                        <div className=\"text-white font-semibold\">\n                          {formatCurrency(result.quote.price)}\n                        </div>\n                        <div className={result.quote.change >= 0 ? \"text-green-400\" : \"text-red-400\"}>\n                          {formatPercentage(result.quote.changePercent)}\n                        </div>\n                      </div>\n                      <div>\n                        <div className=\"text-slate-300\">Entry</div>\n                        <div className=\"text-blue-400 font-semibold\">\n                          {formatCurrency(result.analysis.entryPrice)}\n                        </div>\n                      </div>\n                      <div>\n                        <div className=\"text-slate-300\">R/R Ratio</div>\n                        <div className=\"text-green-400 font-semibold\">\n                          {result.analysis.riskRewardRatio.toFixed(2)}:1\n                        </div>\n                      </div>\n                      <div>\n                        <div className=\"text-slate-300\">Confidence</div>\n                        <div className=\"text-white font-semibold\">\n                          {result.analysis.confidence.toFixed(1)}%\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AARA;;;;;;;;AAcO,SAAS,aAAa,EAAE,WAAW,KAAK,EAAgB;IAC7D,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,0OAAQ,EAAC;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,0OAAQ,EAAqB;IACnE,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,0OAAQ,EAA8B;IAC9E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,0OAAQ,EAAS;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,0OAAQ,EAAgB;IAElD,MAAM,UAAU;QACd;QAAc;QAAsB;QAAc;QAClD;QAAa;QAAY;QAA0B;KACpD;IAED,0CAA0C;IAC1C,IAAA,2OAAS,EAAC;QACR,IAAI,UAAU;YACZ;QACF;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,kBAAkB;QACtB,cAAc;QACd,SAAS;QACT,eAAe;QAEf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,gCAAgC;YAChC,MAAM,UAAuB;gBAC3B,cAAc,KAAK,YAAY;gBAC/B,iBAAiB,KAAK,OAAO,CAAC,MAAM;gBACpC,aAAa,KAAK,YAAY,GAAG,KAAK,OAAO,CAAC,MAAM;gBACpD,kBAAkB,KAAK,OAAO;gBAC9B,iBAAiB,CAAC;gBAClB,cAAc;YAChB;YAEA,eAAe;QACjB,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,qBAAqB;QACrC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,iBAAiB;QACrB,cAAc;QACd,SAAS;QACT,eAAe;QAEf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,eAAe;QACjB,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,oBAAoB;QACpC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,mBAAmB;QACvB,cAAc;QACd,SAAS;QACT,eAAe;QAEf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,mBAAmB,gBAAgB,SAAS,CAAC;YACjG,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,eAAe;QACjB,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,sBAAsB;QACtC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,uQAAC,yPAAU;oBAAC,WAAU;;;;;;YAC/B,KAAK;gBACH,qBAAO,uQAAC,+PAAY;oBAAC,WAAU;;;;;;YACjC;gBACE,qBAAO,uQAAC,sOAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,IAAI,eAAe,QAAQ,CAAC,QAAQ,OAAO;QAC3C,IAAI,eAAe,QAAQ,CAAC,SAAS,OAAO;QAC5C,OAAO;IACT;IAEA,qBACE,uQAAC;QAAI,WAAU;;0BAEb,uQAAC,iKAAI;gBAAC,WAAU;;kCACd,uQAAC,uKAAU;;0CACT,uQAAC,sKAAS;gCAAC,WAAU;;kDACnB,uQAAC,yOAAM;wCAAC,WAAU;;;;;;oCAA+B;;;;;;;0CAGnD,uQAAC,4KAAe;gCAAC,WAAU;0CAAiB;;;;;;;;;;;;kCAI9C,uQAAC,wKAAW;;0CACV,uQAAC;gCAAI,WAAU;;kDACb,uQAAC,qKAAM;wCACL,SAAS;wCACT,UAAU;wCACV,WAAU;;4CAET,cAAc,iBAAiB,wBAC9B,uQAAC,qPAAO;gDAAC,WAAU;;;;;uDACjB;4CAAK;;;;;;;kDAIX,uQAAC,qKAAM;wCACL,SAAS;wCACT,UAAU;wCACV,SAAQ;wCACR,WAAU;;4CAET,cAAc,iBAAiB,uBAC9B,uQAAC,qPAAO;gDAAC,WAAU;;;;;uDACjB;4CAAK;;;;;;;kDAIX,uQAAC;wCAAI,WAAU;;0DACb,uQAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gDACjD,WAAU;gDACV,UAAU;0DAET,QAAQ,GAAG,CAAC,CAAA,uBACX,uQAAC;wDAAoB,OAAO;kEAAS;uDAAxB;;;;;;;;;;0DAGjB,uQAAC,qKAAM;gDACL,SAAS;gDACT,UAAU;gDACV,SAAQ;gDACR,WAAU;;oDAET,cAAc,iBAAiB,yBAC9B,uQAAC,qPAAO;wDAAC,WAAU;;;;;+DACjB;oDAAK;;;;;;;;;;;;;;;;;;;4BAMd,4BACC,uQAAC;gCAAI,WAAU;;kDACb,uQAAC,qPAAO;wCAAC,WAAU;;;;;;kDACnB,uQAAC;wCAAE,WAAU;kDAAsB;;;;;;;;;;;;;;;;;;;;;;;;YAS1C,uBACC,uQAAC,iKAAI;gBAAC,WAAU;0BACd,cAAA,uQAAC,wKAAW;oBAAC,WAAU;8BACrB,cAAA,uQAAC;wBAAE,WAAU;kCAA4B;;;;;;;;;;;;;;;;YAM9C,6BACC,uQAAC;gBAAI,WAAU;;kCAEb,uQAAC,iKAAI;wBAAC,WAAU;;0CACd,uQAAC,uKAAU;0CACT,cAAA,uQAAC,sKAAS;oCAAC,WAAU;8CAAa;;;;;;;;;;;0CAEpC,uQAAC,wKAAW;0CACV,cAAA,uQAAC;oCAAI,WAAU;;sDACb,uQAAC;4CAAI,WAAU;;8DACb,uQAAC;oDAAI,WAAU;8DAAiC,YAAY,YAAY;;;;;;8DACxE,uQAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;sDAE1C,uQAAC;4CAAI,WAAU;;8DACb,uQAAC;oDAAI,WAAU;8DAAqC,YAAY,eAAe;;;;;;8DAC/E,uQAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;sDAE1C,uQAAC;4CAAI,WAAU;;8DACb,uQAAC;oDAAI,WAAU;8DAAmC,YAAY,WAAW;;;;;;8DACzE,uQAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;sDAE1C,uQAAC;4CAAI,WAAU;;8DACb,uQAAC;oDAAI,WAAU;8DACZ,YAAY,YAAY,GAAG,GAAG,CAAC,YAAY,YAAY,GAAG,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG;;;;;;8DAEnF,uQAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOhD,uQAAC,iKAAI;wBAAC,WAAU;;0CACd,uQAAC,uKAAU;0CACT,cAAA,uQAAC,sKAAS;oCAAC,WAAU;;sDACnB,uQAAC,yPAAU;4CAAC,WAAU;;;;;;wCAAgC;;;;;;;;;;;;0CAI1D,uQAAC,wKAAW;0CACV,cAAA,uQAAC;oCAAI,WAAU;8CACZ,YAAY,gBAAgB,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACzC,uQAAC;4CAAwB,WAAU;;8DACjC,uQAAC;oDAAI,WAAU;;sEACb,uQAAC;4DAAI,WAAU;;8EACb,uQAAC;oEAAI,WAAU;;wEAA+B;wEAAE,OAAO,IAAI;;;;;;;8EAC3D,uQAAC;;sFACC,uQAAC;4EAAI,WAAU;;8FACb,uQAAC;oFAAK,WAAU;8FAAoC,OAAO,MAAM;;;;;;gFAChE,aAAa,OAAO,QAAQ,CAAC,KAAK;8FACnC,uQAAC,mKAAK;oFAAC,WAAW,uBAAuB,OAAO,QAAQ,CAAC,cAAc;8FACpE,OAAO,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;sFAGjD,uQAAC;4EAAI,WAAU;sFAA0B,OAAO,IAAI;;;;;;;;;;;;;;;;;;sEAGxD,uQAAC;4DAAI,WAAU;;8EACb,uQAAC;oEAAI,WAAU;;wEACZ,OAAO,KAAK,CAAC,OAAO,CAAC;wEAAG;;;;;;;8EAE3B,uQAAC;oEAAI,WAAU;8EAAyB;;;;;;;;;;;;;;;;;;8DAI5C,uQAAC;oDAAI,WAAU;;sEACb,uQAAC;;8EACC,uQAAC;oEAAI,WAAU;8EAAiB;;;;;;8EAChC,uQAAC;oEAAI,WAAU;8EACZ,IAAA,8JAAc,EAAC,OAAO,KAAK,CAAC,KAAK;;;;;;8EAEpC,uQAAC;oEAAI,WAAW,OAAO,KAAK,CAAC,MAAM,IAAI,IAAI,mBAAmB;8EAC3D,IAAA,gKAAgB,EAAC,OAAO,KAAK,CAAC,aAAa;;;;;;;;;;;;sEAGhD,uQAAC;;8EACC,uQAAC;oEAAI,WAAU;8EAAiB;;;;;;8EAChC,uQAAC;oEAAI,WAAU;8EACZ,IAAA,8JAAc,EAAC,OAAO,QAAQ,CAAC,UAAU;;;;;;;;;;;;sEAG9C,uQAAC;;8EACC,uQAAC;oEAAI,WAAU;8EAAiB;;;;;;8EAChC,uQAAC;oEAAI,WAAU;;wEACZ,OAAO,QAAQ,CAAC,eAAe,CAAC,OAAO,CAAC;wEAAG;;;;;;;;;;;;;sEAGhD,uQAAC;;8EACC,uQAAC;oEAAI,WAAU;8EAAiB;;;;;;8EAChC,uQAAC;oEAAI,WAAU;;wEACZ,OAAO,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC;wEAAG;;;;;;;;;;;;;;;;;;;;2CAhDrC,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6DzC", "debugId": null}}, {"offset": {"line": 1004, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/components/TradingSetupCard.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport {\n  TrendingUp,\n  TrendingDown,\n  Target,\n  Shield,\n  Clock,\n  DollarSign,\n  AlertTriangle,\n  CheckCircle,\n  Play,\n  Eye,\n  Brain,\n  Loader2,\n  Sparkles\n} from 'lucide-react';\nimport { StrategySetup } from '@/lib/swingStrategies';\n\ninterface TradingSetupCardProps {\n  setup: StrategySetup & { symbol: string };\n  onExecuteTrade?: (setup: StrategySetup & { symbol: string }) => void;\n  onViewChart?: (symbol: string) => void;\n}\n\ninterface AIRiskAssessment {\n  riskScore: number;\n  riskFactors: string[];\n  recommendations: string[];\n  sentiment: 'bullish' | 'bearish' | 'neutral';\n}\n\nexport default function TradingSetupCard({ setup, onExecuteTrade, onViewChart }: TradingSetupCardProps) {\n  const [showDetails, setShowDetails] = useState(false);\n  const [aiRiskAssessment, setAiRiskAssessment] = useState<AIRiskAssessment | null>(null);\n  const [isLoadingAI, setIsLoadingAI] = useState(false);\n  const [aiEnabled, setAiEnabled] = useState(false);\n\n  // Check AI status and load risk assessment\n  useEffect(() => {\n    checkAIStatus();\n  }, []);\n\n  const checkAIStatus = async () => {\n    try {\n      const response = await fetch('/api/ai?action=status');\n      const status = await response.json();\n      setAiEnabled(status.enabled);\n\n      if (status.enabled) {\n        loadRiskAssessment();\n      }\n    } catch (error) {\n      console.error('Error checking AI status:', error);\n    }\n  };\n\n  const loadRiskAssessment = async () => {\n    setIsLoadingAI(true);\n    try {\n      const response = await fetch('/api/ai', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          action: 'risk-assessment',\n          data: { setup }\n        })\n      });\n\n      if (response.ok) {\n        const { riskAssessment } = await response.json();\n        setAiRiskAssessment(riskAssessment);\n      }\n    } catch (error) {\n      console.error('Error loading AI risk assessment:', error);\n    } finally {\n      setIsLoadingAI(false);\n    }\n  };\n\n  const getConfidenceColor = (confidence: number) => {\n    if (confidence >= 80) return 'bg-green-500/20 text-green-400';\n    if (confidence >= 60) return 'bg-yellow-500/20 text-yellow-400';\n    return 'bg-red-500/20 text-red-400';\n  };\n\n  const getStrategyName = (strategy: string) => {\n    return strategy === 'overnight_momentum' \n      ? 'Overnight Momentum' \n      : 'Technical Breakout';\n  };\n\n  const riskReward = ((setup.targets[0] - setup.entryPrice) / (setup.entryPrice - setup.stopLoss)).toFixed(1);\n\n  const getSentimentColor = (sentiment: string) => {\n    switch (sentiment) {\n      case 'bullish': return 'text-green-400';\n      case 'bearish': return 'text-red-400';\n      default: return 'text-yellow-400';\n    }\n  };\n\n  const getRiskScoreColor = (score: number) => {\n    if (score <= 3) return 'text-green-400';\n    if (score <= 6) return 'text-yellow-400';\n    return 'text-red-400';\n  };\n\n  return (\n    <Card className=\"bg-slate-800/50 border-slate-700\">\n      <CardHeader className=\"pb-3\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-3\">\n            <CardTitle className=\"text-xl text-white\">{setup.symbol}</CardTitle>\n            <Badge variant=\"outline\" className=\"text-blue-400 border-blue-400\">\n              {getStrategyName(setup.strategy)}\n            </Badge>\n            <Badge className={getConfidenceColor(setup.confidence)}>\n              {setup.confidence}% Confidence\n            </Badge>\n          </div>\n          <div className=\"flex gap-2\">\n            <Button\n              size=\"sm\"\n              variant=\"outline\"\n              onClick={() => onViewChart?.(setup.symbol)}\n              className=\"text-slate-300 hover:text-white\"\n            >\n              <Eye className=\"h-4 w-4 mr-1\" />\n              Chart\n            </Button>\n            <Button\n              size=\"sm\"\n              onClick={() => onExecuteTrade?.(setup)}\n              className=\"bg-green-600 hover:bg-green-700 text-white\"\n            >\n              <Play className=\"h-4 w-4 mr-1\" />\n              Execute Trade\n            </Button>\n          </div>\n        </div>\n      </CardHeader>\n\n      <CardContent className=\"space-y-4\">\n        {/* Key Metrics */}\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n          <div className=\"text-center p-3 bg-slate-700/30 rounded-lg\">\n            <div className=\"flex items-center justify-center mb-1\">\n              <TrendingUp className=\"h-4 w-4 text-green-400 mr-1\" />\n              <span className=\"text-xs text-slate-400\">Entry</span>\n            </div>\n            <div className=\"text-lg font-bold text-white\">${setup.preciseEntry.price.toFixed(2)}</div>\n            <div className=\"text-xs text-slate-400\">{setup.preciseEntry.orderType}</div>\n          </div>\n\n          <div className=\"text-center p-3 bg-slate-700/30 rounded-lg\">\n            <div className=\"flex items-center justify-center mb-1\">\n              <Shield className=\"h-4 w-4 text-red-400 mr-1\" />\n              <span className=\"text-xs text-slate-400\">Stop Loss</span>\n            </div>\n            <div className=\"text-lg font-bold text-white\">${setup.preciseExit.stopLoss.price.toFixed(2)}</div>\n            <div className=\"text-xs text-slate-400\">{setup.preciseExit.stopLoss.orderType}</div>\n          </div>\n\n          <div className=\"text-center p-3 bg-slate-700/30 rounded-lg\">\n            <div className=\"flex items-center justify-center mb-1\">\n              <Target className=\"h-4 w-4 text-blue-400 mr-1\" />\n              <span className=\"text-xs text-slate-400\">First Target</span>\n            </div>\n            <div className=\"text-lg font-bold text-white\">${setup.targets[0].toFixed(2)}</div>\n            <div className=\"text-xs text-slate-400\">R/R: {riskReward}:1</div>\n          </div>\n\n          <div className=\"text-center p-3 bg-slate-700/30 rounded-lg\">\n            <div className=\"flex items-center justify-center mb-1\">\n              <DollarSign className=\"h-4 w-4 text-yellow-400 mr-1\" />\n              <span className=\"text-xs text-slate-400\">Position Size</span>\n            </div>\n            <div className=\"text-lg font-bold text-white\">{setup.positionSize}</div>\n            <div className=\"text-xs text-slate-400\">shares</div>\n          </div>\n        </div>\n\n        {/* Entry Conditions */}\n        <div className=\"p-3 bg-slate-700/20 rounded-lg\">\n          <h4 className=\"text-sm font-semibold text-white mb-2 flex items-center\">\n            <CheckCircle className=\"h-4 w-4 text-green-400 mr-2\" />\n            Entry Conditions\n          </h4>\n          <div className=\"space-y-1\">\n            {setup.preciseEntry.conditions.map((condition, index) => (\n              <div key={index} className=\"text-xs text-slate-300 flex items-center\">\n                <div className=\"w-1 h-1 bg-green-400 rounded-full mr-2\"></div>\n                {condition}\n              </div>\n            ))}\n          </div>\n          <div className=\"mt-2 text-xs text-blue-400\">\n            <Clock className=\"h-3 w-3 inline mr-1\" />\n            {setup.preciseEntry.timing}\n          </div>\n        </div>\n\n        {/* Take Profit Levels */}\n        <div className=\"p-3 bg-slate-700/20 rounded-lg\">\n          <h4 className=\"text-sm font-semibold text-white mb-2 flex items-center\">\n            <Target className=\"h-4 w-4 text-blue-400 mr-2\" />\n            Take Profit Plan\n          </h4>\n          <div className=\"space-y-2\">\n            {setup.preciseExit.takeProfits.map((tp, index) => (\n              <div key={index} className=\"flex items-center justify-between text-xs\">\n                <div className=\"text-slate-300\">\n                  <span className=\"font-medium\">{tp.target}:</span> ${tp.price.toFixed(2)}\n                </div>\n                <div className=\"text-slate-400\">\n                  {tp.percentage}% of position\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Risk Management */}\n        <div className=\"p-3 bg-slate-700/20 rounded-lg\">\n          <h4 className=\"text-sm font-semibold text-white mb-2 flex items-center\">\n            <Shield className=\"h-4 w-4 text-red-400 mr-2\" />\n            Risk Management\n          </h4>\n          <div className=\"grid grid-cols-2 gap-4 text-xs\">\n            <div>\n              <span className=\"text-slate-400\">Max Risk:</span>\n              <span className=\"text-white ml-2\">${setup.riskManagement.maxRiskDollars.toFixed(0)}</span>\n            </div>\n            <div>\n              <span className=\"text-slate-400\">Account Risk:</span>\n              <span className=\"text-white ml-2\">{setup.riskManagement.accountRiskPercent}%</span>\n            </div>\n            <div>\n              <span className=\"text-slate-400\">Time Stop:</span>\n              <span className=\"text-white ml-2\">{setup.riskManagement.timeStopHours}h</span>\n            </div>\n            <div>\n              <span className=\"text-slate-400\">Max Drawdown:</span>\n              <span className=\"text-white ml-2\">{setup.riskManagement.maxDrawdownPercent}%</span>\n            </div>\n          </div>\n        </div>\n\n        {/* AI Risk Assessment */}\n        {aiEnabled && (\n          <div className=\"p-3 bg-gradient-to-r from-blue-900/20 to-purple-900/20 rounded-lg border border-blue-500/20\">\n            <h4 className=\"text-sm font-semibold text-white mb-2 flex items-center\">\n              <Brain className=\"h-4 w-4 text-blue-400 mr-2\" />\n              AI Risk Assessment\n              <Sparkles className=\"h-3 w-3 text-blue-400 ml-1\" />\n            </h4>\n\n            {isLoadingAI ? (\n              <div className=\"flex items-center justify-center py-4\">\n                <Loader2 className=\"h-4 w-4 animate-spin text-blue-400 mr-2\" />\n                <span className=\"text-xs text-slate-300\">Analyzing risk...</span>\n              </div>\n            ) : aiRiskAssessment ? (\n              <div className=\"space-y-3\">\n                {/* Risk Score and Sentiment */}\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center\">\n                    <span className=\"text-xs text-slate-400\">Risk Score:</span>\n                    <span className={`ml-2 text-sm font-bold ${getRiskScoreColor(aiRiskAssessment.riskScore)}`}>\n                      {aiRiskAssessment.riskScore}/10\n                    </span>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <span className=\"text-xs text-slate-400\">Sentiment:</span>\n                    <span className={`ml-2 text-sm font-semibold capitalize ${getSentimentColor(aiRiskAssessment.sentiment)}`}>\n                      {aiRiskAssessment.sentiment}\n                    </span>\n                  </div>\n                </div>\n\n                {/* Risk Factors */}\n                {aiRiskAssessment.riskFactors.length > 0 && (\n                  <div>\n                    <span className=\"text-xs text-slate-400 mb-1 block\">Key Risk Factors:</span>\n                    <div className=\"space-y-1\">\n                      {aiRiskAssessment.riskFactors.slice(0, 2).map((factor, index) => (\n                        <div key={index} className=\"text-xs text-slate-300 flex items-start\">\n                          <AlertTriangle className=\"h-3 w-3 text-yellow-400 mr-1 mt-0.5 flex-shrink-0\" />\n                          {factor}\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                )}\n\n                {/* AI Recommendations */}\n                {aiRiskAssessment.recommendations.length > 0 && (\n                  <div>\n                    <span className=\"text-xs text-slate-400 mb-1 block\">AI Recommendations:</span>\n                    <div className=\"space-y-1\">\n                      {aiRiskAssessment.recommendations.slice(0, 2).map((rec, index) => (\n                        <div key={index} className=\"text-xs text-slate-300 flex items-start\">\n                          <CheckCircle className=\"h-3 w-3 text-blue-400 mr-1 mt-0.5 flex-shrink-0\" />\n                          {rec}\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                )}\n              </div>\n            ) : (\n              <div className=\"text-xs text-slate-400 text-center py-2\">\n                AI risk assessment unavailable\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Toggle Details */}\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={() => setShowDetails(!showDetails)}\n          className=\"w-full text-slate-300 hover:text-white\"\n        >\n          {showDetails ? 'Hide' : 'Show'} Execution Details\n        </Button>\n\n        {/* Detailed Execution Plan */}\n        {showDetails && (\n          <div className=\"space-y-4 pt-4 border-t border-slate-700\">\n            {/* Entry Instructions */}\n            <div>\n              <h5 className=\"text-sm font-semibold text-green-400 mb-2\">Entry Instructions:</h5>\n              <div className=\"space-y-1\">\n                {setup.executionPlan.entryInstructions.map((instruction, index) => (\n                  <div key={index} className=\"text-xs text-slate-300 flex items-start\">\n                    <div className=\"w-4 h-4 rounded-full bg-green-500/20 text-green-400 flex items-center justify-center text-[10px] mr-2 mt-0.5 flex-shrink-0\">\n                      {index + 1}\n                    </div>\n                    {instruction}\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Exit Instructions */}\n            <div>\n              <h5 className=\"text-sm font-semibold text-red-400 mb-2\">Exit Instructions:</h5>\n              <div className=\"space-y-1\">\n                {setup.executionPlan.exitInstructions.map((instruction, index) => (\n                  <div key={index} className=\"text-xs text-slate-300 flex items-start\">\n                    <div className=\"w-4 h-4 rounded-full bg-red-500/20 text-red-400 flex items-center justify-center text-[10px] mr-2 mt-0.5 flex-shrink-0\">\n                      {index + 1}\n                    </div>\n                    {instruction}\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Monitoring Points */}\n            <div>\n              <h5 className=\"text-sm font-semibold text-blue-400 mb-2\">Monitor These:</h5>\n              <div className=\"space-y-1\">\n                {setup.executionPlan.monitoringPoints.map((point, index) => (\n                  <div key={index} className=\"text-xs text-slate-300 flex items-center\">\n                    <Eye className=\"h-3 w-3 text-blue-400 mr-2\" />\n                    {point}\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Contingency Plans */}\n            <div>\n              <h5 className=\"text-sm font-semibold text-yellow-400 mb-2\">Contingency Plans:</h5>\n              <div className=\"space-y-1\">\n                {setup.executionPlan.contingencyPlans.map((plan, index) => (\n                  <div key={index} className=\"text-xs text-slate-300 flex items-start\">\n                    <AlertTriangle className=\"h-3 w-3 text-yellow-400 mr-2 mt-0.5 flex-shrink-0\" />\n                    {plan}\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Invalidation */}\n            <div className=\"p-3 bg-red-500/10 border border-red-500/20 rounded-lg\">\n              <h5 className=\"text-sm font-semibold text-red-400 mb-1\">Setup Invalidation:</h5>\n              <div className=\"text-xs text-slate-300\">{setup.invalidation}</div>\n            </div>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAoCe,SAAS,iBAAiB,EAAE,KAAK,EAAE,cAAc,EAAE,WAAW,EAAyB;IACpG,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,0OAAQ,EAAC;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,0OAAQ,EAA0B;IAClF,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,0OAAQ,EAAC;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,0OAAQ,EAAC;IAE3C,2CAA2C;IAC3C,IAAA,2OAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,aAAa,OAAO,OAAO;YAE3B,IAAI,OAAO,OAAO,EAAE;gBAClB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,MAAM,qBAAqB;QACzB,eAAe;QACf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,WAAW;gBACtC,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,MAAM;wBAAE;oBAAM;gBAChB;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,EAAE,cAAc,EAAE,GAAG,MAAM,SAAS,IAAI;gBAC9C,oBAAoB;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,cAAc,IAAI,OAAO;QAC7B,IAAI,cAAc,IAAI,OAAO;QAC7B,OAAO;IACT;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAO,aAAa,uBAChB,uBACA;IACN;IAEA,MAAM,aAAa,CAAC,CAAC,MAAM,OAAO,CAAC,EAAE,GAAG,MAAM,UAAU,IAAI,CAAC,MAAM,UAAU,GAAG,MAAM,QAAQ,CAAC,EAAE,OAAO,CAAC;IAEzG,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,SAAS,GAAG,OAAO;QACvB,IAAI,SAAS,GAAG,OAAO;QACvB,OAAO;IACT;IAEA,qBACE,uQAAC,iKAAI;QAAC,WAAU;;0BACd,uQAAC,uKAAU;gBAAC,WAAU;0BACpB,cAAA,uQAAC;oBAAI,WAAU;;sCACb,uQAAC;4BAAI,WAAU;;8CACb,uQAAC,sKAAS;oCAAC,WAAU;8CAAsB,MAAM,MAAM;;;;;;8CACvD,uQAAC,mKAAK;oCAAC,SAAQ;oCAAU,WAAU;8CAChC,gBAAgB,MAAM,QAAQ;;;;;;8CAEjC,uQAAC,mKAAK;oCAAC,WAAW,mBAAmB,MAAM,UAAU;;wCAClD,MAAM,UAAU;wCAAC;;;;;;;;;;;;;sCAGtB,uQAAC;4BAAI,WAAU;;8CACb,uQAAC,qKAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS,IAAM,cAAc,MAAM,MAAM;oCACzC,WAAU;;sDAEV,uQAAC,gOAAG;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGlC,uQAAC,qKAAM;oCACL,MAAK;oCACL,SAAS,IAAM,iBAAiB;oCAChC,WAAU;;sDAEV,uQAAC,mOAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAOzC,uQAAC,wKAAW;gBAAC,WAAU;;kCAErB,uQAAC;wBAAI,WAAU;;0CACb,uQAAC;gCAAI,WAAU;;kDACb,uQAAC;wCAAI,WAAU;;0DACb,uQAAC,yPAAU;gDAAC,WAAU;;;;;;0DACtB,uQAAC;gDAAK,WAAU;0DAAyB;;;;;;;;;;;;kDAE3C,uQAAC;wCAAI,WAAU;;4CAA+B;4CAAE,MAAM,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC;;;;;;;kDACjF,uQAAC;wCAAI,WAAU;kDAA0B,MAAM,YAAY,CAAC,SAAS;;;;;;;;;;;;0CAGvE,uQAAC;gCAAI,WAAU;;kDACb,uQAAC;wCAAI,WAAU;;0DACb,uQAAC,yOAAM;gDAAC,WAAU;;;;;;0DAClB,uQAAC;gDAAK,WAAU;0DAAyB;;;;;;;;;;;;kDAE3C,uQAAC;wCAAI,WAAU;;4CAA+B;4CAAE,MAAM,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC;;;;;;;kDACzF,uQAAC;wCAAI,WAAU;kDAA0B,MAAM,WAAW,CAAC,QAAQ,CAAC,SAAS;;;;;;;;;;;;0CAG/E,uQAAC;gCAAI,WAAU;;kDACb,uQAAC;wCAAI,WAAU;;0DACb,uQAAC,yOAAM;gDAAC,WAAU;;;;;;0DAClB,uQAAC;gDAAK,WAAU;0DAAyB;;;;;;;;;;;;kDAE3C,uQAAC;wCAAI,WAAU;;4CAA+B;4CAAE,MAAM,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC;;;;;;;kDACzE,uQAAC;wCAAI,WAAU;;4CAAyB;4CAAM;4CAAW;;;;;;;;;;;;;0CAG3D,uQAAC;gCAAI,WAAU;;kDACb,uQAAC;wCAAI,WAAU;;0DACb,uQAAC,yPAAU;gDAAC,WAAU;;;;;;0DACtB,uQAAC;gDAAK,WAAU;0DAAyB;;;;;;;;;;;;kDAE3C,uQAAC;wCAAI,WAAU;kDAAgC,MAAM,YAAY;;;;;;kDACjE,uQAAC;wCAAI,WAAU;kDAAyB;;;;;;;;;;;;;;;;;;kCAK5C,uQAAC;wBAAI,WAAU;;0CACb,uQAAC;gCAAG,WAAU;;kDACZ,uQAAC,mQAAW;wCAAC,WAAU;;;;;;oCAAgC;;;;;;;0CAGzD,uQAAC;gCAAI,WAAU;0CACZ,MAAM,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,sBAC7C,uQAAC;wCAAgB,WAAU;;0DACzB,uQAAC;gDAAI,WAAU;;;;;;4CACd;;uCAFO;;;;;;;;;;0CAMd,uQAAC;gCAAI,WAAU;;kDACb,uQAAC,sOAAK;wCAAC,WAAU;;;;;;oCAChB,MAAM,YAAY,CAAC,MAAM;;;;;;;;;;;;;kCAK9B,uQAAC;wBAAI,WAAU;;0CACb,uQAAC;gCAAG,WAAU;;kDACZ,uQAAC,yOAAM;wCAAC,WAAU;;;;;;oCAA+B;;;;;;;0CAGnD,uQAAC;gCAAI,WAAU;0CACZ,MAAM,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI,sBACtC,uQAAC;wCAAgB,WAAU;;0DACzB,uQAAC;gDAAI,WAAU;;kEACb,uQAAC;wDAAK,WAAU;;4DAAe,GAAG,MAAM;4DAAC;;;;;;;oDAAQ;oDAAG,GAAG,KAAK,CAAC,OAAO,CAAC;;;;;;;0DAEvE,uQAAC;gDAAI,WAAU;;oDACZ,GAAG,UAAU;oDAAC;;;;;;;;uCALT;;;;;;;;;;;;;;;;kCAahB,uQAAC;wBAAI,WAAU;;0CACb,uQAAC;gCAAG,WAAU;;kDACZ,uQAAC,yOAAM;wCAAC,WAAU;;;;;;oCAA8B;;;;;;;0CAGlD,uQAAC;gCAAI,WAAU;;kDACb,uQAAC;;0DACC,uQAAC;gDAAK,WAAU;0DAAiB;;;;;;0DACjC,uQAAC;gDAAK,WAAU;;oDAAkB;oDAAE,MAAM,cAAc,CAAC,cAAc,CAAC,OAAO,CAAC;;;;;;;;;;;;;kDAElF,uQAAC;;0DACC,uQAAC;gDAAK,WAAU;0DAAiB;;;;;;0DACjC,uQAAC;gDAAK,WAAU;;oDAAmB,MAAM,cAAc,CAAC,kBAAkB;oDAAC;;;;;;;;;;;;;kDAE7E,uQAAC;;0DACC,uQAAC;gDAAK,WAAU;0DAAiB;;;;;;0DACjC,uQAAC;gDAAK,WAAU;;oDAAmB,MAAM,cAAc,CAAC,aAAa;oDAAC;;;;;;;;;;;;;kDAExE,uQAAC;;0DACC,uQAAC;gDAAK,WAAU;0DAAiB;;;;;;0DACjC,uQAAC;gDAAK,WAAU;;oDAAmB,MAAM,cAAc,CAAC,kBAAkB;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;;oBAMhF,2BACC,uQAAC;wBAAI,WAAU;;0CACb,uQAAC;gCAAG,WAAU;;kDACZ,uQAAC,sOAAK;wCAAC,WAAU;;;;;;oCAA+B;kDAEhD,uQAAC,+OAAQ;wCAAC,WAAU;;;;;;;;;;;;4BAGrB,4BACC,uQAAC;gCAAI,WAAU;;kDACb,uQAAC,qPAAO;wCAAC,WAAU;;;;;;kDACnB,uQAAC;wCAAK,WAAU;kDAAyB;;;;;;;;;;;uCAEzC,iCACF,uQAAC;gCAAI,WAAU;;kDAEb,uQAAC;wCAAI,WAAU;;0DACb,uQAAC;gDAAI,WAAU;;kEACb,uQAAC;wDAAK,WAAU;kEAAyB;;;;;;kEACzC,uQAAC;wDAAK,WAAW,CAAC,uBAAuB,EAAE,kBAAkB,iBAAiB,SAAS,GAAG;;4DACvF,iBAAiB,SAAS;4DAAC;;;;;;;;;;;;;0DAGhC,uQAAC;gDAAI,WAAU;;kEACb,uQAAC;wDAAK,WAAU;kEAAyB;;;;;;kEACzC,uQAAC;wDAAK,WAAW,CAAC,sCAAsC,EAAE,kBAAkB,iBAAiB,SAAS,GAAG;kEACtG,iBAAiB,SAAS;;;;;;;;;;;;;;;;;;oCAMhC,iBAAiB,WAAW,CAAC,MAAM,GAAG,mBACrC,uQAAC;;0DACC,uQAAC;gDAAK,WAAU;0DAAoC;;;;;;0DACpD,uQAAC;gDAAI,WAAU;0DACZ,iBAAiB,WAAW,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,sBACrD,uQAAC;wDAAgB,WAAU;;0EACzB,uQAAC,kQAAa;gEAAC,WAAU;;;;;;4DACxB;;uDAFO;;;;;;;;;;;;;;;;oCAUjB,iBAAiB,eAAe,CAAC,MAAM,GAAG,mBACzC,uQAAC;;0DACC,uQAAC;gDAAK,WAAU;0DAAoC;;;;;;0DACpD,uQAAC;gDAAI,WAAU;0DACZ,iBAAiB,eAAe,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBACtD,uQAAC;wDAAgB,WAAU;;0EACzB,uQAAC,mQAAW;gEAAC,WAAU;;;;;;4DACtB;;uDAFO;;;;;;;;;;;;;;;;;;;;;qDAUpB,uQAAC;gCAAI,WAAU;0CAA0C;;;;;;;;;;;;kCAQ/D,uQAAC,qKAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,eAAe,CAAC;wBAC/B,WAAU;;4BAET,cAAc,SAAS;4BAAO;;;;;;;oBAIhC,6BACC,uQAAC;wBAAI,WAAU;;0CAEb,uQAAC;;kDACC,uQAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,uQAAC;wCAAI,WAAU;kDACZ,MAAM,aAAa,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,aAAa,sBACvD,uQAAC;gDAAgB,WAAU;;kEACzB,uQAAC;wDAAI,WAAU;kEACZ,QAAQ;;;;;;oDAEV;;+CAJO;;;;;;;;;;;;;;;;0CAWhB,uQAAC;;kDACC,uQAAC;wCAAG,WAAU;kDAA0C;;;;;;kDACxD,uQAAC;wCAAI,WAAU;kDACZ,MAAM,aAAa,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,aAAa,sBACtD,uQAAC;gDAAgB,WAAU;;kEACzB,uQAAC;wDAAI,WAAU;kEACZ,QAAQ;;;;;;oDAEV;;+CAJO;;;;;;;;;;;;;;;;0CAWhB,uQAAC;;kDACC,uQAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,uQAAC;wCAAI,WAAU;kDACZ,MAAM,aAAa,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,OAAO,sBAChD,uQAAC;gDAAgB,WAAU;;kEACzB,uQAAC,gOAAG;wDAAC,WAAU;;;;;;oDACd;;+CAFO;;;;;;;;;;;;;;;;0CAShB,uQAAC;;kDACC,uQAAC;wCAAG,WAAU;kDAA6C;;;;;;kDAC3D,uQAAC;wCAAI,WAAU;kDACZ,MAAM,aAAa,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC/C,uQAAC;gDAAgB,WAAU;;kEACzB,uQAAC,kQAAa;wDAAC,WAAU;;;;;;oDACxB;;+CAFO;;;;;;;;;;;;;;;;0CAShB,uQAAC;gCAAI,WAAU;;kDACb,uQAAC;wCAAG,WAAU;kDAA0C;;;;;;kDACxD,uQAAC;wCAAI,WAAU;kDAA0B,MAAM,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzE", "debugId": null}}, {"offset": {"line": 2153, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/components/AIInsights.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { \n  Brain, \n  TrendingUp, \n  TrendingDown, \n  AlertTriangle, \n  Target, \n  Lightbulb,\n  Loader2,\n  Sparkles,\n  BarChart3\n} from 'lucide-react';\nimport { EnhancedScanResult } from '@/lib/enhancedSwingScanner';\n\ninterface AIInsightsProps {\n  scanResults: EnhancedScanResult[];\n  marketConditions: any;\n  onRefresh?: () => void;\n}\n\ninterface AIStatus {\n  enabled: boolean;\n  model: string | null;\n  features: {\n    marketCommentary: boolean;\n    riskAssessment: boolean;\n    tradingRecommendations: boolean;\n  };\n}\n\ninterface TradingRecommendations {\n  topPicks: string[];\n  avoidList: string[];\n  marketOutlook: string;\n  actionItems: string[];\n}\n\nexport default function AIInsights({ scanResults, marketConditions, onRefresh }: AIInsightsProps) {\n  const [aiStatus, setAiStatus] = useState<AIStatus | null>(null);\n  const [marketCommentary, setMarketCommentary] = useState<string>('');\n  const [recommendations, setRecommendations] = useState<TradingRecommendations | null>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  // Check AI status on component mount\n  useEffect(() => {\n    checkAIStatus();\n  }, []);\n\n  // Generate insights when scan results change\n  useEffect(() => {\n    if (scanResults.length > 0 && aiStatus?.enabled) {\n      generateInsights();\n    }\n  }, [scanResults, aiStatus]);\n\n  const checkAIStatus = async () => {\n    try {\n      const response = await fetch('/api/ai?action=status');\n      const status = await response.json();\n      setAiStatus(status);\n    } catch (error) {\n      console.error('Error checking AI status:', error);\n      setError('Unable to connect to AI service');\n    }\n  };\n\n  const generateInsights = async () => {\n    if (!aiStatus?.enabled || scanResults.length === 0) return;\n\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      // Generate market commentary\n      const commentaryResponse = await fetch('/api/ai', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          action: 'market-commentary',\n          data: { scanResults, marketConditions }\n        })\n      });\n      \n      if (commentaryResponse.ok) {\n        const { commentary } = await commentaryResponse.json();\n        setMarketCommentary(commentary);\n      }\n\n      // Generate trading recommendations\n      const recommendationsResponse = await fetch('/api/ai', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          action: 'trading-recommendations',\n          data: { \n            scanResults,\n            userPreferences: {\n              riskTolerance: 'medium',\n              tradingStyle: 'moderate',\n              accountSize: 100000\n            }\n          }\n        })\n      });\n\n      if (recommendationsResponse.ok) {\n        const { recommendations: recs } = await recommendationsResponse.json();\n        setRecommendations(recs);\n      }\n\n    } catch (error) {\n      console.error('Error generating AI insights:', error);\n      setError('Failed to generate AI insights');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  if (!aiStatus) {\n    return (\n      <Card className=\"bg-slate-800/50 border-slate-700\">\n        <CardContent className=\"p-6\">\n          <div className=\"flex items-center justify-center\">\n            <Loader2 className=\"h-6 w-6 animate-spin text-blue-400 mr-2\" />\n            <span className=\"text-slate-300\">Checking AI status...</span>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  if (!aiStatus.enabled) {\n    return (\n      <Card className=\"bg-slate-800/50 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"text-white flex items-center\">\n            <Brain className=\"mr-2 h-5 w-5 text-gray-400\" />\n            AI Insights\n            <Badge variant=\"outline\" className=\"ml-2 text-gray-400 border-gray-400\">\n              Disabled\n            </Badge>\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"text-center py-6\">\n            <Brain className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n            <p className=\"text-slate-300 mb-4\">\n              AI-powered insights are currently disabled.\n            </p>\n            <p className=\"text-sm text-slate-400 mb-4\">\n              Enable OpenAI integration to get intelligent market analysis, risk assessments, and personalized trading recommendations.\n            </p>\n            <Button variant=\"outline\" onClick={checkAIStatus}>\n              Check Configuration\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* AI Status Header */}\n      <Card className=\"bg-slate-800/50 border-slate-700\">\n        <CardHeader className=\"pb-3\">\n          <CardTitle className=\"text-white flex items-center justify-between\">\n            <div className=\"flex items-center\">\n              <Sparkles className=\"mr-2 h-5 w-5 text-blue-400\" />\n              AI-Powered Insights\n              <Badge className=\"ml-2 bg-blue-500/20 text-blue-400\">\n                {aiStatus.model || 'GPT-4o'}\n              </Badge>\n            </div>\n            <Button\n              size=\"sm\"\n              variant=\"outline\"\n              onClick={generateInsights}\n              disabled={isLoading}\n              className=\"text-slate-300 hover:text-white\"\n            >\n              {isLoading ? (\n                <Loader2 className=\"h-4 w-4 animate-spin mr-1\" />\n              ) : (\n                <Brain className=\"h-4 w-4 mr-1\" />\n              )}\n              {isLoading ? 'Analyzing...' : 'Refresh Insights'}\n            </Button>\n          </CardTitle>\n        </CardHeader>\n      </Card>\n\n      {error && (\n        <Card className=\"bg-red-900/20 border-red-500/30\">\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center text-red-400\">\n              <AlertTriangle className=\"h-4 w-4 mr-2\" />\n              {error}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Market Commentary */}\n      {marketCommentary && (\n        <Card className=\"bg-slate-800/50 border-slate-700\">\n          <CardHeader>\n            <CardTitle className=\"text-white flex items-center\">\n              <BarChart3 className=\"mr-2 h-5 w-5 text-green-400\" />\n              Market Commentary\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"prose prose-invert max-w-none\">\n              <p className=\"text-slate-300 leading-relaxed whitespace-pre-line\">\n                {marketCommentary}\n              </p>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Trading Recommendations */}\n      {recommendations && (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          {/* Top Picks */}\n          <Card className=\"bg-slate-800/50 border-slate-700\">\n            <CardHeader>\n              <CardTitle className=\"text-white flex items-center\">\n                <TrendingUp className=\"mr-2 h-5 w-5 text-green-400\" />\n                AI Top Picks\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-2\">\n                {recommendations.topPicks.map((symbol, index) => (\n                  <div key={symbol} className=\"flex items-center justify-between p-2 bg-green-900/20 rounded\">\n                    <div className=\"flex items-center\">\n                      <div className=\"w-6 h-6 rounded-full bg-green-500/20 text-green-400 flex items-center justify-center text-sm font-bold mr-3\">\n                        {index + 1}\n                      </div>\n                      <span className=\"text-white font-semibold\">{symbol}</span>\n                    </div>\n                    <Badge className=\"bg-green-500/20 text-green-400\">\n                      Recommended\n                    </Badge>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Action Items */}\n          <Card className=\"bg-slate-800/50 border-slate-700\">\n            <CardHeader>\n              <CardTitle className=\"text-white flex items-center\">\n                <Lightbulb className=\"mr-2 h-5 w-5 text-yellow-400\" />\n                Action Items\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-2\">\n                {recommendations.actionItems.map((item, index) => (\n                  <div key={index} className=\"flex items-start p-2 bg-yellow-900/20 rounded\">\n                    <Target className=\"h-4 w-4 text-yellow-400 mr-2 mt-0.5 flex-shrink-0\" />\n                    <span className=\"text-slate-300 text-sm\">{item}</span>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      )}\n\n      {/* Market Outlook */}\n      {recommendations?.marketOutlook && (\n        <Card className=\"bg-slate-800/50 border-slate-700\">\n          <CardHeader>\n            <CardTitle className=\"text-white flex items-center\">\n              <TrendingUp className=\"mr-2 h-5 w-5 text-blue-400\" />\n              Market Outlook\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <p className=\"text-slate-300 leading-relaxed\">\n              {recommendations.marketOutlook}\n            </p>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Avoid List */}\n      {recommendations?.avoidList && recommendations.avoidList.length > 0 && (\n        <Card className=\"bg-slate-800/50 border-slate-700\">\n          <CardHeader>\n            <CardTitle className=\"text-white flex items-center\">\n              <TrendingDown className=\"mr-2 h-5 w-5 text-red-400\" />\n              Caution List\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-2\">\n              {recommendations.avoidList.map((symbol, index) => (\n                <div key={symbol} className=\"flex items-center justify-between p-2 bg-red-900/20 rounded\">\n                  <span className=\"text-white font-semibold\">{symbol}</span>\n                  <Badge className=\"bg-red-500/20 text-red-400\">\n                    Avoid\n                  </Badge>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {isLoading && (\n        <Card className=\"bg-slate-800/50 border-slate-700\">\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-center\">\n              <Loader2 className=\"h-6 w-6 animate-spin text-blue-400 mr-2\" />\n              <span className=\"text-slate-300\">Generating AI insights...</span>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AA0Ce,SAAS,WAAW,EAAE,WAAW,EAAE,gBAAgB,EAAE,SAAS,EAAmB;IAC9F,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,0OAAQ,EAAkB;IAC1D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,0OAAQ,EAAS;IACjE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,0OAAQ,EAAgC;IACtF,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,0OAAQ,EAAC;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,0OAAQ,EAAgB;IAElD,qCAAqC;IACrC,IAAA,2OAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,6CAA6C;IAC7C,IAAA,2OAAS,EAAC;QACR,IAAI,YAAY,MAAM,GAAG,KAAK,UAAU,SAAS;YAC/C;QACF;IACF,GAAG;QAAC;QAAa;KAAS;IAE1B,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS;QACX;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,UAAU,WAAW,YAAY,MAAM,KAAK,GAAG;QAEpD,aAAa;QACb,SAAS;QAET,IAAI;YACF,6BAA6B;YAC7B,MAAM,qBAAqB,MAAM,MAAM,WAAW;gBAChD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,MAAM;wBAAE;wBAAa;oBAAiB;gBACxC;YACF;YAEA,IAAI,mBAAmB,EAAE,EAAE;gBACzB,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,mBAAmB,IAAI;gBACpD,oBAAoB;YACtB;YAEA,mCAAmC;YACnC,MAAM,0BAA0B,MAAM,MAAM,WAAW;gBACrD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,MAAM;wBACJ;wBACA,iBAAiB;4BACf,eAAe;4BACf,cAAc;4BACd,aAAa;wBACf;oBACF;gBACF;YACF;YAEA,IAAI,wBAAwB,EAAE,EAAE;gBAC9B,MAAM,EAAE,iBAAiB,IAAI,EAAE,GAAG,MAAM,wBAAwB,IAAI;gBACpE,mBAAmB;YACrB;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,CAAC,UAAU;QACb,qBACE,uQAAC,iKAAI;YAAC,WAAU;sBACd,cAAA,uQAAC,wKAAW;gBAAC,WAAU;0BACrB,cAAA,uQAAC;oBAAI,WAAU;;sCACb,uQAAC,qPAAO;4BAAC,WAAU;;;;;;sCACnB,uQAAC;4BAAK,WAAU;sCAAiB;;;;;;;;;;;;;;;;;;;;;;IAK3C;IAEA,IAAI,CAAC,SAAS,OAAO,EAAE;QACrB,qBACE,uQAAC,iKAAI;YAAC,WAAU;;8BACd,uQAAC,uKAAU;8BACT,cAAA,uQAAC,sKAAS;wBAAC,WAAU;;0CACnB,uQAAC,sOAAK;gCAAC,WAAU;;;;;;4BAA+B;0CAEhD,uQAAC,mKAAK;gCAAC,SAAQ;gCAAU,WAAU;0CAAqC;;;;;;;;;;;;;;;;;8BAK5E,uQAAC,wKAAW;8BACV,cAAA,uQAAC;wBAAI,WAAU;;0CACb,uQAAC,sOAAK;gCAAC,WAAU;;;;;;0CACjB,uQAAC;gCAAE,WAAU;0CAAsB;;;;;;0CAGnC,uQAAC;gCAAE,WAAU;0CAA8B;;;;;;0CAG3C,uQAAC,qKAAM;gCAAC,SAAQ;gCAAU,SAAS;0CAAe;;;;;;;;;;;;;;;;;;;;;;;IAO5D;IAEA,qBACE,uQAAC;QAAI,WAAU;;0BAEb,uQAAC,iKAAI;gBAAC,WAAU;0BACd,cAAA,uQAAC,uKAAU;oBAAC,WAAU;8BACpB,cAAA,uQAAC,sKAAS;wBAAC,WAAU;;0CACnB,uQAAC;gCAAI,WAAU;;kDACb,uQAAC,+OAAQ;wCAAC,WAAU;;;;;;oCAA+B;kDAEnD,uQAAC,mKAAK;wCAAC,WAAU;kDACd,SAAS,KAAK,IAAI;;;;;;;;;;;;0CAGvB,uQAAC,qKAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU;gCACV,WAAU;;oCAET,0BACC,uQAAC,qPAAO;wCAAC,WAAU;;;;;6DAEnB,uQAAC,sOAAK;wCAAC,WAAU;;;;;;oCAElB,YAAY,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;YAMrC,uBACC,uQAAC,iKAAI;gBAAC,WAAU;0BACd,cAAA,uQAAC,wKAAW;oBAAC,WAAU;8BACrB,cAAA,uQAAC;wBAAI,WAAU;;0CACb,uQAAC,kQAAa;gCAAC,WAAU;;;;;;4BACxB;;;;;;;;;;;;;;;;;YAOR,kCACC,uQAAC,iKAAI;gBAAC,WAAU;;kCACd,uQAAC,uKAAU;kCACT,cAAA,uQAAC,sKAAS;4BAAC,WAAU;;8CACnB,uQAAC,wPAAS;oCAAC,WAAU;;;;;;gCAAgC;;;;;;;;;;;;kCAIzD,uQAAC,wKAAW;kCACV,cAAA,uQAAC;4BAAI,WAAU;sCACb,cAAA,uQAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;;;;;;;;;;;YAQV,iCACC,uQAAC;gBAAI,WAAU;;kCAEb,uQAAC,iKAAI;wBAAC,WAAU;;0CACd,uQAAC,uKAAU;0CACT,cAAA,uQAAC,sKAAS;oCAAC,WAAU;;sDACnB,uQAAC,yPAAU;4CAAC,WAAU;;;;;;wCAAgC;;;;;;;;;;;;0CAI1D,uQAAC,wKAAW;0CACV,cAAA,uQAAC;oCAAI,WAAU;8CACZ,gBAAgB,QAAQ,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACrC,uQAAC;4CAAiB,WAAU;;8DAC1B,uQAAC;oDAAI,WAAU;;sEACb,uQAAC;4DAAI,WAAU;sEACZ,QAAQ;;;;;;sEAEX,uQAAC;4DAAK,WAAU;sEAA4B;;;;;;;;;;;;8DAE9C,uQAAC,mKAAK;oDAAC,WAAU;8DAAiC;;;;;;;2CAP1C;;;;;;;;;;;;;;;;;;;;;kCAiBlB,uQAAC,iKAAI;wBAAC,WAAU;;0CACd,uQAAC,uKAAU;0CACT,cAAA,uQAAC,sKAAS;oCAAC,WAAU;;sDACnB,uQAAC,kPAAS;4CAAC,WAAU;;;;;;wCAAiC;;;;;;;;;;;;0CAI1D,uQAAC,wKAAW;0CACV,cAAA,uQAAC;oCAAI,WAAU;8CACZ,gBAAgB,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,sBACtC,uQAAC;4CAAgB,WAAU;;8DACzB,uQAAC,yOAAM;oDAAC,WAAU;;;;;;8DAClB,uQAAC;oDAAK,WAAU;8DAA0B;;;;;;;2CAFlC;;;;;;;;;;;;;;;;;;;;;;;;;;;YAYrB,iBAAiB,+BAChB,uQAAC,iKAAI;gBAAC,WAAU;;kCACd,uQAAC,uKAAU;kCACT,cAAA,uQAAC,sKAAS;4BAAC,WAAU;;8CACnB,uQAAC,yPAAU;oCAAC,WAAU;;;;;;gCAA+B;;;;;;;;;;;;kCAIzD,uQAAC,wKAAW;kCACV,cAAA,uQAAC;4BAAE,WAAU;sCACV,gBAAgB,aAAa;;;;;;;;;;;;;;;;;YAOrC,iBAAiB,aAAa,gBAAgB,SAAS,CAAC,MAAM,GAAG,mBAChE,uQAAC,iKAAI;gBAAC,WAAU;;kCACd,uQAAC,uKAAU;kCACT,cAAA,uQAAC,sKAAS;4BAAC,WAAU;;8CACnB,uQAAC,+PAAY;oCAAC,WAAU;;;;;;gCAA8B;;;;;;;;;;;;kCAI1D,uQAAC,wKAAW;kCACV,cAAA,uQAAC;4BAAI,WAAU;sCACZ,gBAAgB,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACtC,uQAAC;oCAAiB,WAAU;;sDAC1B,uQAAC;4CAAK,WAAU;sDAA4B;;;;;;sDAC5C,uQAAC,mKAAK;4CAAC,WAAU;sDAA6B;;;;;;;mCAFtC;;;;;;;;;;;;;;;;;;;;;YAYnB,2BACC,uQAAC,iKAAI;gBAAC,WAAU;0BACd,cAAA,uQAAC,wKAAW;oBAAC,WAAU;8BACrB,cAAA,uQAAC;wBAAI,WAAU;;0CACb,uQAAC,qPAAO;gCAAC,WAAU;;;;;;0CACnB,uQAAC;gCAAK,WAAU;0CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}, {"offset": {"line": 2881, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/components/StrategyScanner.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Loader2, Zap, Target, Clock, AlertTriangle, TrendingUp, Moon, BarChart3, ExternalLink } from 'lucide-react'\nimport { EnhancedScanResult, StrategyScanSummary } from '@/lib/enhancedSwingScanner'\nimport { formatCurrency, formatPercentage } from '@/lib/utils'\nimport TradingSetupCard from './TradingSetupCard'\nimport AIInsights from './AIInsights'\n\ninterface StrategyScannerProps {\n  autoScan?: boolean\n  accountSize?: number\n}\n\nexport function StrategyScanner({ autoScan = false, accountSize = 100000 }: StrategyScannerProps) {\n  const [isScanning, setIsScanning] = useState(false)\n  const [scanResults, setScanResults] = useState<StrategyScanSummary | null>(null)\n  const [selectedStrategy, setSelectedStrategy] = useState<'both' | 'overnight' | 'breakout'>('both')\n  const [error, setError] = useState<string | null>(null)\n  const [userAccountSize, setUserAccountSize] = useState(accountSize)\n\n  // Handle manual trade execution\n  const handleExecuteTrade = async (setup: any) => {\n    try {\n      // Open trading interface or redirect to paper trading\n      alert(`Execute Trade for ${setup.symbol}:\\n\\nEntry: $${setup.preciseEntry.price.toFixed(2)} (${setup.preciseEntry.orderType})\\nStop: $${setup.preciseExit.stopLoss.price.toFixed(2)}\\nTarget: $${setup.targets[0].toFixed(2)}\\nShares: ${setup.positionSize}\\n\\nThis would normally open your trading platform or paper trading interface.`)\n    } catch (error) {\n      console.error('Error executing trade:', error)\n    }\n  }\n\n  // Handle chart viewing\n  const handleViewChart = (symbol: string) => {\n    // Open chart in new tab (TradingView or similar)\n    const chartUrl = `https://www.tradingview.com/chart/?symbol=${symbol}`\n    window.open(chartUrl, '_blank')\n  }\n\n  // Auto-scan on component mount if enabled\n  useEffect(() => {\n    if (autoScan) {\n      handleStrategyScan('quick')\n    }\n  }, [autoScan])\n\n  const handleStrategyScan = async (scanType: 'quick' | 'full') => {\n    setIsScanning(true)\n    setError(null)\n    setScanResults(null)\n\n    try {\n      const response = await fetch(\n        `/api/scanner/strategies?type=${scanType}&accountSize=${userAccountSize}&limit=20`\n      )\n      \n      if (!response.ok) throw new Error('Failed to fetch strategy scan results')\n      \n      const data = await response.json()\n      setScanResults(data)\n    } catch (err) {\n      setError('Failed to perform strategy scan. Please try again.')\n      console.error('Strategy scan error:', err)\n    } finally {\n      setIsScanning(false)\n    }\n  }\n\n  const getStrategyIcon = (strategy: string) => {\n    switch (strategy) {\n      case 'overnight_momentum':\n        return <Moon className=\"h-4 w-4 text-purple-400\" />\n      case 'technical_breakout':\n        return <TrendingUp className=\"h-4 w-4 text-green-400\" />\n      default:\n        return <BarChart3 className=\"h-4 w-4 text-blue-400\" />\n    }\n  }\n\n  const getStrategyName = (strategy: string) => {\n    switch (strategy) {\n      case 'overnight_momentum':\n        return 'Overnight Momentum'\n      case 'technical_breakout':\n        return 'Technical Breakout'\n      default:\n        return 'Mixed Strategy'\n    }\n  }\n\n  const getStrategyColor = (strategy: string) => {\n    switch (strategy) {\n      case 'overnight_momentum':\n        return 'bg-purple-500/20 text-purple-400'\n      case 'technical_breakout':\n        return 'bg-green-500/20 text-green-400'\n      default:\n        return 'bg-blue-500/20 text-blue-400'\n    }\n  }\n\n  const filteredResults = scanResults?.topSetups.filter(result => {\n    if (selectedStrategy === 'both') return true\n    if (selectedStrategy === 'overnight') return result.overnightSetup\n    if (selectedStrategy === 'breakout') return result.breakoutSetup\n    return true\n  }) || []\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Strategy Scanner Controls */}\n      <Card className=\"bg-slate-800/50 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"text-white flex items-center\">\n            <Zap className=\"mr-2 h-5 w-5 text-yellow-400\" />\n            Professional Swing Trading Strategies\n          </CardTitle>\n          <CardDescription className=\"text-slate-300\">\n            Automated scanner implementing proven swing trading methodologies with precise entry/exit rules\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          {/* Account Size Input */}\n          <div className=\"mb-4\">\n            <label className=\"block text-sm text-slate-300 mb-2\">Account Size (for position sizing)</label>\n            <input\n              type=\"number\"\n              value={userAccountSize}\n              onChange={(e) => setUserAccountSize(parseInt(e.target.value) || 100000)}\n              className=\"w-32 px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm\"\n              min=\"10000\"\n              step=\"10000\"\n              disabled={isScanning}\n            />\n          </div>\n\n          {/* Strategy Filter */}\n          <div className=\"mb-4\">\n            <label className=\"block text-sm text-slate-300 mb-2\">Strategy Filter</label>\n            <div className=\"flex gap-2\">\n              <Button\n                variant={selectedStrategy === 'both' ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => setSelectedStrategy('both')}\n                className={selectedStrategy === 'both' ? 'bg-blue-600' : 'border-slate-600 text-slate-300'}\n              >\n                All Strategies\n              </Button>\n              <Button\n                variant={selectedStrategy === 'overnight' ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => setSelectedStrategy('overnight')}\n                className={selectedStrategy === 'overnight' ? 'bg-purple-600' : 'border-slate-600 text-slate-300'}\n              >\n                <Moon className=\"mr-1 h-3 w-3\" />\n                Overnight\n              </Button>\n              <Button\n                variant={selectedStrategy === 'breakout' ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => setSelectedStrategy('breakout')}\n                className={selectedStrategy === 'breakout' ? 'bg-green-600' : 'border-slate-600 text-slate-300'}\n              >\n                <TrendingUp className=\"mr-1 h-3 w-3\" />\n                Breakout\n              </Button>\n            </div>\n          </div>\n\n          {/* Scan Buttons */}\n          <div className=\"flex gap-4 mb-4\">\n            <Button\n              onClick={() => handleStrategyScan('quick')}\n              disabled={isScanning}\n              className=\"bg-blue-600 hover:bg-blue-700\"\n            >\n              {isScanning ? (\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n              ) : (\n                <Zap className=\"mr-2 h-4 w-4\" />\n              )}\n              Quick Strategy Scan\n            </Button>\n            \n            <Button\n              onClick={() => handleStrategyScan('full')}\n              disabled={isScanning}\n              variant=\"outline\"\n              className=\"border-slate-600 text-slate-300 hover:bg-slate-800\"\n            >\n              {isScanning ? (\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n              ) : (\n                <Target className=\"mr-2 h-4 w-4\" />\n              )}\n              Full Strategy Scan\n            </Button>\n          </div>\n\n          {/* Market Conditions */}\n          {scanResults?.marketConditions && (\n            <div className=\"p-3 bg-slate-700/50 rounded-lg\">\n              <div className=\"flex items-center justify-between text-sm\">\n                <span className=\"text-slate-300\">Market Status:</span>\n                <div className=\"flex items-center gap-4\">\n                  <span className=\"text-white\">{scanResults.marketConditions.timeOfDay}</span>\n                  <div className=\"flex gap-2\">\n                    <Badge className={scanResults.marketConditions.marketHours\n                      ? 'bg-green-500/20 text-green-400'\n                      : 'bg-red-500/20 text-red-400'\n                    }>\n                      {scanResults.marketConditions.marketHours ? 'Market Open' : 'Market Closed'}\n                    </Badge>\n                    <Badge className={scanResults.marketConditions.isOptimalScanTime\n                      ? 'bg-blue-500/20 text-blue-400'\n                      : 'bg-yellow-500/20 text-yellow-400'\n                    }>\n                      {scanResults.marketConditions.isOptimalScanTime ? 'Optimal Scan Time' : 'Outside Optimal Hours'}\n                    </Badge>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {isScanning && (\n            <div className=\"text-center py-4\">\n              <Loader2 className=\"mx-auto h-8 w-8 animate-spin text-blue-400\" />\n              <p className=\"text-slate-300 mt-2\">\n                Analyzing stocks for professional swing trading setups...\n              </p>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Error Display */}\n      {error && (\n        <Card className=\"bg-red-900/20 border-red-500/50\">\n          <CardContent className=\"p-6\">\n            <p className=\"text-red-300 text-center\">{error}</p>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Strategy Scan Results */}\n      {scanResults && (\n        <div className=\"space-y-6\">\n          {/* Scan Summary */}\n          <Card className=\"bg-slate-800/50 border-slate-700\">\n            <CardHeader>\n              <CardTitle className=\"text-white\">Strategy Scan Summary</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-white\">{scanResults.totalScanned}</div>\n                  <div className=\"text-sm text-slate-300\">Stocks Scanned</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-purple-400\">{scanResults.overnightSetups}</div>\n                  <div className=\"text-sm text-slate-300\">Overnight Setups</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-green-400\">{scanResults.breakoutSetups}</div>\n                  <div className=\"text-sm text-slate-300\">Breakout Setups</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-blue-400\">{scanResults.bothStrategies}</div>\n                  <div className=\"text-sm text-slate-300\">Both Strategies</div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Professional Trading Setups */}\n          <div className=\"space-y-6\">\n            <div className=\"flex items-center justify-between\">\n              <h2 className=\"text-2xl font-bold text-white flex items-center\">\n                <Target className=\"mr-3 h-6 w-6 text-green-400\" />\n                Professional Trading Setups ({filteredResults.length})\n              </h2>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => window.open('https://www.tradingview.com', '_blank')}\n                className=\"text-slate-300 hover:text-white\"\n              >\n                <ExternalLink className=\"h-4 w-4 mr-2\" />\n                Open TradingView\n              </Button>\n            </div>\n\n            {filteredResults.map((result, index) => {\n              // Convert to TradingSetupCard format - prioritize best strategy\n              const bestSetup = result.bestStrategy === 'overnight_momentum'\n                ? result.overnightSetup\n                : result.breakoutSetup;\n\n              if (!bestSetup) return null;\n\n              const setupWithSymbol = {\n                ...bestSetup,\n                symbol: result.symbol\n              };\n\n              return (\n                <TradingSetupCard\n                  key={result.symbol}\n                  setup={setupWithSymbol}\n                  onExecuteTrade={handleExecuteTrade}\n                  onViewChart={handleViewChart}\n                />\n              );\n            })}\n          </div>\n\n          {/* AI Insights Section */}\n          {filteredResults.length > 0 && (\n            <div className=\"mt-8\">\n              <AIInsights\n                scanResults={filteredResults}\n                marketConditions={scanResults.marketConditions}\n                onRefresh={() => runScan()}\n              />\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AACA;AAVA;;;;;;;;;AAiBO,SAAS,gBAAgB,EAAE,WAAW,KAAK,EAAE,cAAc,MAAM,EAAwB;IAC9F,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,0OAAQ,EAAC;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,0OAAQ,EAA6B;IAC3E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,0OAAQ,EAAoC;IAC5F,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,0OAAQ,EAAgB;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,0OAAQ,EAAC;IAEvD,gCAAgC;IAChC,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,sDAAsD;YACtD,MAAM,CAAC,kBAAkB,EAAE,MAAM,MAAM,CAAC,aAAa,EAAE,MAAM,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,MAAM,YAAY,CAAC,SAAS,CAAC,UAAU,EAAE,MAAM,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,WAAW,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,UAAU,EAAE,MAAM,YAAY,CAAC,8EAA8E,CAAC;QAC7U,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,uBAAuB;IACvB,MAAM,kBAAkB,CAAC;QACvB,iDAAiD;QACjD,MAAM,WAAW,CAAC,0CAA0C,EAAE,QAAQ;QACtE,OAAO,IAAI,CAAC,UAAU;IACxB;IAEA,0CAA0C;IAC1C,IAAA,2OAAS,EAAC;QACR,IAAI,UAAU;YACZ,mBAAmB;QACrB;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,qBAAqB,OAAO;QAChC,cAAc;QACd,SAAS;QACT,eAAe;QAEf,IAAI;YACF,MAAM,WAAW,MAAM,MACrB,CAAC,6BAA6B,EAAE,SAAS,aAAa,EAAE,gBAAgB,SAAS,CAAC;YAGpF,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,eAAe;QACjB,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,qBAAO,uQAAC,mOAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,uQAAC,yPAAU;oBAAC,WAAU;;;;;;YAC/B;gBACE,qBAAO,uQAAC,wPAAS;oBAAC,WAAU;;;;;;QAChC;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB,aAAa,UAAU,OAAO,CAAA;QACpD,IAAI,qBAAqB,QAAQ,OAAO;QACxC,IAAI,qBAAqB,aAAa,OAAO,OAAO,cAAc;QAClE,IAAI,qBAAqB,YAAY,OAAO,OAAO,aAAa;QAChE,OAAO;IACT,MAAM,EAAE;IAER,qBACE,uQAAC;QAAI,WAAU;;0BAEb,uQAAC,iKAAI;gBAAC,WAAU;;kCACd,uQAAC,uKAAU;;0CACT,uQAAC,sKAAS;gCAAC,WAAU;;kDACnB,uQAAC,gOAAG;wCAAC,WAAU;;;;;;oCAAiC;;;;;;;0CAGlD,uQAAC,4KAAe;gCAAC,WAAU;0CAAiB;;;;;;;;;;;;kCAI9C,uQAAC,wKAAW;;0CAEV,uQAAC;gCAAI,WAAU;;kDACb,uQAAC;wCAAM,WAAU;kDAAoC;;;;;;kDACrD,uQAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,mBAAmB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;wCAChE,WAAU;wCACV,KAAI;wCACJ,MAAK;wCACL,UAAU;;;;;;;;;;;;0CAKd,uQAAC;gCAAI,WAAU;;kDACb,uQAAC;wCAAM,WAAU;kDAAoC;;;;;;kDACrD,uQAAC;wCAAI,WAAU;;0DACb,uQAAC,qKAAM;gDACL,SAAS,qBAAqB,SAAS,YAAY;gDACnD,MAAK;gDACL,SAAS,IAAM,oBAAoB;gDACnC,WAAW,qBAAqB,SAAS,gBAAgB;0DAC1D;;;;;;0DAGD,uQAAC,qKAAM;gDACL,SAAS,qBAAqB,cAAc,YAAY;gDACxD,MAAK;gDACL,SAAS,IAAM,oBAAoB;gDACnC,WAAW,qBAAqB,cAAc,kBAAkB;;kEAEhE,uQAAC,mOAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,uQAAC,qKAAM;gDACL,SAAS,qBAAqB,aAAa,YAAY;gDACvD,MAAK;gDACL,SAAS,IAAM,oBAAoB;gDACnC,WAAW,qBAAqB,aAAa,iBAAiB;;kEAE9D,uQAAC,yPAAU;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;0CAO7C,uQAAC;gCAAI,WAAU;;kDACb,uQAAC,qKAAM;wCACL,SAAS,IAAM,mBAAmB;wCAClC,UAAU;wCACV,WAAU;;4CAET,2BACC,uQAAC,qPAAO;gDAAC,WAAU;;;;;qEAEnB,uQAAC,gOAAG;gDAAC,WAAU;;;;;;4CACf;;;;;;;kDAIJ,uQAAC,qKAAM;wCACL,SAAS,IAAM,mBAAmB;wCAClC,UAAU;wCACV,SAAQ;wCACR,WAAU;;4CAET,2BACC,uQAAC,qPAAO;gDAAC,WAAU;;;;;qEAEnB,uQAAC,yOAAM;gDAAC,WAAU;;;;;;4CAClB;;;;;;;;;;;;;4BAML,aAAa,kCACZ,uQAAC;gCAAI,WAAU;0CACb,cAAA,uQAAC;oCAAI,WAAU;;sDACb,uQAAC;4CAAK,WAAU;sDAAiB;;;;;;sDACjC,uQAAC;4CAAI,WAAU;;8DACb,uQAAC;oDAAK,WAAU;8DAAc,YAAY,gBAAgB,CAAC,SAAS;;;;;;8DACpE,uQAAC;oDAAI,WAAU;;sEACb,uQAAC,mKAAK;4DAAC,WAAW,YAAY,gBAAgB,CAAC,WAAW,GACtD,mCACA;sEAED,YAAY,gBAAgB,CAAC,WAAW,GAAG,gBAAgB;;;;;;sEAE9D,uQAAC,mKAAK;4DAAC,WAAW,YAAY,gBAAgB,CAAC,iBAAiB,GAC5D,iCACA;sEAED,YAAY,gBAAgB,CAAC,iBAAiB,GAAG,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAQnF,4BACC,uQAAC;gCAAI,WAAU;;kDACb,uQAAC,qPAAO;wCAAC,WAAU;;;;;;kDACnB,uQAAC;wCAAE,WAAU;kDAAsB;;;;;;;;;;;;;;;;;;;;;;;;YAS1C,uBACC,uQAAC,iKAAI;gBAAC,WAAU;0BACd,cAAA,uQAAC,wKAAW;oBAAC,WAAU;8BACrB,cAAA,uQAAC;wBAAE,WAAU;kCAA4B;;;;;;;;;;;;;;;;YAM9C,6BACC,uQAAC;gBAAI,WAAU;;kCAEb,uQAAC,iKAAI;wBAAC,WAAU;;0CACd,uQAAC,uKAAU;0CACT,cAAA,uQAAC,sKAAS;oCAAC,WAAU;8CAAa;;;;;;;;;;;0CAEpC,uQAAC,wKAAW;0CACV,cAAA,uQAAC;oCAAI,WAAU;;sDACb,uQAAC;4CAAI,WAAU;;8DACb,uQAAC;oDAAI,WAAU;8DAAiC,YAAY,YAAY;;;;;;8DACxE,uQAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;sDAE1C,uQAAC;4CAAI,WAAU;;8DACb,uQAAC;oDAAI,WAAU;8DAAsC,YAAY,eAAe;;;;;;8DAChF,uQAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;sDAE1C,uQAAC;4CAAI,WAAU;;8DACb,uQAAC;oDAAI,WAAU;8DAAqC,YAAY,cAAc;;;;;;8DAC9E,uQAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;sDAE1C,uQAAC;4CAAI,WAAU;;8DACb,uQAAC;oDAAI,WAAU;8DAAoC,YAAY,cAAc;;;;;;8DAC7E,uQAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOhD,uQAAC;wBAAI,WAAU;;0CACb,uQAAC;gCAAI,WAAU;;kDACb,uQAAC;wCAAG,WAAU;;0DACZ,uQAAC,yOAAM;gDAAC,WAAU;;;;;;4CAAgC;4CACpB,gBAAgB,MAAM;4CAAC;;;;;;;kDAEvD,uQAAC,qKAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC,+BAA+B;wCAC1D,WAAU;;0DAEV,uQAAC,+PAAY;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;4BAK5C,gBAAgB,GAAG,CAAC,CAAC,QAAQ;gCAC5B,gEAAgE;gCAChE,MAAM,YAAY,OAAO,YAAY,KAAK,uBACtC,OAAO,cAAc,GACrB,OAAO,aAAa;gCAExB,IAAI,CAAC,WAAW,OAAO;gCAEvB,MAAM,kBAAkB;oCACtB,GAAG,SAAS;oCACZ,QAAQ,OAAO,MAAM;gCACvB;gCAEA,qBACE,uQAAC,0KAAgB;oCAEf,OAAO;oCACP,gBAAgB;oCAChB,aAAa;mCAHR,OAAO,MAAM;;;;;4BAMxB;;;;;;;oBAID,gBAAgB,MAAM,GAAG,mBACxB,uQAAC;wBAAI,WAAU;kCACb,cAAA,uQAAC,oKAAU;4BACT,aAAa;4BACb,kBAAkB,YAAY,gBAAgB;4BAC9C,WAAW,IAAM;;;;;;;;;;;;;;;;;;;;;;;AAQjC", "debugId": null}}, {"offset": {"line": 3588, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/lib/alertSystem.ts"], "sourcesContent": ["import { PerfectPickSetup, PreMarketGapScan, Catalyst } from '@/types/trading'\n\nexport type AlertType = \n  | 'new_catalyst'\n  | 'perfect_pick_found'\n  | 'pre_market_gap'\n  | 'pmh_break'\n  | 'stop_loss_hit'\n  | 'profit_target_hit'\n  | 'entry_trigger'\n  | 'volume_spike'\n\nexport interface Alert {\n  id: string\n  type: AlertType\n  symbol: string\n  title: string\n  message: string\n  priority: 'low' | 'medium' | 'high' | 'critical'\n  timestamp: string\n  data?: any\n  read: boolean\n  actionable: boolean\n  actions?: AlertAction[]\n}\n\nexport interface AlertAction {\n  id: string\n  label: string\n  type: 'execute_trade' | 'view_chart' | 'update_stop' | 'take_profit' | 'dismiss'\n  data?: any\n}\n\nexport class AlertSystem {\n  private alerts: Alert[] = []\n  private subscribers: ((alerts: Alert[]) => void)[] = []\n\n  constructor() {\n    // Alert system initialized without audio\n  }\n\n  /**\n   * Subscribe to alert updates\n   */\n  subscribe(callback: (alerts: Alert[]) => void): () => void {\n    this.subscribers.push(callback)\n    \n    // Return unsubscribe function\n    return () => {\n      const index = this.subscribers.indexOf(callback)\n      if (index > -1) {\n        this.subscribers.splice(index, 1)\n      }\n    }\n  }\n\n  /**\n   * Add a new alert\n   */\n  addAlert(alert: Omit<Alert, 'id' | 'timestamp' | 'read'>): void {\n    const newAlert: Alert = {\n      ...alert,\n      id: this.generateAlertId(),\n      timestamp: new Date().toISOString(),\n      read: false\n    }\n\n    this.alerts.unshift(newAlert) // Add to beginning of array\n    \n    // Keep only last 100 alerts\n    if (this.alerts.length > 100) {\n      this.alerts = this.alerts.slice(0, 100)\n    }\n\n    // Show browser notification for critical alerts\n    if (newAlert.priority === 'critical') {\n      this.showBrowserNotification(newAlert)\n    }\n\n    this.notifySubscribers()\n  }\n\n  /**\n   * Mark alert as read\n   */\n  markAsRead(alertId: string): void {\n    const alert = this.alerts.find(a => a.id === alertId)\n    if (alert) {\n      alert.read = true\n      this.notifySubscribers()\n    }\n  }\n\n  /**\n   * Mark all alerts as read\n   */\n  markAllAsRead(): void {\n    this.alerts.forEach(alert => alert.read = true)\n    this.notifySubscribers()\n  }\n\n  /**\n   * Remove alert\n   */\n  removeAlert(alertId: string): void {\n    this.alerts = this.alerts.filter(a => a.id !== alertId)\n    this.notifySubscribers()\n  }\n\n  /**\n   * Clear all alerts\n   */\n  clearAllAlerts(): void {\n    this.alerts = []\n    this.notifySubscribers()\n  }\n\n  /**\n   * Get all alerts\n   */\n  getAlerts(): Alert[] {\n    return [...this.alerts]\n  }\n\n  /**\n   * Get unread alerts count\n   */\n  getUnreadCount(): number {\n    return this.alerts.filter(a => !a.read).length\n  }\n\n  /**\n   * Create catalyst alert\n   */\n  createCatalystAlert(catalyst: Catalyst): void {\n    const priority = catalyst.tier === 'tier_1' ? 'high' : \n                    catalyst.tier === 'tier_2' ? 'medium' : 'low'\n\n    this.addAlert({\n      type: 'new_catalyst',\n      symbol: catalyst.symbol,\n      title: `New ${catalyst.tier.replace('_', ' ').toUpperCase()} Catalyst: ${catalyst.symbol}`,\n      message: catalyst.title,\n      priority,\n      data: { catalyst },\n      actionable: true,\n      actions: [\n        {\n          id: 'view_details',\n          label: 'View Details',\n          type: 'view_chart',\n          data: { symbol: catalyst.symbol }\n        }\n      ]\n    })\n  }\n\n  /**\n   * Create Perfect-Pick alert\n   */\n  createPerfectPickAlert(setup: PerfectPickSetup): void {\n    this.addAlert({\n      type: 'perfect_pick_found',\n      symbol: setup.symbol,\n      title: `Perfect-Pick Setup Found: ${setup.symbol}`,\n      message: `Grade ${setup.setupGrade} setup with ${setup.catalyst.type.replace(/_/g, ' ')} catalyst`,\n      priority: setup.setupGrade.startsWith('A') ? 'high' : 'medium',\n      data: { setup },\n      actionable: true,\n      actions: [\n        {\n          id: 'execute_trade',\n          label: 'Execute Trade',\n          type: 'execute_trade',\n          data: { setup }\n        },\n        {\n          id: 'view_chart',\n          label: 'View Chart',\n          type: 'view_chart',\n          data: { symbol: setup.symbol }\n        }\n      ]\n    })\n  }\n\n  /**\n   * Create pre-market gap alert\n   */\n  createGapAlert(gapScan: PreMarketGapScan): void {\n    const priority = gapScan.gapPercent > 10 ? 'high' : \n                    gapScan.gapPercent > 5 ? 'medium' : 'low'\n\n    this.addAlert({\n      type: 'pre_market_gap',\n      symbol: gapScan.symbol,\n      title: `Pre-Market Gap: ${gapScan.symbol}`,\n      message: `${gapScan.gapPercent.toFixed(1)}% gap with ${gapScan.catalyst ? 'catalyst' : 'no catalyst'}`,\n      priority,\n      data: { gapScan },\n      actionable: gapScan.meetsAllCriteria,\n      actions: gapScan.meetsAllCriteria ? [\n        {\n          id: 'analyze_setup',\n          label: 'Analyze Setup',\n          type: 'view_chart',\n          data: { symbol: gapScan.symbol }\n        }\n      ] : undefined\n    })\n  }\n\n  /**\n   * Create PMH break alert\n   */\n  createPMHBreakAlert(symbol: string, currentPrice: number, pmh: number): void {\n    this.addAlert({\n      type: 'pmh_break',\n      symbol,\n      title: `PMH Break: ${symbol}`,\n      message: `Price ${currentPrice.toFixed(2)} broke above PMH ${pmh.toFixed(2)}`,\n      priority: 'high',\n      data: { currentPrice, pmh },\n      actionable: true,\n      actions: [\n        {\n          id: 'execute_entry',\n          label: 'Execute Entry',\n          type: 'execute_trade',\n          data: { symbol, entryPrice: currentPrice }\n        }\n      ]\n    })\n  }\n\n  /**\n   * Create stop loss alert\n   */\n  createStopLossAlert(symbol: string, currentPrice: number, stopLoss: number): void {\n    this.addAlert({\n      type: 'stop_loss_hit',\n      symbol,\n      title: `Stop Loss Hit: ${symbol}`,\n      message: `Price ${currentPrice.toFixed(2)} hit stop loss ${stopLoss.toFixed(2)}`,\n      priority: 'critical',\n      data: { currentPrice, stopLoss },\n      actionable: true,\n      actions: [\n        {\n          id: 'exit_position',\n          label: 'Exit Position',\n          type: 'execute_trade',\n          data: { symbol, action: 'sell', price: currentPrice }\n        }\n      ]\n    })\n  }\n\n  /**\n   * Create profit target alert\n   */\n  createProfitTargetAlert(symbol: string, currentPrice: number, target: number, rLevel: number): void {\n    this.addAlert({\n      type: 'profit_target_hit',\n      symbol,\n      title: `Profit Target Hit: ${symbol}`,\n      message: `Price ${currentPrice.toFixed(2)} hit ${rLevel}R target ${target.toFixed(2)}`,\n      priority: 'high',\n      data: { currentPrice, target, rLevel },\n      actionable: true,\n      actions: [\n        {\n          id: 'take_profit',\n          label: 'Take Profit',\n          type: 'take_profit',\n          data: { symbol, price: currentPrice, rLevel }\n        }\n      ]\n    })\n  }\n\n  /**\n   * Create volume spike alert\n   */\n  createVolumeSpikeAlert(symbol: string, currentVolume: number, avgVolume: number): void {\n    const volumeRatio = currentVolume / avgVolume\n    \n    this.addAlert({\n      type: 'volume_spike',\n      symbol,\n      title: `Volume Spike: ${symbol}`,\n      message: `Volume ${volumeRatio.toFixed(1)}x above average`,\n      priority: volumeRatio > 5 ? 'high' : 'medium',\n      data: { currentVolume, avgVolume, volumeRatio },\n      actionable: false\n    })\n  }\n\n  /**\n   * Monitor Perfect-Pick setups for alerts\n   */\n  monitorPerfectPickSetups(setups: PerfectPickSetup[]): void {\n    setups.forEach(setup => {\n      // Check if this is a new setup (not already alerted)\n      const existingAlert = this.alerts.find(\n        a => a.type === 'perfect_pick_found' && \n             a.symbol === setup.symbol && \n             a.data?.setup?.createdAt === setup.createdAt\n      )\n\n      if (!existingAlert && setup.overallScore >= 80) {\n        this.createPerfectPickAlert(setup)\n      }\n    })\n  }\n\n  /**\n   * Monitor gap scans for alerts\n   */\n  monitorGapScans(gapScans: PreMarketGapScan[]): void {\n    gapScans.forEach(gapScan => {\n      // Check if this is a new gap (not already alerted)\n      const existingAlert = this.alerts.find(\n        a => a.type === 'pre_market_gap' && \n             a.symbol === gapScan.symbol &&\n             Math.abs(new Date(a.timestamp).getTime() - new Date(gapScan.scanTime).getTime()) < 60000 // Within 1 minute\n      )\n\n      if (!existingAlert && gapScan.gapPercent >= 3) {\n        this.createGapAlert(gapScan)\n      }\n    })\n  }\n\n  private generateAlertId(): string {\n    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n  }\n\n  private notifySubscribers(): void {\n    this.subscribers.forEach(callback => callback([...this.alerts]))\n  }\n\n\n\n  private showBrowserNotification(alert: Alert): void {\n    if ('Notification' in window && Notification.permission === 'granted') {\n      new Notification(alert.title, {\n        body: alert.message,\n        icon: '/favicon.ico',\n        tag: alert.symbol // Prevent duplicate notifications for same symbol\n      })\n    } else if ('Notification' in window && Notification.permission !== 'denied') {\n      Notification.requestPermission().then(permission => {\n        if (permission === 'granted') {\n          new Notification(alert.title, {\n            body: alert.message,\n            icon: '/favicon.ico',\n            tag: alert.symbol\n          })\n        }\n      })\n    }\n  }\n}\n\n// Create singleton instance\nexport const alertSystem = new AlertSystem()\n"], "names": [], "mappings": ";;;;;;AAiCO,MAAM;IACH,SAAkB,EAAE,CAAA;IACpB,cAA6C,EAAE,CAAA;IAEvD,aAAc;IACZ,yCAAyC;IAC3C;IAEA;;GAEC,GACD,UAAU,QAAmC,EAAc;QACzD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QAEtB,8BAA8B;QAC9B,OAAO;YACL,MAAM,QAAQ,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;YACvC,IAAI,QAAQ,CAAC,GAAG;gBACd,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO;YACjC;QACF;IACF;IAEA;;GAEC,GACD,SAAS,KAA+C,EAAQ;QAC9D,MAAM,WAAkB;YACtB,GAAG,KAAK;YACR,IAAI,IAAI,CAAC,eAAe;YACxB,WAAW,IAAI,OAAO,WAAW;YACjC,MAAM;QACR;QAEA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAU,4BAA4B;QAE1D,4BAA4B;QAC5B,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,KAAK;YAC5B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG;QACrC;QAEA,gDAAgD;QAChD,IAAI,SAAS,QAAQ,KAAK,YAAY;YACpC,IAAI,CAAC,uBAAuB,CAAC;QAC/B;QAEA,IAAI,CAAC,iBAAiB;IACxB;IAEA;;GAEC,GACD,WAAW,OAAe,EAAQ;QAChC,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC7C,IAAI,OAAO;YACT,MAAM,IAAI,GAAG;YACb,IAAI,CAAC,iBAAiB;QACxB;IACF;IAEA;;GAEC,GACD,gBAAsB;QACpB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA,QAAS,MAAM,IAAI,GAAG;QAC1C,IAAI,CAAC,iBAAiB;IACxB;IAEA;;GAEC,GACD,YAAY,OAAe,EAAQ;QACjC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC/C,IAAI,CAAC,iBAAiB;IACxB;IAEA;;GAEC,GACD,iBAAuB;QACrB,IAAI,CAAC,MAAM,GAAG,EAAE;QAChB,IAAI,CAAC,iBAAiB;IACxB;IAEA;;GAEC,GACD,YAAqB;QACnB,OAAO;eAAI,IAAI,CAAC,MAAM;SAAC;IACzB;IAEA;;GAEC,GACD,iBAAyB;QACvB,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,IAAI,EAAE,MAAM;IAChD;IAEA;;GAEC,GACD,oBAAoB,QAAkB,EAAQ;QAC5C,MAAM,WAAW,SAAS,IAAI,KAAK,WAAW,SAC9B,SAAS,IAAI,KAAK,WAAW,WAAW;QAExD,IAAI,CAAC,QAAQ,CAAC;YACZ,MAAM;YACN,QAAQ,SAAS,MAAM;YACvB,OAAO,CAAC,IAAI,EAAE,SAAS,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW,GAAG,WAAW,EAAE,SAAS,MAAM,EAAE;YAC1F,SAAS,SAAS,KAAK;YACvB;YACA,MAAM;gBAAE;YAAS;YACjB,YAAY;YACZ,SAAS;gBACP;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;wBAAE,QAAQ,SAAS,MAAM;oBAAC;gBAClC;aACD;QACH;IACF;IAEA;;GAEC,GACD,uBAAuB,KAAuB,EAAQ;QACpD,IAAI,CAAC,QAAQ,CAAC;YACZ,MAAM;YACN,QAAQ,MAAM,MAAM;YACpB,OAAO,CAAC,0BAA0B,EAAE,MAAM,MAAM,EAAE;YAClD,SAAS,CAAC,MAAM,EAAE,MAAM,UAAU,CAAC,YAAY,EAAE,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,SAAS,CAAC;YAClG,UAAU,MAAM,UAAU,CAAC,UAAU,CAAC,OAAO,SAAS;YACtD,MAAM;gBAAE;YAAM;YACd,YAAY;YACZ,SAAS;gBACP;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;wBAAE;oBAAM;gBAChB;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;wBAAE,QAAQ,MAAM,MAAM;oBAAC;gBAC/B;aACD;QACH;IACF;IAEA;;GAEC,GACD,eAAe,OAAyB,EAAQ;QAC9C,MAAM,WAAW,QAAQ,UAAU,GAAG,KAAK,SAC3B,QAAQ,UAAU,GAAG,IAAI,WAAW;QAEpD,IAAI,CAAC,QAAQ,CAAC;YACZ,MAAM;YACN,QAAQ,QAAQ,MAAM;YACtB,OAAO,CAAC,gBAAgB,EAAE,QAAQ,MAAM,EAAE;YAC1C,SAAS,GAAG,QAAQ,UAAU,CAAC,OAAO,CAAC,GAAG,WAAW,EAAE,QAAQ,QAAQ,GAAG,aAAa,eAAe;YACtG;YACA,MAAM;gBAAE;YAAQ;YAChB,YAAY,QAAQ,gBAAgB;YACpC,SAAS,QAAQ,gBAAgB,GAAG;gBAClC;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;wBAAE,QAAQ,QAAQ,MAAM;oBAAC;gBACjC;aACD,GAAG;QACN;IACF;IAEA;;GAEC,GACD,oBAAoB,MAAc,EAAE,YAAoB,EAAE,GAAW,EAAQ;QAC3E,IAAI,CAAC,QAAQ,CAAC;YACZ,MAAM;YACN;YACA,OAAO,CAAC,WAAW,EAAE,QAAQ;YAC7B,SAAS,CAAC,MAAM,EAAE,aAAa,OAAO,CAAC,GAAG,iBAAiB,EAAE,IAAI,OAAO,CAAC,IAAI;YAC7E,UAAU;YACV,MAAM;gBAAE;gBAAc;YAAI;YAC1B,YAAY;YACZ,SAAS;gBACP;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;wBAAE;wBAAQ,YAAY;oBAAa;gBAC3C;aACD;QACH;IACF;IAEA;;GAEC,GACD,oBAAoB,MAAc,EAAE,YAAoB,EAAE,QAAgB,EAAQ;QAChF,IAAI,CAAC,QAAQ,CAAC;YACZ,MAAM;YACN;YACA,OAAO,CAAC,eAAe,EAAE,QAAQ;YACjC,SAAS,CAAC,MAAM,EAAE,aAAa,OAAO,CAAC,GAAG,eAAe,EAAE,SAAS,OAAO,CAAC,IAAI;YAChF,UAAU;YACV,MAAM;gBAAE;gBAAc;YAAS;YAC/B,YAAY;YACZ,SAAS;gBACP;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;wBAAE;wBAAQ,QAAQ;wBAAQ,OAAO;oBAAa;gBACtD;aACD;QACH;IACF;IAEA;;GAEC,GACD,wBAAwB,MAAc,EAAE,YAAoB,EAAE,MAAc,EAAE,MAAc,EAAQ;QAClG,IAAI,CAAC,QAAQ,CAAC;YACZ,MAAM;YACN;YACA,OAAO,CAAC,mBAAmB,EAAE,QAAQ;YACrC,SAAS,CAAC,MAAM,EAAE,aAAa,OAAO,CAAC,GAAG,KAAK,EAAE,OAAO,SAAS,EAAE,OAAO,OAAO,CAAC,IAAI;YACtF,UAAU;YACV,MAAM;gBAAE;gBAAc;gBAAQ;YAAO;YACrC,YAAY;YACZ,SAAS;gBACP;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;wBAAE;wBAAQ,OAAO;wBAAc;oBAAO;gBAC9C;aACD;QACH;IACF;IAEA;;GAEC,GACD,uBAAuB,MAAc,EAAE,aAAqB,EAAE,SAAiB,EAAQ;QACrF,MAAM,cAAc,gBAAgB;QAEpC,IAAI,CAAC,QAAQ,CAAC;YACZ,MAAM;YACN;YACA,OAAO,CAAC,cAAc,EAAE,QAAQ;YAChC,SAAS,CAAC,OAAO,EAAE,YAAY,OAAO,CAAC,GAAG,eAAe,CAAC;YAC1D,UAAU,cAAc,IAAI,SAAS;YACrC,MAAM;gBAAE;gBAAe;gBAAW;YAAY;YAC9C,YAAY;QACd;IACF;IAEA;;GAEC,GACD,yBAAyB,MAA0B,EAAQ;QACzD,OAAO,OAAO,CAAC,CAAA;YACb,qDAAqD;YACrD,MAAM,gBAAgB,IAAI,CAAC,MAAM,CAAC,IAAI,CACpC,CAAA,IAAK,EAAE,IAAI,KAAK,wBACX,EAAE,MAAM,KAAK,MAAM,MAAM,IACzB,EAAE,IAAI,EAAE,OAAO,cAAc,MAAM,SAAS;YAGnD,IAAI,CAAC,iBAAiB,MAAM,YAAY,IAAI,IAAI;gBAC9C,IAAI,CAAC,sBAAsB,CAAC;YAC9B;QACF;IACF;IAEA;;GAEC,GACD,gBAAgB,QAA4B,EAAQ;QAClD,SAAS,OAAO,CAAC,CAAA;YACf,mDAAmD;YACnD,MAAM,gBAAgB,IAAI,CAAC,MAAM,CAAC,IAAI,CACpC,CAAA,IAAK,EAAE,IAAI,KAAK,oBACX,EAAE,MAAM,KAAK,QAAQ,MAAM,IAC3B,KAAK,GAAG,CAAC,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,QAAQ,QAAQ,EAAE,OAAO,MAAM,MAAM,kBAAkB;;YAGlH,IAAI,CAAC,iBAAiB,QAAQ,UAAU,IAAI,GAAG;gBAC7C,IAAI,CAAC,cAAc,CAAC;YACtB;QACF;IACF;IAEQ,kBAA0B;QAChC,OAAO,CAAC,MAAM,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IACzE;IAEQ,oBAA0B;QAChC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA,WAAY,SAAS;mBAAI,IAAI,CAAC,MAAM;aAAC;IAChE;IAIQ,wBAAwB,KAAY,EAAQ;QAClD,IAAI,kBAAkB,UAAU,aAAa,UAAU,KAAK,WAAW;YACrE,IAAI,aAAa,MAAM,KAAK,EAAE;gBAC5B,MAAM,MAAM,OAAO;gBACnB,MAAM;gBACN,KAAK,MAAM,MAAM,CAAC,kDAAkD;YACtE;QACF,OAAO,IAAI,kBAAkB,UAAU,aAAa,UAAU,KAAK,UAAU;YAC3E,aAAa,iBAAiB,GAAG,IAAI,CAAC,CAAA;gBACpC,IAAI,eAAe,WAAW;oBAC5B,IAAI,aAAa,MAAM,KAAK,EAAE;wBAC5B,MAAM,MAAM,OAAO;wBACnB,MAAM;wBACN,KAAK,MAAM,MAAM;oBACnB;gBACF;YACF;QACF;IACF;AACF;AAGO,MAAM,cAAc,IAAI", "debugId": null}}, {"offset": {"line": 3914, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/components/EventDrivenScanner.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  TrendingUp,\n  Zap,\n  Target,\n  AlertTriangle,\n  Clock,\n  DollarSign,\n  Activity,\n  Loader2,\n  RefreshCw,\n  Filter,\n  Star,\n  Play,\n  Eye,\n  CheckCircle\n} from 'lucide-react'\nimport { PerfectPickSetup, PreMarketGapScan, Catalyst } from '@/types/trading'\nimport { alertSystem } from '@/lib/alertSystem'\nimport { formatCurrency, formatPercentage } from '@/lib/utils'\n\ninterface EventDrivenScannerProps {\n  accountSize?: number\n  riskPercent?: number\n}\n\ninterface UnifiedTradingOpportunity {\n  symbol: string\n  name: string\n  price: number\n  gapPercent: number\n  catalyst?: Catalyst\n  technicalGrade: string\n  technicalScore: number\n  riskReward: number\n  positionSize: number\n  entryPrice: number\n  stopLoss: number\n  target3R: number\n  riskAmount: number\n  potentialProfit: number\n  setupScore: number\n  meetsAllCriteria: boolean\n  exclusionReasons: string[]\n}\n\nexport function EventDrivenScanner({\n  accountSize = 100000,\n  riskPercent = 2\n}: EventDrivenScannerProps) {\n  const [tradingOpportunities, setTradingOpportunities] = useState<UnifiedTradingOpportunity[]>([])\n  const [isLoading, setIsLoading] = useState(false)\n  const [lastScanTime, setLastScanTime] = useState<string>('')\n  const [scanSummary, setScanSummary] = useState<any>(null)\n\n  // Auto-refresh every 15 minutes during market hours\n  useEffect(() => {\n    const interval = setInterval(() => {\n      const now = new Date()\n      const hour = now.getHours()\n      // Refresh during pre-market (4-9:30 AM EST) and market hours (9:30 AM - 4 PM EST)\n      if ((hour >= 4 && hour < 21)) {\n        handleUnifiedScan()\n      }\n    }, 15 * 60 * 1000) // 15 minutes\n\n    return () => clearInterval(interval)\n  }, [])\n\n  const handleUnifiedScan = async () => {\n    setIsLoading(true)\n    try {\n      console.log('🎯 Starting unified Event-Driven scan...')\n\n      // Run the complete Perfect-Pick scan which includes all components\n      const response = await fetch(`/api/scanner/perfect-pick?accountSize=${accountSize}&riskPercent=${riskPercent}&limit=30`)\n      const data = await response.json()\n\n      if (data.success && data.data.setups) {\n        // Convert Perfect-Pick setups to unified trading opportunities\n        const opportunities: UnifiedTradingOpportunity[] = data.data.setups.map((setup: PerfectPickSetup) => ({\n          symbol: setup.symbol,\n          name: setup.name,\n          price: setup.gapScan.price,\n          gapPercent: setup.gapScan.gapPercent,\n          catalyst: setup.catalyst,\n          technicalGrade: setup.technicalGate.overallGrade,\n          technicalScore: setup.technicalGate.gateScore,\n          riskReward: setup.rewardPlanning.riskRewardRatio,\n          positionSize: setup.riskManagement.positionSize,\n          entryPrice: setup.riskManagement.entryPrice,\n          stopLoss: setup.riskManagement.stopLoss,\n          target3R: setup.rewardPlanning.target3R,\n          riskAmount: setup.riskManagement.positionSize * setup.riskManagement.riskPerShare,\n          potentialProfit: setup.riskManagement.positionSize * setup.riskManagement.riskPerShare * setup.rewardPlanning.riskRewardRatio,\n          setupScore: setup.overallScore,\n          meetsAllCriteria: setup.validationChecks.noExclusionFlags,\n          exclusionReasons: setup.exclusionReasons\n        }))\n\n        setTradingOpportunities(opportunities)\n        setScanSummary(data.data.summary)\n        setLastScanTime(new Date().toLocaleTimeString())\n\n        // Generate alerts for new opportunities\n        alertSystem.monitorPerfectPickSetups(data.data.setups)\n\n        console.log(`✅ Found ${opportunities.length} trading opportunities`)\n      } else {\n        console.log('⚠️ No trading opportunities found')\n        setTradingOpportunities([])\n      }\n    } catch (error) {\n      console.error('Error running unified scan:', error)\n      setTradingOpportunities([])\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const getCatalystBadgeColor = (catalyst: Catalyst) => {\n    if (catalyst.tier === 'tier_1') return 'bg-green-500 text-white'\n    if (catalyst.tier === 'tier_2') return 'bg-yellow-500 text-white'\n    return 'bg-gray-500 text-white'\n  }\n\n  const getCatalystImpactIcon = (impact: string) => {\n    switch (impact) {\n      case 'bullish': return <TrendingUp className=\"h-4 w-4 text-green-500\" />\n      case 'bearish': return <AlertTriangle className=\"h-4 w-4 text-red-500\" />\n      default: return <Activity className=\"h-4 w-4 text-gray-500\" />\n    }\n  }\n\n  const getSetupGradeColor = (grade: string) => {\n    if (grade.startsWith('A')) return 'text-green-600 bg-green-50 border-green-200'\n    if (grade.startsWith('B')) return 'text-blue-600 bg-blue-50 border-blue-200'\n    if (grade.startsWith('C')) return 'text-yellow-600 bg-yellow-50 border-yellow-200'\n    return 'text-red-600 bg-red-50 border-red-200'\n  }\n\n  const getFreshnessColor = (freshness: string) => {\n    switch (freshness) {\n      case 'fresh': return 'text-green-600 bg-green-50'\n      case 'moderate': return 'text-yellow-600 bg-yellow-50'\n      case 'stale': return 'text-red-600 bg-red-50'\n      default: return 'text-gray-600 bg-gray-50'\n    }\n  }\n\n  const handleExecuteTrade = (opportunity: UnifiedTradingOpportunity) => {\n    console.log('Execute trade for:', opportunity.symbol)\n    // TODO: Implement trade execution\n    alertSystem.addAlert({\n      type: 'entry_trigger',\n      symbol: opportunity.symbol,\n      title: `Trade Executed: ${opportunity.symbol}`,\n      message: `Entered position at ${formatCurrency(opportunity.entryPrice)}`,\n      priority: 'high',\n      actionable: false\n    })\n  }\n\n  const handleViewChart = (symbol: string) => {\n    console.log('View chart for:', symbol)\n    // TODO: Implement chart viewing\n    window.open(`https://finance.yahoo.com/quote/${symbol}`, '_blank')\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h2 className=\"text-2xl font-bold\">Event-Driven Scanner</h2>\n          <p className=\"text-muted-foreground\">\n            Unified catalyst detection, gap analysis, and technical screening\n          </p>\n        </div>\n        <div className=\"flex items-center gap-2\">\n          {lastScanTime && (\n            <span className=\"text-sm text-muted-foreground\">\n              Last scan: {lastScanTime}\n            </span>\n          )}\n          <Button\n            onClick={handleUnifiedScan}\n            disabled={isLoading}\n            className=\"bg-blue-600 hover:bg-blue-700\"\n          >\n            {isLoading ? (\n              <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\n            ) : (\n              <Target className=\"h-4 w-4 mr-2\" />\n            )}\n            {isLoading ? 'Scanning...' : 'Scan Opportunities'}\n          </Button>\n        </div>\n      </div>\n\n      {/* Scan Summary */}\n      {scanSummary && (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Activity className=\"h-5 w-5\" />\n              Scan Results\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-blue-600\">{tradingOpportunities.length}</div>\n                <div className=\"text-sm text-muted-foreground\">Opportunities Found</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-green-600\">\n                  {tradingOpportunities.filter(op => op.meetsAllCriteria).length}\n                </div>\n                <div className=\"text-sm text-muted-foreground\">Perfect-Pick Setups</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-purple-600\">\n                  {tradingOpportunities.length > 0\n                    ? formatPercentage(tradingOpportunities.reduce((sum, op) => sum + op.gapPercent, 0) / tradingOpportunities.length)\n                    : '0%'\n                  }\n                </div>\n                <div className=\"text-sm text-muted-foreground\">Avg Gap</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-orange-600\">\n                  {new Set(tradingOpportunities.filter(op => op.catalyst).map(op => op.catalyst!.type)).size}\n                </div>\n                <div className=\"text-sm text-muted-foreground\">Catalyst Types</div>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Trading Opportunities */}\n      <div className=\"space-y-4\">\n        <div className=\"flex items-center justify-between\">\n          <h3 className=\"text-lg font-semibold\">Trading Opportunities</h3>\n          <div className=\"flex items-center gap-2\">\n            <Badge variant=\"outline\" className=\"text-xs\">\n              {tradingOpportunities.filter(op => op.meetsAllCriteria).length} Perfect-Pick\n            </Badge>\n            <Badge variant=\"outline\" className=\"text-xs\">\n              {tradingOpportunities.filter(op => op.catalyst?.tier === 'tier_1').length} Tier-1 Catalysts\n            </Badge>\n          </div>\n        </div>\n\n        <div className=\"grid gap-4\">\n          {tradingOpportunities.map((opportunity) => (\n            <Card key={opportunity.symbol} className=\"hover:shadow-lg transition-all duration-200 border-l-4 border-l-blue-500\">\n              <CardHeader className=\"pb-3\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center gap-3\">\n                    <div>\n                      <CardTitle className=\"text-xl font-bold flex items-center gap-2\">\n                        {opportunity.symbol}\n                        {opportunity.meetsAllCriteria && <Star className=\"h-5 w-5 text-yellow-500\" />}\n                      </CardTitle>\n                      <CardDescription>{opportunity.name}</CardDescription>\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      <Badge className={`${getSetupGradeColor(opportunity.technicalGrade)} border`}>\n                        Grade {opportunity.technicalGrade}\n                      </Badge>\n                      <Badge variant=\"outline\" className=\"font-semibold\">\n                        Score: {opportunity.setupScore}/100\n                      </Badge>\n                    </div>\n                  </div>\n                  <div className=\"text-right\">\n                    <div className=\"text-2xl font-bold text-blue-600\">\n                      {formatCurrency(opportunity.price)}\n                    </div>\n                    <div className=\"text-sm font-semibold text-green-600\">\n                      Gap: +{formatPercentage(opportunity.gapPercent)}\n                    </div>\n                  </div>\n                </div>\n              </CardHeader>\n\n              <CardContent className=\"space-y-4\">\n                {/* Catalyst Information */}\n                {opportunity.catalyst && (\n                  <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border\">\n                    <div className=\"flex items-center gap-2 mb-2\">\n                      <Zap className=\"h-5 w-5 text-purple-600\" />\n                      <span className=\"font-semibold text-purple-800\">Catalyst</span>\n                      {getCatalystImpactIcon(opportunity.catalyst.impact)}\n                      <Badge className={getCatalystBadgeColor(opportunity.catalyst)}>\n                        {opportunity.catalyst.tier.replace('_', ' ').toUpperCase()}\n                      </Badge>\n                      <Badge className={getFreshnessColor(opportunity.catalyst.freshness)}>\n                        {opportunity.catalyst.freshness}\n                      </Badge>\n                    </div>\n                    <h4 className=\"font-medium text-gray-800 mb-1\">{opportunity.catalyst.title}</h4>\n                    <p className=\"text-sm text-gray-600 mb-2\">{opportunity.catalyst.description}</p>\n                    <div className=\"flex items-center gap-4 text-xs text-gray-500\">\n                      <span>Quality: {opportunity.catalyst.qualityScore}/10</span>\n                      <span>Source: {opportunity.catalyst.source}</span>\n                      <span className=\"flex items-center gap-1\">\n                        <Clock className=\"h-3 w-3\" />\n                        {new Date(opportunity.catalyst.announcementTime).toLocaleString()}\n                      </span>\n                    </div>\n                  </div>\n                )}\n\n                {/* Risk/Reward Summary */}\n                <div className=\"grid grid-cols-4 gap-4\">\n                  <div className=\"text-center p-3 bg-blue-50 rounded-lg\">\n                    <div className=\"text-sm text-blue-600 font-medium\">Entry</div>\n                    <div className=\"text-lg font-bold text-blue-800\">\n                      {formatCurrency(opportunity.entryPrice)}\n                    </div>\n                  </div>\n                  <div className=\"text-center p-3 bg-red-50 rounded-lg\">\n                    <div className=\"text-sm text-red-600 font-medium\">Stop Loss</div>\n                    <div className=\"text-lg font-bold text-red-800\">\n                      {formatCurrency(opportunity.stopLoss)}\n                    </div>\n                  </div>\n                  <div className=\"text-center p-3 bg-green-50 rounded-lg\">\n                    <div className=\"text-sm text-green-600 font-medium\">Target (3R)</div>\n                    <div className=\"text-lg font-bold text-green-800\">\n                      {formatCurrency(opportunity.target3R)}\n                    </div>\n                  </div>\n                  <div className=\"text-center p-3 bg-purple-50 rounded-lg\">\n                    <div className=\"text-sm text-purple-600 font-medium\">R/R Ratio</div>\n                    <div className=\"text-lg font-bold text-purple-800\">\n                      {opportunity.riskReward}:1\n                    </div>\n                  </div>\n                </div>\n\n                {/* Position Sizing */}\n                <div className=\"bg-gray-50 p-4 rounded-lg\">\n                  <div className=\"grid grid-cols-3 gap-4 text-sm\">\n                    <div>\n                      <span className=\"text-gray-600\">Position Size:</span>\n                      <div className=\"font-semibold\">{opportunity.positionSize} shares</div>\n                    </div>\n                    <div>\n                      <span className=\"text-gray-600\">Risk Amount:</span>\n                      <div className=\"font-semibold text-red-600\">{formatCurrency(opportunity.riskAmount)}</div>\n                    </div>\n                    <div>\n                      <span className=\"text-gray-600\">Potential Profit (3R):</span>\n                      <div className=\"font-semibold text-green-600\">{formatCurrency(opportunity.potentialProfit)}</div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Technical Summary */}\n                <div className=\"flex items-center justify-between p-3 bg-slate-50 rounded-lg\">\n                  <div className=\"flex items-center gap-2\">\n                    <Target className=\"h-4 w-4 text-slate-600\" />\n                    <span className=\"text-sm font-medium\">Technical Analysis:</span>\n                    <Badge variant={opportunity.technicalGrade === 'A' ? 'default' : 'secondary'}>\n                      Grade {opportunity.technicalGrade}\n                    </Badge>\n                    <span className=\"text-xs text-muted-foreground\">\n                      ({opportunity.technicalScore}/100)\n                    </span>\n                  </div>\n                  <div className=\"flex items-center gap-1 text-xs text-muted-foreground\">\n                    {opportunity.meetsAllCriteria && <CheckCircle className=\"h-3 w-3 text-green-500\" />}\n                    <span>{opportunity.meetsAllCriteria ? 'All Criteria Met' : 'Partial Match'}</span>\n                  </div>\n                </div>\n\n                {/* Exclusion Reasons */}\n                {opportunity.exclusionReasons.length > 0 && (\n                  <div className=\"p-3 bg-red-50 border border-red-200 rounded-lg\">\n                    <div className=\"flex items-center gap-2 mb-1\">\n                      <AlertTriangle className=\"h-4 w-4 text-red-600\" />\n                      <span className=\"font-medium text-red-800\">Exclusion Reasons</span>\n                    </div>\n                    <ul className=\"text-sm text-red-600 space-y-1\">\n                      {opportunity.exclusionReasons.map((reason, index) => (\n                        <li key={index} className=\"flex items-center gap-1\">\n                          <span>• {reason}</span>\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                )}\n\n                {/* Action Buttons */}\n                <div className=\"flex items-center gap-2 pt-2\">\n                  <Button\n                    onClick={() => handleExecuteTrade(opportunity)}\n                    className=\"flex-1 bg-blue-600 hover:bg-blue-700\"\n                    disabled={!opportunity.meetsAllCriteria}\n                  >\n                    <Play className=\"h-4 w-4 mr-2\" />\n                    Execute Trade\n                  </Button>\n                  <Button\n                    variant=\"outline\"\n                    onClick={() => handleViewChart(opportunity.symbol)}\n                    className=\"flex-1\"\n                  >\n                    <Eye className=\"h-4 w-4 mr-2\" />\n                    View Chart\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n\n        {/* Empty State */}\n        {tradingOpportunities.length === 0 && !isLoading && (\n          <Card>\n            <CardContent className=\"text-center py-12\">\n              <Target className=\"h-16 w-16 mx-auto text-muted-foreground mb-4\" />\n              <h3 className=\"text-lg font-semibold mb-2\">No Trading Opportunities Found</h3>\n              <p className=\"text-muted-foreground mb-4\">\n                Click \"Scan Opportunities\" to search for event-driven trading setups with catalyst detection, gap analysis, and technical screening.\n              </p>\n              <Button\n                onClick={handleUnifiedScan}\n                disabled={isLoading}\n                className=\"bg-blue-600 hover:bg-blue-700\"\n              >\n                <Target className=\"h-4 w-4 mr-2\" />\n                Start Scanning\n              </Button>\n            </CardContent>\n          </Card>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AACA;AAxBA;;;;;;;;;AAmDO,SAAS,mBAAmB,EACjC,cAAc,MAAM,EACpB,cAAc,CAAC,EACS;IACxB,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,IAAA,0OAAQ,EAA8B,EAAE;IAChG,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,0OAAQ,EAAC;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,0OAAQ,EAAS;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,0OAAQ,EAAM;IAEpD,oDAAoD;IACpD,IAAA,2OAAS,EAAC;QACR,MAAM,WAAW,YAAY;YAC3B,MAAM,MAAM,IAAI;YAChB,MAAM,OAAO,IAAI,QAAQ;YACzB,kFAAkF;YAClF,IAAK,QAAQ,KAAK,OAAO,IAAK;gBAC5B;YACF;QACF,GAAG,KAAK,KAAK,MAAM,aAAa;;QAEhC,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,aAAa;QACb,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,mEAAmE;YACnE,MAAM,WAAW,MAAM,MAAM,CAAC,sCAAsC,EAAE,YAAY,aAAa,EAAE,YAAY,SAAS,CAAC;YACvH,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,CAAC,MAAM,EAAE;gBACpC,+DAA+D;gBAC/D,MAAM,gBAA6C,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,QAA4B,CAAC;wBACpG,QAAQ,MAAM,MAAM;wBACpB,MAAM,MAAM,IAAI;wBAChB,OAAO,MAAM,OAAO,CAAC,KAAK;wBAC1B,YAAY,MAAM,OAAO,CAAC,UAAU;wBACpC,UAAU,MAAM,QAAQ;wBACxB,gBAAgB,MAAM,aAAa,CAAC,YAAY;wBAChD,gBAAgB,MAAM,aAAa,CAAC,SAAS;wBAC7C,YAAY,MAAM,cAAc,CAAC,eAAe;wBAChD,cAAc,MAAM,cAAc,CAAC,YAAY;wBAC/C,YAAY,MAAM,cAAc,CAAC,UAAU;wBAC3C,UAAU,MAAM,cAAc,CAAC,QAAQ;wBACvC,UAAU,MAAM,cAAc,CAAC,QAAQ;wBACvC,YAAY,MAAM,cAAc,CAAC,YAAY,GAAG,MAAM,cAAc,CAAC,YAAY;wBACjF,iBAAiB,MAAM,cAAc,CAAC,YAAY,GAAG,MAAM,cAAc,CAAC,YAAY,GAAG,MAAM,cAAc,CAAC,eAAe;wBAC7H,YAAY,MAAM,YAAY;wBAC9B,kBAAkB,MAAM,gBAAgB,CAAC,gBAAgB;wBACzD,kBAAkB,MAAM,gBAAgB;oBAC1C,CAAC;gBAED,wBAAwB;gBACxB,eAAe,KAAK,IAAI,CAAC,OAAO;gBAChC,gBAAgB,IAAI,OAAO,kBAAkB;gBAE7C,wCAAwC;gBACxC,iKAAW,CAAC,wBAAwB,CAAC,KAAK,IAAI,CAAC,MAAM;gBAErD,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,cAAc,MAAM,CAAC,sBAAsB,CAAC;YACrE,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,wBAAwB,EAAE;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,wBAAwB,EAAE;QAC5B,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,IAAI,SAAS,IAAI,KAAK,UAAU,OAAO;QACvC,IAAI,SAAS,IAAI,KAAK,UAAU,OAAO;QACvC,OAAO;IACT;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBAAW,qBAAO,uQAAC,yPAAU;oBAAC,WAAU;;;;;;YAC7C,KAAK;gBAAW,qBAAO,uQAAC,kQAAa;oBAAC,WAAU;;;;;;YAChD;gBAAS,qBAAO,uQAAC,+OAAQ;oBAAC,WAAU;;;;;;QACtC;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,MAAM,UAAU,CAAC,MAAM,OAAO;QAClC,IAAI,MAAM,UAAU,CAAC,MAAM,OAAO;QAClC,IAAI,MAAM,UAAU,CAAC,MAAM,OAAO;QAClC,OAAO;IACT;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,QAAQ,GAAG,CAAC,sBAAsB,YAAY,MAAM;QACpD,kCAAkC;QAClC,iKAAW,CAAC,QAAQ,CAAC;YACnB,MAAM;YACN,QAAQ,YAAY,MAAM;YAC1B,OAAO,CAAC,gBAAgB,EAAE,YAAY,MAAM,EAAE;YAC9C,SAAS,CAAC,oBAAoB,EAAE,IAAA,8JAAc,EAAC,YAAY,UAAU,GAAG;YACxE,UAAU;YACV,YAAY;QACd;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,QAAQ,GAAG,CAAC,mBAAmB;QAC/B,gCAAgC;QAChC,OAAO,IAAI,CAAC,CAAC,gCAAgC,EAAE,QAAQ,EAAE;IAC3D;IAEA,qBACE,uQAAC;QAAI,WAAU;;0BAEb,uQAAC;gBAAI,WAAU;;kCACb,uQAAC;;0CACC,uQAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,uQAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,uQAAC;wBAAI,WAAU;;4BACZ,8BACC,uQAAC;gCAAK,WAAU;;oCAAgC;oCAClC;;;;;;;0CAGhB,uQAAC,qKAAM;gCACL,SAAS;gCACT,UAAU;gCACV,WAAU;;oCAET,0BACC,uQAAC,qPAAO;wCAAC,WAAU;;;;;6DAEnB,uQAAC,yOAAM;wCAAC,WAAU;;;;;;oCAEnB,YAAY,gBAAgB;;;;;;;;;;;;;;;;;;;YAMlC,6BACC,uQAAC,iKAAI;;kCACH,uQAAC,uKAAU;kCACT,cAAA,uQAAC,sKAAS;4BAAC,WAAU;;8CACnB,uQAAC,+OAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIpC,uQAAC,wKAAW;kCACV,cAAA,uQAAC;4BAAI,WAAU;;8CACb,uQAAC;oCAAI,WAAU;;sDACb,uQAAC;4CAAI,WAAU;sDAAoC,qBAAqB,MAAM;;;;;;sDAC9E,uQAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;8CAEjD,uQAAC;oCAAI,WAAU;;sDACb,uQAAC;4CAAI,WAAU;sDACZ,qBAAqB,MAAM,CAAC,CAAA,KAAM,GAAG,gBAAgB,EAAE,MAAM;;;;;;sDAEhE,uQAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;8CAEjD,uQAAC;oCAAI,WAAU;;sDACb,uQAAC;4CAAI,WAAU;sDACZ,qBAAqB,MAAM,GAAG,IAC3B,IAAA,gKAAgB,EAAC,qBAAqB,MAAM,CAAC,CAAC,KAAK,KAAO,MAAM,GAAG,UAAU,EAAE,KAAK,qBAAqB,MAAM,IAC/G;;;;;;sDAGN,uQAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;8CAEjD,uQAAC;oCAAI,WAAU;;sDACb,uQAAC;4CAAI,WAAU;sDACZ,IAAI,IAAI,qBAAqB,MAAM,CAAC,CAAA,KAAM,GAAG,QAAQ,EAAE,GAAG,CAAC,CAAA,KAAM,GAAG,QAAQ,CAAE,IAAI,GAAG,IAAI;;;;;;sDAE5F,uQAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQzD,uQAAC;gBAAI,WAAU;;kCACb,uQAAC;wBAAI,WAAU;;0CACb,uQAAC;gCAAG,WAAU;0CAAwB;;;;;;0CACtC,uQAAC;gCAAI,WAAU;;kDACb,uQAAC,mKAAK;wCAAC,SAAQ;wCAAU,WAAU;;4CAChC,qBAAqB,MAAM,CAAC,CAAA,KAAM,GAAG,gBAAgB,EAAE,MAAM;4CAAC;;;;;;;kDAEjE,uQAAC,mKAAK;wCAAC,SAAQ;wCAAU,WAAU;;4CAChC,qBAAqB,MAAM,CAAC,CAAA,KAAM,GAAG,QAAQ,EAAE,SAAS,UAAU,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;kCAKhF,uQAAC;wBAAI,WAAU;kCACZ,qBAAqB,GAAG,CAAC,CAAC,4BACzB,uQAAC,iKAAI;gCAA0B,WAAU;;kDACvC,uQAAC,uKAAU;wCAAC,WAAU;kDACpB,cAAA,uQAAC;4CAAI,WAAU;;8DACb,uQAAC;oDAAI,WAAU;;sEACb,uQAAC;;8EACC,uQAAC,sKAAS;oEAAC,WAAU;;wEAClB,YAAY,MAAM;wEAClB,YAAY,gBAAgB,kBAAI,uQAAC,mOAAI;4EAAC,WAAU;;;;;;;;;;;;8EAEnD,uQAAC,4KAAe;8EAAE,YAAY,IAAI;;;;;;;;;;;;sEAEpC,uQAAC;4DAAI,WAAU;;8EACb,uQAAC,mKAAK;oEAAC,WAAW,GAAG,mBAAmB,YAAY,cAAc,EAAE,OAAO,CAAC;;wEAAE;wEACrE,YAAY,cAAc;;;;;;;8EAEnC,uQAAC,mKAAK;oEAAC,SAAQ;oEAAU,WAAU;;wEAAgB;wEACzC,YAAY,UAAU;wEAAC;;;;;;;;;;;;;;;;;;;8DAIrC,uQAAC;oDAAI,WAAU;;sEACb,uQAAC;4DAAI,WAAU;sEACZ,IAAA,8JAAc,EAAC,YAAY,KAAK;;;;;;sEAEnC,uQAAC;4DAAI,WAAU;;gEAAuC;gEAC7C,IAAA,gKAAgB,EAAC,YAAY,UAAU;;;;;;;;;;;;;;;;;;;;;;;;kDAMtD,uQAAC,wKAAW;wCAAC,WAAU;;4CAEpB,YAAY,QAAQ,kBACnB,uQAAC;gDAAI,WAAU;;kEACb,uQAAC;wDAAI,WAAU;;0EACb,uQAAC,gOAAG;gEAAC,WAAU;;;;;;0EACf,uQAAC;gEAAK,WAAU;0EAAgC;;;;;;4DAC/C,sBAAsB,YAAY,QAAQ,CAAC,MAAM;0EAClD,uQAAC,mKAAK;gEAAC,WAAW,sBAAsB,YAAY,QAAQ;0EACzD,YAAY,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;0EAE1D,uQAAC,mKAAK;gEAAC,WAAW,kBAAkB,YAAY,QAAQ,CAAC,SAAS;0EAC/D,YAAY,QAAQ,CAAC,SAAS;;;;;;;;;;;;kEAGnC,uQAAC;wDAAG,WAAU;kEAAkC,YAAY,QAAQ,CAAC,KAAK;;;;;;kEAC1E,uQAAC;wDAAE,WAAU;kEAA8B,YAAY,QAAQ,CAAC,WAAW;;;;;;kEAC3E,uQAAC;wDAAI,WAAU;;0EACb,uQAAC;;oEAAK;oEAAU,YAAY,QAAQ,CAAC,YAAY;oEAAC;;;;;;;0EAClD,uQAAC;;oEAAK;oEAAS,YAAY,QAAQ,CAAC,MAAM;;;;;;;0EAC1C,uQAAC;gEAAK,WAAU;;kFACd,uQAAC,sOAAK;wEAAC,WAAU;;;;;;oEAChB,IAAI,KAAK,YAAY,QAAQ,CAAC,gBAAgB,EAAE,cAAc;;;;;;;;;;;;;;;;;;;0DAOvE,uQAAC;gDAAI,WAAU;;kEACb,uQAAC;wDAAI,WAAU;;0EACb,uQAAC;gEAAI,WAAU;0EAAoC;;;;;;0EACnD,uQAAC;gEAAI,WAAU;0EACZ,IAAA,8JAAc,EAAC,YAAY,UAAU;;;;;;;;;;;;kEAG1C,uQAAC;wDAAI,WAAU;;0EACb,uQAAC;gEAAI,WAAU;0EAAmC;;;;;;0EAClD,uQAAC;gEAAI,WAAU;0EACZ,IAAA,8JAAc,EAAC,YAAY,QAAQ;;;;;;;;;;;;kEAGxC,uQAAC;wDAAI,WAAU;;0EACb,uQAAC;gEAAI,WAAU;0EAAqC;;;;;;0EACpD,uQAAC;gEAAI,WAAU;0EACZ,IAAA,8JAAc,EAAC,YAAY,QAAQ;;;;;;;;;;;;kEAGxC,uQAAC;wDAAI,WAAU;;0EACb,uQAAC;gEAAI,WAAU;0EAAsC;;;;;;0EACrD,uQAAC;gEAAI,WAAU;;oEACZ,YAAY,UAAU;oEAAC;;;;;;;;;;;;;;;;;;;0DAM9B,uQAAC;gDAAI,WAAU;0DACb,cAAA,uQAAC;oDAAI,WAAU;;sEACb,uQAAC;;8EACC,uQAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,uQAAC;oEAAI,WAAU;;wEAAiB,YAAY,YAAY;wEAAC;;;;;;;;;;;;;sEAE3D,uQAAC;;8EACC,uQAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,uQAAC;oEAAI,WAAU;8EAA8B,IAAA,8JAAc,EAAC,YAAY,UAAU;;;;;;;;;;;;sEAEpF,uQAAC;;8EACC,uQAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,uQAAC;oEAAI,WAAU;8EAAgC,IAAA,8JAAc,EAAC,YAAY,eAAe;;;;;;;;;;;;;;;;;;;;;;;0DAM/F,uQAAC;gDAAI,WAAU;;kEACb,uQAAC;wDAAI,WAAU;;0EACb,uQAAC,yOAAM;gEAAC,WAAU;;;;;;0EAClB,uQAAC;gEAAK,WAAU;0EAAsB;;;;;;0EACtC,uQAAC,mKAAK;gEAAC,SAAS,YAAY,cAAc,KAAK,MAAM,YAAY;;oEAAa;oEACrE,YAAY,cAAc;;;;;;;0EAEnC,uQAAC;gEAAK,WAAU;;oEAAgC;oEAC5C,YAAY,cAAc;oEAAC;;;;;;;;;;;;;kEAGjC,uQAAC;wDAAI,WAAU;;4DACZ,YAAY,gBAAgB,kBAAI,uQAAC,mQAAW;gEAAC,WAAU;;;;;;0EACxD,uQAAC;0EAAM,YAAY,gBAAgB,GAAG,qBAAqB;;;;;;;;;;;;;;;;;;4CAK9D,YAAY,gBAAgB,CAAC,MAAM,GAAG,mBACrC,uQAAC;gDAAI,WAAU;;kEACb,uQAAC;wDAAI,WAAU;;0EACb,uQAAC,kQAAa;gEAAC,WAAU;;;;;;0EACzB,uQAAC;gEAAK,WAAU;0EAA2B;;;;;;;;;;;;kEAE7C,uQAAC;wDAAG,WAAU;kEACX,YAAY,gBAAgB,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACzC,uQAAC;gEAAe,WAAU;0EACxB,cAAA,uQAAC;;wEAAK;wEAAG;;;;;;;+DADF;;;;;;;;;;;;;;;;0DASjB,uQAAC;gDAAI,WAAU;;kEACb,uQAAC,qKAAM;wDACL,SAAS,IAAM,mBAAmB;wDAClC,WAAU;wDACV,UAAU,CAAC,YAAY,gBAAgB;;0EAEvC,uQAAC,mOAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGnC,uQAAC,qKAAM;wDACL,SAAQ;wDACR,SAAS,IAAM,gBAAgB,YAAY,MAAM;wDACjD,WAAU;;0EAEV,uQAAC,gOAAG;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;+BA3J7B,YAAY,MAAM;;;;;;;;;;oBAqKhC,qBAAqB,MAAM,KAAK,KAAK,CAAC,2BACrC,uQAAC,iKAAI;kCACH,cAAA,uQAAC,wKAAW;4BAAC,WAAU;;8CACrB,uQAAC,yOAAM;oCAAC,WAAU;;;;;;8CAClB,uQAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,uQAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAG1C,uQAAC,qKAAM;oCACL,SAAS;oCACT,UAAU;oCACV,WAAU;;sDAEV,uQAAC,yOAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnD", "debugId": null}}, {"offset": {"line": 5072, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/components/AlertCenter.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  Bell,\n  BellRing,\n  X,\n  Eye,\n  Play,\n  TrendingUp,\n  Zap,\n  Target,\n  AlertTriangle,\n  DollarSign,\n  Activity,\n  Clock,\n  CheckCircle\n} from 'lucide-react'\nimport { Alert, AlertType, alertSystem } from '@/lib/alertSystem'\nimport { formatCurrency, formatPercentage } from '@/lib/utils'\n\ninterface AlertCenterProps {\n  className?: string\n}\n\nexport function AlertCenter({ className }: AlertCenterProps) {\n  const [alerts, setAlerts] = useState<Alert[]>([])\n  const [isOpen, setIsOpen] = useState(false)\n  const [unreadCount, setUnreadCount] = useState(0)\n\n  useEffect(() => {\n    // Subscribe to alert updates\n    const unsubscribe = alertSystem.subscribe((updatedAlerts) => {\n      setAlerts(updatedAlerts)\n      setUnreadCount(alertSystem.getUnreadCount())\n    })\n\n    // Initial load\n    setAlerts(alertSystem.getAlerts())\n    setUnreadCount(alertSystem.getUnreadCount())\n\n    return unsubscribe\n  }, [])\n\n  const getAlertIcon = (type: AlertType) => {\n    switch (type) {\n      case 'new_catalyst': return <Zap className=\"h-4 w-4\" />\n      case 'perfect_pick_found': return <Target className=\"h-4 w-4\" />\n      case 'pre_market_gap': return <TrendingUp className=\"h-4 w-4\" />\n      case 'pmh_break': return <Activity className=\"h-4 w-4\" />\n      case 'stop_loss_hit': return <AlertTriangle className=\"h-4 w-4\" />\n      case 'profit_target_hit': return <DollarSign className=\"h-4 w-4\" />\n      case 'entry_trigger': return <Play className=\"h-4 w-4\" />\n      case 'volume_spike': return <Activity className=\"h-4 w-4\" />\n      default: return <Bell className=\"h-4 w-4\" />\n    }\n  }\n\n  const getAlertColor = (priority: string) => {\n    switch (priority) {\n      case 'critical': return 'text-red-600 bg-red-50 border-red-200'\n      case 'high': return 'text-orange-600 bg-orange-50 border-orange-200'\n      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200'\n      case 'low': return 'text-blue-600 bg-blue-50 border-blue-200'\n      default: return 'text-gray-600 bg-gray-50 border-gray-200'\n    }\n  }\n\n  const handleAlertAction = async (alert: Alert, actionId: string) => {\n    const action = alert.actions?.find(a => a.id === actionId)\n    if (!action) return\n\n    switch (action.type) {\n      case 'execute_trade':\n        console.log('Execute trade:', action.data)\n        // TODO: Implement trade execution\n        break\n      case 'view_chart':\n        console.log('View chart:', action.data)\n        // TODO: Implement chart viewing\n        break\n      case 'take_profit':\n        console.log('Take profit:', action.data)\n        // TODO: Implement profit taking\n        break\n      case 'update_stop':\n        console.log('Update stop:', action.data)\n        // TODO: Implement stop loss update\n        break\n      case 'dismiss':\n        alertSystem.removeAlert(alert.id)\n        break\n    }\n\n    // Mark alert as read after action\n    alertSystem.markAsRead(alert.id)\n  }\n\n  const formatTimeAgo = (timestamp: string) => {\n    const now = new Date()\n    const alertTime = new Date(timestamp)\n    const diffMs = now.getTime() - alertTime.getTime()\n    const diffMins = Math.floor(diffMs / 60000)\n    const diffHours = Math.floor(diffMins / 60)\n\n    if (diffMins < 1) return 'Just now'\n    if (diffMins < 60) return `${diffMins}m ago`\n    if (diffHours < 24) return `${diffHours}h ago`\n    return alertTime.toLocaleDateString()\n  }\n\n  return (\n    <div className={`relative ${className}`}>\n      {/* Alert Bell Button */}\n      <Button\n        variant=\"outline\"\n        size=\"sm\"\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"relative\"\n      >\n        {unreadCount > 0 ? (\n          <BellRing className=\"h-4 w-4\" />\n        ) : (\n          <Bell className=\"h-4 w-4\" />\n        )}\n        {unreadCount > 0 && (\n          <Badge className=\"absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs bg-red-500\">\n            {unreadCount > 99 ? '99+' : unreadCount}\n          </Badge>\n        )}\n      </Button>\n\n      {/* Alert Dropdown */}\n      {isOpen && (\n        <div className=\"absolute right-0 top-full mt-2 w-96 max-h-96 overflow-y-auto bg-white border rounded-lg shadow-lg z-50\">\n          <div className=\"p-4 border-b\">\n            <div className=\"flex items-center justify-between\">\n              <h3 className=\"font-semibold\">Alerts</h3>\n              <div className=\"flex items-center gap-2\">\n                {unreadCount > 0 && (\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={() => alertSystem.markAllAsRead()}\n                  >\n                    Mark all read\n                  </Button>\n                )}\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={() => setIsOpen(false)}\n                >\n                  <X className=\"h-4 w-4\" />\n                </Button>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"max-h-80 overflow-y-auto\">\n            {alerts.length === 0 ? (\n              <div className=\"p-8 text-center text-muted-foreground\">\n                <Bell className=\"h-8 w-8 mx-auto mb-2 opacity-50\" />\n                <p>No alerts yet</p>\n              </div>\n            ) : (\n              alerts.map((alert) => (\n                <div\n                  key={alert.id}\n                  className={`p-4 border-b hover:bg-gray-50 ${\n                    !alert.read ? 'bg-blue-50/50' : ''\n                  }`}\n                  onClick={() => !alert.read && alertSystem.markAsRead(alert.id)}\n                >\n                  <div className=\"flex items-start gap-3\">\n                    <div className={`p-2 rounded-full ${getAlertColor(alert.priority)}`}>\n                      {getAlertIcon(alert.type)}\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"flex items-center gap-2 mb-1\">\n                        <h4 className=\"font-medium text-sm truncate\">\n                          {alert.title}\n                        </h4>\n                        {!alert.read && (\n                          <div className=\"w-2 h-2 bg-blue-500 rounded-full flex-shrink-0\" />\n                        )}\n                      </div>\n                      <p className=\"text-sm text-muted-foreground mb-2\">\n                        {alert.message}\n                      </p>\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center gap-2 text-xs text-muted-foreground\">\n                          <Clock className=\"h-3 w-3\" />\n                          {formatTimeAgo(alert.timestamp)}\n                          <Badge variant=\"outline\" className=\"text-xs\">\n                            {alert.priority}\n                          </Badge>\n                        </div>\n                      </div>\n                      \n                      {/* Alert Actions */}\n                      {alert.actionable && alert.actions && alert.actions.length > 0 && (\n                        <div className=\"flex items-center gap-2 mt-2\">\n                          {alert.actions.map((action) => (\n                            <Button\n                              key={action.id}\n                              variant=\"outline\"\n                              size=\"sm\"\n                              onClick={(e) => {\n                                e.stopPropagation()\n                                handleAlertAction(alert, action.id)\n                              }}\n                              className=\"text-xs\"\n                            >\n                              {action.type === 'execute_trade' && <Play className=\"h-3 w-3 mr-1\" />}\n                              {action.type === 'view_chart' && <Eye className=\"h-3 w-3 mr-1\" />}\n                              {action.type === 'take_profit' && <DollarSign className=\"h-3 w-3 mr-1\" />}\n                              {action.label}\n                            </Button>\n                          ))}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ))\n            )}\n          </div>\n\n          {alerts.length > 0 && (\n            <div className=\"p-4 border-t\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => {\n                  alertSystem.clearAllAlerts()\n                  setIsOpen(false)\n                }}\n                className=\"w-full text-sm\"\n              >\n                Clear all alerts\n              </Button>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  )\n}\n\n// Alert Toast Component for critical alerts\nexport function AlertToast({ alert, onDismiss }: { alert: Alert; onDismiss: () => void }) {\n  useEffect(() => {\n    // Auto-dismiss after 10 seconds for non-critical alerts\n    if (alert.priority !== 'critical') {\n      const timer = setTimeout(onDismiss, 10000)\n      return () => clearTimeout(timer)\n    }\n  }, [alert.priority, onDismiss])\n\n  return (\n    <Card className={`fixed top-4 right-4 w-80 z-50 shadow-lg border-l-4 ${\n      alert.priority === 'critical' ? 'border-l-red-500' : \n      alert.priority === 'high' ? 'border-l-orange-500' : \n      'border-l-blue-500'\n    }`}>\n      <CardHeader className=\"pb-2\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-2\">\n            <div className={`p-1 rounded-full ${getAlertColor(alert.priority)}`}>\n              {getAlertIcon(alert.type)}\n            </div>\n            <CardTitle className=\"text-sm\">{alert.title}</CardTitle>\n          </div>\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={onDismiss}\n            className=\"h-6 w-6 p-0\"\n          >\n            <X className=\"h-3 w-3\" />\n          </Button>\n        </div>\n      </CardHeader>\n      <CardContent className=\"pt-0\">\n        <p className=\"text-sm text-muted-foreground mb-3\">\n          {alert.message}\n        </p>\n        {alert.actionable && alert.actions && (\n          <div className=\"flex gap-2\">\n            {alert.actions.slice(0, 2).map((action) => (\n              <Button\n                key={action.id}\n                variant={action.type === 'execute_trade' ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => {\n                  // Handle action\n                  console.log('Toast action:', action)\n                  onDismiss()\n                }}\n                className=\"text-xs\"\n              >\n                {action.label}\n              </Button>\n            ))}\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n\n  function getAlertColor(priority: string) {\n    switch (priority) {\n      case 'critical': return 'text-red-600 bg-red-50'\n      case 'high': return 'text-orange-600 bg-orange-50'\n      case 'medium': return 'text-yellow-600 bg-yellow-50'\n      case 'low': return 'text-blue-600 bg-blue-50'\n      default: return 'text-gray-600 bg-gray-50'\n    }\n  }\n\n  function getAlertIcon(type: AlertType) {\n    switch (type) {\n      case 'new_catalyst': return <Zap className=\"h-4 w-4\" />\n      case 'perfect_pick_found': return <Target className=\"h-4 w-4\" />\n      case 'pre_market_gap': return <TrendingUp className=\"h-4 w-4\" />\n      case 'pmh_break': return <Activity className=\"h-4 w-4\" />\n      case 'stop_loss_hit': return <AlertTriangle className=\"h-4 w-4\" />\n      case 'profit_target_hit': return <DollarSign className=\"h-4 w-4\" />\n      case 'entry_trigger': return <Play className=\"h-4 w-4\" />\n      case 'volume_spike': return <Activity className=\"h-4 w-4\" />\n      default: return <Bell className=\"h-4 w-4\" />\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AArBA;;;;;;;;AA4BO,SAAS,YAAY,EAAE,SAAS,EAAoB;IACzD,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,0OAAQ,EAAU,EAAE;IAChD,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,0OAAQ,EAAC;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,0OAAQ,EAAC;IAE/C,IAAA,2OAAS,EAAC;QACR,6BAA6B;QAC7B,MAAM,cAAc,iKAAW,CAAC,SAAS,CAAC,CAAC;YACzC,UAAU;YACV,eAAe,iKAAW,CAAC,cAAc;QAC3C;QAEA,eAAe;QACf,UAAU,iKAAW,CAAC,SAAS;QAC/B,eAAe,iKAAW,CAAC,cAAc;QAEzC,OAAO;IACT,GAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBAAgB,qBAAO,uQAAC,gOAAG;oBAAC,WAAU;;;;;;YAC3C,KAAK;gBAAsB,qBAAO,uQAAC,yOAAM;oBAAC,WAAU;;;;;;YACpD,KAAK;gBAAkB,qBAAO,uQAAC,yPAAU;oBAAC,WAAU;;;;;;YACpD,KAAK;gBAAa,qBAAO,uQAAC,+OAAQ;oBAAC,WAAU;;;;;;YAC7C,KAAK;gBAAiB,qBAAO,uQAAC,kQAAa;oBAAC,WAAU;;;;;;YACtD,KAAK;gBAAqB,qBAAO,uQAAC,yPAAU;oBAAC,WAAU;;;;;;YACvD,KAAK;gBAAiB,qBAAO,uQAAC,mOAAI;oBAAC,WAAU;;;;;;YAC7C,KAAK;gBAAgB,qBAAO,uQAAC,+OAAQ;oBAAC,WAAU;;;;;;YAChD;gBAAS,qBAAO,uQAAC,mOAAI;oBAAC,WAAU;;;;;;QAClC;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,oBAAoB,OAAO,OAAc;QAC7C,MAAM,SAAS,MAAM,OAAO,EAAE,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK;QACjD,IAAI,CAAC,QAAQ;QAEb,OAAQ,OAAO,IAAI;YACjB,KAAK;gBACH,QAAQ,GAAG,CAAC,kBAAkB,OAAO,IAAI;gBAEzC;YACF,KAAK;gBACH,QAAQ,GAAG,CAAC,eAAe,OAAO,IAAI;gBAEtC;YACF,KAAK;gBACH,QAAQ,GAAG,CAAC,gBAAgB,OAAO,IAAI;gBAEvC;YACF,KAAK;gBACH,QAAQ,GAAG,CAAC,gBAAgB,OAAO,IAAI;gBAEvC;YACF,KAAK;gBACH,iKAAW,CAAC,WAAW,CAAC,MAAM,EAAE;gBAChC;QACJ;QAEA,kCAAkC;QAClC,iKAAW,CAAC,UAAU,CAAC,MAAM,EAAE;IACjC;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,MAAM,IAAI;QAChB,MAAM,YAAY,IAAI,KAAK;QAC3B,MAAM,SAAS,IAAI,OAAO,KAAK,UAAU,OAAO;QAChD,MAAM,WAAW,KAAK,KAAK,CAAC,SAAS;QACrC,MAAM,YAAY,KAAK,KAAK,CAAC,WAAW;QAExC,IAAI,WAAW,GAAG,OAAO;QACzB,IAAI,WAAW,IAAI,OAAO,GAAG,SAAS,KAAK,CAAC;QAC5C,IAAI,YAAY,IAAI,OAAO,GAAG,UAAU,KAAK,CAAC;QAC9C,OAAO,UAAU,kBAAkB;IACrC;IAEA,qBACE,uQAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;;0BAErC,uQAAC,qKAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;oBAET,cAAc,kBACb,uQAAC,mPAAQ;wBAAC,WAAU;;;;;6CAEpB,uQAAC,mOAAI;wBAAC,WAAU;;;;;;oBAEjB,cAAc,mBACb,uQAAC,mKAAK;wBAAC,WAAU;kCACd,cAAc,KAAK,QAAQ;;;;;;;;;;;;YAMjC,wBACC,uQAAC;gBAAI,WAAU;;kCACb,uQAAC;wBAAI,WAAU;kCACb,cAAA,uQAAC;4BAAI,WAAU;;8CACb,uQAAC;oCAAG,WAAU;8CAAgB;;;;;;8CAC9B,uQAAC;oCAAI,WAAU;;wCACZ,cAAc,mBACb,uQAAC,qKAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,iKAAW,CAAC,aAAa;sDACzC;;;;;;sDAIH,uQAAC,qKAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,UAAU;sDAEzB,cAAA,uQAAC,0NAAC;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMrB,uQAAC;wBAAI,WAAU;kCACZ,OAAO,MAAM,KAAK,kBACjB,uQAAC;4BAAI,WAAU;;8CACb,uQAAC,mOAAI;oCAAC,WAAU;;;;;;8CAChB,uQAAC;8CAAE;;;;;;;;;;;mCAGL,OAAO,GAAG,CAAC,CAAC,sBACV,uQAAC;gCAEC,WAAW,CAAC,8BAA8B,EACxC,CAAC,MAAM,IAAI,GAAG,kBAAkB,IAChC;gCACF,SAAS,IAAM,CAAC,MAAM,IAAI,IAAI,iKAAW,CAAC,UAAU,CAAC,MAAM,EAAE;0CAE7D,cAAA,uQAAC;oCAAI,WAAU;;sDACb,uQAAC;4CAAI,WAAW,CAAC,iBAAiB,EAAE,cAAc,MAAM,QAAQ,GAAG;sDAChE,aAAa,MAAM,IAAI;;;;;;sDAE1B,uQAAC;4CAAI,WAAU;;8DACb,uQAAC;oDAAI,WAAU;;sEACb,uQAAC;4DAAG,WAAU;sEACX,MAAM,KAAK;;;;;;wDAEb,CAAC,MAAM,IAAI,kBACV,uQAAC;4DAAI,WAAU;;;;;;;;;;;;8DAGnB,uQAAC;oDAAE,WAAU;8DACV,MAAM,OAAO;;;;;;8DAEhB,uQAAC;oDAAI,WAAU;8DACb,cAAA,uQAAC;wDAAI,WAAU;;0EACb,uQAAC,sOAAK;gEAAC,WAAU;;;;;;4DAChB,cAAc,MAAM,SAAS;0EAC9B,uQAAC,mKAAK;gEAAC,SAAQ;gEAAU,WAAU;0EAChC,MAAM,QAAQ;;;;;;;;;;;;;;;;;gDAMpB,MAAM,UAAU,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,MAAM,GAAG,mBAC3D,uQAAC;oDAAI,WAAU;8DACZ,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,uBAClB,uQAAC,qKAAM;4DAEL,SAAQ;4DACR,MAAK;4DACL,SAAS,CAAC;gEACR,EAAE,eAAe;gEACjB,kBAAkB,OAAO,OAAO,EAAE;4DACpC;4DACA,WAAU;;gEAET,OAAO,IAAI,KAAK,iCAAmB,uQAAC,mOAAI;oEAAC,WAAU;;;;;;gEACnD,OAAO,IAAI,KAAK,8BAAgB,uQAAC,gOAAG;oEAAC,WAAU;;;;;;gEAC/C,OAAO,IAAI,KAAK,+BAAiB,uQAAC,yPAAU;oEAAC,WAAU;;;;;;gEACvD,OAAO,KAAK;;2DAZR,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;+BArCrB,MAAM,EAAE;;;;;;;;;;oBA6DpB,OAAO,MAAM,GAAG,mBACf,uQAAC;wBAAI,WAAU;kCACb,cAAA,uQAAC,qKAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;gCACP,iKAAW,CAAC,cAAc;gCAC1B,UAAU;4BACZ;4BACA,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AASf;AAGO,SAAS,WAAW,EAAE,KAAK,EAAE,SAAS,EAA2C;IACtF,IAAA,2OAAS,EAAC;QACR,wDAAwD;QACxD,IAAI,MAAM,QAAQ,KAAK,YAAY;YACjC,MAAM,QAAQ,WAAW,WAAW;YACpC,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC,MAAM,QAAQ;QAAE;KAAU;IAE9B,qBACE,uQAAC,iKAAI;QAAC,WAAW,CAAC,mDAAmD,EACnE,MAAM,QAAQ,KAAK,aAAa,qBAChC,MAAM,QAAQ,KAAK,SAAS,wBAC5B,qBACA;;0BACA,uQAAC,uKAAU;gBAAC,WAAU;0BACpB,cAAA,uQAAC;oBAAI,WAAU;;sCACb,uQAAC;4BAAI,WAAU;;8CACb,uQAAC;oCAAI,WAAW,CAAC,iBAAiB,EAAE,cAAc,MAAM,QAAQ,GAAG;8CAChE,aAAa,MAAM,IAAI;;;;;;8CAE1B,uQAAC,sKAAS;oCAAC,WAAU;8CAAW,MAAM,KAAK;;;;;;;;;;;;sCAE7C,uQAAC,qKAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,uQAAC,0NAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAInB,uQAAC,wKAAW;gBAAC,WAAU;;kCACrB,uQAAC;wBAAE,WAAU;kCACV,MAAM,OAAO;;;;;;oBAEf,MAAM,UAAU,IAAI,MAAM,OAAO,kBAChC,uQAAC;wBAAI,WAAU;kCACZ,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,uBAC9B,uQAAC,qKAAM;gCAEL,SAAS,OAAO,IAAI,KAAK,kBAAkB,YAAY;gCACvD,MAAK;gCACL,SAAS;oCACP,gBAAgB;oCAChB,QAAQ,GAAG,CAAC,iBAAiB;oCAC7B;gCACF;gCACA,WAAU;0CAET,OAAO,KAAK;+BAVR,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;IAmB5B,SAAS,cAAc,QAAgB;QACrC,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,SAAS,aAAa,IAAe;QACnC,OAAQ;YACN,KAAK;gBAAgB,qBAAO,uQAAC,gOAAG;oBAAC,WAAU;;;;;;YAC3C,KAAK;gBAAsB,qBAAO,uQAAC,yOAAM;oBAAC,WAAU;;;;;;YACpD,KAAK;gBAAkB,qBAAO,uQAAC,yPAAU;oBAAC,WAAU;;;;;;YACpD,KAAK;gBAAa,qBAAO,uQAAC,+OAAQ;oBAAC,WAAU;;;;;;YAC7C,KAAK;gBAAiB,qBAAO,uQAAC,kQAAa;oBAAC,WAAU;;;;;;YACtD,KAAK;gBAAqB,qBAAO,uQAAC,yPAAU;oBAAC,WAAU;;;;;;YACvD,KAAK;gBAAiB,qBAAO,uQAAC,mOAAI;oBAAC,WAAU;;;;;;YAC7C,KAAK;gBAAgB,qBAAO,uQAAC,+OAAQ;oBAAC,WAAU;;;;;;YAChD;gBAAS,qBAAO,uQAAC,mOAAI;oBAAC,WAAU;;;;;;QAClC;IACF;AACF", "debugId": null}}, {"offset": {"line": 5761, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/components/AIConfiguration.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { \n  Brain, \n  Settings, \n  CheckCircle, \n  XCircle, \n  Loader2,\n  Sparkles,\n  AlertTriangle,\n  Info\n} from 'lucide-react';\n\ninterface AIStatus {\n  enabled: boolean;\n  model: string | null;\n  features: {\n    marketCommentary: boolean;\n    riskAssessment: boolean;\n    tradingRecommendations: boolean;\n  };\n}\n\nexport default function AIConfiguration() {\n  const [aiStatus, setAiStatus] = useState<AIStatus | null>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    checkAIStatus();\n  }, []);\n\n  const checkAIStatus = async () => {\n    setIsLoading(true);\n    setError(null);\n    \n    try {\n      const response = await fetch('/api/ai?action=status');\n      const status = await response.json();\n      setAiStatus(status);\n    } catch (error) {\n      console.error('Error checking AI status:', error);\n      setError('Unable to connect to AI service');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const testAIConnection = async () => {\n    setIsLoading(true);\n    setError(null);\n    \n    try {\n      const response = await fetch('/api/ai?action=model');\n      if (response.ok) {\n        const { model } = await response.json();\n        setAiStatus(prev => prev ? { ...prev, model } : null);\n        alert(`AI Connection Successful!\\nUsing model: ${model}`);\n      } else {\n        throw new Error('Failed to connect to AI service');\n      }\n    } catch (error) {\n      console.error('Error testing AI connection:', error);\n      setError('AI connection test failed');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  if (isLoading && !aiStatus) {\n    return (\n      <Card className=\"bg-slate-800/50 border-slate-700\">\n        <CardContent className=\"p-6\">\n          <div className=\"flex items-center justify-center\">\n            <Loader2 className=\"h-6 w-6 animate-spin text-blue-400 mr-2\" />\n            <span className=\"text-slate-300\">Checking AI configuration...</span>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* AI Status Overview */}\n      <Card className=\"bg-slate-800/50 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"text-white flex items-center justify-between\">\n            <div className=\"flex items-center\">\n              <Brain className=\"mr-2 h-5 w-5 text-blue-400\" />\n              AI Configuration\n            </div>\n            <Button\n              size=\"sm\"\n              variant=\"outline\"\n              onClick={checkAIStatus}\n              disabled={isLoading}\n              className=\"text-slate-300 hover:text-white\"\n            >\n              {isLoading ? (\n                <Loader2 className=\"h-4 w-4 animate-spin mr-1\" />\n              ) : (\n                <Settings className=\"h-4 w-4 mr-1\" />\n              )}\n              Refresh Status\n            </Button>\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          {error && (\n            <div className=\"mb-4 p-3 bg-red-900/20 border border-red-500/30 rounded-lg\">\n              <div className=\"flex items-center text-red-400\">\n                <AlertTriangle className=\"h-4 w-4 mr-2\" />\n                {error}\n              </div>\n            </div>\n          )}\n\n          {aiStatus && (\n            <div className=\"space-y-4\">\n              {/* Status Overview */}\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                <div className=\"text-center p-4 bg-slate-700/30 rounded-lg\">\n                  <div className=\"flex items-center justify-center mb-2\">\n                    {aiStatus.enabled ? (\n                      <CheckCircle className=\"h-6 w-6 text-green-400\" />\n                    ) : (\n                      <XCircle className=\"h-6 w-6 text-red-400\" />\n                    )}\n                  </div>\n                  <div className=\"text-sm text-slate-400\">Status</div>\n                  <div className={`font-semibold ${aiStatus.enabled ? 'text-green-400' : 'text-red-400'}`}>\n                    {aiStatus.enabled ? 'Enabled' : 'Disabled'}\n                  </div>\n                </div>\n\n                <div className=\"text-center p-4 bg-slate-700/30 rounded-lg\">\n                  <div className=\"flex items-center justify-center mb-2\">\n                    <Sparkles className=\"h-6 w-6 text-blue-400\" />\n                  </div>\n                  <div className=\"text-sm text-slate-400\">Model</div>\n                  <div className=\"font-semibold text-white\">\n                    {aiStatus.model || 'Not Available'}\n                  </div>\n                </div>\n\n                <div className=\"text-center p-4 bg-slate-700/30 rounded-lg\">\n                  <Button\n                    onClick={testAIConnection}\n                    disabled={!aiStatus.enabled || isLoading}\n                    className=\"w-full bg-blue-600 hover:bg-blue-700\"\n                  >\n                    {isLoading ? (\n                      <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\n                    ) : (\n                      <CheckCircle className=\"h-4 w-4 mr-2\" />\n                    )}\n                    Test Connection\n                  </Button>\n                </div>\n              </div>\n\n              {/* Feature Status */}\n              <div>\n                <h3 className=\"text-lg font-semibold text-white mb-3 flex items-center\">\n                  <Settings className=\"h-5 w-5 mr-2\" />\n                  Available Features\n                </h3>\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  <div className=\"p-3 bg-slate-700/20 rounded-lg\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <span className=\"text-white font-medium\">Market Commentary</span>\n                      <Badge className={aiStatus.features.marketCommentary && aiStatus.enabled \n                        ? 'bg-green-500/20 text-green-400' \n                        : 'bg-gray-500/20 text-gray-400'\n                      }>\n                        {aiStatus.features.marketCommentary && aiStatus.enabled ? 'Active' : 'Inactive'}\n                      </Badge>\n                    </div>\n                    <p className=\"text-xs text-slate-400\">\n                      AI-powered market analysis and commentary on scan results\n                    </p>\n                  </div>\n\n                  <div className=\"p-3 bg-slate-700/20 rounded-lg\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <span className=\"text-white font-medium\">Risk Assessment</span>\n                      <Badge className={aiStatus.features.riskAssessment && aiStatus.enabled \n                        ? 'bg-green-500/20 text-green-400' \n                        : 'bg-gray-500/20 text-gray-400'\n                      }>\n                        {aiStatus.features.riskAssessment && aiStatus.enabled ? 'Active' : 'Inactive'}\n                      </Badge>\n                    </div>\n                    <p className=\"text-xs text-slate-400\">\n                      Individual setup risk analysis with AI-generated recommendations\n                    </p>\n                  </div>\n\n                  <div className=\"p-3 bg-slate-700/20 rounded-lg\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <span className=\"text-white font-medium\">Trading Recommendations</span>\n                      <Badge className={aiStatus.features.tradingRecommendations && aiStatus.enabled \n                        ? 'bg-green-500/20 text-green-400' \n                        : 'bg-gray-500/20 text-gray-400'\n                      }>\n                        {aiStatus.features.tradingRecommendations && aiStatus.enabled ? 'Active' : 'Inactive'}\n                      </Badge>\n                    </div>\n                    <p className=\"text-xs text-slate-400\">\n                      Personalized trading recommendations based on your preferences\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              {/* Configuration Instructions */}\n              {!aiStatus.enabled && (\n                <div className=\"p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg\">\n                  <h4 className=\"text-white font-semibold mb-2 flex items-center\">\n                    <Info className=\"h-4 w-4 mr-2\" />\n                    Enable AI Features\n                  </h4>\n                  <div className=\"text-sm text-slate-300 space-y-2\">\n                    <p>To enable AI-powered insights:</p>\n                    <ol className=\"list-decimal list-inside space-y-1 ml-4\">\n                      <li>Ensure OPENAI_API_KEY is set in your .env.local file</li>\n                      <li>Set OPENAI_ENABLED=true in your environment variables</li>\n                      <li>Restart the application</li>\n                      <li>Click \"Refresh Status\" to verify the configuration</li>\n                    </ol>\n                  </div>\n                </div>\n              )}\n\n              {/* Success Message */}\n              {aiStatus.enabled && (\n                <div className=\"p-4 bg-green-900/20 border border-green-500/30 rounded-lg\">\n                  <h4 className=\"text-green-400 font-semibold mb-2 flex items-center\">\n                    <CheckCircle className=\"h-4 w-4 mr-2\" />\n                    AI Features Active\n                  </h4>\n                  <p className=\"text-sm text-slate-300\">\n                    Your SwingTrader AI is enhanced with GPT-4o intelligence. You'll see AI-powered insights \n                    in your trading setup cards and market analysis sections.\n                  </p>\n                </div>\n              )}\n            </div>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AA2Be,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,0OAAQ,EAAkB;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,0OAAQ,EAAC;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,0OAAQ,EAAgB;IAElD,IAAA,2OAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB;QACvB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI;gBACrC,YAAY,CAAA,OAAQ,OAAO;wBAAE,GAAG,IAAI;wBAAE;oBAAM,IAAI;gBAChD,MAAM,CAAC,wCAAwC,EAAE,OAAO;YAC1D,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,aAAa,CAAC,UAAU;QAC1B,qBACE,uQAAC,iKAAI;YAAC,WAAU;sBACd,cAAA,uQAAC,wKAAW;gBAAC,WAAU;0BACrB,cAAA,uQAAC;oBAAI,WAAU;;sCACb,uQAAC,qPAAO;4BAAC,WAAU;;;;;;sCACnB,uQAAC;4BAAK,WAAU;sCAAiB;;;;;;;;;;;;;;;;;;;;;;IAK3C;IAEA,qBACE,uQAAC;QAAI,WAAU;kBAEb,cAAA,uQAAC,iKAAI;YAAC,WAAU;;8BACd,uQAAC,uKAAU;8BACT,cAAA,uQAAC,sKAAS;wBAAC,WAAU;;0CACnB,uQAAC;gCAAI,WAAU;;kDACb,uQAAC,sOAAK;wCAAC,WAAU;;;;;;oCAA+B;;;;;;;0CAGlD,uQAAC,qKAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU;gCACV,WAAU;;oCAET,0BACC,uQAAC,qPAAO;wCAAC,WAAU;;;;;6DAEnB,uQAAC,+OAAQ;wCAAC,WAAU;;;;;;oCACpB;;;;;;;;;;;;;;;;;;8BAKR,uQAAC,wKAAW;;wBACT,uBACC,uQAAC;4BAAI,WAAU;sCACb,cAAA,uQAAC;gCAAI,WAAU;;kDACb,uQAAC,kQAAa;wCAAC,WAAU;;;;;;oCACxB;;;;;;;;;;;;wBAKN,0BACC,uQAAC;4BAAI,WAAU;;8CAEb,uQAAC;oCAAI,WAAU;;sDACb,uQAAC;4CAAI,WAAU;;8DACb,uQAAC;oDAAI,WAAU;8DACZ,SAAS,OAAO,iBACf,uQAAC,mQAAW;wDAAC,WAAU;;;;;6EAEvB,uQAAC,gPAAO;wDAAC,WAAU;;;;;;;;;;;8DAGvB,uQAAC;oDAAI,WAAU;8DAAyB;;;;;;8DACxC,uQAAC;oDAAI,WAAW,CAAC,cAAc,EAAE,SAAS,OAAO,GAAG,mBAAmB,gBAAgB;8DACpF,SAAS,OAAO,GAAG,YAAY;;;;;;;;;;;;sDAIpC,uQAAC;4CAAI,WAAU;;8DACb,uQAAC;oDAAI,WAAU;8DACb,cAAA,uQAAC,+OAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,uQAAC;oDAAI,WAAU;8DAAyB;;;;;;8DACxC,uQAAC;oDAAI,WAAU;8DACZ,SAAS,KAAK,IAAI;;;;;;;;;;;;sDAIvB,uQAAC;4CAAI,WAAU;sDACb,cAAA,uQAAC,qKAAM;gDACL,SAAS;gDACT,UAAU,CAAC,SAAS,OAAO,IAAI;gDAC/B,WAAU;;oDAET,0BACC,uQAAC,qPAAO;wDAAC,WAAU;;;;;6EAEnB,uQAAC,mQAAW;wDAAC,WAAU;;;;;;oDACvB;;;;;;;;;;;;;;;;;;8CAOR,uQAAC;;sDACC,uQAAC;4CAAG,WAAU;;8DACZ,uQAAC,+OAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,uQAAC;4CAAI,WAAU;;8DACb,uQAAC;oDAAI,WAAU;;sEACb,uQAAC;4DAAI,WAAU;;8EACb,uQAAC;oEAAK,WAAU;8EAAyB;;;;;;8EACzC,uQAAC,mKAAK;oEAAC,WAAW,SAAS,QAAQ,CAAC,gBAAgB,IAAI,SAAS,OAAO,GACpE,mCACA;8EAED,SAAS,QAAQ,CAAC,gBAAgB,IAAI,SAAS,OAAO,GAAG,WAAW;;;;;;;;;;;;sEAGzE,uQAAC;4DAAE,WAAU;sEAAyB;;;;;;;;;;;;8DAKxC,uQAAC;oDAAI,WAAU;;sEACb,uQAAC;4DAAI,WAAU;;8EACb,uQAAC;oEAAK,WAAU;8EAAyB;;;;;;8EACzC,uQAAC,mKAAK;oEAAC,WAAW,SAAS,QAAQ,CAAC,cAAc,IAAI,SAAS,OAAO,GAClE,mCACA;8EAED,SAAS,QAAQ,CAAC,cAAc,IAAI,SAAS,OAAO,GAAG,WAAW;;;;;;;;;;;;sEAGvE,uQAAC;4DAAE,WAAU;sEAAyB;;;;;;;;;;;;8DAKxC,uQAAC;oDAAI,WAAU;;sEACb,uQAAC;4DAAI,WAAU;;8EACb,uQAAC;oEAAK,WAAU;8EAAyB;;;;;;8EACzC,uQAAC,mKAAK;oEAAC,WAAW,SAAS,QAAQ,CAAC,sBAAsB,IAAI,SAAS,OAAO,GAC1E,mCACA;8EAED,SAAS,QAAQ,CAAC,sBAAsB,IAAI,SAAS,OAAO,GAAG,WAAW;;;;;;;;;;;;sEAG/E,uQAAC;4DAAE,WAAU;sEAAyB;;;;;;;;;;;;;;;;;;;;;;;;gCAQ3C,CAAC,SAAS,OAAO,kBAChB,uQAAC;oCAAI,WAAU;;sDACb,uQAAC;4CAAG,WAAU;;8DACZ,uQAAC,mOAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,uQAAC;4CAAI,WAAU;;8DACb,uQAAC;8DAAE;;;;;;8DACH,uQAAC;oDAAG,WAAU;;sEACZ,uQAAC;sEAAG;;;;;;sEACJ,uQAAC;sEAAG;;;;;;sEACJ,uQAAC;sEAAG;;;;;;sEACJ,uQAAC;sEAAG;;;;;;;;;;;;;;;;;;;;;;;;gCAOX,SAAS,OAAO,kBACf,uQAAC;oCAAI,WAAU;;sDACb,uQAAC;4CAAG,WAAU;;8DACZ,uQAAC,mQAAW;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAG1C,uQAAC;4CAAE,WAAU;sDAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYxD", "debugId": null}}, {"offset": {"line": 6387, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { <PERSON><PERSON>dingUp, BarChart3, Target, Shield, Brain, Zap, Search, Loader2, Scan } from 'lucide-react'\nimport { <PERSON><PERSON> } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { SwingTradingAnalysis, StockData } from '@/types/trading'\nimport { formatCurrency, formatPercentage } from '@/lib/utils'\nimport { SwingScanner } from '@/components/SwingScanner'\nimport { StrategyScanner } from '@/components/StrategyScanner'\nimport { EventDrivenScanner } from '@/components/EventDrivenScanner'\nimport { AlertCenter } from '@/components/AlertCenter'\nimport AIConfiguration from '@/components/AIConfiguration'\n// import TradingInterface from '@/components/TradingInterface'\n\nexport default function Home() {\n  const [selectedSymbol, setSelectedSymbol] = useState('SPY')\n  const [customSymbol, setCustomSymbol] = useState('')\n  const [isAnalyzing, setIsAnalyzing] = useState(false)\n  const [analysis, setAnalysis] = useState<SwingTradingAnalysis | null>(null)\n  const [stockData, setStockData] = useState<StockData | null>(null)\n  const [error, setError] = useState<string | null>(null)\n  const [activeTab, setActiveTab] = useState<'individual' | 'scanner' | 'strategies' | 'event-driven' | 'trading' | 'ai'>('event-driven')\n\n  const popularSymbols = ['SPY', 'QQQ', 'AAPL', 'TSLA', 'NVDA', 'MSFT', 'AMZN', 'GOOGL']\n\n  const handleAnalysis = async (symbol: string) => {\n    setIsAnalyzing(true)\n    setError(null)\n    setAnalysis(null)\n    setStockData(null)\n\n    try {\n      // Fetch stock quote and analysis in parallel\n      const [quoteResponse, analysisResponse] = await Promise.all([\n        fetch(`/api/stocks/quote/${symbol}`),\n        fetch(`/api/analysis/swing/${symbol}`)\n      ])\n\n      if (!quoteResponse.ok || !analysisResponse.ok) {\n        throw new Error('Failed to fetch data')\n      }\n\n      const [quoteData, analysisData] = await Promise.all([\n        quoteResponse.json(),\n        analysisResponse.json()\n      ])\n\n      setStockData(quoteData)\n      setAnalysis(analysisData)\n    } catch (err) {\n      setError('Failed to analyze stock. Please try again.')\n      console.error('Analysis error:', err)\n    } finally {\n      setIsAnalyzing(false)\n    }\n  }\n\n  const handleCustomSymbolSubmit = (e: React.FormEvent) => {\n    e.preventDefault()\n    if (customSymbol.trim()) {\n      handleAnalysis(customSymbol.toUpperCase())\n      setSelectedSymbol(customSymbol.toUpperCase())\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900\">\n      {/* Header */}\n      <header className=\"border-b border-slate-800 bg-slate-900/50 backdrop-blur-sm\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-2\">\n              <Brain className=\"h-8 w-8 text-blue-400\" />\n              <h1 className=\"text-2xl font-bold text-white\">SwingTrader AI</h1>\n            </div>\n            <nav className=\"hidden md:flex items-center space-x-6\">\n              <button\n                onClick={() => setActiveTab('strategies')}\n                className={`transition-colors ${activeTab === 'strategies' ? 'text-white' : 'text-slate-300 hover:text-white'}`}\n              >\n                Pro Strategies\n              </button>\n              <button\n                onClick={() => setActiveTab('scanner')}\n                className={`transition-colors ${activeTab === 'scanner' ? 'text-white' : 'text-slate-300 hover:text-white'}`}\n              >\n                Basic Scanner\n              </button>\n              <button\n                onClick={() => setActiveTab('individual')}\n                className={`transition-colors ${activeTab === 'individual' ? 'text-white' : 'text-slate-300 hover:text-white'}`}\n              >\n                Individual Analysis\n              </button>\n              <button\n                onClick={() => setActiveTab('trading')}\n                className={`transition-colors ${activeTab === 'trading' ? 'text-white' : 'text-slate-300 hover:text-white'}`}\n              >\n                Paper Trading\n              </button>\n              <AlertCenter />\n              <Button variant=\"outline\" className=\"border-blue-400 text-blue-400 hover:bg-blue-400 hover:text-white\">\n                Sign In\n              </Button>\n            </nav>\n          </div>\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <section className=\"py-20 px-4\">\n        <div className=\"container mx-auto text-center\">\n          <h2 className=\"text-5xl font-bold text-white mb-6\">\n            AI-Powered Swing Trading Analysis\n          </h2>\n          <p className=\"text-xl text-slate-300 mb-8 max-w-3xl mx-auto\">\n            Professional swing trading strategies with automated scanning, precise entry/exit rules,\n            and risk management based on proven methodologies.\n          </p>\n\n          {/* Tab Navigation */}\n          <div className=\"flex justify-center mb-8\">\n            <div className=\"bg-slate-800/50 rounded-lg p-1 flex\">\n              <button\n                onClick={() => setActiveTab('event-driven')}\n                className={`px-6 py-3 rounded-md transition-all ${\n                  activeTab === 'event-driven'\n                    ? 'bg-blue-600 text-white'\n                    : 'text-slate-300 hover:text-white hover:bg-slate-700/50'\n                }`}\n              >\n                <Target className=\"inline mr-2 h-4 w-4\" />\n                Event-Driven\n              </button>\n              <button\n                onClick={() => setActiveTab('strategies')}\n                className={`px-6 py-3 rounded-md transition-all ${\n                  activeTab === 'strategies'\n                    ? 'bg-blue-600 text-white'\n                    : 'text-slate-300 hover:text-white hover:bg-slate-700/50'\n                }`}\n              >\n                <Zap className=\"inline mr-2 h-4 w-4\" />\n                Pro Strategies\n              </button>\n              <button\n                onClick={() => setActiveTab('scanner')}\n                className={`px-6 py-3 rounded-md transition-all ${\n                  activeTab === 'scanner'\n                    ? 'bg-blue-600 text-white'\n                    : 'text-slate-300 hover:text-white hover:bg-slate-700/50'\n                }`}\n              >\n                <Scan className=\"inline mr-2 h-4 w-4\" />\n                Basic Scanner\n              </button>\n              <button\n                onClick={() => setActiveTab('individual')}\n                className={`px-6 py-3 rounded-md transition-all ${\n                  activeTab === 'individual'\n                    ? 'bg-blue-600 text-white'\n                    : 'text-slate-300 hover:text-white hover:bg-slate-700/50'\n                }`}\n              >\n                <Search className=\"inline mr-2 h-4 w-4\" />\n                Individual Analysis\n              </button>\n              <button\n                onClick={() => setActiveTab('trading')}\n                className={`px-6 py-3 rounded-md transition-all ${\n                  activeTab === 'trading'\n                    ? 'bg-blue-600 text-white'\n                    : 'text-slate-300 hover:text-white hover:bg-slate-700/50'\n                }`}\n              >\n                <TrendingUp className=\"inline mr-2 h-4 w-4\" />\n                Trading\n              </button>\n              <button\n                onClick={() => setActiveTab('ai')}\n                className={`px-6 py-3 rounded-md transition-all ${\n                  activeTab === 'ai'\n                    ? 'bg-blue-600 text-white'\n                    : 'text-slate-300 hover:text-white hover:bg-slate-700/50'\n                }`}\n              >\n                <Brain className=\"inline mr-2 h-4 w-4\" />\n                AI Config\n              </button>\n            </div>\n          </div>\n\n          {/* Content based on active tab */}\n          {activeTab === 'event-driven' ? (\n            <div className=\"mb-8\">\n              <h3 className=\"text-lg text-slate-300 mb-4\">Event-Driven Catalyst Detection & Perfect-Pick Trading</h3>\n              <p className=\"text-slate-400 mb-6\">\n                Advanced catalyst detection with pre-market gap scanning, technical gate analysis, and Perfect-Pick setups\n              </p>\n            </div>\n          ) : activeTab === 'strategies' ? (\n            <div className=\"mb-8\">\n              <h3 className=\"text-lg text-slate-300 mb-4\">Professional Swing Trading Strategies</h3>\n              <p className=\"text-slate-400 mb-6\">\n                Overnight Momentum & Technical Breakout strategies with precise entry/exit rules and position sizing\n              </p>\n            </div>\n          ) : activeTab === 'scanner' ? (\n            <div className=\"mb-8\">\n              <h3 className=\"text-lg text-slate-300 mb-4\">Basic Swing Trading Scanner</h3>\n              <p className=\"text-slate-400 mb-6\">\n                General swing trading analysis with technical indicators and trend detection\n              </p>\n            </div>\n          ) : (\n            <div className=\"mb-8\">\n              <h3 className=\"text-lg text-slate-300 mb-4\">Individual Stock Analysis</h3>\n              <div className=\"flex flex-wrap justify-center gap-2 mb-6\">\n                {popularSymbols.map((symbol) => (\n                  <Button\n                    key={symbol}\n                    variant={selectedSymbol === symbol ? \"default\" : \"outline\"}\n                    onClick={() => {\n                      setSelectedSymbol(symbol)\n                      handleAnalysis(symbol)\n                    }}\n                    disabled={isAnalyzing}\n                    className={selectedSymbol === symbol\n                      ? \"bg-blue-600 hover:bg-blue-700\"\n                      : \"border-slate-600 text-slate-300 hover:bg-slate-800\"\n                    }\n                  >\n                    {symbol}\n                  </Button>\n                ))}\n              </div>\n\n              {/* Custom Symbol Input */}\n              <form onSubmit={handleCustomSymbolSubmit} className=\"flex justify-center gap-2 mb-6\">\n                <div className=\"relative\">\n                  <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                  <input\n                    type=\"text\"\n                    placeholder=\"Enter symbol (e.g., AAPL)\"\n                    value={customSymbol}\n                    onChange={(e) => setCustomSymbol(e.target.value)}\n                    className=\"pl-10 pr-4 py-2 bg-slate-800 border border-slate-600 rounded-md text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    disabled={isAnalyzing}\n                  />\n                </div>\n                <Button\n                  type=\"submit\"\n                  disabled={isAnalyzing || !customSymbol.trim()}\n                  className=\"bg-blue-600 hover:bg-blue-700\"\n                >\n                  Analyze\n                </Button>\n              </form>\n\n              <Button\n                size=\"lg\"\n                onClick={() => handleAnalysis(selectedSymbol)}\n                disabled={isAnalyzing}\n                className=\"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3\"\n              >\n                {isAnalyzing ? (\n                  <>\n                    <Loader2 className=\"mr-2 h-5 w-5 animate-spin\" />\n                    Analyzing {selectedSymbol}...\n                  </>\n                ) : (\n                  <>\n                    <Zap className=\"mr-2 h-5 w-5\" />\n                    Get AI Analysis for {selectedSymbol}\n                  </>\n                )}\n              </Button>\n            </div>\n          )}\n        </div>\n      </section>\n\n      {/* Event-Driven Scanner Section */}\n      {activeTab === 'event-driven' && (\n        <section className=\"py-12 px-4\">\n          <div className=\"container mx-auto\">\n            <EventDrivenScanner accountSize={100000} riskPercent={2} />\n          </div>\n        </section>\n      )}\n\n      {/* Strategy Scanner Section */}\n      {activeTab === 'strategies' && (\n        <section className=\"py-12 px-4\">\n          <div className=\"container mx-auto\">\n            <StrategyScanner autoScan={true} accountSize={100000} />\n          </div>\n        </section>\n      )}\n\n      {/* Basic Scanner Section */}\n      {activeTab === 'scanner' && (\n        <section className=\"py-12 px-4\">\n          <div className=\"container mx-auto\">\n            <SwingScanner autoScan={false} />\n          </div>\n        </section>\n      )}\n\n      {/* Trading Section */}\n      {activeTab === 'trading' && (\n        <section className=\"py-12 px-4\">\n          <div className=\"container mx-auto\">\n            <div className=\"mb-8\">\n              <h2 className=\"text-3xl font-bold text-white mb-4\">Paper Trading</h2>\n              <p className=\"text-slate-400 mb-6\">\n                Execute swing trades with Alpaca or Interactive Brokers paper trading accounts\n              </p>\n            </div>\n            <div className=\"text-center text-slate-400\">\n              <h3 className=\"text-xl mb-4\">Paper Trading Interface</h3>\n              <p>IBKR and Alpaca integration is ready!</p>\n              <p className=\"mt-2\">Trading interface temporarily disabled due to component loading issue.</p>\n              <p className=\"mt-2\">API endpoints are working: <code>/api/trading</code></p>\n            </div>\n          </div>\n        </section>\n      )}\n\n      {/* AI Configuration Section */}\n      {activeTab === 'ai' && (\n        <section className=\"py-12 px-4\">\n          <div className=\"container mx-auto\">\n            <div className=\"mb-8\">\n              <h2 className=\"text-3xl font-bold text-white mb-4\">AI Configuration</h2>\n              <p className=\"text-slate-400 mb-6\">\n                Configure and manage AI-powered features for enhanced trading analysis\n              </p>\n            </div>\n            <AIConfiguration />\n          </div>\n        </section>\n      )}\n\n      {/* Error Display */}\n      {error && (\n        <section className=\"py-8 px-4\">\n          <div className=\"container mx-auto\">\n            <Card className=\"bg-red-900/20 border-red-500/50\">\n              <CardContent className=\"p-6\">\n                <p className=\"text-red-300 text-center\">{error}</p>\n              </CardContent>\n            </Card>\n          </div>\n        </section>\n      )}\n\n      {/* Analysis Results */}\n      {activeTab === 'individual' && (stockData || analysis) && (\n        <section className=\"py-12 px-4\">\n          <div className=\"container mx-auto\">\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n\n              {/* Stock Quote Card */}\n              {stockData && (\n                <Card className=\"bg-slate-800/50 border-slate-700\">\n                  <CardHeader>\n                    <CardTitle className=\"text-white flex items-center\">\n                      <BarChart3 className=\"mr-2 h-5 w-5 text-blue-400\" />\n                      {stockData.symbol} Quote\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-3\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Price:</span>\n                        <span className=\"text-white font-semibold\">{formatCurrency(stockData.price)}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Change:</span>\n                        <span className={stockData.change >= 0 ? \"text-green-400\" : \"text-red-400\"}>\n                          {formatCurrency(stockData.change)} ({formatPercentage(stockData.changePercent)})\n                        </span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Volume:</span>\n                        <span className=\"text-white\">{stockData.volume.toLocaleString()}</span>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n\n              {/* Trading Levels Card */}\n              {analysis && (\n                <Card className=\"bg-slate-800/50 border-slate-700\">\n                  <CardHeader>\n                    <CardTitle className=\"text-white flex items-center\">\n                      <Target className=\"mr-2 h-5 w-5 text-green-400\" />\n                      Trading Levels\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-3\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Entry:</span>\n                        <span className=\"text-white font-semibold\">{formatCurrency(analysis.entryPrice)}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Stop Loss:</span>\n                        <span className=\"text-red-400\">{formatCurrency(analysis.stopLoss)}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Take Profit:</span>\n                        <span className=\"text-green-400\">{formatCurrency(analysis.takeProfit)}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Risk/Reward:</span>\n                        <span className=\"text-blue-400 font-semibold\">{analysis.riskRewardRatio.toFixed(2)}:1</span>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n\n              {/* Analysis Summary Card */}\n              {analysis && (\n                <Card className=\"bg-slate-800/50 border-slate-700\">\n                  <CardHeader>\n                    <CardTitle className=\"text-white flex items-center\">\n                      <Brain className=\"mr-2 h-5 w-5 text-purple-400\" />\n                      AI Analysis\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-3\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Trend:</span>\n                        <span className={`font-semibold ${\n                          analysis.trend === 'BULLISH' ? 'text-green-400' :\n                          analysis.trend === 'BEARISH' ? 'text-red-400' : 'text-yellow-400'\n                        }`}>\n                          {analysis.trend}\n                        </span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Confidence:</span>\n                        <span className=\"text-white font-semibold\">{analysis.confidence.toFixed(1)}%</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-slate-300\">Recommendation:</span>\n                        <span className={`font-semibold ${\n                          analysis.recommendation.includes('BUY') ? 'text-green-400' :\n                          analysis.recommendation.includes('SELL') ? 'text-red-400' : 'text-yellow-400'\n                        }`}>\n                          {analysis.recommendation.replace('_', ' ')}\n                        </span>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n            </div>\n\n            {/* Technical Indicators */}\n            {analysis && (\n              <Card className=\"mt-6 bg-slate-800/50 border-slate-700\">\n                <CardHeader>\n                  <CardTitle className=\"text-white flex items-center\">\n                    <TrendingUp className=\"mr-2 h-5 w-5 text-orange-400\" />\n                    Technical Indicators\n                  </CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                    {analysis.indicators.map((indicator, index) => (\n                      <div key={index} className=\"p-4 bg-slate-700/50 rounded-lg\">\n                        <div className=\"flex items-center justify-between mb-2\">\n                          <h4 className=\"text-white font-medium\">{indicator.name}</h4>\n                          <span className={`px-2 py-1 rounded text-xs font-semibold ${\n                            indicator.signal === 'BUY' ? 'bg-green-500/20 text-green-400' :\n                            indicator.signal === 'SELL' ? 'bg-red-500/20 text-red-400' :\n                            'bg-yellow-500/20 text-yellow-400'\n                          }`}>\n                            {indicator.signal}\n                          </span>\n                        </div>\n                        <p className=\"text-slate-300 text-sm\">{indicator.description}</p>\n                      </div>\n                    ))}\n                  </div>\n                </CardContent>\n              </Card>\n            )}\n\n            {/* Support and Resistance Levels */}\n            {analysis && (analysis.supportLevels.length > 0 || analysis.resistanceLevels.length > 0) && (\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mt-6\">\n                {analysis.supportLevels.length > 0 && (\n                  <Card className=\"bg-slate-800/50 border-slate-700\">\n                    <CardHeader>\n                      <CardTitle className=\"text-white flex items-center\">\n                        <Shield className=\"mr-2 h-5 w-5 text-green-400\" />\n                        Support Levels\n                      </CardTitle>\n                    </CardHeader>\n                    <CardContent>\n                      <div className=\"space-y-2\">\n                        {analysis.supportLevels.map((level, index) => (\n                          <div key={index} className=\"flex justify-between\">\n                            <span className=\"text-slate-300\">Support {index + 1}:</span>\n                            <span className=\"text-green-400 font-semibold\">{formatCurrency(level)}</span>\n                          </div>\n                        ))}\n                      </div>\n                    </CardContent>\n                  </Card>\n                )}\n\n                {analysis.resistanceLevels.length > 0 && (\n                  <Card className=\"bg-slate-800/50 border-slate-700\">\n                    <CardHeader>\n                      <CardTitle className=\"text-white flex items-center\">\n                        <Shield className=\"mr-2 h-5 w-5 text-red-400\" />\n                        Resistance Levels\n                      </CardTitle>\n                    </CardHeader>\n                    <CardContent>\n                      <div className=\"space-y-2\">\n                        {analysis.resistanceLevels.map((level, index) => (\n                          <div key={index} className=\"flex justify-between\">\n                            <span className=\"text-slate-300\">Resistance {index + 1}:</span>\n                            <span className=\"text-red-400 font-semibold\">{formatCurrency(level)}</span>\n                          </div>\n                        ))}\n                      </div>\n                    </CardContent>\n                  </Card>\n                )}\n              </div>\n            )}\n          </div>\n        </section>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;AAee,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,0OAAQ,EAAC;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,0OAAQ,EAAC;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,0OAAQ,EAAC;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,0OAAQ,EAA8B;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,0OAAQ,EAAmB;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,0OAAQ,EAAgB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,0OAAQ,EAA8E;IAExH,MAAM,iBAAiB;QAAC;QAAO;QAAO;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;KAAQ;IAEtF,MAAM,iBAAiB,OAAO;QAC5B,eAAe;QACf,SAAS;QACT,YAAY;QACZ,aAAa;QAEb,IAAI;YACF,6CAA6C;YAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC1D,MAAM,CAAC,kBAAkB,EAAE,QAAQ;gBACnC,MAAM,CAAC,oBAAoB,EAAE,QAAQ;aACtC;YAED,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,iBAAiB,EAAE,EAAE;gBAC7C,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,CAAC,WAAW,aAAa,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAClD,cAAc,IAAI;gBAClB,iBAAiB,IAAI;aACtB;YAED,aAAa;YACb,YAAY;QACd,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,mBAAmB;QACnC,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,2BAA2B,CAAC;QAChC,EAAE,cAAc;QAChB,IAAI,aAAa,IAAI,IAAI;YACvB,eAAe,aAAa,WAAW;YACvC,kBAAkB,aAAa,WAAW;QAC5C;IACF;IAEA,qBACE,uQAAC;QAAI,WAAU;;0BAEb,uQAAC;gBAAO,WAAU;0BAChB,cAAA,uQAAC;oBAAI,WAAU;8BACb,cAAA,uQAAC;wBAAI,WAAU;;0CACb,uQAAC;gCAAI,WAAU;;kDACb,uQAAC,sOAAK;wCAAC,WAAU;;;;;;kDACjB,uQAAC;wCAAG,WAAU;kDAAgC;;;;;;;;;;;;0CAEhD,uQAAC;gCAAI,WAAU;;kDACb,uQAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,kBAAkB,EAAE,cAAc,eAAe,eAAe,mCAAmC;kDAChH;;;;;;kDAGD,uQAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,kBAAkB,EAAE,cAAc,YAAY,eAAe,mCAAmC;kDAC7G;;;;;;kDAGD,uQAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,kBAAkB,EAAE,cAAc,eAAe,eAAe,mCAAmC;kDAChH;;;;;;kDAGD,uQAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,kBAAkB,EAAE,cAAc,YAAY,eAAe,mCAAmC;kDAC7G;;;;;;kDAGD,uQAAC,yKAAW;;;;;kDACZ,uQAAC,qKAAM;wCAAC,SAAQ;wCAAU,WAAU;kDAAmE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS/G,uQAAC;gBAAQ,WAAU;0BACjB,cAAA,uQAAC;oBAAI,WAAU;;sCACb,uQAAC;4BAAG,WAAU;sCAAqC;;;;;;sCAGnD,uQAAC;4BAAE,WAAU;sCAAgD;;;;;;sCAM7D,uQAAC;4BAAI,WAAU;sCACb,cAAA,uQAAC;gCAAI,WAAU;;kDACb,uQAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,oCAAoC,EAC9C,cAAc,iBACV,2BACA,yDACJ;;0DAEF,uQAAC,yOAAM;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;kDAG5C,uQAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,oCAAoC,EAC9C,cAAc,eACV,2BACA,yDACJ;;0DAEF,uQAAC,gOAAG;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;kDAGzC,uQAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,oCAAoC,EAC9C,cAAc,YACV,2BACA,yDACJ;;0DAEF,uQAAC,mOAAI;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;kDAG1C,uQAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,oCAAoC,EAC9C,cAAc,eACV,2BACA,yDACJ;;0DAEF,uQAAC,yOAAM;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;kDAG5C,uQAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,oCAAoC,EAC9C,cAAc,YACV,2BACA,yDACJ;;0DAEF,uQAAC,yPAAU;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;kDAGhD,uQAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,oCAAoC,EAC9C,cAAc,OACV,2BACA,yDACJ;;0DAEF,uQAAC,sOAAK;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;;;;;;;;;;;;wBAO9C,cAAc,+BACb,uQAAC;4BAAI,WAAU;;8CACb,uQAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,uQAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;mCAInC,cAAc,6BAChB,uQAAC;4BAAI,WAAU;;8CACb,uQAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,uQAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;mCAInC,cAAc,0BAChB,uQAAC;4BAAI,WAAU;;8CACb,uQAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,uQAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;iDAKrC,uQAAC;4BAAI,WAAU;;8CACb,uQAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,uQAAC;oCAAI,WAAU;8CACZ,eAAe,GAAG,CAAC,CAAC,uBACnB,uQAAC,qKAAM;4CAEL,SAAS,mBAAmB,SAAS,YAAY;4CACjD,SAAS;gDACP,kBAAkB;gDAClB,eAAe;4CACjB;4CACA,UAAU;4CACV,WAAW,mBAAmB,SAC1B,kCACA;sDAGH;2CAZI;;;;;;;;;;8CAkBX,uQAAC;oCAAK,UAAU;oCAA0B,WAAU;;sDAClD,uQAAC;4CAAI,WAAU;;8DACb,uQAAC,yOAAM;oDAAC,WAAU;;;;;;8DAClB,uQAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oDAC/C,WAAU;oDACV,UAAU;;;;;;;;;;;;sDAGd,uQAAC,qKAAM;4CACL,MAAK;4CACL,UAAU,eAAe,CAAC,aAAa,IAAI;4CAC3C,WAAU;sDACX;;;;;;;;;;;;8CAKH,uQAAC,qKAAM;oCACL,MAAK;oCACL,SAAS,IAAM,eAAe;oCAC9B,UAAU;oCACV,WAAU;8CAET,4BACC;;0DACE,uQAAC,qPAAO;gDAAC,WAAU;;;;;;4CAA8B;4CACtC;4CAAe;;qEAG5B;;0DACE,uQAAC,gOAAG;gDAAC,WAAU;;;;;;4CAAiB;4CACX;;;;;;;;;;;;;;;;;;;;;;;;;YAUlC,cAAc,gCACb,uQAAC;gBAAQ,WAAU;0BACjB,cAAA,uQAAC;oBAAI,WAAU;8BACb,cAAA,uQAAC,uLAAkB;wBAAC,aAAa;wBAAQ,aAAa;;;;;;;;;;;;;;;;YAM3D,cAAc,8BACb,uQAAC;gBAAQ,WAAU;0BACjB,cAAA,uQAAC;oBAAI,WAAU;8BACb,cAAA,uQAAC,iLAAe;wBAAC,UAAU;wBAAM,aAAa;;;;;;;;;;;;;;;;YAMnD,cAAc,2BACb,uQAAC;gBAAQ,WAAU;0BACjB,cAAA,uQAAC;oBAAI,WAAU;8BACb,cAAA,uQAAC,2KAAY;wBAAC,UAAU;;;;;;;;;;;;;;;;YAM7B,cAAc,2BACb,uQAAC;gBAAQ,WAAU;0BACjB,cAAA,uQAAC;oBAAI,WAAU;;sCACb,uQAAC;4BAAI,WAAU;;8CACb,uQAAC;oCAAG,WAAU;8CAAqC;;;;;;8CACnD,uQAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;;sCAIrC,uQAAC;4BAAI,WAAU;;8CACb,uQAAC;oCAAG,WAAU;8CAAe;;;;;;8CAC7B,uQAAC;8CAAE;;;;;;8CACH,uQAAC;oCAAE,WAAU;8CAAO;;;;;;8CACpB,uQAAC;oCAAE,WAAU;;wCAAO;sDAA2B,uQAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAO5D,cAAc,sBACb,uQAAC;gBAAQ,WAAU;0BACjB,cAAA,uQAAC;oBAAI,WAAU;;sCACb,uQAAC;4BAAI,WAAU;;8CACb,uQAAC;oCAAG,WAAU;8CAAqC;;;;;;8CACnD,uQAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;;sCAIrC,uQAAC,yKAAe;;;;;;;;;;;;;;;;YAMrB,uBACC,uQAAC;gBAAQ,WAAU;0BACjB,cAAA,uQAAC;oBAAI,WAAU;8BACb,cAAA,uQAAC,iKAAI;wBAAC,WAAU;kCACd,cAAA,uQAAC,wKAAW;4BAAC,WAAU;sCACrB,cAAA,uQAAC;gCAAE,WAAU;0CAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;YAQlD,cAAc,gBAAgB,CAAC,aAAa,QAAQ,mBACnD,uQAAC;gBAAQ,WAAU;0BACjB,cAAA,uQAAC;oBAAI,WAAU;;sCACb,uQAAC;4BAAI,WAAU;;gCAGZ,2BACC,uQAAC,iKAAI;oCAAC,WAAU;;sDACd,uQAAC,uKAAU;sDACT,cAAA,uQAAC,sKAAS;gDAAC,WAAU;;kEACnB,uQAAC,wPAAS;wDAAC,WAAU;;;;;;oDACpB,UAAU,MAAM;oDAAC;;;;;;;;;;;;sDAGtB,uQAAC,wKAAW;sDACV,cAAA,uQAAC;gDAAI,WAAU;;kEACb,uQAAC;wDAAI,WAAU;;0EACb,uQAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,uQAAC;gEAAK,WAAU;0EAA4B,IAAA,8JAAc,EAAC,UAAU,KAAK;;;;;;;;;;;;kEAE5E,uQAAC;wDAAI,WAAU;;0EACb,uQAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,uQAAC;gEAAK,WAAW,UAAU,MAAM,IAAI,IAAI,mBAAmB;;oEACzD,IAAA,8JAAc,EAAC,UAAU,MAAM;oEAAE;oEAAG,IAAA,gKAAgB,EAAC,UAAU,aAAa;oEAAE;;;;;;;;;;;;;kEAGnF,uQAAC;wDAAI,WAAU;;0EACb,uQAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,uQAAC;gEAAK,WAAU;0EAAc,UAAU,MAAM,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAQtE,0BACC,uQAAC,iKAAI;oCAAC,WAAU;;sDACd,uQAAC,uKAAU;sDACT,cAAA,uQAAC,sKAAS;gDAAC,WAAU;;kEACnB,uQAAC,yOAAM;wDAAC,WAAU;;;;;;oDAAgC;;;;;;;;;;;;sDAItD,uQAAC,wKAAW;sDACV,cAAA,uQAAC;gDAAI,WAAU;;kEACb,uQAAC;wDAAI,WAAU;;0EACb,uQAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,uQAAC;gEAAK,WAAU;0EAA4B,IAAA,8JAAc,EAAC,SAAS,UAAU;;;;;;;;;;;;kEAEhF,uQAAC;wDAAI,WAAU;;0EACb,uQAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,uQAAC;gEAAK,WAAU;0EAAgB,IAAA,8JAAc,EAAC,SAAS,QAAQ;;;;;;;;;;;;kEAElE,uQAAC;wDAAI,WAAU;;0EACb,uQAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,uQAAC;gEAAK,WAAU;0EAAkB,IAAA,8JAAc,EAAC,SAAS,UAAU;;;;;;;;;;;;kEAEtE,uQAAC;wDAAI,WAAU;;0EACb,uQAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,uQAAC;gEAAK,WAAU;;oEAA+B,SAAS,eAAe,CAAC,OAAO,CAAC;oEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAQ5F,0BACC,uQAAC,iKAAI;oCAAC,WAAU;;sDACd,uQAAC,uKAAU;sDACT,cAAA,uQAAC,sKAAS;gDAAC,WAAU;;kEACnB,uQAAC,sOAAK;wDAAC,WAAU;;;;;;oDAAiC;;;;;;;;;;;;sDAItD,uQAAC,wKAAW;sDACV,cAAA,uQAAC;gDAAI,WAAU;;kEACb,uQAAC;wDAAI,WAAU;;0EACb,uQAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,uQAAC;gEAAK,WAAW,CAAC,cAAc,EAC9B,SAAS,KAAK,KAAK,YAAY,mBAC/B,SAAS,KAAK,KAAK,YAAY,iBAAiB,mBAChD;0EACC,SAAS,KAAK;;;;;;;;;;;;kEAGnB,uQAAC;wDAAI,WAAU;;0EACb,uQAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,uQAAC;gEAAK,WAAU;;oEAA4B,SAAS,UAAU,CAAC,OAAO,CAAC;oEAAG;;;;;;;;;;;;;kEAE7E,uQAAC;wDAAI,WAAU;;0EACb,uQAAC;gEAAK,WAAU;0EAAiB;;;;;;0EACjC,uQAAC;gEAAK,WAAW,CAAC,cAAc,EAC9B,SAAS,cAAc,CAAC,QAAQ,CAAC,SAAS,mBAC1C,SAAS,cAAc,CAAC,QAAQ,CAAC,UAAU,iBAAiB,mBAC5D;0EACC,SAAS,cAAc,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAUnD,0BACC,uQAAC,iKAAI;4BAAC,WAAU;;8CACd,uQAAC,uKAAU;8CACT,cAAA,uQAAC,sKAAS;wCAAC,WAAU;;0DACnB,uQAAC,yPAAU;gDAAC,WAAU;;;;;;4CAAiC;;;;;;;;;;;;8CAI3D,uQAAC,wKAAW;8CACV,cAAA,uQAAC;wCAAI,WAAU;kDACZ,SAAS,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,sBACnC,uQAAC;gDAAgB,WAAU;;kEACzB,uQAAC;wDAAI,WAAU;;0EACb,uQAAC;gEAAG,WAAU;0EAA0B,UAAU,IAAI;;;;;;0EACtD,uQAAC;gEAAK,WAAW,CAAC,wCAAwC,EACxD,UAAU,MAAM,KAAK,QAAQ,mCAC7B,UAAU,MAAM,KAAK,SAAS,+BAC9B,oCACA;0EACC,UAAU,MAAM;;;;;;;;;;;;kEAGrB,uQAAC;wDAAE,WAAU;kEAA0B,UAAU,WAAW;;;;;;;+CAXpD;;;;;;;;;;;;;;;;;;;;;wBAoBnB,YAAY,CAAC,SAAS,aAAa,CAAC,MAAM,GAAG,KAAK,SAAS,gBAAgB,CAAC,MAAM,GAAG,CAAC,mBACrF,uQAAC;4BAAI,WAAU;;gCACZ,SAAS,aAAa,CAAC,MAAM,GAAG,mBAC/B,uQAAC,iKAAI;oCAAC,WAAU;;sDACd,uQAAC,uKAAU;sDACT,cAAA,uQAAC,sKAAS;gDAAC,WAAU;;kEACnB,uQAAC,yOAAM;wDAAC,WAAU;;;;;;oDAAgC;;;;;;;;;;;;sDAItD,uQAAC,wKAAW;sDACV,cAAA,uQAAC;gDAAI,WAAU;0DACZ,SAAS,aAAa,CAAC,GAAG,CAAC,CAAC,OAAO,sBAClC,uQAAC;wDAAgB,WAAU;;0EACzB,uQAAC;gEAAK,WAAU;;oEAAiB;oEAAS,QAAQ;oEAAE;;;;;;;0EACpD,uQAAC;gEAAK,WAAU;0EAAgC,IAAA,8JAAc,EAAC;;;;;;;uDAFvD;;;;;;;;;;;;;;;;;;;;;gCAUnB,SAAS,gBAAgB,CAAC,MAAM,GAAG,mBAClC,uQAAC,iKAAI;oCAAC,WAAU;;sDACd,uQAAC,uKAAU;sDACT,cAAA,uQAAC,sKAAS;gDAAC,WAAU;;kEACnB,uQAAC,yOAAM;wDAAC,WAAU;;;;;;oDAA8B;;;;;;;;;;;;sDAIpD,uQAAC,wKAAW;sDACV,cAAA,uQAAC;gDAAI,WAAU;0DACZ,SAAS,gBAAgB,CAAC,GAAG,CAAC,CAAC,OAAO,sBACrC,uQAAC;wDAAgB,WAAU;;0EACzB,uQAAC;gEAAK,WAAU;;oEAAiB;oEAAY,QAAQ;oEAAE;;;;;;;0EACvD,uQAAC;gEAAK,WAAU;0EAA8B,IAAA,8JAAc,EAAC;;;;;;;uDAFrD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBpC", "debugId": null}}]}