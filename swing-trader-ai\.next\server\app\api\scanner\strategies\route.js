/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/scanner/strategies/route";
exports.ids = ["app/api/scanner/strategies/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fscanner%2Fstrategies%2Froute&page=%2Fapi%2Fscanner%2Fstrategies%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscanner%2Fstrategies%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fscanner%2Fstrategies%2Froute&page=%2Fapi%2Fscanner%2Fstrategies%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscanner%2Fstrategies%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var C_Users_SJFit_Desktop_shittyidea_swing_trader_ai_src_app_api_scanner_strategies_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/scanner/strategies/route.ts */ \"(rsc)/./src/app/api/scanner/strategies/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/scanner/strategies/route\",\n        pathname: \"/api/scanner/strategies\",\n        filename: \"route\",\n        bundlePath: \"app/api/scanner/strategies/route\"\n    },\n    distDir: \".next\" || 0,\n    relativeProjectDir:  false || '',\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\shittyidea\\\\swing-trader-ai\\\\src\\\\app\\\\api\\\\scanner\\\\strategies\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_SJFit_Desktop_shittyidea_swing_trader_ai_src_app_api_scanner_strategies_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/scanner/strategies/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = false;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        if (!(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fscanner%2Fstrategies%2Froute&page=%2Fapi%2Fscanner%2Fstrategies%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscanner%2Fstrategies%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/scanner/strategies/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/scanner/strategies/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_enhancedSwingScanner__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/enhancedSwingScanner */ \"(rsc)/./src/lib/enhancedSwingScanner.ts\");\n/* harmony import */ var _data_stockUniverse__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/stockUniverse */ \"(rsc)/./src/data/stockUniverse.ts\");\n\n\n\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const scanType = searchParams.get('type') || 'quick' // quick, full\n        ;\n        const accountSize = parseInt(searchParams.get('accountSize') || '100000');\n        const limit = parseInt(searchParams.get('limit') || '20');\n        console.log(`Starting ${scanType} strategy scan...`);\n        // Set account size for position sizing\n        const scanner = new _lib_enhancedSwingScanner__WEBPACK_IMPORTED_MODULE_1__.EnhancedSwingScanner(accountSize);\n        let symbols;\n        let maxConcurrent;\n        if (scanType === 'full') {\n            // Full scan: All 70+ stocks with slower processing\n            symbols = _data_stockUniverse__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_SWING_SYMBOLS;\n            maxConcurrent = 3;\n            console.log(`Full scan: ${symbols.length} stocks`);\n        } else {\n            // Quick scan: Top 30 swing trading candidates with faster processing\n            symbols = _data_stockUniverse__WEBPACK_IMPORTED_MODULE_2__.PRIORITY_SWING_SYMBOLS;\n            maxConcurrent = 6;\n            console.log(`Quick scan: ${symbols.length} priority stocks`);\n        }\n        const summary = await scanner.scanWithStrategies(symbols, maxConcurrent);\n        // Limit results if requested\n        const limitedSummary = {\n            ...summary,\n            topSetups: summary.topSetups.slice(0, limit)\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(limitedSummary);\n    } catch (error) {\n        console.error('Error in strategy scanner API:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to perform strategy scan'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/scanner/strategies/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/data/stockUniverse.ts":
/*!***********************************!*\
  !*** ./src/data/stockUniverse.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_SWING_SYMBOLS: () => (/* binding */ DEFAULT_SWING_SYMBOLS),\n/* harmony export */   ENERGY_SYMBOLS: () => (/* binding */ ENERGY_SYMBOLS),\n/* harmony export */   FINANCIAL_SYMBOLS: () => (/* binding */ FINANCIAL_SYMBOLS),\n/* harmony export */   HEALTHCARE_SYMBOLS: () => (/* binding */ HEALTHCARE_SYMBOLS),\n/* harmony export */   PRIORITY_SWING_SYMBOLS: () => (/* binding */ PRIORITY_SWING_SYMBOLS),\n/* harmony export */   SWING_TRADING_UNIVERSE: () => (/* binding */ SWING_TRADING_UNIVERSE),\n/* harmony export */   TECH_SYMBOLS: () => (/* binding */ TECH_SYMBOLS),\n/* harmony export */   getHighVolumeStocks: () => (/* binding */ getHighVolumeStocks),\n/* harmony export */   getStocksBySector: () => (/* binding */ getStocksBySector),\n/* harmony export */   getStocksByVolatility: () => (/* binding */ getStocksByVolatility),\n/* harmony export */   getTopSwingTradingStocks: () => (/* binding */ getTopSwingTradingStocks)\n/* harmony export */ });\n/**\n * Comprehensive stock universe for swing trading\n * Focus on liquid, volatile large-cap stocks with good technical patterns\n */ const SWING_TRADING_UNIVERSE = [\n    // Technology - High Growth & Volatility\n    {\n        symbol: 'AAPL',\n        name: 'Apple Inc.',\n        sector: 'Technology',\n        marketCap: 3000,\n        avgVolume: 50000000,\n        volatility: 'Medium',\n        swingTradingRating: 9\n    },\n    {\n        symbol: 'MSFT',\n        name: 'Microsoft Corporation',\n        sector: 'Technology',\n        marketCap: 2800,\n        avgVolume: 25000000,\n        volatility: 'Medium',\n        swingTradingRating: 8\n    },\n    {\n        symbol: 'NVDA',\n        name: 'NVIDIA Corporation',\n        sector: 'Technology',\n        marketCap: 1800,\n        avgVolume: 45000000,\n        volatility: 'High',\n        swingTradingRating: 10\n    },\n    {\n        symbol: 'GOOGL',\n        name: 'Alphabet Inc. Class A',\n        sector: 'Technology',\n        marketCap: 1700,\n        avgVolume: 25000000,\n        volatility: 'Medium',\n        swingTradingRating: 8\n    },\n    {\n        symbol: 'GOOG',\n        name: 'Alphabet Inc. Class C',\n        sector: 'Technology',\n        marketCap: 1700,\n        avgVolume: ********,\n        volatility: 'Medium',\n        swingTradingRating: 8\n    },\n    {\n        symbol: 'META',\n        name: 'Meta Platforms Inc.',\n        sector: 'Technology',\n        marketCap: 800,\n        avgVolume: ********,\n        volatility: 'High',\n        swingTradingRating: 9\n    },\n    {\n        symbol: 'TSLA',\n        name: 'Tesla Inc.',\n        sector: 'Technology',\n        marketCap: 800,\n        avgVolume: 75000000,\n        volatility: 'High',\n        swingTradingRating: 10\n    },\n    {\n        symbol: 'AMZN',\n        name: 'Amazon.com Inc.',\n        sector: 'Technology',\n        marketCap: 1500,\n        avgVolume: 35000000,\n        volatility: 'Medium',\n        swingTradingRating: 8\n    },\n    {\n        symbol: 'NFLX',\n        name: 'Netflix Inc.',\n        sector: 'Technology',\n        marketCap: 200,\n        avgVolume: 8000000,\n        volatility: 'High',\n        swingTradingRating: 9\n    },\n    {\n        symbol: 'ORCL',\n        name: 'Oracle Corporation',\n        sector: 'Technology',\n        marketCap: 350,\n        avgVolume: ********,\n        volatility: 'Medium',\n        swingTradingRating: 7\n    },\n    {\n        symbol: 'CRM',\n        name: 'Salesforce Inc.',\n        sector: 'Technology',\n        marketCap: 250,\n        avgVolume: 6000000,\n        volatility: 'High',\n        swingTradingRating: 8\n    },\n    {\n        symbol: 'ADBE',\n        name: 'Adobe Inc.',\n        sector: 'Technology',\n        marketCap: 220,\n        avgVolume: 3000000,\n        volatility: 'Medium',\n        swingTradingRating: 7\n    },\n    {\n        symbol: 'AVGO',\n        name: 'Broadcom Inc.',\n        sector: 'Technology',\n        marketCap: 600,\n        avgVolume: 2000000,\n        volatility: 'Medium',\n        swingTradingRating: 8\n    },\n    {\n        symbol: 'TSM',\n        name: 'Taiwan Semiconductor',\n        sector: 'Technology',\n        marketCap: 500,\n        avgVolume: ********,\n        volatility: 'Medium',\n        swingTradingRating: 8\n    },\n    {\n        symbol: 'ASML',\n        name: 'ASML Holding N.V.',\n        sector: 'Technology',\n        marketCap: 300,\n        avgVolume: 1500000,\n        volatility: 'High',\n        swingTradingRating: 8\n    },\n    {\n        symbol: 'AMD',\n        name: 'Advanced Micro Devices',\n        sector: 'Technology',\n        marketCap: 220,\n        avgVolume: 45000000,\n        volatility: 'High',\n        swingTradingRating: 9\n    },\n    {\n        symbol: 'INTC',\n        name: 'Intel Corporation',\n        sector: 'Technology',\n        marketCap: 200,\n        avgVolume: 25000000,\n        volatility: 'Medium',\n        swingTradingRating: 7\n    },\n    {\n        symbol: 'QCOM',\n        name: 'QUALCOMM Incorporated',\n        sector: 'Technology',\n        marketCap: 180,\n        avgVolume: 8000000,\n        volatility: 'Medium',\n        swingTradingRating: 8\n    },\n    {\n        symbol: 'PLTR',\n        name: 'Palantir Technologies',\n        sector: 'Technology',\n        marketCap: 60,\n        avgVolume: 35000000,\n        volatility: 'High',\n        swingTradingRating: 9\n    },\n    {\n        symbol: 'SNOW',\n        name: 'Snowflake Inc.',\n        sector: 'Technology',\n        marketCap: 50,\n        avgVolume: 4000000,\n        volatility: 'High',\n        swingTradingRating: 8\n    },\n    // Financial Services - Interest Rate Sensitive\n    {\n        symbol: 'JPM',\n        name: 'JPMorgan Chase & Co.',\n        sector: 'Financial',\n        marketCap: 500,\n        avgVolume: ********,\n        volatility: 'Medium',\n        swingTradingRating: 8\n    },\n    {\n        symbol: 'BAC',\n        name: 'Bank of America Corp.',\n        sector: 'Financial',\n        marketCap: 300,\n        avgVolume: ********,\n        volatility: 'Medium',\n        swingTradingRating: 8\n    },\n    {\n        symbol: 'WFC',\n        name: 'Wells Fargo & Company',\n        sector: 'Financial',\n        marketCap: 180,\n        avgVolume: ********,\n        volatility: 'Medium',\n        swingTradingRating: 7\n    },\n    {\n        symbol: 'GS',\n        name: 'Goldman Sachs Group',\n        sector: 'Financial',\n        marketCap: 120,\n        avgVolume: 2500000,\n        volatility: 'High',\n        swingTradingRating: 8\n    },\n    {\n        symbol: 'MS',\n        name: 'Morgan Stanley',\n        sector: 'Financial',\n        marketCap: 150,\n        avgVolume: 8000000,\n        volatility: 'High',\n        swingTradingRating: 8\n    },\n    {\n        symbol: 'C',\n        name: 'Citigroup Inc.',\n        sector: 'Financial',\n        marketCap: 120,\n        avgVolume: ********,\n        volatility: 'High',\n        swingTradingRating: 8\n    },\n    {\n        symbol: 'BRK.B',\n        name: 'Berkshire Hathaway B',\n        sector: 'Financial',\n        marketCap: 900,\n        avgVolume: 4000000,\n        volatility: 'Low',\n        swingTradingRating: 6\n    },\n    {\n        symbol: 'V',\n        name: 'Visa Inc.',\n        sector: 'Financial',\n        marketCap: 500,\n        avgVolume: 6000000,\n        volatility: 'Medium',\n        swingTradingRating: 7\n    },\n    {\n        symbol: 'MA',\n        name: 'Mastercard Inc.',\n        sector: 'Financial',\n        marketCap: 400,\n        avgVolume: 3000000,\n        volatility: 'Medium',\n        swingTradingRating: 7\n    },\n    {\n        symbol: 'PYPL',\n        name: 'PayPal Holdings Inc.',\n        sector: 'Financial',\n        marketCap: 70,\n        avgVolume: ********,\n        volatility: 'High',\n        swingTradingRating: 8\n    },\n    // Healthcare & Biotech - Defensive with Growth\n    {\n        symbol: 'JNJ',\n        name: 'Johnson & Johnson',\n        sector: 'Healthcare',\n        marketCap: 450,\n        avgVolume: 7000000,\n        volatility: 'Low',\n        swingTradingRating: 6\n    },\n    {\n        symbol: 'UNH',\n        name: 'UnitedHealth Group',\n        sector: 'Healthcare',\n        marketCap: 500,\n        avgVolume: 3000000,\n        volatility: 'Medium',\n        swingTradingRating: 7\n    },\n    {\n        symbol: 'PFE',\n        name: 'Pfizer Inc.',\n        sector: 'Healthcare',\n        marketCap: 160,\n        avgVolume: 25000000,\n        volatility: 'Medium',\n        swingTradingRating: 7\n    },\n    {\n        symbol: 'ABBV',\n        name: 'AbbVie Inc.',\n        sector: 'Healthcare',\n        marketCap: 300,\n        avgVolume: 6000000,\n        volatility: 'Medium',\n        swingTradingRating: 7\n    },\n    {\n        symbol: 'TMO',\n        name: 'Thermo Fisher Scientific',\n        sector: 'Healthcare',\n        marketCap: 200,\n        avgVolume: 1500000,\n        volatility: 'Medium',\n        swingTradingRating: 7\n    },\n    {\n        symbol: 'DHR',\n        name: 'Danaher Corporation',\n        sector: 'Healthcare',\n        marketCap: 180,\n        avgVolume: 2000000,\n        volatility: 'Medium',\n        swingTradingRating: 7\n    },\n    {\n        symbol: 'BMY',\n        name: 'Bristol Myers Squibb',\n        sector: 'Healthcare',\n        marketCap: 120,\n        avgVolume: 10000000,\n        volatility: 'Medium',\n        swingTradingRating: 7\n    },\n    {\n        symbol: 'AMGN',\n        name: 'Amgen Inc.',\n        sector: 'Healthcare',\n        marketCap: 150,\n        avgVolume: 2500000,\n        volatility: 'Medium',\n        swingTradingRating: 7\n    },\n    {\n        symbol: 'GILD',\n        name: 'Gilead Sciences Inc.',\n        sector: 'Healthcare',\n        marketCap: 80,\n        avgVolume: 6000000,\n        volatility: 'Medium',\n        swingTradingRating: 7\n    },\n    {\n        symbol: 'MRNA',\n        name: 'Moderna Inc.',\n        sector: 'Healthcare',\n        marketCap: 30,\n        avgVolume: 8000000,\n        volatility: 'High',\n        swingTradingRating: 9\n    },\n    // Consumer & Retail - Economic Sensitive\n    {\n        symbol: 'WMT',\n        name: 'Walmart Inc.',\n        sector: 'Consumer',\n        marketCap: 600,\n        avgVolume: 8000000,\n        volatility: 'Low',\n        swingTradingRating: 6\n    },\n    {\n        symbol: 'HD',\n        name: 'Home Depot Inc.',\n        sector: 'Consumer',\n        marketCap: 350,\n        avgVolume: 3000000,\n        volatility: 'Medium',\n        swingTradingRating: 7\n    },\n    {\n        symbol: 'MCD',\n        name: 'McDonald\\'s Corporation',\n        sector: 'Consumer',\n        marketCap: 200,\n        avgVolume: 2500000,\n        volatility: 'Low',\n        swingTradingRating: 6\n    },\n    {\n        symbol: 'NKE',\n        name: 'Nike Inc.',\n        sector: 'Consumer',\n        marketCap: 150,\n        avgVolume: 6000000,\n        volatility: 'Medium',\n        swingTradingRating: 7\n    },\n    {\n        symbol: 'SBUX',\n        name: 'Starbucks Corporation',\n        sector: 'Consumer',\n        marketCap: 110,\n        avgVolume: 6000000,\n        volatility: 'Medium',\n        swingTradingRating: 7\n    },\n    {\n        symbol: 'TGT',\n        name: 'Target Corporation',\n        sector: 'Consumer',\n        marketCap: 70,\n        avgVolume: 4000000,\n        volatility: 'High',\n        swingTradingRating: 8\n    },\n    {\n        symbol: 'COST',\n        name: 'Costco Wholesale Corp.',\n        sector: 'Consumer',\n        marketCap: 350,\n        avgVolume: 2000000,\n        volatility: 'Medium',\n        swingTradingRating: 7\n    },\n    {\n        symbol: 'LOW',\n        name: 'Lowe\\'s Companies Inc.',\n        sector: 'Consumer',\n        marketCap: 150,\n        avgVolume: 3000000,\n        volatility: 'Medium',\n        swingTradingRating: 7\n    },\n    // Energy - Commodity Driven\n    {\n        symbol: 'XOM',\n        name: 'Exxon Mobil Corporation',\n        sector: 'Energy',\n        marketCap: 450,\n        avgVolume: ********,\n        volatility: 'High',\n        swingTradingRating: 8\n    },\n    {\n        symbol: 'CVX',\n        name: 'Chevron Corporation',\n        sector: 'Energy',\n        marketCap: 300,\n        avgVolume: ********,\n        volatility: 'Medium',\n        swingTradingRating: 7\n    },\n    {\n        symbol: 'COP',\n        name: 'ConocoPhillips',\n        sector: 'Energy',\n        marketCap: 150,\n        avgVolume: 8000000,\n        volatility: 'High',\n        swingTradingRating: 8\n    },\n    {\n        symbol: 'SLB',\n        name: 'Schlumberger Limited',\n        sector: 'Energy',\n        marketCap: 60,\n        avgVolume: ********,\n        volatility: 'High',\n        swingTradingRating: 8\n    },\n    {\n        symbol: 'EOG',\n        name: 'EOG Resources Inc.',\n        sector: 'Energy',\n        marketCap: 70,\n        avgVolume: 4000000,\n        volatility: 'High',\n        swingTradingRating: 8\n    },\n    // Industrial & Materials\n    {\n        symbol: 'CAT',\n        name: 'Caterpillar Inc.',\n        sector: 'Industrial',\n        marketCap: 180,\n        avgVolume: 3000000,\n        volatility: 'High',\n        swingTradingRating: 8\n    },\n    {\n        symbol: 'BA',\n        name: 'Boeing Company',\n        sector: 'Industrial',\n        marketCap: 120,\n        avgVolume: 8000000,\n        volatility: 'High',\n        swingTradingRating: 9\n    },\n    {\n        symbol: 'GE',\n        name: 'General Electric Co.',\n        sector: 'Industrial',\n        marketCap: 180,\n        avgVolume: 45000000,\n        volatility: 'High',\n        swingTradingRating: 9\n    },\n    {\n        symbol: 'MMM',\n        name: '3M Company',\n        sector: 'Industrial',\n        marketCap: 60,\n        avgVolume: 3000000,\n        volatility: 'Medium',\n        swingTradingRating: 7\n    },\n    {\n        symbol: 'HON',\n        name: 'Honeywell International',\n        sector: 'Industrial',\n        marketCap: 140,\n        avgVolume: 2500000,\n        volatility: 'Medium',\n        swingTradingRating: 7\n    },\n    // Communication & Media\n    {\n        symbol: 'DIS',\n        name: 'Walt Disney Company',\n        sector: 'Media',\n        marketCap: 180,\n        avgVolume: ********,\n        volatility: 'High',\n        swingTradingRating: 8\n    },\n    {\n        symbol: 'CMCSA',\n        name: 'Comcast Corporation',\n        sector: 'Media',\n        marketCap: 150,\n        avgVolume: ********,\n        volatility: 'Medium',\n        swingTradingRating: 7\n    },\n    {\n        symbol: 'VZ',\n        name: 'Verizon Communications',\n        sector: 'Telecom',\n        marketCap: 170,\n        avgVolume: ********,\n        volatility: 'Low',\n        swingTradingRating: 6\n    },\n    {\n        symbol: 'T',\n        name: 'AT&T Inc.',\n        sector: 'Telecom',\n        marketCap: 120,\n        avgVolume: 35000000,\n        volatility: 'Medium',\n        swingTradingRating: 7\n    },\n    // High-Volatility Growth Stocks\n    {\n        symbol: 'ROKU',\n        name: 'Roku Inc.',\n        sector: 'Technology',\n        marketCap: 5,\n        avgVolume: 8000000,\n        volatility: 'High',\n        swingTradingRating: 9\n    },\n    {\n        symbol: 'SHOP',\n        name: 'Shopify Inc.',\n        sector: 'Technology',\n        marketCap: 80,\n        avgVolume: 3000000,\n        volatility: 'High',\n        swingTradingRating: 9\n    },\n    {\n        symbol: 'SQ',\n        name: 'Block Inc.',\n        sector: 'Financial',\n        marketCap: 40,\n        avgVolume: 8000000,\n        volatility: 'High',\n        swingTradingRating: 9\n    },\n    {\n        symbol: 'UBER',\n        name: 'Uber Technologies',\n        sector: 'Technology',\n        marketCap: 150,\n        avgVolume: ********,\n        volatility: 'High',\n        swingTradingRating: 9\n    },\n    {\n        symbol: 'LYFT',\n        name: 'Lyft Inc.',\n        sector: 'Technology',\n        marketCap: 6,\n        avgVolume: 4000000,\n        volatility: 'High',\n        swingTradingRating: 8\n    },\n    {\n        symbol: 'DASH',\n        name: 'DoorDash Inc.',\n        sector: 'Technology',\n        marketCap: 50,\n        avgVolume: 3000000,\n        volatility: 'High',\n        swingTradingRating: 8\n    },\n    {\n        symbol: 'COIN',\n        name: 'Coinbase Global Inc.',\n        sector: 'Financial',\n        marketCap: 50,\n        avgVolume: 8000000,\n        volatility: 'High',\n        swingTradingRating: 9\n    },\n    {\n        symbol: 'HOOD',\n        name: 'Robinhood Markets Inc.',\n        sector: 'Financial',\n        marketCap: 15,\n        avgVolume: 10000000,\n        volatility: 'High',\n        swingTradingRating: 9\n    },\n    // REITs & Utilities (Lower volatility but good for certain strategies)\n    {\n        symbol: 'SPG',\n        name: 'Simon Property Group',\n        sector: 'REIT',\n        marketCap: 50,\n        avgVolume: 2000000,\n        volatility: 'Medium',\n        swingTradingRating: 6\n    },\n    {\n        symbol: 'PLD',\n        name: 'Prologis Inc.',\n        sector: 'REIT',\n        marketCap: 120,\n        avgVolume: 2500000,\n        volatility: 'Medium',\n        swingTradingRating: 6\n    },\n    {\n        symbol: 'NEE',\n        name: 'NextEra Energy Inc.',\n        sector: 'Utilities',\n        marketCap: 150,\n        avgVolume: 8000000,\n        volatility: 'Low',\n        swingTradingRating: 6\n    }\n];\n// Helper functions\nconst getStocksByVolatility = (volatility)=>{\n    return SWING_TRADING_UNIVERSE.filter((stock)=>stock.volatility === volatility);\n};\nconst getStocksBySector = (sector)=>{\n    return SWING_TRADING_UNIVERSE.filter((stock)=>stock.sector === sector);\n};\nconst getTopSwingTradingStocks = (limit = 50)=>{\n    return SWING_TRADING_UNIVERSE.sort((a, b)=>b.swingTradingRating - a.swingTradingRating).slice(0, limit);\n};\nconst getHighVolumeStocks = (minVolume = 10000000)=>{\n    return SWING_TRADING_UNIVERSE.filter((stock)=>stock.avgVolume >= minVolume);\n};\n// Default stock symbols for quick access\nconst DEFAULT_SWING_SYMBOLS = SWING_TRADING_UNIVERSE.map((stock)=>stock.symbol);\n// High-priority stocks for scanning (top swing trading candidates)\nconst PRIORITY_SWING_SYMBOLS = getTopSwingTradingStocks(30).map((stock)=>stock.symbol);\n// Sector-based symbol lists\nconst TECH_SYMBOLS = getStocksBySector('Technology').map((stock)=>stock.symbol);\nconst FINANCIAL_SYMBOLS = getStocksBySector('Financial').map((stock)=>stock.symbol);\nconst HEALTHCARE_SYMBOLS = getStocksBySector('Healthcare').map((stock)=>stock.symbol);\nconst ENERGY_SYMBOLS = getStocksBySector('Energy').map((stock)=>stock.symbol);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/data/stockUniverse.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/enhancedSwingScanner.ts":
/*!*****************************************!*\
  !*** ./src/lib/enhancedSwingScanner.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EnhancedSwingScanner: () => (/* binding */ EnhancedSwingScanner),\n/* harmony export */   enhancedSwingScanner: () => (/* binding */ enhancedSwingScanner)\n/* harmony export */ });\n/* harmony import */ var _swingStrategies__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./swingStrategies */ \"(rsc)/./src/lib/swingStrategies.ts\");\n/* harmony import */ var _polygon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./polygon */ \"(rsc)/./src/lib/polygon.ts\");\n/* harmony import */ var _data_stockUniverse__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/stockUniverse */ \"(rsc)/./src/data/stockUniverse.ts\");\n/* harmony import */ var _barrel_optimize_names_format_subDays_date_fns__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=format,subDays!=!date-fns */ \"(rsc)/./node_modules/date-fns/format.js\");\n/* harmony import */ var _barrel_optimize_names_format_subDays_date_fns__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=format,subDays!=!date-fns */ \"(rsc)/./node_modules/date-fns/subDays.js\");\n\n\n\n\nclass EnhancedSwingScanner {\n    constructor(accountSize = 100000){\n        this.polygonAPI = new _polygon__WEBPACK_IMPORTED_MODULE_1__.PolygonAPI(process.env.POLYGON_API_KEY);\n        this.accountSize = accountSize;\n    }\n    // Main enhanced scanning function\n    async scanWithStrategies(symbols, maxConcurrent = 5) {\n        const startTime = Date.now();\n        const results = [];\n        const failed = [];\n        console.log(`Starting enhanced strategy scan of ${symbols.length} stocks...`);\n        // Check if we're in optimal scan time (12:00-16:00 ET)\n        const marketConditions = this.getMarketConditions();\n        // Process stocks in batches\n        for(let i = 0; i < symbols.length; i += maxConcurrent){\n            const batch = symbols.slice(i, i + maxConcurrent);\n            const batchPromises = batch.map((symbol)=>this.scanSingleStockStrategies(symbol));\n            const batchResults = await Promise.allSettled(batchPromises);\n            batchResults.forEach((result, index)=>{\n                const symbol = batch[index];\n                if (result.status === 'fulfilled' && result.value) {\n                    results.push(result.value);\n                } else {\n                    failed.push(symbol);\n                    console.warn(`Failed to scan ${symbol}:`, result.status === 'rejected' ? result.reason : 'Unknown error');\n                }\n            });\n            // Rate limiting delay\n            if (i + maxConcurrent < symbols.length) {\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n            }\n        }\n        // Sort by overall score and assign ranks\n        results.sort((a, b)=>b.overallScore - a.overallScore);\n        results.forEach((result, index)=>{\n            result.rank = index + 1;\n        });\n        // Calculate summary statistics\n        const overnightSetups = results.filter((r)=>r.overnightSetup).length;\n        const breakoutSetups = results.filter((r)=>r.breakoutSetup).length;\n        const bothStrategies = results.filter((r)=>r.overnightSetup && r.breakoutSetup).length;\n        const scanDuration = Date.now() - startTime;\n        return {\n            totalScanned: symbols.length,\n            overnightSetups,\n            breakoutSetups,\n            bothStrategies,\n            topSetups: results.slice(0, 25),\n            scanDuration,\n            marketConditions\n        };\n    }\n    // Scan individual stock for both strategies\n    async scanSingleStockStrategies(symbol) {\n        try {\n            console.log(`Starting scan for ${symbol}...`);\n            // Get stock quote and historical data\n            const [quote, historicalData] = await Promise.all([\n                this.polygonAPI.getStockQuote(symbol),\n                this.getHistoricalData(symbol)\n            ]);\n            console.log(`Quote for ${symbol}:`, quote);\n            console.log(`Historical data length for ${symbol}:`, historicalData?.length);\n            if (!quote) {\n                throw new Error(`No quote data available for ${symbol}`);\n            }\n            if (!historicalData || historicalData.length < 30) {\n                throw new Error(`Insufficient historical data for ${symbol} - need at least 30 days, got ${historicalData?.length || 0}`);\n            }\n            // Analyze both strategies\n            const overnightSetup = _swingStrategies__WEBPACK_IMPORTED_MODULE_0__.SwingTradingStrategies.analyzeOvernightMomentum(symbol, historicalData, quote, this.accountSize);\n            const breakoutSetup = _swingStrategies__WEBPACK_IMPORTED_MODULE_0__.SwingTradingStrategies.analyzeTechnicalBreakout(symbol, historicalData, quote, this.accountSize);\n            // Skip if no valid setups\n            if (!overnightSetup && !breakoutSetup) {\n                return null;\n            }\n            // Determine best strategy and overall score\n            const { bestStrategy, overallScore } = this.calculateBestStrategy(overnightSetup, breakoutSetup);\n            // Generate alerts and warnings\n            const alerts = this.generateAlerts(overnightSetup, breakoutSetup, quote);\n            const riskWarnings = this.generateRiskWarnings(overnightSetup, breakoutSetup, quote);\n            const result = {\n                symbol,\n                name: quote.name || symbol,\n                sector: this.getSectorForSymbol(symbol),\n                quote,\n                overnightSetup: overnightSetup || undefined,\n                breakoutSetup: breakoutSetup || undefined,\n                bestStrategy,\n                overallScore,\n                rank: 0,\n                scanTime: new Date().toISOString(),\n                alerts,\n                riskWarnings\n            };\n            console.log(`Successfully scanned ${symbol} with score ${overallScore}`);\n            return result;\n        } catch (error) {\n            console.error(`Error scanning ${symbol}:`, error);\n            if (error instanceof Error) {\n                console.error(`Error message: ${error.message}`);\n                console.error(`Error stack: ${error.stack}`);\n            }\n            return null;\n        }\n    }\n    // Get historical data with optimized API usage\n    async getHistoricalData(symbol) {\n        const to = (0,_barrel_optimize_names_format_subDays_date_fns__WEBPACK_IMPORTED_MODULE_3__.format)(new Date(), 'yyyy-MM-dd');\n        const from = (0,_barrel_optimize_names_format_subDays_date_fns__WEBPACK_IMPORTED_MODULE_3__.format)((0,_barrel_optimize_names_format_subDays_date_fns__WEBPACK_IMPORTED_MODULE_4__.subDays)(new Date(), 100), 'yyyy-MM-dd') // 100 days should be sufficient\n        ;\n        try {\n            console.log(`Fetching historical data for ${symbol} from ${from} to ${to}`);\n            const data = await this.polygonAPI.getHistoricalData(symbol, 'day', 1, from, to);\n            if (data.length === 0) {\n                console.warn(`No historical data returned for ${symbol}`);\n                throw new Error('No historical data available');\n            }\n            console.log(`Successfully fetched ${data.length} days of data for ${symbol}`);\n            return data;\n        } catch (error) {\n            console.error(`Failed to fetch historical data for ${symbol}:`, error);\n            throw error;\n        }\n    }\n    // Calculate best strategy and overall score\n    calculateBestStrategy(overnight, breakout) {\n        if (!overnight && !breakout) {\n            return {\n                overallScore: 0\n            };\n        }\n        if (overnight && !breakout) {\n            return {\n                bestStrategy: 'overnight_momentum',\n                overallScore: overnight.confidence\n            };\n        }\n        if (breakout && !overnight) {\n            return {\n                bestStrategy: 'technical_breakout',\n                overallScore: breakout.confidence\n            };\n        }\n        if (overnight && breakout) {\n            // Both strategies valid - choose higher confidence\n            if (overnight.confidence > breakout.confidence) {\n                return {\n                    bestStrategy: 'overnight_momentum',\n                    overallScore: overnight.confidence + 5\n                } // Bonus for multiple setups\n                ;\n            } else {\n                return {\n                    bestStrategy: 'technical_breakout',\n                    overallScore: breakout.confidence + 5\n                };\n            }\n        }\n        return {\n            overallScore: 0\n        };\n    }\n    // Generate trading alerts\n    generateAlerts(overnight, breakout, quote) {\n        const alerts = [];\n        if (overnight) {\n            alerts.push(`🚀 OVERNIGHT MOMENTUM: Entry ${overnight.entryPrice.toFixed(2)}, Target ${overnight.targets[0].toFixed(2)}`);\n            alerts.push(`⏰ Execute in final 30-60 min before close`);\n            alerts.push(`🛑 Stop: ${overnight.stopLoss.toFixed(2)} (${((overnight.entryPrice - overnight.stopLoss) / overnight.entryPrice * 100).toFixed(1)}% risk)`);\n        }\n        if (breakout) {\n            alerts.push(`📈 BREAKOUT SETUP: Entry ${breakout.entryPrice.toFixed(2)}, riding 8-EMA`);\n            alerts.push(`🎯 Targets: ${breakout.targets.map((t)=>t.toFixed(2)).join(', ')}`);\n            alerts.push(`🛑 Stop: Daily close below ${breakout.stopLoss.toFixed(2)}`);\n        }\n        if (quote && quote.changePercent > 5) {\n            alerts.push(`🔥 Strong momentum: +${quote.changePercent.toFixed(1)}% today`);\n        }\n        return alerts;\n    }\n    // Generate risk warnings\n    generateRiskWarnings(overnight, breakout, quote) {\n        const warnings = [];\n        if (overnight) {\n            warnings.push(`⚠️ Overnight gap risk - size down vs intraday trades`);\n            if (quote && quote.changePercent > 8) {\n                warnings.push(`⚠️ Extended move (+${quote.changePercent.toFixed(1)}%) - consider smaller size`);\n            }\n        }\n        if (quote && (quote.marketCap || 0) < **********) {\n            warnings.push(`⚠️ Small cap overnight risk - volatile gaps possible`);\n        }\n        if (quote && quote.volume < 1000000) {\n            warnings.push(`⚠️ Lower volume - may have liquidity issues`);\n        }\n        return warnings;\n    }\n    // Get market conditions with proper timezone handling\n    getMarketConditions() {\n        const now = new Date();\n        // Get current time in Eastern Time (market timezone)\n        const etNow = new Date(now.toLocaleString(\"en-US\", {\n            timeZone: \"America/New_York\"\n        }));\n        const etHour = etNow.getHours();\n        const etMinute = etNow.getMinutes();\n        const etTimeDecimal = etHour + etMinute / 60;\n        // Get local time for display\n        const localHour = now.getHours();\n        const localMinute = now.getMinutes();\n        // Check if it's a weekday (Monday = 1, Friday = 5)\n        const dayOfWeek = etNow.getDay();\n        const isWeekday = dayOfWeek >= 1 && dayOfWeek <= 5;\n        return {\n            timeOfDay: `${localHour.toString().padStart(2, '0')}:${localMinute.toString().padStart(2, '0')} Local (${etHour.toString().padStart(2, '0')}:${etMinute.toString().padStart(2, '0')} ET)`,\n            isOptimalScanTime: isWeekday && etTimeDecimal >= 12 && etTimeDecimal <= 16,\n            marketHours: isWeekday && etTimeDecimal >= 9.5 && etTimeDecimal <= 16,\n            etTime: `${etHour.toString().padStart(2, '0')}:${etMinute.toString().padStart(2, '0')} ET`,\n            isWeekday\n        };\n    }\n    // Get sector for symbol using stock universe data\n    getSectorForSymbol(symbol) {\n        const stockInfo = _data_stockUniverse__WEBPACK_IMPORTED_MODULE_2__.SWING_TRADING_UNIVERSE.find((stock)=>stock.symbol === symbol);\n        return stockInfo?.sector || 'Other';\n    }\n    // Quick scan with strategies\n    async quickStrategyScan(prioritySymbols) {\n        const summary = await this.scanWithStrategies(prioritySymbols, 8);\n        return summary.topSetups;\n    }\n}\n// Create singleton instance\nconst enhancedSwingScanner = new EnhancedSwingScanner();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/enhancedSwingScanner.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/indicators.ts":
/*!*******************************!*\
  !*** ./src/lib/indicators.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TechnicalIndicators: () => (/* binding */ TechnicalIndicators)\n/* harmony export */ });\nclass TechnicalIndicators {\n    // Simple Moving Average\n    static sma(data, period) {\n        const result = [];\n        for(let i = period - 1; i < data.length; i++){\n            const sum = data.slice(i - period + 1, i + 1).reduce((a, b)=>a + b, 0);\n            result.push(sum / period);\n        }\n        return result;\n    }\n    // Exponential Moving Average\n    static ema(data, period) {\n        const result = [];\n        const multiplier = 2 / (period + 1);\n        // Start with SMA for first value\n        let ema = data.slice(0, period).reduce((a, b)=>a + b, 0) / period;\n        result.push(ema);\n        for(let i = period; i < data.length; i++){\n            ema = data[i] * multiplier + ema * (1 - multiplier);\n            result.push(ema);\n        }\n        return result;\n    }\n    // Relative Strength Index\n    static rsi(data, period = 14) {\n        const gains = [];\n        const losses = [];\n        for(let i = 1; i < data.length; i++){\n            const change = data[i] - data[i - 1];\n            gains.push(change > 0 ? change : 0);\n            losses.push(change < 0 ? Math.abs(change) : 0);\n        }\n        const avgGains = this.sma(gains, period);\n        const avgLosses = this.sma(losses, period);\n        return avgGains.map((gain, i)=>{\n            const rs = gain / avgLosses[i];\n            return 100 - 100 / (1 + rs);\n        });\n    }\n    // MACD (Moving Average Convergence Divergence)\n    static macd(data, fastPeriod = 12, slowPeriod = 26, signalPeriod = 9) {\n        const fastEMA = this.ema(data, fastPeriod);\n        const slowEMA = this.ema(data, slowPeriod);\n        // Align arrays (slowEMA starts later)\n        const startIndex = slowPeriod - fastPeriod;\n        const macdLine = fastEMA.slice(startIndex).map((fast, i)=>fast - slowEMA[i]);\n        const signalLine = this.ema(macdLine, signalPeriod);\n        const histogram = macdLine.slice(signalPeriod - 1).map((macd, i)=>macd - signalLine[i]);\n        return {\n            macd: macdLine,\n            signal: signalLine,\n            histogram\n        };\n    }\n    // Bollinger Bands\n    static bollingerBands(data, period = 20, stdDev = 2) {\n        const sma = this.sma(data, period);\n        const bands = sma.map((avg, i)=>{\n            const slice = data.slice(i, i + period);\n            const variance = slice.reduce((sum, val)=>sum + Math.pow(val - avg, 2), 0) / period;\n            const standardDeviation = Math.sqrt(variance);\n            return {\n                upper: avg + standardDeviation * stdDev,\n                middle: avg,\n                lower: avg - standardDeviation * stdDev\n            };\n        });\n        return bands;\n    }\n    // Support and Resistance Levels\n    static findSupportResistance(candles, lookback = 20) {\n        const highs = candles.map((c)=>c.high);\n        const lows = candles.map((c)=>c.low);\n        const resistance = [];\n        const support = [];\n        for(let i = lookback; i < candles.length - lookback; i++){\n            const currentHigh = highs[i];\n            const currentLow = lows[i];\n            // Check if current high is a local maximum\n            const isResistance = highs.slice(i - lookback, i).every((h)=>h <= currentHigh) && highs.slice(i + 1, i + lookback + 1).every((h)=>h <= currentHigh);\n            // Check if current low is a local minimum\n            const isSupport = lows.slice(i - lookback, i).every((l)=>l >= currentLow) && lows.slice(i + 1, i + lookback + 1).every((l)=>l >= currentLow);\n            if (isResistance) resistance.push(currentHigh);\n            if (isSupport) support.push(currentLow);\n        }\n        return {\n            support,\n            resistance\n        };\n    }\n    // Volume analysis\n    static volumeAnalysis(candles, period = 20) {\n        const volumes = candles.map((c)=>c.volume);\n        const avgVolume = this.sma(volumes, period);\n        const currentVolume = volumes[volumes.length - 1];\n        const currentAvgVolume = avgVolume[avgVolume.length - 1];\n        return {\n            currentVolume,\n            averageVolume: currentAvgVolume,\n            volumeRatio: currentVolume / currentAvgVolume,\n            isHighVolume: currentVolume > currentAvgVolume * 1.5,\n            isLowVolume: currentVolume < currentAvgVolume * 0.5\n        };\n    }\n    // Swing Trading Analysis\n    static analyzeSwingSetup(candles) {\n        const closes = candles.map((c)=>c.close);\n        const indicators = [];\n        // RSI Analysis\n        const rsi = this.rsi(closes);\n        const currentRSI = rsi[rsi.length - 1];\n        let rsiSignal = 'NEUTRAL';\n        let rsiDescription = `RSI: ${currentRSI.toFixed(2)}`;\n        if (currentRSI < 30) {\n            rsiSignal = 'BUY';\n            rsiDescription += ' - Oversold condition, potential bounce';\n        } else if (currentRSI > 70) {\n            rsiSignal = 'SELL';\n            rsiDescription += ' - Overbought condition, potential pullback';\n        } else {\n            rsiDescription += ' - Neutral zone';\n        }\n        indicators.push({\n            name: 'RSI',\n            value: currentRSI,\n            signal: rsiSignal,\n            description: rsiDescription\n        });\n        // Moving Average Analysis\n        const sma20 = this.sma(closes, 20);\n        const sma50 = this.sma(closes, 50);\n        const currentPrice = closes[closes.length - 1];\n        const currentSMA20 = sma20[sma20.length - 1];\n        const currentSMA50 = sma50[sma50.length - 1];\n        let maSignal = 'NEUTRAL';\n        let maDescription = `Price vs SMA20: ${((currentPrice / currentSMA20 - 1) * 100).toFixed(2)}%`;\n        if (currentPrice > currentSMA20 && currentSMA20 > currentSMA50) {\n            maSignal = 'BUY';\n            maDescription += ' - Bullish trend';\n        } else if (currentPrice < currentSMA20 && currentSMA20 < currentSMA50) {\n            maSignal = 'SELL';\n            maDescription += ' - Bearish trend';\n        } else {\n            maDescription += ' - Mixed signals';\n        }\n        indicators.push({\n            name: 'Moving Averages',\n            value: (currentPrice / currentSMA20 - 1) * 100,\n            signal: maSignal,\n            description: maDescription\n        });\n        // MACD Analysis\n        const macdData = this.macd(closes);\n        const currentMACD = macdData.macd[macdData.macd.length - 1];\n        const currentSignal = macdData.signal[macdData.signal.length - 1];\n        const currentHistogram = macdData.histogram[macdData.histogram.length - 1];\n        let macdSignal = 'NEUTRAL';\n        let macdDescription = `MACD: ${currentMACD.toFixed(4)}, Signal: ${currentSignal.toFixed(4)}`;\n        if (currentMACD > currentSignal && currentHistogram > 0) {\n            macdSignal = 'BUY';\n            macdDescription += ' - Bullish momentum';\n        } else if (currentMACD < currentSignal && currentHistogram < 0) {\n            macdSignal = 'SELL';\n            macdDescription += ' - Bearish momentum';\n        } else {\n            macdDescription += ' - Momentum shifting';\n        }\n        indicators.push({\n            name: 'MACD',\n            value: currentHistogram,\n            signal: macdSignal,\n            description: macdDescription\n        });\n        // Volume Analysis\n        const volumeData = this.volumeAnalysis(candles);\n        let volumeSignal = 'NEUTRAL';\n        let volumeDescription = `Volume: ${(volumeData.volumeRatio * 100).toFixed(0)}% of average`;\n        if (volumeData.isHighVolume) {\n            volumeSignal = 'BUY';\n            volumeDescription += ' - High volume confirms move';\n        } else if (volumeData.isLowVolume) {\n            volumeSignal = 'SELL';\n            volumeDescription += ' - Low volume, weak conviction';\n        } else {\n            volumeDescription += ' - Normal volume';\n        }\n        indicators.push({\n            name: 'Volume',\n            value: volumeData.volumeRatio,\n            signal: volumeSignal,\n            description: volumeDescription\n        });\n        return indicators;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/indicators.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/polygon.ts":
/*!****************************!*\
  !*** ./src/lib/polygon.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PolygonAPI: () => (/* binding */ PolygonAPI),\n/* harmony export */   polygonAPI: () => (/* binding */ polygonAPI)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/lib/axios.js\");\n\nconst POLYGON_BASE_URL = 'https://api.polygon.io';\nconst API_KEY = process.env.POLYGON_API_KEY;\nclass PolygonAPI {\n    constructor(apiKey){\n        this.apiKey = apiKey || API_KEY || '';\n        if (!this.apiKey) {\n            throw new Error('Polygon API key is required');\n        }\n    }\n    // Get current stock quote using Polygon's snapshot endpoint (better for paid plans)\n    async getStockQuote(symbol) {\n        try {\n            // Use snapshot endpoint for real-time data (available on paid plans)\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${POLYGON_BASE_URL}/v2/snapshot/locale/us/markets/stocks/tickers/${symbol}`, {\n                params: {\n                    apikey: this.apiKey\n                }\n            });\n            if (!response.data || !response.data.ticker) {\n                throw new Error(`No data found for ${symbol}`);\n            }\n            const data = response.data.ticker;\n            // Extract data from Polygon snapshot response structure\n            const dayData = data.day || {};\n            const prevDayData = data.prevDay || {};\n            const minData = data.min || {};\n            // Use the most recent price available\n            const currentPrice = dayData.c || minData.c || prevDayData.c;\n            const prevClose = prevDayData.c;\n            const change = data.todaysChange || currentPrice - prevClose;\n            const changePercent = data.todaysChangePerc || change / prevClose * 100;\n            return {\n                symbol: symbol.toUpperCase(),\n                name: data.name || symbol.toUpperCase(),\n                price: currentPrice || 0,\n                change: change || 0,\n                changePercent: changePercent || 0,\n                volume: dayData.v || minData.v || 1000000,\n                marketCap: data.market_cap || this.estimateMarketCap(symbol, currentPrice || 0),\n                pe: undefined,\n                dividend: undefined\n            };\n        } catch (error) {\n            console.error('Error fetching stock quote from Polygon:', error);\n            // Fallback to previous day data if snapshot fails\n            try {\n                const fallbackResponse = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/prev`, {\n                    params: {\n                        adjusted: 'true',\n                        apikey: this.apiKey\n                    }\n                });\n                const data = fallbackResponse.data.results[0];\n                return {\n                    symbol: symbol.toUpperCase(),\n                    name: symbol.toUpperCase(),\n                    price: data.c || 0,\n                    change: data.c - data.o || 0,\n                    changePercent: data.o ? (data.c - data.o) / data.o * 100 : 0,\n                    volume: data.v || 1000000,\n                    marketCap: this.estimateMarketCap(symbol, data.c || 0),\n                    pe: undefined,\n                    dividend: undefined\n                };\n            } catch (fallbackError) {\n                console.error('Polygon fallback also failed:', fallbackError);\n                throw new Error(`Failed to fetch quote for ${symbol}`);\n            }\n        }\n    }\n    /**\n   * Estimate market cap based on symbol and price\n   * This is a fallback when Polygon doesn't provide market cap data\n   */ estimateMarketCap(symbol, price) {\n        // Import stock universe data for better estimates\n        const stockEstimates = {\n            // Large cap (>$200B)\n            'AAPL': 3000000000000,\n            'MSFT': 2800000000000,\n            'NVDA': 1800000000000,\n            'GOOGL': 1700000000000,\n            'GOOG': 1700000000000,\n            'AMZN': ********00000,\n            'TSLA': 800000000000,\n            'META': 800000000000,\n            'BRK.B': 900000000000,\n            // Mid-large cap ($50B-$200B)\n            'JPM': 500000000000,\n            'V': 500000000000,\n            'UNH': 500000000000,\n            'JNJ': 450000000000,\n            'XOM': 450000000000,\n            'WMT': 600000000000,\n            'PG': ********0000,\n            'MA': ********0000,\n            'HD': 350000000000,\n            'CVX': 300000000000,\n            'ABBV': 300000000000,\n            'BAC': 300000000000,\n            'COST': 350000000000,\n            'AVGO': 600000000000,\n            'TSM': 500000000000,\n            // Mid cap ($10B-$50B)\n            'NFLX': ********0000,\n            'ORCL': 350000000000,\n            'CRM': 250000000000,\n            'ADBE': 2********000,\n            'AMD': 2********000,\n            'INTC': ********0000,\n            'QCOM': 180000000000,\n            'TMO': ********0000,\n            'DHR': 180000000000,\n            'CAT': 180000000000,\n            'GE': 180000000000,\n            'DIS': 180000000000,\n            'VZ': 170000000000,\n            'PFE': 160000000000,\n            'NKE': ********0000,\n            'MS': ********0000,\n            'UBER': ********0000,\n            'C': 1********000,\n            'GS': 1********000,\n            'T': 1********000,\n            'AMGN': ********0000,\n            'HON': 1********000,\n            'LOW': ********0000,\n            'BMY': 1********000,\n            'CMCSA': ********0000,\n            'SBUX': 1**********0,\n            'MMM': 60000000000,\n            // Smaller cap but popular swing trading stocks\n            'PLTR': 60000000000,\n            'SHOP': 80000000000,\n            'GILD': 80000000000,\n            'TGT': 70000000000,\n            'COP': ********0000,\n            'EOG': 70000000000,\n            'SLB': 60000000000,\n            'PYPL': 70000000000,\n            'SQ': ********000,\n            'COIN': 50000000000,\n            'DASH': 50000000000,\n            'MRNA': 30000000000,\n            'SNOW': 50000000000,\n            'ROKU': 5000000000,\n            'HOOD': ********000,\n            'LYFT': 6000000000,\n            'SPG': 50000000000,\n            'PLD': 1********000,\n            'NEE': ********0000\n        };\n        // Return estimated market cap if available, otherwise estimate based on price\n        if (stockEstimates[symbol]) {\n            return stockEstimates[symbol];\n        }\n        // Rough estimation based on price (very approximate)\n        if (price > 500) return **********00 // Assume large cap if high price\n        ;\n        if (price > 100) return 50000000000 // Assume mid-large cap\n        ;\n        if (price > 50) return ********000 // Assume mid cap\n        ;\n        if (price > 10) return 5000000000 // Assume small-mid cap\n        ;\n        return ********** // Default to $1B minimum for scanning\n        ;\n    }\n    // Get historical candlestick data (optimized for paid plans)\n    async getHistoricalData(symbol, timespan = 'day', multiplier = 1, from, to) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/range/${multiplier}/${timespan}/${from}/${to}`, {\n                params: {\n                    adjusted: 'true',\n                    sort: 'asc',\n                    limit: 50000,\n                    apikey: this.apiKey\n                }\n            });\n            if (!response.data.results || response.data.results.length === 0) {\n                console.warn(`No historical data found for ${symbol}`);\n                return [];\n            }\n            return response.data.results.map((candle)=>({\n                    timestamp: candle.t,\n                    open: candle.o,\n                    high: candle.h,\n                    low: candle.l,\n                    close: candle.c,\n                    volume: candle.v\n                }));\n        } catch (error) {\n            console.error(`Error fetching historical data for ${symbol}:`, error);\n            // Log the specific error for debugging\n            if (error.response) {\n                console.error(`Polygon API Error: ${error.response.status} - ${error.response.statusText}`);\n                console.error('Response data:', error.response.data);\n            }\n            throw new Error(`Failed to fetch historical data for ${symbol}: ${error.message}`);\n        }\n    }\n    // Get company details\n    async getCompanyDetails(symbol) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${POLYGON_BASE_URL}/v3/reference/tickers/${symbol}`, {\n                params: {\n                    apikey: this.apiKey\n                }\n            });\n            return response.data.results;\n        } catch (error) {\n            console.error('Error fetching company details:', error);\n            return null;\n        }\n    }\n    // Get market status\n    async getMarketStatus() {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${POLYGON_BASE_URL}/v1/marketstatus/now`, {\n                params: {\n                    apikey: this.apiKey\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching market status:', error);\n            return null;\n        }\n    }\n    // Search for stocks\n    async searchStocks(query, limit = 10) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${POLYGON_BASE_URL}/v3/reference/tickers`, {\n                params: {\n                    search: query,\n                    market: 'stocks',\n                    active: 'true',\n                    limit,\n                    apikey: this.apiKey\n                }\n            });\n            return response.data.results || [];\n        } catch (error) {\n            console.error('Error searching stocks:', error);\n            return [];\n        }\n    }\n}\n// Create a singleton instance\nconst polygonAPI = new PolygonAPI();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/polygon.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/swingStrategies.ts":
/*!************************************!*\
  !*** ./src/lib/swingStrategies.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SwingTradingStrategies: () => (/* binding */ SwingTradingStrategies)\n/* harmony export */ });\n/* harmony import */ var _indicators__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./indicators */ \"(rsc)/./src/lib/indicators.ts\");\n\nclass SwingTradingStrategies {\n    static{\n        this.DEFAULT_CRITERIA = {\n            minPrice: 5.0,\n            minVolume: 500000,\n            minMarketCap: **********,\n            minATRPercent: 2.0,\n            above200SMA: true,\n            maxDistanceFrom8EMA: 2.0,\n            minRoomToResistance: 1.0,\n            scanTimeStart: \"12:00\",\n            scanTimeEnd: \"16:00\",\n            maxRiskPerTrade: 1.0,\n            maxConcurrentPositions: 3\n        };\n    }\n    // Strategy #1: Overnight Momentum Continuation\n    static analyzeOvernightMomentum(symbol, candles, quote, accountSize = 100000) {\n        if (candles.length < 50) return null;\n        const closes = candles.map((c)=>c.close);\n        const highs = candles.map((c)=>c.high);\n        const lows = candles.map((c)=>c.low);\n        const volumes = candles.map((c)=>c.volume);\n        const currentPrice = quote.price;\n        const currentVolume = quote.volume;\n        const changePercent = quote.changePercent;\n        // Calculate technical indicators (adjusted for shorter history)\n        const sma50 = _indicators__WEBPACK_IMPORTED_MODULE_0__.TechnicalIndicators.sma(closes, Math.min(50, closes.length - 1)) // Use 50-day instead of 200-day\n        ;\n        const ema8 = _indicators__WEBPACK_IMPORTED_MODULE_0__.TechnicalIndicators.ema(closes, Math.min(8, closes.length - 1));\n        const atr = this.calculateATR(candles, Math.min(14, candles.length - 1));\n        const current50SMA = sma50[sma50.length - 1];\n        const current8EMA = ema8[ema8.length - 1];\n        const currentATR = atr[atr.length - 1];\n        // Basic qualification filters (using 50-day SMA instead of 200-day)\n        if (!this.passesBasicFilters(quote, currentVolume, current50SMA, currentPrice)) {\n            return null;\n        }\n        // Check if it's a top intraday gainer (top decile movers)\n        if (changePercent < 2.0) return null // Minimum 2% gain for momentum\n        ;\n        // Check distance from 8-EMA (not wildly extended)\n        const distanceFrom8EMA = Math.abs(currentPrice - current8EMA) / currentATR;\n        if (distanceFrom8EMA > this.DEFAULT_CRITERIA.maxDistanceFrom8EMA) return null;\n        // Look for defended intraday level (simplified - using VWAP proxy)\n        const vwap = this.calculateVWAP(candles.slice(-1)[0]);\n        const keyLevel = Math.max(vwap, currentPrice * 0.98) // Approximate defended level\n        ;\n        // Check if holding gains (>50% of day's range)\n        const todayHigh = highs[highs.length - 1];\n        const todayLow = lows[lows.length - 1];\n        const dayRange = todayHigh - todayLow;\n        const currentFromLow = currentPrice - todayLow;\n        const holdingGainsPercent = currentFromLow / dayRange;\n        if (holdingGainsPercent < 0.5) return null // Must hold >50% of range\n        ;\n        // Calculate room to next resistance\n        const roomToResistance = this.calculateRoomToResistance(candles, currentPrice, currentATR);\n        if (roomToResistance < this.DEFAULT_CRITERIA.minRoomToResistance) return null;\n        // Position sizing (risk 0.5-1% of account)\n        const riskPercent = 0.75 // 0.75% risk for overnight holds\n        ;\n        const stopDistance = currentPrice - keyLevel;\n        const riskAmount = accountSize * (riskPercent / 100);\n        const positionSize = Math.floor(riskAmount / stopDistance);\n        // Targets: Pre-market scale at 3-5%, opening hour at 5-8%\n        const targets = [\n            currentPrice * 1.03,\n            currentPrice * 1.05,\n            currentPrice * 1.08 // 8% extended target\n        ];\n        const confidence = this.calculateOvernightConfidence(changePercent, holdingGainsPercent, currentVolume, roomToResistance);\n        return {\n            strategy: 'overnight_momentum',\n            confidence,\n            entryPrice: currentPrice,\n            stopLoss: keyLevel,\n            targets,\n            positionSize,\n            riskAmount,\n            holdingPeriod: 'overnight',\n            keyLevel,\n            invalidation: `Daily close below ${keyLevel.toFixed(2)} or gap down below level`,\n            notes: [\n                'Enter final 30-60 min before close',\n                'Exit pre-market on strength or first 45min',\n                'Hard stop if gaps below defended level',\n                'Scale out aggressively if gaps >1 ATR up'\n            ],\n            // Precise entry execution\n            preciseEntry: {\n                price: currentPrice * 0.999,\n                orderType: 'limit',\n                timing: 'Final 30-60 minutes before market close',\n                conditions: [\n                    `Stock holding above ${keyLevel.toFixed(2)} (defended level)`,\n                    `Volume above ${(currentVolume * 0.8).toLocaleString()} shares`,\n                    `Price above ${current8EMA.toFixed(2)} (8-EMA)`,\n                    'No late-day selling pressure'\n                ],\n                urgency: 'wait_for_pullback'\n            },\n            // Precise exit execution\n            preciseExit: {\n                stopLoss: {\n                    price: keyLevel * 0.995,\n                    orderType: 'stop',\n                    reason: 'Defended level broken - invalidates setup',\n                    triggerConditions: [\n                        'Any close below defended level',\n                        'Gap down below key level',\n                        'Heavy selling into close'\n                    ]\n                },\n                takeProfits: [\n                    {\n                        price: targets[0],\n                        percentage: 33,\n                        target: 'T1 - Pre-market (3%)',\n                        reasoning: 'Take profits on pre-market strength',\n                        orderType: 'limit'\n                    },\n                    {\n                        price: targets[1],\n                        percentage: 33,\n                        target: 'T2 - Opening hour (5%)',\n                        reasoning: 'Scale out on opening momentum',\n                        orderType: 'limit'\n                    },\n                    {\n                        price: targets[2],\n                        percentage: 34,\n                        target: 'T3 - Extended (8%)',\n                        reasoning: 'Final exit on extended move',\n                        orderType: 'limit'\n                    }\n                ]\n            },\n            // Risk management details\n            riskManagement: {\n                maxRiskDollars: riskAmount,\n                accountRiskPercent: riskPercent,\n                sharesForRisk: positionSize,\n                invalidationPrice: keyLevel,\n                timeStopHours: 18,\n                maxDrawdownPercent: 2.0\n            },\n            // Execution plan\n            executionPlan: {\n                entryInstructions: [\n                    '1. Wait for final 30-60 minutes before close',\n                    '2. Confirm stock is holding defended level',\n                    '3. Place limit order slightly below current price',\n                    '4. Cancel if not filled by close'\n                ],\n                exitInstructions: [\n                    '1. Set stop-loss immediately after fill',\n                    '2. Monitor pre-market for gap up',\n                    '3. Scale out 1/3 at each target level',\n                    '4. Exit all by 10:15 AM if no momentum'\n                ],\n                monitoringPoints: [\n                    'Pre-market price action and volume',\n                    'Opening gap and first 15-minute candle',\n                    'Key level defense throughout session',\n                    'Overall market sentiment'\n                ],\n                contingencyPlans: [\n                    'If gaps down: Exit immediately at market open',\n                    'If gaps up >2%: Scale out more aggressively',\n                    'If sideways: Exit by 10:15 AM',\n                    'If market weakness: Tighten stops'\n                ]\n            }\n        };\n    }\n    // Strategy #2: Technical Breakout Trend-Follow (8-EMA + 200-SMA)\n    static analyzeTechnicalBreakout(symbol, candles, quote, accountSize = 100000) {\n        if (candles.length < 50) return null;\n        const closes = candles.map((c)=>c.close);\n        const volumes = candles.map((c)=>c.volume);\n        const currentPrice = quote.price;\n        // Calculate technical indicators (adjusted for shorter history)\n        const sma50 = _indicators__WEBPACK_IMPORTED_MODULE_0__.TechnicalIndicators.sma(closes, Math.min(50, closes.length - 1));\n        const ema8 = _indicators__WEBPACK_IMPORTED_MODULE_0__.TechnicalIndicators.ema(closes, Math.min(8, closes.length - 1));\n        const atr = this.calculateATR(candles, Math.min(14, candles.length - 1));\n        const current50SMA = sma50[sma50.length - 1];\n        const current8EMA = ema8[ema8.length - 1];\n        const currentATR = atr[atr.length - 1];\n        // Basic qualification filters (using 50-day SMA)\n        if (!this.passesBasicFilters(quote, quote.volume, current50SMA, currentPrice)) {\n            return null;\n        }\n        // Check if in clear uptrend above 50-SMA (adjusted from 200-SMA)\n        if (currentPrice <= current50SMA) return null;\n        // Check 8-EMA behavior - should be \"hugging\" the 8-EMA\n        const distanceFrom8EMA = Math.abs(currentPrice - current8EMA);\n        const emaDistancePercent = distanceFrom8EMA / currentPrice * 100;\n        // Should be close to 8-EMA (within 2-3% for quality trend-follow)\n        if (emaDistancePercent > 3.0) return null;\n        // Check for recent breakout or EMA reclaim\n        const recentEMAReclaim = this.checkEMAReclaim(closes, ema8, 5) // Last 5 days\n        ;\n        if (!recentEMAReclaim) return null;\n        // Volume expansion check\n        const avgVolume = _indicators__WEBPACK_IMPORTED_MODULE_0__.TechnicalIndicators.sma(volumes, 20);\n        const currentAvgVolume = avgVolume[avgVolume.length - 1];\n        const volumeExpansion = quote.volume / currentAvgVolume;\n        if (volumeExpansion < 1.2) return null // Need some volume expansion\n        ;\n        // Calculate room to next resistance\n        const roomToResistance = this.calculateRoomToResistance(candles, currentPrice, currentATR);\n        if (roomToResistance < 1.5) return null // Need more room for trend-follow\n        ;\n        // Position sizing (risk 1% of account)\n        const riskPercent = 1.0;\n        const stopDistance = currentPrice - current8EMA // Stop at 8-EMA break\n        ;\n        const riskAmount = accountSize * (riskPercent / 100);\n        const positionSize = Math.floor(riskAmount / stopDistance);\n        // Targets: Scale at resistance levels\n        const targets = [\n            currentPrice * 1.05,\n            currentPrice * 1.10,\n            currentPrice * 1.15 // 15% extended target\n        ];\n        const confidence = this.calculateBreakoutConfidence(emaDistancePercent, volumeExpansion, roomToResistance, quote.changePercent);\n        return {\n            strategy: 'technical_breakout',\n            confidence,\n            entryPrice: currentPrice,\n            stopLoss: current8EMA,\n            targets,\n            positionSize,\n            riskAmount,\n            holdingPeriod: 'days_to_weeks',\n            keyLevel: current8EMA,\n            invalidation: `Daily close below 8-EMA (${current8EMA.toFixed(2)})`,\n            notes: [\n                'Enter on afternoon reclaim of 8-EMA',\n                'Add only on higher-low pullbacks to 8-EMA',\n                'Scale partials at resistance levels',\n                'Exit on daily close below 8-EMA'\n            ],\n            // Precise entry execution\n            preciseEntry: {\n                price: current8EMA * 1.002,\n                orderType: 'limit',\n                timing: 'Afternoon reclaim or first pullback to 8-EMA',\n                conditions: [\n                    `Price reclaiming ${current8EMA.toFixed(2)} (8-EMA) with volume`,\n                    `Above ${current50SMA.toFixed(2)} (50-day SMA)`,\n                    `Volume expansion above ${(quote.volume * 1.2).toLocaleString()}`,\n                    'No major resistance overhead'\n                ],\n                urgency: 'breakout_confirmation'\n            },\n            // Precise exit execution\n            preciseExit: {\n                stopLoss: {\n                    price: current8EMA * 0.998,\n                    orderType: 'stop',\n                    reason: '8-EMA breakdown invalidates trend-follow setup',\n                    triggerConditions: [\n                        'Daily close below 8-EMA',\n                        'Intraday break with volume',\n                        'Loss of 50-SMA support'\n                    ]\n                },\n                takeProfits: [\n                    {\n                        price: targets[0],\n                        percentage: 25,\n                        target: 'R1 - First resistance (5%)',\n                        reasoning: 'Take partial profits at first resistance',\n                        orderType: 'limit'\n                    },\n                    {\n                        price: targets[1],\n                        percentage: 35,\n                        target: 'R2 - Major resistance (10%)',\n                        reasoning: 'Scale out at major resistance level',\n                        orderType: 'limit'\n                    },\n                    {\n                        price: targets[2],\n                        percentage: 40,\n                        target: 'R3 - Extension (15%)',\n                        reasoning: 'Final exit on extended breakout',\n                        orderType: 'limit'\n                    }\n                ]\n            },\n            // Risk management details\n            riskManagement: {\n                maxRiskDollars: riskAmount,\n                accountRiskPercent: riskPercent,\n                sharesForRisk: positionSize,\n                invalidationPrice: current8EMA,\n                timeStopHours: 72,\n                maxDrawdownPercent: 3.0\n            },\n            // Execution plan\n            executionPlan: {\n                entryInstructions: [\n                    '1. Wait for afternoon reclaim of 8-EMA',\n                    '2. Confirm volume expansion on breakout',\n                    '3. Place limit order above 8-EMA',\n                    '4. Only enter on higher-low pullbacks'\n                ],\n                exitInstructions: [\n                    '1. Set stop-loss below 8-EMA immediately',\n                    '2. Scale out 25% at first resistance',\n                    '3. Trail stop to breakeven after R1',\n                    '4. Exit remaining on 8-EMA breakdown'\n                ],\n                monitoringPoints: [\n                    '8-EMA as dynamic support/resistance',\n                    'Volume confirmation on moves',\n                    'Overall market trend alignment',\n                    'Sector strength/weakness'\n                ],\n                contingencyPlans: [\n                    'If fails at resistance: Tighten stops',\n                    'If market weakness: Exit early',\n                    'If sector rotation: Consider exit',\n                    'If extended: Take more profits'\n                ]\n            }\n        };\n    }\n    // Helper methods\n    static passesBasicFilters(quote, volume, sma50, price) {\n        return price >= this.DEFAULT_CRITERIA.minPrice && volume >= this.DEFAULT_CRITERIA.minVolume && (quote.marketCap || 0) >= this.DEFAULT_CRITERIA.minMarketCap && price > sma50 // Using 50-day SMA instead of 200-day for shorter history\n        ;\n    }\n    static calculateATR(candles, period) {\n        const trueRanges = [];\n        for(let i = 1; i < candles.length; i++){\n            const high = candles[i].high;\n            const low = candles[i].low;\n            const prevClose = candles[i - 1].close;\n            const tr = Math.max(high - low, Math.abs(high - prevClose), Math.abs(low - prevClose));\n            trueRanges.push(tr);\n        }\n        return _indicators__WEBPACK_IMPORTED_MODULE_0__.TechnicalIndicators.sma(trueRanges, period);\n    }\n    static calculateVWAP(candle) {\n        // Simplified VWAP calculation using typical price\n        return (candle.high + candle.low + candle.close) / 3;\n    }\n    static calculateRoomToResistance(candles, currentPrice, atr) {\n        // Find recent highs as resistance levels\n        const recentHighs = candles.slice(-20).map((c)=>c.high);\n        const maxHigh = Math.max(...recentHighs);\n        const roomToHigh = maxHigh - currentPrice;\n        return roomToHigh / atr;\n    }\n    static checkEMAReclaim(closes, ema8, lookback) {\n        // Check if price recently reclaimed 8-EMA\n        for(let i = Math.max(0, closes.length - lookback); i < closes.length - 1; i++){\n            if (closes[i] < ema8[i] && closes[i + 1] > ema8[i + 1]) {\n                return true // Found a reclaim\n                ;\n            }\n        }\n        return false;\n    }\n    static calculateOvernightConfidence(changePercent, holdingGains, volume, roomToResistance) {\n        let confidence = 50;\n        // Change percent bonus\n        if (changePercent > 5) confidence += 15;\n        else if (changePercent > 3) confidence += 10;\n        else if (changePercent > 2) confidence += 5;\n        // Holding gains bonus\n        if (holdingGains > 0.8) confidence += 15;\n        else if (holdingGains > 0.6) confidence += 10;\n        else if (holdingGains > 0.5) confidence += 5;\n        // Volume bonus\n        if (volume > 2000000) confidence += 10;\n        else if (volume > 1000000) confidence += 5;\n        // Room to resistance\n        if (roomToResistance > 2) confidence += 10;\n        else if (roomToResistance > 1.5) confidence += 5;\n        return Math.min(95, Math.max(30, confidence));\n    }\n    static calculateBreakoutConfidence(emaDistance, volumeExpansion, roomToResistance, changePercent) {\n        let confidence = 60;\n        // EMA proximity bonus (closer is better for trend-follow)\n        if (emaDistance < 1) confidence += 15;\n        else if (emaDistance < 2) confidence += 10;\n        else if (emaDistance < 3) confidence += 5;\n        // Volume expansion bonus\n        if (volumeExpansion > 2) confidence += 15;\n        else if (volumeExpansion > 1.5) confidence += 10;\n        else if (volumeExpansion > 1.2) confidence += 5;\n        // Room to resistance\n        if (roomToResistance > 3) confidence += 15;\n        else if (roomToResistance > 2) confidence += 10;\n        else if (roomToResistance > 1.5) confidence += 5;\n        // Positive momentum\n        if (changePercent > 2) confidence += 5;\n        return Math.min(95, Math.max(40, confidence));\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/swingStrategies.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/date-fns"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fscanner%2Fstrategies%2Froute&page=%2Fapi%2Fscanner%2Fstrategies%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscanner%2Fstrategies%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cshittyidea%5Cswing-trader-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();