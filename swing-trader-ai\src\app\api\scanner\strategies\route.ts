import { NextRequest, NextResponse } from 'next/server'
import { EnhancedSwingScanner } from '@/lib/enhancedSwingScanner'
import { PRIORITY_SWING_SYMBOLS, DEFAULT_SWING_SYMBOLS, getTopSwingTradingStocks } from '@/data/stockUniverse'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const scanType = searchParams.get('type') || 'quick' // quick, full
    const accountSize = parseInt(searchParams.get('accountSize') || '100000')
    const limit = parseInt(searchParams.get('limit') || '20')
    
    console.log(`Starting ${scanType} strategy scan...`)

    // Set account size for position sizing
    const scanner = new EnhancedSwingScanner(accountSize)

    let symbols: string[]
    let maxConcurrent: number

    if (scanType === 'full') {
      // Full scan: All 70+ stocks with slower processing
      symbols = DEFAULT_SWING_SYMBOLS
      maxConcurrent = 3
      console.log(`Full scan: ${symbols.length} stocks`)
    } else {
      // Quick scan: Top 30 swing trading candidates with faster processing
      symbols = PRIORITY_SWING_SYMBOLS
      maxConcurrent = 6
      console.log(`Quick scan: ${symbols.length} priority stocks`)
    }

    const summary = await scanner.scanWithStrategies(symbols, maxConcurrent)
    
    // Limit results if requested
    const limitedSummary = {
      ...summary,
      topSetups: summary.topSetups.slice(0, limit)
    }
    
    return NextResponse.json(limitedSummary)
  } catch (error) {
    console.error('Error in strategy scanner API:', error)
    return NextResponse.json(
      { error: 'Failed to perform strategy scan' },
      { status: 500 }
    )
  }
}
