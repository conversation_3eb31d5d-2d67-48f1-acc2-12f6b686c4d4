module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},14747,(e,t,r)=>{t.exports=e.x("path",()=>require("path"))},78006,e=>{"use strict";e.s(["PolygonAPI",()=>s]);var t=e.i(55362);let r="https://api.polygon.io",a=process.env.POLYGON_API_KEY;class s{apiKey;constructor(e){if(this.apiKey=e||a||"",!this.apiKey)throw Error("Polygon API key is required")}async getStockQuote(e){try{let a=(await t.default.get(`${r}/v2/snapshot/locale/us/markets/stocks/tickers/${e}`,{params:{apikey:this.apiKey}})).data.results[0];if(!a)throw Error(`No data found for ${e}`);let s=a.value||a,o=s.day||{},n=s.prevDay||{};s.lastQuote;let i=(s.lastTrade||{}).p||o.c||n.c,l=n.c||o.o,c=i-l;return{symbol:e.toUpperCase(),name:s.name||e.toUpperCase(),price:i,change:c,changePercent:c/l*100,volume:o.v||0,marketCap:s.market_cap,pe:void 0,dividend:void 0}}catch(a){console.error("Error fetching stock quote from Polygon:",a);try{let a=(await t.default.get(`${r}/v2/aggs/ticker/${e}/prev`,{params:{adjusted:"true",apikey:this.apiKey}})).data.results[0];return{symbol:e.toUpperCase(),name:e.toUpperCase(),price:a.c,change:a.c-a.o,changePercent:(a.c-a.o)/a.o*100,volume:a.v,marketCap:void 0,pe:void 0,dividend:void 0}}catch(t){throw console.error("Polygon fallback also failed:",t),Error(`Failed to fetch quote for ${e}`)}}}async getHistoricalData(e,a="day",s=1,o,n){try{let i=await t.default.get(`${r}/v2/aggs/ticker/${e}/range/${s}/${a}/${o}/${n}`,{params:{adjusted:"true",sort:"asc",limit:5e4,apikey:this.apiKey}});if(!i.data.results||0===i.data.results.length)return console.warn(`No historical data found for ${e}`),[];return i.data.results.map(e=>({timestamp:e.t,open:e.o,high:e.h,low:e.l,close:e.c,volume:e.v}))}catch(t){throw console.error(`Error fetching historical data for ${e}:`,t),t.response&&(console.error(`Polygon API Error: ${t.response.status} - ${t.response.statusText}`),console.error("Response data:",t.response.data)),Error(`Failed to fetch historical data for ${e}: ${t.message}`)}}async getCompanyDetails(e){try{return(await t.default.get(`${r}/v3/reference/tickers/${e}`,{params:{apikey:this.apiKey}})).data.results}catch(e){return console.error("Error fetching company details:",e),null}}async getMarketStatus(){try{return(await t.default.get(`${r}/v1/marketstatus/now`,{params:{apikey:this.apiKey}})).data}catch(e){return console.error("Error fetching market status:",e),null}}async searchStocks(e,a=10){try{return(await t.default.get(`${r}/v3/reference/tickers`,{params:{search:e,market:"stocks",active:"true",limit:a,apikey:this.apiKey}})).data.results||[]}catch(e){return console.error("Error searching stocks:",e),[]}}}new s},22275,(e,t,r)=>{},1122,e=>{"use strict";e.s(["handler",()=>A,"patchFetch",()=>P,"routeModule",()=>w,"serverHooks",()=>C,"workAsyncStorage",()=>E,"workUnitAsyncStorage",()=>k],1122);var t=e.i(11971),r=e.i(6780),a=e.i(51842),s=e.i(62950),o=e.i(21346),n=e.i(30506),i=e.i(63077),l=e.i(34765),c=e.i(64182),p=e.i(85062),d=e.i(51548),u=e.i(95133),h=e.i(8819),m=e.i(41050),y=e.i(93695);e.i(96641);var g=e.i(3893);e.s(["GET",()=>f],70562);var v=e.i(59169),x=e.i(78006);async function f(e,{params:t}){try{let{symbol:r}=await t,{searchParams:a}=new URL(e.url),s=a.get("timespan")||"day",o=parseInt(a.get("multiplier")||"1"),n=a.get("from"),i=a.get("to");if(!r)return v.NextResponse.json({error:"Symbol parameter is required"},{status:400});if(!n||!i)return v.NextResponse.json({error:"From and to date parameters are required"},{status:400});let l=new x.PolygonAPI(process.env.POLYGON_API_KEY),c=await l.getHistoricalData(r.toUpperCase(),s,o,n,i);return v.NextResponse.json(c)}catch(e){return console.error("Error in historical data API:",e),v.NextResponse.json({error:"Failed to fetch historical data"},{status:500})}}var R=e.i(70562);let w=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/stocks/historical/[symbol]/route",pathname:"/api/stocks/historical/[symbol]",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/swing-trader-ai/src/app/api/stocks/historical/[symbol]/route.ts",nextConfigOutput:"",userland:R}),{workAsyncStorage:E,workUnitAsyncStorage:k,serverHooks:C}=w;function P(){return(0,a.patchFetch)({workAsyncStorage:E,workUnitAsyncStorage:k})}async function A(e,t,a){var v;let x="/api/stocks/historical/[symbol]/route";x=x.replace(/\/index$/,"")||"/";let f=await w.prepare(e,t,{srcPage:x,multiZoneDraftMode:!1});if(!f)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:R,params:E,nextConfig:k,isDraftMode:C,prerenderManifest:P,routerServerContext:A,isOnDemandRevalidate:b,revalidateOnlyGenerated:N,resolvedPathname:$}=f,q=(0,n.normalizeAppPath)(x),j=!!(P.dynamicRoutes[q]||P.routes[$]);if(j&&!C){let e=!!P.routes[$],t=P.dynamicRoutes[q];if(t&&!1===t.fallback&&!e)throw new y.NoFallbackError}let O=null;!j||w.isDev||C||(O="/index"===(O=$)?"/":O);let T=!0===w.isDev||!j,_=j&&!T,I=e.method||"GET",S=(0,o.getTracer)(),U=S.getActiveScopeSpan(),H={params:E,prerenderManifest:P,renderOpts:{experimental:{cacheComponents:!!k.experimental.cacheComponents,authInterrupts:!!k.experimental.authInterrupts},supportsDynamicResponse:T,incrementalCache:(0,s.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(v=k.experimental)?void 0:v.cacheLife,isRevalidate:_,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>w.onRequestError(e,t,a,A)},sharedContext:{buildId:R}},K=new i.NodeNextRequest(e),D=new i.NodeNextResponse(t),M=l.NextRequestAdapter.fromNodeNextRequest(K,(0,l.signalFromNodeResponse)(t));try{let n=async r=>w.handle(M,H).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=S.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==c.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let s=a.get("next.route");if(s){let e=`${I} ${s}`;r.setAttributes({"next.route":s,"http.route":s,"next.span_name":e}),r.updateName(e)}else r.updateName(`${I} ${e.url}`)}),i=async o=>{var i,l;let c=async({previousCacheEntry:r})=>{try{if(!(0,s.getRequestMeta)(e,"minimalMode")&&b&&N&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let i=await n(o);e.fetchMetrics=H.renderOpts.fetchMetrics;let l=H.renderOpts.pendingWaitUntil;l&&a.waitUntil&&(a.waitUntil(l),l=void 0);let c=H.renderOpts.collectedTags;if(!j)return await (0,d.sendResponse)(K,D,i,H.renderOpts.pendingWaitUntil),null;{let e=await i.blob(),t=(0,u.toNodeOutgoingHttpHeaders)(i.headers);c&&(t[m.NEXT_CACHE_TAGS_HEADER]=c),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==H.renderOpts.collectedRevalidate&&!(H.renderOpts.collectedRevalidate>=m.INFINITE_CACHE)&&H.renderOpts.collectedRevalidate,a=void 0===H.renderOpts.collectedExpire||H.renderOpts.collectedExpire>=m.INFINITE_CACHE?void 0:H.renderOpts.collectedExpire;return{value:{kind:g.CachedRouteKind.APP_ROUTE,status:i.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await w.onRequestError(e,t,{routerKind:"App Router",routePath:x,routeType:"route",revalidateReason:(0,p.getRevalidateReason)({isRevalidate:_,isOnDemandRevalidate:b})},A),t}},y=await w.handleResponse({req:e,nextConfig:k,cacheKey:O,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:P,isRoutePPREnabled:!1,isOnDemandRevalidate:b,revalidateOnlyGenerated:N,responseGenerator:c,waitUntil:a.waitUntil});if(!j)return null;if((null==y||null==(i=y.value)?void 0:i.kind)!==g.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==y||null==(l=y.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,s.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",b?"REVALIDATED":y.isMiss?"MISS":y.isStale?"STALE":"HIT"),C&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let v=(0,u.fromNodeOutgoingHttpHeaders)(y.value.headers);return(0,s.getRequestMeta)(e,"minimalMode")&&j||v.delete(m.NEXT_CACHE_TAGS_HEADER),!y.cacheControl||t.getHeader("Cache-Control")||v.get("Cache-Control")||v.set("Cache-Control",(0,h.getCacheControlHeader)(y.cacheControl)),await (0,d.sendResponse)(K,D,new Response(y.value.body,{headers:v,status:y.value.status||200})),null};U?await i(U):await S.withPropagatedContext(e.headers,()=>S.trace(c.BaseServerSpan.handleRequest,{spanName:`${I} ${e.url}`,kind:o.SpanKind.SERVER,attributes:{"http.method":I,"http.target":e.url}},i))}catch(t){if(t instanceof y.NoFallbackError||await w.onRequestError(e,t,{routerKind:"App Router",routePath:q,routeType:"route",revalidateReason:(0,p.getRevalidateReason)({isRevalidate:_,isOnDemandRevalidate:b})}),j)throw t;return await (0,d.sendResponse)(K,D,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__d5327e76._.js.map