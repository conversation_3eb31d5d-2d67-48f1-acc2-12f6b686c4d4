{"version": 3, "sources": ["turbopack:///[project]/swing-trader-ai/node_modules/next/dist/esm/build/templates/app-route.js", "turbopack:///[project]/swing-trader-ai/src/app/api/backtest/perfect-pick/route.ts", "turbopack:///[project]/swing-trader-ai/src/lib/backtesting.ts"], "sourcesContent": ["import { AppRouteRouteModule } from \"next/dist/esm/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/esm/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/esm/server/lib/patch-fetch\";\nimport { getRequestMeta } from \"next/dist/esm/server/request-meta\";\nimport { getTracer, SpanKind } from \"next/dist/esm/server/lib/trace/tracer\";\nimport { normalizeAppPath } from \"next/dist/esm/shared/lib/router/utils/app-paths\";\nimport { NodeNextRequest, NodeNextResponse } from \"next/dist/esm/server/base-http/node\";\nimport { NextRequestAdapter, signalFromNodeResponse } from \"next/dist/esm/server/web/spec-extension/adapters/next-request\";\nimport { BaseServerSpan } from \"next/dist/esm/server/lib/trace/constants\";\nimport { getRevalidateReason } from \"next/dist/esm/server/instrumentation/utils\";\nimport { sendResponse } from \"next/dist/esm/server/send-response\";\nimport { fromNodeOutgoingHttpHeaders, toNodeOutgoingHttpHeaders } from \"next/dist/esm/server/web/utils\";\nimport { getCacheControlHeader } from \"next/dist/esm/server/lib/cache-control\";\nimport { INFINITE_CACHE, NEXT_CACHE_TAGS_HEADER } from \"next/dist/esm/lib/constants\";\nimport { NoFallbackError } from \"next/dist/esm/shared/lib/no-fallback-error.external\";\nimport { CachedRouteKind } from \"next/dist/esm/server/response-cache\";\nimport * as userland from \"INNER_APP_ROUTE\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/backtest/perfect-pick/route\",\n        pathname: \"/api/backtest/perfect-pick\",\n        filename: \"route\",\n        bundlePath: \"\"\n    },\n    distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n    relativeProjectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || '',\n    resolvedPagePath: \"[project]/swing-trader-ai/src/app/api/backtest/perfect-pick/route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\nexport async function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/backtest/perfect-pick/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (process.env.TURBOPACK) {\n        srcPage = srcPage.replace(/\\/index$/, '') || '/';\n    } else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = process.env.__NEXT_MULTI_ZONE_DRAFT_MODE;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = normalizeAppPath(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = getTracer();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: getRequestMeta(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new NodeNextRequest(req);\n    const nodeNextRes = new NodeNextResponse(res);\n    const nextReq = NextRequestAdapter.fromNodeNextRequest(nodeNextReq, signalFromNodeResponse(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!getRequestMeta(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = toNodeOutgoingHttpHeaders(response.headers);\n                        if (cacheTags) {\n                            headers[NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await sendResponse(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: getRevalidateReason({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!getRequestMeta(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = fromNodeOutgoingHttpHeaders(cacheEntry.value.headers);\n            if (!(getRequestMeta(req, 'minimalMode') && isIsr)) {\n                headers.delete(NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', getCacheControlHeader(cacheEntry.cacheControl));\n            }\n            await sendResponse(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        if (!(err instanceof NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: getRevalidateReason({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await sendResponse(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n", "import { NextRequest, NextResponse } from 'next/server'\nimport { PerfectPickBacktester } from '@/lib/backtesting'\nimport { PolygonAPI } from '@/lib/polygon'\nimport { PerfectPickSetup } from '@/types/trading'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const { setups, maxHoldingDays = 30, startDate, endDate } = body\n\n    if (!setups || !Array.isArray(setups)) {\n      return NextResponse.json(\n        { success: false, error: 'Setups array is required' },\n        { status: 400 }\n      )\n    }\n\n    console.log(`🧪 Starting backtest for ${setups.length} Perfect-Pick setups...`)\n\n    const backtester = new PerfectPickBacktester()\n    const polygonAPI = new PolygonAPI(process.env.POLYGON_API_KEY)\n\n    // Helper function to get historical data\n    const getHistoricalData = async (symbol: string) => {\n      const end = endDate || new Date().toISOString().split('T')[0]\n      const start = startDate || new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 1 year ago\n      \n      return await polygonAPI.getHistoricalData(symbol, start, end, '1', 'day')\n    }\n\n    // Run backtest\n    const results = await backtester.backtestMultipleSetups(\n      setups as PerfectPickSetup[],\n      getHistoricalData,\n      maxHoldingDays\n    )\n\n    // Generate comprehensive summary\n    const summary = backtester.generateSummary(results)\n    const performanceByCatalystTier = backtester.getPerformanceByCatalystTier(results)\n    const performanceByTechnicalGrade = backtester.getPerformanceByTechnicalGrade(results)\n\n    console.log(`✅ Backtest complete. ${results.length} trades analyzed.`)\n\n    const response = {\n      success: true,\n      data: {\n        results,\n        summary,\n        performanceByCatalystTier,\n        performanceByTechnicalGrade,\n        metadata: {\n          totalSetups: setups.length,\n          successfulBacktests: results.length,\n          failedBacktests: setups.length - results.length,\n          maxHoldingDays,\n          dateRange: { startDate, endDate },\n          generatedAt: new Date().toISOString()\n        }\n      }\n    }\n\n    return NextResponse.json(response)\n  } catch (error) {\n    console.error('Error in Perfect-Pick backtest API:', error)\n    return NextResponse.json(\n      { \n        success: false, \n        error: 'Failed to run backtest',\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    \n    const symbols = searchParams.get('symbols')?.split(',').filter(Boolean)\n    const maxHoldingDays = parseInt(searchParams.get('maxHoldingDays') || '30')\n    const startDate = searchParams.get('startDate')\n    const endDate = searchParams.get('endDate')\n    const catalystTypes = searchParams.get('catalystTypes')?.split(',').filter(Boolean)\n    const minSetupGrade = searchParams.get('minSetupGrade') || 'C'\n\n    if (!symbols || symbols.length === 0) {\n      return NextResponse.json(\n        { success: false, error: 'Symbols parameter is required' },\n        { status: 400 }\n      )\n    }\n\n    console.log(`🧪 Running historical Perfect-Pick backtest for ${symbols.length} symbols...`)\n\n    // This would typically involve:\n    // 1. Historical catalyst detection for the given symbols and date range\n    // 2. Historical Perfect-Pick setup generation\n    // 3. Backtesting those setups\n    // For now, we'll return a simplified response\n\n    const mockResults = symbols.map(symbol => ({\n      symbol,\n      totalTrades: Math.floor(Math.random() * 20) + 5,\n      winRate: 0.6 + Math.random() * 0.3,\n      avgRMultiple: 1.5 + Math.random() * 2,\n      totalReturn: Math.random() * 50 - 10,\n      maxDrawdown: -(Math.random() * 15 + 5)\n    }))\n\n    const response = {\n      success: true,\n      data: {\n        historicalBacktest: mockResults,\n        summary: {\n          totalSymbols: symbols.length,\n          avgWinRate: mockResults.reduce((sum, r) => sum + r.winRate, 0) / mockResults.length,\n          avgRMultiple: mockResults.reduce((sum, r) => sum + r.avgRMultiple, 0) / mockResults.length,\n          totalTrades: mockResults.reduce((sum, r) => sum + r.totalTrades, 0)\n        },\n        parameters: {\n          symbols,\n          maxHoldingDays,\n          dateRange: { startDate, endDate },\n          catalystTypes,\n          minSetupGrade\n        },\n        generatedAt: new Date().toISOString()\n      }\n    }\n\n    return NextResponse.json(response)\n  } catch (error) {\n    console.error('Error in historical backtest API:', error)\n    return NextResponse.json(\n      { \n        success: false, \n        error: 'Failed to run historical backtest',\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    )\n  }\n}\n", "import { PerfectPickSetup, CandlestickData, Catalyst } from '@/types/trading'\n\nexport interface BacktestResult {\n  symbol: string\n  entryDate: string\n  entryPrice: number\n  exitDate?: string\n  exitPrice?: number\n  stopLoss: number\n  targets: number[]\n  outcome: 'win' | 'loss' | 'breakeven' | 'open'\n  rMultiple: number // Actual R achieved\n  holdingDays: number\n  maxDrawdown: number\n  maxRunup: number\n  exitReason: 'stop_loss' | 'target_hit' | 'time_exit' | 'manual' | 'still_open'\n  catalystType: string\n  catalystQuality: number\n  setupGrade: string\n  technicalScore: number\n}\n\nexport interface BacktestSummary {\n  totalTrades: number\n  winRate: number\n  avgRMultiple: number\n  avgHoldingDays: number\n  maxDrawdown: number\n  totalReturn: number\n  sharpeRatio: number\n  profitFactor: number\n  largestWin: number\n  largestLoss: number\n  consecutiveWins: number\n  consecutiveLosses: number\n  bySetupGrade: Record<string, BacktestSummary>\n  byCatalystType: Record<string, BacktestSummary>\n  monthlyReturns: { month: string; return: number }[]\n}\n\nexport class PerfectPickBacktester {\n  private results: BacktestResult[] = []\n\n  /**\n   * Backtest a Perfect-Pick setup using historical data\n   */\n  async backtestSetup(\n    setup: PerfectPickSetup,\n    historicalData: CandlestickData[],\n    maxHoldingDays: number = 30\n  ): Promise<BacktestResult> {\n    const entryPrice = setup.riskManagement.entryPrice\n    const stopLoss = setup.riskManagement.stopLoss\n    const targets = [\n      setup.rewardPlanning.target3R,\n      setup.rewardPlanning.target4R,\n      setup.rewardPlanning.target5R\n    ]\n\n    // Find entry date in historical data\n    const entryIndex = historicalData.findIndex(candle => \n      Math.abs(candle.close - entryPrice) / entryPrice < 0.02 // Within 2% of entry price\n    )\n\n    if (entryIndex === -1) {\n      throw new Error('Entry price not found in historical data')\n    }\n\n    const entryCandle = historicalData[entryIndex]\n    let exitPrice: number | undefined\n    let exitDate: string | undefined\n    let exitReason: BacktestResult['exitReason'] = 'still_open'\n    let outcome: BacktestResult['outcome'] = 'open'\n    let rMultiple = 0\n    let maxDrawdown = 0\n    let maxRunup = 0\n    let holdingDays = 0\n\n    // Simulate trade execution from entry point\n    for (let i = entryIndex + 1; i < Math.min(entryIndex + maxHoldingDays, historicalData.length); i++) {\n      const candle = historicalData[i]\n      holdingDays = i - entryIndex\n\n      // Calculate current P&L\n      const currentPL = (candle.close - entryPrice) / (entryPrice - stopLoss)\n      maxRunup = Math.max(maxRunup, currentPL)\n      maxDrawdown = Math.min(maxDrawdown, currentPL)\n\n      // Check for stop loss hit\n      if (candle.low <= stopLoss) {\n        exitPrice = stopLoss\n        exitDate = new Date(candle.timestamp).toISOString()\n        exitReason = 'stop_loss'\n        outcome = 'loss'\n        rMultiple = -1\n        break\n      }\n\n      // Check for target hits (in order)\n      for (let j = 0; j < targets.length; j++) {\n        if (candle.high >= targets[j]) {\n          exitPrice = targets[j]\n          exitDate = new Date(candle.timestamp).toISOString()\n          exitReason = 'target_hit'\n          outcome = 'win'\n          rMultiple = j + 3 // 3R, 4R, or 5R\n          break\n        }\n      }\n\n      if (exitPrice) break\n    }\n\n    // If no exit found, use time-based exit\n    if (!exitPrice && holdingDays >= maxHoldingDays) {\n      const lastCandle = historicalData[Math.min(entryIndex + maxHoldingDays, historicalData.length - 1)]\n      exitPrice = lastCandle.close\n      exitDate = new Date(lastCandle.timestamp).toISOString()\n      exitReason = 'time_exit'\n      rMultiple = (exitPrice - entryPrice) / (entryPrice - stopLoss)\n      outcome = rMultiple > 0 ? 'win' : rMultiple < 0 ? 'loss' : 'breakeven'\n    }\n\n    const result: BacktestResult = {\n      symbol: setup.symbol,\n      entryDate: new Date(entryCandle.timestamp).toISOString(),\n      entryPrice,\n      exitDate,\n      exitPrice,\n      stopLoss,\n      targets,\n      outcome,\n      rMultiple,\n      holdingDays,\n      maxDrawdown,\n      maxRunup,\n      exitReason,\n      catalystType: setup.catalyst.type,\n      catalystQuality: setup.catalyst.qualityScore,\n      setupGrade: setup.setupGrade,\n      technicalScore: setup.technicalGate.gateScore\n    }\n\n    this.results.push(result)\n    return result\n  }\n\n  /**\n   * Batch backtest multiple setups\n   */\n  async backtestMultipleSetups(\n    setups: PerfectPickSetup[],\n    getHistoricalData: (symbol: string) => Promise<CandlestickData[]>,\n    maxHoldingDays: number = 30\n  ): Promise<BacktestResult[]> {\n    const results: BacktestResult[] = []\n\n    for (const setup of setups) {\n      try {\n        const historicalData = await getHistoricalData(setup.symbol)\n        const result = await this.backtestSetup(setup, historicalData, maxHoldingDays)\n        results.push(result)\n      } catch (error) {\n        console.error(`Error backtesting ${setup.symbol}:`, error)\n      }\n    }\n\n    return results\n  }\n\n  /**\n   * Generate comprehensive backtest summary\n   */\n  generateSummary(results: BacktestResult[]): BacktestSummary {\n    if (results.length === 0) {\n      return this.getEmptySummary()\n    }\n\n    const completedTrades = results.filter(r => r.outcome !== 'open')\n    const wins = completedTrades.filter(r => r.outcome === 'win')\n    const losses = completedTrades.filter(r => r.outcome === 'loss')\n\n    const winRate = completedTrades.length > 0 ? wins.length / completedTrades.length : 0\n    const avgRMultiple = completedTrades.length > 0 \n      ? completedTrades.reduce((sum, r) => sum + r.rMultiple, 0) / completedTrades.length \n      : 0\n\n    const avgHoldingDays = completedTrades.length > 0\n      ? completedTrades.reduce((sum, r) => sum + r.holdingDays, 0) / completedTrades.length\n      : 0\n\n    const totalReturn = completedTrades.reduce((sum, r) => sum + r.rMultiple, 0)\n    const maxDrawdown = Math.min(...results.map(r => r.maxDrawdown))\n\n    // Calculate Profit Factor\n    const grossProfit = wins.reduce((sum, r) => sum + r.rMultiple, 0)\n    const grossLoss = Math.abs(losses.reduce((sum, r) => sum + r.rMultiple, 0))\n    const profitFactor = grossLoss > 0 ? grossProfit / grossLoss : grossProfit > 0 ? Infinity : 0\n\n    // Calculate Sharpe Ratio (simplified)\n    const returns = completedTrades.map(r => r.rMultiple)\n    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length\n    const stdDev = Math.sqrt(returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length)\n    const sharpeRatio = stdDev > 0 ? avgReturn / stdDev : 0\n\n    // Find largest win/loss\n    const largestWin = Math.max(...completedTrades.map(r => r.rMultiple))\n    const largestLoss = Math.min(...completedTrades.map(r => r.rMultiple))\n\n    // Calculate consecutive wins/losses\n    const { consecutiveWins, consecutiveLosses } = this.calculateConsecutiveWinsLosses(completedTrades)\n\n    // Group by setup grade\n    const bySetupGrade = this.groupResultsByField(completedTrades, 'setupGrade')\n\n    // Group by catalyst type\n    const byCatalystType = this.groupResultsByField(completedTrades, 'catalystType')\n\n    // Generate monthly returns (simplified)\n    const monthlyReturns = this.calculateMonthlyReturns(completedTrades)\n\n    return {\n      totalTrades: completedTrades.length,\n      winRate,\n      avgRMultiple,\n      avgHoldingDays,\n      maxDrawdown,\n      totalReturn,\n      sharpeRatio,\n      profitFactor,\n      largestWin,\n      largestLoss,\n      consecutiveWins,\n      consecutiveLosses,\n      bySetupGrade,\n      byCatalystType,\n      monthlyReturns\n    }\n  }\n\n  /**\n   * Get performance by catalyst tier\n   */\n  getPerformanceByCatalystTier(results: BacktestResult[]): Record<string, BacktestSummary> {\n    const tier1Types = ['earnings_beat_guidance', 'fda_approval', 'merger_acquisition']\n    const tier2Types = ['analyst_upgrade', 'stock_split', 'partnership']\n    \n    const tier1Results = results.filter(r => tier1Types.includes(r.catalystType))\n    const tier2Results = results.filter(r => tier2Types.includes(r.catalystType))\n    const tier3Results = results.filter(r => !tier1Types.includes(r.catalystType) && !tier2Types.includes(r.catalystType))\n\n    return {\n      'Tier 1': this.generateSummary(tier1Results),\n      'Tier 2': this.generateSummary(tier2Results),\n      'Tier 3': this.generateSummary(tier3Results)\n    }\n  }\n\n  /**\n   * Get performance by technical grade\n   */\n  getPerformanceByTechnicalGrade(results: BacktestResult[]): Record<string, BacktestSummary> {\n    const gradeA = results.filter(r => r.setupGrade.startsWith('A'))\n    const gradeB = results.filter(r => r.setupGrade.startsWith('B'))\n    const gradeC = results.filter(r => r.setupGrade.startsWith('C'))\n\n    return {\n      'Grade A': this.generateSummary(gradeA),\n      'Grade B': this.generateSummary(gradeB),\n      'Grade C': this.generateSummary(gradeC)\n    }\n  }\n\n  private getEmptySummary(): BacktestSummary {\n    return {\n      totalTrades: 0,\n      winRate: 0,\n      avgRMultiple: 0,\n      avgHoldingDays: 0,\n      maxDrawdown: 0,\n      totalReturn: 0,\n      sharpeRatio: 0,\n      profitFactor: 0,\n      largestWin: 0,\n      largestLoss: 0,\n      consecutiveWins: 0,\n      consecutiveLosses: 0,\n      bySetupGrade: {},\n      byCatalystType: {},\n      monthlyReturns: []\n    }\n  }\n\n  private calculateConsecutiveWinsLosses(results: BacktestResult[]): { consecutiveWins: number; consecutiveLosses: number } {\n    let maxConsecutiveWins = 0\n    let maxConsecutiveLosses = 0\n    let currentWinStreak = 0\n    let currentLossStreak = 0\n\n    for (const result of results) {\n      if (result.outcome === 'win') {\n        currentWinStreak++\n        currentLossStreak = 0\n        maxConsecutiveWins = Math.max(maxConsecutiveWins, currentWinStreak)\n      } else if (result.outcome === 'loss') {\n        currentLossStreak++\n        currentWinStreak = 0\n        maxConsecutiveLosses = Math.max(maxConsecutiveLosses, currentLossStreak)\n      }\n    }\n\n    return {\n      consecutiveWins: maxConsecutiveWins,\n      consecutiveLosses: maxConsecutiveLosses\n    }\n  }\n\n  private groupResultsByField(results: BacktestResult[], field: keyof BacktestResult): Record<string, BacktestSummary> {\n    const grouped = results.reduce((acc, result) => {\n      const key = String(result[field])\n      if (!acc[key]) acc[key] = []\n      acc[key].push(result)\n      return acc\n    }, {} as Record<string, BacktestResult[]>)\n\n    const summaries: Record<string, BacktestSummary> = {}\n    for (const [key, groupResults] of Object.entries(grouped)) {\n      summaries[key] = this.generateSummary(groupResults)\n    }\n\n    return summaries\n  }\n\n  private calculateMonthlyReturns(results: BacktestResult[]): { month: string; return: number }[] {\n    const monthlyData = results.reduce((acc, result) => {\n      const month = new Date(result.entryDate).toISOString().slice(0, 7) // YYYY-MM\n      if (!acc[month]) acc[month] = 0\n      acc[month] += result.rMultiple\n      return acc\n    }, {} as Record<string, number>)\n\n    return Object.entries(monthlyData)\n      .map(([month, returnValue]) => ({ month, return: returnValue }))\n      .sort((a, b) => a.month.localeCompare(b.month))\n  }\n\n  /**\n   * Export results to CSV format\n   */\n  exportToCSV(results: BacktestResult[]): string {\n    const headers = [\n      'Symbol', 'Entry Date', 'Entry Price', 'Exit Date', 'Exit Price',\n      'Stop Loss', 'Outcome', 'R Multiple', 'Holding Days', 'Exit Reason',\n      'Catalyst Type', 'Catalyst Quality', 'Setup Grade', 'Technical Score'\n    ]\n\n    const rows = results.map(result => [\n      result.symbol,\n      result.entryDate,\n      result.entryPrice.toFixed(2),\n      result.exitDate || '',\n      result.exitPrice?.toFixed(2) || '',\n      result.stopLoss.toFixed(2),\n      result.outcome,\n      result.rMultiple.toFixed(2),\n      result.holdingDays.toString(),\n      result.exitReason,\n      result.catalystType,\n      result.catalystQuality.toString(),\n      result.setupGrade,\n      result.technicalScore.toString()\n    ])\n\n    return [headers, ...rows].map(row => row.join(',')).join('\\n')\n  }\n}\n"], "names": [], "mappings": "qLAAA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,CAAA,CAAA,OAAA,IAAA,EAAA,EAAA,CAAA,CAAA,4CCfA,IAAA,EAAA,EAAA,CAAA,CAAA,MCwCO,OAAM,EACH,QAA4B,EAAE,AAKtC,OAAM,cACJ,CAAuB,CACvB,CAAiC,CACjC,EAAyB,EAAE,CACF,CACzB,IAkBI,EACA,EAnBE,EAAa,EAAM,cAAc,CAAC,UAAU,CAC5C,EAAW,EAAM,cAAc,CAAC,QAAQ,CACxC,EAAU,CACd,EAAM,cAAc,CAAC,QAAQ,CAC7B,EAAM,cAAc,CAAC,QAAQ,CAC7B,EAAM,cAAc,CAAC,QAAQ,CAC9B,CAGK,EAAa,EAAe,SAAS,CAAC,GAC1C,KAAK,GAAG,CAAC,EAAO,KAAK,CAAG,GAAc,EAAa,KAAK,AAG1D,GAAmB,CAAC,GAAG,CAAnB,EACF,MAAM,AAAI,MAAM,KAJmE,uCAOrF,IAAM,EAAc,CAAc,CAAC,EAAW,CAG1C,EAA2C,aAC3C,EAAqC,OACrC,EAAY,EACZ,EAAc,EACd,EAAW,EACX,EAAc,EAGlB,IAAK,IAAI,EAAI,EAAa,EAAG,EAAI,KAAK,GAAG,CAAC,EAAa,EAAgB,EAAe,MAAM,EAAG,IAAK,CAClG,IAAM,EAAS,CAAc,CAAC,EAAE,CAChC,EAAc,EAAI,EAGlB,IAAM,EAAY,CAAC,EAAO,KAAK,CAAG,CAAA,CAAU,EAAK,EAAD,AAAc,CAAA,CAAQ,CAKtE,GAJA,EAAW,KAAK,GAAG,CAAC,EAAU,GAC9B,EAAc,KAAK,GAAG,CAAC,EAAa,GAGhC,EAAO,GAAG,EAAI,EAAU,CAC1B,EAAY,EACZ,EAAW,IAAI,KAAK,EAAO,SAAS,EAAE,WAAW,GACjD,EAAa,YACb,EAAU,OACV,EAAY,CAAC,EACb,KACF,CAGA,IAAK,IAAI,EAAI,EAAG,EAAI,EAAQ,MAAM,CAAE,IAAK,AACvC,GAAI,EAAO,IAAI,EAAI,CAAO,CAAC,EAAE,CAAE,CAC7B,EAAY,CAAO,CAAC,EAAE,CACtB,EAAW,IAAI,KAAK,EAAO,SAAS,EAAE,WAAW,GACjD,EAAa,aACb,EAAU,MACV,EAAY,EAAI,EAChB,CADkB,IAEpB,CAGF,GAAI,EAAW,KACjB,CAGA,AATwC,GASpC,CAAC,GAAa,GAAe,EAAgB,CAC/C,IAAM,EAAa,CAAc,CAAC,KAAK,GAAG,CAAC,EAAa,EAAgB,EAAe,MAAM,CAAG,GAAG,CACnG,EAAY,EAAW,KAAK,CAC5B,EAAW,IAAI,KAAK,EAAW,SAAS,EAAE,WAAW,GACrD,EAAa,YAEb,EAAU,AADV,GAAY,CAAC,EAAY,CAAA,CAAU,EAAK,EAAD,AAAc,CAAA,CAAQ,EACvC,EAAI,MAAQ,EAAY,EAAI,OAAS,WAC7D,CAEA,IAAM,EAAyB,CAC7B,OAAQ,EAAM,MAAM,CACpB,UAAW,IAAI,KAAK,EAAY,SAAS,EAAE,WAAW,cACtD,WACA,YACA,WACA,UACA,UACA,YACA,cACA,cACA,WACA,EACA,aACA,aAAc,EAAM,QAAQ,CAAC,IAAI,CACjC,gBAAiB,EAAM,QAAQ,CAAC,YAAY,CAC5C,WAAY,EAAM,UAAU,CAC5B,eAAgB,EAAM,aAAa,CAAC,SAAS,AAC/C,EAGA,OADA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GACX,CACT,CAKA,MAAM,uBACJ,CAA0B,CAC1B,CAAiE,CACjE,EAAyB,EAAE,CACA,CAC3B,IAAM,EAA4B,EAAE,CAEpC,IAAK,IAAM,KAAS,EAClB,GAAI,CACF,CAFwB,GAElB,EAAiB,MAAM,EAAkB,EAAM,MAAM,EACrD,EAAS,MAAM,IAAI,CAAC,aAAa,CAAC,EAAO,EAAgB,GAC/D,EAAQ,IAAI,CAAC,EACf,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,CAAC,kBAAkB,EAAE,EAAM,MAAM,CAAC,CAAC,CAAC,CAAE,EACtD,CAGF,OAAO,CACT,CAKA,gBAAgB,CAAyB,CAAmB,CAC1D,GAAuB,GAAG,CAAtB,EAAQ,MAAM,CAChB,OAAO,IAAI,CAAC,eAAe,GAG7B,IAAM,EAAkB,EAAQ,MAAM,CAAC,GAAmB,SAAd,EAAE,OAAO,EAC/C,EAAO,EAAgB,MAAM,CAAC,GAAmB,QAAd,EAAE,OAAO,EAC5C,EAAS,EAAgB,MAAM,CAAC,GAAmB,SAAd,EAAE,OAAO,EAE9C,EAAU,EAAgB,MAAM,CAAG,EAAI,EAAK,MAAM,CAAG,EAAgB,MAAM,CAAG,EAC9E,EAAe,EAAgB,MAAM,CAAG,EAC1C,EAAgB,MAAM,CAAC,CAAC,EAAK,IAAM,EAAM,EAAE,SAAS,CAAE,GAAK,EAAgB,MAAM,CACjF,EAEE,EAAiB,EAAgB,MAAM,CAAG,EAC5C,EAAgB,MAAM,CAAC,CAAC,EAAK,IAAM,EAAM,EAAE,WAAW,CAAE,GAAK,EAAgB,MAAM,CACnF,EAEE,EAAc,EAAgB,MAAM,CAAC,CAAC,EAAK,IAAM,EAAM,EAAE,SAAS,CAAE,GACpE,EAAc,KAAK,GAAG,IAAI,EAAQ,GAAG,CAAC,GAAK,EAAE,WAAW,GAGxD,EAAc,EAAK,MAAM,CAAC,CAAC,EAAK,IAAM,EAAM,EAAE,SAAS,CAAE,GACzD,EAAY,KAAK,GAAG,CAAC,EAAO,MAAM,CAAC,CAAC,EAAK,IAAM,EAAM,EAAE,SAAS,CAAE,IAIlE,EAAU,EAAgB,GAAG,CAAC,GAAK,EAAE,SAAS,EAC9C,EAAY,EAAQ,MAAM,CAAC,CAAC,EAAK,IAAM,EAAM,EAAG,GAAK,EAAQ,MAAM,CACnE,EAAS,KAAK,IAAI,CAAC,EAAQ,MAAM,CAAC,CAAC,EAAK,IAAM,EAAM,KAAK,GAAG,CAAC,EAAI,EAAW,GAAI,GAAK,EAAQ,MAAM,EAInG,EAAa,KAAK,GAAG,IAAI,EAAgB,GAAG,CAAC,GAAK,EAAE,SAAS,GAC7D,EAAc,KAAK,GAAG,IAAI,EAAgB,GAAG,CAAC,GAAK,EAAE,SAAS,GAG9D,iBAAE,CAAe,mBAAE,CAAiB,CAAE,CAAG,IAAI,CAAC,8BAA8B,CAAC,GAG7E,EAAe,IAAI,CAAC,mBAAmB,CAAC,EAAiB,cAGzD,EAAiB,IAAI,CAAC,mBAAmB,CAAC,EAAiB,gBAG3D,EAAiB,IAAI,CAAC,uBAAuB,CAAC,GAEpD,MAAO,CACL,YAAa,EAAgB,MAAM,CACnC,UACA,8BACA,cACA,cACA,EACA,YAzBkB,EAAS,EAAI,EAAY,EAAS,EA0BpD,aAhCmB,EAAY,EAAI,EAAc,EAAY,EAAc,EAAI,IAAW,EAiC1F,yBACA,EACA,oCACA,eACA,iBACA,iBACA,CACF,CACF,CAKA,6BAA6B,CAAyB,CAAmC,CACvF,IAAM,EAAa,CAAC,yBAA0B,eAAgB,qBAAqB,CAC7E,EAAa,CAAC,kBAAmB,cAAe,cAAc,CAE9D,EAAe,EAAQ,MAAM,CAAC,GAAK,EAAW,QAAQ,CAAC,EAAE,YAAY,GACrE,EAAe,EAAQ,MAAM,CAAC,GAAK,EAAW,QAAQ,CAAC,EAAE,YAAY,GACrE,EAAe,EAAQ,MAAM,CAAC,GAAK,CAAC,EAAW,QAAQ,CAAC,EAAE,YAAY,GAAK,CAAC,EAAW,QAAQ,CAAC,EAAE,YAAY,GAEpH,MAAO,CACL,SAAU,IAAI,CAAC,eAAe,CAAC,GAC/B,SAAU,IAAI,CAAC,eAAe,CAAC,GAC/B,SAAU,IAAI,CAAC,eAAe,CAAC,EACjC,CACF,CAKA,+BAA+B,CAAyB,CAAmC,CACzF,IAAM,EAAS,EAAQ,MAAM,CAAC,GAAK,EAAE,UAAU,CAAC,UAAU,CAAC,MACrD,EAAS,EAAQ,MAAM,CAAC,GAAK,EAAE,UAAU,CAAC,UAAU,CAAC,MACrD,EAAS,EAAQ,MAAM,CAAC,GAAK,EAAE,UAAU,CAAC,UAAU,CAAC,MAE3D,MAAO,CACL,UAAW,IAAI,CAAC,eAAe,CAAC,GAChC,UAAW,IAAI,CAAC,eAAe,CAAC,GAChC,UAAW,IAAI,CAAC,eAAe,CAAC,EAClC,CACF,CAEQ,iBAAmC,CACzC,MAAO,CACL,YAAa,EACb,QAAS,EACT,aAAc,EACd,eAAgB,EAChB,YAAa,EACb,YAAa,EACb,YAAa,EACb,aAAc,EACd,WAAY,EACZ,YAAa,EACb,gBAAiB,EACjB,kBAAmB,EACnB,aAAc,CAAC,EACf,eAAgB,CAAC,EACjB,eAAgB,EAAE,AACpB,CACF,CAEQ,+BAA+B,CAAyB,CAA0D,CACxH,IAAI,EAAqB,EACrB,EAAuB,EACvB,EAAmB,EACnB,EAAoB,EAExB,IAAK,IAAM,KAAU,EACI,MADK,CACE,CAA1B,EAAO,OAAO,EAEhB,EAAoB,EACpB,EAAqB,KAAK,GAAG,CAAC,IAAoB,IACtB,QAAQ,CAA3B,EAAO,OAAO,GAEvB,EAAmB,EACnB,EAAuB,KAAK,GAAG,CAAC,IAAsB,IAI1D,MAAO,CACL,gBAAiB,EACjB,kBAAmB,CACrB,CACF,CAEQ,oBAAoB,CAAyB,CAAE,CAA2B,CAAmC,CACnH,IAAM,EAAU,EAAQ,MAAM,CAAC,CAAC,EAAK,KACnC,IAAM,EAAM,OAAO,CAAM,CAAC,EAAM,EAGhC,OAFK,AAAD,CAAI,CAAC,EAAI,GAAE,CAAG,CAAC,EAAI,CAAG,EAAA,AAAE,EAC5B,CAAG,CAAC,EAAI,CAAC,IAAI,CAAC,GACP,CACT,EAAG,CAAC,GAEE,EAA6C,CAAC,EACpD,IAAK,GAAM,CAAC,EAAK,EAAa,GAAI,OAAO,OAAO,CAAC,GAC/C,CAAS,CAAC,EAAI,CAAG,CADwC,GACpC,CAAC,eAAe,CAAC,GAGxC,OAAO,CACT,CAEQ,wBAAwB,CAAyB,CAAuC,CAQ9F,OAAO,OAAO,OAAO,CAPD,AAOE,EAPM,MAAM,CAAC,CAAC,EAAK,KACvC,IAAM,EAAQ,IAAI,KAAK,EAAO,SAAS,EAAE,WAAW,GAAG,KAAK,CAAC,EAAG,GAAG,AAGnE,OAFI,AAAC,CAAG,CAAC,CADoE,CAC9D,GAAE,CAAG,CAAC,EAAM,EAAG,EAC9B,CAAG,CAAC,EAAM,EAAI,EAAO,SAAS,CACvB,CACT,EAAG,CAAC,IAGD,GAAG,CAAC,CAAC,CAAC,EAAO,EAAY,GAAK,AAAC,QAAE,EAAO,OAAQ,EAAY,CAAC,EAC7D,IAAI,CAAC,CAAC,EAAG,IAAM,EAAE,KAAK,CAAC,aAAa,CAAC,EAAE,KAAK,EACjD,CAKA,YAAY,CAAyB,CAAU,CAwB7C,MAAO,CAvBS,CACd,SAAU,aAAc,cAAe,YAAa,aACpD,YAAa,UAAW,aAAc,eAAgB,cACtD,gBAAiB,mBAAoB,cAAe,kBACrD,IAEY,EAAQ,GAAG,CAAC,GAAU,CACjC,EAAO,MAAM,CACb,EAAO,SAAS,CAChB,EAAO,UAAU,CAAC,OAAO,CAAC,GAC1B,EAAO,QAAQ,EAAI,GACnB,EAAO,SAAS,EAAE,QAAQ,IAAM,GAChC,EAAO,QAAQ,CAAC,OAAO,CAAC,GACxB,EAAO,OAAO,CACd,EAAO,SAAS,CAAC,OAAO,CAAC,GACzB,EAAO,WAAW,CAAC,QAAQ,GAC3B,EAAO,UAAU,CACjB,EAAO,YAAY,CACnB,EAAO,eAAe,CAAC,QAAQ,GAC/B,EAAO,UAAU,CACjB,EAAO,cAAc,CAAC,QAAQ,GAC/B,EAEwB,CAAC,GAAG,CAAC,GAAO,EAAI,IAAI,CAAC,MAAM,IAAI,CAAC,KAC3D,CACF,CDrXA,IAAA,EAAA,EAAA,CAAA,CAAA,OAGO,eAAe,EAAK,CAAoB,EAC7C,GAAI,CAEF,GAAM,QAAE,CAAM,gBAAE,EAAiB,EAAE,WAAE,CAAS,SAAE,CAAO,CAAE,CAD5C,EAC+C,IADzC,EAAQ,IAAI,GAG/B,GAAI,CAAC,GAAU,CAAC,MAAM,OAAO,CAAC,GAC5B,MADqC,CAC9B,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,SAAS,EAAO,MAAO,0BAA2B,EACpD,CAAE,OAAQ,GAAI,GAIlB,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,EAAO,MAAM,CAAC,uBAAuB,CAAC,EAE9E,IAAM,EAAa,IAAI,EACjB,EAAa,IAAI,EAAA,UAAU,CAAC,QAAQ,GAAG,CAAC,eAAe,EAGvD,EAAoB,MAAO,IAC/B,IAAM,EAAM,GAAW,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CACvD,EAAQ,GAAa,IAAI,KAAK,KAAK,GAAG,GAAK,MAAM,GAAqB,EAAhB,KAAK,IAAsB,CAAjB,EAAoB,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,AAExG,OAAO,MAF8G,AAExG,EAAW,iBAAiB,CAAC,EAAQ,EAAO,EAAK,IAAK,MACrE,EAGM,EAAU,MAAM,EAAW,sBAAsB,CACrD,EACA,EACA,GAII,EAAU,EAAW,eAAe,CAAC,GACrC,EAA4B,EAAW,4BAA4B,CAAC,GACpE,EAA8B,EAAW,8BAA8B,CAAC,GAE9E,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,EAAQ,MAAM,CAAC,iBAAiB,CAAC,EAErE,IAAM,EAAW,CACf,SAAS,EACT,KAAM,SACJ,UACA,4BACA,8BACA,EACA,SAAU,CACR,YAAa,EAAO,MAAM,CAC1B,oBAAqB,EAAQ,MAAM,CACnC,gBAAiB,EAAO,MAAM,CAAG,EAAQ,MAAM,gBAC/C,EACA,UAAW,WAAE,UAAW,CAAQ,EAChC,YAAa,IAAI,OAAO,WAAW,EACrC,CACF,CACF,EAEA,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,EAC3B,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,sCAAuC,GAC9C,EAAA,YAAY,CAAC,IAAI,CACtB,CACE,SAAS,EACT,MAAO,yBACP,QAAS,aAAiB,MAAQ,EAAM,OAAO,CAAG,eACpD,EACA,CAAE,OAAQ,GAAI,EAElB,CACF,CAEO,eAAe,EAAI,CAAoB,EAC5C,GAAI,CACF,GAAM,cAAE,CAAY,CAAE,CAAG,IAAI,IAAI,EAAQ,GAAG,EAEtC,EAAU,EAAa,GAAG,CAAC,YAAY,MAAM,KAAK,OAAO,SACzD,EAAiB,SAAS,EAAa,GAAG,CAAC,mBAAqB,MAChE,EAAY,EAAa,GAAG,CAAC,aAC7B,EAAU,EAAa,GAAG,CAAC,WAC3B,EAAgB,EAAa,GAAG,CAAC,kBAAkB,MAAM,KAAK,OAAO,SACrE,EAAgB,EAAa,GAAG,CAAC,kBAAoB,IAE3D,GAAI,CAAC,GAA8B,GAAG,CAAtB,EAAQ,MAAM,CAC5B,OAAO,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,SAAS,EAAO,MAAO,+BAAgC,EACzD,CAAE,OAAQ,GAAI,GAIlB,QAAQ,GAAG,CAAC,CAAC,gDAAgD,EAAE,EAAQ,MAAM,CAAC,WAAW,CAAC,EAQ1F,IAAM,EAAc,EAAQ,GAAG,CAAC,IAAW,KAAD,GACxC,EACA,YAAa,KAAK,KAAK,CAAC,AAAgB,QAAX,MAAM,IAAW,EAC9C,QAAS,GAAsB,GAAhB,KAAK,MAAM,GAC1B,aAAc,IAAsB,EAAhB,KAAK,MAAM,GAC/B,YAA6B,GAAhB,KAAK,MAAM,GAAU,GAClC,YAAa,CAAC,CAAiB,GAAhB,KAAK,MAAM,IAAU,CAAC,CACvC,CAAC,EAEK,EAAW,CACf,SAAS,EACT,KAAM,CACJ,mBAAoB,EACpB,QAAS,CACP,aAAc,EAAQ,MAAM,CAC5B,WAAY,EAAY,MAAM,CAAC,CAAC,EAAK,IAAM,EAAM,EAAE,OAAO,CAAE,GAAK,EAAY,MAAM,CACnF,aAAc,EAAY,MAAM,CAAC,CAAC,EAAK,IAAM,EAAM,EAAE,YAAY,CAAE,GAAK,EAAY,MAAM,CAC1F,YAAa,EAAY,MAAM,CAAC,CAAC,EAAK,IAAM,EAAM,EAAE,WAAW,CAAE,EACnE,EACA,WAAY,SACV,EACA,iBACA,UAAW,CAAE,oBAAW,CAAQ,gBAChC,gBACA,CACF,EACA,YAAa,IAAI,OAAO,WAAW,EACrC,CACF,EAEA,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,EAC3B,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,oCAAqC,GAC5C,EAAA,YAAY,CAAC,IAAI,CACtB,CACE,SAAS,EACT,MAAO,oCACP,QAAS,aAAiB,MAAQ,EAAM,OAAO,CAAG,eACpD,EACA,CAAE,OAAQ,GAAI,EAElB,CACF,CDhIA,IAAA,EAAA,EAAA,CAAA,CAAA,OAIA,IAAM,EAAc,IAAI,EAAA,mBAAmB,CAAC,CACxC,WAAY,CACR,KAAM,EAAA,SAAS,CAAC,SAAS,CACzB,KAAM,mCACN,SAAU,6BACV,SAAU,QACV,WAAY,EAChB,EACA,QAAS,CAAA,OACT,IADiD,eACc,CAA3C,EACpB,iBAAkB,uEAClB,iBAZqB,GAarB,SAAA,CACJ,GAIM,kBAAE,CAAgB,sBAAE,CAAoB,CAAE,aAAW,CAAE,CAAG,EAChE,SAAS,IACL,MAAO,CAAA,EAAA,EAAA,UAAW,AAAX,EAAY,kBACf,EACA,sBACJ,EACJ,CAEO,eAAe,EAAQ,CAAG,CAAE,CAAG,CAAE,CAAG,EACvC,IAAI,EACJ,IAAI,EAAU,mCAKV,EAAU,EAAQ,OAAO,CAAC,WAAY,KAAO,IAMjD,IAAM,EAAgB,MAAM,EAAY,OAAO,CAAC,EAAK,EAAK,SACtD,EACA,mBAHE,CAAA,CAIN,GACA,GAAI,CAAC,EAID,OAHA,EAAI,IADY,MACF,CAAG,IACjB,EAAI,GAAG,CAAC,eACR,AAAiB,OAAO,CAApB,IAAyB,KAAhB,EAAoB,EAAI,SAAS,CAAC,IAAI,CAAC,EAAK,QAAQ,OAAO,IACjE,KAEX,GAAM,SAAE,CAAO,QAAE,CAAM,YAAE,CAAU,CAAE,aAAW,mBAAE,CAAiB,qBAAE,CAAmB,sBAAE,CAAoB,yBAAE,CAAuB,kBAAE,CAAgB,CAAE,CAAG,EACxJ,EAAoB,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,GACvC,GAAQ,EAAQ,EAAkB,aAAa,CAAC,EAAkB,EAAI,EAAkB,MAAM,CAAC,EAAA,AAAiB,EACpH,GAAI,GAAS,CAAC,EAAa,CACvB,IAAM,GAAgB,CAAQ,EAAkB,MAAM,CAAC,EAAiB,CAClE,EAAgB,EAAkB,aAAa,CAAC,EAAkB,CACxE,GAAI,IAC+B,IAA3B,EAAc,KADH,GACW,EAAc,CAAC,EACrC,MAAM,IAAI,EAAA,CAD0C,cAC3B,AAGrC,CACA,IAAI,EAAW,MACX,GAAU,EAAY,IAAb,CAAkB,EAAK,EAAD,EAG/B,EAAW,AAAa,OAHqB,KAC7C,EAAW,CAAA,EAEwB,IAAM,CAAA,EAE7C,IAAM,EACN,AAAsB,OAAV,CAAkB,IAAb,EAEjB,CAAC,EAKK,EAAe,GAAS,CAAC,EACzB,EAAS,EAAI,MAAM,EAAI,MACvB,EAAS,CAAA,EAAA,EAAA,SAAS,AAAT,IACT,EAAa,EAAO,UAVyE,QAUvD,GACtC,EAAU,CACZ,2BACA,EACA,WAAY,CACR,aAAc,CACV,iBAAiB,CAAQ,EAAW,YAAY,CAAC,eAAe,CAChE,gBAAgB,CAAQ,EAAW,YAAY,CAAC,cAAc,AAClE,0BACA,EACA,iBAAkB,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,oBACtC,kBAAmB,AAAwD,OAAvD,EAA2B,EAAW,YAAA,AAAY,EAAY,KAAK,EAAI,EAAyB,SAAS,cAC7H,EACA,UAAW,EAAI,SAAS,CACxB,QAAS,AAAC,IACN,EAAI,EAAE,CAAC,QAAS,EACpB,EACA,sBAAkB,EAClB,8BAA+B,CAAC,EAAO,EAAU,IAAe,EAAY,cAAc,CAAC,EAAK,EAAO,EAAc,EACzH,EACA,cAAe,SACX,CACJ,CACJ,EACM,EAAc,IAAI,EAAA,eAAe,CAAC,GAClC,EAAc,IAAI,EAAA,gBAAgB,CAAC,GACnC,EAAU,EAAA,kBAAkB,CAAC,mBAAmB,CAAC,EAAa,CAAA,EAAA,EAAA,sBAAA,AAAsB,EAAC,IAC3F,GAAI,CACA,IAAM,EAAoB,MAAO,GACtB,EAAY,MAAM,CAAC,EAAS,GAAS,OAAO,CAAC,KAChD,GAAI,CAAC,EAAM,OACX,EAAK,aAAa,CAAC,CACf,mBAAoB,EAAI,UAAU,CAClC,WAAY,EAChB,GACA,IAAM,EAAqB,EAAO,qBAAqB,GAEvD,GAAI,CAAC,EACD,OAEJ,GAAI,EAAmB,GAAG,CAAC,EAHF,kBAGwB,EAAA,cAAc,CAAC,aAAa,CAAE,YAC3E,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,EAAmB,GAAG,CAAC,kBAAkB,qEAAqE,CAAC,EAG9J,IAAM,EAAQ,EAAmB,GAAG,CAAC,cACrC,GAAI,EAAO,CACP,IAAM,EAAO,CAAA,EAAG,EAAO,CAAC,EAAE,EAAA,CAAO,CACjC,EAAK,aAAa,CAAC,CACf,aAAc,EACd,aAAc,EACd,iBAAkB,CACtB,GACA,EAAK,UAAU,CAAC,EACpB,MACI,CADG,CACE,UAAU,CAAC,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAE9C,GAEE,EAAiB,MAAO,QACtB,EA0FI,EAzFR,IAAM,EAAoB,MAAO,oBAAE,CAAkB,CAAE,IACnD,GAAI,CACA,GAAI,CAAC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GAAwB,GAA2B,CAAC,EAK3F,OAJA,EAAI,SAD2G,CACjG,CAAG,IAEjB,EAAI,SAAS,CAAC,iBAAkB,eAChC,EAAI,GAAG,CAAC,gCACD,KAEX,IAAM,EAAW,MAAM,EAAkB,GACzC,EAAI,YAAY,CAAG,EAAQ,UAAU,CAAC,YAAY,CAClD,IAAI,EAAmB,EAAQ,UAAU,CAAC,gBAAgB,CAGtD,GACI,EAAI,SAAS,EAAE,CACf,CAFc,CAEV,SAAS,CAAC,GACd,OAAmB,GAG3B,IAAM,EAAY,EAAQ,UAAU,CAAC,aAAa,CAGlD,IAAI,EA6BA,OADA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,EAAU,EAAQ,UAAU,CAAC,gBAAgB,EACnF,IA7BA,EACP,IAAM,EAAO,MAAM,EAAS,IAAI,GAE1B,EAAU,CAAA,EAAA,EAAA,yBAAA,AAAyB,EAAC,EAAS,OAAO,CACtD,KACA,CAAO,CAAC,EAAA,EADG,oBACmB,CAAC,CAAG,CAAA,EAElC,CAAC,CAAO,CAAC,eAAe,EAAI,EAAK,IAAI,EAAE,AACvC,EAAO,CAAC,eAAe,CAAG,EAAK,IAAA,AAAI,EAEvC,IAAM,EAAa,KAAkD,IAA3C,EAAQ,UAAU,CAAC,mBAAmB,GAAoB,GAAQ,UAAU,CAAC,mBAAmB,EAAI,EAAA,cAAA,AAAc,GAAG,AAAQ,EAAQ,UAAU,CAAC,mBAAmB,CACvL,EAAS,KAA8C,IAAvC,EAAQ,UAAU,CAAC,eAAe,EAAoB,EAAQ,UAAU,CAAC,eAAe,EAAI,EAAA,cAAc,MAAG,EAAY,EAAQ,UAAU,CAAC,eAAe,CAcjL,MAZmB,CACf,AAWG,MAXI,CACH,KAAM,EAAA,eAAe,CAAC,SAAS,CAC/B,OAAQ,EAAS,MAAM,CACvB,KAAM,OAAO,IAAI,CAAC,MAAM,EAAK,WAAW,YACxC,CACJ,EACA,aAAc,YACV,SACA,CACJ,CACJ,CAEJ,CAKJ,CAAE,KALS,CAKF,EAAK,CAcV,MAX0B,MAAtB,EAA6B,KAAK,EAAI,EAAmB,OAAA,AAAO,EAAE,CAClE,MAAM,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,cAClC,uBACA,CACJ,EACJ,EAAG,GAED,CACV,CACJ,EACM,EAAa,MAAM,EAAY,cAAc,CAAC,KAChD,aACA,WACA,EACA,UAAW,EAAA,SAAS,CAAC,SAAS,CAC9B,YAAY,oBACZ,EACA,mBAAmB,uBACnB,0BACA,oBACA,EACA,UAAW,EAAI,SAAS,AAC5B,GAEA,GAAI,CAAC,EACD,KADQ,EACD,KAEX,GAAI,CAAe,MAAd,CAAqB,EAAmD,AAA1C,GAAJ,IAAK,EAAoB,EAAW,KAAA,AAAK,EAAY,KAAK,EAAI,EAAkB,IAAI,IAAM,EAAA,eAAe,CAAC,SAAS,CAE9I,CAFgJ,KAE1I,OAAO,cAAc,CAAC,AAAI,MAAM,CAAC,kDAAkD,EAAgB,MAAd,CAAqB,EAAS,AAA2C,GAA/C,IAAK,EAAqB,EAAW,KAAK,AAAL,EAAiB,KAAK,EAAI,EAAmB,IAAI,CAAA,CAAE,EAAG,oBAAqB,CACjO,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,EAEA,CAAC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAgB,AACrC,EAAI,SAAS,CAAC,iBAAkB,EAAuB,cAAgB,EAAW,MAAM,CAAG,OAAS,EAAW,OAAO,CAAG,QAAU,OAGnI,GACA,EAAI,QADS,CACA,CAAC,gBAAiB,2DAEnC,IAAM,EAAU,CAAA,EAAA,EAAA,2BAAA,AAA2B,EAAC,EAAW,KAAK,CAAC,OAAO,EAapE,MAZM,AAAF,AAAE,CAAA,AAAD,EAAC,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GACxC,EAD6C,AACrC,GADwC,GAClC,CAAC,EAAA,sBAAsB,GAIrC,EAAW,YAAY,EAAK,EAAD,AAAK,SAAS,CAAC,kBAAqB,EAAD,AAAS,GAAG,CAAC,kBAAkB,AAC7F,EAAQ,GAAG,CAAC,gBAAiB,CAAA,EAAA,EAAA,qBAAA,AAAqB,EAAC,EAAW,YAAY,GAE9E,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,EAAW,KAAK,CAAC,IAAI,CAAE,SAC7E,EACA,OAAQ,EAAW,KAAK,CAAC,MAAM,EAAI,GACvC,IACO,IACX,EAGI,EACA,MAAM,EAAe,EADT,CAGZ,MAAM,EAAO,qBAAqB,CAAC,EAAI,OAAO,CAAE,IAAI,EAAO,KAAK,CAAC,EAAA,cAAc,CAAC,aAAa,CAAE,CACvF,SAAU,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAChC,KAAM,EAAA,QAAQ,CAAC,MAAM,CACrB,WAAY,CACR,cAAe,EACf,cAAe,EAAI,GAAG,AAC1B,CACJ,EAAG,GAEf,CAAE,MAAO,EAAK,CAcV,GAbI,AAAE,CAAD,YAAgB,EAAA,eAAe,EAChC,CADmC,KAC7B,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,cAClC,uBACA,CACJ,EACJ,GAIA,EAAO,MAAM,EAKjB,OAHA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,KAAM,CAC5D,OAAQ,GACZ,IACO,IACX,CACJ,EAEA,qCAAqC", "ignoreList": [0]}