module.exports=[18622,(e,r,a)=>{r.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,r,a)=>{r.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},70406,(e,r,a)=>{r.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,r,a)=>{r.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},24361,(e,r,a)=>{r.exports=e.x("util",()=>require("util"))},32319,(e,r,a)=>{r.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},24725,(e,r,a)=>{r.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},20635,(e,r,a)=>{r.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},14747,(e,r,a)=>{r.exports=e.x("path",()=>require("path"))},78006,e=>{"use strict";e.s(["PolygonAPI",()=>o]);var r=e.i(55362);let a="https://api.polygon.io",t=process.env.POLYGON_API_KEY;class o{apiKey;constructor(e){if(this.apiKey=e||t||"",!this.apiKey)throw Error("Polygon API key is required")}async getStockQuote(e){try{let t=(await r.default.get(`${a}/v2/snapshot/locale/us/markets/stocks/tickers/${e}`,{params:{apikey:this.apiKey}})).data.results[0];if(!t)throw Error(`No data found for ${e}`);let o=t.value||t,s=o.day||{},n=o.prevDay||{};o.lastQuote;let l=(o.lastTrade||{}).p||s.c||n.c,i=n.c||s.o,c=l-i;return{symbol:e.toUpperCase(),name:o.name||e.toUpperCase(),price:l,change:c,changePercent:c/i*100,volume:s.v||0,marketCap:o.market_cap,pe:void 0,dividend:void 0}}catch(t){console.error("Error fetching stock quote from Polygon:",t);try{let t=(await r.default.get(`${a}/v2/aggs/ticker/${e}/prev`,{params:{adjusted:"true",apikey:this.apiKey}})).data.results[0];return{symbol:e.toUpperCase(),name:e.toUpperCase(),price:t.c,change:t.c-t.o,changePercent:(t.c-t.o)/t.o*100,volume:t.v,marketCap:void 0,pe:void 0,dividend:void 0}}catch(r){throw console.error("Polygon fallback also failed:",r),Error(`Failed to fetch quote for ${e}`)}}}async getHistoricalData(e,t="day",o=1,s,n){try{let l=await r.default.get(`${a}/v2/aggs/ticker/${e}/range/${o}/${t}/${s}/${n}`,{params:{adjusted:"true",sort:"asc",limit:5e4,apikey:this.apiKey}});if(!l.data.results||0===l.data.results.length)return console.warn(`No historical data found for ${e}`),[];return l.data.results.map(e=>({timestamp:e.t,open:e.o,high:e.h,low:e.l,close:e.c,volume:e.v}))}catch(r){throw console.error(`Error fetching historical data for ${e}:`,r),r.response&&(console.error(`Polygon API Error: ${r.response.status} - ${r.response.statusText}`),console.error("Response data:",r.response.data)),Error(`Failed to fetch historical data for ${e}: ${r.message}`)}}async getCompanyDetails(e){try{return(await r.default.get(`${a}/v3/reference/tickers/${e}`,{params:{apikey:this.apiKey}})).data.results}catch(e){return console.error("Error fetching company details:",e),null}}async getMarketStatus(){try{return(await r.default.get(`${a}/v1/marketstatus/now`,{params:{apikey:this.apiKey}})).data}catch(e){return console.error("Error fetching market status:",e),null}}async searchStocks(e,t=10){try{return(await r.default.get(`${a}/v3/reference/tickers`,{params:{search:e,market:"stocks",active:"true",limit:t,apikey:this.apiKey}})).data.results||[]}catch(e){return console.error("Error searching stocks:",e),[]}}}new o},29547,e=>{"use strict";e.s(["FMPAPI",()=>o]);var r=e.i(55362);let a="https://financialmodelingprep.com/api",t=process.env.FMP_API_KEY;class o{apiKey;constructor(e){if(this.apiKey=e||t||"",!this.apiKey)throw Error("FMP API key is required")}async getStockQuote(e){try{let t=(await r.default.get(`${a}/v3/quote/${e}`,{params:{apikey:this.apiKey}})).data[0];if(!t)throw Error(`No data found for symbol ${e}`);return{symbol:t.symbol,name:t.name||t.symbol,price:t.price,change:t.change,changePercent:t.changesPercentage,volume:t.volume,marketCap:t.marketCap,pe:t.pe,dividend:void 0}}catch(r){throw console.error("Error fetching FMP stock quote:",r),Error(`Failed to fetch quote for ${e}`)}}async getCompanyProfile(e){try{return(await r.default.get(`${a}/v3/profile/${e}`,{params:{apikey:this.apiKey}})).data[0]}catch(e){return console.error("Error fetching company profile:",e),null}}async getFinancialRatios(e){try{return(await r.default.get(`${a}/v3/ratios/${e}`,{params:{apikey:this.apiKey}})).data[0]}catch(e){return console.error("Error fetching financial ratios:",e),null}}async getKeyMetrics(e){try{return(await r.default.get(`${a}/v3/key-metrics/${e}`,{params:{apikey:this.apiKey}})).data[0]}catch(e){return console.error("Error fetching key metrics:",e),null}}async getAnalystRecommendations(e){try{return(await r.default.get(`${a}/v3/analyst-stock-recommendations/${e}`,{params:{apikey:this.apiKey}})).data}catch(e){return console.error("Error fetching analyst recommendations:",e),[]}}async getEarningsCalendar(e,t){try{let o={apikey:this.apiKey};return e&&(o.from=e),t&&(o.to=t),(await r.default.get(`${a}/v3/earning_calendar`,{params:o})).data}catch(e){return console.error("Error fetching earnings calendar:",e),[]}}async getEconomicCalendar(e,t){try{let o={apikey:this.apiKey};return e&&(o.from=e),t&&(o.to=t),(await r.default.get(`${a}/v3/economic_calendar`,{params:o})).data}catch(e){return console.error("Error fetching economic calendar:",e),[]}}async searchStocks(e,t=10){try{return(await r.default.get(`${a}/v3/search`,{params:{query:e,limit:t,apikey:this.apiKey}})).data}catch(e){return console.error("Error searching stocks:",e),[]}}async getSectorPerformance(){try{return(await r.default.get(`${a}/v3/sector-performance`,{params:{apikey:this.apiKey}})).data}catch(e){return console.error("Error fetching sector performance:",e),[]}}async getMarketMovers(e){try{return(await r.default.get(`${a}/v3/stock_market/${e}`,{params:{apikey:this.apiKey}})).data}catch(r){return console.error(`Error fetching market ${e}:`,r),[]}}async getEarningsCalendar(e,t=30){try{let o=new Date;o.setDate(o.getDate()-t);let s=new Date;return(await r.default.get(`${a}/v3/earning_calendar`,{params:{apikey:this.apiKey,from:o.toISOString().split("T")[0],to:s.toISOString().split("T")[0],...e&&{symbol:e.toUpperCase()}}})).data||[]}catch(e){return console.error("Error fetching earnings calendar:",e),[]}}async getStockNews(e,t=50){try{return(await r.default.get(`${a}/v3/stock_news`,{params:{apikey:this.apiKey,tickers:e.toUpperCase(),limit:t}})).data||[]}catch(e){return console.error("Error fetching stock news:",e),[]}}async getAnalystRecommendations(e,t=30){try{return(await r.default.get(`${a}/v3/analyst-stock-recommendations/${e.toUpperCase()}`,{params:{apikey:this.apiKey,limit:t}})).data||[]}catch(e){return console.error("Error fetching analyst recommendations:",e),[]}}async getInsiderTrading(e,t=30){try{let o=await r.default.get(`${a}/v4/insider-trading`,{params:{apikey:this.apiKey,symbol:e.toUpperCase(),limit:5*t}}),s=new Date;return s.setDate(s.getDate()-t),(o.data||[]).filter(e=>new Date(e.filingDate)>=s)}catch(e){return console.error("Error fetching insider trading:",e),[]}}async getSECFilings(e,t=30){try{let o=await r.default.get(`${a}/v3/sec_filings/${e.toUpperCase()}`,{params:{apikey:this.apiKey,limit:2*t}}),s=new Date;return s.setDate(s.getDate()-t),(o.data||[]).filter(e=>new Date(e.filedDate)>=s)}catch(e){return console.error("Error fetching SEC filings:",e),[]}}async getPreMarketQuote(e){try{let t=(await r.default.get(`${a}/v3/quote/${e.toUpperCase()}`,{params:{apikey:this.apiKey}})).data[0];if(!t)return null;return{symbol:t.symbol,price:t.price,previousClose:t.previousClose,change:t.change,changePercent:t.changesPercentage,volume:t.volume,marketCap:t.marketCap,avgVolume:t.avgVolume,preMarketPrice:t.preMarketPrice||t.price,preMarketChange:t.preMarketChange||t.change,preMarketChangePercent:t.preMarketChangePercent||t.changesPercentage}}catch(e){return console.error("Error fetching pre-market quote:",e),null}}async getMultiplePreMarketQuotes(e){try{let t=e.map(e=>e.toUpperCase()).join(",");return((await r.default.get(`${a}/v3/quote/${t}`,{params:{apikey:this.apiKey}})).data||[]).map(e=>({symbol:e.symbol,price:e.price,previousClose:e.previousClose,change:e.change,changePercent:e.changesPercentage,volume:e.volume,marketCap:e.marketCap,avgVolume:e.avgVolume,preMarketPrice:e.preMarketPrice||e.price,preMarketChange:e.preMarketChange||e.change,preMarketChangePercent:e.preMarketChangePercent||e.changesPercentage}))}catch(e){return console.error("Error fetching multiple pre-market quotes:",e),[]}}async getCompanyProfile(e){try{return(await r.default.get(`${a}/v3/profile/${e.toUpperCase()}`,{params:{apikey:this.apiKey}})).data[0]||null}catch(e){return console.error("Error fetching company profile:",e),null}}}new o},58445,e=>{"use strict";e.s(["TechnicalIndicators",()=>r]);class r{static sma(e,r){let a=[];for(let t=r-1;t<e.length;t++){let o=e.slice(t-r+1,t+1).reduce((e,r)=>e+r,0);a.push(o/r)}return a}static ema(e,r){let a=[],t=2/(r+1),o=e.slice(0,r).reduce((e,r)=>e+r,0)/r;a.push(o);for(let s=r;s<e.length;s++)a.push(o=e[s]*t+o*(1-t));return a}static rsi(e,r=14){let a=[],t=[];for(let r=1;r<e.length;r++){let o=e[r]-e[r-1];a.push(o>0?o:0),t.push(o<0?Math.abs(o):0)}let o=this.sma(a,r),s=this.sma(t,r);return o.map((e,r)=>100-100/(1+e/s[r]))}static macd(e,r=12,a=26,t=9){let o=this.ema(e,r),s=this.ema(e,a),n=o.slice(a-r).map((e,r)=>e-s[r]),l=this.ema(n,t),i=n.slice(t-1).map((e,r)=>e-l[r]);return{macd:n,signal:l,histogram:i}}static bollingerBands(e,r=20,a=2){return this.sma(e,r).map((t,o)=>{let s=Math.sqrt(e.slice(o,o+r).reduce((e,r)=>e+Math.pow(r-t,2),0)/r);return{upper:t+s*a,middle:t,lower:t-s*a}})}static findSupportResistance(e,r=20){let a=e.map(e=>e.high),t=e.map(e=>e.low),o=[],s=[];for(let n=r;n<e.length-r;n++){let e=a[n],l=t[n],i=a.slice(n-r,n).every(r=>r<=e)&&a.slice(n+1,n+r+1).every(r=>r<=e),c=t.slice(n-r,n).every(e=>e>=l)&&t.slice(n+1,n+r+1).every(e=>e>=l);i&&o.push(e),c&&s.push(l)}return{support:s,resistance:o}}static volumeAnalysis(e,r=20){let a=e.map(e=>e.volume),t=this.sma(a,r),o=a[a.length-1],s=t[t.length-1];return{currentVolume:o,averageVolume:s,volumeRatio:o/s,isHighVolume:o>1.5*s,isLowVolume:o<.5*s}}static analyzeSwingSetup(e){let r=e.map(e=>e.close),a=[],t=this.rsi(r),o=t[t.length-1],s="NEUTRAL",n=`RSI: ${o.toFixed(2)}`;o<30?(s="BUY",n+=" - Oversold condition, potential bounce"):o>70?(s="SELL",n+=" - Overbought condition, potential pullback"):n+=" - Neutral zone",a.push({name:"RSI",value:o,signal:s,description:n});let l=this.sma(r,20),i=this.sma(r,50),c=r[r.length-1],m=l[l.length-1],p=i[i.length-1],y="NEUTRAL",g=`Price vs SMA20: ${((c/m-1)*100).toFixed(2)}%`;c>m&&m>p?(y="BUY",g+=" - Bullish trend"):c<m&&m<p?(y="SELL",g+=" - Bearish trend"):g+=" - Mixed signals",a.push({name:"Moving Averages",value:(c/m-1)*100,signal:y,description:g});let u=this.macd(r),h=u.macd[u.macd.length-1],d=u.signal[u.signal.length-1],C=u.histogram[u.histogram.length-1],k="NEUTRAL",v=`MACD: ${h.toFixed(4)}, Signal: ${d.toFixed(4)}`;h>d&&C>0?(k="BUY",v+=" - Bullish momentum"):h<d&&C<0?(k="SELL",v+=" - Bearish momentum"):v+=" - Momentum shifting",a.push({name:"MACD",value:C,signal:k,description:v});let f=this.volumeAnalysis(e),b="NEUTRAL",S=`Volume: ${(100*f.volumeRatio).toFixed(0)}% of average`;return f.isHighVolume?(b="BUY",S+=" - High volume confirms move"):f.isLowVolume?(b="SELL",S+=" - Low volume, weak conviction"):S+=" - Normal volume",a.push({name:"Volume",value:f.volumeRatio,signal:b,description:S}),a}}},5744,e=>{"use strict";e.s(["ALL_SYMBOLS",()=>t,"PRIORITY_SYMBOLS",()=>o,"STOCKS_BY_SECTOR",()=>a]);let r=[{symbol:"MSFT",name:"Microsoft Corp",sector:"Technology",marketCap:"large"},{symbol:"NVDA",name:"NVIDIA Corp",sector:"Technology",marketCap:"large"},{symbol:"AMZN",name:"Amazon.com Inc",sector:"Consumer Discretionary",marketCap:"large"},{symbol:"GOOG",name:"Alphabet Inc Class C",sector:"Technology",marketCap:"large"},{symbol:"GOOGL",name:"Alphabet Inc Class A",sector:"Technology",marketCap:"large"},{symbol:"META",name:"Meta Platforms Inc",sector:"Technology",marketCap:"large"},{symbol:"AVGO",name:"Broadcom Inc",sector:"Technology",marketCap:"large"},{symbol:"TSM",name:"Taiwan Semiconductor",sector:"Technology",marketCap:"large"},{symbol:"ORCL",name:"Oracle Corp",sector:"Technology",marketCap:"large"},{symbol:"NFLX",name:"Netflix Inc",sector:"Communication Services",marketCap:"large"},{symbol:"CSCO",name:"Cisco Systems Inc",sector:"Technology",marketCap:"large"},{symbol:"AMD",name:"Advanced Micro Devices Inc",sector:"Technology",marketCap:"large"},{symbol:"JPM",name:"JPMorgan Chase & Co",sector:"Financial Services",marketCap:"large"},{symbol:"BAC",name:"Bank of America Corp",sector:"Financial Services",marketCap:"large"},{symbol:"MS",name:"Morgan Stanley",sector:"Financial Services",marketCap:"large"},{symbol:"SCHW",name:"Charles Schwab Corp",sector:"Financial Services",marketCap:"large"},{symbol:"C",name:"Citigroup Inc",sector:"Financial Services",marketCap:"large"},{symbol:"JNJ",name:"Johnson & Johnson",sector:"Healthcare",marketCap:"large"},{symbol:"ABBV",name:"AbbVie Inc",sector:"Healthcare",marketCap:"large"},{symbol:"MRK",name:"Merck & Co Inc",sector:"Healthcare",marketCap:"large"},{symbol:"GILD",name:"Gilead Sciences Inc",sector:"Healthcare",marketCap:"large"},{symbol:"GE",name:"General Electric Co",sector:"Industrial",marketCap:"large"},{symbol:"CAT",name:"Caterpillar Inc",sector:"Industrial",marketCap:"large"},{symbol:"BA",name:"Boeing Co",sector:"Industrial",marketCap:"large"},{symbol:"GEV",name:"GE Vernova Inc",sector:"Industrial",marketCap:"large"},{symbol:"ASML",name:"ASML Holding NV",sector:"Technology",marketCap:"large"},{symbol:"MU",name:"Micron Technology Inc",sector:"Technology",marketCap:"large"},{symbol:"LRCX",name:"Lam Research Corp",sector:"Technology",marketCap:"large"},{symbol:"DIS",name:"Walt Disney Co",sector:"Communication Services",marketCap:"large"},{symbol:"SBUX",name:"Starbucks Corp",sector:"Consumer Discretionary",marketCap:"large"},{symbol:"MO",name:"Altria Group Inc",sector:"Consumer Staples",marketCap:"large"},{symbol:"CEG",name:"Constellation Energy Corp",sector:"Utilities",marketCap:"large"},{symbol:"VST",name:"Vistra Corp",sector:"Utilities",marketCap:"mid"},{symbol:"GM",name:"General Motors Co",sector:"Consumer Discretionary",marketCap:"large"},{symbol:"PLTR",name:"Palantir Technologies Inc",sector:"Technology",marketCap:"mid"},{symbol:"APP",name:"Applovin Corp",sector:"Technology",marketCap:"mid"},{symbol:"DASH",name:"DoorDash Inc",sector:"Consumer Discretionary",marketCap:"mid"},{symbol:"NET",name:"Cloudflare Inc",sector:"Technology",marketCap:"mid"},{symbol:"DDOG",name:"Datadog Inc",sector:"Technology",marketCap:"mid"},{symbol:"ZS",name:"Zscaler Inc",sector:"Technology",marketCap:"mid"},{symbol:"SHOP",name:"Shopify Inc",sector:"Technology",marketCap:"mid"},{symbol:"RBLX",name:"Roblox Corp",sector:"Communication Services",marketCap:"mid"},{symbol:"AEM",name:"Agnico Eagle Mines Ltd",sector:"Materials",marketCap:"mid"},{symbol:"NEM",name:"Newmont Corp",sector:"Materials",marketCap:"large"},{symbol:"CCJ",name:"Cameco Corp",sector:"Energy",marketCap:"mid"},{symbol:"PAAS",name:"Pan American Silver Corp",sector:"Materials",marketCap:"small"},{symbol:"BTG",name:"B2Gold Corp",sector:"Materials",marketCap:"small"},{symbol:"HL",name:"Hecla Mining Co",sector:"Materials",marketCap:"small"},{symbol:"MP",name:"MP Materials Corp",sector:"Materials",marketCap:"small"},{symbol:"AG",name:"First Majestic Silver Corp",sector:"Materials",marketCap:"small"},{symbol:"UAL",name:"United Airlines Holdings Inc",sector:"Industrial",marketCap:"mid"},{symbol:"NCLH",name:"Norwegian Cruise Line",sector:"Consumer Discretionary",marketCap:"mid"},{symbol:"HOOD",name:"Robinhood Markets Inc",sector:"Financial Services",marketCap:"mid"},{symbol:"SOFI",name:"SoFi Technologies Inc",sector:"Financial Services",marketCap:"small"},{symbol:"CELH",name:"Celsius Holdings Inc",sector:"Consumer Staples",marketCap:"small"},{symbol:"LEVI",name:"Levi Strauss & Co",sector:"Consumer Discretionary",marketCap:"small"},{symbol:"ELF",name:"e.l.f. Beauty Inc",sector:"Consumer Discretionary",marketCap:"small"},{symbol:"ETSY",name:"Etsy Inc",sector:"Consumer Discretionary",marketCap:"mid"},{symbol:"W",name:"Wayfair Inc",sector:"Consumer Discretionary",marketCap:"mid"},{symbol:"RIOT",name:"Riot Platforms Inc",sector:"Technology",marketCap:"small"},{symbol:"HUT",name:"Hut 8 Corp",sector:"Technology",marketCap:"small"},{symbol:"IREN",name:"IREN Ltd",sector:"Technology",marketCap:"small"},{symbol:"BILI",name:"Bilibili Inc",sector:"Communication Services",marketCap:"small"},{symbol:"TIGR",name:"UP Fintech Holding Ltd",sector:"Financial Services",marketCap:"small"},{symbol:"FUTU",name:"Futu Holdings Ltd",sector:"Financial Services",marketCap:"small"},{symbol:"NBIS",name:"Nebius Group NV",sector:"Technology",marketCap:"small"},{symbol:"SOUN",name:"SoundHound AI Inc",sector:"Technology",marketCap:"small"},{symbol:"IONQ",name:"IonQ Inc",sector:"Technology",marketCap:"small"},{symbol:"RGTI",name:"Rigetti Computing Inc",sector:"Technology",marketCap:"small"},{symbol:"RKLB",name:"Rocket Lab Corp",sector:"Industrial",marketCap:"small"},{symbol:"ASTS",name:"AST SpaceMobile Inc",sector:"Technology",marketCap:"small"},{symbol:"VRT",name:"Vertiv Holdings Co",sector:"Industrial",marketCap:"mid"}],a=r.reduce((e,r)=>(e[r.sector]||(e[r.sector]=[]),e[r.sector].push(r),e),{}),t=r.map(e=>e.symbol),o=["MSFT","NVDA","AMZN","GOOG","GOOGL","META","AVGO","TSM","JPM","NFLX","ORCL","JNJ","BAC","ABBV","ASML","PLTR"]}];

//# sourceMappingURL=%5Broot-of-the-server%5D__87e9731c._.js.map