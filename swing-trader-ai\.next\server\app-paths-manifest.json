{"/_not-found/page": "app/_not-found/page.js", "/api/ai/route": "app/api/ai/route.js", "/api/analysis/strategy/[symbol]/route": "app/api/analysis/strategy/[symbol]/route.js", "/api/analysis/swing/[symbol]/route": "app/api/analysis/swing/[symbol]/route.js", "/api/backtest/perfect-pick/route": "app/api/backtest/perfect-pick/route.js", "/api/catalyst/detect/route": "app/api/catalyst/detect/route.js", "/api/scanner/full/route": "app/api/scanner/full/route.js", "/api/scanner/gap-scan/route": "app/api/scanner/gap-scan/route.js", "/api/scanner/perfect-pick/route": "app/api/scanner/perfect-pick/route.js", "/api/scanner/quick/route": "app/api/scanner/quick/route.js", "/api/scanner/sector/[sector]/route": "app/api/scanner/sector/[sector]/route.js", "/api/scanner/strategies/route": "app/api/scanner/strategies/route.js", "/api/stocks/historical/[symbol]/route": "app/api/stocks/historical/[symbol]/route.js", "/api/stocks/quote/[symbol]/route": "app/api/stocks/quote/[symbol]/route.js", "/api/stocks/search/route": "app/api/stocks/search/route.js", "/api/trading/route": "app/api/trading/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/page": "app/page.js"}