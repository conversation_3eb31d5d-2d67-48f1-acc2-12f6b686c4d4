module.exports = [
"[turbopack-node]/transforms/postcss.ts { CONFIG => \"[project]/swing-trader-ai/postcss.config.mjs [postcss] (ecmascript)\" } [postcss] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "build/chunks/6bf44_8854af41._.js",
  "build/chunks/[root-of-the-server]__71eb1fec._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[turbopack-node]/transforms/postcss.ts { CONFIG => \"[project]/swing-trader-ai/postcss.config.mjs [postcss] (ecmascript)\" } [postcss] (ecmascript)");
    });
});
}),
];