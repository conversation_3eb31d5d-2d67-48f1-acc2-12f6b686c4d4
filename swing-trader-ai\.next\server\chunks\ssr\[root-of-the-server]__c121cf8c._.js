module.exports=[93695,(a,b,c)=>{b.exports=a.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},67057,(a,b,c)=>{},21720,a=>{"use strict";a.s(["default",()=>b]);let b=(0,a.i(427).registerClientReference)(function(){throw Error("Attempted to call the default export of [project]/swing-trader-ai/src/app/page.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/swing-trader-ai/src/app/page.tsx <module evaluation>","default")},35200,a=>{"use strict";a.s(["default",()=>b]);let b=(0,a.i(427).registerClientReference)(function(){throw Error("Attempted to call the default export of [project]/swing-trader-ai/src/app/page.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/swing-trader-ai/src/app/page.tsx","default")},58039,a=>{"use strict";a.i(21720);var b=a.i(35200);a.n(b)}];

//# sourceMappingURL=%5Broot-of-the-server%5D__c121cf8c._.js.map