module.exports=[75092,e=>{"use strict";e.s(["handler",()=>O,"patchFetch",()=>_,"routeModule",()=>x,"serverHooks",()=>I,"workAsyncStorage",()=>T,"workUnitAsyncStorage",()=>b],75092);var t=e.i(11971),a=e.i(6780),r=e.i(51842),n=e.i(62950),l=e.i(21346),i=e.i(30506),s=e.i(63077),o=e.i(34765),c=e.i(64182),u=e.i(85062),p=e.i(51548),h=e.i(95133),d=e.i(8819),g=e.i(41050),m=e.i(93695);e.i(96641);var f=e.i(3893);e.s(["GET",()=>E,"POST",()=>C],40636);var y=e.i(59169),P=e.i(80412),v=e.i(58445),w=e.i(78006),S=e.i(29547);class A{polygonAPI;fmpAPI;constructor(e,t){this.polygonAPI=new w.PolygonAPI(e),this.fmpAPI=new S.FMPAPI(t)}async analyzeTechnicalGate(e){try{let t=await this.getHistoricalData(e,250);if(!t||t.length<200)return console.error(`Insufficient data for ${e} - need at least 200 days`),null;let a=t[t.length-1].close,r=v.TechnicalIndicators.calculateSMA(t,200),n=v.TechnicalIndicators.calculateEMA(t,8),l=this.calculateVWAP(t.slice(-20)),i=this.analyzeDailyTrend(t),s=a>r[r.length-1],o=a>n[n.length-1],c=this.checkEMA8Respect(t,n),u=this.checkAllTimeHigh(t),p=this.checkCleanBreakout(t),h=this.checkVolumeExpansion(t),d=this.calculateResistanceLevels(t),g=this.calculateSupportLevels(t),m=this.calculateGateScore({dailyTrendConfirmed:i,aboveSMA200:s,aboveEMA8:o,respectsEMA8:c,isAtAllTimeHigh:u,hasCleanBreakout:p,volumeExpansion:h}),f=this.calculateOverallGrade(m);return{symbol:e,dailyTrendConfirmed:i,aboveSMA200:s,aboveEMA8:o,respectsEMA8:c,isAtAllTimeHigh:u,hasCleanBreakout:p,volumeExpansion:h,overallGrade:f,gateScore:m,resistanceLevels:d,supportLevels:g,keyTechnicalLevels:{sma200:r[r.length-1],ema8:n[n.length-1],vwap:l,previousHigh:Math.max(...t.slice(-20).map(e=>e.high)),previousLow:Math.min(...t.slice(-20).map(e=>e.low))}}}catch(t){return console.error(`Error analyzing technical gate for ${e}:`,t),null}}async getHistoricalData(e,t){try{let a=new Date,r=new Date;return r.setDate(r.getDate()-t),await this.polygonAPI.getHistoricalData(e,r.toISOString().split("T")[0],a.toISOString().split("T")[0],"1","day")}catch(t){return console.error(`Error fetching historical data for ${e}:`,t),[]}}analyzeDailyTrend(e){if(e.length<20)return!1;let t=e.slice(-20),a=t.slice(0,10),r=t.slice(10),n=Math.max(...a.map(e=>e.high)),l=Math.min(...a.map(e=>e.low)),i=Math.max(...r.map(e=>e.high)),s=Math.min(...r.map(e=>e.low));return i>n&&s>l}checkEMA8Respect(e,t){if(e.length<20||t.length<20)return!1;let a=e.slice(-20),r=t.slice(-20),n=0;for(let e=0;e<a.length;e++){let t=a[e],l=r[e];t.low>=.98*l&&n++}return n/a.length>=.7}checkAllTimeHigh(e){return e[e.length-1].close>=.95*Math.max(...e.map(e=>e.high))}checkCleanBreakout(e){if(e.length<30)return!1;let t=e.slice(-30),a=t.slice(-5),r=t.slice(-30,-5),n=Math.max(...r.map(e=>e.high)),l=Math.min(...r.map(e=>e.low)),i=Math.max(...a.map(e=>e.high)),s=a.reduce((e,t)=>e+t.volume,0)/a.length,o=r.reduce((e,t)=>e+t.volume,0)/r.length;return(n-l)/l<.2&&i>n&&s>1.5*o}checkVolumeExpansion(e){if(e.length<20)return!1;let t=e.slice(-5),a=e.slice(-25,-5);return t.reduce((e,t)=>e+t.volume,0)/t.length>1.5*(a.reduce((e,t)=>e+t.volume,0)/a.length)}calculateVWAP(e){let t=0,a=0;for(let r of e)a+=(r.high+r.low+r.close)/3*r.volume,t+=r.volume;return t>0?a/t:0}calculateResistanceLevels(e){let t=[];for(let a=5;a<e.length-5;a++){let r=e[a],n=!0;for(let t=a-5;t<=a+5;t++)if(t!==a&&e[t].high>=r.high){n=!1;break}n&&t.push(r.high)}return t.slice(-5).sort((e,t)=>t-e)}calculateSupportLevels(e){let t=[];for(let a=5;a<e.length-5;a++){let r=e[a],n=!0;for(let t=a-5;t<=a+5;t++)if(t!==a&&e[t].low<=r.low){n=!1;break}n&&t.push(r.low)}return t.slice(-5).sort((e,t)=>t-e)}calculateGateScore(e){let t=0;return e.dailyTrendConfirmed&&(t+=20),e.aboveSMA200&&(t+=20),e.aboveEMA8&&(t+=15),e.respectsEMA8&&(t+=15),e.isAtAllTimeHigh&&(t+=15),e.hasCleanBreakout&&(t+=10),e.volumeExpansion&&(t+=5),Math.min(100,t)}calculateOverallGrade(e){return e>=90?"A":e>=80?"B":e>=70?"C":e>=60?"D":"F"}async batchAnalyzeTechnicalGates(e){let t=[];for(let a=0;a<e.length;a+=5){let r=e.slice(a,a+5).map(e=>this.analyzeTechnicalGate(e)),n=await Promise.all(r);t.push(...n.filter(e=>null!==e)),a+5<e.length&&await new Promise(e=>setTimeout(e,1e3))}return t.sort((e,t)=>t.gateScore-e.gateScore)}}var k=e.i(27478);class R{gapScanner;technicalAnalyzer;catalystEngine;polygonAPI;fmpAPI;constructor(e,t){this.gapScanner=new P.PreMarketGapScanner(e,t),this.technicalAnalyzer=new A(t,e),this.catalystEngine=new k.CatalystDetectionEngine(e,t),this.polygonAPI=new w.PolygonAPI(t),this.fmpAPI=new S.FMPAPI(e)}async runPerfectPickScan(e=1e5,t=2,a){console.log("🎯 Starting Perfect-Pick Trading System scan...");try{console.log("📊 Running pre-market gap scan...");let r=(await this.gapScanner.runGapScan(a)).filter(e=>e.gapPercent>=3&&e.gapPercent<=15&&e.marketCap>=8e8&&e.price>1);if(console.log(`✅ Found ${r.length} qualified gap candidates`),0===r.length)return[];console.log("🔍 Running technical gate analysis...");let n=r.map(e=>e.symbol),l=(await this.technicalAnalyzer.batchAnalyzeTechnicalGates(n)).filter(e=>["A","B"].includes(e.overallGrade)&&e.aboveSMA200&&e.aboveEMA8);console.log(`✅ ${l.length} stocks passed technical gate`);let i=[];for(let a of r){let r=l.find(e=>e.symbol===a.symbol);if(!r)continue;let n=a.catalyst;if(!n||!this.isValidCatalyst(n))continue;let s=await this.createPerfectPickSetup(a,r,n,e,t);s&&this.validatePerfectPickSetup(s)&&i.push(s)}return i.sort((e,t)=>t.overallScore-e.overallScore),console.log(`🎯 Generated ${i.length} Perfect-Pick setups`),i}catch(e){return console.error("Error running Perfect-Pick scan:",e),[]}}async createPerfectPickSetup(e,t,a,r,n){try{let l=e.symbol,i=e.price,s=e.preMarketLow,o=.99*s,c=i-o;if(c<=0)return null;let u=Math.floor(n/100*r/c),p=Math.floor(.05*r/i),h=Math.min(u,p),d=this.checkExclusionCriteria(e,t,a),g={hasValidCatalyst:this.isValidCatalyst(a),meetsGapCriteria:e.meetsAllCriteria,passesTechnicalGate:["A","B"].includes(t.overallGrade),hasEntryTrigger:!0,meetsRiskReward:!0,noExclusionFlags:0===d.length},m=this.calculateOverallScore(e,t,a,g),f=this.calculateSetupGrade(m);return{symbol:l,name:e.name,catalyst:a,gapScan:e,technicalGate:t,riskManagement:{entryPrice:i,stopLoss:o,stopLossType:"pre_market_low",riskPerShare:c,positionSize:h,accountRiskPercent:n,maxPositionPercent:5},rewardPlanning:{riskRewardRatio:3,target3R:i+3*c,target4R:i+4*c,target5R:i+5*c,scaleOutPlan:[{level:3,percentage:25},{level:4,percentage:25},{level:5,percentage:25}]},overallScore:m,setupGrade:f,exclusionReasons:d,validationChecks:g,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}}catch(t){return console.error(`Error creating Perfect-Pick setup for ${e.symbol}:`,t),null}}async generateEntryTrigger(e,t){try{let a,r,n=await this.fmpAPI.getStockQuote(e),l=n.price,i=.995*l,s=[];l>t?(a="pmh_break",r="immediate",s.push("Clean break above pre-market high")):l<=i&&l>.98*i?(a="vwap_pullback",r="wait_for_pullback",s.push("Pullback to VWAP support")):(a="first_candle_close",r="breakout_confirmation",s.push("Wait for first 5-min candle close above PMH"));let o=n.volume>1.5*(n.volume||0);return{symbol:e,preMarketHigh:t,preMarketLow:.95*t,vwap:i,entrySignalType:a,entryPrice:l,entryTime:new Date().toISOString(),volumeConfirmation:o,vwapRising:!0,noMajorResistance:!0,triggerValid:o&&!0,urgency:r,conditions:s}}catch(t){return console.error(`Error generating entry trigger for ${e}:`,t),null}}isValidCatalyst(e){let t=["earnings_beat_guidance","fda_approval","drug_trial_results","contract_win","partnership","merger_acquisition"].includes(e.type)||["analyst_upgrade","stock_split","sector_rotation"].includes(e.type),a="fresh"===e.freshness||"moderate"===e.freshness,r=e.qualityScore>=6,n=e.verified;return t&&a&&r&&n}checkExclusionCriteria(e,t,a){let r=[];return t.dailyTrendConfirmed||r.push("Stock not in confirmed daily uptrend"),t.aboveSMA200||r.push("Stock below 200-day SMA"),e.gapPercent>15&&r.push("Gap too extended (>15%)"),e.averageDailyVolume<5e5&&r.push("Low liquidity (avg daily volume <500K)"),"bearish"===a.impact&&r.push("Negative catalyst detected"),"stale"===a.freshness&&r.push("Catalyst is stale (>72 hours old)"),r}calculateOverallScore(e,t,a,r){let n;return Math.min(100,Math.round(n=0+Math.min(25,2*e.gapPercent)+t.gateScore/100*35+a.qualityScore/10*25+Object.values(r).filter(Boolean).length/Object.keys(r).length*15))}calculateSetupGrade(e){return e>=95?"A+":e>=90?"A":e>=85?"B+":e>=80?"B":e>=75?"C+":e>=70?"C":e>=60?"D":"F"}validatePerfectPickSetup(e){let t=e.validationChecks;return[t.hasValidCatalyst,t.meetsGapCriteria,t.passesTechnicalGate,t.meetsRiskReward,t.noExclusionFlags].every(e=>e)&&e.overallScore>=70}getSetupSummary(e){let t=e.length,a=e.reduce((e,t)=>(e[t.setupGrade]=(e[t.setupGrade]||0)+1,e),{}),r=e.reduce((e,t)=>(e[t.catalyst.type]=(e[t.catalyst.type]||0)+1,e),{}),n=t>0?e.reduce((e,t)=>e+t.overallScore,0)/t:0,l=t>0?e.reduce((e,t)=>e+t.gapScan.gapPercent,0)/t:0;return{totalSetups:t,avgScore:Math.round(100*n)/100,avgGap:Math.round(100*l)/100,gradeBreakdown:a,catalystBreakdown:r,generatedAt:new Date().toISOString()}}async updatePerfectPickSetups(e){let t=[];for(let a of e)try{let e=(await this.fmpAPI.getStockQuote(a.symbol)).price,r=await this.generateEntryTrigger(a.symbol,a.gapScan.preMarketHigh),n={...a,entryTrigger:r,riskManagement:{...a.riskManagement,entryPrice:e},updatedAt:new Date().toISOString()};t.push(n)}catch(e){console.error(`Error updating setup for ${a.symbol}:`,e),t.push(a)}return t}}async function E(e){try{let{searchParams:t}=new URL(e.url),a=parseInt(t.get("accountSize")||"100000"),r=parseFloat(t.get("riskPercent")||"2"),n=t.get("universe")?.split(",").filter(Boolean),l=parseInt(t.get("limit")||"20");console.log("🎯 Perfect-Pick API called with params:",{accountSize:a,riskPercent:r,customUniverse:n?.length||"default",limit:l});let i=new R(process.env.FMP_API_KEY,process.env.POLYGON_API_KEY),s=(await i.runPerfectPickScan(a,r,n)).slice(0,l),o=i.getSetupSummary(s),c={success:!0,data:{setups:s,summary:o,scanParams:{accountSize:a,riskPercent:r,universeSize:n?.length||"default",limit:l},timestamp:new Date().toISOString()}};return y.NextResponse.json(c)}catch(e){return console.error("Error in Perfect-Pick API:",e),y.NextResponse.json({success:!1,error:"Failed to run Perfect-Pick scan",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function C(e){try{let{action:t,data:a}=await e.json(),r=new R(process.env.FMP_API_KEY,process.env.POLYGON_API_KEY);switch(t){case"update_setups":let n=await r.updatePerfectPickSetups(a.setups);return y.NextResponse.json({success:!0,data:{setups:n}});case"generate_entry_trigger":let l=await r.generateEntryTrigger(a.symbol,a.preMarketHigh);return y.NextResponse.json({success:!0,data:{entryTrigger:l}});default:return y.NextResponse.json({success:!1,error:"Invalid action"},{status:400})}}catch(e){return console.error("Error in Perfect-Pick POST API:",e),y.NextResponse.json({success:!1,error:"Failed to process Perfect-Pick request",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}var M=e.i(40636);let x=new t.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/scanner/perfect-pick/route",pathname:"/api/scanner/perfect-pick",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/swing-trader-ai/src/app/api/scanner/perfect-pick/route.ts",nextConfigOutput:"",userland:M}),{workAsyncStorage:T,workUnitAsyncStorage:b,serverHooks:I}=x;function _(){return(0,r.patchFetch)({workAsyncStorage:T,workUnitAsyncStorage:b})}async function O(e,t,r){var y;let P="/api/scanner/perfect-pick/route";P=P.replace(/\/index$/,"")||"/";let v=await x.prepare(e,t,{srcPage:P,multiZoneDraftMode:!1});if(!v)return t.statusCode=400,t.end("Bad Request"),null==r.waitUntil||r.waitUntil.call(r,Promise.resolve()),null;let{buildId:w,params:S,nextConfig:A,isDraftMode:k,prerenderManifest:R,routerServerContext:E,isOnDemandRevalidate:C,revalidateOnlyGenerated:M,resolvedPathname:T}=v,b=(0,i.normalizeAppPath)(P),I=!!(R.dynamicRoutes[b]||R.routes[T]);if(I&&!k){let e=!!R.routes[T],t=R.dynamicRoutes[b];if(t&&!1===t.fallback&&!e)throw new m.NoFallbackError}let _=null;!I||x.isDev||k||(_="/index"===(_=T)?"/":_);let O=!0===x.isDev||!I,N=I&&!O,D=e.method||"GET",G=(0,l.getTracer)(),H=G.getActiveScopeSpan(),q={params:S,prerenderManifest:R,renderOpts:{experimental:{cacheComponents:!!A.experimental.cacheComponents,authInterrupts:!!A.experimental.authInterrupts},supportsDynamicResponse:O,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(y=A.experimental)?void 0:y.cacheLife,isRevalidate:N,waitUntil:r.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,a,r)=>x.onRequestError(e,t,r,E)},sharedContext:{buildId:w}},F=new s.NodeNextRequest(e),U=new s.NodeNextResponse(t),L=o.NextRequestAdapter.fromNodeNextRequest(F,(0,o.signalFromNodeResponse)(t));try{let i=async a=>x.handle(L,q).finally(()=>{if(!a)return;a.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let r=G.getRootSpanAttributes();if(!r)return;if(r.get("next.span_type")!==c.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${r.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=r.get("next.route");if(n){let e=`${D} ${n}`;a.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),a.updateName(e)}else a.updateName(`${D} ${e.url}`)}),s=async l=>{var s,o;let c=async({previousCacheEntry:a})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&C&&M&&!a)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let s=await i(l);e.fetchMetrics=q.renderOpts.fetchMetrics;let o=q.renderOpts.pendingWaitUntil;o&&r.waitUntil&&(r.waitUntil(o),o=void 0);let c=q.renderOpts.collectedTags;if(!I)return await (0,p.sendResponse)(F,U,s,q.renderOpts.pendingWaitUntil),null;{let e=await s.blob(),t=(0,h.toNodeOutgoingHttpHeaders)(s.headers);c&&(t[g.NEXT_CACHE_TAGS_HEADER]=c),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let a=void 0!==q.renderOpts.collectedRevalidate&&!(q.renderOpts.collectedRevalidate>=g.INFINITE_CACHE)&&q.renderOpts.collectedRevalidate,r=void 0===q.renderOpts.collectedExpire||q.renderOpts.collectedExpire>=g.INFINITE_CACHE?void 0:q.renderOpts.collectedExpire;return{value:{kind:f.CachedRouteKind.APP_ROUTE,status:s.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:a,expire:r}}}}catch(t){throw(null==a?void 0:a.isStale)&&await x.onRequestError(e,t,{routerKind:"App Router",routePath:P,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:N,isOnDemandRevalidate:C})},E),t}},m=await x.handleResponse({req:e,nextConfig:A,cacheKey:_,routeKind:a.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:R,isRoutePPREnabled:!1,isOnDemandRevalidate:C,revalidateOnlyGenerated:M,responseGenerator:c,waitUntil:r.waitUntil});if(!I)return null;if((null==m||null==(s=m.value)?void 0:s.kind)!==f.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==m||null==(o=m.value)?void 0:o.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",C?"REVALIDATED":m.isMiss?"MISS":m.isStale?"STALE":"HIT"),k&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let y=(0,h.fromNodeOutgoingHttpHeaders)(m.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&I||y.delete(g.NEXT_CACHE_TAGS_HEADER),!m.cacheControl||t.getHeader("Cache-Control")||y.get("Cache-Control")||y.set("Cache-Control",(0,d.getCacheControlHeader)(m.cacheControl)),await (0,p.sendResponse)(F,U,new Response(m.value.body,{headers:y,status:m.value.status||200})),null};H?await s(H):await G.withPropagatedContext(e.headers,()=>G.trace(c.BaseServerSpan.handleRequest,{spanName:`${D} ${e.url}`,kind:l.SpanKind.SERVER,attributes:{"http.method":D,"http.target":e.url}},s))}catch(t){if(t instanceof m.NoFallbackError||await x.onRequestError(e,t,{routerKind:"App Router",routePath:b,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:N,isOnDemandRevalidate:C})}),I)throw t;return await (0,p.sendResponse)(F,U,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=6bf44_next_dist_esm_build_templates_app-route_cc5f841a.js.map