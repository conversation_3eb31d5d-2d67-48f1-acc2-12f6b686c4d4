import axios from 'axios'
import { StockData } from '@/types/trading'

const FMP_BASE_URL = 'https://financialmodelingprep.com/api'
const API_KEY = process.env.FMP_API_KEY

export class FMPAPI {
  private apiKey: string

  constructor(apiKey?: string) {
    this.apiKey = apiKey || API_KEY || ''
    if (!this.apiKey) {
      throw new Error('FMP API key is required')
    }
  }

  // Get real-time stock quote
  async getStockQuote(symbol: string): Promise<StockData> {
    try {
      const response = await axios.get(
        `${FMP_BASE_URL}/v3/quote/${symbol}`,
        {
          params: {
            apikey: this.apiKey
          }
        }
      )

      const data = response.data[0]
      if (!data) {
        throw new Error(`No data found for symbol ${symbol}`)
      }

      return {
        symbol: data.symbol,
        name: data.name || data.symbol,
        price: data.price,
        change: data.change,
        changePercent: data.changesPercentage,
        volume: data.volume,
        marketCap: data.marketCap,
        pe: data.pe,
        dividend: undefined // Will be fetched separately if needed
      }
    } catch (error) {
      console.error('Error fetching FMP stock quote:', error)
      throw new Error(`Failed to fetch quote for ${symbol}`)
    }
  }

  // Get company profile
  async getCompanyProfile(symbol: string) {
    try {
      const response = await axios.get(
        `${FMP_BASE_URL}/v3/profile/${symbol}`,
        {
          params: {
            apikey: this.apiKey
          }
        }
      )

      return response.data[0]
    } catch (error) {
      console.error('Error fetching company profile:', error)
      return null
    }
  }

  // Get financial ratios
  async getFinancialRatios(symbol: string) {
    try {
      const response = await axios.get(
        `${FMP_BASE_URL}/v3/ratios/${symbol}`,
        {
          params: {
            apikey: this.apiKey
          }
        }
      )

      return response.data[0] // Most recent ratios
    } catch (error) {
      console.error('Error fetching financial ratios:', error)
      return null
    }
  }

  // Get key metrics
  async getKeyMetrics(symbol: string) {
    try {
      const response = await axios.get(
        `${FMP_BASE_URL}/v3/key-metrics/${symbol}`,
        {
          params: {
            apikey: this.apiKey
          }
        }
      )

      return response.data[0] // Most recent metrics
    } catch (error) {
      console.error('Error fetching key metrics:', error)
      return null
    }
  }

  // Get analyst recommendations
  async getAnalystRecommendations(symbol: string) {
    try {
      const response = await axios.get(
        `${FMP_BASE_URL}/v3/analyst-stock-recommendations/${symbol}`,
        {
          params: {
            apikey: this.apiKey
          }
        }
      )

      return response.data
    } catch (error) {
      console.error('Error fetching analyst recommendations:', error)
      return []
    }
  }

  // Get earnings calendar
  async getEarningsCalendar(from?: string, to?: string) {
    try {
      const params: any = {
        apikey: this.apiKey
      }

      if (from) params.from = from
      if (to) params.to = to

      const response = await axios.get(
        `${FMP_BASE_URL}/v3/earning_calendar`,
        { params }
      )

      return response.data
    } catch (error) {
      console.error('Error fetching earnings calendar:', error)
      return []
    }
  }

  // Get economic calendar
  async getEconomicCalendar(from?: string, to?: string) {
    try {
      const params: any = {
        apikey: this.apiKey
      }

      if (from) params.from = from
      if (to) params.to = to

      const response = await axios.get(
        `${FMP_BASE_URL}/v3/economic_calendar`,
        { params }
      )

      return response.data
    } catch (error) {
      console.error('Error fetching economic calendar:', error)
      return []
    }
  }

  // Search for stocks
  async searchStocks(query: string, limit: number = 10) {
    try {
      const response = await axios.get(
        `${FMP_BASE_URL}/v3/search`,
        {
          params: {
            query,
            limit,
            apikey: this.apiKey
          }
        }
      )

      return response.data
    } catch (error) {
      console.error('Error searching stocks:', error)
      return []
    }
  }

  // Get sector performance
  async getSectorPerformance() {
    try {
      const response = await axios.get(
        `${FMP_BASE_URL}/v3/sector-performance`,
        {
          params: {
            apikey: this.apiKey
          }
        }
      )

      return response.data
    } catch (error) {
      console.error('Error fetching sector performance:', error)
      return []
    }
  }

  // Get market gainers/losers
  async getMarketMovers(type: 'gainers' | 'losers' | 'actives') {
    try {
      const response = await axios.get(
        `${FMP_BASE_URL}/v3/stock_market/${type}`,
        {
          params: {
            apikey: this.apiKey
          }
        }
      )

      return response.data
    } catch (error) {
      console.error(`Error fetching market ${type}:`, error)
      return []
    }
  }

  // ===== CATALYST DETECTION ENDPOINTS =====

  // Get earnings calendar for catalyst detection
  async getEarningsCalendar(symbol?: string, days: number = 30) {
    try {
      const fromDate = new Date()
      fromDate.setDate(fromDate.getDate() - days)
      const toDate = new Date()

      const response = await axios.get(
        `${FMP_BASE_URL}/v3/earning_calendar`,
        {
          params: {
            apikey: this.apiKey,
            from: fromDate.toISOString().split('T')[0],
            to: toDate.toISOString().split('T')[0],
            ...(symbol && { symbol: symbol.toUpperCase() })
          }
        }
      )

      return response.data || []
    } catch (error) {
      console.error('Error fetching earnings calendar:', error)
      return []
    }
  }

  // Get stock news for catalyst detection
  async getStockNews(symbol: string, limit: number = 50) {
    try {
      const response = await axios.get(
        `${FMP_BASE_URL}/v3/stock_news`,
        {
          params: {
            apikey: this.apiKey,
            tickers: symbol.toUpperCase(),
            limit
          }
        }
      )

      return response.data || []
    } catch (error) {
      console.error('Error fetching stock news:', error)
      return []
    }
  }

  // Get analyst recommendations
  async getAnalystRecommendations(symbol: string, days: number = 30) {
    try {
      const response = await axios.get(
        `${FMP_BASE_URL}/v3/analyst-stock-recommendations/${symbol.toUpperCase()}`,
        {
          params: {
            apikey: this.apiKey,
            limit: days
          }
        }
      )

      return response.data || []
    } catch (error) {
      console.error('Error fetching analyst recommendations:', error)
      return []
    }
  }

  // Get insider trading data
  async getInsiderTrading(symbol: string, days: number = 30) {
    try {
      const response = await axios.get(
        `${FMP_BASE_URL}/v4/insider-trading`,
        {
          params: {
            apikey: this.apiKey,
            symbol: symbol.toUpperCase(),
            limit: days * 5 // Approximate multiple to get enough data
          }
        }
      )

      // Filter to last N days
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - days)

      return (response.data || []).filter((trade: any) =>
        new Date(trade.filingDate) >= cutoffDate
      )
    } catch (error) {
      console.error('Error fetching insider trading:', error)
      return []
    }
  }

  // Get SEC filings
  async getSECFilings(symbol: string, days: number = 30) {
    try {
      const response = await axios.get(
        `${FMP_BASE_URL}/v3/sec_filings/${symbol.toUpperCase()}`,
        {
          params: {
            apikey: this.apiKey,
            limit: days * 2 // Get more filings to filter by date
          }
        }
      )

      // Filter to last N days
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - days)

      return (response.data || []).filter((filing: any) =>
        new Date(filing.filedDate) >= cutoffDate
      )
    } catch (error) {
      console.error('Error fetching SEC filings:', error)
      return []
    }
  }

  // Get pre-market quotes for gap scanning
  async getPreMarketQuote(symbol: string) {
    try {
      const response = await axios.get(
        `${FMP_BASE_URL}/v3/quote/${symbol.toUpperCase()}`,
        {
          params: {
            apikey: this.apiKey
          }
        }
      )

      const data = response.data[0]
      if (!data) return null

      return {
        symbol: data.symbol,
        price: data.price,
        previousClose: data.previousClose,
        change: data.change,
        changePercent: data.changesPercentage,
        volume: data.volume,
        marketCap: data.marketCap,
        avgVolume: data.avgVolume,
        // Pre-market specific data (if available)
        preMarketPrice: data.preMarketPrice || data.price,
        preMarketChange: data.preMarketChange || data.change,
        preMarketChangePercent: data.preMarketChangePercent || data.changesPercentage
      }
    } catch (error) {
      console.error('Error fetching pre-market quote:', error)
      return null
    }
  }

  // Get multiple pre-market quotes efficiently
  async getMultiplePreMarketQuotes(symbols: string[]) {
    try {
      const symbolsString = symbols.map(s => s.toUpperCase()).join(',')
      const response = await axios.get(
        `${FMP_BASE_URL}/v3/quote/${symbolsString}`,
        {
          params: {
            apikey: this.apiKey
          }
        }
      )

      return (response.data || []).map((data: any) => ({
        symbol: data.symbol,
        price: data.price,
        previousClose: data.previousClose,
        change: data.change,
        changePercent: data.changesPercentage,
        volume: data.volume,
        marketCap: data.marketCap,
        avgVolume: data.avgVolume,
        preMarketPrice: data.preMarketPrice || data.price,
        preMarketChange: data.preMarketChange || data.change,
        preMarketChangePercent: data.preMarketChangePercent || data.changesPercentage
      }))
    } catch (error) {
      console.error('Error fetching multiple pre-market quotes:', error)
      return []
    }
  }

  // Get company profile for additional context
  async getCompanyProfile(symbol: string) {
    try {
      const response = await axios.get(
        `${FMP_BASE_URL}/v3/profile/${symbol.toUpperCase()}`,
        {
          params: {
            apikey: this.apiKey
          }
        }
      )

      return response.data[0] || null
    } catch (error) {
      console.error('Error fetching company profile:', error)
      return null
    }
  }
}

// Create a singleton instance
export const fmpAPI = new FMPAPI()
