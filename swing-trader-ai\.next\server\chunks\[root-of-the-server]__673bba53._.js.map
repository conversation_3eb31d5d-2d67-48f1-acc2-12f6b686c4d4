{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/lib/openai.ts"], "sourcesContent": ["import OpenAI from 'openai';\nimport { EnhancedScanResult } from './enhancedSwingScanner';\nimport { StrategySetup } from './swingStrategies';\n\n// Rate limiting configuration\nconst RATE_LIMIT_DELAY = 1000; // 1 second between requests\nlet lastRequestTime = 0;\n\n// Initialize OpenAI client\nconst openai = new OpenAI({\n  apiKey: process.env.OPENAI_API_KEY,\n});\n\n// Check if OpenAI is enabled\nexport const isOpenAIEnabled = (): boolean => {\n  return process.env.OPENAI_ENABLED === 'true' && !!process.env.OPENAI_API_KEY;\n};\n\n// Rate limiting helper\nconst enforceRateLimit = async (): Promise<void> => {\n  const now = Date.now();\n  const timeSinceLastRequest = now - lastRequestTime;\n  \n  if (timeSinceLastRequest < RATE_LIMIT_DELAY) {\n    const delay = RATE_LIMIT_DELAY - timeSinceLastRequest;\n    await new Promise(resolve => setTimeout(resolve, delay));\n  }\n  \n  lastRequestTime = Date.now();\n};\n\n// Get the latest available OpenAI model\nexport const getLatestModel = async (): Promise<string> => {\n  try {\n    await enforceRateLimit();\n    \n    const models = await openai.models.list();\n    const gptModels = models.data\n      .filter(model => model.id.startsWith('gpt-'))\n      .sort((a, b) => b.created - a.created);\n    \n    // Prefer GPT-4 models, then GPT-3.5\n    const preferredModels = [\n      'gpt-4o',\n      'gpt-4o-mini', \n      'gpt-4-turbo',\n      'gpt-4',\n      'gpt-3.5-turbo'\n    ];\n    \n    for (const preferred of preferredModels) {\n      const found = gptModels.find(model => model.id === preferred);\n      if (found) {\n        console.log(`Using OpenAI model: ${found.id}`);\n        return found.id;\n      }\n    }\n    \n    // Fallback to the latest GPT model\n    if (gptModels.length > 0) {\n      console.log(`Using fallback OpenAI model: ${gptModels[0].id}`);\n      return gptModels[0].id;\n    }\n    \n    throw new Error('No GPT models available');\n  } catch (error) {\n    console.error('Error getting OpenAI models:', error);\n    return 'gpt-4o'; // Default fallback\n  }\n};\n\n// AI-powered market commentary\nexport const generateMarketCommentary = async (\n  scanResults: EnhancedScanResult[],\n  marketConditions: any\n): Promise<string> => {\n  if (!isOpenAIEnabled()) {\n    return \"AI analysis disabled. Enable in configuration to get intelligent market insights.\";\n  }\n\n  try {\n    await enforceRateLimit();\n    \n    const model = await getLatestModel();\n    const topResults = scanResults.slice(0, 5);\n    \n    const prompt = `As a professional swing trading analyst, provide a concise market commentary based on the following scan results and market conditions:\n\nMarket Conditions:\n- Time: ${marketConditions.timeOfDay}\n- Market Hours: ${marketConditions.marketHours ? 'Open' : 'Closed'}\n- Optimal Scan Time: ${marketConditions.isOptimalScanTime ? 'Yes' : 'No'}\n\nTop Trading Opportunities:\n${topResults.map((result, i) => `\n${i + 1}. ${result.symbol} (Score: ${result.overallScore.toFixed(1)}/100)\n   - Best Strategy: ${result.bestStrategy || 'None'}\n   - Price Action: Recent momentum and volume patterns\n   - Key Levels: Support/resistance analysis\n`).join('')}\n\nProvide a 2-3 paragraph market commentary focusing on:\n1. Overall market sentiment and trading conditions\n2. Key themes and sectors showing strength/weakness\n3. Risk considerations and trading recommendations\n\nKeep it professional, actionable, and under 200 words.`;\n\n    const completion = await openai.chat.completions.create({\n      model,\n      messages: [\n        {\n          role: 'system',\n          content: 'You are a professional swing trading analyst with expertise in technical analysis and market psychology. Provide clear, actionable insights.'\n        },\n        {\n          role: 'user',\n          content: prompt\n        }\n      ],\n      max_tokens: 300,\n      temperature: 0.7,\n    });\n\n    return completion.choices[0]?.message?.content || 'Unable to generate market commentary at this time.';\n  } catch (error) {\n    console.error('Error generating market commentary:', error);\n    return 'Market commentary temporarily unavailable. Technical analysis remains fully functional.';\n  }\n};\n\n// AI-powered risk assessment for individual setups\nexport const generateRiskAssessment = async (\n  setup: StrategySetup & { symbol: string },\n  marketData?: any\n): Promise<{\n  riskScore: number;\n  riskFactors: string[];\n  recommendations: string[];\n  sentiment: 'bullish' | 'bearish' | 'neutral';\n  technicalAnalysis?: string;\n  entryStrategy?: string;\n  exitStrategy?: string;\n}> => {\n  if (!isOpenAIEnabled()) {\n    return {\n      riskScore: 5,\n      riskFactors: ['AI analysis disabled'],\n      recommendations: ['Enable AI features for enhanced risk assessment'],\n      sentiment: 'neutral',\n      technicalAnalysis: 'AI analysis disabled. Enable OpenAI integration for comprehensive technical analysis.',\n      entryStrategy: 'Follow the provided entry conditions and timing.',\n      exitStrategy: 'Use the calculated stop loss and take profit levels.'\n    };\n  }\n\n  try {\n    await enforceRateLimit();\n\n    const model = await getLatestModel();\n\n    const riskReward = ((setup.targets[0] - setup.entryPrice) / (setup.entryPrice - setup.stopLoss)).toFixed(2);\n\n    const prompt = `As a professional swing trading analyst, provide a comprehensive analysis of this trading setup:\n\nTRADING SETUP DETAILS:\nSymbol: ${setup.symbol}\nStrategy: ${setup.strategy}\nConfidence: ${setup.confidence}%\nEntry Price: $${setup.entryPrice}\nStop Loss: $${setup.stopLoss}\nFirst Target: $${setup.targets[0]}\nRisk/Reward Ratio: ${riskReward}:1\nPosition Size: ${setup.positionSize} shares\nRisk Amount: $${setup.riskAmount}\n\nENTRY CONDITIONS:\n${setup.preciseEntry?.conditions?.join('\\n') || 'Standard entry conditions apply'}\n\nTIMING: ${setup.preciseEntry?.timing || 'Market hours'}\n\nProvide a detailed JSON response with:\n{\n  \"riskScore\": 1-10 (1=low risk, 10=high risk),\n  \"riskFactors\": [\"specific risk factor 1\", \"specific risk factor 2\", ...],\n  \"recommendations\": [\"actionable recommendation 1\", \"actionable recommendation 2\", ...],\n  \"sentiment\": \"bullish|bearish|neutral\",\n  \"technicalAnalysis\": \"2-3 sentence technical analysis of the setup including key levels and momentum\",\n  \"entryStrategy\": \"Specific entry execution guidance\",\n  \"exitStrategy\": \"Detailed exit management strategy\"\n}\n\nFocus on:\n1. Technical setup quality and market structure\n2. Risk management and position sizing appropriateness\n3. Entry timing and execution considerations\n4. Exit strategy and profit management\n5. Current market conditions impact`;\n\n    const completion = await openai.chat.completions.create({\n      model,\n      messages: [\n        {\n          role: 'system',\n          content: 'You are a professional swing trading analyst with 15+ years experience. Provide detailed, actionable analysis in valid JSON format. Be specific and practical in your recommendations.'\n        },\n        {\n          role: 'user',\n          content: prompt\n        }\n      ],\n      max_tokens: 600,\n      temperature: 0.4,\n    });\n\n    const response = completion.choices[0]?.message?.content;\n    if (response) {\n      try {\n        const parsed = JSON.parse(response);\n        return {\n          riskScore: parsed.riskScore || 5,\n          riskFactors: parsed.riskFactors || ['Unable to assess risk factors'],\n          recommendations: parsed.recommendations || ['Review setup manually'],\n          sentiment: parsed.sentiment || 'neutral',\n          technicalAnalysis: parsed.technicalAnalysis || 'Technical analysis unavailable',\n          entryStrategy: parsed.entryStrategy || 'Follow standard entry procedures',\n          exitStrategy: parsed.exitStrategy || 'Use provided stop loss and targets'\n        };\n      } catch (parseError) {\n        console.error('Error parsing AI risk assessment:', parseError);\n        console.log('Raw response:', response);\n      }\n    }\n\n    // Fallback response\n    return {\n      riskScore: 5,\n      riskFactors: ['Unable to complete AI risk analysis'],\n      recommendations: ['Review setup manually', 'Consider current market conditions'],\n      sentiment: 'neutral',\n      technicalAnalysis: 'AI analysis temporarily unavailable. Review technical indicators manually.',\n      entryStrategy: 'Follow the provided entry conditions and timing guidelines.',\n      exitStrategy: 'Use the calculated stop loss and take profit levels with proper position management.'\n    };\n  } catch (error) {\n    console.error('Error generating risk assessment:', error);\n    return {\n      riskScore: 5,\n      riskFactors: ['AI risk assessment temporarily unavailable'],\n      recommendations: ['Proceed with standard risk management'],\n      sentiment: 'neutral',\n      technicalAnalysis: 'AI analysis service temporarily unavailable.',\n      entryStrategy: 'Follow standard entry procedures.',\n      exitStrategy: 'Use provided risk management levels.'\n    };\n  }\n};\n\n// AI-powered personalized trading recommendations\nexport const generateTradingRecommendations = async (\n  scanResults: EnhancedScanResult[],\n  userPreferences?: {\n    riskTolerance: 'low' | 'medium' | 'high';\n    tradingStyle: 'conservative' | 'moderate' | 'aggressive';\n    accountSize: number;\n  }\n): Promise<{\n  topPicks: string[];\n  avoidList: string[];\n  marketOutlook: string;\n  actionItems: string[];\n}> => {\n  if (!isOpenAIEnabled()) {\n    return {\n      topPicks: ['AI recommendations disabled'],\n      avoidList: [],\n      marketOutlook: 'Enable AI features for personalized recommendations',\n      actionItems: ['Configure OpenAI integration']\n    };\n  }\n\n  try {\n    await enforceRateLimit();\n    \n    const model = await getLatestModel();\n    const topResults = scanResults.slice(0, 10);\n    \n    const prompt = `As a professional trading advisor, analyze these swing trading opportunities and provide personalized recommendations:\n\nUser Profile:\n- Risk Tolerance: ${userPreferences?.riskTolerance || 'medium'}\n- Trading Style: ${userPreferences?.tradingStyle || 'moderate'}\n- Account Size: $${userPreferences?.accountSize?.toLocaleString() || '100,000'}\n\nAvailable Opportunities:\n${topResults.map((result, i) => `\n${i + 1}. ${result.symbol} (Score: ${result.overallScore.toFixed(1)}/100)\n   - Strategy: ${result.bestStrategy || 'None'}\n   - Confidence: High/Medium/Low based on score\n`).join('')}\n\nProvide a JSON response with:\n{\n  \"topPicks\": [\"symbol1\", \"symbol2\", \"symbol3\"],\n  \"avoidList\": [\"symbol1\", \"symbol2\"],\n  \"marketOutlook\": \"brief market outlook\",\n  \"actionItems\": [\"action1\", \"action2\", \"action3\"]\n}\n\nFocus on risk-appropriate recommendations for the user's profile.`;\n\n    const completion = await openai.chat.completions.create({\n      model,\n      messages: [\n        {\n          role: 'system',\n          content: 'You are a professional trading advisor. Provide personalized, risk-appropriate recommendations in valid JSON format.'\n        },\n        {\n          role: 'user',\n          content: prompt\n        }\n      ],\n      max_tokens: 500,\n      temperature: 0.4,\n    });\n\n    const response = completion.choices[0]?.message?.content;\n    if (response) {\n      try {\n        return JSON.parse(response);\n      } catch (parseError) {\n        console.error('Error parsing AI recommendations:', parseError);\n      }\n    }\n    \n    // Fallback response\n    return {\n      topPicks: topResults.slice(0, 3).map(r => r.symbol),\n      avoidList: [],\n      marketOutlook: 'Mixed market conditions - proceed with caution',\n      actionItems: ['Review top-scoring setups', 'Monitor market conditions', 'Manage position sizes']\n    };\n  } catch (error) {\n    console.error('Error generating trading recommendations:', error);\n    return {\n      topPicks: [],\n      avoidList: [],\n      marketOutlook: 'AI recommendations temporarily unavailable',\n      actionItems: ['Use technical analysis for decision making']\n    };\n  }\n};\n\nexport default {\n  isOpenAIEnabled,\n  getLatestModel,\n  generateMarketCommentary,\n  generateRiskAssessment,\n  generateTradingRecommendations\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;;AAIA,8BAA8B;AAC9B,MAAM,mBAAmB,MAAM,4BAA4B;AAC3D,IAAI,kBAAkB;AAEtB,2BAA2B;AAC3B,MAAM,SAAS,IAAI,4MAAM,CAAC;IACxB,QAAQ,QAAQ,GAAG,CAAC,cAAc;AACpC;AAGO,MAAM,kBAAkB;IAC7B,OAAO,QAAQ,GAAG,CAAC,cAAc,KAAK,UAAU,CAAC,CAAC,QAAQ,GAAG,CAAC,cAAc;AAC9E;AAEA,uBAAuB;AACvB,MAAM,mBAAmB;IACvB,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,uBAAuB,MAAM;IAEnC,IAAI,uBAAuB,kBAAkB;QAC3C,MAAM,QAAQ,mBAAmB;QACjC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;IACnD;IAEA,kBAAkB,KAAK,GAAG;AAC5B;AAGO,MAAM,iBAAiB;IAC5B,IAAI;QACF,MAAM;QAEN,MAAM,SAAS,MAAM,OAAO,MAAM,CAAC,IAAI;QACvC,MAAM,YAAY,OAAO,IAAI,CAC1B,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,CAAC,UAAU,CAAC,SACpC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,OAAO,GAAG,EAAE,OAAO;QAEvC,oCAAoC;QACpC,MAAM,kBAAkB;YACtB;YACA;YACA;YACA;YACA;SACD;QAED,KAAK,MAAM,aAAa,gBAAiB;YACvC,MAAM,QAAQ,UAAU,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;YACnD,IAAI,OAAO;gBACT,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,MAAM,EAAE,EAAE;gBAC7C,OAAO,MAAM,EAAE;YACjB;QACF;QAEA,mCAAmC;QACnC,IAAI,UAAU,MAAM,GAAG,GAAG;YACxB,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,SAAS,CAAC,EAAE,CAAC,EAAE,EAAE;YAC7D,OAAO,SAAS,CAAC,EAAE,CAAC,EAAE;QACxB;QAEA,MAAM,IAAI,MAAM;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,UAAU,mBAAmB;IACtC;AACF;AAGO,MAAM,2BAA2B,OACtC,aACA;IAEA,IAAI,CAAC,mBAAmB;QACtB,OAAO;IACT;IAEA,IAAI;QACF,MAAM;QAEN,MAAM,QAAQ,MAAM;QACpB,MAAM,aAAa,YAAY,KAAK,CAAC,GAAG;QAExC,MAAM,SAAS,CAAC;;;QAGZ,EAAE,iBAAiB,SAAS,CAAC;gBACrB,EAAE,iBAAiB,WAAW,GAAG,SAAS,SAAS;qBAC9C,EAAE,iBAAiB,iBAAiB,GAAG,QAAQ,KAAK;;;AAGzE,EAAE,WAAW,GAAG,CAAC,CAAC,QAAQ,IAAM,CAAC;AACjC,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,MAAM,CAAC,SAAS,EAAE,OAAO,YAAY,CAAC,OAAO,CAAC,GAAG;oBAChD,EAAE,OAAO,YAAY,IAAI,OAAO;;;AAGpD,CAAC,EAAE,IAAI,CAAC,IAAI;;;;;;;sDAO0C,CAAC;QAEnD,MAAM,aAAa,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACtD;YACA,UAAU;gBACR;oBACE,MAAM;oBACN,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;aACD;YACD,YAAY;YACZ,aAAa;QACf;QAEA,OAAO,WAAW,OAAO,CAAC,EAAE,EAAE,SAAS,WAAW;IACpD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;IACT;AACF;AAGO,MAAM,yBAAyB,OACpC,OACA;IAUA,IAAI,CAAC,mBAAmB;QACtB,OAAO;YACL,WAAW;YACX,aAAa;gBAAC;aAAuB;YACrC,iBAAiB;gBAAC;aAAkD;YACpE,WAAW;YACX,mBAAmB;YACnB,eAAe;YACf,cAAc;QAChB;IACF;IAEA,IAAI;QACF,MAAM;QAEN,MAAM,QAAQ,MAAM;QAEpB,MAAM,aAAa,CAAC,CAAC,MAAM,OAAO,CAAC,EAAE,GAAG,MAAM,UAAU,IAAI,CAAC,MAAM,UAAU,GAAG,MAAM,QAAQ,CAAC,EAAE,OAAO,CAAC;QAEzG,MAAM,SAAS,CAAC;;;QAGZ,EAAE,MAAM,MAAM,CAAC;UACb,EAAE,MAAM,QAAQ,CAAC;YACf,EAAE,MAAM,UAAU,CAAC;cACjB,EAAE,MAAM,UAAU,CAAC;YACrB,EAAE,MAAM,QAAQ,CAAC;eACd,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC;mBACf,EAAE,WAAW;eACjB,EAAE,MAAM,YAAY,CAAC;cACtB,EAAE,MAAM,UAAU,CAAC;;;AAGjC,EAAE,MAAM,YAAY,EAAE,YAAY,KAAK,SAAS,kCAAkC;;QAE1E,EAAE,MAAM,YAAY,EAAE,UAAU,eAAe;;;;;;;;;;;;;;;;;;mCAkBpB,CAAC;QAEhC,MAAM,aAAa,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACtD;YACA,UAAU;gBACR;oBACE,MAAM;oBACN,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;aACD;YACD,YAAY;YACZ,aAAa;QACf;QAEA,MAAM,WAAW,WAAW,OAAO,CAAC,EAAE,EAAE,SAAS;QACjD,IAAI,UAAU;YACZ,IAAI;gBACF,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,OAAO;oBACL,WAAW,OAAO,SAAS,IAAI;oBAC/B,aAAa,OAAO,WAAW,IAAI;wBAAC;qBAAgC;oBACpE,iBAAiB,OAAO,eAAe,IAAI;wBAAC;qBAAwB;oBACpE,WAAW,OAAO,SAAS,IAAI;oBAC/B,mBAAmB,OAAO,iBAAiB,IAAI;oBAC/C,eAAe,OAAO,aAAa,IAAI;oBACvC,cAAc,OAAO,YAAY,IAAI;gBACvC;YACF,EAAE,OAAO,YAAY;gBACnB,QAAQ,KAAK,CAAC,qCAAqC;gBACnD,QAAQ,GAAG,CAAC,iBAAiB;YAC/B;QACF;QAEA,oBAAoB;QACpB,OAAO;YACL,WAAW;YACX,aAAa;gBAAC;aAAsC;YACpD,iBAAiB;gBAAC;gBAAyB;aAAqC;YAChF,WAAW;YACX,mBAAmB;YACnB,eAAe;YACf,cAAc;QAChB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;YACL,WAAW;YACX,aAAa;gBAAC;aAA6C;YAC3D,iBAAiB;gBAAC;aAAwC;YAC1D,WAAW;YACX,mBAAmB;YACnB,eAAe;YACf,cAAc;QAChB;IACF;AACF;AAGO,MAAM,iCAAiC,OAC5C,aACA;IAWA,IAAI,CAAC,mBAAmB;QACtB,OAAO;YACL,UAAU;gBAAC;aAA8B;YACzC,WAAW,EAAE;YACb,eAAe;YACf,aAAa;gBAAC;aAA+B;QAC/C;IACF;IAEA,IAAI;QACF,MAAM;QAEN,MAAM,QAAQ,MAAM;QACpB,MAAM,aAAa,YAAY,KAAK,CAAC,GAAG;QAExC,MAAM,SAAS,CAAC;;;kBAGF,EAAE,iBAAiB,iBAAiB,SAAS;iBAC9C,EAAE,iBAAiB,gBAAgB,WAAW;iBAC9C,EAAE,iBAAiB,aAAa,oBAAoB,UAAU;;;AAG/E,EAAE,WAAW,GAAG,CAAC,CAAC,QAAQ,IAAM,CAAC;AACjC,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,MAAM,CAAC,SAAS,EAAE,OAAO,YAAY,CAAC,OAAO,CAAC,GAAG;eACrD,EAAE,OAAO,YAAY,IAAI,OAAO;;AAE/C,CAAC,EAAE,IAAI,CAAC,IAAI;;;;;;;;;;iEAUqD,CAAC;QAE9D,MAAM,aAAa,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACtD;YACA,UAAU;gBACR;oBACE,MAAM;oBACN,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;aACD;YACD,YAAY;YACZ,aAAa;QACf;QAEA,MAAM,WAAW,WAAW,OAAO,CAAC,EAAE,EAAE,SAAS;QACjD,IAAI,UAAU;YACZ,IAAI;gBACF,OAAO,KAAK,KAAK,CAAC;YACpB,EAAE,OAAO,YAAY;gBACnB,QAAQ,KAAK,CAAC,qCAAqC;YACrD;QACF;QAEA,oBAAoB;QACpB,OAAO;YACL,UAAU,WAAW,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;YAClD,WAAW,EAAE;YACb,eAAe;YACf,aAAa;gBAAC;gBAA6B;gBAA6B;aAAwB;QAClG;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6CAA6C;QAC3D,OAAO;YACL,UAAU,EAAE;YACZ,WAAW,EAAE;YACb,eAAe;YACf,aAAa;gBAAC;aAA6C;QAC7D;IACF;AACF;uCAEe;IACb;IACA;IACA;IACA;IACA;AACF", "debugId": null}}, {"offset": {"line": 394, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/src/app/api/ai/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { \n  isOpenAIEnabled, \n  getLatestModel, \n  generateMarketCommentary, \n  generateRiskAssessment, \n  generateTradingRecommendations \n} from '@/lib/openai';\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const action = searchParams.get('action');\n\n    switch (action) {\n      case 'status':\n        return NextResponse.json({\n          enabled: isOpenAIEnabled(),\n          model: isOpenAIEnabled() ? await getLatestModel() : null,\n          features: {\n            marketCommentary: true,\n            riskAssessment: true,\n            tradingRecommendations: true\n          }\n        });\n\n      case 'model':\n        if (!isOpenAIEnabled()) {\n          return NextResponse.json({ error: 'OpenAI not enabled' }, { status: 400 });\n        }\n        \n        const model = await getLatestModel();\n        return NextResponse.json({ model });\n\n      default:\n        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });\n    }\n  } catch (error) {\n    console.error('AI API error:', error);\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    if (!isOpenAIEnabled()) {\n      return NextResponse.json({ error: 'OpenAI not enabled' }, { status: 400 });\n    }\n\n    const body = await request.json();\n    const { action, data } = body;\n\n    switch (action) {\n      case 'market-commentary':\n        const { scanResults, marketConditions } = data;\n        const commentary = await generateMarketCommentary(scanResults, marketConditions);\n        return NextResponse.json({ commentary });\n\n      case 'risk-assessment':\n        const { setup } = data;\n        const riskAssessment = await generateRiskAssessment(setup);\n        return NextResponse.json({ riskAssessment });\n\n      case 'trading-recommendations':\n        const { scanResults: results, userPreferences } = data;\n        const recommendations = await generateTradingRecommendations(results, userPreferences);\n        return NextResponse.json({ recommendations });\n\n      default:\n        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });\n    }\n  } catch (error) {\n    console.error('AI API POST error:', error);\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAQO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC;QAEhC,OAAQ;YACN,KAAK;gBACH,OAAO,yKAAY,CAAC,IAAI,CAAC;oBACvB,SAAS,IAAA,kKAAe;oBACxB,OAAO,IAAA,kKAAe,MAAK,MAAM,IAAA,iKAAc,MAAK;oBACpD,UAAU;wBACR,kBAAkB;wBAClB,gBAAgB;wBAChB,wBAAwB;oBAC1B;gBACF;YAEF,KAAK;gBACH,IAAI,CAAC,IAAA,kKAAe,KAAI;oBACtB,OAAO,yKAAY,CAAC,IAAI,CAAC;wBAAE,OAAO;oBAAqB,GAAG;wBAAE,QAAQ;oBAAI;gBAC1E;gBAEA,MAAM,QAAQ,MAAM,IAAA,iKAAc;gBAClC,OAAO,yKAAY,CAAC,IAAI,CAAC;oBAAE;gBAAM;YAEnC;gBACE,OAAO,yKAAY,CAAC,IAAI,CAAC;oBAAE,OAAO;gBAAiB,GAAG;oBAAE,QAAQ;gBAAI;QACxE;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,OAAO,yKAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,IAAI,CAAC,IAAA,kKAAe,KAAI;YACtB,OAAO,yKAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAqB,GAAG;gBAAE,QAAQ;YAAI;QAC1E;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;QAEzB,OAAQ;YACN,KAAK;gBACH,MAAM,EAAE,WAAW,EAAE,gBAAgB,EAAE,GAAG;gBAC1C,MAAM,aAAa,MAAM,IAAA,2KAAwB,EAAC,aAAa;gBAC/D,OAAO,yKAAY,CAAC,IAAI,CAAC;oBAAE;gBAAW;YAExC,KAAK;gBACH,MAAM,EAAE,KAAK,EAAE,GAAG;gBAClB,MAAM,iBAAiB,MAAM,IAAA,yKAAsB,EAAC;gBACpD,OAAO,yKAAY,CAAC,IAAI,CAAC;oBAAE;gBAAe;YAE5C,KAAK;gBACH,MAAM,EAAE,aAAa,OAAO,EAAE,eAAe,EAAE,GAAG;gBAClD,MAAM,kBAAkB,MAAM,IAAA,iLAA8B,EAAC,SAAS;gBACtE,OAAO,yKAAY,CAAC,IAAI,CAAC;oBAAE;gBAAgB;YAE7C;gBACE,OAAO,yKAAY,CAAC,IAAI,CAAC;oBAAE,OAAO;gBAAiB,GAAG;oBAAE,QAAQ;gBAAI;QACxE;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,yKAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}]}