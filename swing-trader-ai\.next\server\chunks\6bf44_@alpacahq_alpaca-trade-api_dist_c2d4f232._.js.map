{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/node_modules/%40alpacahq/alpaca-trade-api/dist/api.js"], "sourcesContent": ["\"use strict\";\nconst axios = require(\"axios\");\nconst joinUrl = require(\"urljoin\");\nfunction httpRequest(endpoint, queryParams, body, method) {\n    const { baseUrl, keyId, secretKey, apiVersion, oauth } = this.configuration;\n    const req = {\n        method: method || \"GET\",\n        url: joinUrl(baseUrl, apiVersion, endpoint),\n        params: queryParams || {},\n        headers: oauth !== \"\"\n            ? {\n                \"content-type\": method !== \"DELETE\" ? \"application/json\" : \"\",\n                Authorization: \"Bearer \" + oauth,\n            }\n            : {\n                \"content-type\": method !== \"DELETE\" ? \"application/json\" : \"\",\n                \"APCA-API-KEY-ID\": keyId,\n                \"APCA-API-SECRET-KEY\": secretKey,\n            },\n        data: body || undefined,\n    };\n    return axios(req);\n}\nfunction dataHttpRequest(endpoint, queryParams, body, method) {\n    const { dataBaseUrl, keyId, secretKey, oauth } = this.configuration;\n    const req = {\n        method: method || \"GET\",\n        url: joinUrl(dataBaseUrl, \"v1\", endpoint),\n        params: queryParams || {},\n        headers: oauth !== \"\"\n            ? {\n                \"content-type\": method !== \"DELETE\" ? \"application/json\" : \"\",\n                Authorization: \"Bearer \" + oauth,\n            }\n            : {\n                \"content-type\": method !== \"DELETE\" ? \"application/json\" : \"\",\n                \"APCA-API-KEY-ID\": keyId,\n                \"APCA-API-SECRET-KEY\": secretKey,\n            },\n        data: body || undefined,\n    };\n    return axios(req);\n}\nfunction sendRequest(f, endpoint, queryParams, body, method) {\n    return f(endpoint, queryParams, body, method).then((resp) => resp.data);\n}\nmodule.exports = {\n    httpRequest,\n    dataHttpRequest,\n    sendRequest,\n};\n"], "names": [], "mappings": "AACA,MAAM;AACN,MAAM;AACN,SAAS,YAAY,QAAQ,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM;IACpD,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,aAAa;IAC3E,MAAM,MAAM;QACR,QAAQ,UAAU;QAClB,KAAK,QAAQ,SAAS,YAAY;QAClC,QAAQ,eAAe,CAAC;QACxB,SAAS,UAAU,KACb;YACE,gBAAgB,WAAW,WAAW,qBAAqB;YAC3D,eAAe,YAAY;QAC/B,IACE;YACE,gBAAgB,WAAW,WAAW,qBAAqB;YAC3D,mBAAmB;YACnB,uBAAuB;QAC3B;QACJ,MAAM,QAAQ;IAClB;IACA,OAAO,MAAM;AACjB;AACA,SAAS,gBAAgB,QAAQ,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM;IACxD,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,aAAa;IACnE,MAAM,MAAM;QACR,QAAQ,UAAU;QAClB,KAAK,QAAQ,aAAa,MAAM;QAChC,QAAQ,eAAe,CAAC;QACxB,SAAS,UAAU,KACb;YACE,gBAAgB,WAAW,WAAW,qBAAqB;YAC3D,eAAe,YAAY;QAC/B,IACE;YACE,gBAAgB,WAAW,WAAW,qBAAqB;YAC3D,mBAAmB;YACnB,uBAAuB;QAC3B;QACJ,MAAM,QAAQ;IAClB;IACA,OAAO,MAAM;AACjB;AACA,SAAS,YAAY,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM;IACvD,OAAO,EAAE,UAAU,aAAa,MAAM,QAAQ,IAAI,CAAC,CAAC,OAAS,KAAK,IAAI;AAC1E;AACA,OAAO,OAAO,GAAG;IACb;IACA;IACA;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/node_modules/%40alpacahq/alpaca-trade-api/dist/resources/account.js"], "sourcesContent": ["\"use strict\";\nconst { omitBy, isNil } = require(\"lodash\");\nfunction get() {\n    return this.sendRequest(\"/account\");\n}\nfunction updateConfigs(configs) {\n    return this.sendRequest(\"/account/configurations\", null, configs, \"PATCH\");\n}\nfunction getConfigs() {\n    return this.sendRequest(\"/account/configurations\");\n}\nfunction getActivities({ activityTypes, until, after, direction, date, pageSize, pageToken, }) {\n    if (Array.isArray(activityTypes)) {\n        activityTypes = activityTypes.join(\",\");\n    }\n    const queryParams = omitBy({\n        activity_types: activityTypes,\n        until,\n        after,\n        direction,\n        date,\n        page_size: pageSize,\n        page_token: pageToken,\n    }, isNil);\n    return this.sendRequest(\"/account/activities\", queryParams);\n}\nfunction getPortfolioHistory({ date_start, date_end, period, timeframe, extended_hours, }) {\n    const queryParams = omitBy({\n        date_start,\n        date_end,\n        period,\n        timeframe,\n        extended_hours,\n    }, isNil);\n    return this.sendRequest(\"/account/portfolio/history\", queryParams);\n}\nmodule.exports = {\n    get,\n    getConfigs,\n    updateConfigs,\n    getActivities,\n    getPortfolioHistory,\n};\n"], "names": [], "mappings": "AACA,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE;AACvB,SAAS;IACL,OAAO,IAAI,CAAC,WAAW,CAAC;AAC5B;AACA,SAAS,cAAc,OAAO;IAC1B,OAAO,IAAI,CAAC,WAAW,CAAC,2BAA2B,MAAM,SAAS;AACtE;AACA,SAAS;IACL,OAAO,IAAI,CAAC,WAAW,CAAC;AAC5B;AACA,SAAS,cAAc,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAG;IACzF,IAAI,MAAM,OAAO,CAAC,gBAAgB;QAC9B,gBAAgB,cAAc,IAAI,CAAC;IACvC;IACA,MAAM,cAAc,OAAO;QACvB,gBAAgB;QAChB;QACA;QACA;QACA;QACA,WAAW;QACX,YAAY;IAChB,GAAG;IACH,OAAO,IAAI,CAAC,WAAW,CAAC,uBAAuB;AACnD;AACA,SAAS,oBAAoB,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,cAAc,EAAG;IACrF,MAAM,cAAc,OAAO;QACvB;QACA;QACA;QACA;QACA;IACJ,GAAG;IACH,OAAO,IAAI,CAAC,WAAW,CAAC,8BAA8B;AAC1D;AACA,OAAO,OAAO,GAAG;IACb;IACA;IACA;IACA;IACA;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/node_modules/%40alpacahq/alpaca-trade-api/dist/resources/position.js"], "sourcesContent": ["\"use strict\";\nfunction getAll() {\n    return this.sendRequest(\"/positions\");\n}\nfunction getOne(symbol) {\n    return this.sendRequest(\"/positions/\" + symbol);\n}\nfunction closeAll() {\n    return this.sendRequest(\"/positions\", null, null, \"DELETE\");\n}\nfunction closeOne(symbol) {\n    return this.sendRequest(\"/positions/\" + symbol, null, null, \"DELETE\");\n}\nmodule.exports = {\n    getAll,\n    getOne,\n    closeAll,\n    closeOne,\n};\n"], "names": [], "mappings": "AACA,SAAS;IACL,OAAO,IAAI,CAAC,WAAW,CAAC;AAC5B;AACA,SAAS,OAAO,MAAM;IAClB,OAAO,IAAI,CAAC,WAAW,CAAC,gBAAgB;AAC5C;AACA,SAAS;IACL,OAAO,IAAI,CAAC,WAAW,CAAC,cAAc,MAAM,MAAM;AACtD;AACA,SAAS,SAAS,MAAM;IACpB,OAAO,IAAI,CAAC,WAAW,CAAC,gBAAgB,QAAQ,MAAM,MAAM;AAChE;AACA,OAAO,OAAO,GAAG;IACb;IACA;IACA;IACA;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/node_modules/%40alpacahq/alpaca-trade-api/dist/utils/dateformat.js"], "sourcesContent": ["'use strict';\n// certain endpoints don't accept ISO dates,\n// so to allow the user to use regular JS date objects\n// with the api, we need to convert them to strings\nfunction toDateString(date) {\n    if (date == null || typeof date === 'string')\n        return date;\n    const year = date.getUTCFullYear();\n    const month = numPad(date.getUTCMonth() + 1);\n    const day = numPad(date.getUTCDate());\n    return `${year}-${month}-${day}`;\n}\nfunction numPad(num) {\n    if (num < 10) {\n        return '0' + num;\n    }\n    return num;\n}\nmodule.exports = {\n    toDateString\n};\n"], "names": [], "mappings": "AACA,4CAA4C;AAC5C,sDAAsD;AACtD,mDAAmD;AACnD,SAAS,aAAa,IAAI;IACtB,IAAI,QAAQ,QAAQ,OAAO,SAAS,UAChC,OAAO;IACX,MAAM,OAAO,KAAK,cAAc;IAChC,MAAM,QAAQ,OAAO,KAAK,WAAW,KAAK;IAC1C,MAAM,MAAM,OAAO,KAAK,UAAU;IAClC,OAAO,GAAG,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,KAAK;AACpC;AACA,SAAS,OAAO,GAAG;IACf,IAAI,MAAM,IAAI;QACV,OAAO,MAAM;IACjB;IACA,OAAO;AACX;AACA,OAAO,OAAO,GAAG;IACb;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/node_modules/%40alpacahq/alpaca-trade-api/dist/resources/calendar.js"], "sourcesContent": ["\"use strict\";\nconst { omitBy, isNil } = require(\"lodash\");\nconst { toDateString } = require(\"../utils/dateformat\");\nfunction get({ start, end } = {}) {\n    const queryParams = omitBy({\n        start: toDateString(start),\n        end: toDateString(end),\n    }, isNil);\n    return this.sendRequest(\"/calendar\", queryParams);\n}\nmodule.exports = {\n    get,\n};\n"], "names": [], "mappings": "AACA,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE;AACvB,MAAM,EAAE,YAAY,EAAE;AACtB,SAAS,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAC5B,MAAM,cAAc,OAAO;QACvB,OAAO,aAAa;QACpB,KAAK,aAAa;IACtB,GAAG;IACH,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa;AACzC;AACA,OAAO,OAAO,GAAG;IACb;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 161, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/node_modules/%40alpacahq/alpaca-trade-api/dist/resources/clock.js"], "sourcesContent": ["\"use strict\";\nfunction get() {\n    return this.sendRequest(\"/clock\");\n}\nmodule.exports = {\n    get,\n};\n"], "names": [], "mappings": "AACA,SAAS;IACL,OAAO,IAAI,CAAC,WAAW,CAAC;AAC5B;AACA,OAAO,OAAO,GAAG;IACb;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/node_modules/%40alpacahq/alpaca-trade-api/dist/resources/asset.js"], "sourcesContent": ["\"use strict\";\nfunction getAll(options = {}) {\n    return this.sendRequest(\"/assets\", options);\n}\nfunction getOne(symbol) {\n    return this.sendRequest(\"/assets/\" + symbol);\n}\nmodule.exports = {\n    getAll,\n    getOne,\n};\n"], "names": [], "mappings": "AACA,SAAS,OAAO,UAAU,CAAC,CAAC;IACxB,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW;AACvC;AACA,SAAS,OAAO,MAAM;IAClB,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa;AACzC;AACA,OAAO,OAAO,GAAG;IACb;IACA;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 185, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/node_modules/%40alpacahq/alpaca-trade-api/dist/resources/order.js"], "sourcesContent": ["\"use strict\";\nconst { omitBy, isNil } = require(\"lodash\");\nfunction getAll({ status, until, after, limit, direction, nested, symbols } = {}) {\n    const queryParams = omitBy({\n        status,\n        until,\n        after,\n        limit,\n        direction,\n        nested,\n        symbols\n    }, isNil);\n    return this.sendRequest(\"/orders\", queryParams);\n}\nfunction getOne(id) {\n    return this.sendRequest(\"/orders/\" + id);\n}\nfunction getByClientOrderId(clientOrderId) {\n    const queryParams = { client_order_id: clientOrderId };\n    return this.sendRequest(\"/orders:by_client_order_id\", queryParams);\n}\nfunction post(order) {\n    return this.sendRequest(\"/orders\", null, order, \"POST\");\n}\nfunction cancel(id) {\n    return this.sendRequest(\"/orders/\" + id, null, null, \"DELETE\");\n}\nfunction cancelAll() {\n    return this.sendRequest(\"/orders\", null, null, \"DELETE\");\n}\nfunction patchOrder(id, newOrder) {\n    return this.sendRequest(`/orders/${id}`, null, newOrder, \"PATCH\");\n}\nmodule.exports = {\n    getAll,\n    getOne,\n    getByClientOrderId,\n    post,\n    patchOrder,\n    cancel,\n    cancelAll,\n};\n"], "names": [], "mappings": "AACA,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE;AACvB,SAAS,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;IAC5E,MAAM,cAAc,OAAO;QACvB;QACA;QACA;QACA;QACA;QACA;QACA;IACJ,GAAG;IACH,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW;AACvC;AACA,SAAS,OAAO,EAAE;IACd,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa;AACzC;AACA,SAAS,mBAAmB,aAAa;IACrC,MAAM,cAAc;QAAE,iBAAiB;IAAc;IACrD,OAAO,IAAI,CAAC,WAAW,CAAC,8BAA8B;AAC1D;AACA,SAAS,KAAK,KAAK;IACf,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,MAAM,OAAO;AACpD;AACA,SAAS,OAAO,EAAE;IACd,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,IAAI,MAAM,MAAM;AACzD;AACA,SAAS;IACL,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,MAAM,MAAM;AACnD;AACA,SAAS,WAAW,EAAE,EAAE,QAAQ;IAC5B,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM,UAAU;AAC7D;AACA,OAAO,OAAO,GAAG;IACb;IACA;IACA;IACA;IACA;IACA;IACA;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 232, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/node_modules/%40alpacahq/alpaca-trade-api/dist/resources/watchlist.js"], "sourcesContent": ["\"use strict\";\n// todo: try this\n// const { omitBy, isNil } = require('lodash')\nfunction getAll() {\n    return this.sendRequest(\"/watchlists\");\n}\nfunction getOne(id) {\n    return this.sendRequest(`/watchlists/${id}`);\n}\nfunction addWatchlist(name, symbols = undefined) {\n    const body = { name: name, symbols: symbols };\n    return this.sendRequest(\"/watchlists\", null, body, \"POST\");\n}\nfunction addToWatchlist(id, symbol) {\n    const body = { symbol: symbol };\n    return this.sendRequest(`/watchlists/${id}`, null, body, \"POST\");\n}\nfunction updateWatchlist(id, newWatchList) {\n    return this.sendRequest(`/watchlists/${id}`, null, newWatchList, \"PUT\");\n}\nfunction deleteWatchlist(id) {\n    return this.sendRequest(`/watchlists/${id}`, null, null, \"DELETE\");\n}\nfunction deleteFromWatchlist(id, symbol) {\n    return this.sendRequest(`/watchlists/${id}/${symbol}`, null, null, \"DELETE\");\n}\nmodule.exports = {\n    getAll,\n    getOne,\n    addWatchlist,\n    addToWatchlist,\n    updateWatchlist,\n    deleteWatchlist,\n    deleteFromWatchlist,\n};\n"], "names": [], "mappings": "AACA,iBAAiB;AACjB,8CAA8C;AAC9C,SAAS;IACL,OAAO,IAAI,CAAC,WAAW,CAAC;AAC5B;AACA,SAAS,OAAO,EAAE;IACd,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,YAAY,EAAE,IAAI;AAC/C;AACA,SAAS,aAAa,IAAI,EAAE,UAAU,SAAS;IAC3C,MAAM,OAAO;QAAE,MAAM;QAAM,SAAS;IAAQ;IAC5C,OAAO,IAAI,CAAC,WAAW,CAAC,eAAe,MAAM,MAAM;AACvD;AACA,SAAS,eAAe,EAAE,EAAE,MAAM;IAC9B,MAAM,OAAO;QAAE,QAAQ;IAAO;IAC9B,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,YAAY,EAAE,IAAI,EAAE,MAAM,MAAM;AAC7D;AACA,SAAS,gBAAgB,EAAE,EAAE,YAAY;IACrC,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,YAAY,EAAE,IAAI,EAAE,MAAM,cAAc;AACrE;AACA,SAAS,gBAAgB,EAAE;IACvB,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,YAAY,EAAE,IAAI,EAAE,MAAM,MAAM;AAC7D;AACA,SAAS,oBAAoB,EAAE,EAAE,MAAM;IACnC,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,YAAY,EAAE,GAAG,CAAC,EAAE,QAAQ,EAAE,MAAM,MAAM;AACvE;AACA,OAAO,OAAO,GAAG;IACb;IACA;IACA;IACA;IACA;IACA;IACA;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 275, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/node_modules/%40alpacahq/alpaca-trade-api/dist/resources/datav2/entityv2.js"], "sourcesContent": ["\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeCorporateActions = exports.getCorporateActionsSize = exports.convertCorporateActions = exports.NewTimeframe = exports.TimeFrameUnit = exports.AlpacaNews = exports.AlpacaOptionSnapshotV1Beta1 = exports.AlpacaOptionQuoteV1Beta1 = exports.AlpacaOptionTradeV1Beta1 = exports.AlpacaOptionBarV1Beta1 = exports.AlpacaCryptoOrderbook = exports.AlpacaCryptoSnapshot = exports.AlpacaCryptoBar = exports.AlpacaCryptoQuote = exports.AlpacaCryptoTrade = exports.AlpacaCorrectionV2 = exports.AlpacaCancelErrorV2 = exports.AlpacaLuldV2 = exports.AlpacaStatusV2 = exports.AlpacaSnapshotV2 = exports.AlpacaBarV2 = exports.AlpacaQuoteV2 = exports.AlpacaTradeV2 = void 0;\nconst mapKeys_1 = __importDefault(require(\"lodash/mapKeys\"));\nconst mapValues_1 = __importDefault(require(\"lodash/mapValues\"));\nconst trade_mapping_v2 = {\n    S: \"Symbol\",\n    i: \"ID\",\n    x: \"Exchange\",\n    p: \"Price\",\n    s: \"Size\",\n    t: \"Timestamp\",\n    c: \"Conditions\",\n    z: \"Tape\",\n};\nconst quote_mapping_v2 = {\n    S: \"Symbol\",\n    bx: \"BidExchange\",\n    bp: \"BidPrice\",\n    bs: \"BidSize\",\n    ax: \"AskExchange\",\n    ap: \"AskPrice\",\n    as: \"AskSize\",\n    t: \"Timestamp\",\n    c: \"Conditions\",\n    z: \"Tape\",\n};\nconst bar_mapping_v2 = {\n    S: \"Symbol\",\n    o: \"OpenPrice\",\n    h: \"HighPrice\",\n    l: \"LowPrice\",\n    c: \"ClosePrice\",\n    v: \"Volume\",\n    t: \"Timestamp\",\n    vw: \"VWAP\",\n    n: \"TradeCount\",\n};\nconst snapshot_mapping_v2 = {\n    symbol: \"symbol\",\n    latestTrade: \"LatestTrade\",\n    latestQuote: \"LatestQuote\",\n    minuteBar: \"MinuteBar\",\n    dailyBar: \"DailyBar\",\n    prevDailyBar: \"PrevDailyBar\",\n};\nconst status_mapping_v2 = {\n    S: \"Symbol\",\n    sc: \"StatusCode\",\n    sm: \"StatusMessage\",\n    rc: \"ReasonCode\",\n    rm: \"ReasonMessage\",\n    t: \"Timestamp\",\n    z: \"Tape\",\n};\nconst luld_mapping_v2 = {\n    S: \"Symbol\",\n    u: \"LimitUpPrice\",\n    d: \"LimitDownPrice\",\n    i: \"Indicator\",\n    t: \"Timestamp\",\n    z: \"Tape\",\n};\nconst cancel_error_mapping_v2 = {\n    S: \"Symbol\",\n    i: \"ID\",\n    x: \"Exchange\",\n    p: \"Price\",\n    s: \"Size\",\n    a: \"CancelErrorAction\",\n    z: \"Tape\",\n    t: \"Timestamp\",\n};\nconst correction_mapping_v2 = {\n    S: \"Symbol\",\n    x: \"Exchange\",\n    oi: \"OriginalID\",\n    op: \"OriginalPrice\",\n    os: \"OriginalSize\",\n    oc: \"OriginalConditions\",\n    ci: \"CorrectedID\",\n    cp: \"CorrectedPrice\",\n    cs: \"CorrectedSize\",\n    cc: \"CorrectedConditions\",\n    z: \"Tape\",\n    t: \"Timestamp\",\n};\nconst crypto_trade_mapping = {\n    S: \"Symbol\",\n    t: \"Timestamp\",\n    x: \"Exchange\",\n    p: \"Price\",\n    s: \"Size\",\n    tks: \"TakerSide\",\n    i: \"ID\",\n};\nconst crypto_quote_mapping = {\n    t: \"Timestamp\",\n    bp: \"BidPrice\",\n    bs: \"BidSize\",\n    ap: \"AskPrice\",\n    as: \"AskSize\",\n};\nconst crypto_bar_mapping = {\n    t: \"Timestamp\",\n    o: \"Open\",\n    h: \"High\",\n    l: \"Low\",\n    c: \"Close\",\n    v: \"Volume\",\n    vw: \"VWAP\",\n    n: \"TradeCount\",\n};\nconst crypto_snapshot_mapping = {\n    latestTrade: \"LatestTrade\",\n    latestQuote: \"LatestQuote\",\n    minuteBar: \"MinuteBar\",\n    dailyBar: \"DailyBar\",\n    prevDailyBar: \"PrevDailyBar\",\n};\nconst crypto_orderbook_entry_mapping = {\n    p: \"Price\",\n    s: \"Size\",\n};\nconst crypto_orderbook_mapping = {\n    t: \"Timestamp\",\n    b: \"Bids\",\n    a: \"Asks\",\n};\nconst news_image_mapping = {\n    size: \"Size\",\n    url: \"URL\",\n};\nconst news_mapping = {\n    id: \"ID\",\n    author: \"Author\",\n    created_at: \"CreatedAt\",\n    updated_at: \"UpdatedAt\",\n    headline: \"Headline\",\n    summary: \"Summary\",\n    content: \"Content\",\n    images: \"Images\",\n    url: \"URL\",\n    symbols: \"Symbols\",\n    source: \"Source\",\n};\nconst option_bar_mapping = {\n    S: \"Symbol\",\n    o: \"Open\",\n    h: \"High\",\n    l: \"Low\",\n    c: \"Close\",\n    v: \"Volume\",\n    t: \"Timestamp\",\n    vw: \"VWAP\",\n    n: \"TradeCount\",\n};\nconst option_tarde_mapping = {\n    S: \"Symbol\",\n    x: \"Exchange\",\n    p: \"Price\",\n    s: \"Size\",\n    t: \"Timestamp\",\n    c: \"Condition\",\n};\nconst option_quote_mapping = {\n    S: \"Symbol\",\n    bx: \"BidExchange\",\n    bp: \"BidPrice\",\n    bs: \"BidSize\",\n    ax: \"AskExchange\",\n    ap: \"AskPrice\",\n    as: \"AskSize\",\n    t: \"Timestamp\",\n    c: \"Conditions\",\n    z: \"Tape\",\n};\nconst option_snapshot_mapping = {\n    symbol: \"symbol\",\n    latestTrade: \"LatestTrade\",\n    latestQuote: \"LatestQuote\",\n    impliedVolatility: \"ImpliedVolatility\",\n    greeks: \"Greeks\",\n};\nfunction AlpacaTradeV2(data) {\n    return aliasObjectKey(data, trade_mapping_v2);\n}\nexports.AlpacaTradeV2 = AlpacaTradeV2;\nfunction AlpacaQuoteV2(data) {\n    return aliasObjectKey(data, quote_mapping_v2);\n}\nexports.AlpacaQuoteV2 = AlpacaQuoteV2;\nfunction AlpacaBarV2(data) {\n    return aliasObjectKey(data, bar_mapping_v2);\n}\nexports.AlpacaBarV2 = AlpacaBarV2;\nfunction AlpacaSnapshotV2(data) {\n    const snapshot = aliasObjectKey(data, snapshot_mapping_v2);\n    return (0, mapValues_1.default)(snapshot, (value, key) => {\n        return convertSnapshotData(key, value, false);\n    });\n}\nexports.AlpacaSnapshotV2 = AlpacaSnapshotV2;\nfunction AlpacaStatusV2(data) {\n    return aliasObjectKey(data, status_mapping_v2);\n}\nexports.AlpacaStatusV2 = AlpacaStatusV2;\nfunction AlpacaLuldV2(data) {\n    return aliasObjectKey(data, luld_mapping_v2);\n}\nexports.AlpacaLuldV2 = AlpacaLuldV2;\nfunction AlpacaCancelErrorV2(data) {\n    return aliasObjectKey(data, cancel_error_mapping_v2);\n}\nexports.AlpacaCancelErrorV2 = AlpacaCancelErrorV2;\nfunction AlpacaCorrectionV2(data) {\n    return aliasObjectKey(data, correction_mapping_v2);\n}\nexports.AlpacaCorrectionV2 = AlpacaCorrectionV2;\nfunction AlpacaCryptoTrade(data) {\n    return aliasObjectKey(data, crypto_trade_mapping);\n}\nexports.AlpacaCryptoTrade = AlpacaCryptoTrade;\nfunction AlpacaCryptoQuote(data) {\n    return aliasObjectKey(data, crypto_quote_mapping);\n}\nexports.AlpacaCryptoQuote = AlpacaCryptoQuote;\nfunction AlpacaCryptoBar(data) {\n    return aliasObjectKey(data, crypto_bar_mapping);\n}\nexports.AlpacaCryptoBar = AlpacaCryptoBar;\nfunction AlpacaCryptoSnapshot(data) {\n    const snapshot = aliasObjectKey(data, crypto_snapshot_mapping);\n    return (0, mapValues_1.default)(snapshot, (value, key) => {\n        return convertSnapshotData(key, value, true);\n    });\n}\nexports.AlpacaCryptoSnapshot = AlpacaCryptoSnapshot;\nfunction AlpacaCryptoOrderbook(data) {\n    const mapFn = (entries) => entries.map((entry) => aliasObjectKey(entry, crypto_orderbook_entry_mapping));\n    const orderbook = aliasObjectKey(data, crypto_orderbook_mapping);\n    return Object.assign(Object.assign({}, orderbook), { Bids: mapFn(orderbook.Bids), Asks: mapFn(orderbook.Asks) });\n}\nexports.AlpacaCryptoOrderbook = AlpacaCryptoOrderbook;\nfunction AlpacaOptionBarV1Beta1(data) {\n    return aliasObjectKey(data, option_bar_mapping);\n}\nexports.AlpacaOptionBarV1Beta1 = AlpacaOptionBarV1Beta1;\nfunction AlpacaOptionTradeV1Beta1(data) {\n    return aliasObjectKey(data, option_tarde_mapping);\n}\nexports.AlpacaOptionTradeV1Beta1 = AlpacaOptionTradeV1Beta1;\nfunction AlpacaOptionQuoteV1Beta1(data) {\n    return aliasObjectKey(data, option_quote_mapping);\n}\nexports.AlpacaOptionQuoteV1Beta1 = AlpacaOptionQuoteV1Beta1;\nfunction AlpacaOptionSnapshotV1Beta1(data) {\n    const snapshot = aliasObjectKey(data, option_snapshot_mapping);\n    return (0, mapValues_1.default)(snapshot, (value, key) => {\n        return convertOptionSnapshotData(key, value);\n    });\n}\nexports.AlpacaOptionSnapshotV1Beta1 = AlpacaOptionSnapshotV1Beta1;\nfunction aliasObjectKey(data, mapping) {\n    return (0, mapKeys_1.default)(data, (_value, key) => {\n        return Object.hasOwn(mapping, key) ? mapping[key] : key;\n    });\n}\nfunction convertSnapshotData(key, data, isCrypto) {\n    switch (key) {\n        case \"LatestTrade\":\n            return isCrypto ? AlpacaCryptoTrade(data) : AlpacaTradeV2(data);\n        case \"LatestQuote\":\n            return isCrypto ? AlpacaCryptoQuote(data) : AlpacaQuoteV2(data);\n        case \"MinuteBar\":\n        case \"DailyBar\":\n        case \"PrevDailyBar\":\n            return isCrypto ? AlpacaCryptoBar(data) : AlpacaBarV2(data);\n        default:\n            return data;\n    }\n}\nfunction convertOptionSnapshotData(key, data) {\n    switch (key) {\n        case \"LatestTrade\":\n            return AlpacaOptionTradeV1Beta1(data);\n        case \"LatestQuote\":\n            return AlpacaOptionQuoteV1Beta1(data);\n        default:\n            return data;\n    }\n}\nfunction AlpacaNews(data) {\n    const mappedNews = aliasObjectKey(data, news_mapping);\n    if (mappedNews.Images) {\n        mappedNews.Images.forEach((element) => {\n            return aliasObjectKey(element, news_image_mapping);\n        });\n    }\n    return mappedNews;\n}\nexports.AlpacaNews = AlpacaNews;\nvar TimeFrameUnit;\n(function (TimeFrameUnit) {\n    TimeFrameUnit[\"MIN\"] = \"Min\";\n    TimeFrameUnit[\"HOUR\"] = \"Hour\";\n    TimeFrameUnit[\"DAY\"] = \"Day\";\n    TimeFrameUnit[\"WEEK\"] = \"Week\";\n    TimeFrameUnit[\"MONTH\"] = \"Month\";\n})(TimeFrameUnit || (exports.TimeFrameUnit = TimeFrameUnit = {}));\nfunction NewTimeframe(amount, unit) {\n    if (amount <= 0) {\n        throw new Error(\"amount must be a positive integer value\");\n    }\n    if (unit == TimeFrameUnit.MIN && amount > 59) {\n        throw new Error(\"minute timeframe can only be used with amount between 1-59\");\n    }\n    if (unit == TimeFrameUnit.HOUR && amount > 23) {\n        throw new Error(\"hour timeframe can only be used with amounts 1-23\");\n    }\n    if ((unit == TimeFrameUnit.DAY || unit == TimeFrameUnit.WEEK) && amount != 1) {\n        throw new Error(\"day and week timeframes can only be used with amount 1\");\n    }\n    if (unit == TimeFrameUnit.MONTH && ![1, 2, 3, 6, 12].includes(amount)) {\n        throw new Error(\"month timeframe can only be used with amount 1, 2, 3, 6 and 12\");\n    }\n    return `${amount}${unit}`;\n}\nexports.NewTimeframe = NewTimeframe;\nconst cash_dividend_mapping = {\n    ex_date: \"ExDate\",\n    foreign: \"Foreign\",\n    payable_date: \"PayableDate\",\n    process_date: \"ProcessDate\",\n    rate: \"Rate\",\n    record_date: \"RecordDate\",\n    special: \"Special\",\n    symbol: \"Symbol\",\n};\nconst reverse_split_mapping = {\n    ex_date: \"ExDate\",\n    new_rate: \"NewRate\",\n    old_rate: \"OldRate\",\n    payable_date: \"PayableDate\",\n    process_date: \"ProcessDate\",\n    record_date: \"RecordDate\",\n    symbol: \"Symbol\",\n};\nconst forward_split_mapping = {\n    due_bill_redemption_date: \"DueBillRedemptionDate\",\n    ex_date: \"ExDate\",\n    new_rate: \"NewRate\",\n    old_rate: \"OldRate\",\n    payable_date: \"PayableDate\",\n    process_date: \"ProcessDate\",\n    record_date: \"RecordDate\",\n    symbol: \"Symbol\",\n};\nconst unit_split_mapping = {\n    alternate_rate: \"AlternateRate\",\n    alternate_symbol: \"AlternateSymbol\",\n    effective_date: \"EffectiveDate\",\n    new_rate: \"NewRate\",\n    new_symbol: \"NewSymbol\",\n    old_rate: \"OldRate\",\n    old_symbol: \"OldSymbol\",\n    process_date: \"ProcessDate\",\n};\nconst cash_merger_mapping = {\n    acquiree_symbol: \"AcquireeSymbol\",\n    acquirer_symbol: \"AcquirerSymbol\",\n    effective_date: \"EffectiveDate\",\n    process_date: \"ProcessDate\",\n    rate: \"Rate\",\n};\nconst stock_merger_mapping = {\n    acquiree_rate: \"AcquireeRate\",\n    acquiree_symbol: \"AcquireeSymbol\",\n    acquirer_rate: \"AcquirerRate\",\n    acquirer_symbol: \"AcquirerSymbol\",\n    effective_date: \"EffectiveDate\",\n    payable_date: \"PayableDate\",\n    process_date: \"ProcessDate\",\n};\nconst stock_and_cash_merger_mapping = {\n    stock_merger_mapping,\n    cash_rate: \"CashRate\",\n};\nconst stock_dividends_mapping = {\n    ex_date: \"ExDate\",\n    payable_date: \"PayableDate\",\n    process_date: \"ProcessDate\",\n    rate: \"Rate\",\n    record_date: \"RecordDate\",\n    symbol: \"Symbol\",\n};\nconst redemption_mapping = {\n    payable_date: \"PayableDate\",\n    process_date: \"ProcessDate\",\n    rate: \"Rate\",\n    symbol: \"Symbol\",\n};\nconst spin_off_mapping = {\n    ex_date: \"ExDate\",\n    new_rate: \"NewRate\",\n    new_symbol: \"NewSymbol\",\n    process_date: \"ProcessDate\",\n    record_date: \"RecordDate\",\n    source_rate: \"Rate\",\n    source_symbol: \"SourceSymbol\",\n};\nconst name_change_mapping = {\n    new_symbol: \"NewSymbol\",\n    old_symbol: \"OldSymbol\",\n    process_date: \"ProcessDate\",\n};\nconst worthless_removal_mapping = {\n    symbol: \"Symbol\",\n    process_date: \"ProcessDate\",\n};\nconst rights_distribution_mapping = {\n    source_symbol: \"SourceSymbol\",\n    new_symbol: \"NewSymbol\",\n    rate: \"Rate\",\n    process_date: \"ProcessDate\",\n    ex_date: \"ExDate\",\n    payable_date: \"PayableDate\",\n    record_date: \"RecordDate\",\n    expiration_date: \"ExpirationDate\",\n};\nfunction convertCorporateActions(data) {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o;\n    let cas = {};\n    if (((_a = data.cash_dividends) === null || _a === void 0 ? void 0 : _a.length) > 0) {\n        cas.CashDividends = cas.CashDividends ? cas.CashDividends : Array();\n        data.cash_dividends.forEach((cd) => {\n            cas.CashDividends.push(aliasObjectKey(cd, cash_dividend_mapping));\n        });\n    }\n    if (((_b = data.reverse_splits) === null || _b === void 0 ? void 0 : _b.length) > 0) {\n        cas.ReverseSplits = cas.ReverseSplits ? cas.ReverseSplits : Array();\n        data.reverse_splits.forEach((rs) => {\n            cas.ReverseSplits.push(aliasObjectKey(rs, reverse_split_mapping));\n        });\n    }\n    if (((_c = data.forward_splits) === null || _c === void 0 ? void 0 : _c.length) > 0) {\n        cas.ForwardSplits = cas.ForwardSplits ? cas.ForwardSplits : Array();\n        data.forward_splits.forEach((fs) => {\n            cas.ForwardSplits.push(aliasObjectKey(fs, forward_split_mapping));\n        });\n    }\n    if (((_d = data.unit_splits) === null || _d === void 0 ? void 0 : _d.length) > 0) {\n        cas.UnitSplits = cas.UnitSplits ? cas.UnitSplits : Array();\n        data.unit_splits.forEach((fs) => {\n            cas.UnitSplits.push(aliasObjectKey(fs, unit_split_mapping));\n        });\n    }\n    if (((_e = data.cash_mergers) === null || _e === void 0 ? void 0 : _e.length) > 0) {\n        cas.CashMergers = cas.CashMergers ? cas.CashMergers : Array();\n        data.cash_mergers.forEach((cm) => {\n            cas.CashMergers.push(aliasObjectKey(cm, cash_merger_mapping));\n        });\n    }\n    if (((_f = data.stock_mergers) === null || _f === void 0 ? void 0 : _f.length) > 0) {\n        cas.StockMergers = cas.StockMergers ? cas.StockMergers : Array();\n        data.stock_mergers.forEach((sm) => {\n            cas.StockMergers.push(aliasObjectKey(sm, stock_merger_mapping));\n        });\n    }\n    if (((_g = data.stock_and_cash_mergers) === null || _g === void 0 ? void 0 : _g.length) > 0) {\n        cas.StockAndCashMerger = cas.StockAndCashMerger\n            ? cas.StockAndCashMerger\n            : Array();\n        data.stock_and_cash_mergers.forEach((scm) => {\n            cas.StockAndCashMerger.push(aliasObjectKey(scm, stock_and_cash_merger_mapping));\n        });\n    }\n    if (((_h = data.stock_dividends) === null || _h === void 0 ? void 0 : _h.length) > 0) {\n        cas.StockDividends = cas.StockDividends\n            ? cas.StockDividends\n            : Array();\n        data.stock_dividends.forEach((sd) => {\n            cas.StockDividends.push(aliasObjectKey(sd, stock_dividends_mapping));\n        });\n    }\n    if (((_j = data.redemptions) === null || _j === void 0 ? void 0 : _j.length) > 0) {\n        cas.Redemptions = cas.Redemptions ? cas.Redemptions : Array();\n        data.redemptions.forEach((r) => {\n            cas.Redemptions.push(aliasObjectKey(r, redemption_mapping));\n        });\n    }\n    if (((_k = data.spin_offs) === null || _k === void 0 ? void 0 : _k.length) > 0) {\n        cas.SpinOffs = cas.SpinOffs ? cas.SpinOffs : Array();\n        data.spin_offs.forEach((so) => {\n            cas.SpinOffs.push(aliasObjectKey(so, spin_off_mapping));\n        });\n    }\n    if (((_l = data.name_changes) === null || _l === void 0 ? void 0 : _l.length) > 0) {\n        cas.NameChanges = cas.NameChanges ? cas.NameChanges : Array();\n        data.name_changes.forEach((nc) => {\n            cas.NameChanges.push(aliasObjectKey(nc, name_change_mapping));\n        });\n    }\n    if (((_m = data.worthless_removals) === null || _m === void 0 ? void 0 : _m.length) > 0) {\n        cas.WorthlessRemovals = cas.WorthlessRemovals\n            ? cas.WorthlessRemovals\n            : Array();\n        data.worthless_removals.forEach((wr) => {\n            cas.WorthlessRemovals.push(aliasObjectKey(wr, worthless_removal_mapping));\n        });\n    }\n    if (((_o = data.rights_distributions) === null || _o === void 0 ? void 0 : _o.length) > 0) {\n        cas.RightsDistributions = cas.RightsDistributions\n            ? cas.RightsDistributions\n            : Array();\n        data.rights_distributions.forEach((rd) => {\n            cas.RightsDistributions.push(aliasObjectKey(rd, rights_distribution_mapping));\n        });\n    }\n    return cas;\n}\nexports.convertCorporateActions = convertCorporateActions;\nfunction getCorporateActionsSize(cas) {\n    let sum = 0;\n    for (const key in cas) {\n        sum += cas[key]\n            ? cas[key].length\n            : 0;\n    }\n    return sum;\n}\nexports.getCorporateActionsSize = getCorporateActionsSize;\nfunction mergeCorporateActions(ca1, ca2) {\n    return {\n        CashDividends: (ca1.CashDividends || []).concat(ca2.CashDividends || []),\n        ReverseSplits: (ca1.ReverseSplits || []).concat(ca2.ReverseSplits || []),\n        ForwardSplits: (ca1.ForwardSplits || []).concat(ca2.ForwardSplits || []),\n        UnitSplits: (ca1.UnitSplits || []).concat(ca2.UnitSplits || []),\n        CashMergers: (ca1.CashMergers || []).concat(ca2.CashMergers || []),\n        StockMergers: (ca1.StockMergers || []).concat(ca2.StockMergers || []),\n        StockAndCashMerger: (ca1.StockAndCashMerger || []).concat(ca2.StockAndCashMerger || []),\n        StockDividends: (ca1.StockDividends || []).concat(ca2.StockDividends || []),\n        Redemptions: (ca1.Redemptions || []).concat(ca2.Redemptions || []),\n        SpinOffs: (ca1.SpinOffs || []).concat(ca2.SpinOffs || []),\n        NameChanges: (ca1.NameChanges || []).concat(ca2.NameChanges || []),\n        WorthlessRemovals: (ca1.WorthlessRemovals || []).concat(ca2.WorthlessRemovals || []),\n        RightsDistributions: (ca1.RightsDistributions || []).concat(ca2.RightsDistributions || []),\n    };\n}\nexports.mergeCorporateActions = mergeCorporateActions;\n"], "names": [], "mappings": "AACA,IAAI,kBAAkB,4DAAS,yDAAK,eAAe,IAAK,SAAU,GAAG;IACjE,OAAO,AAAC,OAAO,IAAI,UAAU,GAAI,MAAM;QAAE,WAAW;IAAI;AAC5D;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,qBAAqB,GAAG,QAAQ,uBAAuB,GAAG,QAAQ,uBAAuB,GAAG,QAAQ,YAAY,GAAG,QAAQ,aAAa,GAAG,QAAQ,UAAU,GAAG,QAAQ,2BAA2B,GAAG,QAAQ,wBAAwB,GAAG,QAAQ,wBAAwB,GAAG,QAAQ,sBAAsB,GAAG,QAAQ,qBAAqB,GAAG,QAAQ,oBAAoB,GAAG,QAAQ,eAAe,GAAG,QAAQ,iBAAiB,GAAG,QAAQ,iBAAiB,GAAG,QAAQ,kBAAkB,GAAG,QAAQ,mBAAmB,GAAG,QAAQ,YAAY,GAAG,QAAQ,cAAc,GAAG,QAAQ,gBAAgB,GAAG,QAAQ,WAAW,GAAG,QAAQ,aAAa,GAAG,QAAQ,aAAa,GAAG,KAAK;AACvpB,MAAM,YAAY;AAClB,MAAM,cAAc;AACpB,MAAM,mBAAmB;IACrB,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;AACP;AACA,MAAM,mBAAmB;IACrB,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,GAAG;IACH,GAAG;IACH,GAAG;AACP;AACA,MAAM,iBAAiB;IACnB,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,IAAI;IACJ,GAAG;AACP;AACA,MAAM,sBAAsB;IACxB,QAAQ;IACR,aAAa;IACb,aAAa;IACb,WAAW;IACX,UAAU;IACV,cAAc;AAClB;AACA,MAAM,oBAAoB;IACtB,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,GAAG;IACH,GAAG;AACP;AACA,MAAM,kBAAkB;IACpB,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;AACP;AACA,MAAM,0BAA0B;IAC5B,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;AACP;AACA,MAAM,wBAAwB;IAC1B,GAAG;IACH,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,GAAG;IACH,GAAG;AACP;AACA,MAAM,uBAAuB;IACzB,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,KAAK;IACL,GAAG;AACP;AACA,MAAM,uBAAuB;IACzB,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACR;AACA,MAAM,qBAAqB;IACvB,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,IAAI;IACJ,GAAG;AACP;AACA,MAAM,0BAA0B;IAC5B,aAAa;IACb,aAAa;IACb,WAAW;IACX,UAAU;IACV,cAAc;AAClB;AACA,MAAM,iCAAiC;IACnC,GAAG;IACH,GAAG;AACP;AACA,MAAM,2BAA2B;IAC7B,GAAG;IACH,GAAG;IACH,GAAG;AACP;AACA,MAAM,qBAAqB;IACvB,MAAM;IACN,KAAK;AACT;AACA,MAAM,eAAe;IACjB,IAAI;IACJ,QAAQ;IACR,YAAY;IACZ,YAAY;IACZ,UAAU;IACV,SAAS;IACT,SAAS;IACT,QAAQ;IACR,KAAK;IACL,SAAS;IACT,QAAQ;AACZ;AACA,MAAM,qBAAqB;IACvB,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,IAAI;IACJ,GAAG;AACP;AACA,MAAM,uBAAuB;IACzB,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;AACP;AACA,MAAM,uBAAuB;IACzB,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,GAAG;IACH,GAAG;IACH,GAAG;AACP;AACA,MAAM,0BAA0B;IAC5B,QAAQ;IACR,aAAa;IACb,aAAa;IACb,mBAAmB;IACnB,QAAQ;AACZ;AACA,SAAS,cAAc,IAAI;IACvB,OAAO,eAAe,MAAM;AAChC;AACA,QAAQ,aAAa,GAAG;AACxB,SAAS,cAAc,IAAI;IACvB,OAAO,eAAe,MAAM;AAChC;AACA,QAAQ,aAAa,GAAG;AACxB,SAAS,YAAY,IAAI;IACrB,OAAO,eAAe,MAAM;AAChC;AACA,QAAQ,WAAW,GAAG;AACtB,SAAS,iBAAiB,IAAI;IAC1B,MAAM,WAAW,eAAe,MAAM;IACtC,OAAO,CAAC,GAAG,YAAY,OAAO,EAAE,UAAU,CAAC,OAAO;QAC9C,OAAO,oBAAoB,KAAK,OAAO;IAC3C;AACJ;AACA,QAAQ,gBAAgB,GAAG;AAC3B,SAAS,eAAe,IAAI;IACxB,OAAO,eAAe,MAAM;AAChC;AACA,QAAQ,cAAc,GAAG;AACzB,SAAS,aAAa,IAAI;IACtB,OAAO,eAAe,MAAM;AAChC;AACA,QAAQ,YAAY,GAAG;AACvB,SAAS,oBAAoB,IAAI;IAC7B,OAAO,eAAe,MAAM;AAChC;AACA,QAAQ,mBAAmB,GAAG;AAC9B,SAAS,mBAAmB,IAAI;IAC5B,OAAO,eAAe,MAAM;AAChC;AACA,QAAQ,kBAAkB,GAAG;AAC7B,SAAS,kBAAkB,IAAI;IAC3B,OAAO,eAAe,MAAM;AAChC;AACA,QAAQ,iBAAiB,GAAG;AAC5B,SAAS,kBAAkB,IAAI;IAC3B,OAAO,eAAe,MAAM;AAChC;AACA,QAAQ,iBAAiB,GAAG;AAC5B,SAAS,gBAAgB,IAAI;IACzB,OAAO,eAAe,MAAM;AAChC;AACA,QAAQ,eAAe,GAAG;AAC1B,SAAS,qBAAqB,IAAI;IAC9B,MAAM,WAAW,eAAe,MAAM;IACtC,OAAO,CAAC,GAAG,YAAY,OAAO,EAAE,UAAU,CAAC,OAAO;QAC9C,OAAO,oBAAoB,KAAK,OAAO;IAC3C;AACJ;AACA,QAAQ,oBAAoB,GAAG;AAC/B,SAAS,sBAAsB,IAAI;IAC/B,MAAM,QAAQ,CAAC,UAAY,QAAQ,GAAG,CAAC,CAAC,QAAU,eAAe,OAAO;IACxE,MAAM,YAAY,eAAe,MAAM;IACvC,OAAO,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,YAAY;QAAE,MAAM,MAAM,UAAU,IAAI;QAAG,MAAM,MAAM,UAAU,IAAI;IAAE;AAClH;AACA,QAAQ,qBAAqB,GAAG;AAChC,SAAS,uBAAuB,IAAI;IAChC,OAAO,eAAe,MAAM;AAChC;AACA,QAAQ,sBAAsB,GAAG;AACjC,SAAS,yBAAyB,IAAI;IAClC,OAAO,eAAe,MAAM;AAChC;AACA,QAAQ,wBAAwB,GAAG;AACnC,SAAS,yBAAyB,IAAI;IAClC,OAAO,eAAe,MAAM;AAChC;AACA,QAAQ,wBAAwB,GAAG;AACnC,SAAS,4BAA4B,IAAI;IACrC,MAAM,WAAW,eAAe,MAAM;IACtC,OAAO,CAAC,GAAG,YAAY,OAAO,EAAE,UAAU,CAAC,OAAO;QAC9C,OAAO,0BAA0B,KAAK;IAC1C;AACJ;AACA,QAAQ,2BAA2B,GAAG;AACtC,SAAS,eAAe,IAAI,EAAE,OAAO;IACjC,OAAO,CAAC,GAAG,UAAU,OAAO,EAAE,MAAM,CAAC,QAAQ;QACzC,OAAO,OAAO,MAAM,CAAC,SAAS,OAAO,OAAO,CAAC,IAAI,GAAG;IACxD;AACJ;AACA,SAAS,oBAAoB,GAAG,EAAE,IAAI,EAAE,QAAQ;IAC5C,OAAQ;QACJ,KAAK;YACD,OAAO,WAAW,kBAAkB,QAAQ,cAAc;QAC9D,KAAK;YACD,OAAO,WAAW,kBAAkB,QAAQ,cAAc;QAC9D,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO,WAAW,gBAAgB,QAAQ,YAAY;QAC1D;YACI,OAAO;IACf;AACJ;AACA,SAAS,0BAA0B,GAAG,EAAE,IAAI;IACxC,OAAQ;QACJ,KAAK;YACD,OAAO,yBAAyB;QACpC,KAAK;YACD,OAAO,yBAAyB;QACpC;YACI,OAAO;IACf;AACJ;AACA,SAAS,WAAW,IAAI;IACpB,MAAM,aAAa,eAAe,MAAM;IACxC,IAAI,WAAW,MAAM,EAAE;QACnB,WAAW,MAAM,CAAC,OAAO,CAAC,CAAC;YACvB,OAAO,eAAe,SAAS;QACnC;IACJ;IACA,OAAO;AACX;AACA,QAAQ,UAAU,GAAG;AACrB,IAAI;AACJ,CAAC,SAAU,aAAa;IACpB,aAAa,CAAC,MAAM,GAAG;IACvB,aAAa,CAAC,OAAO,GAAG;IACxB,aAAa,CAAC,MAAM,GAAG;IACvB,aAAa,CAAC,OAAO,GAAG;IACxB,aAAa,CAAC,QAAQ,GAAG;AAC7B,CAAC,EAAE,iBAAiB,CAAC,QAAQ,aAAa,GAAG,gBAAgB,CAAC,CAAC;AAC/D,SAAS,aAAa,MAAM,EAAE,IAAI;IAC9B,IAAI,UAAU,GAAG;QACb,MAAM,IAAI,MAAM;IACpB;IACA,IAAI,QAAQ,cAAc,GAAG,IAAI,SAAS,IAAI;QAC1C,MAAM,IAAI,MAAM;IACpB;IACA,IAAI,QAAQ,cAAc,IAAI,IAAI,SAAS,IAAI;QAC3C,MAAM,IAAI,MAAM;IACpB;IACA,IAAI,CAAC,QAAQ,cAAc,GAAG,IAAI,QAAQ,cAAc,IAAI,KAAK,UAAU,GAAG;QAC1E,MAAM,IAAI,MAAM;IACpB;IACA,IAAI,QAAQ,cAAc,KAAK,IAAI,CAAC;QAAC;QAAG;QAAG;QAAG;QAAG;KAAG,CAAC,QAAQ,CAAC,SAAS;QACnE,MAAM,IAAI,MAAM;IACpB;IACA,OAAO,GAAG,SAAS,MAAM;AAC7B;AACA,QAAQ,YAAY,GAAG;AACvB,MAAM,wBAAwB;IAC1B,SAAS;IACT,SAAS;IACT,cAAc;IACd,cAAc;IACd,MAAM;IACN,aAAa;IACb,SAAS;IACT,QAAQ;AACZ;AACA,MAAM,wBAAwB;IAC1B,SAAS;IACT,UAAU;IACV,UAAU;IACV,cAAc;IACd,cAAc;IACd,aAAa;IACb,QAAQ;AACZ;AACA,MAAM,wBAAwB;IAC1B,0BAA0B;IAC1B,SAAS;IACT,UAAU;IACV,UAAU;IACV,cAAc;IACd,cAAc;IACd,aAAa;IACb,QAAQ;AACZ;AACA,MAAM,qBAAqB;IACvB,gBAAgB;IAChB,kBAAkB;IAClB,gBAAgB;IAChB,UAAU;IACV,YAAY;IACZ,UAAU;IACV,YAAY;IACZ,cAAc;AAClB;AACA,MAAM,sBAAsB;IACxB,iBAAiB;IACjB,iBAAiB;IACjB,gBAAgB;IAChB,cAAc;IACd,MAAM;AACV;AACA,MAAM,uBAAuB;IACzB,eAAe;IACf,iBAAiB;IACjB,eAAe;IACf,iBAAiB;IACjB,gBAAgB;IAChB,cAAc;IACd,cAAc;AAClB;AACA,MAAM,gCAAgC;IAClC;IACA,WAAW;AACf;AACA,MAAM,0BAA0B;IAC5B,SAAS;IACT,cAAc;IACd,cAAc;IACd,MAAM;IACN,aAAa;IACb,QAAQ;AACZ;AACA,MAAM,qBAAqB;IACvB,cAAc;IACd,cAAc;IACd,MAAM;IACN,QAAQ;AACZ;AACA,MAAM,mBAAmB;IACrB,SAAS;IACT,UAAU;IACV,YAAY;IACZ,cAAc;IACd,aAAa;IACb,aAAa;IACb,eAAe;AACnB;AACA,MAAM,sBAAsB;IACxB,YAAY;IACZ,YAAY;IACZ,cAAc;AAClB;AACA,MAAM,4BAA4B;IAC9B,QAAQ;IACR,cAAc;AAClB;AACA,MAAM,8BAA8B;IAChC,eAAe;IACf,YAAY;IACZ,MAAM;IACN,cAAc;IACd,SAAS;IACT,cAAc;IACd,aAAa;IACb,iBAAiB;AACrB;AACA,SAAS,wBAAwB,IAAI;IACjC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;IACpD,IAAI,MAAM,CAAC;IACX,IAAI,CAAC,CAAC,KAAK,KAAK,cAAc,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,IAAI,GAAG;QACjF,IAAI,aAAa,GAAG,IAAI,aAAa,GAAG,IAAI,aAAa,GAAG;QAC5D,KAAK,cAAc,CAAC,OAAO,CAAC,CAAC;YACzB,IAAI,aAAa,CAAC,IAAI,CAAC,eAAe,IAAI;QAC9C;IACJ;IACA,IAAI,CAAC,CAAC,KAAK,KAAK,cAAc,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,IAAI,GAAG;QACjF,IAAI,aAAa,GAAG,IAAI,aAAa,GAAG,IAAI,aAAa,GAAG;QAC5D,KAAK,cAAc,CAAC,OAAO,CAAC,CAAC;YACzB,IAAI,aAAa,CAAC,IAAI,CAAC,eAAe,IAAI;QAC9C;IACJ;IACA,IAAI,CAAC,CAAC,KAAK,KAAK,cAAc,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,IAAI,GAAG;QACjF,IAAI,aAAa,GAAG,IAAI,aAAa,GAAG,IAAI,aAAa,GAAG;QAC5D,KAAK,cAAc,CAAC,OAAO,CAAC,CAAC;YACzB,IAAI,aAAa,CAAC,IAAI,CAAC,eAAe,IAAI;QAC9C;IACJ;IACA,IAAI,CAAC,CAAC,KAAK,KAAK,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,IAAI,GAAG;QAC9E,IAAI,UAAU,GAAG,IAAI,UAAU,GAAG,IAAI,UAAU,GAAG;QACnD,KAAK,WAAW,CAAC,OAAO,CAAC,CAAC;YACtB,IAAI,UAAU,CAAC,IAAI,CAAC,eAAe,IAAI;QAC3C;IACJ;IACA,IAAI,CAAC,CAAC,KAAK,KAAK,YAAY,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,IAAI,GAAG;QAC/E,IAAI,WAAW,GAAG,IAAI,WAAW,GAAG,IAAI,WAAW,GAAG;QACtD,KAAK,YAAY,CAAC,OAAO,CAAC,CAAC;YACvB,IAAI,WAAW,CAAC,IAAI,CAAC,eAAe,IAAI;QAC5C;IACJ;IACA,IAAI,CAAC,CAAC,KAAK,KAAK,aAAa,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,IAAI,GAAG;QAChF,IAAI,YAAY,GAAG,IAAI,YAAY,GAAG,IAAI,YAAY,GAAG;QACzD,KAAK,aAAa,CAAC,OAAO,CAAC,CAAC;YACxB,IAAI,YAAY,CAAC,IAAI,CAAC,eAAe,IAAI;QAC7C;IACJ;IACA,IAAI,CAAC,CAAC,KAAK,KAAK,sBAAsB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,IAAI,GAAG;QACzF,IAAI,kBAAkB,GAAG,IAAI,kBAAkB,GACzC,IAAI,kBAAkB,GACtB;QACN,KAAK,sBAAsB,CAAC,OAAO,CAAC,CAAC;YACjC,IAAI,kBAAkB,CAAC,IAAI,CAAC,eAAe,KAAK;QACpD;IACJ;IACA,IAAI,CAAC,CAAC,KAAK,KAAK,eAAe,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,IAAI,GAAG;QAClF,IAAI,cAAc,GAAG,IAAI,cAAc,GACjC,IAAI,cAAc,GAClB;QACN,KAAK,eAAe,CAAC,OAAO,CAAC,CAAC;YAC1B,IAAI,cAAc,CAAC,IAAI,CAAC,eAAe,IAAI;QAC/C;IACJ;IACA,IAAI,CAAC,CAAC,KAAK,KAAK,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,IAAI,GAAG;QAC9E,IAAI,WAAW,GAAG,IAAI,WAAW,GAAG,IAAI,WAAW,GAAG;QACtD,KAAK,WAAW,CAAC,OAAO,CAAC,CAAC;YACtB,IAAI,WAAW,CAAC,IAAI,CAAC,eAAe,GAAG;QAC3C;IACJ;IACA,IAAI,CAAC,CAAC,KAAK,KAAK,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,IAAI,GAAG;QAC5E,IAAI,QAAQ,GAAG,IAAI,QAAQ,GAAG,IAAI,QAAQ,GAAG;QAC7C,KAAK,SAAS,CAAC,OAAO,CAAC,CAAC;YACpB,IAAI,QAAQ,CAAC,IAAI,CAAC,eAAe,IAAI;QACzC;IACJ;IACA,IAAI,CAAC,CAAC,KAAK,KAAK,YAAY,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,IAAI,GAAG;QAC/E,IAAI,WAAW,GAAG,IAAI,WAAW,GAAG,IAAI,WAAW,GAAG;QACtD,KAAK,YAAY,CAAC,OAAO,CAAC,CAAC;YACvB,IAAI,WAAW,CAAC,IAAI,CAAC,eAAe,IAAI;QAC5C;IACJ;IACA,IAAI,CAAC,CAAC,KAAK,KAAK,kBAAkB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,IAAI,GAAG;QACrF,IAAI,iBAAiB,GAAG,IAAI,iBAAiB,GACvC,IAAI,iBAAiB,GACrB;QACN,KAAK,kBAAkB,CAAC,OAAO,CAAC,CAAC;YAC7B,IAAI,iBAAiB,CAAC,IAAI,CAAC,eAAe,IAAI;QAClD;IACJ;IACA,IAAI,CAAC,CAAC,KAAK,KAAK,oBAAoB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,IAAI,GAAG;QACvF,IAAI,mBAAmB,GAAG,IAAI,mBAAmB,GAC3C,IAAI,mBAAmB,GACvB;QACN,KAAK,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAC/B,IAAI,mBAAmB,CAAC,IAAI,CAAC,eAAe,IAAI;QACpD;IACJ;IACA,OAAO;AACX;AACA,QAAQ,uBAAuB,GAAG;AAClC,SAAS,wBAAwB,GAAG;IAChC,IAAI,MAAM;IACV,IAAK,MAAM,OAAO,IAAK;QACnB,OAAO,GAAG,CAAC,IAAI,GACT,GAAG,CAAC,IAAI,CAAC,MAAM,GACf;IACV;IACA,OAAO;AACX;AACA,QAAQ,uBAAuB,GAAG;AAClC,SAAS,sBAAsB,GAAG,EAAE,GAAG;IACnC,OAAO;QACH,eAAe,CAAC,IAAI,aAAa,IAAI,EAAE,EAAE,MAAM,CAAC,IAAI,aAAa,IAAI,EAAE;QACvE,eAAe,CAAC,IAAI,aAAa,IAAI,EAAE,EAAE,MAAM,CAAC,IAAI,aAAa,IAAI,EAAE;QACvE,eAAe,CAAC,IAAI,aAAa,IAAI,EAAE,EAAE,MAAM,CAAC,IAAI,aAAa,IAAI,EAAE;QACvE,YAAY,CAAC,IAAI,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,IAAI,UAAU,IAAI,EAAE;QAC9D,aAAa,CAAC,IAAI,WAAW,IAAI,EAAE,EAAE,MAAM,CAAC,IAAI,WAAW,IAAI,EAAE;QACjE,cAAc,CAAC,IAAI,YAAY,IAAI,EAAE,EAAE,MAAM,CAAC,IAAI,YAAY,IAAI,EAAE;QACpE,oBAAoB,CAAC,IAAI,kBAAkB,IAAI,EAAE,EAAE,MAAM,CAAC,IAAI,kBAAkB,IAAI,EAAE;QACtF,gBAAgB,CAAC,IAAI,cAAc,IAAI,EAAE,EAAE,MAAM,CAAC,IAAI,cAAc,IAAI,EAAE;QAC1E,aAAa,CAAC,IAAI,WAAW,IAAI,EAAE,EAAE,MAAM,CAAC,IAAI,WAAW,IAAI,EAAE;QACjE,UAAU,CAAC,IAAI,QAAQ,IAAI,EAAE,EAAE,MAAM,CAAC,IAAI,QAAQ,IAAI,EAAE;QACxD,aAAa,CAAC,IAAI,WAAW,IAAI,EAAE,EAAE,MAAM,CAAC,IAAI,WAAW,IAAI,EAAE;QACjE,mBAAmB,CAAC,IAAI,iBAAiB,IAAI,EAAE,EAAE,MAAM,CAAC,IAAI,iBAAiB,IAAI,EAAE;QACnF,qBAAqB,CAAC,IAAI,mBAAmB,IAAI,EAAE,EAAE,MAAM,CAAC,IAAI,mBAAmB,IAAI,EAAE;IAC7F;AACJ;AACA,QAAQ,qBAAqB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 834, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/node_modules/%40alpacahq/alpaca-trade-api/dist/resources/datav2/rest_v2.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __await = (this && this.__await) || function (v) { return this instanceof __await ? (this.v = v, this) : new __await(v); }\nvar __asyncGenerator = (this && this.__asyncGenerator) || function (thisArg, _arguments, generator) {\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n    function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n    function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n    function fulfill(value) { resume(\"next\", value); }\n    function reject(value) { resume(\"throw\", value); }\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n};\nvar __asyncValues = (this && this.__asyncValues) || function (o) {\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n    var m = o[Symbol.asyncIterator], i;\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getCorporateActions = exports.getOptionChain = exports.getOptionSnapshots = exports.getLatestOptionQuotes = exports.getLatestOptionTrades = exports.getMultiOptionTradesAsync = exports.getMultiOptionTrades = exports.getMultiOptionBarsAsync = exports.getMultiOptionBars = exports.getNews = exports.Sort = exports.getLatestCryptoOrderbooks = exports.getCryptoSnapshots = exports.getLatestCryptoQuotes = exports.getLatestCryptoTrades = exports.getLatestCryptoBars = exports.getCryptoBars = exports.getCryptoQuotes = exports.getCryptoTrades = exports.getSnapshots = exports.getSnapshot = exports.getLatestBars = exports.getLatestBar = exports.getLatestQuotes = exports.getLatestQuote = exports.getLatestTrades = exports.getLatestTrade = exports.getMultiBarsAsync = exports.getMultiBars = exports.getBars = exports.getMultiQuotesAsync = exports.getMultiQuotes = exports.getQuotes = exports.getMultiTradesAsync = exports.getMultiTrades = exports.getTrades = exports.getMultiDataV2 = exports.getDataV2 = exports.dataV2HttpRequest = exports.TYPE = exports.Adjustment = void 0;\nconst axios_1 = __importDefault(require(\"axios\"));\nconst entityv2_1 = require(\"./entityv2\");\n// Number of data points to return.\nconst V2_MAX_LIMIT = 10000;\nconst V2_NEWS_MAX_LIMIT = 50;\nconst V1_BETA1_MAX_LIMIT = 1000;\nvar Adjustment;\n(function (Adjustment) {\n    Adjustment[\"RAW\"] = \"raw\";\n    Adjustment[\"DIVIDEND\"] = \"dividend\";\n    Adjustment[\"SPLIT\"] = \"split\";\n    Adjustment[\"ALL\"] = \"all\";\n})(Adjustment || (exports.Adjustment = Adjustment = {}));\nvar TYPE;\n(function (TYPE) {\n    TYPE[\"TRADES\"] = \"trades\";\n    TYPE[\"QUOTES\"] = \"quotes\";\n    TYPE[\"BARS\"] = \"bars\";\n    TYPE[\"SNAPSHOTS\"] = \"snapshots\";\n})(TYPE || (exports.TYPE = TYPE = {}));\nfunction dataV2HttpRequest(url, queryParams, config) {\n    const { dataBaseUrl, keyId, secretKey, oauth } = config;\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        \"Accept-Encoding\": \"gzip\",\n    };\n    if (oauth == \"\") {\n        headers[\"APCA-API-KEY-ID\"] = keyId;\n        headers[\"APCA-API-SECRET-KEY\"] = secretKey;\n    }\n    else {\n        headers[\"Authorization\"] = \"Bearer \" + oauth;\n    }\n    return axios_1.default\n        .get(`${dataBaseUrl}${url}`, {\n        params: queryParams,\n        headers: headers,\n    })\n        .catch((err) => {\n        var _a, _b;\n        throw new Error(`code: ${((_a = err.response) === null || _a === void 0 ? void 0 : _a.status) || err.statusCode}, message: ${(_b = err.response) === null || _b === void 0 ? void 0 : _b.data.message}`);\n    });\n}\nexports.dataV2HttpRequest = dataV2HttpRequest;\nfunction getQueryLimit(totalLimit, pageLimit, received) {\n    let limit = 0;\n    if (pageLimit !== 0) {\n        limit = pageLimit;\n    }\n    if (totalLimit !== 0) {\n        const remaining = totalLimit - received;\n        if (remaining <= 0) {\n            // this should never happen\n            return -1;\n        }\n        if (limit == 0 || limit > remaining) {\n            limit = remaining;\n        }\n    }\n    return limit;\n}\nfunction getDataV2(endpoint, path, options, config) {\n    return __asyncGenerator(this, arguments, function* getDataV2_1() {\n        var _a;\n        let pageToken = null;\n        let received = 0;\n        const pageLimit = options.pageLimit\n            ? Math.min(options.pageLimit, V2_MAX_LIMIT)\n            : V2_MAX_LIMIT;\n        delete options.pageLimit;\n        options.limit = (_a = options.limit) !== null && _a !== void 0 ? _a : 0;\n        while (options.limit > received || options.limit === 0) {\n            let limit;\n            if (options.limit !== 0) {\n                limit = getQueryLimit(options.limit, pageLimit, received);\n                if (limit == -1) {\n                    break;\n                }\n            }\n            else {\n                limit = null;\n            }\n            const resp = yield __await(dataV2HttpRequest(path, Object.assign(Object.assign({}, options), { limit, page_token: pageToken }), config));\n            const items = resp.data[endpoint] || [];\n            for (const item of items) {\n                yield yield __await(item);\n            }\n            received += items.length;\n            pageToken = resp.data.next_page_token;\n            if (!pageToken) {\n                break;\n            }\n        }\n    });\n}\nexports.getDataV2 = getDataV2;\nfunction getMultiDataV2(symbols, url, endpoint, options, config) {\n    return __asyncGenerator(this, arguments, function* getMultiDataV2_1() {\n        var _a;\n        let pageToken = null;\n        let received = 0;\n        const pageLimit = options.pageLimit\n            ? Math.min(options.pageLimit, V2_MAX_LIMIT)\n            : V2_MAX_LIMIT;\n        delete options.pageLimit;\n        options.limit = (_a = options.limit) !== null && _a !== void 0 ? _a : 0;\n        while (options.limit > received || options.limit === 0) {\n            const limit = getQueryLimit(options.limit, pageLimit, received);\n            if (limit == -1) {\n                break;\n            }\n            const params = Object.assign(Object.assign({}, options), { symbols: symbols.join(\",\"), limit: limit, page_token: pageToken });\n            const resp = yield __await(dataV2HttpRequest(`${url}${endpoint}`, params, config));\n            const items = resp.data[endpoint];\n            for (const symbol in items) {\n                for (const data of items[symbol]) {\n                    received++;\n                    yield yield __await({ symbol: symbol, data: data });\n                }\n            }\n            pageToken = resp.data.next_page_token;\n            if (!pageToken) {\n                break;\n            }\n        }\n    });\n}\nexports.getMultiDataV2 = getMultiDataV2;\nfunction getTrades(symbol, options, config) {\n    return __asyncGenerator(this, arguments, function* getTrades_1() {\n        var _a, e_1, _b, _c;\n        const trades = getDataV2(TYPE.TRADES, `/v2/stocks/${symbol}/${TYPE.TRADES}`, options, config);\n        try {\n            for (var _d = true, trades_1 = __asyncValues(trades), trades_1_1; trades_1_1 = yield __await(trades_1.next()), _a = trades_1_1.done, !_a; _d = true) {\n                _c = trades_1_1.value;\n                _d = false;\n                const trade = _c;\n                yield yield __await((0, entityv2_1.AlpacaTradeV2)(trade));\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (!_d && !_a && (_b = trades_1.return)) yield __await(_b.call(trades_1));\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    });\n}\nexports.getTrades = getTrades;\nfunction getMultiTrades(symbols, options, config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        var _a, e_2, _b, _c;\n        const multiTrades = getMultiTradesAsync(symbols, options, config);\n        const trades = new Map();\n        try {\n            for (var _d = true, multiTrades_1 = __asyncValues(multiTrades), multiTrades_1_1; multiTrades_1_1 = yield multiTrades_1.next(), _a = multiTrades_1_1.done, !_a; _d = true) {\n                _c = multiTrades_1_1.value;\n                _d = false;\n                const t = _c;\n                const items = trades.get(t.Symbol) || new Array();\n                trades.set(t.Symbol, [...items, t]);\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (!_d && !_a && (_b = multiTrades_1.return)) yield _b.call(multiTrades_1);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        return trades;\n    });\n}\nexports.getMultiTrades = getMultiTrades;\nfunction getMultiTradesAsync(symbols, options, config) {\n    return __asyncGenerator(this, arguments, function* getMultiTradesAsync_1() {\n        var _a, e_3, _b, _c;\n        const multiTrades = getMultiDataV2(symbols, \"/v2/stocks/\", TYPE.TRADES, options, config);\n        try {\n            for (var _d = true, multiTrades_2 = __asyncValues(multiTrades), multiTrades_2_1; multiTrades_2_1 = yield __await(multiTrades_2.next()), _a = multiTrades_2_1.done, !_a; _d = true) {\n                _c = multiTrades_2_1.value;\n                _d = false;\n                const t = _c;\n                t.data = Object.assign(Object.assign({}, t.data), { S: t.symbol });\n                yield yield __await((0, entityv2_1.AlpacaTradeV2)(t.data));\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (!_d && !_a && (_b = multiTrades_2.return)) yield __await(_b.call(multiTrades_2));\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n    });\n}\nexports.getMultiTradesAsync = getMultiTradesAsync;\nfunction getQuotes(symbol, options, config) {\n    return __asyncGenerator(this, arguments, function* getQuotes_1() {\n        var _a, e_4, _b, _c;\n        const quotes = getDataV2(TYPE.QUOTES, `/v2/stocks/${symbol}/${TYPE.QUOTES}`, options, config);\n        try {\n            for (var _d = true, quotes_1 = __asyncValues(quotes), quotes_1_1; quotes_1_1 = yield __await(quotes_1.next()), _a = quotes_1_1.done, !_a; _d = true) {\n                _c = quotes_1_1.value;\n                _d = false;\n                const quote = _c;\n                yield yield __await((0, entityv2_1.AlpacaQuoteV2)(quote));\n            }\n        }\n        catch (e_4_1) { e_4 = { error: e_4_1 }; }\n        finally {\n            try {\n                if (!_d && !_a && (_b = quotes_1.return)) yield __await(_b.call(quotes_1));\n            }\n            finally { if (e_4) throw e_4.error; }\n        }\n    });\n}\nexports.getQuotes = getQuotes;\nfunction getMultiQuotes(symbols, options, config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        var _a, e_5, _b, _c;\n        const multiQuotes = getMultiQuotesAsync(symbols, options, config);\n        const quotes = new Map();\n        try {\n            for (var _d = true, multiQuotes_1 = __asyncValues(multiQuotes), multiQuotes_1_1; multiQuotes_1_1 = yield multiQuotes_1.next(), _a = multiQuotes_1_1.done, !_a; _d = true) {\n                _c = multiQuotes_1_1.value;\n                _d = false;\n                const q = _c;\n                const items = quotes.get(q.Symbol) || new Array();\n                quotes.set(q.Symbol, [...items, q]);\n            }\n        }\n        catch (e_5_1) { e_5 = { error: e_5_1 }; }\n        finally {\n            try {\n                if (!_d && !_a && (_b = multiQuotes_1.return)) yield _b.call(multiQuotes_1);\n            }\n            finally { if (e_5) throw e_5.error; }\n        }\n        return quotes;\n    });\n}\nexports.getMultiQuotes = getMultiQuotes;\nfunction getMultiQuotesAsync(symbols, options, config) {\n    return __asyncGenerator(this, arguments, function* getMultiQuotesAsync_1() {\n        var _a, e_6, _b, _c;\n        const multiQuotes = getMultiDataV2(symbols, \"/v2/stocks/\", TYPE.QUOTES, options, config);\n        try {\n            for (var _d = true, multiQuotes_2 = __asyncValues(multiQuotes), multiQuotes_2_1; multiQuotes_2_1 = yield __await(multiQuotes_2.next()), _a = multiQuotes_2_1.done, !_a; _d = true) {\n                _c = multiQuotes_2_1.value;\n                _d = false;\n                const q = _c;\n                q.data = Object.assign(Object.assign({}, q.data), { S: q.symbol });\n                yield yield __await((0, entityv2_1.AlpacaQuoteV2)(q.data));\n            }\n        }\n        catch (e_6_1) { e_6 = { error: e_6_1 }; }\n        finally {\n            try {\n                if (!_d && !_a && (_b = multiQuotes_2.return)) yield __await(_b.call(multiQuotes_2));\n            }\n            finally { if (e_6) throw e_6.error; }\n        }\n    });\n}\nexports.getMultiQuotesAsync = getMultiQuotesAsync;\nfunction getBars(symbol, options, config) {\n    return __asyncGenerator(this, arguments, function* getBars_1() {\n        var _a, e_7, _b, _c;\n        const bars = getDataV2(TYPE.BARS, `/v2/stocks/${symbol}/${TYPE.BARS}`, options, config);\n        try {\n            for (var _d = true, _e = __asyncValues(bars || []), _f; _f = yield __await(_e.next()), _a = _f.done, !_a; _d = true) {\n                _c = _f.value;\n                _d = false;\n                const bar = _c;\n                yield yield __await((0, entityv2_1.AlpacaBarV2)(bar));\n            }\n        }\n        catch (e_7_1) { e_7 = { error: e_7_1 }; }\n        finally {\n            try {\n                if (!_d && !_a && (_b = _e.return)) yield __await(_b.call(_e));\n            }\n            finally { if (e_7) throw e_7.error; }\n        }\n    });\n}\nexports.getBars = getBars;\nfunction getMultiBars(symbols, options, config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        var _a, e_8, _b, _c;\n        const multiBars = getMultiBarsAsync(symbols, options, config);\n        const bars = new Map();\n        try {\n            for (var _d = true, multiBars_1 = __asyncValues(multiBars), multiBars_1_1; multiBars_1_1 = yield multiBars_1.next(), _a = multiBars_1_1.done, !_a; _d = true) {\n                _c = multiBars_1_1.value;\n                _d = false;\n                const b = _c;\n                const items = bars.get(b.Symbol) || new Array();\n                bars.set(b.Symbol, [...items, b]);\n            }\n        }\n        catch (e_8_1) { e_8 = { error: e_8_1 }; }\n        finally {\n            try {\n                if (!_d && !_a && (_b = multiBars_1.return)) yield _b.call(multiBars_1);\n            }\n            finally { if (e_8) throw e_8.error; }\n        }\n        return bars;\n    });\n}\nexports.getMultiBars = getMultiBars;\nfunction getMultiBarsAsync(symbols, options, config) {\n    return __asyncGenerator(this, arguments, function* getMultiBarsAsync_1() {\n        var _a, e_9, _b, _c;\n        const multiBars = getMultiDataV2(symbols, \"/v2/stocks/\", TYPE.BARS, options, config);\n        try {\n            for (var _d = true, multiBars_2 = __asyncValues(multiBars), multiBars_2_1; multiBars_2_1 = yield __await(multiBars_2.next()), _a = multiBars_2_1.done, !_a; _d = true) {\n                _c = multiBars_2_1.value;\n                _d = false;\n                const b = _c;\n                b.data = Object.assign(Object.assign({}, b.data), { S: b.symbol });\n                yield yield __await((0, entityv2_1.AlpacaBarV2)(b.data));\n            }\n        }\n        catch (e_9_1) { e_9 = { error: e_9_1 }; }\n        finally {\n            try {\n                if (!_d && !_a && (_b = multiBars_2.return)) yield __await(_b.call(multiBars_2));\n            }\n            finally { if (e_9) throw e_9.error; }\n        }\n    });\n}\nexports.getMultiBarsAsync = getMultiBarsAsync;\nfunction getLatestTrade(symbol, config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const resp = yield dataV2HttpRequest(`/v2/stocks/${symbol}/trades/latest`, {}, config);\n        return (0, entityv2_1.AlpacaTradeV2)(resp.data.trade);\n    });\n}\nexports.getLatestTrade = getLatestTrade;\nfunction getLatestTrades(symbols, config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const resp = yield dataV2HttpRequest(`/v2/stocks/${TYPE.TRADES}/latest`, { symbols: symbols.join(\",\") }, config);\n        const multiLatestTrades = resp.data.trades;\n        const multiLatestTradesResp = new Map();\n        for (const symbol in multiLatestTrades) {\n            multiLatestTradesResp.set(symbol, (0, entityv2_1.AlpacaTradeV2)(Object.assign({ S: symbol }, multiLatestTrades[symbol])));\n        }\n        return multiLatestTradesResp;\n    });\n}\nexports.getLatestTrades = getLatestTrades;\nfunction getLatestQuote(symbol, config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const resp = yield dataV2HttpRequest(`/v2/stocks/${symbol}/quotes/latest`, {}, config);\n        return (0, entityv2_1.AlpacaQuoteV2)(resp.data.quote);\n    });\n}\nexports.getLatestQuote = getLatestQuote;\nfunction getLatestQuotes(symbols, config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const resp = yield dataV2HttpRequest(`/v2/stocks/${TYPE.QUOTES}/latest`, { symbols: symbols.join(\",\") }, config);\n        const multiLatestQuotes = resp.data.quotes;\n        const multiLatestQuotesResp = new Map();\n        for (const symbol in multiLatestQuotes) {\n            multiLatestQuotesResp.set(symbol, (0, entityv2_1.AlpacaQuoteV2)(Object.assign({ S: symbol }, multiLatestQuotes[symbol])));\n        }\n        return multiLatestQuotesResp;\n    });\n}\nexports.getLatestQuotes = getLatestQuotes;\nfunction getLatestBar(symbol, config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const resp = yield dataV2HttpRequest(`/v2/stocks/${symbol}/bars/latest`, {}, config);\n        return (0, entityv2_1.AlpacaBarV2)(resp.data.bar);\n    });\n}\nexports.getLatestBar = getLatestBar;\nfunction getLatestBars(symbols, config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const resp = yield dataV2HttpRequest(`/v2/stocks/${TYPE.BARS}/latest`, { symbols: symbols.join(\",\") }, config);\n        const multiLatestBars = resp.data.bars;\n        const multiLatestBarsResp = new Map();\n        for (const symbol in multiLatestBars) {\n            multiLatestBarsResp.set(symbol, (0, entityv2_1.AlpacaBarV2)(Object.assign({ S: symbol }, multiLatestBars[symbol])));\n        }\n        return multiLatestBarsResp;\n    });\n}\nexports.getLatestBars = getLatestBars;\nfunction getSnapshot(symbol, config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const resp = yield dataV2HttpRequest(`/v2/stocks/${symbol}/snapshot`, {}, config);\n        return (0, entityv2_1.AlpacaSnapshotV2)(resp.data);\n    });\n}\nexports.getSnapshot = getSnapshot;\nfunction getSnapshots(symbols, config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const resp = yield dataV2HttpRequest(`/v2/stocks/snapshots?symbols=${symbols.join(\",\")}`, {}, config);\n        const result = Object.entries(resp.data).map(([key, val]) => {\n            return (0, entityv2_1.AlpacaSnapshotV2)(Object.assign({ symbol: key }, val));\n        });\n        return result;\n    });\n}\nexports.getSnapshots = getSnapshots;\nfunction getCryptoTrades(symbols, options, config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        var _a, e_10, _b, _c;\n        const cryptoTrades = getMultiDataV2(symbols, \"/v1beta3/crypto/us/\", TYPE.TRADES, options, config);\n        const trades = new Map();\n        try {\n            for (var _d = true, cryptoTrades_1 = __asyncValues(cryptoTrades), cryptoTrades_1_1; cryptoTrades_1_1 = yield cryptoTrades_1.next(), _a = cryptoTrades_1_1.done, !_a; _d = true) {\n                _c = cryptoTrades_1_1.value;\n                _d = false;\n                const t = _c;\n                const items = trades.get(t.symbol) || new Array();\n                trades.set(t.symbol, [...items, (0, entityv2_1.AlpacaCryptoTrade)(t.data)]);\n            }\n        }\n        catch (e_10_1) { e_10 = { error: e_10_1 }; }\n        finally {\n            try {\n                if (!_d && !_a && (_b = cryptoTrades_1.return)) yield _b.call(cryptoTrades_1);\n            }\n            finally { if (e_10) throw e_10.error; }\n        }\n        return trades;\n    });\n}\nexports.getCryptoTrades = getCryptoTrades;\nfunction getCryptoQuotes(symbols, options, config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        var _a, e_11, _b, _c;\n        const cryptoQuotes = getMultiDataV2(symbols, \"/v1beta3/crypto/us/\", TYPE.QUOTES, options, config);\n        const quotes = new Map();\n        try {\n            for (var _d = true, cryptoQuotes_1 = __asyncValues(cryptoQuotes), cryptoQuotes_1_1; cryptoQuotes_1_1 = yield cryptoQuotes_1.next(), _a = cryptoQuotes_1_1.done, !_a; _d = true) {\n                _c = cryptoQuotes_1_1.value;\n                _d = false;\n                const t = _c;\n                const items = quotes.get(t.symbol) || new Array();\n                quotes.set(t.symbol, [...items, (0, entityv2_1.AlpacaCryptoQuote)(t.data)]);\n            }\n        }\n        catch (e_11_1) { e_11 = { error: e_11_1 }; }\n        finally {\n            try {\n                if (!_d && !_a && (_b = cryptoQuotes_1.return)) yield _b.call(cryptoQuotes_1);\n            }\n            finally { if (e_11) throw e_11.error; }\n        }\n        return quotes;\n    });\n}\nexports.getCryptoQuotes = getCryptoQuotes;\nfunction getCryptoBars(symbols, options, config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        var _a, e_12, _b, _c;\n        const cryptoBars = getMultiDataV2(symbols, \"/v1beta3/crypto/us/\", TYPE.BARS, options, config);\n        const bars = new Map();\n        try {\n            for (var _d = true, cryptoBars_1 = __asyncValues(cryptoBars), cryptoBars_1_1; cryptoBars_1_1 = yield cryptoBars_1.next(), _a = cryptoBars_1_1.done, !_a; _d = true) {\n                _c = cryptoBars_1_1.value;\n                _d = false;\n                const t = _c;\n                const items = bars.get(t.symbol) || new Array();\n                bars.set(t.symbol, [...items, (0, entityv2_1.AlpacaCryptoBar)(t.data)]);\n            }\n        }\n        catch (e_12_1) { e_12 = { error: e_12_1 }; }\n        finally {\n            try {\n                if (!_d && !_a && (_b = cryptoBars_1.return)) yield _b.call(cryptoBars_1);\n            }\n            finally { if (e_12) throw e_12.error; }\n        }\n        return bars;\n    });\n}\nexports.getCryptoBars = getCryptoBars;\nfunction getLatestCryptoBars(symbols, config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const params = { symbols: symbols.join(\",\") };\n        const resp = yield dataV2HttpRequest(`/v1beta3/crypto/us/latest/bars`, params, config);\n        const multiLatestCryptoBars = resp.data.bars;\n        const result = new Map();\n        for (const symbol in multiLatestCryptoBars) {\n            const bar = multiLatestCryptoBars[symbol];\n            result.set(symbol, (0, entityv2_1.AlpacaCryptoBar)(bar));\n        }\n        return result;\n    });\n}\nexports.getLatestCryptoBars = getLatestCryptoBars;\nfunction getLatestCryptoTrades(symbols, config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const params = { symbols: symbols.join(\",\") };\n        const resp = yield dataV2HttpRequest(`/v1beta3/crypto/us/latest/trades`, params, config);\n        const multiLatestCryptoTrades = resp.data.trades;\n        const result = new Map();\n        for (const symbol in multiLatestCryptoTrades) {\n            const trade = multiLatestCryptoTrades[symbol];\n            result.set(symbol, (0, entityv2_1.AlpacaCryptoTrade)(trade));\n        }\n        return result;\n    });\n}\nexports.getLatestCryptoTrades = getLatestCryptoTrades;\nfunction getLatestCryptoQuotes(symbols, config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const params = { symbols: symbols.join(\",\") };\n        const resp = yield dataV2HttpRequest(`/v1beta3/crypto/us/latest/quotes`, params, config);\n        const multiLatestCryptoQuotes = resp.data.quotes;\n        const result = new Map();\n        for (const symbol in multiLatestCryptoQuotes) {\n            const quote = multiLatestCryptoQuotes[symbol];\n            result.set(symbol, (0, entityv2_1.AlpacaCryptoQuote)(quote));\n        }\n        return result;\n    });\n}\nexports.getLatestCryptoQuotes = getLatestCryptoQuotes;\nfunction getCryptoSnapshots(symbols, config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const params = { symbols: symbols.join(\",\") };\n        const resp = yield dataV2HttpRequest(`/v1beta3/crypto/us/snapshots`, params, config);\n        const snapshots = resp.data.snapshots;\n        const result = new Map();\n        for (const symbol in snapshots) {\n            const snapshot = snapshots[symbol];\n            result.set(symbol, (0, entityv2_1.AlpacaCryptoSnapshot)(snapshot));\n        }\n        return result;\n    });\n}\nexports.getCryptoSnapshots = getCryptoSnapshots;\nfunction getLatestCryptoOrderbooks(symbols, config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const params = { symbols: symbols.join(\",\") };\n        const resp = yield dataV2HttpRequest(`/v1beta3/crypto/us/latest/orderbooks`, params, config);\n        const orderbooks = resp.data.orderbooks;\n        const result = new Map();\n        for (const symbol in orderbooks) {\n            const orderbook = orderbooks[symbol];\n            result.set(symbol, (0, entityv2_1.AlpacaCryptoOrderbook)(orderbook));\n        }\n        return result;\n    });\n}\nexports.getLatestCryptoOrderbooks = getLatestCryptoOrderbooks;\nvar Sort;\n(function (Sort) {\n    Sort[\"ASC\"] = \"asc\";\n    Sort[\"DESC\"] = \"desc\";\n})(Sort || (exports.Sort = Sort = {}));\nfunction getNewsParams(options) {\n    var _a;\n    const query = {};\n    query.symbols = ((_a = options.symbols) === null || _a === void 0 ? void 0 : _a.length) > 0 ? options.symbols.join(\",\") : null;\n    query.start = options.start;\n    query.end = options.end;\n    query.sort = options.sort;\n    query.include_content = options.includeContent;\n    query.exclude_contentless = options.excludeContentless;\n    return query;\n}\nfunction getNews(options, config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        var _a;\n        if (options.totalLimit && options.totalLimit < 0) {\n            throw new Error(\"negative total limit\");\n        }\n        if (options.pageLimit && options.pageLimit < 0) {\n            throw new Error(\"negative page limit\");\n        }\n        let pageToken = null;\n        let received = 0;\n        const pageLimit = (options === null || options === void 0 ? void 0 : options.pageLimit)\n            ? Math.min(options.pageLimit, V2_NEWS_MAX_LIMIT)\n            : V2_NEWS_MAX_LIMIT;\n        options === null || options === void 0 ? true : delete options.pageLimit;\n        const totalLimit = (_a = options.totalLimit) !== null && _a !== void 0 ? _a : 10;\n        const result = [];\n        const params = getNewsParams(options);\n        let limit;\n        for (;;) {\n            limit = getQueryLimit(totalLimit, pageLimit, received);\n            if (limit < 1) {\n                break;\n            }\n            const resp = yield dataV2HttpRequest(\"/v1beta1/news\", Object.assign(Object.assign({}, params), { limit: limit, page_token: pageToken }), config);\n            resp.data.news.forEach((n) => result.push((0, entityv2_1.AlpacaNews)(n)));\n            received += resp.data.news.length;\n            pageToken = resp.data.next_page_token;\n            if (!pageToken) {\n                break;\n            }\n        }\n        return result;\n    });\n}\nexports.getNews = getNews;\nfunction getMultiOptionBars(symbols, options, config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        var _a, e_13, _b, _c;\n        const multiBars = getMultiOptionBarsAsync(symbols, options, config);\n        const bars = new Map();\n        try {\n            for (var _d = true, multiBars_3 = __asyncValues(multiBars), multiBars_3_1; multiBars_3_1 = yield multiBars_3.next(), _a = multiBars_3_1.done, !_a; _d = true) {\n                _c = multiBars_3_1.value;\n                _d = false;\n                const b = _c;\n                // symbol will always have a value\n                let symbol = b.Symbol ? b.Symbol : \"\";\n                delete b.Symbol;\n                const items = bars.get(symbol) || new Array();\n                bars.set(symbol, [...items, b]);\n            }\n        }\n        catch (e_13_1) { e_13 = { error: e_13_1 }; }\n        finally {\n            try {\n                if (!_d && !_a && (_b = multiBars_3.return)) yield _b.call(multiBars_3);\n            }\n            finally { if (e_13) throw e_13.error; }\n        }\n        return bars;\n    });\n}\nexports.getMultiOptionBars = getMultiOptionBars;\nfunction getMultiOptionBarsAsync(symbols, options, config) {\n    return __asyncGenerator(this, arguments, function* getMultiOptionBarsAsync_1() {\n        var _a, e_14, _b, _c;\n        const multiBars = getMultiDataV2(symbols, \"/v1beta1/options/\", TYPE.BARS, options, config);\n        try {\n            for (var _d = true, multiBars_4 = __asyncValues(multiBars), multiBars_4_1; multiBars_4_1 = yield __await(multiBars_4.next()), _a = multiBars_4_1.done, !_a; _d = true) {\n                _c = multiBars_4_1.value;\n                _d = false;\n                const b = _c;\n                b.data = Object.assign(Object.assign({}, b.data), { S: b.symbol });\n                yield yield __await((0, entityv2_1.AlpacaOptionBarV1Beta1)(b.data));\n            }\n        }\n        catch (e_14_1) { e_14 = { error: e_14_1 }; }\n        finally {\n            try {\n                if (!_d && !_a && (_b = multiBars_4.return)) yield __await(_b.call(multiBars_4));\n            }\n            finally { if (e_14) throw e_14.error; }\n        }\n    });\n}\nexports.getMultiOptionBarsAsync = getMultiOptionBarsAsync;\nfunction getMultiOptionTrades(symbols, options, config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        var _a, e_15, _b, _c;\n        const multiTrades = getMultiOptionTradesAsync(symbols, options, config);\n        const trades = new Map();\n        try {\n            for (var _d = true, multiTrades_3 = __asyncValues(multiTrades), multiTrades_3_1; multiTrades_3_1 = yield multiTrades_3.next(), _a = multiTrades_3_1.done, !_a; _d = true) {\n                _c = multiTrades_3_1.value;\n                _d = false;\n                const t = _c;\n                // symbol will always have a value\n                let symbol = t.Symbol ? t.Symbol : \"\";\n                delete t.Symbol;\n                const items = trades.get(symbol) || new Array();\n                trades.set(symbol, [...items, t]);\n            }\n        }\n        catch (e_15_1) { e_15 = { error: e_15_1 }; }\n        finally {\n            try {\n                if (!_d && !_a && (_b = multiTrades_3.return)) yield _b.call(multiTrades_3);\n            }\n            finally { if (e_15) throw e_15.error; }\n        }\n        return trades;\n    });\n}\nexports.getMultiOptionTrades = getMultiOptionTrades;\nfunction getMultiOptionTradesAsync(symbols, options, config) {\n    return __asyncGenerator(this, arguments, function* getMultiOptionTradesAsync_1() {\n        var _a, e_16, _b, _c;\n        const multiBars = getMultiDataV2(symbols, \"/v1beta1/options/\", TYPE.TRADES, options, config);\n        try {\n            for (var _d = true, multiBars_5 = __asyncValues(multiBars), multiBars_5_1; multiBars_5_1 = yield __await(multiBars_5.next()), _a = multiBars_5_1.done, !_a; _d = true) {\n                _c = multiBars_5_1.value;\n                _d = false;\n                const b = _c;\n                b.data = Object.assign(Object.assign({}, b.data), { S: b.symbol });\n                yield yield __await((0, entityv2_1.AlpacaOptionTradeV1Beta1)(b.data));\n            }\n        }\n        catch (e_16_1) { e_16 = { error: e_16_1 }; }\n        finally {\n            try {\n                if (!_d && !_a && (_b = multiBars_5.return)) yield __await(_b.call(multiBars_5));\n            }\n            finally { if (e_16) throw e_16.error; }\n        }\n    });\n}\nexports.getMultiOptionTradesAsync = getMultiOptionTradesAsync;\nfunction getLatestOptionTrades(symbols, config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const resp = yield dataV2HttpRequest(`/v1beta1/options/${TYPE.TRADES}/latest`, { symbols: symbols.join(\",\") }, config);\n        const multiLatestTrades = resp.data.trades;\n        const multiLatestTradesResp = new Map();\n        for (const symbol in multiLatestTrades) {\n            multiLatestTradesResp.set(symbol, (0, entityv2_1.AlpacaOptionTradeV1Beta1)(Object.assign({}, multiLatestTrades[symbol])));\n        }\n        return multiLatestTradesResp;\n    });\n}\nexports.getLatestOptionTrades = getLatestOptionTrades;\nfunction getLatestOptionQuotes(symbols, config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const resp = yield dataV2HttpRequest(`/v1beta1/options/${TYPE.QUOTES}/latest`, { symbols: symbols.join(\",\") }, config);\n        const multiLatestQuotes = resp.data.quotes;\n        const multiLatestQuotesResp = new Map();\n        for (const symbol in multiLatestQuotes) {\n            multiLatestQuotesResp.set(symbol, (0, entityv2_1.AlpacaOptionQuoteV1Beta1)(Object.assign({}, multiLatestQuotes[symbol])));\n        }\n        return multiLatestQuotesResp;\n    });\n}\nexports.getLatestOptionQuotes = getLatestOptionQuotes;\nfunction getOptionSnapshots(symbols, config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const resp = yield dataV2HttpRequest(`/v1beta1/options/snapshots?symbols=${symbols.join(\",\")}`, {}, config);\n        const result = Object.entries(resp.data.snapshots).map(([key, val]) => {\n            return (0, entityv2_1.AlpacaOptionSnapshotV1Beta1)(Object.assign({ Symbol: key }, val));\n        });\n        return result;\n    });\n}\nexports.getOptionSnapshots = getOptionSnapshots;\nfunction getOptionChain(underlyingSymbol, options, config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        var _a;\n        if (options.totalLimit && options.totalLimit < 0) {\n            throw new Error(\"negative total limit\");\n        }\n        if (options.pageLimit && options.pageLimit < 0) {\n            throw new Error(\"negative page limit\");\n        }\n        let pageToken = null;\n        let received = 0;\n        const pageLimit = (options === null || options === void 0 ? void 0 : options.pageLimit)\n            ? Math.min(options.pageLimit, V1_BETA1_MAX_LIMIT)\n            : V1_BETA1_MAX_LIMIT;\n        delete options.pageLimit;\n        const totalLimit = (_a = options === null || options === void 0 ? void 0 : options.totalLimit) !== null && _a !== void 0 ? _a : 10000;\n        delete options.totalLimit;\n        const result = [];\n        let limit;\n        for (;;) {\n            limit = getQueryLimit(totalLimit, pageLimit, received);\n            if (limit < 1) {\n                break;\n            }\n            const resp = yield dataV2HttpRequest(`/v1beta1/options/snapshots/${underlyingSymbol}`, Object.assign(Object.assign({}, options), { limit: limit, page_token: pageToken }), config);\n            const res = Object.entries(resp.data.snapshots).map(([key, val]) => {\n                return (0, entityv2_1.AlpacaOptionSnapshotV1Beta1)(Object.assign({ Symbol: key }, val));\n            });\n            received = received + res.length;\n            result.push(...res);\n            pageToken = resp.data.next_page_token;\n            if (!pageToken) {\n                break;\n            }\n        }\n        return result;\n    });\n}\nexports.getOptionChain = getOptionChain;\nfunction getCorporateActions(symbols, options, config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        var _a, _b;\n        if (options.totalLimit && options.totalLimit < 0) {\n            throw new Error(\"negative total limit\");\n        }\n        if (options.pageLimit && options.pageLimit < 0) {\n            throw new Error(\"negative page limit\");\n        }\n        let pageToken = null;\n        let received = 0;\n        const pageLimit = (options === null || options === void 0 ? void 0 : options.pageLimit)\n            ? Math.min(options.pageLimit, V1_BETA1_MAX_LIMIT)\n            : V1_BETA1_MAX_LIMIT;\n        options === null || options === void 0 ? true : delete options.pageLimit;\n        const totalLimit = (_a = options === null || options === void 0 ? void 0 : options.totalLimit) !== null && _a !== void 0 ? _a : V2_MAX_LIMIT;\n        delete options.totalLimit;\n        let result = {};\n        const types = (_b = options === null || options === void 0 ? void 0 : options.types) === null || _b === void 0 ? void 0 : _b.join(\",\");\n        const params = Object.assign(Object.assign({}, options), { symbols, types });\n        let limit;\n        for (;;) {\n            limit = getQueryLimit(totalLimit, pageLimit, received);\n            if (limit < 1) {\n                break;\n            }\n            const resp = yield dataV2HttpRequest(`/v1beta1/corporate-actions`, Object.assign(Object.assign({}, params), { limit: limit, page_token: pageToken }), config);\n            const cas = (0, entityv2_1.convertCorporateActions)(resp.data.corporate_actions);\n            result = (0, entityv2_1.mergeCorporateActions)(result, cas);\n            received += (0, entityv2_1.getCorporateActionsSize)(cas);\n            pageToken = resp.data.next_page_token;\n            if (!pageToken) {\n                break;\n            }\n        }\n        return result;\n    });\n}\nexports.getCorporateActions = getCorporateActions;\n"], "names": [], "mappings": "AACA,IAAI,YAAY,4DAAS,yDAAK,SAAS,IAAK,SAAU,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS;IACnF,SAAS,MAAM,KAAK;QAAI,OAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,OAAO;YAAI,QAAQ;QAAQ;IAAI;IAC3G,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,EAAE,SAAU,OAAO,EAAE,MAAM;QACrD,SAAS,UAAU,KAAK;YAAI,IAAI;gBAAE,KAAK,UAAU,IAAI,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC1F,SAAS,SAAS,KAAK;YAAI,IAAI;gBAAE,KAAK,SAAS,CAAC,QAAQ,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC7F,SAAS,KAAK,MAAM;YAAI,OAAO,IAAI,GAAG,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,IAAI,CAAC,WAAW;QAAW;QAC7G,KAAK,CAAC,YAAY,UAAU,KAAK,CAAC,SAAS,cAAc,EAAE,CAAC,EAAE,IAAI;IACtE;AACJ;AACA,IAAI,UAAU,4DAAS,yDAAK,OAAO,IAAK,SAAU,CAAC;IAAI,OAAO,IAAI,YAAY,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI,IAAI,QAAQ;AAAI;AAC7H,IAAI,mBAAmB,4DAAS,yDAAK,gBAAgB,IAAK,SAAU,OAAO,EAAE,UAAU,EAAE,SAAS;IAC9F,IAAI,CAAC,OAAO,aAAa,EAAE,MAAM,IAAI,UAAU;IAC/C,IAAI,IAAI,UAAU,KAAK,CAAC,SAAS,cAAc,EAAE,GAAG,GAAG,IAAI,EAAE;IAC7D,OAAO,IAAI,CAAC,GAAG,KAAK,SAAS,KAAK,UAAU,KAAK,UAAU,cAAc,CAAC,CAAC,OAAO,aAAa,CAAC,GAAG;QAAc,OAAO,IAAI;IAAE,GAAG;;;IACjI,SAAS,YAAY,CAAC;QAAI,OAAO,SAAU,CAAC;YAAI,OAAO,QAAQ,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG;QAAS;IAAG;IAC9F,SAAS,KAAK,CAAC,EAAE,CAAC;QAAI,IAAI,CAAC,CAAC,EAAE,EAAE;YAAE,CAAC,CAAC,EAAE,GAAG,SAAU,CAAC;gBAAI,OAAO,IAAI,QAAQ,SAAU,CAAC,EAAE,CAAC;oBAAI,EAAE,IAAI,CAAC;wBAAC;wBAAG;wBAAG;wBAAG;qBAAE,IAAI,KAAK,OAAO,GAAG;gBAAI;YAAI;YAAG,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE;QAAG;IAAE;IACvK,SAAS,OAAO,CAAC,EAAE,CAAC;QAAI,IAAI;YAAE,KAAK,CAAC,CAAC,EAAE,CAAC;QAAK,EAAE,OAAO,GAAG;YAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAAI;IAAE;IACjF,SAAS,KAAK,CAAC;QAAI,EAAE,KAAK,YAAY,UAAU,QAAQ,OAAO,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,UAAU,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;IAAI;IACvH,SAAS,QAAQ,KAAK;QAAI,OAAO,QAAQ;IAAQ;IACjD,SAAS,OAAO,KAAK;QAAI,OAAO,SAAS;IAAQ;IACjD,SAAS,OAAO,CAAC,EAAE,CAAC;QAAI,IAAI,EAAE,IAAI,EAAE,KAAK,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;IAAG;AACrF;AACA,IAAI,gBAAgB,4DAAS,yDAAK,aAAa,IAAK,SAAU,CAAC;IAC3D,IAAI,CAAC,OAAO,aAAa,EAAE,MAAM,IAAI,UAAU;IAC/C,IAAI,IAAI,CAAC,CAAC,OAAO,aAAa,CAAC,EAAE;IACjC,OAAO,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,aAAa,aAAa,SAAS,KAAK,CAAC,CAAC,OAAO,QAAQ,CAAC,IAAI,IAAI,CAAC,GAAG,KAAK,SAAS,KAAK,UAAU,KAAK,WAAW,CAAC,CAAC,OAAO,aAAa,CAAC,GAAG;QAAc,OAAO,IAAI;IAAE,GAAG,CAAC;;;IAC/M,SAAS,KAAK,CAAC;QAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,SAAU,CAAC;YAAI,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;gBAAI,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,OAAO,SAAS,QAAQ,EAAE,IAAI,EAAE,EAAE,KAAK;YAAG;QAAI;IAAG;IAC/J,SAAS,OAAO,OAAO,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;QAAI,QAAQ,OAAO,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;YAAI,QAAQ;gBAAE,OAAO;gBAAG,MAAM;YAAE;QAAI,GAAG;IAAS;AAC/H;AACA,IAAI,kBAAkB,4DAAS,yDAAK,eAAe,IAAK,SAAU,GAAG;IACjE,OAAO,AAAC,OAAO,IAAI,UAAU,GAAI,MAAM;QAAE,WAAW;IAAI;AAC5D;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,mBAAmB,GAAG,QAAQ,cAAc,GAAG,QAAQ,kBAAkB,GAAG,QAAQ,qBAAqB,GAAG,QAAQ,qBAAqB,GAAG,QAAQ,yBAAyB,GAAG,QAAQ,oBAAoB,GAAG,QAAQ,uBAAuB,GAAG,QAAQ,kBAAkB,GAAG,QAAQ,OAAO,GAAG,QAAQ,IAAI,GAAG,QAAQ,yBAAyB,GAAG,QAAQ,kBAAkB,GAAG,QAAQ,qBAAqB,GAAG,QAAQ,qBAAqB,GAAG,QAAQ,mBAAmB,GAAG,QAAQ,aAAa,GAAG,QAAQ,eAAe,GAAG,QAAQ,eAAe,GAAG,QAAQ,YAAY,GAAG,QAAQ,WAAW,GAAG,QAAQ,aAAa,GAAG,QAAQ,YAAY,GAAG,QAAQ,eAAe,GAAG,QAAQ,cAAc,GAAG,QAAQ,eAAe,GAAG,QAAQ,cAAc,GAAG,QAAQ,iBAAiB,GAAG,QAAQ,YAAY,GAAG,QAAQ,OAAO,GAAG,QAAQ,mBAAmB,GAAG,QAAQ,cAAc,GAAG,QAAQ,SAAS,GAAG,QAAQ,mBAAmB,GAAG,QAAQ,cAAc,GAAG,QAAQ,SAAS,GAAG,QAAQ,cAAc,GAAG,QAAQ,SAAS,GAAG,QAAQ,iBAAiB,GAAG,QAAQ,IAAI,GAAG,QAAQ,UAAU,GAAG,KAAK;AACjjC,MAAM,UAAU;AAChB,MAAM;AACN,mCAAmC;AACnC,MAAM,eAAe;AACrB,MAAM,oBAAoB;AAC1B,MAAM,qBAAqB;AAC3B,IAAI;AACJ,CAAC,SAAU,UAAU;IACjB,UAAU,CAAC,MAAM,GAAG;IACpB,UAAU,CAAC,WAAW,GAAG;IACzB,UAAU,CAAC,QAAQ,GAAG;IACtB,UAAU,CAAC,MAAM,GAAG;AACxB,CAAC,EAAE,cAAc,CAAC,QAAQ,UAAU,GAAG,aAAa,CAAC,CAAC;AACtD,IAAI;AACJ,CAAC,SAAU,IAAI;IACX,IAAI,CAAC,SAAS,GAAG;IACjB,IAAI,CAAC,SAAS,GAAG;IACjB,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,YAAY,GAAG;AACxB,CAAC,EAAE,QAAQ,CAAC,QAAQ,IAAI,GAAG,OAAO,CAAC,CAAC;AACpC,SAAS,kBAAkB,GAAG,EAAE,WAAW,EAAE,MAAM;IAC/C,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG;IACjD,MAAM,UAAU;QACZ,gBAAgB;QAChB,mBAAmB;IACvB;IACA,IAAI,SAAS,IAAI;QACb,OAAO,CAAC,kBAAkB,GAAG;QAC7B,OAAO,CAAC,sBAAsB,GAAG;IACrC,OACK;QACD,OAAO,CAAC,gBAAgB,GAAG,YAAY;IAC3C;IACA,OAAO,QAAQ,OAAO,CACjB,GAAG,CAAC,GAAG,cAAc,KAAK,EAAE;QAC7B,QAAQ;QACR,SAAS;IACb,GACK,KAAK,CAAC,CAAC;QACR,IAAI,IAAI;QACR,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,KAAK,IAAI,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,KAAK,IAAI,UAAU,CAAC,WAAW,EAAE,CAAC,KAAK,IAAI,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE;IAC3M;AACJ;AACA,QAAQ,iBAAiB,GAAG;AAC5B,SAAS,cAAc,UAAU,EAAE,SAAS,EAAE,QAAQ;IAClD,IAAI,QAAQ;IACZ,IAAI,cAAc,GAAG;QACjB,QAAQ;IACZ;IACA,IAAI,eAAe,GAAG;QAClB,MAAM,YAAY,aAAa;QAC/B,IAAI,aAAa,GAAG;YAChB,2BAA2B;YAC3B,OAAO,CAAC;QACZ;QACA,IAAI,SAAS,KAAK,QAAQ,WAAW;YACjC,QAAQ;QACZ;IACJ;IACA,OAAO;AACX;AACA,SAAS,UAAU,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM;IAC9C,OAAO,iBAAiB,IAAI,EAAE,WAAW,UAAU;QAC/C,IAAI;QACJ,IAAI,YAAY;QAChB,IAAI,WAAW;QACf,MAAM,YAAY,QAAQ,SAAS,GAC7B,KAAK,GAAG,CAAC,QAAQ,SAAS,EAAE,gBAC5B;QACN,OAAO,QAAQ,SAAS;QACxB,QAAQ,KAAK,GAAG,CAAC,KAAK,QAAQ,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QACtE,MAAO,QAAQ,KAAK,GAAG,YAAY,QAAQ,KAAK,KAAK,EAAG;YACpD,IAAI;YACJ,IAAI,QAAQ,KAAK,KAAK,GAAG;gBACrB,QAAQ,cAAc,QAAQ,KAAK,EAAE,WAAW;gBAChD,IAAI,SAAS,CAAC,GAAG;oBACb;gBACJ;YACJ,OACK;gBACD,QAAQ;YACZ;YACA,MAAM,OAAO,MAAM,QAAQ,kBAAkB,MAAM,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,UAAU;gBAAE;gBAAO,YAAY;YAAU,IAAI;YAChI,MAAM,QAAQ,KAAK,IAAI,CAAC,SAAS,IAAI,EAAE;YACvC,KAAK,MAAM,QAAQ,MAAO;gBACtB,MAAM,MAAM,QAAQ;YACxB;YACA,YAAY,MAAM,MAAM;YACxB,YAAY,KAAK,IAAI,CAAC,eAAe;YACrC,IAAI,CAAC,WAAW;gBACZ;YACJ;QACJ;IACJ;AACJ;AACA,QAAQ,SAAS,GAAG;AACpB,SAAS,eAAe,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM;IAC3D,OAAO,iBAAiB,IAAI,EAAE,WAAW,UAAU;QAC/C,IAAI;QACJ,IAAI,YAAY;QAChB,IAAI,WAAW;QACf,MAAM,YAAY,QAAQ,SAAS,GAC7B,KAAK,GAAG,CAAC,QAAQ,SAAS,EAAE,gBAC5B;QACN,OAAO,QAAQ,SAAS;QACxB,QAAQ,KAAK,GAAG,CAAC,KAAK,QAAQ,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QACtE,MAAO,QAAQ,KAAK,GAAG,YAAY,QAAQ,KAAK,KAAK,EAAG;YACpD,MAAM,QAAQ,cAAc,QAAQ,KAAK,EAAE,WAAW;YACtD,IAAI,SAAS,CAAC,GAAG;gBACb;YACJ;YACA,MAAM,SAAS,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,UAAU;gBAAE,SAAS,QAAQ,IAAI,CAAC;gBAAM,OAAO;gBAAO,YAAY;YAAU;YAC3H,MAAM,OAAO,MAAM,QAAQ,kBAAkB,GAAG,MAAM,UAAU,EAAE,QAAQ;YAC1E,MAAM,QAAQ,KAAK,IAAI,CAAC,SAAS;YACjC,IAAK,MAAM,UAAU,MAAO;gBACxB,KAAK,MAAM,QAAQ,KAAK,CAAC,OAAO,CAAE;oBAC9B;oBACA,MAAM,MAAM,QAAQ;wBAAE,QAAQ;wBAAQ,MAAM;oBAAK;gBACrD;YACJ;YACA,YAAY,KAAK,IAAI,CAAC,eAAe;YACrC,IAAI,CAAC,WAAW;gBACZ;YACJ;QACJ;IACJ;AACJ;AACA,QAAQ,cAAc,GAAG;AACzB,SAAS,UAAU,MAAM,EAAE,OAAO,EAAE,MAAM;IACtC,OAAO,iBAAiB,IAAI,EAAE,WAAW,UAAU;QAC/C,IAAI,IAAI,KAAK,IAAI;QACjB,MAAM,SAAS,UAAU,KAAK,MAAM,EAAE,CAAC,WAAW,EAAE,OAAO,CAAC,EAAE,KAAK,MAAM,EAAE,EAAE,SAAS;QACtF,IAAI;YACA,IAAK,IAAI,KAAK,MAAM,WAAW,cAAc,SAAS,YAAY,aAAa,MAAM,QAAQ,SAAS,IAAI,KAAK,KAAK,WAAW,IAAI,EAAE,CAAC,IAAI,KAAK,KAAM;gBACjJ,KAAK,WAAW,KAAK;gBACrB,KAAK;gBACL,MAAM,QAAQ;gBACd,MAAM,MAAM,QAAQ,CAAC,GAAG,WAAW,aAAa,EAAE;YACtD;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,SAAS,MAAM,GAAG,MAAM,QAAQ,GAAG,IAAI,CAAC;YACpE,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;IACJ;AACJ;AACA,QAAQ,SAAS,GAAG;AACpB,SAAS,eAAe,OAAO,EAAE,OAAO,EAAE,MAAM;IAC5C,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,IAAI,IAAI,KAAK,IAAI;QACjB,MAAM,cAAc,oBAAoB,SAAS,SAAS;QAC1D,MAAM,SAAS,IAAI;QACnB,IAAI;YACA,IAAK,IAAI,KAAK,MAAM,gBAAgB,cAAc,cAAc,iBAAiB,kBAAkB,MAAM,cAAc,IAAI,IAAI,KAAK,gBAAgB,IAAI,EAAE,CAAC,IAAI,KAAK,KAAM;gBACtK,KAAK,gBAAgB,KAAK;gBAC1B,KAAK;gBACL,MAAM,IAAI;gBACV,MAAM,QAAQ,OAAO,GAAG,CAAC,EAAE,MAAM,KAAK,IAAI;gBAC1C,OAAO,GAAG,CAAC,EAAE,MAAM,EAAE;uBAAI;oBAAO;iBAAE;YACtC;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,cAAc,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC;YACjE,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,OAAO;IACX;AACJ;AACA,QAAQ,cAAc,GAAG;AACzB,SAAS,oBAAoB,OAAO,EAAE,OAAO,EAAE,MAAM;IACjD,OAAO,iBAAiB,IAAI,EAAE,WAAW,UAAU;QAC/C,IAAI,IAAI,KAAK,IAAI;QACjB,MAAM,cAAc,eAAe,SAAS,eAAe,KAAK,MAAM,EAAE,SAAS;QACjF,IAAI;YACA,IAAK,IAAI,KAAK,MAAM,gBAAgB,cAAc,cAAc,iBAAiB,kBAAkB,MAAM,QAAQ,cAAc,IAAI,KAAK,KAAK,gBAAgB,IAAI,EAAE,CAAC,IAAI,KAAK,KAAM;gBAC/K,KAAK,gBAAgB,KAAK;gBAC1B,KAAK;gBACL,MAAM,IAAI;gBACV,EAAE,IAAI,GAAG,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG;oBAAE,GAAG,EAAE,MAAM;gBAAC;gBAChE,MAAM,MAAM,QAAQ,CAAC,GAAG,WAAW,aAAa,EAAE,EAAE,IAAI;YAC5D;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,cAAc,MAAM,GAAG,MAAM,QAAQ,GAAG,IAAI,CAAC;YACzE,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;IACJ;AACJ;AACA,QAAQ,mBAAmB,GAAG;AAC9B,SAAS,UAAU,MAAM,EAAE,OAAO,EAAE,MAAM;IACtC,OAAO,iBAAiB,IAAI,EAAE,WAAW,UAAU;QAC/C,IAAI,IAAI,KAAK,IAAI;QACjB,MAAM,SAAS,UAAU,KAAK,MAAM,EAAE,CAAC,WAAW,EAAE,OAAO,CAAC,EAAE,KAAK,MAAM,EAAE,EAAE,SAAS;QACtF,IAAI;YACA,IAAK,IAAI,KAAK,MAAM,WAAW,cAAc,SAAS,YAAY,aAAa,MAAM,QAAQ,SAAS,IAAI,KAAK,KAAK,WAAW,IAAI,EAAE,CAAC,IAAI,KAAK,KAAM;gBACjJ,KAAK,WAAW,KAAK;gBACrB,KAAK;gBACL,MAAM,QAAQ;gBACd,MAAM,MAAM,QAAQ,CAAC,GAAG,WAAW,aAAa,EAAE;YACtD;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,SAAS,MAAM,GAAG,MAAM,QAAQ,GAAG,IAAI,CAAC;YACpE,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;IACJ;AACJ;AACA,QAAQ,SAAS,GAAG;AACpB,SAAS,eAAe,OAAO,EAAE,OAAO,EAAE,MAAM;IAC5C,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,IAAI,IAAI,KAAK,IAAI;QACjB,MAAM,cAAc,oBAAoB,SAAS,SAAS;QAC1D,MAAM,SAAS,IAAI;QACnB,IAAI;YACA,IAAK,IAAI,KAAK,MAAM,gBAAgB,cAAc,cAAc,iBAAiB,kBAAkB,MAAM,cAAc,IAAI,IAAI,KAAK,gBAAgB,IAAI,EAAE,CAAC,IAAI,KAAK,KAAM;gBACtK,KAAK,gBAAgB,KAAK;gBAC1B,KAAK;gBACL,MAAM,IAAI;gBACV,MAAM,QAAQ,OAAO,GAAG,CAAC,EAAE,MAAM,KAAK,IAAI;gBAC1C,OAAO,GAAG,CAAC,EAAE,MAAM,EAAE;uBAAI;oBAAO;iBAAE;YACtC;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,cAAc,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC;YACjE,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,OAAO;IACX;AACJ;AACA,QAAQ,cAAc,GAAG;AACzB,SAAS,oBAAoB,OAAO,EAAE,OAAO,EAAE,MAAM;IACjD,OAAO,iBAAiB,IAAI,EAAE,WAAW,UAAU;QAC/C,IAAI,IAAI,KAAK,IAAI;QACjB,MAAM,cAAc,eAAe,SAAS,eAAe,KAAK,MAAM,EAAE,SAAS;QACjF,IAAI;YACA,IAAK,IAAI,KAAK,MAAM,gBAAgB,cAAc,cAAc,iBAAiB,kBAAkB,MAAM,QAAQ,cAAc,IAAI,KAAK,KAAK,gBAAgB,IAAI,EAAE,CAAC,IAAI,KAAK,KAAM;gBAC/K,KAAK,gBAAgB,KAAK;gBAC1B,KAAK;gBACL,MAAM,IAAI;gBACV,EAAE,IAAI,GAAG,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG;oBAAE,GAAG,EAAE,MAAM;gBAAC;gBAChE,MAAM,MAAM,QAAQ,CAAC,GAAG,WAAW,aAAa,EAAE,EAAE,IAAI;YAC5D;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,cAAc,MAAM,GAAG,MAAM,QAAQ,GAAG,IAAI,CAAC;YACzE,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;IACJ;AACJ;AACA,QAAQ,mBAAmB,GAAG;AAC9B,SAAS,QAAQ,MAAM,EAAE,OAAO,EAAE,MAAM;IACpC,OAAO,iBAAiB,IAAI,EAAE,WAAW,UAAU;QAC/C,IAAI,IAAI,KAAK,IAAI;QACjB,MAAM,OAAO,UAAU,KAAK,IAAI,EAAE,CAAC,WAAW,EAAE,OAAO,CAAC,EAAE,KAAK,IAAI,EAAE,EAAE,SAAS;QAChF,IAAI;YACA,IAAK,IAAI,KAAK,MAAM,KAAK,cAAc,QAAQ,EAAE,GAAG,IAAI,KAAK,MAAM,QAAQ,GAAG,IAAI,KAAK,KAAK,GAAG,IAAI,EAAE,CAAC,IAAI,KAAK,KAAM;gBACjH,KAAK,GAAG,KAAK;gBACb,KAAK;gBACL,MAAM,MAAM;gBACZ,MAAM,MAAM,QAAQ,CAAC,GAAG,WAAW,WAAW,EAAE;YACpD;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,MAAM,GAAG,MAAM,QAAQ,GAAG,IAAI,CAAC;YAC9D,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;IACJ;AACJ;AACA,QAAQ,OAAO,GAAG;AAClB,SAAS,aAAa,OAAO,EAAE,OAAO,EAAE,MAAM;IAC1C,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,IAAI,IAAI,KAAK,IAAI;QACjB,MAAM,YAAY,kBAAkB,SAAS,SAAS;QACtD,MAAM,OAAO,IAAI;QACjB,IAAI;YACA,IAAK,IAAI,KAAK,MAAM,cAAc,cAAc,YAAY,eAAe,gBAAgB,MAAM,YAAY,IAAI,IAAI,KAAK,cAAc,IAAI,EAAE,CAAC,IAAI,KAAK,KAAM;gBAC1J,KAAK,cAAc,KAAK;gBACxB,KAAK;gBACL,MAAM,IAAI;gBACV,MAAM,QAAQ,KAAK,GAAG,CAAC,EAAE,MAAM,KAAK,IAAI;gBACxC,KAAK,GAAG,CAAC,EAAE,MAAM,EAAE;uBAAI;oBAAO;iBAAE;YACpC;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,YAAY,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC;YAC/D,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,OAAO;IACX;AACJ;AACA,QAAQ,YAAY,GAAG;AACvB,SAAS,kBAAkB,OAAO,EAAE,OAAO,EAAE,MAAM;IAC/C,OAAO,iBAAiB,IAAI,EAAE,WAAW,UAAU;QAC/C,IAAI,IAAI,KAAK,IAAI;QACjB,MAAM,YAAY,eAAe,SAAS,eAAe,KAAK,IAAI,EAAE,SAAS;QAC7E,IAAI;YACA,IAAK,IAAI,KAAK,MAAM,cAAc,cAAc,YAAY,eAAe,gBAAgB,MAAM,QAAQ,YAAY,IAAI,KAAK,KAAK,cAAc,IAAI,EAAE,CAAC,IAAI,KAAK,KAAM;gBACnK,KAAK,cAAc,KAAK;gBACxB,KAAK;gBACL,MAAM,IAAI;gBACV,EAAE,IAAI,GAAG,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG;oBAAE,GAAG,EAAE,MAAM;gBAAC;gBAChE,MAAM,MAAM,QAAQ,CAAC,GAAG,WAAW,WAAW,EAAE,EAAE,IAAI;YAC1D;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,YAAY,MAAM,GAAG,MAAM,QAAQ,GAAG,IAAI,CAAC;YACvE,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;IACJ;AACJ;AACA,QAAQ,iBAAiB,GAAG;AAC5B,SAAS,eAAe,MAAM,EAAE,MAAM;IAClC,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,MAAM,OAAO,MAAM,kBAAkB,CAAC,WAAW,EAAE,OAAO,cAAc,CAAC,EAAE,CAAC,GAAG;QAC/E,OAAO,CAAC,GAAG,WAAW,aAAa,EAAE,KAAK,IAAI,CAAC,KAAK;IACxD;AACJ;AACA,QAAQ,cAAc,GAAG;AACzB,SAAS,gBAAgB,OAAO,EAAE,MAAM;IACpC,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,MAAM,OAAO,MAAM,kBAAkB,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC,OAAO,CAAC,EAAE;YAAE,SAAS,QAAQ,IAAI,CAAC;QAAK,GAAG;QACzG,MAAM,oBAAoB,KAAK,IAAI,CAAC,MAAM;QAC1C,MAAM,wBAAwB,IAAI;QAClC,IAAK,MAAM,UAAU,kBAAmB;YACpC,sBAAsB,GAAG,CAAC,QAAQ,CAAC,GAAG,WAAW,aAAa,EAAE,OAAO,MAAM,CAAC;gBAAE,GAAG;YAAO,GAAG,iBAAiB,CAAC,OAAO;QAC1H;QACA,OAAO;IACX;AACJ;AACA,QAAQ,eAAe,GAAG;AAC1B,SAAS,eAAe,MAAM,EAAE,MAAM;IAClC,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,MAAM,OAAO,MAAM,kBAAkB,CAAC,WAAW,EAAE,OAAO,cAAc,CAAC,EAAE,CAAC,GAAG;QAC/E,OAAO,CAAC,GAAG,WAAW,aAAa,EAAE,KAAK,IAAI,CAAC,KAAK;IACxD;AACJ;AACA,QAAQ,cAAc,GAAG;AACzB,SAAS,gBAAgB,OAAO,EAAE,MAAM;IACpC,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,MAAM,OAAO,MAAM,kBAAkB,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC,OAAO,CAAC,EAAE;YAAE,SAAS,QAAQ,IAAI,CAAC;QAAK,GAAG;QACzG,MAAM,oBAAoB,KAAK,IAAI,CAAC,MAAM;QAC1C,MAAM,wBAAwB,IAAI;QAClC,IAAK,MAAM,UAAU,kBAAmB;YACpC,sBAAsB,GAAG,CAAC,QAAQ,CAAC,GAAG,WAAW,aAAa,EAAE,OAAO,MAAM,CAAC;gBAAE,GAAG;YAAO,GAAG,iBAAiB,CAAC,OAAO;QAC1H;QACA,OAAO;IACX;AACJ;AACA,QAAQ,eAAe,GAAG;AAC1B,SAAS,aAAa,MAAM,EAAE,MAAM;IAChC,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,MAAM,OAAO,MAAM,kBAAkB,CAAC,WAAW,EAAE,OAAO,YAAY,CAAC,EAAE,CAAC,GAAG;QAC7E,OAAO,CAAC,GAAG,WAAW,WAAW,EAAE,KAAK,IAAI,CAAC,GAAG;IACpD;AACJ;AACA,QAAQ,YAAY,GAAG;AACvB,SAAS,cAAc,OAAO,EAAE,MAAM;IAClC,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,MAAM,OAAO,MAAM,kBAAkB,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,OAAO,CAAC,EAAE;YAAE,SAAS,QAAQ,IAAI,CAAC;QAAK,GAAG;QACvG,MAAM,kBAAkB,KAAK,IAAI,CAAC,IAAI;QACtC,MAAM,sBAAsB,IAAI;QAChC,IAAK,MAAM,UAAU,gBAAiB;YAClC,oBAAoB,GAAG,CAAC,QAAQ,CAAC,GAAG,WAAW,WAAW,EAAE,OAAO,MAAM,CAAC;gBAAE,GAAG;YAAO,GAAG,eAAe,CAAC,OAAO;QACpH;QACA,OAAO;IACX;AACJ;AACA,QAAQ,aAAa,GAAG;AACxB,SAAS,YAAY,MAAM,EAAE,MAAM;IAC/B,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,MAAM,OAAO,MAAM,kBAAkB,CAAC,WAAW,EAAE,OAAO,SAAS,CAAC,EAAE,CAAC,GAAG;QAC1E,OAAO,CAAC,GAAG,WAAW,gBAAgB,EAAE,KAAK,IAAI;IACrD;AACJ;AACA,QAAQ,WAAW,GAAG;AACtB,SAAS,aAAa,OAAO,EAAE,MAAM;IACjC,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,MAAM,OAAO,MAAM,kBAAkB,CAAC,6BAA6B,EAAE,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG;QAC9F,MAAM,SAAS,OAAO,OAAO,CAAC,KAAK,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,IAAI;YACpD,OAAO,CAAC,GAAG,WAAW,gBAAgB,EAAE,OAAO,MAAM,CAAC;gBAAE,QAAQ;YAAI,GAAG;QAC3E;QACA,OAAO;IACX;AACJ;AACA,QAAQ,YAAY,GAAG;AACvB,SAAS,gBAAgB,OAAO,EAAE,OAAO,EAAE,MAAM;IAC7C,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,IAAI,IAAI,MAAM,IAAI;QAClB,MAAM,eAAe,eAAe,SAAS,uBAAuB,KAAK,MAAM,EAAE,SAAS;QAC1F,MAAM,SAAS,IAAI;QACnB,IAAI;YACA,IAAK,IAAI,KAAK,MAAM,iBAAiB,cAAc,eAAe,kBAAkB,mBAAmB,MAAM,eAAe,IAAI,IAAI,KAAK,iBAAiB,IAAI,EAAE,CAAC,IAAI,KAAK,KAAM;gBAC5K,KAAK,iBAAiB,KAAK;gBAC3B,KAAK;gBACL,MAAM,IAAI;gBACV,MAAM,QAAQ,OAAO,GAAG,CAAC,EAAE,MAAM,KAAK,IAAI;gBAC1C,OAAO,GAAG,CAAC,EAAE,MAAM,EAAE;uBAAI;oBAAO,CAAC,GAAG,WAAW,iBAAiB,EAAE,EAAE,IAAI;iBAAE;YAC9E;QACJ,EACA,OAAO,QAAQ;YAAE,OAAO;gBAAE,OAAO;YAAO;QAAG,SACnC;YACJ,IAAI;gBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,eAAe,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC;YAClE,SACQ;gBAAE,IAAI,MAAM,MAAM,KAAK,KAAK;YAAE;QAC1C;QACA,OAAO;IACX;AACJ;AACA,QAAQ,eAAe,GAAG;AAC1B,SAAS,gBAAgB,OAAO,EAAE,OAAO,EAAE,MAAM;IAC7C,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,IAAI,IAAI,MAAM,IAAI;QAClB,MAAM,eAAe,eAAe,SAAS,uBAAuB,KAAK,MAAM,EAAE,SAAS;QAC1F,MAAM,SAAS,IAAI;QACnB,IAAI;YACA,IAAK,IAAI,KAAK,MAAM,iBAAiB,cAAc,eAAe,kBAAkB,mBAAmB,MAAM,eAAe,IAAI,IAAI,KAAK,iBAAiB,IAAI,EAAE,CAAC,IAAI,KAAK,KAAM;gBAC5K,KAAK,iBAAiB,KAAK;gBAC3B,KAAK;gBACL,MAAM,IAAI;gBACV,MAAM,QAAQ,OAAO,GAAG,CAAC,EAAE,MAAM,KAAK,IAAI;gBAC1C,OAAO,GAAG,CAAC,EAAE,MAAM,EAAE;uBAAI;oBAAO,CAAC,GAAG,WAAW,iBAAiB,EAAE,EAAE,IAAI;iBAAE;YAC9E;QACJ,EACA,OAAO,QAAQ;YAAE,OAAO;gBAAE,OAAO;YAAO;QAAG,SACnC;YACJ,IAAI;gBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,eAAe,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC;YAClE,SACQ;gBAAE,IAAI,MAAM,MAAM,KAAK,KAAK;YAAE;QAC1C;QACA,OAAO;IACX;AACJ;AACA,QAAQ,eAAe,GAAG;AAC1B,SAAS,cAAc,OAAO,EAAE,OAAO,EAAE,MAAM;IAC3C,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,IAAI,IAAI,MAAM,IAAI;QAClB,MAAM,aAAa,eAAe,SAAS,uBAAuB,KAAK,IAAI,EAAE,SAAS;QACtF,MAAM,OAAO,IAAI;QACjB,IAAI;YACA,IAAK,IAAI,KAAK,MAAM,eAAe,cAAc,aAAa,gBAAgB,iBAAiB,MAAM,aAAa,IAAI,IAAI,KAAK,eAAe,IAAI,EAAE,CAAC,IAAI,KAAK,KAAM;gBAChK,KAAK,eAAe,KAAK;gBACzB,KAAK;gBACL,MAAM,IAAI;gBACV,MAAM,QAAQ,KAAK,GAAG,CAAC,EAAE,MAAM,KAAK,IAAI;gBACxC,KAAK,GAAG,CAAC,EAAE,MAAM,EAAE;uBAAI;oBAAO,CAAC,GAAG,WAAW,eAAe,EAAE,EAAE,IAAI;iBAAE;YAC1E;QACJ,EACA,OAAO,QAAQ;YAAE,OAAO;gBAAE,OAAO;YAAO;QAAG,SACnC;YACJ,IAAI;gBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,aAAa,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC;YAChE,SACQ;gBAAE,IAAI,MAAM,MAAM,KAAK,KAAK;YAAE;QAC1C;QACA,OAAO;IACX;AACJ;AACA,QAAQ,aAAa,GAAG;AACxB,SAAS,oBAAoB,OAAO,EAAE,MAAM;IACxC,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,MAAM,SAAS;YAAE,SAAS,QAAQ,IAAI,CAAC;QAAK;QAC5C,MAAM,OAAO,MAAM,kBAAkB,CAAC,8BAA8B,CAAC,EAAE,QAAQ;QAC/E,MAAM,wBAAwB,KAAK,IAAI,CAAC,IAAI;QAC5C,MAAM,SAAS,IAAI;QACnB,IAAK,MAAM,UAAU,sBAAuB;YACxC,MAAM,MAAM,qBAAqB,CAAC,OAAO;YACzC,OAAO,GAAG,CAAC,QAAQ,CAAC,GAAG,WAAW,eAAe,EAAE;QACvD;QACA,OAAO;IACX;AACJ;AACA,QAAQ,mBAAmB,GAAG;AAC9B,SAAS,sBAAsB,OAAO,EAAE,MAAM;IAC1C,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,MAAM,SAAS;YAAE,SAAS,QAAQ,IAAI,CAAC;QAAK;QAC5C,MAAM,OAAO,MAAM,kBAAkB,CAAC,gCAAgC,CAAC,EAAE,QAAQ;QACjF,MAAM,0BAA0B,KAAK,IAAI,CAAC,MAAM;QAChD,MAAM,SAAS,IAAI;QACnB,IAAK,MAAM,UAAU,wBAAyB;YAC1C,MAAM,QAAQ,uBAAuB,CAAC,OAAO;YAC7C,OAAO,GAAG,CAAC,QAAQ,CAAC,GAAG,WAAW,iBAAiB,EAAE;QACzD;QACA,OAAO;IACX;AACJ;AACA,QAAQ,qBAAqB,GAAG;AAChC,SAAS,sBAAsB,OAAO,EAAE,MAAM;IAC1C,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,MAAM,SAAS;YAAE,SAAS,QAAQ,IAAI,CAAC;QAAK;QAC5C,MAAM,OAAO,MAAM,kBAAkB,CAAC,gCAAgC,CAAC,EAAE,QAAQ;QACjF,MAAM,0BAA0B,KAAK,IAAI,CAAC,MAAM;QAChD,MAAM,SAAS,IAAI;QACnB,IAAK,MAAM,UAAU,wBAAyB;YAC1C,MAAM,QAAQ,uBAAuB,CAAC,OAAO;YAC7C,OAAO,GAAG,CAAC,QAAQ,CAAC,GAAG,WAAW,iBAAiB,EAAE;QACzD;QACA,OAAO;IACX;AACJ;AACA,QAAQ,qBAAqB,GAAG;AAChC,SAAS,mBAAmB,OAAO,EAAE,MAAM;IACvC,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,MAAM,SAAS;YAAE,SAAS,QAAQ,IAAI,CAAC;QAAK;QAC5C,MAAM,OAAO,MAAM,kBAAkB,CAAC,4BAA4B,CAAC,EAAE,QAAQ;QAC7E,MAAM,YAAY,KAAK,IAAI,CAAC,SAAS;QACrC,MAAM,SAAS,IAAI;QACnB,IAAK,MAAM,UAAU,UAAW;YAC5B,MAAM,WAAW,SAAS,CAAC,OAAO;YAClC,OAAO,GAAG,CAAC,QAAQ,CAAC,GAAG,WAAW,oBAAoB,EAAE;QAC5D;QACA,OAAO;IACX;AACJ;AACA,QAAQ,kBAAkB,GAAG;AAC7B,SAAS,0BAA0B,OAAO,EAAE,MAAM;IAC9C,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,MAAM,SAAS;YAAE,SAAS,QAAQ,IAAI,CAAC;QAAK;QAC5C,MAAM,OAAO,MAAM,kBAAkB,CAAC,oCAAoC,CAAC,EAAE,QAAQ;QACrF,MAAM,aAAa,KAAK,IAAI,CAAC,UAAU;QACvC,MAAM,SAAS,IAAI;QACnB,IAAK,MAAM,UAAU,WAAY;YAC7B,MAAM,YAAY,UAAU,CAAC,OAAO;YACpC,OAAO,GAAG,CAAC,QAAQ,CAAC,GAAG,WAAW,qBAAqB,EAAE;QAC7D;QACA,OAAO;IACX;AACJ;AACA,QAAQ,yBAAyB,GAAG;AACpC,IAAI;AACJ,CAAC,SAAU,IAAI;IACX,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,OAAO,GAAG;AACnB,CAAC,EAAE,QAAQ,CAAC,QAAQ,IAAI,GAAG,OAAO,CAAC,CAAC;AACpC,SAAS,cAAc,OAAO;IAC1B,IAAI;IACJ,MAAM,QAAQ,CAAC;IACf,MAAM,OAAO,GAAG,CAAC,CAAC,KAAK,QAAQ,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,IAAI,IAAI,QAAQ,OAAO,CAAC,IAAI,CAAC,OAAO;IAC1H,MAAM,KAAK,GAAG,QAAQ,KAAK;IAC3B,MAAM,GAAG,GAAG,QAAQ,GAAG;IACvB,MAAM,IAAI,GAAG,QAAQ,IAAI;IACzB,MAAM,eAAe,GAAG,QAAQ,cAAc;IAC9C,MAAM,mBAAmB,GAAG,QAAQ,kBAAkB;IACtD,OAAO;AACX;AACA,SAAS,QAAQ,OAAO,EAAE,MAAM;IAC5B,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,IAAI;QACJ,IAAI,QAAQ,UAAU,IAAI,QAAQ,UAAU,GAAG,GAAG;YAC9C,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,QAAQ,SAAS,IAAI,QAAQ,SAAS,GAAG,GAAG;YAC5C,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,YAAY;QAChB,IAAI,WAAW;QACf,MAAM,YAAY,CAAC,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,SAAS,IAChF,KAAK,GAAG,CAAC,QAAQ,SAAS,EAAE,qBAC5B;QACN,YAAY,QAAQ,YAAY,KAAK,IAAI,OAAO,OAAO,QAAQ,SAAS;QACxE,MAAM,aAAa,CAAC,KAAK,QAAQ,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QAC9E,MAAM,SAAS,EAAE;QACjB,MAAM,SAAS,cAAc;QAC7B,IAAI;QACJ,OAAS;YACL,QAAQ,cAAc,YAAY,WAAW;YAC7C,IAAI,QAAQ,GAAG;gBACX;YACJ;YACA,MAAM,OAAO,MAAM,kBAAkB,iBAAiB,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,SAAS;gBAAE,OAAO;gBAAO,YAAY;YAAU,IAAI;YACzI,KAAK,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAM,OAAO,IAAI,CAAC,CAAC,GAAG,WAAW,UAAU,EAAE;YACrE,YAAY,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM;YACjC,YAAY,KAAK,IAAI,CAAC,eAAe;YACrC,IAAI,CAAC,WAAW;gBACZ;YACJ;QACJ;QACA,OAAO;IACX;AACJ;AACA,QAAQ,OAAO,GAAG;AAClB,SAAS,mBAAmB,OAAO,EAAE,OAAO,EAAE,MAAM;IAChD,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,IAAI,IAAI,MAAM,IAAI;QAClB,MAAM,YAAY,wBAAwB,SAAS,SAAS;QAC5D,MAAM,OAAO,IAAI;QACjB,IAAI;YACA,IAAK,IAAI,KAAK,MAAM,cAAc,cAAc,YAAY,eAAe,gBAAgB,MAAM,YAAY,IAAI,IAAI,KAAK,cAAc,IAAI,EAAE,CAAC,IAAI,KAAK,KAAM;gBAC1J,KAAK,cAAc,KAAK;gBACxB,KAAK;gBACL,MAAM,IAAI;gBACV,kCAAkC;gBAClC,IAAI,SAAS,EAAE,MAAM,GAAG,EAAE,MAAM,GAAG;gBACnC,OAAO,EAAE,MAAM;gBACf,MAAM,QAAQ,KAAK,GAAG,CAAC,WAAW,IAAI;gBACtC,KAAK,GAAG,CAAC,QAAQ;uBAAI;oBAAO;iBAAE;YAClC;QACJ,EACA,OAAO,QAAQ;YAAE,OAAO;gBAAE,OAAO;YAAO;QAAG,SACnC;YACJ,IAAI;gBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,YAAY,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC;YAC/D,SACQ;gBAAE,IAAI,MAAM,MAAM,KAAK,KAAK;YAAE;QAC1C;QACA,OAAO;IACX;AACJ;AACA,QAAQ,kBAAkB,GAAG;AAC7B,SAAS,wBAAwB,OAAO,EAAE,OAAO,EAAE,MAAM;IACrD,OAAO,iBAAiB,IAAI,EAAE,WAAW,UAAU;QAC/C,IAAI,IAAI,MAAM,IAAI;QAClB,MAAM,YAAY,eAAe,SAAS,qBAAqB,KAAK,IAAI,EAAE,SAAS;QACnF,IAAI;YACA,IAAK,IAAI,KAAK,MAAM,cAAc,cAAc,YAAY,eAAe,gBAAgB,MAAM,QAAQ,YAAY,IAAI,KAAK,KAAK,cAAc,IAAI,EAAE,CAAC,IAAI,KAAK,KAAM;gBACnK,KAAK,cAAc,KAAK;gBACxB,KAAK;gBACL,MAAM,IAAI;gBACV,EAAE,IAAI,GAAG,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG;oBAAE,GAAG,EAAE,MAAM;gBAAC;gBAChE,MAAM,MAAM,QAAQ,CAAC,GAAG,WAAW,sBAAsB,EAAE,EAAE,IAAI;YACrE;QACJ,EACA,OAAO,QAAQ;YAAE,OAAO;gBAAE,OAAO;YAAO;QAAG,SACnC;YACJ,IAAI;gBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,YAAY,MAAM,GAAG,MAAM,QAAQ,GAAG,IAAI,CAAC;YACvE,SACQ;gBAAE,IAAI,MAAM,MAAM,KAAK,KAAK;YAAE;QAC1C;IACJ;AACJ;AACA,QAAQ,uBAAuB,GAAG;AAClC,SAAS,qBAAqB,OAAO,EAAE,OAAO,EAAE,MAAM;IAClD,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,IAAI,IAAI,MAAM,IAAI;QAClB,MAAM,cAAc,0BAA0B,SAAS,SAAS;QAChE,MAAM,SAAS,IAAI;QACnB,IAAI;YACA,IAAK,IAAI,KAAK,MAAM,gBAAgB,cAAc,cAAc,iBAAiB,kBAAkB,MAAM,cAAc,IAAI,IAAI,KAAK,gBAAgB,IAAI,EAAE,CAAC,IAAI,KAAK,KAAM;gBACtK,KAAK,gBAAgB,KAAK;gBAC1B,KAAK;gBACL,MAAM,IAAI;gBACV,kCAAkC;gBAClC,IAAI,SAAS,EAAE,MAAM,GAAG,EAAE,MAAM,GAAG;gBACnC,OAAO,EAAE,MAAM;gBACf,MAAM,QAAQ,OAAO,GAAG,CAAC,WAAW,IAAI;gBACxC,OAAO,GAAG,CAAC,QAAQ;uBAAI;oBAAO;iBAAE;YACpC;QACJ,EACA,OAAO,QAAQ;YAAE,OAAO;gBAAE,OAAO;YAAO;QAAG,SACnC;YACJ,IAAI;gBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,cAAc,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC;YACjE,SACQ;gBAAE,IAAI,MAAM,MAAM,KAAK,KAAK;YAAE;QAC1C;QACA,OAAO;IACX;AACJ;AACA,QAAQ,oBAAoB,GAAG;AAC/B,SAAS,0BAA0B,OAAO,EAAE,OAAO,EAAE,MAAM;IACvD,OAAO,iBAAiB,IAAI,EAAE,WAAW,UAAU;QAC/C,IAAI,IAAI,MAAM,IAAI;QAClB,MAAM,YAAY,eAAe,SAAS,qBAAqB,KAAK,MAAM,EAAE,SAAS;QACrF,IAAI;YACA,IAAK,IAAI,KAAK,MAAM,cAAc,cAAc,YAAY,eAAe,gBAAgB,MAAM,QAAQ,YAAY,IAAI,KAAK,KAAK,cAAc,IAAI,EAAE,CAAC,IAAI,KAAK,KAAM;gBACnK,KAAK,cAAc,KAAK;gBACxB,KAAK;gBACL,MAAM,IAAI;gBACV,EAAE,IAAI,GAAG,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG;oBAAE,GAAG,EAAE,MAAM;gBAAC;gBAChE,MAAM,MAAM,QAAQ,CAAC,GAAG,WAAW,wBAAwB,EAAE,EAAE,IAAI;YACvE;QACJ,EACA,OAAO,QAAQ;YAAE,OAAO;gBAAE,OAAO;YAAO;QAAG,SACnC;YACJ,IAAI;gBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,YAAY,MAAM,GAAG,MAAM,QAAQ,GAAG,IAAI,CAAC;YACvE,SACQ;gBAAE,IAAI,MAAM,MAAM,KAAK,KAAK;YAAE;QAC1C;IACJ;AACJ;AACA,QAAQ,yBAAyB,GAAG;AACpC,SAAS,sBAAsB,OAAO,EAAE,MAAM;IAC1C,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,MAAM,OAAO,MAAM,kBAAkB,CAAC,iBAAiB,EAAE,KAAK,MAAM,CAAC,OAAO,CAAC,EAAE;YAAE,SAAS,QAAQ,IAAI,CAAC;QAAK,GAAG;QAC/G,MAAM,oBAAoB,KAAK,IAAI,CAAC,MAAM;QAC1C,MAAM,wBAAwB,IAAI;QAClC,IAAK,MAAM,UAAU,kBAAmB;YACpC,sBAAsB,GAAG,CAAC,QAAQ,CAAC,GAAG,WAAW,wBAAwB,EAAE,OAAO,MAAM,CAAC,CAAC,GAAG,iBAAiB,CAAC,OAAO;QAC1H;QACA,OAAO;IACX;AACJ;AACA,QAAQ,qBAAqB,GAAG;AAChC,SAAS,sBAAsB,OAAO,EAAE,MAAM;IAC1C,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,MAAM,OAAO,MAAM,kBAAkB,CAAC,iBAAiB,EAAE,KAAK,MAAM,CAAC,OAAO,CAAC,EAAE;YAAE,SAAS,QAAQ,IAAI,CAAC;QAAK,GAAG;QAC/G,MAAM,oBAAoB,KAAK,IAAI,CAAC,MAAM;QAC1C,MAAM,wBAAwB,IAAI;QAClC,IAAK,MAAM,UAAU,kBAAmB;YACpC,sBAAsB,GAAG,CAAC,QAAQ,CAAC,GAAG,WAAW,wBAAwB,EAAE,OAAO,MAAM,CAAC,CAAC,GAAG,iBAAiB,CAAC,OAAO;QAC1H;QACA,OAAO;IACX;AACJ;AACA,QAAQ,qBAAqB,GAAG;AAChC,SAAS,mBAAmB,OAAO,EAAE,MAAM;IACvC,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,MAAM,OAAO,MAAM,kBAAkB,CAAC,mCAAmC,EAAE,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG;QACpG,MAAM,SAAS,OAAO,OAAO,CAAC,KAAK,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,IAAI;YAC9D,OAAO,CAAC,GAAG,WAAW,2BAA2B,EAAE,OAAO,MAAM,CAAC;gBAAE,QAAQ;YAAI,GAAG;QACtF;QACA,OAAO;IACX;AACJ;AACA,QAAQ,kBAAkB,GAAG;AAC7B,SAAS,eAAe,gBAAgB,EAAE,OAAO,EAAE,MAAM;IACrD,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,IAAI;QACJ,IAAI,QAAQ,UAAU,IAAI,QAAQ,UAAU,GAAG,GAAG;YAC9C,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,QAAQ,SAAS,IAAI,QAAQ,SAAS,GAAG,GAAG;YAC5C,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,YAAY;QAChB,IAAI,WAAW;QACf,MAAM,YAAY,CAAC,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,SAAS,IAChF,KAAK,GAAG,CAAC,QAAQ,SAAS,EAAE,sBAC5B;QACN,OAAO,QAAQ,SAAS;QACxB,MAAM,aAAa,CAAC,KAAK,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QAChI,OAAO,QAAQ,UAAU;QACzB,MAAM,SAAS,EAAE;QACjB,IAAI;QACJ,OAAS;YACL,QAAQ,cAAc,YAAY,WAAW;YAC7C,IAAI,QAAQ,GAAG;gBACX;YACJ;YACA,MAAM,OAAO,MAAM,kBAAkB,CAAC,2BAA2B,EAAE,kBAAkB,EAAE,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,UAAU;gBAAE,OAAO;gBAAO,YAAY;YAAU,IAAI;YAC3K,MAAM,MAAM,OAAO,OAAO,CAAC,KAAK,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,IAAI;gBAC3D,OAAO,CAAC,GAAG,WAAW,2BAA2B,EAAE,OAAO,MAAM,CAAC;oBAAE,QAAQ;gBAAI,GAAG;YACtF;YACA,WAAW,WAAW,IAAI,MAAM;YAChC,OAAO,IAAI,IAAI;YACf,YAAY,KAAK,IAAI,CAAC,eAAe;YACrC,IAAI,CAAC,WAAW;gBACZ;YACJ;QACJ;QACA,OAAO;IACX;AACJ;AACA,QAAQ,cAAc,GAAG;AACzB,SAAS,oBAAoB,OAAO,EAAE,OAAO,EAAE,MAAM;IACjD,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,IAAI,IAAI;QACR,IAAI,QAAQ,UAAU,IAAI,QAAQ,UAAU,GAAG,GAAG;YAC9C,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,QAAQ,SAAS,IAAI,QAAQ,SAAS,GAAG,GAAG;YAC5C,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,YAAY;QAChB,IAAI,WAAW;QACf,MAAM,YAAY,CAAC,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,SAAS,IAChF,KAAK,GAAG,CAAC,QAAQ,SAAS,EAAE,sBAC5B;QACN,YAAY,QAAQ,YAAY,KAAK,IAAI,OAAO,OAAO,QAAQ,SAAS;QACxE,MAAM,aAAa,CAAC,KAAK,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QAChI,OAAO,QAAQ,UAAU;QACzB,IAAI,SAAS,CAAC;QACd,MAAM,QAAQ,CAAC,KAAK,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC;QAClI,MAAM,SAAS,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,UAAU;YAAE;YAAS;QAAM;QAC1E,IAAI;QACJ,OAAS;YACL,QAAQ,cAAc,YAAY,WAAW;YAC7C,IAAI,QAAQ,GAAG;gBACX;YACJ;YACA,MAAM,OAAO,MAAM,kBAAkB,CAAC,0BAA0B,CAAC,EAAE,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,SAAS;gBAAE,OAAO;gBAAO,YAAY;YAAU,IAAI;YACtJ,MAAM,MAAM,CAAC,GAAG,WAAW,uBAAuB,EAAE,KAAK,IAAI,CAAC,iBAAiB;YAC/E,SAAS,CAAC,GAAG,WAAW,qBAAqB,EAAE,QAAQ;YACvD,YAAY,CAAC,GAAG,WAAW,uBAAuB,EAAE;YACpD,YAAY,KAAK,IAAI,CAAC,eAAe;YACrC,IAAI,CAAC,WAAW;gBACZ;YACJ;QACJ;QACA,OAAO;IACX;AACJ;AACA,QAAQ,mBAAmB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1894, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/node_modules/%40alpacahq/alpaca-trade-api/dist/resources/datav2/websocket.js"], "sourcesContent": ["\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AlpacaWebsocket = exports.ERROR = exports.CONN_ERROR = exports.EVENT = exports.STATE = void 0;\nconst events_1 = __importDefault(require(\"events\"));\nconst ws_1 = __importDefault(require(\"ws\"));\nconst msgpack5_1 = __importDefault(require(\"msgpack5\"));\n// Connection states. Each of these will also emit EVENT.STATE_CHANGE\nvar STATE;\n(function (STATE) {\n    STATE[\"AUTHENTICATING\"] = \"authenticating\";\n    STATE[\"AUTHENTICATED\"] = \"authenticated\";\n    STATE[\"CONNECTED\"] = \"connected\";\n    STATE[\"CONNECTING\"] = \"connecting\";\n    STATE[\"DISCONNECTED\"] = \"disconnected\";\n    STATE[\"WAITING_TO_CONNECT\"] = \"waiting to connect\";\n    STATE[\"WAITING_TO_RECONNECT\"] = \"waiting to reconnect\";\n})(STATE || (exports.STATE = STATE = {}));\n// Client events\nvar EVENT;\n(function (EVENT) {\n    EVENT[\"CLIENT_ERROR\"] = \"client_error\";\n    EVENT[\"STATE_CHANGE\"] = \"state_change\";\n    EVENT[\"AUTHORIZED\"] = \"authorized\";\n    EVENT[\"UNAUTHORIZED\"] = \"unauthorized\";\n    EVENT[\"TRADES\"] = \"stock_trades\";\n    EVENT[\"QUOTES\"] = \"stock_quotes\";\n    EVENT[\"BARS\"] = \"stock_bars\";\n    EVENT[\"UPDATED_BARS\"] = \"stock_updated_bars\";\n    EVENT[\"DAILY_BARS\"] = \"stock_daily_bars\";\n    EVENT[\"TRADING_STATUSES\"] = \"trading_statuses\";\n    EVENT[\"LULDS\"] = \"lulds\";\n    EVENT[\"CANCEL_ERRORS\"] = \"cancel_errors\";\n    EVENT[\"CORRECTIONS\"] = \"corrections\";\n    EVENT[\"ORDERBOOKS\"] = \"orderbooks\";\n    EVENT[\"NEWS\"] = \"news\";\n})(EVENT || (exports.EVENT = EVENT = {}));\n// Connection errors by code\nexports.CONN_ERROR = new Map([\n    [400, \"invalid syntax\"],\n    [401, \"not authenticated\"],\n    [402, \"auth failed\"],\n    [403, \"already authenticated\"],\n    [404, \"auth timeout\"],\n    [405, \"symbol limit exceeded\"],\n    [406, \"connection limit exceeded\"],\n    [407, \"slow client\"],\n    [408, \"v2 not enabled\"],\n    [409, \"insufficient subscription\"],\n    [500, \"internal error\"],\n]);\n// Connection errors without code\nvar ERROR;\n(function (ERROR) {\n    ERROR[\"MISSING_SECERT_KEY\"] = \"missing secret key\";\n    ERROR[\"MISSING_API_KEY\"] = \"missing api key\";\n    ERROR[\"UNEXPECTED_MESSAGE\"] = \"unexpected message\";\n})(ERROR || (exports.ERROR = ERROR = {}));\nclass AlpacaWebsocket extends events_1.default.EventEmitter {\n    constructor(options) {\n        super();\n        this.msgpack = (0, msgpack5_1.default)();\n        this.session = {\n            apiKey: options.apiKey,\n            secretKey: options.secretKey,\n            subscriptions: options.subscriptions,\n            reconnect: true,\n            verbose: options.verbose,\n            backoff: true,\n            reconnectTimeout: 0,\n            maxReconnectTimeout: 30,\n            backoffIncrement: 0.5,\n            url: options.url,\n            currentState: STATE.WAITING_TO_CONNECT,\n            isReconnected: false,\n            pongWait: 5000,\n        };\n        // Register internal event handlers\n        // Log and emit every state change\n        Object.values(STATE).forEach((s) => {\n            this.on(s, () => {\n                this.emit(EVENT.STATE_CHANGE, s);\n            });\n        });\n    }\n    connect() {\n        this.emit(STATE.CONNECTING);\n        this.session.currentState = STATE.CONNECTING;\n        // Check the credentials\n        if (this.session.apiKey.length === 0) {\n            throw new Error(ERROR.MISSING_API_KEY);\n        }\n        if (this.session.secretKey.length === 0) {\n            throw new Error(ERROR.MISSING_SECERT_KEY);\n        }\n        this.resetSession();\n        this.conn = new ws_1.default(this.session.url, {\n            perMessageDeflate: {\n                serverNoContextTakeover: false,\n                clientNoContextTakeover: false,\n            },\n            headers: {\n                \"Content-Type\": \"application/msgpack\",\n            },\n        });\n        this.conn.binaryType = \"nodebuffer\";\n        this.conn.once(\"open\", () => this.authenticate());\n        this.conn.on(\"message\", (data) => {\n            this.handleMessage(this.msgpack.decode(data));\n        });\n        this.conn.on(\"error\", (err) => {\n            this.emit(EVENT.CLIENT_ERROR, err.message);\n            this.disconnect();\n        });\n        this.conn.on(\"close\", (code, msg) => {\n            this.log(`connection closed with code: ${code} and message: ${msg}`);\n            if (this.session.reconnect) {\n                this.reconnect();\n            }\n        });\n        this.conn.on(\"pong\", () => {\n            if (this.session.pongTimeout) {\n                clearTimeout(this.session.pongTimeout);\n            }\n        });\n        this.session.pingInterval = setInterval(() => {\n            this.ping();\n        }, 10000);\n        this.on(STATE.WAITING_TO_RECONNECT, (ts) => {\n            this.log(`backoff: ${ts}`);\n        });\n    }\n    onConnect(fn) {\n        this.on(STATE.AUTHENTICATED, () => {\n            if (this.session.isReconnected) {\n                //if reconnected the user should subscribe to its symbols again\n                this.subscribeAll();\n            }\n            else {\n                fn();\n            }\n        });\n    }\n    reconnect() {\n        this.log(\"Reconnecting...\");\n        this.session.isReconnected = true;\n        const { backoff, backoffIncrement, maxReconnectTimeout } = this.session;\n        let reconnectTimeout = this.session.reconnectTimeout;\n        if (backoff) {\n            setTimeout(() => {\n                reconnectTimeout += backoffIncrement;\n                if (reconnectTimeout > maxReconnectTimeout) {\n                    reconnectTimeout = maxReconnectTimeout;\n                }\n                this.emit(STATE.WAITING_TO_RECONNECT, reconnectTimeout);\n                this.connect();\n            }, reconnectTimeout * 1000);\n        }\n    }\n    ping() {\n        this.conn.ping();\n        this.session.pongTimeout = setTimeout(() => {\n            this.log(\"no pong received from server, terminating...\");\n            this.conn.terminate();\n        }, this.session.pongWait);\n    }\n    authenticate() {\n        const authMsg = {\n            action: \"auth\",\n            key: this.session.apiKey,\n            secret: this.session.secretKey,\n        };\n        this.conn.send(this.msgpack.encode(authMsg));\n        this.emit(STATE.AUTHENTICATING);\n        this.session.currentState = STATE.AUTHENTICATING;\n    }\n    disconnect() {\n        this.emit(STATE.DISCONNECTED);\n        this.session.currentState = STATE.DISCONNECTED;\n        this.conn.close();\n        this.session.reconnect = false;\n        if (this.session.pongTimeout) {\n            clearTimeout(this.session.pongTimeout);\n        }\n        if (this.session.pingInterval) {\n            clearInterval(this.session.pingInterval);\n        }\n    }\n    onDisconnect(fn) {\n        this.on(STATE.DISCONNECTED, () => fn());\n    }\n    onError(fn) {\n        this.on(EVENT.CLIENT_ERROR, (err) => fn(err));\n    }\n    onStateChange(fn) {\n        this.on(EVENT.STATE_CHANGE, (newState) => fn(newState));\n    }\n    handleMessage(data) {\n        const msgType = (data === null || data === void 0 ? void 0 : data.length) ? data[0].T : \"\";\n        switch (msgType) {\n            case \"success\":\n                if (data[0].msg === \"connected\") {\n                    this.emit(STATE.CONNECTED);\n                    this.session.currentState = STATE.CONNECTED;\n                }\n                else if (data[0].msg === \"authenticated\") {\n                    this.emit(STATE.AUTHENTICATED);\n                    this.session.currentState = STATE.AUTHENTICATED;\n                }\n                break;\n            case \"subscription\":\n                this.updateSubscriptions(data[0]);\n                break;\n            case \"error\":\n                this.emit(EVENT.CLIENT_ERROR, exports.CONN_ERROR.get(data[0].code));\n                break;\n            default:\n                this.dataHandler(data);\n        }\n    }\n    log(msg) {\n        if (this.session.verbose) {\n            // eslint-disable-next-line no-console\n            console.log(msg);\n        }\n    }\n    getSubscriptions() {\n        return this.session.subscriptions;\n    }\n    resetSession() {\n        this.session.reconnect = true;\n        this.session.backoff = true;\n        this.session.reconnectTimeout = 0;\n        this.session.maxReconnectTimeout = 30;\n        this.session.backoffIncrement = 0.5;\n        if (this.session.pongTimeout) {\n            clearTimeout(this.session.pongTimeout);\n        }\n        if (this.session.pingInterval) {\n            clearInterval(this.session.pingInterval);\n        }\n    }\n}\nexports.AlpacaWebsocket = AlpacaWebsocket;\n"], "names": [], "mappings": "AACA,IAAI,kBAAkB,4DAAS,yDAAK,eAAe,IAAK,SAAU,GAAG;IACjE,OAAO,AAAC,OAAO,IAAI,UAAU,GAAI,MAAM;QAAE,WAAW;IAAI;AAC5D;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,eAAe,GAAG,QAAQ,KAAK,GAAG,QAAQ,UAAU,GAAG,QAAQ,KAAK,GAAG,QAAQ,KAAK,GAAG,KAAK;AACpG,MAAM,WAAW;AACjB,MAAM,OAAO;AACb,MAAM,aAAa;AACnB,qEAAqE;AACrE,IAAI;AACJ,CAAC,SAAU,KAAK;IACZ,KAAK,CAAC,iBAAiB,GAAG;IAC1B,KAAK,CAAC,gBAAgB,GAAG;IACzB,KAAK,CAAC,YAAY,GAAG;IACrB,KAAK,CAAC,aAAa,GAAG;IACtB,KAAK,CAAC,eAAe,GAAG;IACxB,KAAK,CAAC,qBAAqB,GAAG;IAC9B,KAAK,CAAC,uBAAuB,GAAG;AACpC,CAAC,EAAE,SAAS,CAAC,QAAQ,KAAK,GAAG,QAAQ,CAAC,CAAC;AACvC,gBAAgB;AAChB,IAAI;AACJ,CAAC,SAAU,KAAK;IACZ,KAAK,CAAC,eAAe,GAAG;IACxB,KAAK,CAAC,eAAe,GAAG;IACxB,KAAK,CAAC,aAAa,GAAG;IACtB,KAAK,CAAC,eAAe,GAAG;IACxB,KAAK,CAAC,SAAS,GAAG;IAClB,KAAK,CAAC,SAAS,GAAG;IAClB,KAAK,CAAC,OAAO,GAAG;IAChB,KAAK,CAAC,eAAe,GAAG;IACxB,KAAK,CAAC,aAAa,GAAG;IACtB,KAAK,CAAC,mBAAmB,GAAG;IAC5B,KAAK,CAAC,QAAQ,GAAG;IACjB,KAAK,CAAC,gBAAgB,GAAG;IACzB,KAAK,CAAC,cAAc,GAAG;IACvB,KAAK,CAAC,aAAa,GAAG;IACtB,KAAK,CAAC,OAAO,GAAG;AACpB,CAAC,EAAE,SAAS,CAAC,QAAQ,KAAK,GAAG,QAAQ,CAAC,CAAC;AACvC,4BAA4B;AAC5B,QAAQ,UAAU,GAAG,IAAI,IAAI;IACzB;QAAC;QAAK;KAAiB;IACvB;QAAC;QAAK;KAAoB;IAC1B;QAAC;QAAK;KAAc;IACpB;QAAC;QAAK;KAAwB;IAC9B;QAAC;QAAK;KAAe;IACrB;QAAC;QAAK;KAAwB;IAC9B;QAAC;QAAK;KAA4B;IAClC;QAAC;QAAK;KAAc;IACpB;QAAC;QAAK;KAAiB;IACvB;QAAC;QAAK;KAA4B;IAClC;QAAC;QAAK;KAAiB;CAC1B;AACD,iCAAiC;AACjC,IAAI;AACJ,CAAC,SAAU,KAAK;IACZ,KAAK,CAAC,qBAAqB,GAAG;IAC9B,KAAK,CAAC,kBAAkB,GAAG;IAC3B,KAAK,CAAC,qBAAqB,GAAG;AAClC,CAAC,EAAE,SAAS,CAAC,QAAQ,KAAK,GAAG,QAAQ,CAAC,CAAC;AACvC,MAAM,wBAAwB,SAAS,OAAO,CAAC,YAAY;IACvD,YAAY,OAAO,CAAE;QACjB,KAAK;QACL,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,WAAW,OAAO;QACrC,IAAI,CAAC,OAAO,GAAG;YACX,QAAQ,QAAQ,MAAM;YACtB,WAAW,QAAQ,SAAS;YAC5B,eAAe,QAAQ,aAAa;YACpC,WAAW;YACX,SAAS,QAAQ,OAAO;YACxB,SAAS;YACT,kBAAkB;YAClB,qBAAqB;YACrB,kBAAkB;YAClB,KAAK,QAAQ,GAAG;YAChB,cAAc,MAAM,kBAAkB;YACtC,eAAe;YACf,UAAU;QACd;QACA,mCAAmC;QACnC,kCAAkC;QAClC,OAAO,MAAM,CAAC,OAAO,OAAO,CAAC,CAAC;YAC1B,IAAI,CAAC,EAAE,CAAC,GAAG;gBACP,IAAI,CAAC,IAAI,CAAC,MAAM,YAAY,EAAE;YAClC;QACJ;IACJ;IACA,UAAU;QACN,IAAI,CAAC,IAAI,CAAC,MAAM,UAAU;QAC1B,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,MAAM,UAAU;QAC5C,wBAAwB;QACxB,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,KAAK,GAAG;YAClC,MAAM,IAAI,MAAM,MAAM,eAAe;QACzC;QACA,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,KAAK,GAAG;YACrC,MAAM,IAAI,MAAM,MAAM,kBAAkB;QAC5C;QACA,IAAI,CAAC,YAAY;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;YAC3C,mBAAmB;gBACf,yBAAyB;gBACzB,yBAAyB;YAC7B;YACA,SAAS;gBACL,gBAAgB;YACpB;QACJ;QACA,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG;QACvB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAM,IAAI,CAAC,YAAY;QAC9C,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC;YACrB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QAC3C;QACA,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC;YACnB,IAAI,CAAC,IAAI,CAAC,MAAM,YAAY,EAAE,IAAI,OAAO;YACzC,IAAI,CAAC,UAAU;QACnB;QACA,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM;YACzB,IAAI,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,KAAK,cAAc,EAAE,KAAK;YACnE,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;gBACxB,IAAI,CAAC,SAAS;YAClB;QACJ;QACA,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ;YACjB,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;gBAC1B,aAAa,IAAI,CAAC,OAAO,CAAC,WAAW;YACzC;QACJ;QACA,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,YAAY;YACpC,IAAI,CAAC,IAAI;QACb,GAAG;QACH,IAAI,CAAC,EAAE,CAAC,MAAM,oBAAoB,EAAE,CAAC;YACjC,IAAI,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI;QAC7B;IACJ;IACA,UAAU,EAAE,EAAE;QACV,IAAI,CAAC,EAAE,CAAC,MAAM,aAAa,EAAE;YACzB,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;gBAC5B,+DAA+D;gBAC/D,IAAI,CAAC,YAAY;YACrB,OACK;gBACD;YACJ;QACJ;IACJ;IACA,YAAY;QACR,IAAI,CAAC,GAAG,CAAC;QACT,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG;QAC7B,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,GAAG,IAAI,CAAC,OAAO;QACvE,IAAI,mBAAmB,IAAI,CAAC,OAAO,CAAC,gBAAgB;QACpD,IAAI,SAAS;YACT,WAAW;gBACP,oBAAoB;gBACpB,IAAI,mBAAmB,qBAAqB;oBACxC,mBAAmB;gBACvB;gBACA,IAAI,CAAC,IAAI,CAAC,MAAM,oBAAoB,EAAE;gBACtC,IAAI,CAAC,OAAO;YAChB,GAAG,mBAAmB;QAC1B;IACJ;IACA,OAAO;QACH,IAAI,CAAC,IAAI,CAAC,IAAI;QACd,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,WAAW;YAClC,IAAI,CAAC,GAAG,CAAC;YACT,IAAI,CAAC,IAAI,CAAC,SAAS;QACvB,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ;IAC5B;IACA,eAAe;QACX,MAAM,UAAU;YACZ,QAAQ;YACR,KAAK,IAAI,CAAC,OAAO,CAAC,MAAM;YACxB,QAAQ,IAAI,CAAC,OAAO,CAAC,SAAS;QAClC;QACA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QACnC,IAAI,CAAC,IAAI,CAAC,MAAM,cAAc;QAC9B,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,MAAM,cAAc;IACpD;IACA,aAAa;QACT,IAAI,CAAC,IAAI,CAAC,MAAM,YAAY;QAC5B,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,MAAM,YAAY;QAC9C,IAAI,CAAC,IAAI,CAAC,KAAK;QACf,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG;QACzB,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YAC1B,aAAa,IAAI,CAAC,OAAO,CAAC,WAAW;QACzC;QACA,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;YAC3B,cAAc,IAAI,CAAC,OAAO,CAAC,YAAY;QAC3C;IACJ;IACA,aAAa,EAAE,EAAE;QACb,IAAI,CAAC,EAAE,CAAC,MAAM,YAAY,EAAE,IAAM;IACtC;IACA,QAAQ,EAAE,EAAE;QACR,IAAI,CAAC,EAAE,CAAC,MAAM,YAAY,EAAE,CAAC,MAAQ,GAAG;IAC5C;IACA,cAAc,EAAE,EAAE;QACd,IAAI,CAAC,EAAE,CAAC,MAAM,YAAY,EAAE,CAAC,WAAa,GAAG;IACjD;IACA,cAAc,IAAI,EAAE;QAChB,MAAM,UAAU,CAAC,SAAS,QAAQ,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG;QACxF,OAAQ;YACJ,KAAK;gBACD,IAAI,IAAI,CAAC,EAAE,CAAC,GAAG,KAAK,aAAa;oBAC7B,IAAI,CAAC,IAAI,CAAC,MAAM,SAAS;oBACzB,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,MAAM,SAAS;gBAC/C,OACK,IAAI,IAAI,CAAC,EAAE,CAAC,GAAG,KAAK,iBAAiB;oBACtC,IAAI,CAAC,IAAI,CAAC,MAAM,aAAa;oBAC7B,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,MAAM,aAAa;gBACnD;gBACA;YACJ,KAAK;gBACD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE;gBAChC;YACJ,KAAK;gBACD,IAAI,CAAC,IAAI,CAAC,MAAM,YAAY,EAAE,QAAQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;gBACjE;YACJ;gBACI,IAAI,CAAC,WAAW,CAAC;QACzB;IACJ;IACA,IAAI,GAAG,EAAE;QACL,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YACtB,sCAAsC;YACtC,QAAQ,GAAG,CAAC;QAChB;IACJ;IACA,mBAAmB;QACf,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa;IACrC;IACA,eAAe;QACX,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG;QACzB,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG;QACvB,IAAI,CAAC,OAAO,CAAC,gBAAgB,GAAG;QAChC,IAAI,CAAC,OAAO,CAAC,mBAAmB,GAAG;QACnC,IAAI,CAAC,OAAO,CAAC,gBAAgB,GAAG;QAChC,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YAC1B,aAAa,IAAI,CAAC,OAAO,CAAC,WAAW;QACzC;QACA,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;YAC3B,cAAc,IAAI,CAAC,OAAO,CAAC,YAAY;QAC3C;IACJ;AACJ;AACA,QAAQ,eAAe,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2178, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/node_modules/%40alpacahq/alpaca-trade-api/dist/resources/datav2/crypto_websocket_v1beta3.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AlpacaCryptoClient = void 0;\nconst entityv2_1 = require(\"./entityv2\");\nconst websocket_1 = require(\"./websocket\");\nconst eventTypeMap = new Map([\n    [\"t\", { event: websocket_1.EVENT.TRADES, parse: entityv2_1.AlpacaCryptoTrade }],\n    [\"q\", { event: websocket_1.EVENT.QUOTES, parse: entityv2_1.AlpacaCryptoQuote }],\n    [\"b\", { event: websocket_1.EVENT.BARS, parse: entityv2_1.AlpacaCryptoBar }],\n    [\"u\", { event: websocket_1.EVENT.UPDATED_BARS, parse: entityv2_1.AlpacaCryptoBar }],\n    [\"d\", { event: websocket_1.EVENT.DAILY_BARS, parse: entityv2_1.AlpacaCryptoBar }],\n    [\"o\", { event: websocket_1.EVENT.ORDERBOOKS, parse: entityv2_1.AlpacaCryptoOrderbook }],\n]);\nclass AlpacaCryptoClient extends websocket_1.AlpacaWebsocket {\n    constructor(options) {\n        options.url = options.url.replace(\"https\", \"wss\") + \"/v1beta3/crypto/us\";\n        options.subscriptions = {\n            trades: [],\n            quotes: [],\n            bars: [],\n            updatedBars: [],\n            dailyBars: [],\n            orderbooks: [],\n        };\n        super(options);\n    }\n    subscribeForTrades(trades) {\n        this.session.subscriptions.trades.push(...trades);\n        this.subscribe({ trades });\n    }\n    subscribeForQuotes(quotes) {\n        this.session.subscriptions.quotes.push(...quotes);\n        this.subscribe({ quotes });\n    }\n    subscribeForBars(bars) {\n        this.session.subscriptions.bars.push(...bars);\n        this.subscribe({ bars });\n    }\n    subscribeForUpdatedBars(updatedBars) {\n        this.session.subscriptions.updatedBars.push(...updatedBars);\n        this.subscribe({ updatedBars });\n    }\n    subscribeForDailyBars(dailyBars) {\n        this.session.subscriptions.dailyBars.push(...dailyBars);\n        this.subscribe({ dailyBars });\n    }\n    subscribeForOrderbooks(orderbooks) {\n        this.session.subscriptions.orderbooks.push(...orderbooks);\n        this.subscribe({ orderbooks });\n    }\n    subscribe(symbols) {\n        var _a, _b, _c, _d, _e, _f;\n        const subMsg = {\n            action: \"subscribe\",\n            trades: (_a = symbols.trades) !== null && _a !== void 0 ? _a : [],\n            quotes: (_b = symbols.quotes) !== null && _b !== void 0 ? _b : [],\n            bars: (_c = symbols.bars) !== null && _c !== void 0 ? _c : [],\n            updatedBars: (_d = symbols.updatedBars) !== null && _d !== void 0 ? _d : [],\n            dailyBars: (_e = symbols.dailyBars) !== null && _e !== void 0 ? _e : [],\n            orderbooks: (_f = symbols.orderbooks) !== null && _f !== void 0 ? _f : [],\n        };\n        this.conn.send(this.msgpack.encode(subMsg));\n    }\n    subscribeAll() {\n        this.subscribe(this.session.subscriptions);\n    }\n    unsubscribeFromTrades(trades) {\n        this.session.subscriptions.trades = this.session.subscriptions.trades.filter((trade) => !trades.includes(trade));\n        this.unsubscribe({ trades });\n    }\n    unsubscribeFromQuotes(quotes) {\n        this.session.subscriptions.quotes = this.session.subscriptions.quotes.filter((quote) => !quotes.includes(quote));\n        this.unsubscribe({ quotes });\n    }\n    unsubscribeFromBars(bars) {\n        this.session.subscriptions.bars = this.session.subscriptions.bars.filter((bar) => !bars.includes(bar));\n        this.unsubscribe({ bars });\n    }\n    unsubscribeFromUpdatedBars(updatedBars) {\n        this.session.subscriptions.updatedBars =\n            this.session.subscriptions.updatedBars.filter((updatedBar) => !updatedBars.includes(updatedBar));\n        this.unsubscribe({ updatedBars });\n    }\n    unsubscriceFromDailyBars(dailyBars) {\n        this.session.subscriptions.dailyBars = this.session.subscriptions.dailyBars.filter((dailyBar) => !dailyBars.includes(dailyBar));\n        this.unsubscribe({ dailyBars });\n    }\n    unsubscribeFromOrderbooks(orderbooks) {\n        this.session.subscriptions.orderbooks = this.session.subscriptions.orderbooks.filter((orderbook) => !orderbooks.includes(orderbook));\n        this.unsubscribe({ orderbooks });\n    }\n    unsubscribe(symbols) {\n        var _a, _b, _c, _d, _e, _f;\n        const unsubMsg = {\n            action: \"unsubscribe\",\n            trades: (_a = symbols.trades) !== null && _a !== void 0 ? _a : [],\n            quotes: (_b = symbols.quotes) !== null && _b !== void 0 ? _b : [],\n            bars: (_c = symbols.bars) !== null && _c !== void 0 ? _c : [],\n            updatedBars: (_d = symbols.updatedBars) !== null && _d !== void 0 ? _d : [],\n            dailyBars: (_e = symbols.dailyBars) !== null && _e !== void 0 ? _e : [],\n            orderbooks: (_f = symbols.orderbooks) !== null && _f !== void 0 ? _f : [],\n        };\n        this.conn.send(this.msgpack.encode(unsubMsg));\n    }\n    updateSubscriptions(msg) {\n        this.session.subscriptions = {\n            trades: msg.trades,\n            quotes: msg.quotes,\n            bars: msg.bars,\n            updatedBars: msg.updatedBars,\n            dailyBars: msg.dailyBars,\n            orderbooks: msg.orderbooks,\n        };\n        this.log(`listening to streams:\n        ${JSON.stringify(this.session.subscriptions)}`);\n    }\n    onCryptoTrade(fn) {\n        this.on(websocket_1.EVENT.TRADES, (trade) => fn(trade));\n    }\n    onCryptoQuote(fn) {\n        this.on(websocket_1.EVENT.QUOTES, (quote) => fn(quote));\n    }\n    onCryptoBar(fn) {\n        this.on(websocket_1.EVENT.BARS, (bar) => fn(bar));\n    }\n    onCryptoUpdatedBar(fn) {\n        this.on(websocket_1.EVENT.UPDATED_BARS, (updatedBar) => fn(updatedBar));\n    }\n    onCryptoDailyBar(fn) {\n        this.on(websocket_1.EVENT.DAILY_BARS, (dailyBar) => fn(dailyBar));\n    }\n    onCryptoOrderbook(fn) {\n        this.on(websocket_1.EVENT.ORDERBOOKS, (orderbook) => fn(orderbook));\n    }\n    dataHandler(data) {\n        data.forEach((element) => {\n            if (\"T\" in element) {\n                const eventType = eventTypeMap.get(element.T);\n                if (eventType) {\n                    this.emit(eventType.event, eventType.parse(element));\n                }\n                else {\n                    this.emit(websocket_1.EVENT.CLIENT_ERROR, websocket_1.ERROR.UNEXPECTED_MESSAGE);\n                }\n            }\n        });\n    }\n}\nexports.AlpacaCryptoClient = AlpacaCryptoClient;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,kBAAkB,GAAG,KAAK;AAClC,MAAM;AACN,MAAM;AACN,MAAM,eAAe,IAAI,IAAI;IACzB;QAAC;QAAK;YAAE,OAAO,YAAY,KAAK,CAAC,MAAM;YAAE,OAAO,WAAW,iBAAiB;QAAC;KAAE;IAC/E;QAAC;QAAK;YAAE,OAAO,YAAY,KAAK,CAAC,MAAM;YAAE,OAAO,WAAW,iBAAiB;QAAC;KAAE;IAC/E;QAAC;QAAK;YAAE,OAAO,YAAY,KAAK,CAAC,IAAI;YAAE,OAAO,WAAW,eAAe;QAAC;KAAE;IAC3E;QAAC;QAAK;YAAE,OAAO,YAAY,KAAK,CAAC,YAAY;YAAE,OAAO,WAAW,eAAe;QAAC;KAAE;IACnF;QAAC;QAAK;YAAE,OAAO,YAAY,KAAK,CAAC,UAAU;YAAE,OAAO,WAAW,eAAe;QAAC;KAAE;IACjF;QAAC;QAAK;YAAE,OAAO,YAAY,KAAK,CAAC,UAAU;YAAE,OAAO,WAAW,qBAAqB;QAAC;KAAE;CAC1F;AACD,MAAM,2BAA2B,YAAY,eAAe;IACxD,YAAY,OAAO,CAAE;QACjB,QAAQ,GAAG,GAAG,QAAQ,GAAG,CAAC,OAAO,CAAC,SAAS,SAAS;QACpD,QAAQ,aAAa,GAAG;YACpB,QAAQ,EAAE;YACV,QAAQ,EAAE;YACV,MAAM,EAAE;YACR,aAAa,EAAE;YACf,WAAW,EAAE;YACb,YAAY,EAAE;QAClB;QACA,KAAK,CAAC;IACV;IACA,mBAAmB,MAAM,EAAE;QACvB,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,IAAI;QAC1C,IAAI,CAAC,SAAS,CAAC;YAAE;QAAO;IAC5B;IACA,mBAAmB,MAAM,EAAE;QACvB,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,IAAI;QAC1C,IAAI,CAAC,SAAS,CAAC;YAAE;QAAO;IAC5B;IACA,iBAAiB,IAAI,EAAE;QACnB,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,IAAI;QACxC,IAAI,CAAC,SAAS,CAAC;YAAE;QAAK;IAC1B;IACA,wBAAwB,WAAW,EAAE;QACjC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,IAAI;QAC/C,IAAI,CAAC,SAAS,CAAC;YAAE;QAAY;IACjC;IACA,sBAAsB,SAAS,EAAE;QAC7B,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,IAAI;QAC7C,IAAI,CAAC,SAAS,CAAC;YAAE;QAAU;IAC/B;IACA,uBAAuB,UAAU,EAAE;QAC/B,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,IAAI;QAC9C,IAAI,CAAC,SAAS,CAAC;YAAE;QAAW;IAChC;IACA,UAAU,OAAO,EAAE;QACf,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;QACxB,MAAM,SAAS;YACX,QAAQ;YACR,QAAQ,CAAC,KAAK,QAAQ,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;YACjE,QAAQ,CAAC,KAAK,QAAQ,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;YACjE,MAAM,CAAC,KAAK,QAAQ,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;YAC7D,aAAa,CAAC,KAAK,QAAQ,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;YAC3E,WAAW,CAAC,KAAK,QAAQ,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;YACvE,YAAY,CAAC,KAAK,QAAQ,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;QAC7E;QACA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;IACvC;IACA,eAAe;QACX,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa;IAC7C;IACA,sBAAsB,MAAM,EAAE;QAC1B,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,QAAU,CAAC,OAAO,QAAQ,CAAC;QACzG,IAAI,CAAC,WAAW,CAAC;YAAE;QAAO;IAC9B;IACA,sBAAsB,MAAM,EAAE;QAC1B,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,QAAU,CAAC,OAAO,QAAQ,CAAC;QACzG,IAAI,CAAC,WAAW,CAAC;YAAE;QAAO;IAC9B;IACA,oBAAoB,IAAI,EAAE;QACtB,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAQ,CAAC,KAAK,QAAQ,CAAC;QACjG,IAAI,CAAC,WAAW,CAAC;YAAE;QAAK;IAC5B;IACA,2BAA2B,WAAW,EAAE;QACpC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,WAAW,GAClC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,aAAe,CAAC,YAAY,QAAQ,CAAC;QACxF,IAAI,CAAC,WAAW,CAAC;YAAE;QAAY;IACnC;IACA,yBAAyB,SAAS,EAAE;QAChC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,WAAa,CAAC,UAAU,QAAQ,CAAC;QACrH,IAAI,CAAC,WAAW,CAAC;YAAE;QAAU;IACjC;IACA,0BAA0B,UAAU,EAAE;QAClC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,YAAc,CAAC,WAAW,QAAQ,CAAC;QACzH,IAAI,CAAC,WAAW,CAAC;YAAE;QAAW;IAClC;IACA,YAAY,OAAO,EAAE;QACjB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;QACxB,MAAM,WAAW;YACb,QAAQ;YACR,QAAQ,CAAC,KAAK,QAAQ,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;YACjE,QAAQ,CAAC,KAAK,QAAQ,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;YACjE,MAAM,CAAC,KAAK,QAAQ,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;YAC7D,aAAa,CAAC,KAAK,QAAQ,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;YAC3E,WAAW,CAAC,KAAK,QAAQ,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;YACvE,YAAY,CAAC,KAAK,QAAQ,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;QAC7E;QACA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;IACvC;IACA,oBAAoB,GAAG,EAAE;QACrB,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG;YACzB,QAAQ,IAAI,MAAM;YAClB,QAAQ,IAAI,MAAM;YAClB,MAAM,IAAI,IAAI;YACd,aAAa,IAAI,WAAW;YAC5B,WAAW,IAAI,SAAS;YACxB,YAAY,IAAI,UAAU;QAC9B;QACA,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,EAAE,KAAK,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG;IAClD;IACA,cAAc,EAAE,EAAE;QACd,IAAI,CAAC,EAAE,CAAC,YAAY,KAAK,CAAC,MAAM,EAAE,CAAC,QAAU,GAAG;IACpD;IACA,cAAc,EAAE,EAAE;QACd,IAAI,CAAC,EAAE,CAAC,YAAY,KAAK,CAAC,MAAM,EAAE,CAAC,QAAU,GAAG;IACpD;IACA,YAAY,EAAE,EAAE;QACZ,IAAI,CAAC,EAAE,CAAC,YAAY,KAAK,CAAC,IAAI,EAAE,CAAC,MAAQ,GAAG;IAChD;IACA,mBAAmB,EAAE,EAAE;QACnB,IAAI,CAAC,EAAE,CAAC,YAAY,KAAK,CAAC,YAAY,EAAE,CAAC,aAAe,GAAG;IAC/D;IACA,iBAAiB,EAAE,EAAE;QACjB,IAAI,CAAC,EAAE,CAAC,YAAY,KAAK,CAAC,UAAU,EAAE,CAAC,WAAa,GAAG;IAC3D;IACA,kBAAkB,EAAE,EAAE;QAClB,IAAI,CAAC,EAAE,CAAC,YAAY,KAAK,CAAC,UAAU,EAAE,CAAC,YAAc,GAAG;IAC5D;IACA,YAAY,IAAI,EAAE;QACd,KAAK,OAAO,CAAC,CAAC;YACV,IAAI,OAAO,SAAS;gBAChB,MAAM,YAAY,aAAa,GAAG,CAAC,QAAQ,CAAC;gBAC5C,IAAI,WAAW;oBACX,IAAI,CAAC,IAAI,CAAC,UAAU,KAAK,EAAE,UAAU,KAAK,CAAC;gBAC/C,OACK;oBACD,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,CAAC,YAAY,EAAE,YAAY,KAAK,CAAC,kBAAkB;gBAClF;YACJ;QACJ;IACJ;AACJ;AACA,QAAQ,kBAAkB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2390, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/node_modules/%40alpacahq/alpaca-trade-api/dist/resources/datav2/news_websocket.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AlpacaNewsCLient = void 0;\nconst websocket_1 = require(\"./websocket\");\nconst entityv2_1 = require(\"./entityv2\");\nconst websocket_2 = require(\"./websocket\");\nclass AlpacaNewsCLient extends websocket_2.AlpacaWebsocket {\n    constructor(options) {\n        const url = \"wss\" + options.url.substr(options.url.indexOf(\":\")) + \"/v1beta1/news\";\n        options.url = url;\n        options.subscriptions = {\n            news: [],\n        };\n        super(options);\n    }\n    subscribeForNews(news) {\n        this.session.subscriptions.news.push(...news);\n        this.subscribe(news);\n    }\n    subscribe(news) {\n        const subMsg = {\n            action: \"subscribe\",\n            news,\n        };\n        console.log(\"subscribing\", subMsg);\n        this.conn.send(this.msgpack.encode(subMsg));\n    }\n    subscribeAll() {\n        if (this.session.subscriptions.news.length > 0) {\n            this.subscribe(this.session.subscriptions.news);\n        }\n    }\n    unsubscribeFromNews(news) {\n        this.session.subscriptions.news = this.session.subscriptions.news.filter((n) => !news.includes(n));\n        this.unsubscribe(news);\n    }\n    unsubscribe(news) {\n        const unsubMsg = {\n            action: \"unsubscribe\",\n            news,\n        };\n        this.conn.send(this.msgpack.encode(unsubMsg));\n    }\n    updateSubscriptions(msg) {\n        this.log(`listening to streams:\n        news: ${msg.news}`);\n        this.session.subscriptions = {\n            news: msg.news,\n        };\n    }\n    onNews(fn) {\n        this.on(websocket_1.EVENT.NEWS, (n) => fn(n));\n    }\n    dataHandler(data) {\n        data.forEach((element) => {\n            if (\"T\" in element) {\n                switch (element.T) {\n                    case \"n\":\n                        this.emit(websocket_1.EVENT.NEWS, (0, entityv2_1.AlpacaNews)(element));\n                        break;\n                    default:\n                        this.emit(websocket_1.EVENT.CLIENT_ERROR, websocket_1.ERROR.UNEXPECTED_MESSAGE);\n                }\n            }\n            else {\n                this.emit(websocket_1.EVENT.CLIENT_ERROR, websocket_1.ERROR.UNEXPECTED_MESSAGE);\n            }\n        });\n    }\n}\nexports.AlpacaNewsCLient = AlpacaNewsCLient;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,gBAAgB,GAAG,KAAK;AAChC,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,yBAAyB,YAAY,eAAe;IACtD,YAAY,OAAO,CAAE;QACjB,MAAM,MAAM,QAAQ,QAAQ,GAAG,CAAC,MAAM,CAAC,QAAQ,GAAG,CAAC,OAAO,CAAC,QAAQ;QACnE,QAAQ,GAAG,GAAG;QACd,QAAQ,aAAa,GAAG;YACpB,MAAM,EAAE;QACZ;QACA,KAAK,CAAC;IACV;IACA,iBAAiB,IAAI,EAAE;QACnB,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,IAAI;QACxC,IAAI,CAAC,SAAS,CAAC;IACnB;IACA,UAAU,IAAI,EAAE;QACZ,MAAM,SAAS;YACX,QAAQ;YACR;QACJ;QACA,QAAQ,GAAG,CAAC,eAAe;QAC3B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;IACvC;IACA,eAAe;QACX,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG;YAC5C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI;QAClD;IACJ;IACA,oBAAoB,IAAI,EAAE;QACtB,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAM,CAAC,KAAK,QAAQ,CAAC;QAC/F,IAAI,CAAC,WAAW,CAAC;IACrB;IACA,YAAY,IAAI,EAAE;QACd,MAAM,WAAW;YACb,QAAQ;YACR;QACJ;QACA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;IACvC;IACA,oBAAoB,GAAG,EAAE;QACrB,IAAI,CAAC,GAAG,CAAC,CAAC;cACJ,EAAE,IAAI,IAAI,EAAE;QAClB,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG;YACzB,MAAM,IAAI,IAAI;QAClB;IACJ;IACA,OAAO,EAAE,EAAE;QACP,IAAI,CAAC,EAAE,CAAC,YAAY,KAAK,CAAC,IAAI,EAAE,CAAC,IAAM,GAAG;IAC9C;IACA,YAAY,IAAI,EAAE;QACd,KAAK,OAAO,CAAC,CAAC;YACV,IAAI,OAAO,SAAS;gBAChB,OAAQ,QAAQ,CAAC;oBACb,KAAK;wBACD,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,WAAW,UAAU,EAAE;wBAC7D;oBACJ;wBACI,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,CAAC,YAAY,EAAE,YAAY,KAAK,CAAC,kBAAkB;gBACtF;YACJ,OACK;gBACD,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,CAAC,YAAY,EAAE,YAAY,KAAK,CAAC,kBAAkB;YAClF;QACJ;IACJ;AACJ;AACA,QAAQ,gBAAgB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2465, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/node_modules/%40alpacahq/alpaca-trade-api/dist/resources/datav2/stock_websocket_v2.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AlpacaStocksClient = void 0;\nconst entityv2_1 = require(\"./entityv2\");\nconst websocket_1 = require(\"./websocket\");\nclass AlpacaStocksClient extends websocket_1.AlpacaWebsocket {\n    constructor(options) {\n        const url = \"wss\" + options.url.substr(options.url.indexOf(\":\")) + \"/v2/\" + options.feed;\n        options.url = url;\n        options.subscriptions = {\n            trades: [],\n            quotes: [],\n            bars: [],\n            updatedBars: [],\n            dailyBars: [],\n            statuses: [],\n            lulds: [],\n            cancelErrors: [],\n            corrections: [],\n        };\n        super(options);\n    }\n    subscribeForTrades(trades) {\n        this.session.subscriptions.trades.push(...trades);\n        this.subscribe({ trades });\n    }\n    subscribeForQuotes(quotes) {\n        this.session.subscriptions.quotes.push(...quotes);\n        this.subscribe({ quotes });\n    }\n    subscribeForBars(bars) {\n        this.session.subscriptions.bars.push(...bars);\n        this.subscribe({ bars });\n    }\n    subscribeForUpdatedBars(updatedBars) {\n        this.session.subscriptions.updatedBars.push(...updatedBars);\n        this.subscribe({ updatedBars });\n    }\n    subscribeForDailyBars(dailyBars) {\n        this.session.subscriptions.dailyBars.push(...dailyBars);\n        this.subscribe({ dailyBars });\n    }\n    subscribeForStatuses(statuses) {\n        this.session.subscriptions.statuses.push(...statuses);\n        this.subscribe({ statuses });\n    }\n    subscribeForLulds(lulds) {\n        this.session.subscriptions.lulds.push(...lulds);\n        this.subscribe({ lulds });\n    }\n    subscribe(symbols) {\n        var _a, _b, _c, _d, _e, _f, _g;\n        const subMsg = {\n            action: \"subscribe\",\n            trades: (_a = symbols.trades) !== null && _a !== void 0 ? _a : [],\n            quotes: (_b = symbols.quotes) !== null && _b !== void 0 ? _b : [],\n            bars: (_c = symbols.bars) !== null && _c !== void 0 ? _c : [],\n            updatedBars: (_d = symbols.updatedBars) !== null && _d !== void 0 ? _d : [],\n            dailyBars: (_e = symbols.dailyBars) !== null && _e !== void 0 ? _e : [],\n            statuses: (_f = symbols.statuses) !== null && _f !== void 0 ? _f : [],\n            lulds: (_g = symbols.lulds) !== null && _g !== void 0 ? _g : [],\n        };\n        this.conn.send(this.msgpack.encode(subMsg));\n    }\n    subscribeAll() {\n        this.subscribe(this.session.subscriptions);\n    }\n    unsubscribeFromTrades(trades) {\n        this.session.subscriptions.trades = this.session.subscriptions.trades.filter((trade) => !trades.includes(trade));\n        this.unsubscribe({ trades });\n    }\n    unsubscribeFromQuotes(quotes) {\n        this.session.subscriptions.quotes = this.session.subscriptions.quotes.filter((quote) => !quotes.includes(quote));\n        this.unsubscribe({ quotes });\n    }\n    unsubscribeFromBars(bars) {\n        this.session.subscriptions.bars = this.session.subscriptions.bars.filter((bar) => !bars.includes(bar));\n        this.unsubscribe({ bars });\n    }\n    unsubscribeFromUpdatedBars(updatedBars) {\n        this.session.subscriptions.updatedBars =\n            this.session.subscriptions.updatedBars.filter((updatedBar) => !updatedBars.includes(updatedBar));\n        this.unsubscribe({ updatedBars });\n    }\n    unsubscribeFromDailyBars(dailyBars) {\n        this.session.subscriptions.dailyBars = this.session.subscriptions.dailyBars.filter((dailyBar) => !dailyBars.includes(dailyBar));\n        this.unsubscribe({ dailyBars });\n    }\n    unsubscribeFromStatuses(statuses) {\n        this.session.subscriptions.statuses = this.session.subscriptions.statuses.filter((status) => !statuses.includes(status));\n        this.unsubscribe({ statuses });\n    }\n    unsubscribeFromLulds(lulds) {\n        this.session.subscriptions.lulds = this.session.subscriptions.lulds.filter((luld) => !lulds.includes(luld));\n        this.unsubscribe({ lulds });\n    }\n    unsubscribe(symbols) {\n        var _a, _b, _c, _d, _e, _f, _g;\n        const unsubMsg = {\n            action: \"unsubscribe\",\n            trades: (_a = symbols.trades) !== null && _a !== void 0 ? _a : [],\n            quotes: (_b = symbols.quotes) !== null && _b !== void 0 ? _b : [],\n            bars: (_c = symbols.bars) !== null && _c !== void 0 ? _c : [],\n            updatedBars: (_d = symbols.updatedBars) !== null && _d !== void 0 ? _d : [],\n            dailyBars: (_e = symbols.dailyBars) !== null && _e !== void 0 ? _e : [],\n            statuses: (_f = symbols.statuses) !== null && _f !== void 0 ? _f : [],\n            lulds: (_g = symbols.lulds) !== null && _g !== void 0 ? _g : [],\n        };\n        this.conn.send(this.msgpack.encode(unsubMsg));\n    }\n    updateSubscriptions(msg) {\n        this.log(`listening to streams:\n        trades: ${msg.trades},\n        quotes: ${msg.quotes},\n        bars: ${msg.bars},\n        updatedBars: ${msg.updatedBars},\n        dailyBars: ${msg.dailyBars},\n        statuses: ${msg.statuses},\n        lulds: ${msg.lulds},\n        cancelErrors: ${msg.cancelErrors},\n        corrections: ${msg.corrections}`);\n        this.session.subscriptions = {\n            trades: msg.trades,\n            quotes: msg.quotes,\n            bars: msg.bars,\n            updatedBars: msg.updatedBars,\n            dailyBars: msg.dailyBars,\n            statuses: msg.statuses,\n            lulds: msg.lulds,\n            cancelErrors: msg.cancelErrors,\n            corrections: msg.corrections,\n        };\n    }\n    onStockTrade(fn) {\n        this.on(websocket_1.EVENT.TRADES, (trade) => fn(trade));\n    }\n    onStockQuote(fn) {\n        this.on(websocket_1.EVENT.QUOTES, (quote) => fn(quote));\n    }\n    onStockBar(fn) {\n        this.on(websocket_1.EVENT.BARS, (bar) => fn(bar));\n    }\n    onStockUpdatedBar(fn) {\n        this.on(websocket_1.EVENT.UPDATED_BARS, (updatedBar) => fn(updatedBar));\n    }\n    onStockDailyBar(fn) {\n        this.on(websocket_1.EVENT.DAILY_BARS, (dailyBar) => fn(dailyBar));\n    }\n    onStatuses(fn) {\n        this.on(websocket_1.EVENT.TRADING_STATUSES, (status) => fn(status));\n    }\n    onLulds(fn) {\n        this.on(websocket_1.EVENT.LULDS, (luld) => fn(luld));\n    }\n    onCancelErrors(fn) {\n        this.on(websocket_1.EVENT.CANCEL_ERRORS, (cancelError) => fn(cancelError));\n    }\n    onCorrections(fn) {\n        this.on(websocket_1.EVENT.CORRECTIONS, (correction) => fn(correction));\n    }\n    dataHandler(data) {\n        data.forEach((element) => {\n            if (\"T\" in element) {\n                switch (element.T) {\n                    case \"t\":\n                        this.emit(websocket_1.EVENT.TRADES, (0, entityv2_1.AlpacaTradeV2)(element));\n                        break;\n                    case \"q\":\n                        this.emit(websocket_1.EVENT.QUOTES, (0, entityv2_1.AlpacaQuoteV2)(element));\n                        break;\n                    case \"b\":\n                        this.emit(websocket_1.EVENT.BARS, (0, entityv2_1.AlpacaBarV2)(element));\n                        break;\n                    case \"u\":\n                        this.emit(websocket_1.EVENT.UPDATED_BARS, (0, entityv2_1.AlpacaBarV2)(element));\n                        break;\n                    case \"d\":\n                        this.emit(websocket_1.EVENT.DAILY_BARS, (0, entityv2_1.AlpacaBarV2)(element));\n                        break;\n                    case \"s\":\n                        this.emit(websocket_1.EVENT.TRADING_STATUSES, (0, entityv2_1.AlpacaStatusV2)(element));\n                        break;\n                    case \"l\":\n                        this.emit(websocket_1.EVENT.LULDS, (0, entityv2_1.AlpacaLuldV2)(element));\n                        break;\n                    case \"x\":\n                        this.emit(websocket_1.EVENT.CANCEL_ERRORS, (0, entityv2_1.AlpacaCancelErrorV2)(element));\n                        break;\n                    case \"c\":\n                        this.emit(websocket_1.EVENT.CORRECTIONS, (0, entityv2_1.AlpacaCorrectionV2)(element));\n                        break;\n                    default:\n                        this.emit(websocket_1.EVENT.CLIENT_ERROR, websocket_1.ERROR.UNEXPECTED_MESSAGE);\n                }\n            }\n        });\n    }\n}\nexports.AlpacaStocksClient = AlpacaStocksClient;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,kBAAkB,GAAG,KAAK;AAClC,MAAM;AACN,MAAM;AACN,MAAM,2BAA2B,YAAY,eAAe;IACxD,YAAY,OAAO,CAAE;QACjB,MAAM,MAAM,QAAQ,QAAQ,GAAG,CAAC,MAAM,CAAC,QAAQ,GAAG,CAAC,OAAO,CAAC,QAAQ,SAAS,QAAQ,IAAI;QACxF,QAAQ,GAAG,GAAG;QACd,QAAQ,aAAa,GAAG;YACpB,QAAQ,EAAE;YACV,QAAQ,EAAE;YACV,MAAM,EAAE;YACR,aAAa,EAAE;YACf,WAAW,EAAE;YACb,UAAU,EAAE;YACZ,OAAO,EAAE;YACT,cAAc,EAAE;YAChB,aAAa,EAAE;QACnB;QACA,KAAK,CAAC;IACV;IACA,mBAAmB,MAAM,EAAE;QACvB,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,IAAI;QAC1C,IAAI,CAAC,SAAS,CAAC;YAAE;QAAO;IAC5B;IACA,mBAAmB,MAAM,EAAE;QACvB,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,IAAI;QAC1C,IAAI,CAAC,SAAS,CAAC;YAAE;QAAO;IAC5B;IACA,iBAAiB,IAAI,EAAE;QACnB,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,IAAI;QACxC,IAAI,CAAC,SAAS,CAAC;YAAE;QAAK;IAC1B;IACA,wBAAwB,WAAW,EAAE;QACjC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,IAAI;QAC/C,IAAI,CAAC,SAAS,CAAC;YAAE;QAAY;IACjC;IACA,sBAAsB,SAAS,EAAE;QAC7B,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,IAAI;QAC7C,IAAI,CAAC,SAAS,CAAC;YAAE;QAAU;IAC/B;IACA,qBAAqB,QAAQ,EAAE;QAC3B,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,IAAI;QAC5C,IAAI,CAAC,SAAS,CAAC;YAAE;QAAS;IAC9B;IACA,kBAAkB,KAAK,EAAE;QACrB,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,IAAI;QACzC,IAAI,CAAC,SAAS,CAAC;YAAE;QAAM;IAC3B;IACA,UAAU,OAAO,EAAE;QACf,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;QAC5B,MAAM,SAAS;YACX,QAAQ;YACR,QAAQ,CAAC,KAAK,QAAQ,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;YACjE,QAAQ,CAAC,KAAK,QAAQ,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;YACjE,MAAM,CAAC,KAAK,QAAQ,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;YAC7D,aAAa,CAAC,KAAK,QAAQ,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;YAC3E,WAAW,CAAC,KAAK,QAAQ,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;YACvE,UAAU,CAAC,KAAK,QAAQ,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;YACrE,OAAO,CAAC,KAAK,QAAQ,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;QACnE;QACA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;IACvC;IACA,eAAe;QACX,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa;IAC7C;IACA,sBAAsB,MAAM,EAAE;QAC1B,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,QAAU,CAAC,OAAO,QAAQ,CAAC;QACzG,IAAI,CAAC,WAAW,CAAC;YAAE;QAAO;IAC9B;IACA,sBAAsB,MAAM,EAAE;QAC1B,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,QAAU,CAAC,OAAO,QAAQ,CAAC;QACzG,IAAI,CAAC,WAAW,CAAC;YAAE;QAAO;IAC9B;IACA,oBAAoB,IAAI,EAAE;QACtB,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAQ,CAAC,KAAK,QAAQ,CAAC;QACjG,IAAI,CAAC,WAAW,CAAC;YAAE;QAAK;IAC5B;IACA,2BAA2B,WAAW,EAAE;QACpC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,WAAW,GAClC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,aAAe,CAAC,YAAY,QAAQ,CAAC;QACxF,IAAI,CAAC,WAAW,CAAC;YAAE;QAAY;IACnC;IACA,yBAAyB,SAAS,EAAE;QAChC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,WAAa,CAAC,UAAU,QAAQ,CAAC;QACrH,IAAI,CAAC,WAAW,CAAC;YAAE;QAAU;IACjC;IACA,wBAAwB,QAAQ,EAAE;QAC9B,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,SAAW,CAAC,SAAS,QAAQ,CAAC;QAChH,IAAI,CAAC,WAAW,CAAC;YAAE;QAAS;IAChC;IACA,qBAAqB,KAAK,EAAE;QACxB,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,OAAS,CAAC,MAAM,QAAQ,CAAC;QACrG,IAAI,CAAC,WAAW,CAAC;YAAE;QAAM;IAC7B;IACA,YAAY,OAAO,EAAE;QACjB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;QAC5B,MAAM,WAAW;YACb,QAAQ;YACR,QAAQ,CAAC,KAAK,QAAQ,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;YACjE,QAAQ,CAAC,KAAK,QAAQ,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;YACjE,MAAM,CAAC,KAAK,QAAQ,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;YAC7D,aAAa,CAAC,KAAK,QAAQ,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;YAC3E,WAAW,CAAC,KAAK,QAAQ,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;YACvE,UAAU,CAAC,KAAK,QAAQ,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;YACrE,OAAO,CAAC,KAAK,QAAQ,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;QACnE;QACA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;IACvC;IACA,oBAAoB,GAAG,EAAE;QACrB,IAAI,CAAC,GAAG,CAAC,CAAC;gBACF,EAAE,IAAI,MAAM,CAAC;gBACb,EAAE,IAAI,MAAM,CAAC;cACf,EAAE,IAAI,IAAI,CAAC;qBACJ,EAAE,IAAI,WAAW,CAAC;mBACpB,EAAE,IAAI,SAAS,CAAC;kBACjB,EAAE,IAAI,QAAQ,CAAC;eAClB,EAAE,IAAI,KAAK,CAAC;sBACL,EAAE,IAAI,YAAY,CAAC;qBACpB,EAAE,IAAI,WAAW,EAAE;QAChC,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG;YACzB,QAAQ,IAAI,MAAM;YAClB,QAAQ,IAAI,MAAM;YAClB,MAAM,IAAI,IAAI;YACd,aAAa,IAAI,WAAW;YAC5B,WAAW,IAAI,SAAS;YACxB,UAAU,IAAI,QAAQ;YACtB,OAAO,IAAI,KAAK;YAChB,cAAc,IAAI,YAAY;YAC9B,aAAa,IAAI,WAAW;QAChC;IACJ;IACA,aAAa,EAAE,EAAE;QACb,IAAI,CAAC,EAAE,CAAC,YAAY,KAAK,CAAC,MAAM,EAAE,CAAC,QAAU,GAAG;IACpD;IACA,aAAa,EAAE,EAAE;QACb,IAAI,CAAC,EAAE,CAAC,YAAY,KAAK,CAAC,MAAM,EAAE,CAAC,QAAU,GAAG;IACpD;IACA,WAAW,EAAE,EAAE;QACX,IAAI,CAAC,EAAE,CAAC,YAAY,KAAK,CAAC,IAAI,EAAE,CAAC,MAAQ,GAAG;IAChD;IACA,kBAAkB,EAAE,EAAE;QAClB,IAAI,CAAC,EAAE,CAAC,YAAY,KAAK,CAAC,YAAY,EAAE,CAAC,aAAe,GAAG;IAC/D;IACA,gBAAgB,EAAE,EAAE;QAChB,IAAI,CAAC,EAAE,CAAC,YAAY,KAAK,CAAC,UAAU,EAAE,CAAC,WAAa,GAAG;IAC3D;IACA,WAAW,EAAE,EAAE;QACX,IAAI,CAAC,EAAE,CAAC,YAAY,KAAK,CAAC,gBAAgB,EAAE,CAAC,SAAW,GAAG;IAC/D;IACA,QAAQ,EAAE,EAAE;QACR,IAAI,CAAC,EAAE,CAAC,YAAY,KAAK,CAAC,KAAK,EAAE,CAAC,OAAS,GAAG;IAClD;IACA,eAAe,EAAE,EAAE;QACf,IAAI,CAAC,EAAE,CAAC,YAAY,KAAK,CAAC,aAAa,EAAE,CAAC,cAAgB,GAAG;IACjE;IACA,cAAc,EAAE,EAAE;QACd,IAAI,CAAC,EAAE,CAAC,YAAY,KAAK,CAAC,WAAW,EAAE,CAAC,aAAe,GAAG;IAC9D;IACA,YAAY,IAAI,EAAE;QACd,KAAK,OAAO,CAAC,CAAC;YACV,IAAI,OAAO,SAAS;gBAChB,OAAQ,QAAQ,CAAC;oBACb,KAAK;wBACD,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,WAAW,aAAa,EAAE;wBAClE;oBACJ,KAAK;wBACD,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,WAAW,aAAa,EAAE;wBAClE;oBACJ,KAAK;wBACD,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,WAAW,WAAW,EAAE;wBAC9D;oBACJ,KAAK;wBACD,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,CAAC,YAAY,EAAE,CAAC,GAAG,WAAW,WAAW,EAAE;wBACtE;oBACJ,KAAK;wBACD,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,CAAC,UAAU,EAAE,CAAC,GAAG,WAAW,WAAW,EAAE;wBACpE;oBACJ,KAAK;wBACD,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,CAAC,gBAAgB,EAAE,CAAC,GAAG,WAAW,cAAc,EAAE;wBAC7E;oBACJ,KAAK;wBACD,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,CAAC,KAAK,EAAE,CAAC,GAAG,WAAW,YAAY,EAAE;wBAChE;oBACJ,KAAK;wBACD,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,CAAC,aAAa,EAAE,CAAC,GAAG,WAAW,mBAAmB,EAAE;wBAC/E;oBACJ,KAAK;wBACD,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,CAAC,WAAW,EAAE,CAAC,GAAG,WAAW,kBAAkB,EAAE;wBAC5E;oBACJ;wBACI,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,CAAC,YAAY,EAAE,YAAY,KAAK,CAAC,kBAAkB;gBACtF;YACJ;QACJ;IACJ;AACJ;AACA,QAAQ,kBAAkB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2696, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/node_modules/%40alpacahq/alpaca-trade-api/dist/resources/datav2/option_websocket_v1beta1.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AlpacaOptionClient = void 0;\nconst entityv2_1 = require(\"./entityv2\");\nconst websocket_1 = require(\"./websocket\");\nclass AlpacaOptionClient extends websocket_1.AlpacaWebsocket {\n    constructor(options) {\n        const url = \"wss\" + options.url.substr(options.url.indexOf(\":\")) + \"/v1beta1/\" + options.feed;\n        options.url = url;\n        options.subscriptions = {\n            trades: [],\n            quotes: [],\n        };\n        super(options);\n    }\n    subscribeForTrades(trades) {\n        this.session.subscriptions.trades.push(...trades);\n        this.subscribe({ trades });\n    }\n    subscribeForQuotes(quotes) {\n        this.session.subscriptions.quotes.push(...quotes);\n        this.subscribe({ quotes });\n    }\n    subscribe(symbols) {\n        var _a, _b;\n        const subMsg = {\n            action: \"subscribe\",\n            trades: (_a = symbols.trades) !== null && _a !== void 0 ? _a : [],\n            quotes: (_b = symbols.quotes) !== null && _b !== void 0 ? _b : [],\n        };\n        this.conn.send(this.msgpack.encode(subMsg));\n    }\n    subscribeAll() {\n        this.subscribe(this.session.subscriptions);\n    }\n    unsubscribeFromTrades(trades) {\n        this.session.subscriptions.trades = this.session.subscriptions.trades.filter((trade) => !trades.includes(trade));\n        this.unsubscribe({ trades });\n    }\n    unsubscribeFromQuotes(quotes) {\n        this.session.subscriptions.quotes = this.session.subscriptions.quotes.filter((quote) => !quotes.includes(quote));\n        this.unsubscribe({ quotes });\n    }\n    unsubscribe(symbols) {\n        var _a, _b;\n        const unsubMsg = {\n            action: \"unsubscribe\",\n            trades: (_a = symbols.trades) !== null && _a !== void 0 ? _a : [],\n            quotes: (_b = symbols.quotes) !== null && _b !== void 0 ? _b : [],\n        };\n        this.conn.send(this.msgpack.encode(unsubMsg));\n    }\n    updateSubscriptions(msg) {\n        this.log(`listening to streams:\n          trades: ${msg.trades},\n          quotes: ${msg.quotes}`);\n        this.session.subscriptions = {\n            trades: msg.trades,\n            quotes: msg.quotes,\n        };\n    }\n    onOptionTrade(fn) {\n        this.on(websocket_1.EVENT.TRADES, (trade) => fn(trade));\n    }\n    onOptionQuote(fn) {\n        this.on(websocket_1.EVENT.QUOTES, (quote) => fn(quote));\n    }\n    dataHandler(data) {\n        data.forEach((element) => {\n            if (\"T\" in element) {\n                switch (element.T) {\n                    case \"t\":\n                        this.emit(websocket_1.EVENT.TRADES, (0, entityv2_1.AlpacaOptionTradeV1Beta1)(element));\n                        break;\n                    case \"q\":\n                        this.emit(websocket_1.EVENT.QUOTES, (0, entityv2_1.AlpacaOptionQuoteV1Beta1)(element));\n                        break;\n                    default:\n                        this.emit(websocket_1.EVENT.CLIENT_ERROR, websocket_1.ERROR.UNEXPECTED_MESSAGE);\n                }\n            }\n        });\n    }\n}\nexports.AlpacaOptionClient = AlpacaOptionClient;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,kBAAkB,GAAG,KAAK;AAClC,MAAM;AACN,MAAM;AACN,MAAM,2BAA2B,YAAY,eAAe;IACxD,YAAY,OAAO,CAAE;QACjB,MAAM,MAAM,QAAQ,QAAQ,GAAG,CAAC,MAAM,CAAC,QAAQ,GAAG,CAAC,OAAO,CAAC,QAAQ,cAAc,QAAQ,IAAI;QAC7F,QAAQ,GAAG,GAAG;QACd,QAAQ,aAAa,GAAG;YACpB,QAAQ,EAAE;YACV,QAAQ,EAAE;QACd;QACA,KAAK,CAAC;IACV;IACA,mBAAmB,MAAM,EAAE;QACvB,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,IAAI;QAC1C,IAAI,CAAC,SAAS,CAAC;YAAE;QAAO;IAC5B;IACA,mBAAmB,MAAM,EAAE;QACvB,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,IAAI;QAC1C,IAAI,CAAC,SAAS,CAAC;YAAE;QAAO;IAC5B;IACA,UAAU,OAAO,EAAE;QACf,IAAI,IAAI;QACR,MAAM,SAAS;YACX,QAAQ;YACR,QAAQ,CAAC,KAAK,QAAQ,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;YACjE,QAAQ,CAAC,KAAK,QAAQ,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;QACrE;QACA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;IACvC;IACA,eAAe;QACX,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa;IAC7C;IACA,sBAAsB,MAAM,EAAE;QAC1B,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,QAAU,CAAC,OAAO,QAAQ,CAAC;QACzG,IAAI,CAAC,WAAW,CAAC;YAAE;QAAO;IAC9B;IACA,sBAAsB,MAAM,EAAE;QAC1B,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,QAAU,CAAC,OAAO,QAAQ,CAAC;QACzG,IAAI,CAAC,WAAW,CAAC;YAAE;QAAO;IAC9B;IACA,YAAY,OAAO,EAAE;QACjB,IAAI,IAAI;QACR,MAAM,WAAW;YACb,QAAQ;YACR,QAAQ,CAAC,KAAK,QAAQ,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;YACjE,QAAQ,CAAC,KAAK,QAAQ,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;QACrE;QACA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;IACvC;IACA,oBAAoB,GAAG,EAAE;QACrB,IAAI,CAAC,GAAG,CAAC,CAAC;kBACA,EAAE,IAAI,MAAM,CAAC;kBACb,EAAE,IAAI,MAAM,EAAE;QACxB,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG;YACzB,QAAQ,IAAI,MAAM;YAClB,QAAQ,IAAI,MAAM;QACtB;IACJ;IACA,cAAc,EAAE,EAAE;QACd,IAAI,CAAC,EAAE,CAAC,YAAY,KAAK,CAAC,MAAM,EAAE,CAAC,QAAU,GAAG;IACpD;IACA,cAAc,EAAE,EAAE;QACd,IAAI,CAAC,EAAE,CAAC,YAAY,KAAK,CAAC,MAAM,EAAE,CAAC,QAAU,GAAG;IACpD;IACA,YAAY,IAAI,EAAE;QACd,KAAK,OAAO,CAAC,CAAC;YACV,IAAI,OAAO,SAAS;gBAChB,OAAQ,QAAQ,CAAC;oBACb,KAAK;wBACD,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,WAAW,wBAAwB,EAAE;wBAC7E;oBACJ,KAAK;wBACD,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,WAAW,wBAAwB,EAAE;wBAC7E;oBACJ;wBACI,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,CAAC,YAAY,EAAE,YAAY,KAAK,CAAC,kBAAkB;gBACtF;YACJ;QACJ;IACJ;AACJ;AACA,QAAQ,kBAAkB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2794, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/node_modules/%40alpacahq/alpaca-trade-api/dist/resources/entity.js"], "sourcesContent": ["\"use strict\";\nlet alpaca_quote_mapping = {\n    T: \"symbol\",\n    X: \"askexchange\",\n    P: \"askprice\",\n    S: \"asksize\",\n    x: \"bidexchange\",\n    p: \"bidprice\",\n    s: \"bidsize\",\n    c: \"conditions\",\n    t: \"timestamp\",\n};\nlet alpaca_trade_mapping = {\n    T: \"symbol\",\n    i: \"tradeID\",\n    x: \"exchange\",\n    p: \"price\",\n    s: \"size\",\n    t: \"timestamp\", // in millisecond\n    z: \"tapeID\",\n    c: \"conditions\",\n};\n// used in websocket with AM.<SYMBOL>\nlet alpaca_agg_minute_bar_mapping = {\n    T: \"symbol\",\n    v: \"volume\",\n    av: \"accumulatedVolume\",\n    op: \"officialOpenPrice\",\n    vw: \"vwap\",\n    o: \"openPrice\",\n    h: \"highPrice\",\n    l: \"lowPrice\",\n    c: \"closePrice\",\n    a: \"averagePrice\",\n    s: \"startEpochTime\",\n    e: \"endEpochTime\",\n};\n// used with rest bars endpoint\nlet alpaca_bar_mapping = {\n    t: \"startEpochTime\", // in seconds\n    o: \"openPrice\",\n    h: \"highPrice\",\n    l: \"lowPrice\",\n    c: \"closePrice\",\n    v: \"volume\",\n};\nlet polygon_quote_mapping = {\n    sym: \"symbol\",\n    ax: \"askexchange\",\n    ap: \"askprice\",\n    as: \"asksize\",\n    bx: \"bidexchange\",\n    bp: \"bidprice\",\n    bs: \"bidsize\",\n    c: \"condition\",\n    t: \"timestamp\",\n};\nfunction AlpacaQuote(data) {\n    return convert(data, alpaca_quote_mapping);\n}\nfunction AlpacaTrade(data) {\n    return convert(data, alpaca_trade_mapping);\n}\nfunction AggMinuteBar(data) {\n    return convert(data, alpaca_agg_minute_bar_mapping);\n}\nfunction Bar(data) {\n    return convert(data, alpaca_bar_mapping);\n}\nfunction convert(data, mapping) {\n    const obj = {};\n    for (let [key, value] of Object.entries(data)) {\n        if (mapping.hasOwnProperty(key)) {\n            obj[mapping[key]] = value;\n        }\n        else {\n            obj[key] = value;\n        }\n    }\n    return obj;\n}\nmodule.exports = {\n    AlpacaTrade: AlpacaTrade,\n    AlpacaQuote: AlpacaQuote,\n    AggMinuteBar: AggMinuteBar,\n    Bar: Bar,\n};\n"], "names": [], "mappings": "AACA,IAAI,uBAAuB;IACvB,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;AACP;AACA,IAAI,uBAAuB;IACvB,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;AACP;AACA,qCAAqC;AACrC,IAAI,gCAAgC;IAChC,GAAG;IACH,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;AACP;AACA,+BAA+B;AAC/B,IAAI,qBAAqB;IACrB,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;AACP;AACA,IAAI,wBAAwB;IACxB,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,GAAG;IACH,GAAG;AACP;AACA,SAAS,YAAY,IAAI;IACrB,OAAO,QAAQ,MAAM;AACzB;AACA,SAAS,YAAY,IAAI;IACrB,OAAO,QAAQ,MAAM;AACzB;AACA,SAAS,aAAa,IAAI;IACtB,OAAO,QAAQ,MAAM;AACzB;AACA,SAAS,IAAI,IAAI;IACb,OAAO,QAAQ,MAAM;AACzB;AACA,SAAS,QAAQ,IAAI,EAAE,OAAO;IAC1B,MAAM,MAAM,CAAC;IACb,KAAK,IAAI,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,MAAO;QAC3C,IAAI,QAAQ,cAAc,CAAC,MAAM;YAC7B,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG;QACxB,OACK;YACD,GAAG,CAAC,IAAI,GAAG;QACf;IACJ;IACA,OAAO;AACX;AACA,OAAO,OAAO,GAAG;IACb,aAAa;IACb,aAAa;IACb,cAAc;IACd,KAAK;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2883, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/node_modules/%40alpacahq/alpaca-trade-api/dist/resources/websockets.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst events = require(\"events\");\nconst WebSocket = require(\"ws\");\nconst entity = require(\"./entity\");\n// Listeners\n// A client can listen on any of the following events, states, or errors\n// Connection states. Each of these will also emit EVENT.STATE_CHANGE\nvar STATE;\n(function (STATE) {\n    STATE.AUTHENTICATING = \"authenticating\";\n    STATE.CONNECTED = \"connected\";\n    STATE.CONNECTING = \"connecting\";\n    STATE.DISCONNECTED = \"disconnected\";\n    STATE.WAITING_TO_CONNECT = \"waiting to connect\";\n    STATE.WAITING_TO_RECONNECT = \"waiting to reconnect\";\n})((STATE = exports.STATE || (exports.STATE = {})));\n// Client events\nvar EVENT;\n(function (EVENT) {\n    EVENT.CLIENT_ERROR = \"client_error\";\n    EVENT.STATE_CHANGE = \"state_change\";\n    EVENT.AUTHORIZED = \"authorized\";\n    EVENT.UNAUTHORIZED = \"unauthorized\";\n    EVENT.ORDER_UPDATE = \"trade_updates\";\n    EVENT.ACCOUNT_UPDATE = \"account_updates\";\n    EVENT.STOCK_TRADES = \"stock_trades\";\n    EVENT.STOCK_QUOTES = \"stock_quotes\";\n    EVENT.STOCK_AGG_SEC = \"stock_agg_sec\";\n    EVENT.STOCK_AGG_MIN = \"stock_agg_min\";\n})((EVENT = exports.EVENT || (exports.EVENT = {})));\n// Connection errors Each of these will also emit EVENT.ERROR\nvar ERROR;\n(function (ERROR) {\n    ERROR.BAD_KEY_OR_SECRET = \"bad key id or secret\";\n    ERROR.CONNECTION_REFUSED = \"connection refused\";\n    ERROR.MISSING_API_KEY = \"missing api key\";\n    ERROR.MISSING_SECRET_KEY = \"missing secret key\";\n    ERROR.UNKNOWN = \"unknown error\";\n})((ERROR = exports.ERROR || (exports.ERROR = {})));\n/**\n * AlpacaStreamClient manages a connection to Alpaca's websocket api\n */\nclass AlpacaStreamClient extends events.EventEmitter {\n    constructor(opts = {}) {\n        super();\n        this.defaultOptions = {\n            // A list of subscriptions to subscribe to on connection\n            subscriptions: [],\n            // Whether the library should reconnect automatically\n            reconnect: true,\n            // Reconnection backoff: if true, then the reconnection time will be initially\n            // reconnectTimeout, then will double with each unsuccessful connection attempt.\n            // It will not exceed maxReconnectTimeout\n            backoff: true,\n            // Initial reconnect timeout (seconds) a minimum of 1 will be used if backoff=false\n            reconnectTimeout: 0,\n            // The maximum amount of time between reconnect tries (applies to backoff)\n            maxReconnectTimeout: 30,\n            // The amount of time to increment the delay between each reconnect attempt\n            backoffIncrement: 0.5,\n            // If true, client outputs detailed log messages\n            verbose: false,\n            // If true we will use the polygon ws data source, otherwise we use\n            // alpaca ws data source\n            usePolygon: false,\n        };\n        // Set minimum reconnectTimeout of 1s if backoff=false\n        if (!opts.backoff && opts.reconnectTimeout < 1) {\n            opts.reconnectTimeout = 1;\n        }\n        // Merge supplied options with defaults\n        this.session = Object.assign(this.defaultOptions, opts);\n        this.session.url = this.session.url.replace(/^http/, \"ws\") + \"/stream\";\n        // Keep track of subscriptions in case we need to reconnect after the client\n        // has called subscribe()\n        this.subscriptionState = {};\n        this.session.subscriptions.forEach((x) => {\n            this.subscriptionState[x] = true;\n        });\n        this.currentState = STATE.WAITING_TO_CONNECT;\n        // Register internal event handlers\n        // Log and emit every state change\n        Object.keys(STATE).forEach((s) => {\n            this.on(STATE[s], () => {\n                this.currentState = STATE[s];\n                this.log(\"info\", `state change: ${STATE[s]}`);\n                this.emit(EVENT.STATE_CHANGE, STATE[s]);\n            });\n        });\n        // Log and emit every error\n        Object.keys(ERROR).forEach((e) => {\n            this.on(ERROR[e], () => {\n                this.log(\"error\", ERROR[e]);\n                this.emit(EVENT.CLIENT_ERROR, ERROR[e]);\n            });\n        });\n    }\n    connect() {\n        // Check the credentials\n        if (this.session.apiKey.length === 0 && this.session.oauth.length === 0) {\n            throw new Error(ERROR.MISSING_API_KEY);\n        }\n        if (this.session.secretKey.length === 0 && this.session.oauth.length === 0) {\n            throw new Error(ERROR.MISSING_SECRET_KEY);\n        }\n        // Reset reconnectDisabled since the user called connect() again\n        this.reconnectDisabled = false;\n        this.emit(STATE.CONNECTING);\n        this.conn = new WebSocket(this.session.url);\n        this.conn.once(\"open\", () => {\n            this.authenticate();\n        });\n        this.conn.on(\"message\", (data) => this.handleMessage(data));\n        this.conn.once(\"error\", (err) => {\n            this.emit(ERROR.CONNECTION_REFUSED);\n        });\n        this.conn.once(\"close\", () => {\n            this.emit(STATE.DISCONNECTED);\n            if (this.session.reconnect && !this.reconnectDisabled) {\n                this.reconnect();\n            }\n        });\n    }\n    _ensure_polygon(channels) {\n        if (this.polygon.connectCalled) {\n            if (channels) {\n                this.polygon.subscribe(channels);\n            }\n            return;\n        }\n        this.polygon.connect(channels);\n    }\n    _unsubscribe_polygon(channels) {\n        if (this.polygon.connectCalled) {\n            if (channels) {\n                this.polygon.unsubscribe(channels);\n            }\n        }\n    }\n    subscribe(keys) {\n        let wsChannels = [];\n        let polygonChannels = [];\n        keys.forEach((key) => {\n            const poly = [\"Q.\", \"T.\", \"A.\", \"AM.\"];\n            let found = poly.filter((channel) => key.startsWith(channel));\n            if (found.length > 0) {\n                polygonChannels.push(key);\n            }\n            else {\n                wsChannels.push(key);\n            }\n        });\n        if (wsChannels.length > 0) {\n            const subMsg = {\n                action: \"listen\",\n                data: {\n                    streams: wsChannels,\n                },\n            };\n            this.send(JSON.stringify(subMsg));\n        }\n        if (polygonChannels.length > 0) {\n            this._ensure_polygon(polygonChannels);\n        }\n        keys.forEach((x) => {\n            this.subscriptionState[x] = true;\n        });\n    }\n    unsubscribe(keys) {\n        // Currently, only Polygon channels can be unsubscribed from\n        let polygonChannels = [];\n        keys.forEach((key) => {\n            const poly = [\"Q.\", \"T.\", \"A.\", \"AM.\"];\n            let found = poly.filter((channel) => key.startsWith(channel));\n            if (found.length > 0) {\n                polygonChannels.push(key);\n            }\n        });\n        if (polygonChannels.length > 0) {\n            this._unsubscribe_polygon(polygonChannels);\n        }\n        keys.forEach((x) => {\n            this.subscriptionState[x] = false;\n        });\n    }\n    subscriptions() {\n        // if the user unsubscribes from certain equities, they will still be\n        // under this.subscriptionState but with value \"false\", so we need to\n        // filter them out\n        return Object.keys(this.subscriptionState).filter((x) => this.subscriptionState[x]);\n    }\n    onConnect(fn) {\n        this.on(STATE.CONNECTED, () => fn());\n    }\n    onDisconnect(fn) {\n        this.on(STATE.DISCONNECTED, () => fn());\n    }\n    onStateChange(fn) {\n        this.on(EVENT.STATE_CHANGE, (newState) => fn(newState));\n    }\n    onError(fn) {\n        this.on(EVENT.CLIENT_ERROR, (err) => fn(err));\n    }\n    onOrderUpdate(fn) {\n        this.on(EVENT.ORDER_UPDATE, (orderUpdate) => fn(orderUpdate));\n    }\n    onAccountUpdate(fn) {\n        this.on(EVENT.ACCOUNT_UPDATE, (accountUpdate) => fn(accountUpdate));\n    }\n    onPolygonConnect(fn) {\n        this.polygon.on(STATE.CONNECTED, () => fn());\n    }\n    onPolygonDisconnect(fn) {\n        this.polygon.on(STATE.DISCONNECTED, () => fn());\n    }\n    onStockTrades(fn) {\n        if (this.session.usePolygon) {\n            this.polygon.on(EVENT.STOCK_TRADES, function (subject, data) {\n                fn(subject, data);\n            });\n        }\n        else {\n            this.on(EVENT.STOCK_TRADES, function (subject, data) {\n                fn(subject, data);\n            });\n        }\n    }\n    onStockQuotes(fn) {\n        if (this.session.usePolygon) {\n            this.polygon.on(EVENT.STOCK_QUOTES, function (subject, data) {\n                fn(subject, data);\n            });\n        }\n        else {\n            this.on(EVENT.STOCK_QUOTES, function (subject, data) {\n                fn(subject, data);\n            });\n        }\n    }\n    onStockAggSec(fn) {\n        this.polygon.on(EVENT.STOCK_AGG_SEC, function (subject, data) {\n            fn(subject, data);\n        });\n    }\n    onStockAggMin(fn) {\n        if (this.session.usePolygon) {\n            this.polygon.on(EVENT.STOCK_AGG_MIN, function (subject, data) {\n                fn(subject, data);\n            });\n        }\n        else {\n            this.on(EVENT.STOCK_AGG_MIN, function (subject, data) {\n                fn(subject, data);\n            });\n        }\n    }\n    send(data) {\n        this.conn.send(data);\n    }\n    disconnect() {\n        this.reconnectDisabled = true;\n        this.conn.close();\n        if (this.polygon) {\n            this.polygon.close();\n        }\n    }\n    state() {\n        return this.currentState;\n    }\n    get(key) {\n        return this.session[key];\n    }\n    reconnect() {\n        setTimeout(() => {\n            if (this.session.backoff) {\n                this.session.reconnectTimeout += this.session.backoffIncrement;\n                if (this.session.reconnectTimeout > this.session.maxReconnectTimeout) {\n                    this.session.reconnectTimeout = this.session.maxReconnectTimeout;\n                }\n            }\n            this.connect();\n        }, this.session.reconnectTimeout * 1000);\n        this.emit(STATE.WAITING_TO_RECONNECT, this.session.reconnectTimeout);\n    }\n    authenticate() {\n        this.emit(STATE.AUTHENTICATING);\n        const authMsg = {\n            action: \"authenticate\",\n            data: {\n                key_id: this.session.apiKey,\n                secret_key: this.session.secretKey,\n            },\n        };\n        this.send(JSON.stringify(authMsg));\n    }\n    handleMessage(data) {\n        // Heartbeat\n        const bytes = new Uint8Array(data);\n        if (bytes.length === 1 && bytes[0] === 1) {\n            return;\n        }\n        let message = JSON.parse(data);\n        const subject = message.stream;\n        if (\"error\" in message.data) {\n            console.log(message.data.error);\n        }\n        switch (subject) {\n            case \"authorization\":\n                this.authResultHandler(message.data.status);\n                break;\n            case \"listening\":\n                this.log(`listening to the streams: ${message.data.streams}`);\n                break;\n            case \"trade_updates\":\n                this.emit(EVENT.ORDER_UPDATE, message.data);\n                break;\n            case \"account_updates\":\n                this.emit(EVENT.ACCOUNT_UPDATE, message.data);\n                break;\n            default:\n                if (message.stream.startsWith(\"T.\")) {\n                    this.emit(EVENT.STOCK_TRADES, subject, entity.AlpacaTrade(message.data));\n                }\n                else if (message.stream.startsWith(\"Q.\")) {\n                    this.emit(EVENT.STOCK_QUOTES, subject, entity.AlpacaQuote(message.data));\n                }\n                else if (message.stream.startsWith(\"AM.\")) {\n                    this.emit(EVENT.STOCK_AGG_MIN, subject, entity.AggMinuteBar(message.data));\n                }\n                else {\n                    this.emit(ERROR.PROTOBUF);\n                }\n        }\n    }\n    authResultHandler(authResult) {\n        switch (authResult) {\n            case \"authorized\":\n                this.emit(STATE.CONNECTED);\n                break;\n            case \"unauthorized\":\n                this.emit(ERROR.BAD_KEY_OR_SECRET);\n                this.disconnect();\n                break;\n            default:\n                break;\n        }\n    }\n    log(level, ...msg) {\n        if (this.session.verbose) {\n            console[level](...msg);\n        }\n    }\n}\nexports.AlpacaStreamClient = AlpacaStreamClient;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM;AACN,MAAM;AACN,YAAY;AACZ,wEAAwE;AACxE,qEAAqE;AACrE,IAAI;AACJ,CAAC,SAAU,KAAK;IACZ,MAAM,cAAc,GAAG;IACvB,MAAM,SAAS,GAAG;IAClB,MAAM,UAAU,GAAG;IACnB,MAAM,YAAY,GAAG;IACrB,MAAM,kBAAkB,GAAG;IAC3B,MAAM,oBAAoB,GAAG;AACjC,CAAC,EAAG,QAAQ,QAAQ,KAAK,IAAI,CAAC,QAAQ,KAAK,GAAG,CAAC,CAAC;AAChD,gBAAgB;AAChB,IAAI;AACJ,CAAC,SAAU,KAAK;IACZ,MAAM,YAAY,GAAG;IACrB,MAAM,YAAY,GAAG;IACrB,MAAM,UAAU,GAAG;IACnB,MAAM,YAAY,GAAG;IACrB,MAAM,YAAY,GAAG;IACrB,MAAM,cAAc,GAAG;IACvB,MAAM,YAAY,GAAG;IACrB,MAAM,YAAY,GAAG;IACrB,MAAM,aAAa,GAAG;IACtB,MAAM,aAAa,GAAG;AAC1B,CAAC,EAAG,QAAQ,QAAQ,KAAK,IAAI,CAAC,QAAQ,KAAK,GAAG,CAAC,CAAC;AAChD,6DAA6D;AAC7D,IAAI;AACJ,CAAC,SAAU,KAAK;IACZ,MAAM,iBAAiB,GAAG;IAC1B,MAAM,kBAAkB,GAAG;IAC3B,MAAM,eAAe,GAAG;IACxB,MAAM,kBAAkB,GAAG;IAC3B,MAAM,OAAO,GAAG;AACpB,CAAC,EAAG,QAAQ,QAAQ,KAAK,IAAI,CAAC,QAAQ,KAAK,GAAG,CAAC,CAAC;AAChD;;CAEC,GACD,MAAM,2BAA2B,OAAO,YAAY;IAChD,YAAY,OAAO,CAAC,CAAC,CAAE;QACnB,KAAK;QACL,IAAI,CAAC,cAAc,GAAG;YAClB,wDAAwD;YACxD,eAAe,EAAE;YACjB,qDAAqD;YACrD,WAAW;YACX,8EAA8E;YAC9E,gFAAgF;YAChF,yCAAyC;YACzC,SAAS;YACT,mFAAmF;YACnF,kBAAkB;YAClB,0EAA0E;YAC1E,qBAAqB;YACrB,2EAA2E;YAC3E,kBAAkB;YAClB,gDAAgD;YAChD,SAAS;YACT,mEAAmE;YACnE,wBAAwB;YACxB,YAAY;QAChB;QACA,sDAAsD;QACtD,IAAI,CAAC,KAAK,OAAO,IAAI,KAAK,gBAAgB,GAAG,GAAG;YAC5C,KAAK,gBAAgB,GAAG;QAC5B;QACA,uCAAuC;QACvC,IAAI,CAAC,OAAO,GAAG,OAAO,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE;QAClD,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,QAAQ;QAC7D,4EAA4E;QAC5E,yBAAyB;QACzB,IAAI,CAAC,iBAAiB,GAAG,CAAC;QAC1B,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YAChC,IAAI,CAAC,iBAAiB,CAAC,EAAE,GAAG;QAChC;QACA,IAAI,CAAC,YAAY,GAAG,MAAM,kBAAkB;QAC5C,mCAAmC;QACnC,kCAAkC;QAClC,OAAO,IAAI,CAAC,OAAO,OAAO,CAAC,CAAC;YACxB,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,EAAE;gBACd,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,EAAE;gBAC5B,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,cAAc,EAAE,KAAK,CAAC,EAAE,EAAE;gBAC5C,IAAI,CAAC,IAAI,CAAC,MAAM,YAAY,EAAE,KAAK,CAAC,EAAE;YAC1C;QACJ;QACA,2BAA2B;QAC3B,OAAO,IAAI,CAAC,OAAO,OAAO,CAAC,CAAC;YACxB,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,EAAE;gBACd,IAAI,CAAC,GAAG,CAAC,SAAS,KAAK,CAAC,EAAE;gBAC1B,IAAI,CAAC,IAAI,CAAC,MAAM,YAAY,EAAE,KAAK,CAAC,EAAE;YAC1C;QACJ;IACJ;IACA,UAAU;QACN,wBAAwB;QACxB,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,KAAK,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,KAAK,GAAG;YACrE,MAAM,IAAI,MAAM,MAAM,eAAe;QACzC;QACA,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,KAAK,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,KAAK,GAAG;YACxE,MAAM,IAAI,MAAM,MAAM,kBAAkB;QAC5C;QACA,gEAAgE;QAChE,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,IAAI,CAAC,MAAM,UAAU;QAC1B,IAAI,CAAC,IAAI,GAAG,IAAI,UAAU,IAAI,CAAC,OAAO,CAAC,GAAG;QAC1C,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ;YACnB,IAAI,CAAC,YAAY;QACrB;QACA,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,OAAS,IAAI,CAAC,aAAa,CAAC;QACrD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;YACrB,IAAI,CAAC,IAAI,CAAC,MAAM,kBAAkB;QACtC;QACA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS;YACpB,IAAI,CAAC,IAAI,CAAC,MAAM,YAAY;YAC5B,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;gBACnD,IAAI,CAAC,SAAS;YAClB;QACJ;IACJ;IACA,gBAAgB,QAAQ,EAAE;QACtB,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;YAC5B,IAAI,UAAU;gBACV,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;YAC3B;YACA;QACJ;QACA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;IACzB;IACA,qBAAqB,QAAQ,EAAE;QAC3B,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;YAC5B,IAAI,UAAU;gBACV,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;YAC7B;QACJ;IACJ;IACA,UAAU,IAAI,EAAE;QACZ,IAAI,aAAa,EAAE;QACnB,IAAI,kBAAkB,EAAE;QACxB,KAAK,OAAO,CAAC,CAAC;YACV,MAAM,OAAO;gBAAC;gBAAM;gBAAM;gBAAM;aAAM;YACtC,IAAI,QAAQ,KAAK,MAAM,CAAC,CAAC,UAAY,IAAI,UAAU,CAAC;YACpD,IAAI,MAAM,MAAM,GAAG,GAAG;gBAClB,gBAAgB,IAAI,CAAC;YACzB,OACK;gBACD,WAAW,IAAI,CAAC;YACpB;QACJ;QACA,IAAI,WAAW,MAAM,GAAG,GAAG;YACvB,MAAM,SAAS;gBACX,QAAQ;gBACR,MAAM;oBACF,SAAS;gBACb;YACJ;YACA,IAAI,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC;QAC7B;QACA,IAAI,gBAAgB,MAAM,GAAG,GAAG;YAC5B,IAAI,CAAC,eAAe,CAAC;QACzB;QACA,KAAK,OAAO,CAAC,CAAC;YACV,IAAI,CAAC,iBAAiB,CAAC,EAAE,GAAG;QAChC;IACJ;IACA,YAAY,IAAI,EAAE;QACd,4DAA4D;QAC5D,IAAI,kBAAkB,EAAE;QACxB,KAAK,OAAO,CAAC,CAAC;YACV,MAAM,OAAO;gBAAC;gBAAM;gBAAM;gBAAM;aAAM;YACtC,IAAI,QAAQ,KAAK,MAAM,CAAC,CAAC,UAAY,IAAI,UAAU,CAAC;YACpD,IAAI,MAAM,MAAM,GAAG,GAAG;gBAClB,gBAAgB,IAAI,CAAC;YACzB;QACJ;QACA,IAAI,gBAAgB,MAAM,GAAG,GAAG;YAC5B,IAAI,CAAC,oBAAoB,CAAC;QAC9B;QACA,KAAK,OAAO,CAAC,CAAC;YACV,IAAI,CAAC,iBAAiB,CAAC,EAAE,GAAG;QAChC;IACJ;IACA,gBAAgB;QACZ,qEAAqE;QACrE,qEAAqE;QACrE,kBAAkB;QAClB,OAAO,OAAO,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC,IAAM,IAAI,CAAC,iBAAiB,CAAC,EAAE;IACtF;IACA,UAAU,EAAE,EAAE;QACV,IAAI,CAAC,EAAE,CAAC,MAAM,SAAS,EAAE,IAAM;IACnC;IACA,aAAa,EAAE,EAAE;QACb,IAAI,CAAC,EAAE,CAAC,MAAM,YAAY,EAAE,IAAM;IACtC;IACA,cAAc,EAAE,EAAE;QACd,IAAI,CAAC,EAAE,CAAC,MAAM,YAAY,EAAE,CAAC,WAAa,GAAG;IACjD;IACA,QAAQ,EAAE,EAAE;QACR,IAAI,CAAC,EAAE,CAAC,MAAM,YAAY,EAAE,CAAC,MAAQ,GAAG;IAC5C;IACA,cAAc,EAAE,EAAE;QACd,IAAI,CAAC,EAAE,CAAC,MAAM,YAAY,EAAE,CAAC,cAAgB,GAAG;IACpD;IACA,gBAAgB,EAAE,EAAE;QAChB,IAAI,CAAC,EAAE,CAAC,MAAM,cAAc,EAAE,CAAC,gBAAkB,GAAG;IACxD;IACA,iBAAiB,EAAE,EAAE;QACjB,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,SAAS,EAAE,IAAM;IAC3C;IACA,oBAAoB,EAAE,EAAE;QACpB,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,YAAY,EAAE,IAAM;IAC9C;IACA,cAAc,EAAE,EAAE;QACd,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;YACzB,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,YAAY,EAAE,SAAU,OAAO,EAAE,IAAI;gBACvD,GAAG,SAAS;YAChB;QACJ,OACK;YACD,IAAI,CAAC,EAAE,CAAC,MAAM,YAAY,EAAE,SAAU,OAAO,EAAE,IAAI;gBAC/C,GAAG,SAAS;YAChB;QACJ;IACJ;IACA,cAAc,EAAE,EAAE;QACd,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;YACzB,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,YAAY,EAAE,SAAU,OAAO,EAAE,IAAI;gBACvD,GAAG,SAAS;YAChB;QACJ,OACK;YACD,IAAI,CAAC,EAAE,CAAC,MAAM,YAAY,EAAE,SAAU,OAAO,EAAE,IAAI;gBAC/C,GAAG,SAAS;YAChB;QACJ;IACJ;IACA,cAAc,EAAE,EAAE;QACd,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,aAAa,EAAE,SAAU,OAAO,EAAE,IAAI;YACxD,GAAG,SAAS;QAChB;IACJ;IACA,cAAc,EAAE,EAAE;QACd,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;YACzB,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,aAAa,EAAE,SAAU,OAAO,EAAE,IAAI;gBACxD,GAAG,SAAS;YAChB;QACJ,OACK;YACD,IAAI,CAAC,EAAE,CAAC,MAAM,aAAa,EAAE,SAAU,OAAO,EAAE,IAAI;gBAChD,GAAG,SAAS;YAChB;QACJ;IACJ;IACA,KAAK,IAAI,EAAE;QACP,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;IACnB;IACA,aAAa;QACT,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,IAAI,CAAC,KAAK;QACf,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,OAAO,CAAC,KAAK;QACtB;IACJ;IACA,QAAQ;QACJ,OAAO,IAAI,CAAC,YAAY;IAC5B;IACA,IAAI,GAAG,EAAE;QACL,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI;IAC5B;IACA,YAAY;QACR,WAAW;YACP,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;gBACtB,IAAI,CAAC,OAAO,CAAC,gBAAgB,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB;gBAC9D,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;oBAClE,IAAI,CAAC,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB;gBACpE;YACJ;YACA,IAAI,CAAC,OAAO;QAChB,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,GAAG;QACnC,IAAI,CAAC,IAAI,CAAC,MAAM,oBAAoB,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB;IACvE;IACA,eAAe;QACX,IAAI,CAAC,IAAI,CAAC,MAAM,cAAc;QAC9B,MAAM,UAAU;YACZ,QAAQ;YACR,MAAM;gBACF,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM;gBAC3B,YAAY,IAAI,CAAC,OAAO,CAAC,SAAS;YACtC;QACJ;QACA,IAAI,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC;IAC7B;IACA,cAAc,IAAI,EAAE;QAChB,YAAY;QACZ,MAAM,QAAQ,IAAI,WAAW;QAC7B,IAAI,MAAM,MAAM,KAAK,KAAK,KAAK,CAAC,EAAE,KAAK,GAAG;YACtC;QACJ;QACA,IAAI,UAAU,KAAK,KAAK,CAAC;QACzB,MAAM,UAAU,QAAQ,MAAM;QAC9B,IAAI,WAAW,QAAQ,IAAI,EAAE;YACzB,QAAQ,GAAG,CAAC,QAAQ,IAAI,CAAC,KAAK;QAClC;QACA,OAAQ;YACJ,KAAK;gBACD,IAAI,CAAC,iBAAiB,CAAC,QAAQ,IAAI,CAAC,MAAM;gBAC1C;YACJ,KAAK;gBACD,IAAI,CAAC,GAAG,CAAC,CAAC,0BAA0B,EAAE,QAAQ,IAAI,CAAC,OAAO,EAAE;gBAC5D;YACJ,KAAK;gBACD,IAAI,CAAC,IAAI,CAAC,MAAM,YAAY,EAAE,QAAQ,IAAI;gBAC1C;YACJ,KAAK;gBACD,IAAI,CAAC,IAAI,CAAC,MAAM,cAAc,EAAE,QAAQ,IAAI;gBAC5C;YACJ;gBACI,IAAI,QAAQ,MAAM,CAAC,UAAU,CAAC,OAAO;oBACjC,IAAI,CAAC,IAAI,CAAC,MAAM,YAAY,EAAE,SAAS,OAAO,WAAW,CAAC,QAAQ,IAAI;gBAC1E,OACK,IAAI,QAAQ,MAAM,CAAC,UAAU,CAAC,OAAO;oBACtC,IAAI,CAAC,IAAI,CAAC,MAAM,YAAY,EAAE,SAAS,OAAO,WAAW,CAAC,QAAQ,IAAI;gBAC1E,OACK,IAAI,QAAQ,MAAM,CAAC,UAAU,CAAC,QAAQ;oBACvC,IAAI,CAAC,IAAI,CAAC,MAAM,aAAa,EAAE,SAAS,OAAO,YAAY,CAAC,QAAQ,IAAI;gBAC5E,OACK;oBACD,IAAI,CAAC,IAAI,CAAC,MAAM,QAAQ;gBAC5B;QACR;IACJ;IACA,kBAAkB,UAAU,EAAE;QAC1B,OAAQ;YACJ,KAAK;gBACD,IAAI,CAAC,IAAI,CAAC,MAAM,SAAS;gBACzB;YACJ,KAAK;gBACD,IAAI,CAAC,IAAI,CAAC,MAAM,iBAAiB;gBACjC,IAAI,CAAC,UAAU;gBACf;YACJ;gBACI;QACR;IACJ;IACA,IAAI,KAAK,EAAE,GAAG,GAAG,EAAE;QACf,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YACtB,OAAO,CAAC,MAAM,IAAI;QACtB;IACJ;AACJ;AACA,QAAQ,kBAAkB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/node_modules/%40alpacahq/alpaca-trade-api/dist/alpaca-trade-api.js"], "sourcesContent": ["\"use strict\";\nrequire(\"dotenv\").config();\nconst api = require(\"./api\");\nconst account = require(\"./resources/account\");\nconst position = require(\"./resources/position\");\nconst calendar = require(\"./resources/calendar\");\nconst clock = require(\"./resources/clock\");\nconst asset = require(\"./resources/asset\");\nconst order = require(\"./resources/order\");\nconst watchlist = require(\"./resources/watchlist\");\nconst dataV2 = require(\"./resources/datav2/rest_v2\");\nconst entityV2 = require(\"./resources/datav2/entityv2\");\nconst crypto_websocket = require(\"./resources/datav2/crypto_websocket_v1beta3\");\nconst news_stream = require(\"./resources/datav2/news_websocket\");\nconst websockets_v2 = require(\"./resources/datav2/stock_websocket_v2\");\nconst option_stream = require(\"./resources/datav2/option_websocket_v1beta1\");\nconst websockets = require(\"./resources/websockets\");\nclass Alpaca {\n    constructor(config = {}) {\n        // Helper methods\n        this.httpRequest = api.httpRequest.bind(this);\n        this.dataHttpRequest = api.dataHttpRequest;\n        // Account\n        this.getAccount = account.get;\n        this.updateAccountConfigurations = account.updateConfigs;\n        this.getAccountConfigurations = account.getConfigs;\n        this.getAccountActivities = account.getActivities;\n        this.getPortfolioHistory = account.getPortfolioHistory;\n        // Positions\n        this.getPositions = position.getAll;\n        this.getPosition = position.getOne;\n        this.closeAllPositions = position.closeAll;\n        this.closePosition = position.closeOne;\n        // Calendar\n        this.getCalendar = calendar.get;\n        // Clock\n        this.getClock = clock.get;\n        // Asset\n        this.getAssets = asset.getAll;\n        this.getAsset = asset.getOne;\n        // Order\n        this.getOrders = order.getAll;\n        this.getOrder = order.getOne;\n        this.getOrderByClientId = order.getByClientOrderId;\n        this.createOrder = order.post;\n        this.replaceOrder = order.patchOrder;\n        this.cancelOrder = order.cancel;\n        this.cancelAllOrders = order.cancelAll;\n        // Watchlists\n        this.getWatchlists = watchlist.getAll;\n        this.getWatchlist = watchlist.getOne;\n        this.addWatchlist = watchlist.addWatchlist;\n        this.addToWatchlist = watchlist.addToWatchlist;\n        this.updateWatchlist = watchlist.updateWatchlist;\n        this.deleteWatchlist = watchlist.deleteWatchlist;\n        this.deleteFromWatchlist = watchlist.deleteFromWatchlist;\n        this.configuration = {\n            baseUrl: config.baseUrl ||\n                process.env.APCA_API_BASE_URL ||\n                (config.paper\n                    ? \"https://paper-api.alpaca.markets\"\n                    : \"https://api.alpaca.markets\"),\n            dataBaseUrl: config.dataBaseUrl ||\n                process.env.APCA_DATA_BASE_URL ||\n                process.env.DATA_PROXY_WS ||\n                \"https://data.alpaca.markets\",\n            dataStreamUrl: config.dataStreamUrl ||\n                process.env.APCA_API_STREAM_URL ||\n                \"https://stream.data.alpaca.markets\",\n            keyId: config.keyId || process.env.APCA_API_KEY_ID || \"\",\n            secretKey: config.secretKey || process.env.APCA_API_SECRET_KEY || \"\",\n            apiVersion: config.apiVersion || process.env.APCA_API_VERSION || \"v2\",\n            oauth: config.oauth || process.env.APCA_API_OAUTH || \"\",\n            feed: config.feed || \"iex\", // use 'sip' if you have PRO subscription\n            optionFeed: config.optionFeed || \"indicative\", // use 'opra' if you have PRO subscription\n            verbose: config.verbose,\n        };\n        this.data_ws = new websockets.AlpacaStreamClient({\n            url: this.configuration.dataBaseUrl,\n            apiKey: this.configuration.keyId,\n            secretKey: this.configuration.secretKey,\n            oauth: this.configuration.oauth,\n        });\n        this.data_ws.STATE = websockets.STATE;\n        this.data_ws.EVENT = websockets.EVENT;\n        this.data_ws.ERROR = websockets.ERROR;\n        this.trade_ws = new websockets.AlpacaStreamClient({\n            url: this.configuration.baseUrl,\n            apiKey: this.configuration.keyId,\n            secretKey: this.configuration.secretKey,\n            oauth: this.configuration.oauth,\n        });\n        this.trade_ws.STATE = websockets.STATE;\n        this.trade_ws.EVENT = websockets.EVENT;\n        this.trade_ws.ERROR = websockets.ERROR;\n        this.data_stream_v2 = new websockets_v2.AlpacaStocksClient({\n            url: this.configuration.dataStreamUrl,\n            feed: this.configuration.feed,\n            apiKey: this.configuration.keyId,\n            secretKey: this.configuration.secretKey,\n            verbose: this.configuration.verbose,\n        });\n        this.adjustment = dataV2.Adjustment;\n        this.timeframeUnit = entityV2.TimeFrameUnit;\n        this.crypto_stream_v1beta3 = new crypto_websocket.AlpacaCryptoClient({\n            url: this.configuration.dataStreamUrl,\n            apiKey: this.configuration.keyId,\n            secretKey: this.configuration.secretKey,\n            verbose: this.configuration.verbose,\n        });\n        this.news_stream = new news_stream.AlpacaNewsCLient({\n            url: this.configuration.dataStreamUrl,\n            apiKey: this.configuration.keyId,\n            secretKey: this.configuration.secretKey,\n            verbose: this.configuration.verbose,\n        });\n        this.option_stream = new option_stream.AlpacaOptionClient({\n            url: this.configuration.dataStreamUrl,\n            feed: this.configuration.optionFeed,\n            apiKey: this.configuration.keyId,\n            secretKey: this.configuration.secretKey,\n            verbose: this.configuration.verbose,\n        });\n    }\n    sendRequest(endpoint, queryParams, body, method) {\n        return api.sendRequest(this.httpRequest, endpoint, queryParams, body, method);\n    }\n    //DataV2\n    getTradesV2(symbol, options, config = this.configuration) {\n        return dataV2.getTrades(symbol, options, config);\n    }\n    getMultiTradesV2(symbols, options, config = this.configuration) {\n        return dataV2.getMultiTrades(symbols, options, config);\n    }\n    getMultiTradesAsyncV2(symbols, options, config = this.configuration) {\n        return dataV2.getMultiTradesAsync(symbols, options, config);\n    }\n    getQuotesV2(symbol, options, config = this.configuration) {\n        return dataV2.getQuotes(symbol, options, config);\n    }\n    getMultiQuotesV2(symbols, options, config = this.configuration) {\n        return dataV2.getMultiQuotes(symbols, options, config);\n    }\n    getMultiQuotesAsyncV2(symbols, options, config = this.configuration) {\n        return dataV2.getMultiQuotesAsync(symbols, options, config);\n    }\n    getBarsV2(symbol, options, config = this.configuration) {\n        return dataV2.getBars(symbol, options, config);\n    }\n    getMultiBarsV2(symbols, options, config = this.configuration) {\n        return dataV2.getMultiBars(symbols, options, config);\n    }\n    getMultiBarsAsyncV2(symbols, options, config = this.configuration) {\n        return dataV2.getMultiBarsAsync(symbols, options, config);\n    }\n    getLatestTrade(symbol, config = this.configuration) {\n        return dataV2.getLatestTrade(symbol, config);\n    }\n    getLatestTrades(symbols, config = this.configuration) {\n        return dataV2.getLatestTrades(symbols, config);\n    }\n    getLatestQuote(symbol, config = this.configuration) {\n        return dataV2.getLatestQuote(symbol, config);\n    }\n    getLatestQuotes(symbols, config = this.configuration) {\n        return dataV2.getLatestQuotes(symbols, config);\n    }\n    getLatestBar(symbol, config = this.configuration) {\n        return dataV2.getLatestBar(symbol, config);\n    }\n    getLatestBars(symbols, config = this.configuration) {\n        return dataV2.getLatestBars(symbols, config);\n    }\n    getSnapshot(symbol, config = this.configuration) {\n        return dataV2.getSnapshot(symbol, config);\n    }\n    getSnapshots(symbols, config = this.configuration) {\n        return dataV2.getSnapshots(symbols, config);\n    }\n    getCryptoTrades(symbols, options, config = this.configuration) {\n        return dataV2.getCryptoTrades(symbols, options, config);\n    }\n    getCryptoQuotes(symbols, options, config = this.configuration) {\n        return dataV2.getCryptoQuotes(symbols, options, config);\n    }\n    getCryptoBars(symbols, options, config = this.configuration) {\n        return dataV2.getCryptoBars(symbols, options, config);\n    }\n    getLatestCryptoTrades(symbols, config = this.configuration) {\n        return dataV2.getLatestCryptoTrades(symbols, config);\n    }\n    getLatestCryptoQuotes(symbols, config = this.configuration) {\n        return dataV2.getLatestCryptoQuotes(symbols, config);\n    }\n    getLatestCryptoBars(symbols, config = this.configuration) {\n        return dataV2.getLatestCryptoBars(symbols, config);\n    }\n    getCryptoSnapshots(symbols, config = this.configuration) {\n        return dataV2.getCryptoSnapshots(symbols, config);\n    }\n    getCryptoOrderbooks(symbols, config = this.configuration) {\n        return dataV2.getLatestCryptoOrderbooks(symbols, config);\n    }\n    getOptionBars(symbols, options, config = this.configuration) {\n        return dataV2.getMultiOptionBars(symbols, options, config);\n    }\n    getOptionTrades(symbols, options, config = this.configuration) {\n        return dataV2.getMultiOptionTrades(symbols, options, config);\n    }\n    getOptionLatestTrades(symbols, config = this.configuration) {\n        return dataV2.getLatestOptionTrades(symbols, config);\n    }\n    getOptionLatestQuotes(symbols, config = this.configuration) {\n        return dataV2.getLatestOptionQuotes(symbols, config);\n    }\n    getOptionSnapshots(symbols, config = this.configuration) {\n        return dataV2.getOptionSnapshots(symbols, config);\n    }\n    getOptionChain(underlying_symbol, options, config = this.configuration) {\n        return dataV2.getOptionChain(underlying_symbol, options, config);\n    }\n    getCorporateActions(symbols, options, config = this.configuration) {\n        return dataV2.getCorporateActions(symbols, options, config);\n    }\n    getNews(options, config = this.configuration) {\n        return dataV2.getNews(options, config);\n    }\n    newTimeframe(amount, unit) {\n        return entityV2.NewTimeframe(amount, unit);\n    }\n}\nmodule.exports = Alpaca;\n"], "names": [], "mappings": "AACA,8GAAkB,MAAM;AACxB,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;IACF,YAAY,SAAS,CAAC,CAAC,CAAE;QACrB,iBAAiB;QACjB,IAAI,CAAC,WAAW,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI;QAC5C,IAAI,CAAC,eAAe,GAAG,IAAI,eAAe;QAC1C,UAAU;QACV,IAAI,CAAC,UAAU,GAAG,QAAQ,GAAG;QAC7B,IAAI,CAAC,2BAA2B,GAAG,QAAQ,aAAa;QACxD,IAAI,CAAC,wBAAwB,GAAG,QAAQ,UAAU;QAClD,IAAI,CAAC,oBAAoB,GAAG,QAAQ,aAAa;QACjD,IAAI,CAAC,mBAAmB,GAAG,QAAQ,mBAAmB;QACtD,YAAY;QACZ,IAAI,CAAC,YAAY,GAAG,SAAS,MAAM;QACnC,IAAI,CAAC,WAAW,GAAG,SAAS,MAAM;QAClC,IAAI,CAAC,iBAAiB,GAAG,SAAS,QAAQ;QAC1C,IAAI,CAAC,aAAa,GAAG,SAAS,QAAQ;QACtC,WAAW;QACX,IAAI,CAAC,WAAW,GAAG,SAAS,GAAG;QAC/B,QAAQ;QACR,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAG;QACzB,QAAQ;QACR,IAAI,CAAC,SAAS,GAAG,MAAM,MAAM;QAC7B,IAAI,CAAC,QAAQ,GAAG,MAAM,MAAM;QAC5B,QAAQ;QACR,IAAI,CAAC,SAAS,GAAG,MAAM,MAAM;QAC7B,IAAI,CAAC,QAAQ,GAAG,MAAM,MAAM;QAC5B,IAAI,CAAC,kBAAkB,GAAG,MAAM,kBAAkB;QAClD,IAAI,CAAC,WAAW,GAAG,MAAM,IAAI;QAC7B,IAAI,CAAC,YAAY,GAAG,MAAM,UAAU;QACpC,IAAI,CAAC,WAAW,GAAG,MAAM,MAAM;QAC/B,IAAI,CAAC,eAAe,GAAG,MAAM,SAAS;QACtC,aAAa;QACb,IAAI,CAAC,aAAa,GAAG,UAAU,MAAM;QACrC,IAAI,CAAC,YAAY,GAAG,UAAU,MAAM;QACpC,IAAI,CAAC,YAAY,GAAG,UAAU,YAAY;QAC1C,IAAI,CAAC,cAAc,GAAG,UAAU,cAAc;QAC9C,IAAI,CAAC,eAAe,GAAG,UAAU,eAAe;QAChD,IAAI,CAAC,eAAe,GAAG,UAAU,eAAe;QAChD,IAAI,CAAC,mBAAmB,GAAG,UAAU,mBAAmB;QACxD,IAAI,CAAC,aAAa,GAAG;YACjB,SAAS,OAAO,OAAO,IACnB,QAAQ,GAAG,CAAC,iBAAiB,IAC7B,CAAC,OAAO,KAAK,GACP,qCACA,4BAA4B;YACtC,aAAa,OAAO,WAAW,IAC3B,QAAQ,GAAG,CAAC,kBAAkB,IAC9B,QAAQ,GAAG,CAAC,aAAa,IACzB;YACJ,eAAe,OAAO,aAAa,IAC/B,QAAQ,GAAG,CAAC,mBAAmB,IAC/B;YACJ,OAAO,OAAO,KAAK,IAAI,QAAQ,GAAG,CAAC,eAAe,IAAI;YACtD,WAAW,OAAO,SAAS,IAAI,QAAQ,GAAG,CAAC,mBAAmB,IAAI;YAClE,YAAY,OAAO,UAAU,IAAI,QAAQ,GAAG,CAAC,gBAAgB,IAAI;YACjE,OAAO,OAAO,KAAK,IAAI,QAAQ,GAAG,CAAC,cAAc,IAAI;YACrD,MAAM,OAAO,IAAI,IAAI;YACrB,YAAY,OAAO,UAAU,IAAI;YACjC,SAAS,OAAO,OAAO;QAC3B;QACA,IAAI,CAAC,OAAO,GAAG,IAAI,WAAW,kBAAkB,CAAC;YAC7C,KAAK,IAAI,CAAC,aAAa,CAAC,WAAW;YACnC,QAAQ,IAAI,CAAC,aAAa,CAAC,KAAK;YAChC,WAAW,IAAI,CAAC,aAAa,CAAC,SAAS;YACvC,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK;QACnC;QACA,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,WAAW,KAAK;QACrC,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,WAAW,KAAK;QACrC,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,WAAW,KAAK;QACrC,IAAI,CAAC,QAAQ,GAAG,IAAI,WAAW,kBAAkB,CAAC;YAC9C,KAAK,IAAI,CAAC,aAAa,CAAC,OAAO;YAC/B,QAAQ,IAAI,CAAC,aAAa,CAAC,KAAK;YAChC,WAAW,IAAI,CAAC,aAAa,CAAC,SAAS;YACvC,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK;QACnC;QACA,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,WAAW,KAAK;QACtC,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,WAAW,KAAK;QACtC,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,WAAW,KAAK;QACtC,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,kBAAkB,CAAC;YACvD,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa;YACrC,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI;YAC7B,QAAQ,IAAI,CAAC,aAAa,CAAC,KAAK;YAChC,WAAW,IAAI,CAAC,aAAa,CAAC,SAAS;YACvC,SAAS,IAAI,CAAC,aAAa,CAAC,OAAO;QACvC;QACA,IAAI,CAAC,UAAU,GAAG,OAAO,UAAU;QACnC,IAAI,CAAC,aAAa,GAAG,SAAS,aAAa;QAC3C,IAAI,CAAC,qBAAqB,GAAG,IAAI,iBAAiB,kBAAkB,CAAC;YACjE,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa;YACrC,QAAQ,IAAI,CAAC,aAAa,CAAC,KAAK;YAChC,WAAW,IAAI,CAAC,aAAa,CAAC,SAAS;YACvC,SAAS,IAAI,CAAC,aAAa,CAAC,OAAO;QACvC;QACA,IAAI,CAAC,WAAW,GAAG,IAAI,YAAY,gBAAgB,CAAC;YAChD,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa;YACrC,QAAQ,IAAI,CAAC,aAAa,CAAC,KAAK;YAChC,WAAW,IAAI,CAAC,aAAa,CAAC,SAAS;YACvC,SAAS,IAAI,CAAC,aAAa,CAAC,OAAO;QACvC;QACA,IAAI,CAAC,aAAa,GAAG,IAAI,cAAc,kBAAkB,CAAC;YACtD,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa;YACrC,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU;YACnC,QAAQ,IAAI,CAAC,aAAa,CAAC,KAAK;YAChC,WAAW,IAAI,CAAC,aAAa,CAAC,SAAS;YACvC,SAAS,IAAI,CAAC,aAAa,CAAC,OAAO;QACvC;IACJ;IACA,YAAY,QAAQ,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,EAAE;QAC7C,OAAO,IAAI,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,aAAa,MAAM;IAC1E;IACA,QAAQ;IACR,YAAY,MAAM,EAAE,OAAO,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QACtD,OAAO,OAAO,SAAS,CAAC,QAAQ,SAAS;IAC7C;IACA,iBAAiB,OAAO,EAAE,OAAO,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QAC5D,OAAO,OAAO,cAAc,CAAC,SAAS,SAAS;IACnD;IACA,sBAAsB,OAAO,EAAE,OAAO,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QACjE,OAAO,OAAO,mBAAmB,CAAC,SAAS,SAAS;IACxD;IACA,YAAY,MAAM,EAAE,OAAO,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QACtD,OAAO,OAAO,SAAS,CAAC,QAAQ,SAAS;IAC7C;IACA,iBAAiB,OAAO,EAAE,OAAO,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QAC5D,OAAO,OAAO,cAAc,CAAC,SAAS,SAAS;IACnD;IACA,sBAAsB,OAAO,EAAE,OAAO,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QACjE,OAAO,OAAO,mBAAmB,CAAC,SAAS,SAAS;IACxD;IACA,UAAU,MAAM,EAAE,OAAO,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QACpD,OAAO,OAAO,OAAO,CAAC,QAAQ,SAAS;IAC3C;IACA,eAAe,OAAO,EAAE,OAAO,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QAC1D,OAAO,OAAO,YAAY,CAAC,SAAS,SAAS;IACjD;IACA,oBAAoB,OAAO,EAAE,OAAO,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QAC/D,OAAO,OAAO,iBAAiB,CAAC,SAAS,SAAS;IACtD;IACA,eAAe,MAAM,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QAChD,OAAO,OAAO,cAAc,CAAC,QAAQ;IACzC;IACA,gBAAgB,OAAO,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QAClD,OAAO,OAAO,eAAe,CAAC,SAAS;IAC3C;IACA,eAAe,MAAM,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QAChD,OAAO,OAAO,cAAc,CAAC,QAAQ;IACzC;IACA,gBAAgB,OAAO,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QAClD,OAAO,OAAO,eAAe,CAAC,SAAS;IAC3C;IACA,aAAa,MAAM,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QAC9C,OAAO,OAAO,YAAY,CAAC,QAAQ;IACvC;IACA,cAAc,OAAO,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QAChD,OAAO,OAAO,aAAa,CAAC,SAAS;IACzC;IACA,YAAY,MAAM,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QAC7C,OAAO,OAAO,WAAW,CAAC,QAAQ;IACtC;IACA,aAAa,OAAO,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QAC/C,OAAO,OAAO,YAAY,CAAC,SAAS;IACxC;IACA,gBAAgB,OAAO,EAAE,OAAO,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QAC3D,OAAO,OAAO,eAAe,CAAC,SAAS,SAAS;IACpD;IACA,gBAAgB,OAAO,EAAE,OAAO,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QAC3D,OAAO,OAAO,eAAe,CAAC,SAAS,SAAS;IACpD;IACA,cAAc,OAAO,EAAE,OAAO,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QACzD,OAAO,OAAO,aAAa,CAAC,SAAS,SAAS;IAClD;IACA,sBAAsB,OAAO,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QACxD,OAAO,OAAO,qBAAqB,CAAC,SAAS;IACjD;IACA,sBAAsB,OAAO,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QACxD,OAAO,OAAO,qBAAqB,CAAC,SAAS;IACjD;IACA,oBAAoB,OAAO,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QACtD,OAAO,OAAO,mBAAmB,CAAC,SAAS;IAC/C;IACA,mBAAmB,OAAO,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QACrD,OAAO,OAAO,kBAAkB,CAAC,SAAS;IAC9C;IACA,oBAAoB,OAAO,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QACtD,OAAO,OAAO,yBAAyB,CAAC,SAAS;IACrD;IACA,cAAc,OAAO,EAAE,OAAO,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QACzD,OAAO,OAAO,kBAAkB,CAAC,SAAS,SAAS;IACvD;IACA,gBAAgB,OAAO,EAAE,OAAO,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QAC3D,OAAO,OAAO,oBAAoB,CAAC,SAAS,SAAS;IACzD;IACA,sBAAsB,OAAO,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QACxD,OAAO,OAAO,qBAAqB,CAAC,SAAS;IACjD;IACA,sBAAsB,OAAO,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QACxD,OAAO,OAAO,qBAAqB,CAAC,SAAS;IACjD;IACA,mBAAmB,OAAO,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QACrD,OAAO,OAAO,kBAAkB,CAAC,SAAS;IAC9C;IACA,eAAe,iBAAiB,EAAE,OAAO,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QACpE,OAAO,OAAO,cAAc,CAAC,mBAAmB,SAAS;IAC7D;IACA,oBAAoB,OAAO,EAAE,OAAO,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QAC/D,OAAO,OAAO,mBAAmB,CAAC,SAAS,SAAS;IACxD;IACA,QAAQ,OAAO,EAAE,SAAS,IAAI,CAAC,aAAa,EAAE;QAC1C,OAAO,OAAO,OAAO,CAAC,SAAS;IACnC;IACA,aAAa,MAAM,EAAE,IAAI,EAAE;QACvB,OAAO,SAAS,YAAY,CAAC,QAAQ;IACzC;AACJ;AACA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3471, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/shittyidea/swing-trader-ai/node_modules/%40alpacahq/alpaca-trade-api/dist/index.js"], "sourcesContent": ["\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst alpaca_trade_api_1 = __importDefault(require(\"./alpaca-trade-api\"));\nexports.default = alpaca_trade_api_1.default;\nmodule.exports = alpaca_trade_api_1.default;\n"], "names": [], "mappings": "AACA,IAAI,kBAAkB,4DAAS,yDAAK,eAAe,IAAK,SAAU,GAAG;IACjE,OAAO,AAAC,OAAO,IAAI,UAAU,GAAI,MAAM;QAAE,WAAW;IAAI;AAC5D;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM,qBAAqB;AAC3B,QAAQ,OAAO,GAAG,mBAAmB,OAAO;AAC5C,OAAO,OAAO,GAAG,mBAAmB,OAAO", "ignoreList": [0], "debugId": null}}]}