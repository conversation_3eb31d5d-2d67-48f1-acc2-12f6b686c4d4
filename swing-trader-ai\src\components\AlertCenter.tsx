'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Bell,
  BellRing,
  X,
  Eye,
  Play,
  TrendingUp,
  Zap,
  Target,
  AlertTriangle,
  DollarSign,
  Activity,
  Clock,
  CheckCircle
} from 'lucide-react'
import { Alert, AlertType, alertSystem } from '@/lib/alertSystem'
import { formatCurrency, formatPercentage } from '@/lib/utils'

interface AlertCenterProps {
  className?: string
}

export function AlertCenter({ className }: AlertCenterProps) {
  const [alerts, setAlerts] = useState<Alert[]>([])
  const [isOpen, setIsOpen] = useState(false)
  const [unreadCount, setUnreadCount] = useState(0)

  useEffect(() => {
    // Subscribe to alert updates
    const unsubscribe = alertSystem.subscribe((updatedAlerts) => {
      setAlerts(updatedAlerts)
      setUnreadCount(alertSystem.getUnreadCount())
    })

    // Initial load
    setAlerts(alertSystem.getAlerts())
    setUnreadCount(alertSystem.getUnreadCount())

    return unsubscribe
  }, [])

  const getAlertIcon = (type: AlertType) => {
    switch (type) {
      case 'new_catalyst': return <Zap className="h-4 w-4" />
      case 'perfect_pick_found': return <Target className="h-4 w-4" />
      case 'pre_market_gap': return <TrendingUp className="h-4 w-4" />
      case 'pmh_break': return <Activity className="h-4 w-4" />
      case 'stop_loss_hit': return <AlertTriangle className="h-4 w-4" />
      case 'profit_target_hit': return <DollarSign className="h-4 w-4" />
      case 'entry_trigger': return <Play className="h-4 w-4" />
      case 'volume_spike': return <Activity className="h-4 w-4" />
      default: return <Bell className="h-4 w-4" />
    }
  }

  const getAlertColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'text-red-600 bg-red-50 border-red-200'
      case 'high': return 'text-orange-600 bg-orange-50 border-orange-200'
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'low': return 'text-blue-600 bg-blue-50 border-blue-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const handleAlertAction = async (alert: Alert, actionId: string) => {
    const action = alert.actions?.find(a => a.id === actionId)
    if (!action) return

    switch (action.type) {
      case 'execute_trade':
        console.log('Execute trade:', action.data)
        // TODO: Implement trade execution
        break
      case 'view_chart':
        console.log('View chart:', action.data)
        // TODO: Implement chart viewing
        break
      case 'take_profit':
        console.log('Take profit:', action.data)
        // TODO: Implement profit taking
        break
      case 'update_stop':
        console.log('Update stop:', action.data)
        // TODO: Implement stop loss update
        break
      case 'dismiss':
        alertSystem.removeAlert(alert.id)
        break
    }

    // Mark alert as read after action
    alertSystem.markAsRead(alert.id)
  }

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date()
    const alertTime = new Date(timestamp)
    const diffMs = now.getTime() - alertTime.getTime()
    const diffMins = Math.floor(diffMs / 60000)
    const diffHours = Math.floor(diffMins / 60)

    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffHours < 24) return `${diffHours}h ago`
    return alertTime.toLocaleDateString()
  }

  return (
    <div className={`relative ${className}`}>
      {/* Alert Bell Button */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className="relative"
      >
        {unreadCount > 0 ? (
          <BellRing className="h-4 w-4" />
        ) : (
          <Bell className="h-4 w-4" />
        )}
        {unreadCount > 0 && (
          <Badge className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs bg-red-500">
            {unreadCount > 99 ? '99+' : unreadCount}
          </Badge>
        )}
      </Button>

      {/* Alert Dropdown */}
      {isOpen && (
        <div className="absolute right-0 top-full mt-2 w-96 max-h-96 overflow-y-auto bg-white border rounded-lg shadow-lg z-50">
          <div className="p-4 border-b">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold">Alerts</h3>
              <div className="flex items-center gap-2">
                {unreadCount > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => alertSystem.markAllAsRead()}
                  >
                    Mark all read
                  </Button>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsOpen(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          <div className="max-h-80 overflow-y-auto">
            {alerts.length === 0 ? (
              <div className="p-8 text-center text-muted-foreground">
                <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No alerts yet</p>
              </div>
            ) : (
              alerts.map((alert) => (
                <div
                  key={alert.id}
                  className={`p-4 border-b hover:bg-gray-50 ${
                    !alert.read ? 'bg-blue-50/50' : ''
                  }`}
                  onClick={() => !alert.read && alertSystem.markAsRead(alert.id)}
                >
                  <div className="flex items-start gap-3">
                    <div className={`p-2 rounded-full ${getAlertColor(alert.priority)}`}>
                      {getAlertIcon(alert.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-sm truncate">
                          {alert.title}
                        </h4>
                        {!alert.read && (
                          <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0" />
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        {alert.message}
                      </p>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2 text-xs text-muted-foreground">
                          <Clock className="h-3 w-3" />
                          {formatTimeAgo(alert.timestamp)}
                          <Badge variant="outline" className="text-xs">
                            {alert.priority}
                          </Badge>
                        </div>
                      </div>
                      
                      {/* Alert Actions */}
                      {alert.actionable && alert.actions && alert.actions.length > 0 && (
                        <div className="flex items-center gap-2 mt-2">
                          {alert.actions.map((action) => (
                            <Button
                              key={action.id}
                              variant="outline"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation()
                                handleAlertAction(alert, action.id)
                              }}
                              className="text-xs"
                            >
                              {action.type === 'execute_trade' && <Play className="h-3 w-3 mr-1" />}
                              {action.type === 'view_chart' && <Eye className="h-3 w-3 mr-1" />}
                              {action.type === 'take_profit' && <DollarSign className="h-3 w-3 mr-1" />}
                              {action.label}
                            </Button>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          {alerts.length > 0 && (
            <div className="p-4 border-t">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  alertSystem.clearAllAlerts()
                  setIsOpen(false)
                }}
                className="w-full text-sm"
              >
                Clear all alerts
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

// Alert Toast Component for critical alerts
export function AlertToast({ alert, onDismiss }: { alert: Alert; onDismiss: () => void }) {
  useEffect(() => {
    // Auto-dismiss after 10 seconds for non-critical alerts
    if (alert.priority !== 'critical') {
      const timer = setTimeout(onDismiss, 10000)
      return () => clearTimeout(timer)
    }
  }, [alert.priority, onDismiss])

  return (
    <Card className={`fixed top-4 right-4 w-80 z-50 shadow-lg border-l-4 ${
      alert.priority === 'critical' ? 'border-l-red-500' : 
      alert.priority === 'high' ? 'border-l-orange-500' : 
      'border-l-blue-500'
    }`}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className={`p-1 rounded-full ${getAlertColor(alert.priority)}`}>
              {getAlertIcon(alert.type)}
            </div>
            <CardTitle className="text-sm">{alert.title}</CardTitle>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onDismiss}
            className="h-6 w-6 p-0"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <p className="text-sm text-muted-foreground mb-3">
          {alert.message}
        </p>
        {alert.actionable && alert.actions && (
          <div className="flex gap-2">
            {alert.actions.slice(0, 2).map((action) => (
              <Button
                key={action.id}
                variant={action.type === 'execute_trade' ? 'default' : 'outline'}
                size="sm"
                onClick={() => {
                  // Handle action
                  console.log('Toast action:', action)
                  onDismiss()
                }}
                className="text-xs"
              >
                {action.label}
              </Button>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )

  function getAlertColor(priority: string) {
    switch (priority) {
      case 'critical': return 'text-red-600 bg-red-50'
      case 'high': return 'text-orange-600 bg-orange-50'
      case 'medium': return 'text-yellow-600 bg-yellow-50'
      case 'low': return 'text-blue-600 bg-blue-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  function getAlertIcon(type: AlertType) {
    switch (type) {
      case 'new_catalyst': return <Zap className="h-4 w-4" />
      case 'perfect_pick_found': return <Target className="h-4 w-4" />
      case 'pre_market_gap': return <TrendingUp className="h-4 w-4" />
      case 'pmh_break': return <Activity className="h-4 w-4" />
      case 'stop_loss_hit': return <AlertTriangle className="h-4 w-4" />
      case 'profit_target_hit': return <DollarSign className="h-4 w-4" />
      case 'entry_trigger': return <Play className="h-4 w-4" />
      case 'volume_spike': return <Activity className="h-4 w-4" />
      default: return <Bell className="h-4 w-4" />
    }
  }
}
