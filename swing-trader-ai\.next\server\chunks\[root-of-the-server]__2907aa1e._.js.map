{"version": 3, "sources": ["turbopack:///[project]/swing-trader-ai/src/lib/polygon.ts", "turbopack:///[project]/swing-trader-ai/src/lib/fmp.ts", "turbopack:///[project]/swing-trader-ai/src/lib/catalystDetection.ts", "turbopack:///[project]/swing-trader-ai/src/app/api/catalyst/detect/route.ts", "turbopack:///[project]/swing-trader-ai/node_modules/next/dist/esm/build/templates/app-route.js"], "sourcesContent": ["import axios from 'axios'\nimport { CandlestickData, StockData } from '@/types/trading'\n\nconst POLYGON_BASE_URL = 'https://api.polygon.io'\nconst API_KEY = process.env.POLYGON_API_KEY\n\nexport class PolygonAPI {\n  private apiKey: string\n\n  constructor(apiKey?: string) {\n    this.apiKey = apiKey || API_KEY || ''\n    if (!this.apiKey) {\n      throw new Error('Polygon API key is required')\n    }\n  }\n\n  // Get current stock quote using Polygon's snapshot endpoint (better for paid plans)\n  async getStockQuote(symbol: string): Promise<StockData> {\n    try {\n      // Use snapshot endpoint for real-time data (available on paid plans)\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v2/snapshot/locale/us/markets/stocks/tickers/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      const data = response.data.results[0]\n      if (!data) {\n        throw new Error(`No data found for ${symbol}`)\n      }\n\n      const ticker = data.value || data\n      const dayData = ticker.day || {}\n      const prevDayData = ticker.prevDay || {}\n      const lastQuote = ticker.lastQuote || {}\n      const lastTrade = ticker.lastTrade || {}\n\n      // Use the most recent price available\n      const currentPrice = lastTrade.p || dayData.c || prevDayData.c\n      const prevClose = prevDayData.c || dayData.o\n      const change = currentPrice - prevClose\n      const changePercent = (change / prevClose) * 100\n\n      return {\n        symbol: symbol.toUpperCase(),\n        name: ticker.name || symbol.toUpperCase(),\n        price: currentPrice,\n        change,\n        changePercent,\n        volume: dayData.v || 0,\n        marketCap: ticker.market_cap,\n        pe: undefined,\n        dividend: undefined\n      }\n    } catch (error) {\n      console.error('Error fetching stock quote from Polygon:', error)\n\n      // Fallback to previous day data if snapshot fails\n      try {\n        const fallbackResponse = await axios.get(\n          `${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/prev`,\n          {\n            params: {\n              adjusted: 'true',\n              apikey: this.apiKey\n            }\n          }\n        )\n\n        const data = fallbackResponse.data.results[0]\n        return {\n          symbol: symbol.toUpperCase(),\n          name: symbol.toUpperCase(),\n          price: data.c,\n          change: data.c - data.o,\n          changePercent: ((data.c - data.o) / data.o) * 100,\n          volume: data.v,\n          marketCap: undefined,\n          pe: undefined,\n          dividend: undefined\n        }\n      } catch (fallbackError) {\n        console.error('Polygon fallback also failed:', fallbackError)\n        throw new Error(`Failed to fetch quote for ${symbol}`)\n      }\n    }\n  }\n\n  // Get historical candlestick data (optimized for paid plans)\n  async getHistoricalData(\n    symbol: string,\n    timespan: 'minute' | 'hour' | 'day' | 'week' | 'month' = 'day',\n    multiplier: number = 1,\n    from: string,\n    to: string\n  ): Promise<CandlestickData[]> {\n    try {\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/range/${multiplier}/${timespan}/${from}/${to}`,\n        {\n          params: {\n            adjusted: 'true',\n            sort: 'asc',\n            limit: 50000, // Higher limit for paid plans\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      if (!response.data.results || response.data.results.length === 0) {\n        console.warn(`No historical data found for ${symbol}`)\n        return []\n      }\n\n      return response.data.results.map((candle: any) => ({\n        timestamp: candle.t,\n        open: candle.o,\n        high: candle.h,\n        low: candle.l,\n        close: candle.c,\n        volume: candle.v\n      }))\n    } catch (error) {\n      console.error(`Error fetching historical data for ${symbol}:`, error)\n\n      // Log the specific error for debugging\n      if (error.response) {\n        console.error(`Polygon API Error: ${error.response.status} - ${error.response.statusText}`)\n        console.error('Response data:', error.response.data)\n      }\n\n      throw new Error(`Failed to fetch historical data for ${symbol}: ${error.message}`)\n    }\n  }\n\n  // Get company details\n  async getCompanyDetails(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v3/reference/tickers/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data.results\n    } catch (error) {\n      console.error('Error fetching company details:', error)\n      return null\n    }\n  }\n\n  // Get market status\n  async getMarketStatus() {\n    try {\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v1/marketstatus/now`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error fetching market status:', error)\n      return null\n    }\n  }\n\n  // Search for stocks\n  async searchStocks(query: string, limit: number = 10) {\n    try {\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v3/reference/tickers`,\n        {\n          params: {\n            search: query,\n            market: 'stocks',\n            active: 'true',\n            limit,\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data.results || []\n    } catch (error) {\n      console.error('Error searching stocks:', error)\n      return []\n    }\n  }\n}\n\n// Create a singleton instance\nexport const polygonAPI = new PolygonAPI()\n", "import axios from 'axios'\nimport { StockData } from '@/types/trading'\n\nconst FMP_BASE_URL = 'https://financialmodelingprep.com/api'\nconst API_KEY = process.env.FMP_API_KEY\n\nexport class FMPAPI {\n  private apiKey: string\n\n  constructor(apiKey?: string) {\n    this.apiKey = apiKey || API_KEY || ''\n    if (!this.apiKey) {\n      throw new Error('FMP API key is required')\n    }\n  }\n\n  // Get real-time stock quote\n  async getStockQuote(symbol: string): Promise<StockData> {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/quote/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      const data = response.data[0]\n      if (!data) {\n        throw new Error(`No data found for symbol ${symbol}`)\n      }\n\n      return {\n        symbol: data.symbol,\n        name: data.name || data.symbol,\n        price: data.price,\n        change: data.change,\n        changePercent: data.changesPercentage,\n        volume: data.volume,\n        marketCap: data.marketCap,\n        pe: data.pe,\n        dividend: undefined // Will be fetched separately if needed\n      }\n    } catch (error) {\n      console.error('Error fetching FMP stock quote:', error)\n      throw new Error(`Failed to fetch quote for ${symbol}`)\n    }\n  }\n\n  // Get company profile\n  async getCompanyProfile(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/profile/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data[0]\n    } catch (error) {\n      console.error('Error fetching company profile:', error)\n      return null\n    }\n  }\n\n  // Get financial ratios\n  async getFinancialRatios(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/ratios/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data[0] // Most recent ratios\n    } catch (error) {\n      console.error('Error fetching financial ratios:', error)\n      return null\n    }\n  }\n\n  // Get key metrics\n  async getKeyMetrics(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/key-metrics/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data[0] // Most recent metrics\n    } catch (error) {\n      console.error('Error fetching key metrics:', error)\n      return null\n    }\n  }\n\n  // Get analyst recommendations\n  async getAnalystRecommendations(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/analyst-stock-recommendations/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error fetching analyst recommendations:', error)\n      return []\n    }\n  }\n\n  // Get earnings calendar\n  async getEarningsCalendar(from?: string, to?: string) {\n    try {\n      const params: any = {\n        apikey: this.apiKey\n      }\n\n      if (from) params.from = from\n      if (to) params.to = to\n\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/earning_calendar`,\n        { params }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error fetching earnings calendar:', error)\n      return []\n    }\n  }\n\n  // Get economic calendar\n  async getEconomicCalendar(from?: string, to?: string) {\n    try {\n      const params: any = {\n        apikey: this.apiKey\n      }\n\n      if (from) params.from = from\n      if (to) params.to = to\n\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/economic_calendar`,\n        { params }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error fetching economic calendar:', error)\n      return []\n    }\n  }\n\n  // Search for stocks\n  async searchStocks(query: string, limit: number = 10) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/search`,\n        {\n          params: {\n            query,\n            limit,\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error searching stocks:', error)\n      return []\n    }\n  }\n\n  // Get sector performance\n  async getSectorPerformance() {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/sector-performance`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error fetching sector performance:', error)\n      return []\n    }\n  }\n\n  // Get market gainers/losers\n  async getMarketMovers(type: 'gainers' | 'losers' | 'actives') {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/stock_market/${type}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error(`Error fetching market ${type}:`, error)\n      return []\n    }\n  }\n\n  // ===== CATALYST DETECTION ENDPOINTS =====\n\n  // Get earnings calendar for catalyst detection\n  async getEarningsCalendar(symbol?: string, days: number = 30) {\n    try {\n      const fromDate = new Date()\n      fromDate.setDate(fromDate.getDate() - days)\n      const toDate = new Date()\n\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/earning_calendar`,\n        {\n          params: {\n            apikey: this.apiKey,\n            from: fromDate.toISOString().split('T')[0],\n            to: toDate.toISOString().split('T')[0],\n            ...(symbol && { symbol: symbol.toUpperCase() })\n          }\n        }\n      )\n\n      return response.data || []\n    } catch (error) {\n      console.error('Error fetching earnings calendar:', error)\n      return []\n    }\n  }\n\n  // Get stock news for catalyst detection\n  async getStockNews(symbol: string, limit: number = 50) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/stock_news`,\n        {\n          params: {\n            apikey: this.apiKey,\n            tickers: symbol.toUpperCase(),\n            limit\n          }\n        }\n      )\n\n      return response.data || []\n    } catch (error) {\n      console.error('Error fetching stock news:', error)\n      return []\n    }\n  }\n\n  // Get analyst recommendations\n  async getAnalystRecommendations(symbol: string, days: number = 30) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/analyst-stock-recommendations/${symbol.toUpperCase()}`,\n        {\n          params: {\n            apikey: this.apiKey,\n            limit: days\n          }\n        }\n      )\n\n      return response.data || []\n    } catch (error) {\n      console.error('Error fetching analyst recommendations:', error)\n      return []\n    }\n  }\n\n  // Get insider trading data\n  async getInsiderTrading(symbol: string, days: number = 30) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v4/insider-trading`,\n        {\n          params: {\n            apikey: this.apiKey,\n            symbol: symbol.toUpperCase(),\n            limit: days * 5 // Approximate multiple to get enough data\n          }\n        }\n      )\n\n      // Filter to last N days\n      const cutoffDate = new Date()\n      cutoffDate.setDate(cutoffDate.getDate() - days)\n\n      return (response.data || []).filter((trade: any) =>\n        new Date(trade.filingDate) >= cutoffDate\n      )\n    } catch (error) {\n      console.error('Error fetching insider trading:', error)\n      return []\n    }\n  }\n\n  // Get SEC filings\n  async getSECFilings(symbol: string, days: number = 30) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/sec_filings/${symbol.toUpperCase()}`,\n        {\n          params: {\n            apikey: this.apiKey,\n            limit: days * 2 // Get more filings to filter by date\n          }\n        }\n      )\n\n      // Filter to last N days\n      const cutoffDate = new Date()\n      cutoffDate.setDate(cutoffDate.getDate() - days)\n\n      return (response.data || []).filter((filing: any) =>\n        new Date(filing.filedDate) >= cutoffDate\n      )\n    } catch (error) {\n      console.error('Error fetching SEC filings:', error)\n      return []\n    }\n  }\n\n  // Get pre-market quotes for gap scanning\n  async getPreMarketQuote(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/quote/${symbol.toUpperCase()}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      const data = response.data[0]\n      if (!data) return null\n\n      return {\n        symbol: data.symbol,\n        price: data.price,\n        previousClose: data.previousClose,\n        change: data.change,\n        changePercent: data.changesPercentage,\n        volume: data.volume,\n        marketCap: data.marketCap,\n        avgVolume: data.avgVolume,\n        // Pre-market specific data (if available)\n        preMarketPrice: data.preMarketPrice || data.price,\n        preMarketChange: data.preMarketChange || data.change,\n        preMarketChangePercent: data.preMarketChangePercent || data.changesPercentage\n      }\n    } catch (error) {\n      console.error('Error fetching pre-market quote:', error)\n      return null\n    }\n  }\n\n  // Get multiple pre-market quotes efficiently\n  async getMultiplePreMarketQuotes(symbols: string[]) {\n    try {\n      const symbolsString = symbols.map(s => s.toUpperCase()).join(',')\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/quote/${symbolsString}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return (response.data || []).map((data: any) => ({\n        symbol: data.symbol,\n        price: data.price,\n        previousClose: data.previousClose,\n        change: data.change,\n        changePercent: data.changesPercentage,\n        volume: data.volume,\n        marketCap: data.marketCap,\n        avgVolume: data.avgVolume,\n        preMarketPrice: data.preMarketPrice || data.price,\n        preMarketChange: data.preMarketChange || data.change,\n        preMarketChangePercent: data.preMarketChangePercent || data.changesPercentage\n      }))\n    } catch (error) {\n      console.error('Error fetching multiple pre-market quotes:', error)\n      return []\n    }\n  }\n\n  // Get company profile for additional context\n  async getCompanyProfile(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/profile/${symbol.toUpperCase()}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data[0] || null\n    } catch (error) {\n      console.error('Error fetching company profile:', error)\n      return null\n    }\n  }\n}\n\n// Create a singleton instance\nexport const fmpAPI = new FMPAPI()\n", "import { \n  Catalyst, \n  CatalystType, \n  CatalystTier, \n  CatalystImpact, \n  CatalystImpactMeasurement \n} from '@/types/trading'\nimport { FMPAPI } from './fmp'\nimport { PolygonAPI } from './polygon'\n\nexport class CatalystDetectionEngine {\n  private fmpAPI: FMPAPI\n  private polygonAPI: PolygonAPI\n  private catalystCache: Map<string, Catalyst[]> = new Map()\n  private impactMeasurements: Map<string, CatalystImpactMeasurement> = new Map()\n\n  constructor(fmpApiKey?: string, polygonApiKey?: string) {\n    this.fmpAPI = new FMPAPI(fmpApiKey)\n    this.polygonAPI = new PolygonAPI(polygonApiKey)\n  }\n\n  /**\n   * Detect catalysts for a specific symbol\n   */\n  async detectCatalysts(symbol: string): Promise<Catalyst[]> {\n    const catalysts: Catalyst[] = []\n\n    try {\n      // Check cache first (5-minute cache)\n      const cacheKey = `${symbol}_${Math.floor(Date.now() / (5 * 60 * 1000))}`\n      if (this.catalystCache.has(cacheKey)) {\n        return this.catalystCache.get(cacheKey)!\n      }\n\n      // Detect different types of catalysts in parallel\n      const [\n        earningsCatalysts,\n        newsCatalysts,\n        analystCatalysts,\n        insiderCatalysts,\n        secFilingCatalysts\n      ] = await Promise.all([\n        this.detectEarningsCatalysts(symbol),\n        this.detectNewsCatalysts(symbol),\n        this.detectAnalystCatalysts(symbol),\n        this.detectInsiderCatalysts(symbol),\n        this.detectSECFilingCatalysts(symbol)\n      ])\n\n      catalysts.push(\n        ...earningsCatalysts,\n        ...newsCatalysts,\n        ...analystCatalysts,\n        ...insiderCatalysts,\n        ...secFilingCatalysts\n      )\n\n      // Sort by quality score and freshness\n      catalysts.sort((a, b) => {\n        const freshnessWeight = this.getFreshnessWeight(a.freshness) - this.getFreshnessWeight(b.freshness)\n        if (freshnessWeight !== 0) return freshnessWeight\n        return b.qualityScore - a.qualityScore\n      })\n\n      // Cache results\n      this.catalystCache.set(cacheKey, catalysts)\n\n      return catalysts\n    } catch (error) {\n      console.error(`Error detecting catalysts for ${symbol}:`, error)\n      return []\n    }\n  }\n\n  /**\n   * Detect earnings-related catalysts\n   */\n  private async detectEarningsCatalysts(symbol: string): Promise<Catalyst[]> {\n    const catalysts: Catalyst[] = []\n\n    try {\n      // Get recent earnings data from FMP\n      const earningsData = await this.fmpAPI.getEarningsCalendar(symbol, 30) // Last 30 days\n      \n      for (const earnings of earningsData) {\n        if (this.isEarningsBeat(earnings)) {\n          const catalyst: Catalyst = {\n            id: `earnings_${symbol}_${earnings.date}`,\n            symbol,\n            type: 'earnings_beat_guidance',\n            tier: 'tier_1', // Highest priority\n            impact: 'bullish',\n            title: `${symbol} Beats Earnings Expectations`,\n            description: `Q${earnings.quarter} earnings beat: EPS ${earnings.actualEPS} vs ${earnings.estimatedEPS} expected`,\n            source: 'FMP Earnings Data',\n            announcementTime: earnings.date,\n            discoveredTime: new Date().toISOString(),\n            qualityScore: this.calculateEarningsQualityScore(earnings),\n            freshness: this.calculateFreshness(earnings.date),\n            estimatedDuration: 'short_term',\n            verified: true,\n            tags: ['earnings', 'beat', 'guidance'],\n            metadata: {\n              actualEPS: earnings.actualEPS,\n              estimatedEPS: earnings.estimatedEPS,\n              beatPercent: ((earnings.actualEPS - earnings.estimatedEPS) / earnings.estimatedEPS) * 100,\n              guidanceRaised: earnings.guidanceRaised || false\n            }\n          }\n          catalysts.push(catalyst)\n        }\n      }\n    } catch (error) {\n      console.error(`Error detecting earnings catalysts for ${symbol}:`, error)\n    }\n\n    return catalysts\n  }\n\n  /**\n   * Detect news-related catalysts\n   */\n  private async detectNewsCatalysts(symbol: string): Promise<Catalyst[]> {\n    const catalysts: Catalyst[] = []\n\n    try {\n      // Get recent news from FMP\n      const newsData = await this.fmpAPI.getStockNews(symbol, 50) // Last 50 articles\n      \n      for (const news of newsData) {\n        const catalystType = this.classifyNewsAsCatalyst(news)\n        if (catalystType) {\n          const catalyst: Catalyst = {\n            id: `news_${symbol}_${news.publishedDate}_${news.title.slice(0, 20).replace(/\\s+/g, '_')}`,\n            symbol,\n            type: catalystType.type,\n            tier: catalystType.tier,\n            impact: catalystType.impact,\n            title: news.title,\n            description: news.text?.slice(0, 200) + '...' || news.title,\n            source: news.site,\n            sourceUrl: news.url,\n            announcementTime: news.publishedDate,\n            discoveredTime: new Date().toISOString(),\n            qualityScore: this.calculateNewsQualityScore(news, catalystType.type),\n            freshness: this.calculateFreshness(news.publishedDate),\n            estimatedDuration: this.estimateNewsDuration(catalystType.type),\n            verified: this.isReliableNewsSource(news.site),\n            tags: this.extractNewsKeywords(news.title + ' ' + (news.text || '')),\n            metadata: {\n              site: news.site,\n              sentiment: news.sentiment || 'neutral'\n            }\n          }\n          catalysts.push(catalyst)\n        }\n      }\n    } catch (error) {\n      console.error(`Error detecting news catalysts for ${symbol}:`, error)\n    }\n\n    return catalysts\n  }\n\n  /**\n   * Detect analyst upgrade/downgrade catalysts\n   */\n  private async detectAnalystCatalysts(symbol: string): Promise<Catalyst[]> {\n    const catalysts: Catalyst[] = []\n\n    try {\n      // Get analyst recommendations from FMP\n      const analystData = await this.fmpAPI.getAnalystRecommendations(symbol, 30)\n      \n      for (const recommendation of analystData) {\n        if (this.isSignificantAnalystChange(recommendation)) {\n          const isUpgrade = recommendation.newGrade > recommendation.previousGrade\n          const catalyst: Catalyst = {\n            id: `analyst_${symbol}_${recommendation.date}_${recommendation.analystCompany}`,\n            symbol,\n            type: isUpgrade ? 'analyst_upgrade' : 'analyst_downgrade',\n            tier: 'tier_2',\n            impact: isUpgrade ? 'bullish' : 'bearish',\n            title: `${recommendation.analystCompany} ${isUpgrade ? 'Upgrades' : 'Downgrades'} ${symbol}`,\n            description: `${recommendation.analystName} at ${recommendation.analystCompany} ${isUpgrade ? 'upgraded' : 'downgraded'} to ${recommendation.newGrade}`,\n            source: 'FMP Analyst Data',\n            announcementTime: recommendation.date,\n            discoveredTime: new Date().toISOString(),\n            qualityScore: this.calculateAnalystQualityScore(recommendation),\n            freshness: this.calculateFreshness(recommendation.date),\n            estimatedDuration: 'medium_term',\n            verified: true,\n            tags: ['analyst', isUpgrade ? 'upgrade' : 'downgrade', recommendation.analystCompany.toLowerCase()],\n            metadata: {\n              analystCompany: recommendation.analystCompany,\n              analystName: recommendation.analystName,\n              previousGrade: recommendation.previousGrade,\n              newGrade: recommendation.newGrade,\n              priceTarget: recommendation.priceTarget\n            }\n          }\n          catalysts.push(catalyst)\n        }\n      }\n    } catch (error) {\n      console.error(`Error detecting analyst catalysts for ${symbol}:`, error)\n    }\n\n    return catalysts\n  }\n\n  /**\n   * Detect insider trading catalysts\n   */\n  private async detectInsiderCatalysts(symbol: string): Promise<Catalyst[]> {\n    const catalysts: Catalyst[] = []\n\n    try {\n      // Get insider trading data from FMP\n      const insiderData = await this.fmpAPI.getInsiderTrading(symbol, 30)\n      \n      for (const trade of insiderData) {\n        if (this.isSignificantInsiderTrade(trade)) {\n          const isBuying = trade.transactionType.toLowerCase().includes('buy') || \n                          trade.transactionType.toLowerCase().includes('purchase')\n          \n          const catalyst: Catalyst = {\n            id: `insider_${symbol}_${trade.filingDate}_${trade.reportingName}`,\n            symbol,\n            type: isBuying ? 'insider_buying' : 'insider_selling',\n            tier: 'tier_2',\n            impact: isBuying ? 'bullish' : 'bearish',\n            title: `${trade.reportingName} ${isBuying ? 'Buys' : 'Sells'} ${symbol} Shares`,\n            description: `${trade.reportingName} (${trade.typeOfOwner}) ${trade.transactionType} ${trade.securitiesTransacted} shares at $${trade.price}`,\n            source: 'SEC Insider Trading Filings',\n            announcementTime: trade.filingDate,\n            discoveredTime: new Date().toISOString(),\n            qualityScore: this.calculateInsiderQualityScore(trade),\n            freshness: this.calculateFreshness(trade.filingDate),\n            estimatedDuration: 'medium_term',\n            verified: true,\n            tags: ['insider', isBuying ? 'buying' : 'selling', trade.typeOfOwner.toLowerCase()],\n            metadata: {\n              reportingName: trade.reportingName,\n              typeOfOwner: trade.typeOfOwner,\n              transactionType: trade.transactionType,\n              securitiesTransacted: trade.securitiesTransacted,\n              price: trade.price,\n              dollarValue: trade.securitiesTransacted * trade.price\n            }\n          }\n          catalysts.push(catalyst)\n        }\n      }\n    } catch (error) {\n      console.error(`Error detecting insider catalysts for ${symbol}:`, error)\n    }\n\n    return catalysts\n  }\n\n  /**\n   * Detect SEC filing catalysts\n   */\n  private async detectSECFilingCatalysts(symbol: string): Promise<Catalyst[]> {\n    const catalysts: Catalyst[] = []\n\n    try {\n      // Get recent SEC filings from FMP\n      const filings = await this.fmpAPI.getSECFilings(symbol, 30)\n      \n      for (const filing of filings) {\n        if (this.isSignificantSECFiling(filing)) {\n          const catalyst: Catalyst = {\n            id: `sec_${symbol}_${filing.filedDate}_${filing.type}`,\n            symbol,\n            type: 'sec_filing',\n            tier: this.getSECFilingTier(filing.type),\n            impact: this.getSECFilingImpact(filing.type),\n            title: `${symbol} Files ${filing.type}`,\n            description: `${filing.type} filing: ${filing.description || 'SEC regulatory filing'}`,\n            source: 'SEC EDGAR Database',\n            sourceUrl: filing.link,\n            announcementTime: filing.filedDate,\n            discoveredTime: new Date().toISOString(),\n            qualityScore: this.calculateSECFilingQualityScore(filing),\n            freshness: this.calculateFreshness(filing.filedDate),\n            estimatedDuration: this.estimateSECFilingDuration(filing.type),\n            verified: true,\n            tags: ['sec', 'filing', filing.type.toLowerCase()],\n            metadata: {\n              filingType: filing.type,\n              cik: filing.cik,\n              acceptedDate: filing.acceptedDate\n            }\n          }\n          catalysts.push(catalyst)\n        }\n      }\n    } catch (error) {\n      console.error(`Error detecting SEC filing catalysts for ${symbol}:`, error)\n    }\n\n    return catalysts\n  }\n\n  // Helper methods for catalyst classification and scoring\n  private getFreshnessWeight(freshness: string): number {\n    switch (freshness) {\n      case 'fresh': return 3\n      case 'moderate': return 2\n      case 'stale': return 1\n      default: return 0\n    }\n  }\n\n  private calculateFreshness(dateString: string): 'fresh' | 'moderate' | 'stale' {\n    const date = new Date(dateString)\n    const now = new Date()\n    const hoursAgo = (now.getTime() - date.getTime()) / (1000 * 60 * 60)\n    \n    if (hoursAgo < 24) return 'fresh'\n    if (hoursAgo < 72) return 'moderate'\n    return 'stale'\n  }\n\n  private isEarningsBeat(earnings: any): boolean {\n    return earnings.actualEPS > earnings.estimatedEPS && \n           (earnings.guidanceRaised || earnings.actualEPS > earnings.estimatedEPS * 1.05)\n  }\n\n  private calculateEarningsQualityScore(earnings: any): number {\n    let score = 5 // Base score\n    \n    // Beat percentage\n    const beatPercent = ((earnings.actualEPS - earnings.estimatedEPS) / earnings.estimatedEPS) * 100\n    if (beatPercent > 20) score += 3\n    else if (beatPercent > 10) score += 2\n    else if (beatPercent > 5) score += 1\n    \n    // Guidance raised\n    if (earnings.guidanceRaised) score += 2\n    \n    // Revenue beat\n    if (earnings.actualRevenue > earnings.estimatedRevenue) score += 1\n    \n    return Math.min(10, score)\n  }\n\n  private classifyNewsAsCatalyst(news: any): { type: CatalystType, tier: CatalystTier, impact: CatalystImpact } | null {\n    const title = news.title.toLowerCase()\n    const text = (news.text || '').toLowerCase()\n    const content = title + ' ' + text\n\n    // FDA/Drug related\n    if (content.includes('fda') && (content.includes('approval') || content.includes('approved'))) {\n      return { type: 'fda_approval', tier: 'tier_1', impact: 'bullish' }\n    }\n    \n    if (content.includes('trial') && (content.includes('positive') || content.includes('successful'))) {\n      return { type: 'drug_trial_results', tier: 'tier_1', impact: 'bullish' }\n    }\n\n    // Contract/Partnership\n    if (content.includes('contract') && (content.includes('win') || content.includes('awarded'))) {\n      return { type: 'contract_win', tier: 'tier_1', impact: 'bullish' }\n    }\n    \n    if (content.includes('partnership') || content.includes('collaboration')) {\n      return { type: 'partnership', tier: 'tier_1', impact: 'bullish' }\n    }\n\n    // M&A\n    if (content.includes('merger') || content.includes('acquisition') || content.includes('buyout')) {\n      return { type: 'merger_acquisition', tier: 'tier_1', impact: 'bullish' }\n    }\n\n    // Stock split\n    if (content.includes('stock split') || content.includes('share split')) {\n      return { type: 'stock_split', tier: 'tier_2', impact: 'bullish' }\n    }\n\n    return null\n  }\n\n  private calculateNewsQualityScore(news: any, catalystType: CatalystType): number {\n    let score = 5 // Base score\n    \n    // Source reliability\n    if (this.isReliableNewsSource(news.site)) score += 2\n    \n    // Catalyst type importance\n    if (['fda_approval', 'merger_acquisition', 'earnings_beat_guidance'].includes(catalystType)) {\n      score += 2\n    }\n    \n    // Sentiment\n    if (news.sentiment === 'positive') score += 1\n    else if (news.sentiment === 'negative') score -= 1\n    \n    return Math.max(1, Math.min(10, score))\n  }\n\n  private isReliableNewsSource(site: string): boolean {\n    const reliableSources = [\n      'reuters.com', 'bloomberg.com', 'wsj.com', 'cnbc.com', \n      'marketwatch.com', 'yahoo.com', 'sec.gov', 'fda.gov'\n    ]\n    return reliableSources.some(source => site.toLowerCase().includes(source))\n  }\n\n  private extractNewsKeywords(text: string): string[] {\n    const keywords = []\n    const content = text.toLowerCase()\n    \n    const keywordMap = {\n      'earnings': ['earnings', 'eps', 'revenue', 'profit'],\n      'fda': ['fda', 'approval', 'drug', 'trial'],\n      'merger': ['merger', 'acquisition', 'buyout', 'takeover'],\n      'partnership': ['partnership', 'collaboration', 'alliance'],\n      'contract': ['contract', 'deal', 'agreement'],\n      'upgrade': ['upgrade', 'raised', 'increased'],\n      'downgrade': ['downgrade', 'lowered', 'reduced']\n    }\n    \n    for (const [category, terms] of Object.entries(keywordMap)) {\n      if (terms.some(term => content.includes(term))) {\n        keywords.push(category)\n      }\n    }\n    \n    return keywords\n  }\n\n  private estimateNewsDuration(catalystType: CatalystType): 'intraday' | 'short_term' | 'medium_term' | 'long_term' {\n    switch (catalystType) {\n      case 'earnings_beat_guidance':\n      case 'fda_approval':\n      case 'merger_acquisition':\n        return 'short_term'\n      case 'analyst_upgrade':\n      case 'analyst_downgrade':\n      case 'partnership':\n        return 'medium_term'\n      case 'stock_split':\n        return 'long_term'\n      default:\n        return 'short_term'\n    }\n  }\n\n  private isSignificantAnalystChange(recommendation: any): boolean {\n    // Check if it's a meaningful grade change\n    const gradeChange = Math.abs(recommendation.newGrade - recommendation.previousGrade)\n    return gradeChange >= 1 && recommendation.priceTarget > 0\n  }\n\n  private calculateAnalystQualityScore(recommendation: any): number {\n    let score = 5 // Base score\n    \n    // Analyst firm reputation (simplified)\n    const topFirms = ['goldman sachs', 'morgan stanley', 'jp morgan', 'bank of america']\n    if (topFirms.some(firm => recommendation.analystCompany.toLowerCase().includes(firm))) {\n      score += 2\n    }\n    \n    // Grade change magnitude\n    const gradeChange = Math.abs(recommendation.newGrade - recommendation.previousGrade)\n    if (gradeChange >= 2) score += 2\n    else if (gradeChange >= 1) score += 1\n    \n    // Price target change\n    if (recommendation.priceTargetChange > 10) score += 1\n    \n    return Math.min(10, score)\n  }\n\n  private isSignificantInsiderTrade(trade: any): boolean {\n    const dollarValue = trade.securitiesTransacted * trade.price\n    return dollarValue >= 1000000 && // $1M+ transactions\n           trade.typeOfOwner !== 'Other' // Exclude generic \"Other\" category\n  }\n\n  private calculateInsiderQualityScore(trade: any): number {\n    let score = 5 // Base score\n    \n    const dollarValue = trade.securitiesTransacted * trade.price\n    \n    // Transaction size\n    if (dollarValue >= ********) score += 3 // $10M+\n    else if (dollarValue >= 5000000) score += 2 // $5M+\n    else if (dollarValue >= 1000000) score += 1 // $1M+\n    \n    // Insider type\n    if (trade.typeOfOwner.toLowerCase().includes('ceo') || \n        trade.typeOfOwner.toLowerCase().includes('cfo')) {\n      score += 2\n    } else if (trade.typeOfOwner.toLowerCase().includes('director')) {\n      score += 1\n    }\n    \n    return Math.min(10, score)\n  }\n\n  private isSignificantSECFiling(filing: any): boolean {\n    const significantFilings = ['8-K', '10-K', '10-Q', '13D', '13G', 'S-1', 'S-4']\n    return significantFilings.includes(filing.type)\n  }\n\n  private getSECFilingTier(filingType: string): CatalystTier {\n    const tier1Filings = ['8-K', '13D', 'S-4'] // Material events, activist investors, M&A\n    const tier2Filings = ['10-K', '10-Q', '13G'] // Regular reports, passive investors\n    \n    if (tier1Filings.includes(filingType)) return 'tier_1'\n    if (tier2Filings.includes(filingType)) return 'tier_2'\n    return 'tier_3'\n  }\n\n  private getSECFilingImpact(filingType: string): CatalystImpact {\n    // Most SEC filings are neutral until analyzed\n    return 'neutral'\n  }\n\n  private calculateSECFilingQualityScore(filing: any): number {\n    let score = 5 // Base score\n    \n    // Filing type importance\n    if (['8-K', '13D'].includes(filing.type)) score += 2\n    else if (['10-K', '10-Q'].includes(filing.type)) score += 1\n    \n    return Math.min(10, score)\n  }\n\n  private estimateSECFilingDuration(filingType: string): 'intraday' | 'short_term' | 'medium_term' | 'long_term' {\n    switch (filingType) {\n      case '8-K': return 'short_term' // Material events\n      case '13D': return 'medium_term' // Activist investors\n      case 'S-4': return 'long_term' // M&A registration\n      default: return 'medium_term'\n    }\n  }\n}\n", "import { NextRequest, NextResponse } from 'next/server'\nimport { CatalystDetectionEngine } from '@/lib/catalystDetection'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    \n    const symbol = searchParams.get('symbol')\n    const symbols = searchParams.get('symbols')?.split(',').filter(Boolean)\n    const catalystTypes = searchParams.get('types')?.split(',').filter(Boolean)\n    const minQuality = parseInt(searchParams.get('minQuality') || '5')\n    const freshOnly = searchParams.get('freshOnly') === 'true'\n    const limit = parseInt(searchParams.get('limit') || '20')\n\n    if (!symbol && !symbols) {\n      return NextResponse.json(\n        { success: false, error: 'Symbol or symbols parameter required' },\n        { status: 400 }\n      )\n    }\n\n    console.log('🔍 Catalyst Detection API called with params:', {\n      symbol,\n      symbols: symbols?.length || 0,\n      catalystTypes,\n      minQuality,\n      freshOnly,\n      limit\n    })\n\n    // Initialize Catalyst Detection Engine\n    const catalystEngine = new CatalystDetectionEngine(\n      process.env.FMP_API_KEY,\n      process.env.POLYGON_API_KEY\n    )\n\n    let allCatalysts = []\n\n    if (symbol) {\n      // Single symbol detection\n      const catalysts = await catalystEngine.detectCatalysts(symbol)\n      allCatalysts.push(...catalysts)\n    } else if (symbols) {\n      // Multiple symbols detection\n      const catalystPromises = symbols.map(sym => catalystEngine.detectCatalysts(sym))\n      const catalystResults = await Promise.all(catalystPromises)\n      allCatalysts = catalystResults.flat()\n    }\n\n    // Apply filters\n    let filteredCatalysts = allCatalysts\n\n    if (catalystTypes && catalystTypes.length > 0) {\n      filteredCatalysts = filteredCatalysts.filter(catalyst => \n        catalystTypes.includes(catalyst.type)\n      )\n    }\n\n    if (minQuality > 1) {\n      filteredCatalysts = filteredCatalysts.filter(catalyst => \n        catalyst.qualityScore >= minQuality\n      )\n    }\n\n    if (freshOnly) {\n      filteredCatalysts = filteredCatalysts.filter(catalyst => \n        catalyst.freshness === 'fresh'\n      )\n    }\n\n    // Sort by quality score and freshness\n    filteredCatalysts.sort((a, b) => {\n      const freshnessWeight = (freshness: string) => {\n        switch (freshness) {\n          case 'fresh': return 3\n          case 'moderate': return 2\n          case 'stale': return 1\n          default: return 0\n        }\n      }\n      \n      const aWeight = freshnessWeight(a.freshness) * 10 + a.qualityScore\n      const bWeight = freshnessWeight(b.freshness) * 10 + b.qualityScore\n      \n      return bWeight - aWeight\n    })\n\n    // Limit results\n    const limitedCatalysts = filteredCatalysts.slice(0, limit)\n\n    // Generate summary statistics\n    const summary = {\n      totalFound: allCatalysts.length,\n      afterFilters: filteredCatalysts.length,\n      returned: limitedCatalysts.length,\n      byTier: limitedCatalysts.reduce((acc, catalyst) => {\n        acc[catalyst.tier] = (acc[catalyst.tier] || 0) + 1\n        return acc\n      }, {} as Record<string, number>),\n      byType: limitedCatalysts.reduce((acc, catalyst) => {\n        acc[catalyst.type] = (acc[catalyst.type] || 0) + 1\n        return acc\n      }, {} as Record<string, number>),\n      byFreshness: limitedCatalysts.reduce((acc, catalyst) => {\n        acc[catalyst.freshness] = (acc[catalyst.freshness] || 0) + 1\n        return acc\n      }, {} as Record<string, number>),\n      avgQualityScore: limitedCatalysts.length > 0 \n        ? Math.round((limitedCatalysts.reduce((sum, c) => sum + c.qualityScore, 0) / limitedCatalysts.length) * 100) / 100\n        : 0\n    }\n\n    const response = {\n      success: true,\n      data: {\n        catalysts: limitedCatalysts,\n        summary,\n        filters: {\n          symbol,\n          symbols: symbols?.length || 0,\n          catalystTypes,\n          minQuality,\n          freshOnly,\n          limit\n        },\n        timestamp: new Date().toISOString()\n      }\n    }\n\n    return NextResponse.json(response)\n  } catch (error) {\n    console.error('Error in Catalyst Detection API:', error)\n    return NextResponse.json(\n      { \n        success: false, \n        error: 'Failed to detect catalysts',\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const { action, data } = body\n\n    const catalystEngine = new CatalystDetectionEngine(\n      process.env.FMP_API_KEY,\n      process.env.POLYGON_API_KEY\n    )\n\n    switch (action) {\n      case 'batch_detect':\n        const symbols = data.symbols || []\n        if (!Array.isArray(symbols) || symbols.length === 0) {\n          return NextResponse.json(\n            { success: false, error: 'Symbols array required' },\n            { status: 400 }\n          )\n        }\n\n        const batchResults = []\n        for (const symbol of symbols) {\n          try {\n            const catalysts = await catalystEngine.detectCatalysts(symbol)\n            batchResults.push({\n              symbol,\n              catalysts,\n              success: true\n            })\n          } catch (error) {\n            batchResults.push({\n              symbol,\n              catalysts: [],\n              success: false,\n              error: error instanceof Error ? error.message : 'Unknown error'\n            })\n          }\n        }\n\n        return NextResponse.json({\n          success: true,\n          data: { results: batchResults }\n        })\n\n      default:\n        return NextResponse.json(\n          { success: false, error: 'Invalid action' },\n          { status: 400 }\n        )\n    }\n  } catch (error) {\n    console.error('Error in Catalyst Detection POST API:', error)\n    return NextResponse.json(\n      { \n        success: false, \n        error: 'Failed to process catalyst detection request',\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    )\n  }\n}\n", "import { AppRouteRouteModule } from \"next/dist/esm/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/esm/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/esm/server/lib/patch-fetch\";\nimport { getRequestMeta } from \"next/dist/esm/server/request-meta\";\nimport { getTracer, SpanKind } from \"next/dist/esm/server/lib/trace/tracer\";\nimport { normalizeAppPath } from \"next/dist/esm/shared/lib/router/utils/app-paths\";\nimport { NodeNextRequest, NodeNextResponse } from \"next/dist/esm/server/base-http/node\";\nimport { NextRequestAdapter, signalFromNodeResponse } from \"next/dist/esm/server/web/spec-extension/adapters/next-request\";\nimport { BaseServerSpan } from \"next/dist/esm/server/lib/trace/constants\";\nimport { getRevalidateReason } from \"next/dist/esm/server/instrumentation/utils\";\nimport { sendResponse } from \"next/dist/esm/server/send-response\";\nimport { fromNodeOutgoingHttpHeaders, toNodeOutgoingHttpHeaders } from \"next/dist/esm/server/web/utils\";\nimport { getCacheControlHeader } from \"next/dist/esm/server/lib/cache-control\";\nimport { INFINITE_CACHE, NEXT_CACHE_TAGS_HEADER } from \"next/dist/esm/lib/constants\";\nimport { NoFallbackError } from \"next/dist/esm/shared/lib/no-fallback-error.external\";\nimport { CachedRouteKind } from \"next/dist/esm/server/response-cache\";\nimport * as userland from \"INNER_APP_ROUTE\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/catalyst/detect/route\",\n        pathname: \"/api/catalyst/detect\",\n        filename: \"route\",\n        bundlePath: \"\"\n    },\n    distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n    relativeProjectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || '',\n    resolvedPagePath: \"[project]/swing-trader-ai/src/app/api/catalyst/detect/route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\nexport async function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/catalyst/detect/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (process.env.TURBOPACK) {\n        srcPage = srcPage.replace(/\\/index$/, '') || '/';\n    } else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = process.env.__NEXT_MULTI_ZONE_DRAFT_MODE;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = normalizeAppPath(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = getTracer();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: getRequestMeta(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new NodeNextRequest(req);\n    const nodeNextRes = new NodeNextResponse(res);\n    const nextReq = NextRequestAdapter.fromNodeNextRequest(nodeNextReq, signalFromNodeResponse(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!getRequestMeta(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = toNodeOutgoingHttpHeaders(response.headers);\n                        if (cacheTags) {\n                            headers[NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await sendResponse(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: getRevalidateReason({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!getRequestMeta(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = fromNodeOutgoingHttpHeaders(cacheEntry.value.headers);\n            if (!(getRequestMeta(req, 'minimalMode') && isIsr)) {\n                headers.delete(NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', getCacheControlHeader(cacheEntry.cacheControl));\n            }\n            await sendResponse(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        if (!(err instanceof NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: getRevalidateReason({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await sendResponse(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n"], "names": [], "mappings": "8yCAAA,IAAA,EAAA,EAAA,CAAA,CAAA,OAGA,IAAM,EAAmB,yBACnB,EAAU,QAAQ,GAAG,CAAC,eAAe,AAEpC,OAAM,EACH,MAAc,AAEtB,aAAY,CAAe,CAAE,CAE3B,GADA,IAAI,CAAC,MAAM,CAAG,GAAU,GAAW,GAC/B,CAAC,IAAI,CAAC,MAAM,CACd,CADgB,KACV,AAAI,MAAM,8BAEpB,CAGA,MAAM,cAAc,CAAc,CAAsB,CACtD,GAAI,CAWF,IAAM,EAAO,CATI,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAiB,8CAA8C,EAAE,EAAA,CAAQ,CAC5E,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MACf,AADqB,CAEvB,EAAA,EAGoB,IAAI,CAAC,OAAO,CAAC,EAAE,CACrC,GAAI,CAAC,EACH,IADS,EACH,AAAI,MAAM,CAAC,kBAAkB,EAAE,EAAA,CAAQ,EAG/C,IAAM,EAAS,EAAK,KAAK,EAAI,EACvB,EAAU,EAAO,GAAG,EAAI,CAAC,EACzB,EAAc,EAAO,OAAO,EAAI,CAAC,EACrB,EAAO,SAAS,CAIlC,GAJsC,CAAC,AAIjC,EAAe,CAHH,EAAO,SAAS,EAAI,EAAC,EAGR,CAAC,EAAI,EAAQ,CAAC,EAAI,EAAY,CAAC,CACxD,EAAY,EAAY,CAAC,EAAI,EAAQ,CAAC,CACtC,EAAS,EAAe,EAG9B,MAAO,CACL,OAAQ,EAAO,WAAW,GAC1B,KAAM,EAAO,IAAI,EAAI,EAAO,WAAW,GACvC,MAAO,SACP,EACA,cAPqB,EAAS,EAAa,IAQ3C,OAAQ,EAAQ,CAAC,EAAI,EACrB,UAAW,EAAO,UAAU,CAC5B,GAAI,OACJ,cAAU,CACZ,CACF,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,2CAA4C,GAG1D,GAAI,CAWF,IAAM,EAAO,CAVY,MAAM,EAAA,OAAK,CAAC,GAAG,CACtC,CAAA,EAAG,EAAiB,gBAAgB,EAAE,EAAO,KAAK,CAAC,CACnD,CACE,OAAQ,CACN,SAAU,OACV,OAAQ,IAAI,CAAC,MACf,AADqB,CAEvB,EAAA,EAG4B,IAAI,CAAC,OAAO,CAAC,EAAE,CAC7C,MAAO,CACL,OAAQ,EAAO,WAAW,GAC1B,KAAM,EAAO,WAAW,GACxB,MAAO,EAAK,CAAC,CACb,OAAQ,EAAK,CAAC,CAAG,EAAK,CAAC,CACvB,cAAgB,CAAC,EAAK,CAAC,CAAG,GAAK,AAAC,EAAI,EAAK,CAAC,CAAI,IAC9C,OAAQ,EAAK,CAAC,CACd,eAAW,EACX,QAAI,EACJ,cAAU,CACZ,CACF,CAAE,MAAO,EAAe,CAEtB,MADA,QAAQ,KAAK,CAAC,gCAAiC,GACrC,AAAJ,MAAU,CAAC,0BAA0B,EAAE,EAAA,CAAQ,CACvD,CACF,CACF,CAGA,MAAM,kBACJ,CAAc,CACd,EAAyD,KAAK,CAC9D,EAAqB,CAAC,CACtB,CAAY,CACZ,CAAU,CACkB,CAC5B,GAAI,CACF,IAAM,EAAW,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAiB,gBAAgB,EAAE,EAAO,OAAO,EAAE,EAAW,CAAC,EAAE,EAAS,CAAC,EAAE,EAAK,CAAC,EAAE,EAAA,CAAI,CAC5F,CACE,OAAQ,CACN,SAAU,OACV,KAAM,MACN,MAAO,IACP,OAAQ,IAAI,CAAC,MACf,AADqB,CAEvB,GAGF,GAAI,CAAC,EAAS,IAAI,CAAC,OAAO,EAAI,AAAiC,GAAG,GAA3B,IAAI,CAAC,OAAO,CAAC,MAAM,CAExD,OADA,QAAQ,IAAI,CAAC,CAAC,6BAA6B,EAAE,EAAA,CAAQ,EAC9C,EAAE,CAGX,OAAO,EAAS,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,AAAC,IAAiB,CACjD,IADgD,MACrC,EAAO,CAAC,CACnB,KAAM,EAAO,CAAC,CACd,KAAM,EAAO,CAAC,CACd,IAAK,EAAO,CAAC,CACb,MAAO,EAAO,CAAC,CACf,OAAQ,EAAO,CAAC,CAClB,CAAC,CACH,CAAE,MAAO,EAAO,CASd,MARA,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,EAAO,CAAC,CAAC,CAAE,GAG3D,EAAM,QAAQ,EAAE,CAClB,QAAQ,KAAK,CAAC,CAAC,mBAAmB,EAAE,EAAM,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,EAAM,QAAQ,CAAC,UAAU,CAAA,CAAE,EAC1F,QAAQ,KAAK,CAAC,iBAAkB,EAAM,QAAQ,CAAC,IAAI,GAG/C,AAAI,MAAM,CAAC,oCAAoC,EAAE,EAAO,EAAE,EAAE,EAAM,OAAO,CAAA,CAAE,CACnF,CACF,CAGA,MAAM,kBAAkB,CAAc,CAAE,CACtC,GAAI,CAUF,MAAO,CATU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAiB,sBAAsB,EAAE,EAAA,CAAQ,CACpD,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,CAAC,OAAO,AAC9B,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,kCAAmC,GAC1C,IACT,CACF,CAGA,MAAM,iBAAkB,CACtB,GAAI,CAUF,MAAO,CATU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAiB,oBAAoB,CAAC,CACzC,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,AACtB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,gCAAiC,GACxC,IACT,CACF,CAGA,MAAM,aAAa,CAAa,CAAE,EAAgB,EAAE,CAAE,CACpD,GAAI,CAcF,MAbiB,AAaV,OAbgB,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAiB,qBAAqB,CAAC,CAC1C,CACE,OAAQ,CACN,OAAQ,EACR,OAAQ,SACR,OAAQ,aACR,EACA,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,CAAC,OAAO,EAAI,EAAE,AACpC,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,0BAA2B,GAClC,EAAE,AACX,CACF,CACF,CAG0B,IAAI,gDCzM9B,IAAA,EAAA,EAAA,CAAA,CAAA,OAGA,IAAM,EAAe,wCACf,EAAU,QAAQ,GAAG,CAAC,WAAW,AAEhC,OAAM,EACH,MAER,AAFsB,aAEV,CAAe,CAAE,CAE3B,GADA,IAAI,CAAC,MAAM,CAAG,GAAU,GAAW,GAC/B,CAAC,IAAI,CAAC,MAAM,CACd,CADgB,KACV,AAAI,MAAM,0BAEpB,CAGA,MAAM,cAAc,CAAc,CAAsB,CACtD,GAAI,CAUF,IAAM,EAAO,CATI,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,UAAU,EAAE,EAAA,CAAQ,CACpC,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGoB,IAAI,CAAC,EAAE,CAC7B,GAAI,CAAC,EACH,IADS,EACC,AAAJ,MAAU,CAAC,yBAAyB,EAAE,EAAA,CAAQ,EAGtD,MAAO,CACL,OAAQ,EAAK,MAAM,CACnB,KAAM,EAAK,IAAI,EAAI,EAAK,MAAM,CAC9B,MAAO,EAAK,KAAK,CACjB,OAAQ,EAAK,MAAM,CACnB,cAAe,EAAK,iBAAiB,CACrC,OAAQ,EAAK,MAAM,CACnB,UAAW,EAAK,SAAS,CACzB,GAAI,EAAK,EAAE,CACX,cAAU,CACZ,CACF,CAAE,MAAO,CAFe,CAER,CAEd,MADA,QAAQ,KAAK,CAAC,iBAH+C,iBAGZ,GAC3C,AAAI,MAAM,CAAC,0BAA0B,EAAE,EAAA,CAAQ,CACvD,CACF,CAGA,MAAM,kBAAkB,CAAc,CAAE,CACtC,GAAI,CAUF,MAAO,CATU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,YAAY,EAAE,EAAA,CAAQ,CACtC,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,CAAC,EAAE,AACzB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,kCAAmC,GAC1C,IACT,CACF,CAGA,MAAM,mBAAmB,CAAc,CAAE,CACvC,GAAI,CAUF,MAAO,CATU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,WAAW,EAAE,EAAA,CAAQ,CACrC,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MACf,AADqB,CAEvB,EAAA,EAGc,IAAI,CAAC,EAAE,AACzB,CAD0B,AACxB,MAAO,EAAO,CAEd,OADA,KAF6C,GAErC,KAAK,CAAC,mCAAoC,GAC3C,IACT,CACF,CAGA,MAAM,cAAc,CAAc,CAAE,CAClC,GAAI,CAUF,MAAO,CATU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,gBAAgB,EAAE,EAAA,CAAQ,CAC1C,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,CAAC,EAAE,AACzB,CAD0B,AACxB,MAAO,EAAO,CAEd,OADA,MAF8C,EAEtC,KAAK,CAAC,8BAA+B,GACtC,IACT,CACF,CAGA,MAAM,0BAA0B,CAAc,CAAE,CAC9C,GAAI,CAUF,MAAO,CATU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,kCAAkC,EAAE,EAAA,CAAQ,CAC5D,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,AACtB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,0CAA2C,GAClD,EAAE,AACX,CACF,CAGA,MAAM,oBAAoB,CAAa,CAAE,CAAW,CAAE,CACpD,GAAI,CACF,IAAM,EAAc,CAClB,OAAQ,IAAI,CAAC,MAAM,AACrB,EAUA,OARI,IAAM,EAAO,IAAI,CAAG,CAAA,EACpB,IAAI,EAAO,EAAE,CAAG,CAAA,EAOb,CALU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,oBAAoB,CAAC,CACrC,QAAE,CAAO,EAAA,EAGK,IAAI,AACtB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,oCAAqC,GAC5C,EACT,AADW,CAEb,CAGA,MAAM,oBAAoB,CAAa,CAAE,CAAW,CAAE,CACpD,GAAI,CACF,IAAM,EAAc,CAClB,OAAQ,IAAI,CAAC,MAAM,AACrB,EAUA,OARI,IAAM,EAAO,IAAI,CAAG,CAAA,EACpB,GAAI,GAAO,EAAE,CAAG,CAAA,EAOb,CALU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,qBAAqB,CAAC,CACtC,QAAE,CAAO,EAAA,EAGK,IAClB,AADsB,CACpB,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,oCAAqC,GAC5C,EAAE,AACX,CACF,CAGA,MAAM,aAAa,CAAa,CAAE,EAAgB,EAAE,CAAE,CACpD,GAAI,CAYF,MAAO,CAXU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,UAAU,CAAC,CAC3B,CACE,OAAQ,OACN,QACA,EACA,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,AACtB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,0BAA2B,GAClC,EAAE,AACX,CACF,CAGA,MAAM,sBAAuB,CAC3B,GAAI,CAUF,MAAO,CATU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,sBAAsB,CAAC,CACvC,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,AACtB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,qCAAsC,GAC7C,EAAE,AACX,CACF,CAGA,MAAM,gBAAgB,CAAsC,CAAE,CAC5D,GAAI,CAUF,MAAO,CATU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,iBAAiB,EAAE,EAAA,CAAM,CACzC,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,AACtB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,EAAK,CAAC,CAAC,CAAE,GACzC,EAAE,AACX,CACF,CAKA,MAAM,oBAAoB,CAAe,CAAE,EAAe,EAAE,CAAE,CAC5D,GAAI,CACF,IAAM,EAAW,IAAI,KACrB,EAAS,OAAO,CAAC,EAAS,OAAO,GAAK,GACtC,IAAM,EAAS,IAAI,KAcnB,MAAO,CAZU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,oBAAoB,CAAC,CACrC,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,CACnB,KAAM,EAAS,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAC1C,GAAI,EAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CACtC,GAAI,GAAU,CAAE,OAAQ,EAAO,WAAW,EAAG,CAAC,AAChD,CACF,EAAA,EAGc,IAAI,EAAI,EAC1B,AAD4B,CAC1B,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,oCAAqC,GAC5C,EAAE,AACX,CACF,CAGA,MAAM,aAAa,CAAc,CAAE,EAAgB,EAAE,CAAE,CACrD,GAAI,CAYF,MAAO,CAXU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,cAAc,CAAC,CAC/B,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,CACnB,QAAS,EAAO,WAAW,SAC3B,CACF,CACF,EAAA,EAGc,IAAI,EAAI,EAAE,AAC5B,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,6BAA8B,GACrC,EAAE,AACX,CACF,CAGA,MAAM,0BAA0B,CAAc,CAAE,EAAe,EAAE,CAAE,CACjE,GAAI,CAWF,MAAO,CAVU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,kCAAkC,EAAE,EAAO,WAAW,GAAA,CAAI,CAC1E,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,CACnB,MAAO,CACT,CACF,EAAA,EAGc,IAAI,EAAI,EAAE,AAC5B,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,0CAA2C,GAClD,EACT,AADW,CAEb,CAGA,MAAM,kBAAkB,CAAc,CAAE,EAAe,EAAE,CAAE,CACzD,GAAI,CACF,IAAM,EAAW,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,mBAAmB,CAAC,CACpC,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,CACnB,OAAQ,EAAO,WAAW,GAC1B,MAAc,EAAP,AAAS,CAClB,CACF,GAII,EAAa,IAAI,KAGvB,OAFA,EAAW,OAAO,CAAC,EAAW,OAAO,AAP2B,GAOtB,GAEnC,CAAC,EAAS,IAAI,EAAI,EAAA,AAAE,EAAE,MAAM,CAAC,AAAC,GACnC,IAAI,KAAK,EAAM,UAAU,GAAK,EAElC,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,kCAAmC,GAC1C,EAAE,AACX,CACF,CAGA,MAAM,cAAc,CAAc,CAAE,EAAe,EAAE,CAAE,CACrD,GAAI,CACF,IAAM,EAAW,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,gBAAgB,EAAE,EAAO,WAAW,GAAA,CAAI,CACxD,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,CACnB,MAAc,EAAP,AAAS,CAClB,CACF,GAII,EAAa,IAAI,KAGvB,OAFA,EAAW,OAAO,CAAC,EAAW,EAP6B,KAOtB,GAAK,GAEnC,CAAC,EAAS,IAAI,EAAI,EAAA,AAAE,EAAE,MAAM,CAAC,AAAC,GACnC,IAAI,KAAK,EAAO,SAAS,GAAK,EAElC,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,8BAA+B,GACtC,EAAE,AACX,CACF,CAGA,MAAM,kBAAkB,CAAc,CAAE,CACtC,GAAI,CAUF,IAAM,EAAO,CATI,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,UAAU,EAAE,EAAO,WAAW,GAAA,CAAI,CAClD,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGoB,IAAI,CAAC,EAAE,CAC7B,GAAI,CAAC,EAAM,OAAO,KAElB,MAAO,CACL,OAAQ,EAAK,MAAM,CACnB,MAAO,EAAK,KAAK,CACjB,cAAe,EAAK,aAAa,CACjC,OAAQ,EAAK,MAAM,CACnB,cAAe,EAAK,iBAAiB,CACrC,OAAQ,EAAK,MAAM,CACnB,UAAW,EAAK,SAAS,CACzB,UAAW,EAAK,SAAS,CAEzB,eAAgB,EAAK,cAAc,EAAI,EAAK,KAAK,CACjD,gBAAiB,EAAK,eAAe,EAAI,EAAK,MAAM,CACpD,uBAAwB,EAAK,sBAAsB,EAAI,EAAK,iBAAiB,AAC/E,CACF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,mCAAoC,GAC3C,IACT,CACF,CAGA,MAAM,2BAA2B,CAAiB,CAAE,CAClD,GAAI,CACF,IAAM,EAAgB,EAAQ,GAAG,CAAC,GAAK,EAAE,WAAW,IAAI,IAAI,CAAC,KAU7D,MAAO,AAAC,EATS,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,UAAU,EAAE,EAAA,CAAe,CAC3C,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGe,IAAI,EAAI,EAAA,AAAE,EAAE,GAAG,CAAE,AAAD,IAAgB,CAC/C,EAD8C,KACtC,EAAK,MAAM,CACnB,MAAO,EAAK,KAAK,CACjB,cAAe,EAAK,aAAa,CACjC,OAAQ,EAAK,MAAM,CACnB,cAAe,EAAK,iBAAiB,CACrC,OAAQ,EAAK,MAAM,CACnB,UAAW,EAAK,SAAS,CACzB,UAAW,EAAK,SAAS,CACzB,eAAgB,EAAK,cAAc,EAAI,EAAK,KAAK,CACjD,gBAAiB,EAAK,eAAe,EAAI,EAAK,MAAM,CACpD,uBAAwB,EAAK,sBAAsB,EAAI,EAAK,iBAAiB,CAC/E,CAAC,CACH,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,6CAA8C,GACrD,EAAE,AACX,CACF,CAGA,MAAM,kBAAkB,CAAc,CAAE,CACtC,GAAI,CAUF,MAAO,CATU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,YAAY,EAAE,EAAO,WAAW,GAAA,CAAI,CACpD,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,CAAC,EAAE,EAAI,IAC7B,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,kCAAmC,GAC1C,IACT,CACF,CACF,CAGsB,IAAI,iEChb1B,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MAEO,OAAM,EACH,MAAc,CACd,UAAsB,CACtB,cAAyC,IAAI,GAAK,CAClD,mBAA6D,IAAI,GAEzE,AAF8E,aAElE,CAAkB,CAAE,CAAsB,CAAE,CACtD,IAAI,CAAC,MAAM,CAAG,IAAI,EAAA,MAAM,CAAC,GACzB,IAAI,CAAC,UAAU,CAAG,IAAI,EAAA,UAAU,CAAC,EACnC,CAKA,MAAM,gBAAgB,CAAc,CAAuB,CACzD,IAAM,EAAwB,EAAE,CAEhC,GAAI,CAEF,IAAM,EAAW,CAAA,EAAG,EAAO,CAAC,EAAE,KAAK,KAAK,CAAC,KAAK,GAAG,GAAM,EAAD,EAAK,CAAS,CAAI,CACxE,EADgE,CAC5D,GADgE,CAC5D,CAAC,aAAa,CAAC,GAAG,CAAC,GACzB,OAAO,CAD6B,GACzB,CAAC,aAAa,CAAC,GAAG,CAAC,GAIhC,GAAM,CACJ,EACA,EACA,EACA,EACA,EACD,CAAG,MAAM,QAAQ,GAAG,CAAC,CACpB,IAAI,CAAC,uBAAuB,CAAC,GAC7B,IAAI,CAAC,mBAAmB,CAAC,GACzB,IAAI,CAAC,sBAAsB,CAAC,GAC5B,IAAI,CAAC,sBAAsB,CAAC,GAC5B,IAAI,CAAC,wBAAwB,CAAC,GAC/B,EAoBD,OAlBA,EAAU,IAAI,IACT,KACA,KACA,KACA,KACA,GAIL,EAAU,IAAI,CAAC,CAAC,EAAG,KACjB,IAAM,EAAkB,IAAI,CAAC,kBAAkB,CAAC,EAAE,SAAS,EAAI,IAAI,CAAC,kBAAkB,CAAC,EAAE,SAAS,SAClG,AAAI,AAAoB,GAAG,GAAO,EAC3B,EAAE,YAAY,CAAG,EAAE,YAAY,AACxC,GAGA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAAU,GAE1B,CACT,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,CAAC,8BAA8B,EAAE,EAAO,CAAC,CAAC,CAAE,GACnD,EAAE,AACX,CACF,CAKA,MAAc,wBAAwB,CAAc,CAAuB,CACzE,IAAM,EAAwB,EAAE,CAEhC,GAAI,CAIF,IAAK,IAAM,IAFU,OAAM,CAEJ,GAFQ,CAAC,MAAM,CAAC,EAEF,iBAFqB,CAAC,EAAQ,GAAA,EAAI,AAGrE,GAAI,IAAI,CAAC,OAH2E,OAG7D,CAAC,GAAW,CACjC,IAAM,EAAqB,CACzB,GAAI,CAAC,SAAS,EAAE,EAAO,CAAC,EAAE,EAAS,IAAI,CAAA,CAAE,QACzC,EACA,KAAM,yBACN,KAAM,SACN,OAAQ,UACR,MAAO,CAAA,EAAG,EAAO,4BAA4B,CAAC,CAC9C,YAAa,CAAC,CAAC,EAAE,EAAS,OAAO,CAAC,oBAAoB,EAAE,EAAS,SAAS,CAAC,IAAI,EAAE,EAAS,YAAY,CAAC,SAAS,CAAC,CACjH,OAAQ,oBACR,iBAAkB,EAAS,IAAI,CAC/B,eAAgB,IAAI,OAAO,WAAW,GACtC,aAAc,IAAI,CAAC,6BAA6B,CAAC,GACjD,UAAW,IAAI,CAAC,kBAAkB,CAAC,EAAS,IAAI,EAChD,kBAAmB,aACnB,UAAU,EACV,KAAM,CAAC,WAAY,OAAQ,WAAW,CACtC,SAAU,CACR,UAAW,EAAS,SAAS,CAC7B,aAAc,EAAS,YAAY,CACnC,YAAc,CAAC,EAAS,SAAS,CAAG,EAAS,YAAA,AAAY,EAAI,EAAS,YAAY,CAAI,IACtF,eAAgB,EAAS,cAAc,GAAI,CAC7C,CACF,EACA,EAAU,IAAI,CAAC,EACjB,CAEJ,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,CAAC,uCAAuC,EAAE,EAAO,CAAC,CAAC,CAAE,EACrE,CAEA,OAAO,CACT,CAKA,MAAc,oBAAoB,CAAc,CAAuB,CACrE,IAAM,EAAwB,EAAE,CAEhC,GAAI,CAIF,IAAK,IAAM,KAFM,GAEE,GAFI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,EAAQ,GAAA,EAAI,AAE/B,CAC3B,IAAM,EAAe,IAAI,CAAC,OAHmD,eAG7B,CAAC,GACjD,GAAI,EAAc,CAChB,IAAM,EAAqB,CACzB,GAAI,CAAC,KAAK,EAAE,EAAO,CAAC,EAAE,EAAK,aAAa,CAAC,CAAC,EAAE,EAAK,KAAK,CAAC,KAAK,CAAC,EAAG,IAAI,OAAO,CAAC,OAAQ,KAAA,CAAM,QAC1F,EACA,KAAM,EAAa,IAAI,CACvB,KAAM,EAAa,IAAI,CACvB,OAAQ,EAAa,MAAM,CAC3B,MAAO,EAAK,KAAK,CACjB,YAAa,EAAK,IAAI,EAAE,MAAM,EAAG,KAAO,MACxC,GADiD,IACzC,CAD8C,CACzC,IAD8C,AAC1C,CACjB,UAAW,EAAK,GAAG,CACnB,iBAAkB,EAAK,aAAa,CACpC,eAAgB,IAAI,OAAO,WAAW,GACtC,aAAc,IAAI,CAAC,yBAAyB,CAAC,EAAM,EAAa,IAAI,EACpE,UAAW,IAAI,CAAC,kBAAkB,CAAC,EAAK,aAAa,EACrD,kBAAmB,IAAI,CAAC,oBAAoB,CAAC,EAAa,IAAI,EAC9D,SAAU,IAAI,CAAC,oBAAoB,CAAC,EAAK,IAAI,EAC7C,KAAM,IAAI,CAAC,mBAAmB,CAAC,EAAK,KAAK,CAAG,KAAO,CAAD,CAAM,IAAI,EAAI,EAAA,CAAE,EAClE,SAAU,CACR,KAAM,EAAK,IAAI,CACf,UAAW,EAAK,SAAS,EAAI,SAC/B,CACF,EACA,EAAU,IAAI,CAAC,EACjB,CACF,CACF,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,EAAO,CAAC,CAAC,CAAE,EACjE,CAEA,OAAO,CACT,CAKA,MAAc,uBAAuB,CAAc,CAAuB,CACxE,IAAM,EAAwB,EAAE,CAEhC,GAAI,CAIF,IAAK,IAAM,KAFS,MAAM,IAAI,CAAC,EAEF,IAFQ,CAAC,OAEI,kBAFqB,CAAC,EAAQ,GAAA,EAGtE,GAAI,IAAI,CAAC,0BAA0B,CAAC,GAAiB,CACnD,IAAM,EAAY,EAAe,QAAQ,CAAG,EAAe,aAAa,CAClE,EAAqB,CACzB,GAAI,CAAC,QAAQ,EAAE,EAAO,CAAC,EAAE,EAAe,IAAI,CAAC,CAAC,EAAE,EAAe,cAAc,CAAA,CAAE,QAC/E,EACA,KAAM,EAAY,kBAAoB,oBACtC,KAAM,SACN,OAAQ,EAAY,UAAY,UAChC,MAAO,CAAA,EAAG,EAAe,cAAc,CAAC,CAAC,EAAE,EAAY,WAAa,aAAa,CAAC,EAAE,EAAA,CAAQ,CAC5F,YAAa,CAAA,EAAG,EAAe,WAAW,CAAC,IAAI,EAAE,EAAe,cAAc,CAAC,CAAC,EAAE,EAAY,WAAa,aAAa,IAAI,EAAE,EAAe,QAAQ,CAAA,CAAE,CACvJ,OAAQ,mBACR,iBAAkB,EAAe,IAAI,CACrC,eAAgB,IAAI,OAAO,WAAW,GACtC,aAAc,IAAI,CAAC,4BAA4B,CAAC,GAChD,UAAW,IAAI,CAAC,kBAAkB,CAAC,EAAe,IAAI,EACtD,kBAAmB,cACnB,UAAU,EACV,KAAM,CAAC,UAAW,EAAY,UAAY,YAAa,EAAe,cAAc,CAAC,WAAW,GAAG,CACnG,SAAU,CACR,eAAgB,EAAe,cAAc,CAC7C,YAAa,EAAe,WAAW,CACvC,cAAe,EAAe,aAAa,CAC3C,SAAU,EAAe,QAAQ,CACjC,YAAa,EAAe,WAAW,AACzC,CACF,EACA,EAAU,IAAI,CAAC,EACjB,CAEJ,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,CAAC,sCAAsC,EAAE,EAAO,CAAC,CAAC,CAAE,EACpE,CAEA,OAAO,CACT,CAKA,MAAc,uBAAuB,CAAc,CAAuB,CACxE,IAAM,EAAwB,EAAE,CAEhC,GAAI,CAIF,IAAK,IAAM,IAFS,KAEA,EAFM,IAAI,CAAC,KAEE,CAFI,CAAC,iBAAiB,CAAC,EAAQ,GAAA,EAG9D,GAAI,IAAI,CAAC,yBAAyB,CAAC,GAAQ,CACzC,IAAM,EAAW,EAAM,eAAe,CAAC,WAAW,GAAG,QAAQ,CAAC,QAC9C,EAAM,eAAe,CAAC,WAAW,GAAG,QAAQ,CAAC,YAEvD,EAAqB,CACzB,GAAI,CAAC,QAAQ,EAAE,EAAO,CAAC,EAAE,EAAM,UAAU,CAAC,CAAC,EAAE,EAAM,aAAa,CAAA,CAAE,QAClE,EACA,KAAM,EAAW,iBAAmB,kBACpC,KAAM,SACN,OAAQ,EAAW,UAAY,UAC/B,MAAO,CAAA,EAAG,EAAM,aAAa,CAAC,CAAC,EAAE,EAAW,OAAS,QAAQ,CAAC,EAAE,EAAO,OAAO,CAAC,CAC/E,YAAa,CAAA,EAAG,EAAM,aAAa,CAAC,EAAE,EAAE,EAAM,WAAW,CAAC,EAAE,EAAE,EAAM,eAAe,CAAC,CAAC,EAAE,EAAM,oBAAoB,CAAC,YAAY,EAAE,EAAM,KAAK,CAAA,CAAE,CAC7I,OAAQ,8BACR,iBAAkB,EAAM,UAAU,CAClC,eAAgB,IAAI,OAAO,WAAW,GACtC,aAAc,IAAI,CAAC,4BAA4B,CAAC,GAChD,UAAW,IAAI,CAAC,kBAAkB,CAAC,EAAM,UAAU,EACnD,kBAAmB,cACnB,UAAU,EACV,KAAM,CAAC,UAAW,EAAW,SAAW,UAAW,EAAM,WAAW,CAAC,WAAW,GAAG,CACnF,SAAU,CACR,cAAe,EAAM,aAAa,CAClC,YAAa,EAAM,WAAW,CAC9B,gBAAiB,EAAM,eAAe,CACtC,qBAAsB,EAAM,oBAAoB,CAChD,MAAO,EAAM,KAAK,CAClB,YAAa,EAAM,oBAAoB,CAAG,EAAM,KAAK,AACvD,CACF,EACA,EAAU,IAAI,CAAC,EACjB,CAEJ,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,CAAC,sCAAsC,EAAE,EAAO,CAAC,CAAC,CAAE,EACpE,CAEA,OAAO,CACT,CAKA,MAAc,yBAAyB,CAAc,CAAuB,CAC1E,IAAM,EAAwB,EAAE,CAEhC,GAAI,CAIF,IAAK,IAAM,KAFK,KAEK,CAFC,IAAI,CAAC,EAEG,IAFG,CAAC,aAAa,CAAC,EAAQ,GAAA,EAGtD,GAAI,IAAI,CAAC,sBAAsB,CAAC,GAAS,CACvC,IAAM,EAAqB,CACzB,GAAI,CAAC,IAAI,EAAE,EAAO,CAAC,EAAE,EAAO,SAAS,CAAC,CAAC,EAAE,EAAO,IAAI,CAAA,CAAE,QACtD,EACA,KAAM,aACN,KAAM,IAAI,CAAC,gBAAgB,CAAC,EAAO,IAAI,EACvC,OAAQ,IAAI,CAAC,kBAAkB,CAAC,EAAO,IAAI,EAC3C,MAAO,CAAA,EAAG,EAAO,OAAO,EAAE,EAAO,IAAI,CAAA,CAAE,CACvC,YAAa,CAAA,EAAG,EAAO,IAAI,CAAC,SAAS,EAAE,EAAO,WAAW,EAAI,wBAAA,CAAyB,CACtF,OAAQ,qBACR,UAAW,EAAO,IAAI,CACtB,iBAAkB,EAAO,SAAS,CAClC,eAAgB,IAAI,OAAO,WAAW,GACtC,aAAc,IAAI,CAAC,8BAA8B,CAAC,GAClD,UAAW,IAAI,CAAC,kBAAkB,CAAC,EAAO,SAAS,EACnD,kBAAmB,IAAI,CAAC,yBAAyB,CAAC,EAAO,IAAI,EAC7D,UAAU,EACV,KAAM,CAAC,MAAO,SAAU,EAAO,IAAI,CAAC,WAAW,GAAG,CAClD,SAAU,CACR,WAAY,EAAO,IAAI,CACvB,IAAK,EAAO,GAAG,CACf,aAAc,EAAO,YAAY,AACnC,CACF,EACA,EAAU,IAAI,CAAC,EACjB,CAEJ,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,CAAC,yCAAyC,EAAE,EAAO,CAAC,CAAC,CAAE,EACvE,CAEA,OAAO,CACT,CAGQ,mBAAmB,CAAiB,CAAU,CACpD,OAAQ,GACN,IAAK,QAAS,OAAO,CACrB,KAAK,WAAY,OAAO,CACxB,KAAK,QAAS,OAAO,CACrB,SAAS,OAAO,CAClB,CACF,CAEQ,mBAAmB,CAAkB,CAAkC,CAC7E,IAAM,EAAO,IAAI,KAAK,GAEhB,EAAW,CADL,AACM,IADF,OACM,OAAO,GAAK,EAAK,OAAO,EAAA,CAAE,CAAK,GAAD,IAAQ,KAAK,AAEjE,AAAI,EAF+D,AAEpD,GAAW,CAAP,OACf,EAAW,GAAW,CAAP,UACZ,OACT,CAEQ,eAAe,CAAa,CAAW,CAC7C,OAAO,EAAS,SAAS,CAAG,EAAS,YAAY,GACzC,CAAD,CAAU,cAAc,EAAI,EAAS,SAAS,CAA2B,KAAxB,EAAS,YAAY,AAAG,CAAI,AACtF,CAEQ,8BAA8B,CAAa,CAAU,CAC3D,IAAI,EAAQ,EAAE,AAGR,EAAe,AAAC,GAAS,QAHJ,CAGa,CAAG,EAAS,YAAY,AAAZ,EAAgB,EAAS,YAAY,CAAI,IAW7F,OAVI,EAAc,GAAI,GAAS,EACtB,EAAc,GAAI,GAAS,EAC3B,EAAc,IAAG,IAAS,EAG/B,EAAS,cAAc,GAAE,IAAS,EAGlC,EAAS,aAAa,CAAG,EAAS,gBAAgB,GAAE,IAAS,EAE1D,KAAK,GAAG,CAAC,GAAI,EACtB,CAEQ,uBAAuB,CAAS,CAA6E,CACnH,IAEM,EAFA,AAAQ,AAEE,EAFG,KAAK,CAAC,WAAW,GAEZ,IADX,CAAC,CACgB,CADX,IAAI,EAAI,EAAA,CAAE,CAAE,WAAW,UAItC,AAAJ,EAAY,QAAQ,CAAC,SAAW,CAAD,CAAS,QAAQ,CAAC,aAAe,EAAQ,QAAQ,CAAC,WAAA,CAAW,CACnF,CAAE,CADoF,IAC9E,eAAgB,KAAM,SAAU,OAAQ,SAAU,EAG/D,EAAQ,QAAQ,CAAC,WAAa,CAAD,CAAS,QAAQ,CAAC,aAAe,EAAQ,QAAQ,CAAC,aAAA,CAAa,CACvF,CAAE,CADwF,IAClF,qBAAsB,KAAM,SAAU,OAAQ,SAAU,EAIrE,EAAQ,QAAQ,CAAC,cAAgB,CAAD,CAAS,QAAQ,CAAC,QAAU,EAAQ,QAAQ,CAAC,UAAA,CAAU,CAClF,CAAE,CADmF,IAC7E,eAAgB,KAAM,SAAU,OAAQ,SAAU,EAG/D,EAAQ,QAAQ,CAAC,gBAAkB,EAAQ,QAAQ,CAAC,iBAC/C,CADiE,AAC/D,KAAM,cAAe,KAAM,SAAU,OAAQ,SAAU,EAI9D,EAAQ,QAAQ,CAAC,WAAa,EAAQ,QAAQ,CAAC,gBAAkB,EAAQ,QAAQ,CAAC,UAC7E,CADwF,AACtF,KAAM,qBAAsB,KAAM,SAAU,OAAQ,SAAU,EAIrE,EAAQ,QAAQ,CAAC,gBAAkB,EAAQ,QAAQ,CAAC,eAC/C,CAD+D,AAC7D,KAAM,cAAe,KAAM,SAAU,OAAQ,SAAU,EAG3D,IACT,CAEQ,0BAA0B,CAAS,CAAE,CAA0B,CAAU,CAC/E,IAAI,EAAQ,EAAE,AAcd,OAXI,IAAI,CAAC,CAHkB,mBAGE,CAAC,EAAK,IAAI,GAAG,KAAS,EAG/C,CAAC,eAAgB,qBAAsB,yBAAyB,CAAC,QAAQ,CAAC,KAC5E,IAAS,EAIY,IALsE,SAKzF,EAAK,SAAS,CAAiB,GAAS,EAChB,aAAnB,EAAK,SAAS,GAAiB,IAAS,EAE1C,KAAK,GAAG,CAAC,EAAG,KAAK,GAAG,CAAC,GAAI,GAClC,CAEQ,qBAAqB,CAAY,CAAW,CAKlD,MAAO,AAJiB,CACtB,cAAe,gBAAiB,UAAW,WAC3C,kBAAmB,YAAa,UAAW,UAC5C,CACsB,IAAI,CAAC,GAAU,EAAK,WAAW,GAAG,QAAQ,CAAC,GACpE,CAEQ,oBAAoB,CAAY,CAAY,CAClD,IAAM,EAAW,EAAE,CACb,EAAU,EAAK,WAAW,GAYhC,IAAK,GAAM,CAAC,EAAU,EAAM,GAAI,OAAO,OAAO,CAV3B,AAU4B,CAT7C,SAAY,CAAC,CAS6C,UATjC,MAAO,UAAW,SAAS,CACpD,IAAO,CAAC,MAAO,WAAY,OAAQ,QAAQ,CAC3C,OAAU,CAAC,SAAU,cAAe,SAAU,WAAW,CACzD,YAAe,CAAC,cAAe,gBAAiB,WAAW,CAC3D,SAAY,CAAC,WAAY,OAAQ,YAAY,CAC7C,QAAW,CAAC,UAAW,SAAU,YAAY,CAC7C,UAAa,CAAC,YAAa,UAAW,UACxC,AADkD,GAI5C,EAAM,IAAI,CAAC,GAAQ,EAAQ,QAAQ,CAAC,KACtC,EAAS,CADqC,GACjC,CAAC,GAIlB,OAAO,CACT,CAEQ,qBAAqB,CAA0B,CAA2D,CAChH,OAAQ,GACN,IAAK,yBACL,IAAK,eACL,IAAK,qBAQL,QAPE,MAAO,YACT,KAAK,kBACL,IAAK,oBACL,IAAK,cACH,MAAO,aACT,KAAK,cACH,MAAO,WAGX,CACF,CAEQ,2BAA2B,CAAmB,CAAW,CAG/D,OADoB,AACb,KADkB,GAAG,CAAC,EAAe,QAAQ,CAAG,EAAe,aAAa,GAC7D,GAAK,EAAe,WAAW,CAAG,CAC1D,CAEQ,6BAA6B,CAAmB,CAAU,CAChE,IAAI,EAAQ,EAAE,AAGG,AACb,CADc,YAHS,IAGQ,iBAAkB,YAAa,kBAAkB,CACvE,IAAI,CAAC,GAAQ,EAAe,cAAc,CAAC,WAAW,GAAG,QAAQ,CAAC,MAC7E,EADqF,EAC5E,EAIX,IAAM,EAAc,KAAK,GAAG,CAAC,EAAe,QAAQ,CAAG,EAAe,aAAa,EAOnF,OANI,GAAe,EAAG,GAAS,EACtB,GAAe,IAAG,IAAS,EAGhC,EAAe,iBAAiB,CAAG,KAAI,IAAS,EAE7C,KAAK,GAAG,CAAC,GAAI,EACtB,CAEQ,0BAA0B,CAAU,CAAW,CAErD,OADoB,AACb,EADmB,oBAAoB,CAAG,EAAM,KAAK,EACtC,KACO,MADI,EACI,EAA9B,EAAM,WACf,AAD0B,CAGlB,EAJ+C,iBACmB,UAGrC,CAAU,CAAU,CACvD,IAAI,EAAQ,EAAE,AAER,EAAc,EAAM,SAFC,WAEmB,CAAG,EAAM,KAAK,CAe5D,OAZI,GAAe,IAAU,GAAS,EAC7B,CAD+B,EAChB,IAAS,EADe,CACN,EACjC,CADmC,EACpB,KAD2B,CAClB,IAAS,EAGtC,CAHwC,CAGlC,MAHyC,KAG9B,CAAC,WAAW,GAAG,QAAQ,CAAC,QACzC,EAAM,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,OAC3C,CADmD,EAC1C,EACA,EAAM,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,aAAa,CAC/D,IAAS,EAGJ,KAAK,GAAG,CAAC,GAAI,EACtB,CAEQ,uBAAuB,CAAW,CAAW,CAEnD,MAAO,AADoB,CAAC,MAAO,OAAQ,OAAQ,MAAO,MAAO,MAAO,MAAM,CACpD,QAAQ,CAAC,EAAO,IAAI,CAChD,CAEQ,iBAAiB,CAAkB,CAAgB,OAIzD,AAAI,AAHiB,CAAC,MAAO,MAAO,MAAM,CAAC,AAG1B,QAAQ,CAAC,GAAoB,SAFzB,AAGjB,CAHkB,AAEiB,OAFT,OAAQ,MAAM,CAG3B,AAJqE,AACzC,QAGpB,CAAC,GAAoB,SACvC,CADgC,OAEzC,CAEQ,OAP4E,YAOzD,CAAkB,CAAkB,CAE7D,MAAO,SACT,CAEQ,+BAA+B,CAAW,CAAU,CAC1D,IAAI,EAAQ,EAAE,AAMd,MAHI,CAAC,MAHsB,AAGf,MAAM,CAAC,QAAQ,CAAC,EAAO,IAAI,EAAG,GAAS,EAC1C,CAAC,OAAQ,OAAO,CAAC,QAAQ,CAAC,EAAO,IAAI,IAAG,IAAS,EAEnD,KAAK,GAAG,CAAC,GAAI,EACtB,CAEQ,0BAA0B,CAAkB,CAA2D,CAC7G,OAAQ,GACN,IAAK,MAAO,MAAO,YACnB,CADgC,IAC3B,MAEL,QAFY,AADsC,MAC/B,aACnB,CADiC,IAC5B,MAAO,MAAO,KADmC,MAGxD,CAFiC,AAGnC,CACF,kBAJwD,wKEzhBxD,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,CAAA,CAAA,OAAA,IAAA,EAAA,EAAA,CAAA,CAAA,4CDfA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEO,eAAe,EAAI,CAAoB,EAC5C,GAAI,CACF,GAAM,cAAE,CAAY,CAAE,CAAG,IAAI,IAAI,EAAQ,GAAG,EAEtC,EAAS,EAAa,GAAG,CAAC,UAC1B,EAAU,EAAa,GAAG,CAAC,YAAY,MAAM,KAAK,OAAO,SACzD,EAAgB,EAAa,GAAG,CAAC,UAAU,MAAM,KAAK,OAAO,SAC7D,EAAa,SAAS,EAAa,GAAG,CAAC,eAAiB,KACxD,EAA8C,SAAlC,EAAa,GAAG,CAAC,aAC7B,EAAQ,SAAS,EAAa,GAAG,CAAC,UAAY,MAEpD,GAAI,CAAC,GAAU,CAAC,EACd,OADuB,AAChB,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,SAAS,EAAO,MAAO,sCAAuC,EAChE,CAAE,OAAQ,GAAI,GAIlB,QAAQ,GAAG,CAAC,gDAAiD,QAC3D,EACA,QAAS,GAAS,QAAU,gBAC5B,aACA,YACA,QACA,CACF,GAGA,IAAM,EAAiB,IAAI,EAAA,uBAAuB,CAChD,QAAQ,GAAG,CAAC,WAAW,CACvB,QAAQ,GAAG,CAAC,eAAe,EAGzB,EAAe,EAAE,CAErB,GAAI,EAAQ,CAEV,IAAM,EAAY,MAAM,EAAe,eAAe,CAAC,GACvD,EAAa,IAAI,IAAI,EACvB,MAAO,GAAI,EAAS,CAElB,IAAM,EAAmB,EAAQ,GAAG,CAAC,GAAO,EAAe,eAAe,CAAC,IAE3E,EAAe,CADS,MAAM,QAAQ,GAAG,CAAC,EAAA,EACX,IAAI,EACrC,CAGA,IAAI,EAAoB,EAEpB,GAAiB,EAAc,MAAM,CAAG,GAAG,CAC7C,EAAoB,EAAkB,MAAM,CAAC,GAC3C,EAAc,QAAQ,CAAC,EAAS,IAAI,EAAA,EAIpC,EAAa,GAAG,CAClB,EAAoB,EAAkB,MAAM,CAAC,GAC3C,EAAS,YAAY,EAAI,EAAA,EAIzB,IACF,EAAoB,EAAkB,GADzB,GAC+B,CAAC,GACpB,UAAvB,EAAS,SAAS,CAAK,EAK3B,EAAkB,IAAI,CAAC,CAAC,EAAG,KACzB,IAAM,EAAkB,AAAC,IACvB,OAAQ,GACN,IAAK,QAAS,OAAO,CACrB,KAAK,WAAY,OAAO,CACxB,KAAK,QAAS,OAAO,CACrB,SAAS,OAAO,CAClB,CACF,EAEM,EAAyC,GAA/B,EAAgB,EAAE,SAAS,EAAS,EAAE,YAAY,CAGlE,OAF+C,AAExC,GAFS,EAAgB,EAAE,SAAS,EAAS,EAAE,YAAY,CAEjD,CACnB,GAGA,IAAM,EAAmB,EAAkB,KAAK,CAAC,EAAG,GAG9C,EAAU,CACd,WAAY,EAAa,MAAM,CAC/B,aAAc,EAAkB,MAAM,CACtC,SAAU,EAAiB,MAAM,CACjC,OAAQ,EAAiB,MAAM,CAAC,CAAC,EAAK,KACpC,CAAG,CAAC,EAAS,IAAI,CAAC,CAAG,CAAC,CAAG,CAAC,EAAS,IAAI,CAAC,GAAI,CAAC,CAAI,EAC1C,GACN,CAAC,GACJ,OAAQ,EAAiB,MAAM,CAAC,CAAC,EAAK,KACpC,CAAG,CAAC,EAAS,IAAI,CAAC,CAAG,CAAC,CAAG,CAAC,EAAS,IAAI,CAAC,GAAI,CAAC,CAAI,EAC1C,GACN,CAAC,GACJ,YAAa,EAAiB,MAAM,CAAC,CAAC,EAAK,KACzC,CAAG,CAAC,EAAS,SAAS,CAAC,CAAG,CAAC,CAAG,CAAC,EAAS,SAAS,CAAC,GAAI,CAAC,CAAI,EACpD,GACN,CAAC,GACJ,gBAAiB,EAAiB,MAAM,CAAG,EACvC,KAAK,KAAK,CAAE,EAAiB,MAAM,CAAC,CAAC,EAAK,IAAM,EAAM,EAAE,YAAY,CAAE,GAAK,EAAiB,MAAM,CAAI,KAAO,IAC7G,CACN,EAEM,EAAW,CACf,SAAS,EACT,KAAM,CACJ,UAAW,EACX,UACA,QAAS,CACP,SACA,QAAS,GAAS,QAAU,gBAC5B,aACA,YACA,QACA,CACF,EACA,UAAW,IAAI,OAAO,WAAW,EACnC,CACF,EAEA,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,EAC3B,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,mCAAoC,GAC3C,EAAA,YAAY,CAAC,IAAI,CACtB,CACE,SAAS,EACT,MAAO,6BACP,QAAS,aAAiB,MAAQ,EAAM,OAAO,CAAG,eACpD,EACA,CAAE,OAAQ,GAAI,EAElB,CACF,CAEO,eAAe,EAAK,CAAoB,EAC7C,GAAI,CAEF,GAAM,QAAE,CAAM,MAAE,CAAI,CAAE,CADT,EACY,IADN,EAAQ,IAAI,GAGzB,EAAiB,IAAI,EAAA,uBAAuB,CAChD,QAAQ,GAAG,CAAC,WAAW,CACvB,QAAQ,GAAG,CAAC,eAAe,EAG7B,GACO,iBADC,EAmCJ,OAAO,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,QAAS,GAAO,MAAO,gBAAiB,EAC1C,CAAE,OAAQ,GAAI,IAnChB,IAAM,EAAU,EAAK,OAAO,EAAI,EAAE,CAClC,GAAI,CAAC,MAAM,OAAO,CAAC,IAA+B,GAAG,CAAtB,EAAQ,MAAM,CAC3C,OAAO,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,SAAS,EAAO,MAAO,wBAAyB,EAClD,CAAE,OAAQ,GAAI,GAIlB,IAAM,EAAe,EAAE,CACvB,IAAK,IAAM,KAAU,EACnB,GAAI,CACF,EAF0B,EAEpB,EAAY,MAAM,EAAe,eAAe,CAAC,GACvD,EAAa,IAAI,CAAC,QAChB,YACA,EACA,SAAS,CACX,EACF,CAAE,MAAO,EAAO,CACd,EAAa,IAAI,CAAC,QAChB,EACA,UAAW,EAAE,CACb,SAAS,EACT,MAAO,aAAiB,MAAQ,EAAM,OAAO,CAAG,eAClD,EACF,CAGF,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,CACvB,SAAS,EACT,KAAM,CAAE,QAAS,CAAa,CAChC,GAQN,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,wCAAyC,GAChD,EAAA,YAAY,CAAC,IAAI,CACtB,CACE,SAAS,EACT,MAAO,+CACP,QAAS,aAAiB,MAAQ,EAAM,OAAO,CAAG,eACpD,EACA,CAAE,OAAQ,GAAI,EAElB,CACF,CC5LA,IAAA,EAAA,EAAA,CAAA,CAAA,OAIA,IAAM,EAAc,IAAI,EAAA,mBAAmB,CAAC,CACxC,WAAY,CACR,KAAM,EAAA,SAAS,CAAC,SAAS,CACzB,KAAM,6BACN,SAAU,uBACV,SAAU,QACV,WAAY,EAChB,EACA,QAAS,CAAA,OACT,IADiD,eACc,CAA3C,EACpB,iBAAkB,iEAClB,iBAZqB,GAarB,SAAA,CACJ,GAIM,kBAAE,CAAgB,sBAAE,CAAoB,aAAE,CAAW,CAAE,CAAG,EAChE,SAAS,IACL,MAAO,CAAA,EAAA,EAAA,UAAW,AAAX,EAAY,kBACf,uBACA,CACJ,EACJ,CAEO,eAAe,EAAQ,CAAG,CAAE,CAAG,CAAE,CAAG,EACvC,IAAI,EACJ,IAAI,EAAU,6BAKV,EAAU,EAAQ,OAAO,CAAC,WAAY,KAAO,IAMjD,IAAM,EAAgB,MAAM,EAAY,OAAO,CAAC,EAAK,EAAK,SACtD,EACA,mBAHE,CAAA,CAIN,GACA,GAAI,CAAC,EAID,OAHA,EAAI,IADY,MACF,CAAG,IACjB,EAAI,GAAG,CAAC,eACS,MAAjB,CAAwB,CAApB,IAAyB,KAAhB,EAAoB,EAAI,SAAS,CAAC,IAAI,CAAC,EAAK,QAAQ,OAAO,IACjE,KAEX,GAAM,SAAE,CAAO,QAAE,CAAM,YAAE,CAAU,aAAE,CAAW,mBAAE,CAAiB,CAAE,qBAAmB,sBAAE,CAAoB,yBAAE,CAAuB,kBAAE,CAAgB,CAAE,CAAG,EACxJ,EAAoB,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,GACvC,GAAQ,EAAQ,EAAkB,aAAa,CAAC,EAAkB,EAAI,EAAkB,MAAM,CAAC,EAAA,AAAiB,EACpH,GAAI,GAAS,CAAC,EAAa,CACvB,IAAM,GAAgB,CAAQ,EAAkB,MAAM,CAAC,EAAiB,CAClE,EAAgB,EAAkB,aAAa,CAAC,EAAkB,CACxE,GAAI,IAC+B,IAA3B,EAAc,KADH,GACW,EAAc,CAAC,EACrC,MAAM,IAAI,EAAA,CAD0C,cAC3B,AAGrC,CACA,IAAI,EAAW,MACX,GAAU,EAAY,IAAb,CAAkB,EAAK,EAAD,EAG/B,EAAW,AAAa,OAHqB,IAC7C,GAAW,CAAA,EAEwB,IAAM,CAAA,EAE7C,IAAM,GACgB,IAAtB,EAAY,EAAkB,GAAb,EAEjB,CAAC,EAKK,EAAe,GAAS,CAAC,EACzB,EAAS,EAAI,MAAM,EAAI,MACvB,EAAS,CAAA,EAAA,EAAA,SAAA,AAAS,IAClB,EAAa,EAAO,WAVyE,OAUvD,GACtC,EAAU,CACZ,2BACA,EACA,WAAY,CACR,aAAc,CACV,iBAAiB,CAAQ,EAAW,YAAY,CAAC,eAAe,CAChE,gBAAgB,CAAQ,EAAW,YAAY,CAAC,cAAc,AAClE,0BACA,EACA,iBAAkB,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,oBACtC,kBAAmB,AAAwD,OAAvD,EAA2B,EAAW,YAAA,AAAY,EAAY,KAAK,EAAI,EAAyB,SAAS,cAC7H,EACA,UAAW,EAAI,SAAS,CACxB,QAAS,AAAC,IACN,EAAI,EAAE,CAAC,QAAS,EACpB,EACA,iBAAkB,OAClB,8BAA+B,CAAC,EAAO,EAAU,IAAe,EAAY,cAAc,CAAC,EAAK,EAAO,EAAc,EACzH,EACA,cAAe,SACX,CACJ,CACJ,EACM,EAAc,IAAI,EAAA,eAAe,CAAC,GAClC,EAAc,IAAI,EAAA,gBAAgB,CAAC,GACnC,EAAU,EAAA,kBAAkB,CAAC,mBAAmB,CAAC,EAAa,CAAA,EAAA,EAAA,sBAAA,AAAsB,EAAC,IAC3F,GAAI,CACA,IAAM,EAAoB,MAAO,GACtB,EAAY,MAAM,CAAC,EAAS,GAAS,OAAO,CAAC,KAChD,GAAI,CAAC,EAAM,OACX,EAAK,aAAa,CAAC,CACf,mBAAoB,EAAI,UAAU,CAClC,YAAY,CAChB,GACA,IAAM,EAAqB,EAAO,qBAAqB,GAEvD,GAAI,CAAC,EACD,OAEJ,GAAI,EAAmB,GAAG,CAAC,EAHF,kBAGwB,EAAA,cAAc,CAAC,aAAa,CAAE,YAC3E,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,EAAmB,GAAG,CAAC,kBAAkB,qEAAqE,CAAC,EAG9J,IAAM,EAAQ,EAAmB,GAAG,CAAC,cACrC,GAAI,EAAO,CACP,IAAM,EAAO,CAAA,EAAG,EAAO,CAAC,EAAE,EAAA,CAAO,CACjC,EAAK,aAAa,CAAC,CACf,aAAc,EACd,aAAc,EACd,iBAAkB,CACtB,GACA,EAAK,UAAU,CAAC,EACpB,MACI,CADG,CACE,UAAU,CAAC,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAE9C,GAEE,EAAiB,MAAO,QACtB,EA0FI,EAzFR,IAAM,EAAoB,MAAO,oBAAE,CAAkB,CAAE,IACnD,GAAI,CACA,GAAI,CAAC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GAAwB,GAA2B,CAAC,EAK3F,OAJA,EAAI,SAD2G,CACjG,CAAG,IAEjB,EAAI,SAAS,CAAC,iBAAkB,eAChC,EAAI,GAAG,CAAC,gCACD,KAEX,IAAM,EAAW,MAAM,EAAkB,GACzC,EAAI,YAAY,CAAG,EAAQ,UAAU,CAAC,YAAY,CAClD,IAAI,EAAmB,EAAQ,UAAU,CAAC,gBAAgB,CAGtD,GACI,EAAI,SAAS,EAAE,CACf,CAFc,CAEV,SAAS,CAAC,GACd,OAAmB,GAG3B,IAAM,EAAY,EAAQ,UAAU,CAAC,aAAa,CAGlD,IAAI,EA6BA,OADA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,EAAU,EAAQ,UAAU,CAAC,gBAAgB,EACnF,IA7BA,EACP,IAAM,EAAO,MAAM,EAAS,IAAI,GAE1B,EAAU,CAAA,EAAA,EAAA,yBAAA,AAAyB,EAAC,EAAS,OAAO,EACtD,IACA,CAAO,CAAC,EAAA,GADG,mBACmB,CAAC,CAAG,CAAA,EAElC,CAAC,CAAO,CAAC,eAAe,EAAI,EAAK,IAAI,EAAE,CACvC,CAAO,CAAC,eAAe,CAAG,EAAK,IAAA,AAAI,EAEvC,IAAM,EAAa,KAAkD,IAA3C,EAAQ,UAAU,CAAC,mBAAmB,IAAoB,EAAQ,UAAU,CAAC,mBAAmB,EAAI,EAAA,cAAA,AAAc,GAAG,AAAQ,EAAQ,UAAU,CAAC,mBAAmB,CACvL,EAAS,AAA8C,SAAvC,EAAQ,UAAU,CAAC,eAAe,EAAoB,EAAQ,UAAU,CAAC,eAAe,EAAI,EAAA,cAAc,MAAG,EAAY,EAAQ,UAAU,CAAC,eAAe,CAcjL,MAZmB,CAYZ,AAXH,MAAO,CACH,KAAM,EAAA,eAAe,CAAC,SAAS,CAC/B,OAAQ,EAAS,MAAM,CACvB,KAAM,OAAO,IAAI,CAAC,MAAM,EAAK,WAAW,YACxC,CACJ,EACA,aAAc,YACV,SACA,CACJ,CACJ,CAEJ,CAKJ,CAAE,KALS,CAKF,EAAK,CAcV,MAX0B,MAAtB,EAA6B,KAAK,EAAI,EAAmB,OAAO,AAAP,EAAS,CAClE,MAAM,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,cAClC,uBACA,CACJ,EACJ,EAAG,GAED,CACV,CACJ,EACM,EAAa,MAAM,EAAY,cAAc,CAAC,KAChD,aACA,WACA,EACA,UAAW,EAAA,SAAS,CAAC,SAAS,CAC9B,YAAY,oBACZ,EACA,mBAAmB,uBACnB,0BACA,oBACA,EACA,UAAW,EAAI,SAAS,AAC5B,GAEA,GAAI,CAAC,EACD,KADQ,EACD,KAEX,GAAI,CAAe,MAAd,CAAqB,EAAS,AAA0C,GAA9C,IAAK,EAAoB,EAAW,KAAA,AAAK,EAAY,KAAK,EAAI,EAAkB,IAAI,IAAM,EAAA,eAAe,CAAC,SAAS,CAE9I,CAFgJ,KAE1I,OAAO,cAAc,CAAC,AAAI,MAAM,CAAC,kDAAkD,EAAgB,MAAd,CAAqB,EAAS,AAA2C,GAA/C,IAAK,EAAqB,EAAW,KAAA,AAAK,EAAY,KAAK,EAAI,EAAmB,IAAI,CAAA,CAAE,EAAG,oBAAqB,CACjO,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,EAEA,CAAC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBACrB,AADqC,EACjC,SAAS,CAAC,iBAAkB,EAAuB,cAAgB,EAAW,MAAM,CAAG,OAAS,EAAW,OAAO,CAAG,QAAU,OAGnI,GACA,EAAI,QADS,CACA,CAAC,gBAAiB,2DAEnC,IAAM,EAAU,CAAA,EAAA,EAAA,2BAA2B,AAA3B,EAA4B,EAAW,KAAK,CAAC,OAAO,EAapE,MAZI,AAAE,CAAD,AAAC,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GACxC,EAD6C,AACrC,GADwC,GAClC,CAAC,EAAA,sBAAsB,GAIrC,EAAW,YAAY,EAAK,EAAI,AAAL,SAAc,CAAC,kBAAqB,EAAD,AAAS,GAAG,CAAC,kBAAkB,AAC7F,EAAQ,GAAG,CAAC,gBAAiB,CAAA,EAAA,EAAA,qBAAA,AAAqB,EAAC,EAAW,YAAY,GAE9E,MAAM,CAAA,EAAA,EAAA,YAAY,AAAZ,EAAa,EAAa,EAAa,IAAI,SAAS,EAAW,KAAK,CAAC,IAAI,CAAE,SAC7E,EACA,OAAQ,EAAW,KAAK,CAAC,MAAM,EAAI,GACvC,IACO,IACX,EAGI,EACA,MAAM,EAAe,EADT,CAGZ,MAAM,EAAO,qBAAqB,CAAC,EAAI,OAAO,CAAE,IAAI,EAAO,KAAK,CAAC,EAAA,cAAc,CAAC,aAAa,CAAE,CACvF,SAAU,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAChC,KAAM,EAAA,QAAQ,CAAC,MAAM,CACrB,WAAY,CACR,cAAe,EACf,cAAe,EAAI,GAAG,AAC1B,CACJ,EAAG,GAEf,CAAE,MAAO,EAAK,CAcV,GAbI,AAAE,CAAD,YAAgB,EAAA,eAAe,EAChC,CADmC,KAC7B,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,cAClC,uBACA,CACJ,EACJ,GAIA,EAAO,MAAM,EAKjB,OAHA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,KAAM,CAC5D,OAAQ,GACZ,IACO,IACX,CACJ,EAEA,qCAAqC", "ignoreList": [4]}