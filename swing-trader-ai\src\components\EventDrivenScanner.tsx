'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  TrendingUp,
  Zap,
  Target,
  AlertTriangle,
  Clock,
  DollarSign,
  Activity,
  Loader2,
  RefreshCw,
  Filter,
  Star,
  Play,
  Eye,
  CheckCircle
} from 'lucide-react'
import { PerfectPickSetup, PreMarketGapScan, Catalyst } from '@/types/trading'
import { DataSourceSelector } from './DataSourceSelector'
import { alertSystem } from '@/lib/alertSystem'
import { formatCurrency, formatPercentage } from '@/lib/utils'

interface EventDrivenScannerProps {
  accountSize?: number
  riskPercent?: number
}

interface UnifiedTradingOpportunity {
  symbol: string
  name: string
  price: number
  gapPercent: number
  catalyst?: Catalyst
  technicalGrade: string
  technicalScore: number
  riskReward: number
  positionSize: number
  entryPrice: number
  stopLoss: number
  target3R: number
  riskAmount: number
  potentialProfit: number
  setupScore: number
  meetsAllCriteria: boolean
  exclusionReasons: string[]
}

export function EventDrivenScanner({
  accountSize = 100000,
  riskPercent = 2
}: EventDrivenScannerProps) {
  const [tradingOpportunities, setTradingOpportunities] = useState<UnifiedTradingOpportunity[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [lastScanTime, setLastScanTime] = useState<string>('')
  const [scanSummary, setScanSummary] = useState<any>(null)
  const [dataSource, setDataSource] = useState<'IBKR' | 'Polygon'>('IBKR')

  // Auto-refresh every 15 minutes during market hours
  useEffect(() => {
    const interval = setInterval(() => {
      const now = new Date()
      const hour = now.getHours()
      // Refresh during pre-market (4-9:30 AM EST) and market hours (9:30 AM - 4 PM EST)
      if ((hour >= 4 && hour < 21)) {
        handleUnifiedScan()
      }
    }, 15 * 60 * 1000) // 15 minutes

    return () => clearInterval(interval)
  }, [])

  const handleUnifiedScan = async () => {
    setIsLoading(true)
    try {
      console.log('🎯 Starting unified Event-Driven scan...')

      // Run the complete Perfect-Pick scan which includes all components
      const response = await fetch(`/api/scanner/perfect-pick?accountSize=${accountSize}&riskPercent=${riskPercent}&limit=30&dataSource=${dataSource}`)
      const data = await response.json()

      if (data.success && data.data.setups) {
        // Convert Perfect-Pick setups to unified trading opportunities
        const opportunities: UnifiedTradingOpportunity[] = data.data.setups.map((setup: PerfectPickSetup) => ({
          symbol: setup.symbol,
          name: setup.name,
          price: setup.gapScan.price,
          gapPercent: setup.gapScan.gapPercent,
          catalyst: setup.catalyst,
          technicalGrade: setup.technicalGate.overallGrade,
          technicalScore: setup.technicalGate.gateScore,
          riskReward: setup.rewardPlanning.riskRewardRatio,
          positionSize: setup.riskManagement.positionSize,
          entryPrice: setup.riskManagement.entryPrice,
          stopLoss: setup.riskManagement.stopLoss,
          target3R: setup.rewardPlanning.target3R,
          riskAmount: setup.riskManagement.positionSize * setup.riskManagement.riskPerShare,
          potentialProfit: setup.riskManagement.positionSize * setup.riskManagement.riskPerShare * setup.rewardPlanning.riskRewardRatio,
          setupScore: setup.overallScore,
          meetsAllCriteria: setup.validationChecks.noExclusionFlags,
          exclusionReasons: setup.exclusionReasons
        }))

        setTradingOpportunities(opportunities)
        setScanSummary(data.data.summary)
        setLastScanTime(new Date().toLocaleTimeString())

        // Generate alerts for new opportunities
        alertSystem.monitorPerfectPickSetups(data.data.setups)

        console.log(`✅ Found ${opportunities.length} trading opportunities`)
      } else {
        console.log('⚠️ No trading opportunities found')
        setTradingOpportunities([])
      }
    } catch (error) {
      console.error('Error running unified scan:', error)
      setTradingOpportunities([])
    } finally {
      setIsLoading(false)
    }
  }

  const getCatalystBadgeColor = (catalyst: Catalyst) => {
    if (catalyst.tier === 'tier_1') return 'bg-green-500 text-white'
    if (catalyst.tier === 'tier_2') return 'bg-yellow-500 text-white'
    return 'bg-gray-500 text-white'
  }

  const getCatalystImpactIcon = (impact: string) => {
    switch (impact) {
      case 'bullish': return <TrendingUp className="h-4 w-4 text-green-500" />
      case 'bearish': return <AlertTriangle className="h-4 w-4 text-red-500" />
      default: return <Activity className="h-4 w-4 text-gray-500" />
    }
  }

  const getSetupGradeColor = (grade: string) => {
    if (grade.startsWith('A')) return 'text-green-600 bg-green-50 border-green-200'
    if (grade.startsWith('B')) return 'text-blue-600 bg-blue-50 border-blue-200'
    if (grade.startsWith('C')) return 'text-yellow-600 bg-yellow-50 border-yellow-200'
    return 'text-red-600 bg-red-50 border-red-200'
  }

  const getFreshnessColor = (freshness: string) => {
    switch (freshness) {
      case 'fresh': return 'text-green-600 bg-green-50'
      case 'moderate': return 'text-yellow-600 bg-yellow-50'
      case 'stale': return 'text-red-600 bg-red-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  const handleExecuteTrade = (opportunity: UnifiedTradingOpportunity) => {
    console.log('Execute trade for:', opportunity.symbol)
    // TODO: Implement trade execution
    alertSystem.addAlert({
      type: 'entry_trigger',
      symbol: opportunity.symbol,
      title: `Trade Executed: ${opportunity.symbol}`,
      message: `Entered position at ${formatCurrency(opportunity.entryPrice)}`,
      priority: 'high',
      actionable: false
    })
  }

  const handleViewChart = (symbol: string) => {
    console.log('View chart for:', symbol)
    // TODO: Implement chart viewing
    window.open(`https://finance.yahoo.com/quote/${symbol}`, '_blank')
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Event-Driven Scanner</h2>
          <p className="text-muted-foreground">
            Unified catalyst detection, gap analysis, and technical screening
          </p>
        </div>
        <div className="flex items-center gap-2">
          <DataSourceSelector
            onSourceChange={setDataSource}
            className="mr-2"
          />
          {lastScanTime && (
            <span className="text-sm text-muted-foreground">
              Last scan: {lastScanTime}
            </span>
          )}
          <Button
            onClick={handleUnifiedScan}
            disabled={isLoading}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <Target className="h-4 w-4 mr-2" />
            )}
            {isLoading ? 'Scanning...' : 'Scan Opportunities'}
          </Button>
        </div>
      </div>

      {/* Scan Summary */}
      {scanSummary && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Scan Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{tradingOpportunities.length}</div>
                <div className="text-sm text-muted-foreground">Opportunities Found</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {tradingOpportunities.filter(op => op.meetsAllCriteria).length}
                </div>
                <div className="text-sm text-muted-foreground">Perfect-Pick Setups</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {tradingOpportunities.length > 0
                    ? formatPercentage(tradingOpportunities.reduce((sum, op) => sum + op.gapPercent, 0) / tradingOpportunities.length)
                    : '0%'
                  }
                </div>
                <div className="text-sm text-muted-foreground">Avg Gap</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {new Set(tradingOpportunities.filter(op => op.catalyst).map(op => op.catalyst!.type)).size}
                </div>
                <div className="text-sm text-muted-foreground">Catalyst Types</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Trading Opportunities */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Trading Opportunities</h3>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {tradingOpportunities.filter(op => op.meetsAllCriteria).length} Perfect-Pick
            </Badge>
            <Badge variant="outline" className="text-xs">
              {tradingOpportunities.filter(op => op.catalyst?.tier === 'tier_1').length} Tier-1 Catalysts
            </Badge>
          </div>
        </div>

        <div className="grid gap-4">
          {tradingOpportunities.map((opportunity) => (
            <Card key={opportunity.symbol} className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-blue-500">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div>
                      <CardTitle className="text-xl font-bold flex items-center gap-2">
                        {opportunity.symbol}
                        {opportunity.meetsAllCriteria && <Star className="h-5 w-5 text-yellow-500" />}
                      </CardTitle>
                      <CardDescription>{opportunity.name}</CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={`${getSetupGradeColor(opportunity.technicalGrade)} border`}>
                        Grade {opportunity.technicalGrade}
                      </Badge>
                      <Badge variant="outline" className="font-semibold">
                        Score: {opportunity.setupScore}/100
                      </Badge>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-blue-600">
                      {formatCurrency(opportunity.price)}
                    </div>
                    <div className="text-sm font-semibold text-green-600">
                      Gap: +{formatPercentage(opportunity.gapPercent)}
                    </div>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                {/* Catalyst Information */}
                {opportunity.catalyst && (
                  <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border">
                    <div className="flex items-center gap-2 mb-2">
                      <Zap className="h-5 w-5 text-purple-600" />
                      <span className="font-semibold text-purple-800">Catalyst</span>
                      {getCatalystImpactIcon(opportunity.catalyst.impact)}
                      <Badge className={getCatalystBadgeColor(opportunity.catalyst)}>
                        {opportunity.catalyst.tier.replace('_', ' ').toUpperCase()}
                      </Badge>
                      <Badge className={getFreshnessColor(opportunity.catalyst.freshness)}>
                        {opportunity.catalyst.freshness}
                      </Badge>
                    </div>
                    <h4 className="font-medium text-gray-800 mb-1">{opportunity.catalyst.title}</h4>
                    <p className="text-sm text-gray-600 mb-2">{opportunity.catalyst.description}</p>
                    <div className="flex items-center gap-4 text-xs text-gray-500">
                      <span>Quality: {opportunity.catalyst.qualityScore}/10</span>
                      <span>Source: {opportunity.catalyst.source}</span>
                      <span className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {new Date(opportunity.catalyst.announcementTime).toLocaleString()}
                      </span>
                    </div>
                  </div>
                )}

                {/* Risk/Reward Summary */}
                <div className="grid grid-cols-4 gap-4">
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <div className="text-sm text-blue-600 font-medium">Entry</div>
                    <div className="text-lg font-bold text-blue-800">
                      {formatCurrency(opportunity.entryPrice)}
                    </div>
                  </div>
                  <div className="text-center p-3 bg-red-50 rounded-lg">
                    <div className="text-sm text-red-600 font-medium">Stop Loss</div>
                    <div className="text-lg font-bold text-red-800">
                      {formatCurrency(opportunity.stopLoss)}
                    </div>
                  </div>
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <div className="text-sm text-green-600 font-medium">Target (3R)</div>
                    <div className="text-lg font-bold text-green-800">
                      {formatCurrency(opportunity.target3R)}
                    </div>
                  </div>
                  <div className="text-center p-3 bg-purple-50 rounded-lg">
                    <div className="text-sm text-purple-600 font-medium">R/R Ratio</div>
                    <div className="text-lg font-bold text-purple-800">
                      {opportunity.riskReward}:1
                    </div>
                  </div>
                </div>

                {/* Position Sizing */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Position Size:</span>
                      <div className="font-semibold">{opportunity.positionSize} shares</div>
                    </div>
                    <div>
                      <span className="text-gray-600">Risk Amount:</span>
                      <div className="font-semibold text-red-600">{formatCurrency(opportunity.riskAmount)}</div>
                    </div>
                    <div>
                      <span className="text-gray-600">Potential Profit (3R):</span>
                      <div className="font-semibold text-green-600">{formatCurrency(opportunity.potentialProfit)}</div>
                    </div>
                  </div>
                </div>

                {/* Technical Summary */}
                <div className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Target className="h-4 w-4 text-slate-600" />
                    <span className="text-sm font-medium">Technical Analysis:</span>
                    <Badge variant={opportunity.technicalGrade === 'A' ? 'default' : 'secondary'}>
                      Grade {opportunity.technicalGrade}
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      ({opportunity.technicalScore}/100)
                    </span>
                  </div>
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    {opportunity.meetsAllCriteria && <CheckCircle className="h-3 w-3 text-green-500" />}
                    <span>{opportunity.meetsAllCriteria ? 'All Criteria Met' : 'Partial Match'}</span>
                  </div>
                </div>

                {/* Exclusion Reasons */}
                {opportunity.exclusionReasons.length > 0 && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex items-center gap-2 mb-1">
                      <AlertTriangle className="h-4 w-4 text-red-600" />
                      <span className="font-medium text-red-800">Exclusion Reasons</span>
                    </div>
                    <ul className="text-sm text-red-600 space-y-1">
                      {opportunity.exclusionReasons.map((reason, index) => (
                        <li key={index} className="flex items-center gap-1">
                          <span>• {reason}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex items-center gap-2 pt-2">
                  <Button
                    onClick={() => handleExecuteTrade(opportunity)}
                    className="flex-1 bg-blue-600 hover:bg-blue-700"
                    disabled={!opportunity.meetsAllCriteria}
                  >
                    <Play className="h-4 w-4 mr-2" />
                    Execute Trade
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handleViewChart(opportunity.symbol)}
                    className="flex-1"
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    View Chart
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {tradingOpportunities.length === 0 && !isLoading && (
          <Card>
            <CardContent className="text-center py-12">
              <Target className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Trading Opportunities Found</h3>
              <p className="text-muted-foreground mb-4">
                Click "Scan Opportunities" to search for event-driven trading setups with catalyst detection, gap analysis, and technical screening.
              </p>
              <Button
                onClick={handleUnifiedScan}
                disabled={isLoading}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Target className="h-4 w-4 mr-2" />
                Start Scanning
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
