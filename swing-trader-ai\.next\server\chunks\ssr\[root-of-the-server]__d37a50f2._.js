module.exports=[89578,a=>{a.v({className:"geist_a71539c9-module__T19VSG__className",variable:"geist_a71539c9-module__T19VSG__variable"})},35214,a=>{a.v({className:"geist_mono_8d43a2aa-module__8Li5zG__className",variable:"geist_mono_8d43a2aa-module__8Li5zG__variable"})},5558,a=>{"use strict";a.s(["default",()=>h,"metadata",()=>g],5558);var b=a.i(47563),c=a.i(89578);let d={className:c.default.className,style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"}};null!=c.default.variable&&(d.variable=c.default.variable);var e=a.i(35214);let f={className:e.default.className,style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"}};null!=e.default.variable&&(f.variable=e.default.variable);let g={title:"SwingTrader AI - AI-Powered Swing Trading Analysis",description:"Advanced AI-powered swing trading analysis platform with real-time market data, technical indicators, and risk management tools."};function h({children:a}){return(0,b.jsx)("html",{lang:"en",children:(0,b.jsx)("body",{className:`${d.variable} ${f.variable} antialiased`,children:a})})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__d37a50f2._.js.map