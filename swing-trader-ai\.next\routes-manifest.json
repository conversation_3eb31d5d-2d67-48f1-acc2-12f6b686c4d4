{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "rewrites": {"beforeFiles": [], "afterFiles": [], "fallback": []}, "dynamicRoutes": [{"page": "/api/analysis/strategy/[symbol]", "regex": "^/api/analysis/strategy/([^/]+?)(?:/)?$", "routeKeys": {"nxtPsymbol": "nxtPsymbol"}, "namedRegex": "^/api/analysis/strategy/(?<nxtPsymbol>[^/]+?)(?:/)?$"}, {"page": "/api/analysis/swing/[symbol]", "regex": "^/api/analysis/swing/([^/]+?)(?:/)?$", "routeKeys": {"nxtPsymbol": "nxtPsymbol"}, "namedRegex": "^/api/analysis/swing/(?<nxtPsymbol>[^/]+?)(?:/)?$"}, {"page": "/api/scanner/sector/[sector]", "regex": "^/api/scanner/sector/([^/]+?)(?:/)?$", "routeKeys": {"nxtPsector": "nxtPsector"}, "namedRegex": "^/api/scanner/sector/(?<nxtPsector>[^/]+?)(?:/)?$"}, {"page": "/api/stocks/historical/[symbol]", "regex": "^/api/stocks/historical/([^/]+?)(?:/)?$", "routeKeys": {"nxtPsymbol": "nxtPsymbol"}, "namedRegex": "^/api/stocks/historical/(?<nxtPsymbol>[^/]+?)(?:/)?$"}, {"page": "/api/stocks/quote/[symbol]", "regex": "^/api/stocks/quote/([^/]+?)(?:/)?$", "routeKeys": {"nxtPsymbol": "nxtPsymbol"}, "namedRegex": "^/api/stocks/quote/(?<nxtPsymbol>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "rsc", "varyHeader": "rsc, next-router-state-tree, next-router-prefetch, next-router-segment-prefetch", "prefetchHeader": "next-router-prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "next-router-segment-prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}}