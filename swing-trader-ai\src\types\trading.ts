export interface StockData {
  symbol: string
  name: string
  price: number
  change: number
  changePercent: number
  volume: number
  marketCap?: number
  pe?: number
  dividend?: number
}

export interface CandlestickData {
  timestamp: number
  open: number
  high: number
  low: number
  close: number
  volume: number
}

export interface TechnicalIndicator {
  name: string
  value: number
  signal: 'BUY' | 'SELL' | 'NEUTRAL'
  description: string
}

export interface SwingTradingAnalysis {
  symbol: string
  timeframe: string
  trend: 'BULLISH' | 'BEARISH' | 'SIDEWAYS'
  confidence: number
  entryPrice: number
  stopLoss: number
  takeProfit: number
  riskRewardRatio: number
  indicators: TechnicalIndicator[]
  supportLevels: number[]
  resistanceLevels: number[]
  analysis: string
  recommendation: 'STRONG_BUY' | 'BUY' | 'HOLD' | 'SELL' | 'STRONG_SELL' | 'NO_TRADE'
}

export interface TradeLog {
  id: string
  userId: string
  symbol: string
  entryPrice: number
  exitPrice?: number
  stopLoss: number
  takeProfit: number
  quantity: number
  side: 'LONG' | 'SHORT'
  status: 'OPEN' | 'CLOSED' | 'CANCELLED'
  entryDate: string
  exitDate?: string
  pnl?: number
  notes?: string
}

export interface SwingTradingStrategy {
  id: string
  name: string
  description: string
  indicators: string[]
  timeframes: string[]
  riskRewardMin: number
  maxRiskPercent: number
}

export interface User {
  id: string
  email: string
  accountSize: number
  riskTolerance: number
  preferredTimeframes: string[]
  createdAt: string
}

// ===== EVENT-DRIVEN CATALYST DETECTION TYPES =====

export type CatalystType =
  | 'earnings_beat_guidance'
  | 'fda_approval'
  | 'drug_trial_results'
  | 'contract_win'
  | 'partnership'
  | 'analyst_upgrade'
  | 'analyst_downgrade'
  | 'stock_split'
  | 'merger_acquisition'
  | 'sector_rotation'
  | 'ipo_launch'
  | 'lockup_expiration'
  | 'insider_buying'
  | 'insider_selling'
  | 'institutional_position_change'
  | 'options_unusual_activity'
  | 'social_sentiment_surge'
  | 'sec_filing'
  | 'dividend_announcement'
  | 'buyback_program'

export type CatalystTier = 'tier_1' | 'tier_2' | 'tier_3'

export type CatalystImpact = 'bullish' | 'bearish' | 'neutral'

export interface Catalyst {
  id: string
  symbol: string
  type: CatalystType
  tier: CatalystTier
  impact: CatalystImpact
  title: string
  description: string
  source: string
  sourceUrl?: string
  announcementTime: string
  discoveredTime: string
  qualityScore: number // 1-10 scale
  priceImpactPercent?: number
  volumeImpactMultiplier?: number
  freshness: 'fresh' | 'moderate' | 'stale' // <24hrs, 24-72hrs, >72hrs
  estimatedDuration: 'intraday' | 'short_term' | 'medium_term' | 'long_term' // <1day, 1-5days, 1-4weeks, >1month
  verified: boolean
  tags: string[]
  relatedSymbols?: string[]
  metadata?: Record<string, any>
}

export interface CatalystImpactMeasurement {
  catalystId: string
  symbol: string
  priceAtAnnouncement: number
  currentPrice: number
  percentMove: number
  volumeAtAnnouncement: number
  currentVolume: number
  volumeSurgeRatio: number
  timeElapsed: number // hours since announcement
  effectiveness: number // 1-10 scale based on price/volume response
  decayFactor: number // how much catalyst strength has diminished
}

// ===== PERFECT-PICK TRADING SYSTEM TYPES =====

export interface PreMarketGapScan {
  symbol: string
  name: string
  sector: string
  price: number
  previousClose: number
  gapPercent: number
  preMarketHigh: number
  preMarketLow: number
  preMarketVolume: number
  preMarketDollarVolume: number
  marketCap: number
  averageDailyVolume: number
  catalyst?: Catalyst
  scanTime: string
  meetsAllCriteria: boolean
  criteriaChecks: {
    priceAbove1Dollar: boolean
    gapAbove3Percent: boolean
    marketCapAbove800M: boolean
    preMarketVolumeAbove20K: boolean
    preMarketDollarVolumeAbove1M: boolean
    excludesPennyStocks: boolean
    hasCatalyst: boolean
  }
}

export interface TechnicalGateAnalysis {
  symbol: string
  dailyTrendConfirmed: boolean
  aboveSMA200: boolean
  aboveEMA8: boolean
  respectsEMA8: boolean
  isAtAllTimeHigh: boolean
  hasCleanBreakout: boolean
  volumeExpansion: boolean
  overallGrade: 'A' | 'B' | 'C' | 'D' | 'F'
  gateScore: number // 0-100
  resistanceLevels: number[]
  supportLevels: number[]
  keyTechnicalLevels: {
    sma200: number
    ema8: number
    vwap: number
    previousHigh: number
    previousLow: number
  }
}

export interface IntradayEntryTrigger {
  symbol: string
  preMarketHigh: number
  preMarketLow: number
  vwap: number
  entrySignalType: 'pmh_break' | 'vwap_pullback' | 'first_candle_close'
  entryPrice: number
  entryTime: string
  volumeConfirmation: boolean
  vwapRising: boolean
  noMajorResistance: boolean
  triggerValid: boolean
  urgency: 'immediate' | 'wait_for_pullback' | 'breakout_confirmation'
  conditions: string[]
}

export interface PerfectPickSetup {
  symbol: string
  name: string
  catalyst: Catalyst
  gapScan: PreMarketGapScan
  technicalGate: TechnicalGateAnalysis
  entryTrigger?: IntradayEntryTrigger
  riskManagement: {
    entryPrice: number
    stopLoss: number // Pre-Market Low or Day 1 low
    stopLossType: 'pre_market_low' | 'day_1_low'
    riskPerShare: number
    positionSize: number
    accountRiskPercent: number // 1%, 2%, or 3%
    maxPositionPercent: number // cap at 5%
  }
  rewardPlanning: {
    riskRewardRatio: number // minimum 3:1 required
    target3R: number
    target4R: number
    target5R: number
    scaleOutPlan: {
      level: number
      percentage: number
    }[]
  }
  overallScore: number // 0-100 composite score
  setupGrade: 'A+' | 'A' | 'B+' | 'B' | 'C+' | 'C' | 'D' | 'F'
  exclusionReasons: string[]
  validationChecks: {
    hasValidCatalyst: boolean
    meetsGapCriteria: boolean
    passesTechnicalGate: boolean
    hasEntryTrigger: boolean
    meetsRiskReward: boolean
    noExclusionFlags: boolean
  }
  createdAt: string
  updatedAt: string
}
