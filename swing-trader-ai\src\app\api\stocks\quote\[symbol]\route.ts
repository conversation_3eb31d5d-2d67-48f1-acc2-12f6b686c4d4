import { NextRequest, NextResponse } from 'next/server'
import { FMPAPI } from '@/lib/fmp'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ symbol: string }> }
) {
  try {
    const { symbol } = await params

    if (!symbol) {
      return NextResponse.json(
        { success: false, error: 'Symbol parameter is required' },
        { status: 400 }
      )
    }

    console.log(`📊 Fetching quote for ${symbol}`)

    // Use FMP API for stock quotes
    const fmpAPI = new FMPAPI(process.env.FMP_API_KEY)

    try {
      const quote = await fmpAPI.getStockQuote(symbol.toUpperCase())

      const response = {
        success: true,
        data: quote,
        timestamp: new Date().toISOString()
      }

      return NextResponse.json(response)
    } catch (apiError) {
      console.error(`FMP API error for ${symbol}:`, apiError)

      // Return a fallback response to prevent UI errors
      const fallbackResponse = {
        success: false,
        error: 'API rate limit or service unavailable',
        data: {
          symbol: symbol.toUpperCase(),
          price: 0,
          change: 0,
          changesPercentage: 0,
          volume: 0,
          avgVolume: 0,
          marketCap: 0,
          pe: 0,
          eps: 0,
          dayHigh: 0,
          dayLow: 0,
          open: 0,
          previousClose: 0
        },
        timestamp: new Date().toISOString()
      }

      return NextResponse.json(fallbackResponse, { status: 200 }) // Return 200 to prevent UI errors
    }
  } catch (error) {
    console.error('Error in quote API:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch stock quote',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
