import { NextRequest, NextResponse } from 'next/server'
import { CatalystDetectionEngine } from '@/lib/catalystDetection'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    const symbol = searchParams.get('symbol')
    const symbols = searchParams.get('symbols')?.split(',').filter(Boolean)
    const catalystTypes = searchParams.get('types')?.split(',').filter(Boolean)
    const minQuality = parseInt(searchParams.get('minQuality') || '5')
    const freshOnly = searchParams.get('freshOnly') === 'true'
    const limit = parseInt(searchParams.get('limit') || '20')

    if (!symbol && !symbols) {
      return NextResponse.json(
        { success: false, error: 'Symbol or symbols parameter required' },
        { status: 400 }
      )
    }

    console.log('🔍 Catalyst Detection API called with params:', {
      symbol,
      symbols: symbols?.length || 0,
      catalystTypes,
      minQuality,
      freshOnly,
      limit
    })

    // Initialize Catalyst Detection Engine
    const catalystEngine = new CatalystDetectionEngine(
      process.env.FMP_API_KEY,
      process.env.POLYGON_API_KEY
    )

    let allCatalysts = []

    if (symbol) {
      // Single symbol detection
      const catalysts = await catalystEngine.detectCatalysts(symbol)
      allCatalysts.push(...catalysts)
    } else if (symbols) {
      // Multiple symbols detection
      const catalystPromises = symbols.map(sym => catalystEngine.detectCatalysts(sym))
      const catalystResults = await Promise.all(catalystPromises)
      allCatalysts = catalystResults.flat()
    }

    // Apply filters
    let filteredCatalysts = allCatalysts

    if (catalystTypes && catalystTypes.length > 0) {
      filteredCatalysts = filteredCatalysts.filter(catalyst => 
        catalystTypes.includes(catalyst.type)
      )
    }

    if (minQuality > 1) {
      filteredCatalysts = filteredCatalysts.filter(catalyst => 
        catalyst.qualityScore >= minQuality
      )
    }

    if (freshOnly) {
      filteredCatalysts = filteredCatalysts.filter(catalyst => 
        catalyst.freshness === 'fresh'
      )
    }

    // Sort by quality score and freshness
    filteredCatalysts.sort((a, b) => {
      const freshnessWeight = (freshness: string) => {
        switch (freshness) {
          case 'fresh': return 3
          case 'moderate': return 2
          case 'stale': return 1
          default: return 0
        }
      }
      
      const aWeight = freshnessWeight(a.freshness) * 10 + a.qualityScore
      const bWeight = freshnessWeight(b.freshness) * 10 + b.qualityScore
      
      return bWeight - aWeight
    })

    // Limit results
    const limitedCatalysts = filteredCatalysts.slice(0, limit)

    // Generate summary statistics
    const summary = {
      totalFound: allCatalysts.length,
      afterFilters: filteredCatalysts.length,
      returned: limitedCatalysts.length,
      byTier: limitedCatalysts.reduce((acc, catalyst) => {
        acc[catalyst.tier] = (acc[catalyst.tier] || 0) + 1
        return acc
      }, {} as Record<string, number>),
      byType: limitedCatalysts.reduce((acc, catalyst) => {
        acc[catalyst.type] = (acc[catalyst.type] || 0) + 1
        return acc
      }, {} as Record<string, number>),
      byFreshness: limitedCatalysts.reduce((acc, catalyst) => {
        acc[catalyst.freshness] = (acc[catalyst.freshness] || 0) + 1
        return acc
      }, {} as Record<string, number>),
      avgQualityScore: limitedCatalysts.length > 0 
        ? Math.round((limitedCatalysts.reduce((sum, c) => sum + c.qualityScore, 0) / limitedCatalysts.length) * 100) / 100
        : 0
    }

    const response = {
      success: true,
      data: {
        catalysts: limitedCatalysts,
        summary,
        filters: {
          symbol,
          symbols: symbols?.length || 0,
          catalystTypes,
          minQuality,
          freshOnly,
          limit
        },
        timestamp: new Date().toISOString()
      }
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error in Catalyst Detection API:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to detect catalysts',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, data } = body

    const catalystEngine = new CatalystDetectionEngine(
      process.env.FMP_API_KEY,
      process.env.POLYGON_API_KEY
    )

    switch (action) {
      case 'batch_detect':
        const symbols = data.symbols || []
        if (!Array.isArray(symbols) || symbols.length === 0) {
          return NextResponse.json(
            { success: false, error: 'Symbols array required' },
            { status: 400 }
          )
        }

        const batchResults = []
        for (const symbol of symbols) {
          try {
            const catalysts = await catalystEngine.detectCatalysts(symbol)
            batchResults.push({
              symbol,
              catalysts,
              success: true
            })
          } catch (error) {
            batchResults.push({
              symbol,
              catalysts: [],
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error'
            })
          }
        }

        return NextResponse.json({
          success: true,
          data: { results: batchResults }
        })

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Error in Catalyst Detection POST API:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process catalyst detection request',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
