{"version": 3, "sources": ["turbopack:///[project]/swing-trader-ai/src/lib/polygon.ts", "turbopack:///[project]/swing-trader-ai/src/lib/fmp.ts", "turbopack:///[project]/swing-trader-ai/src/app/api/scanner/gap-scan/route.ts", "turbopack:///[project]/swing-trader-ai/node_modules/next/dist/esm/build/templates/app-route.js"], "sourcesContent": ["import axios from 'axios'\nimport { CandlestickData, StockData } from '@/types/trading'\n\nconst POLYGON_BASE_URL = 'https://api.polygon.io'\nconst API_KEY = process.env.POLYGON_API_KEY\n\nexport class PolygonAPI {\n  private apiKey: string\n\n  constructor(apiKey?: string) {\n    this.apiKey = apiKey || API_KEY || ''\n    if (!this.apiKey) {\n      throw new Error('Polygon API key is required')\n    }\n  }\n\n  // Get current stock quote using Polygon's snapshot endpoint (better for paid plans)\n  async getStockQuote(symbol: string): Promise<StockData> {\n    try {\n      // Use snapshot endpoint for real-time data (available on paid plans)\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v2/snapshot/locale/us/markets/stocks/tickers/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      const data = response.data.results[0]\n      if (!data) {\n        throw new Error(`No data found for ${symbol}`)\n      }\n\n      const ticker = data.value || data\n      const dayData = ticker.day || {}\n      const prevDayData = ticker.prevDay || {}\n      const lastQuote = ticker.lastQuote || {}\n      const lastTrade = ticker.lastTrade || {}\n\n      // Use the most recent price available\n      const currentPrice = lastTrade.p || dayData.c || prevDayData.c\n      const prevClose = prevDayData.c || dayData.o\n      const change = currentPrice - prevClose\n      const changePercent = (change / prevClose) * 100\n\n      return {\n        symbol: symbol.toUpperCase(),\n        name: ticker.name || symbol.toUpperCase(),\n        price: currentPrice,\n        change,\n        changePercent,\n        volume: dayData.v || 0,\n        marketCap: ticker.market_cap,\n        pe: undefined,\n        dividend: undefined\n      }\n    } catch (error) {\n      console.error('Error fetching stock quote from Polygon:', error)\n\n      // Fallback to previous day data if snapshot fails\n      try {\n        const fallbackResponse = await axios.get(\n          `${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/prev`,\n          {\n            params: {\n              adjusted: 'true',\n              apikey: this.apiKey\n            }\n          }\n        )\n\n        const data = fallbackResponse.data.results[0]\n        return {\n          symbol: symbol.toUpperCase(),\n          name: symbol.toUpperCase(),\n          price: data.c,\n          change: data.c - data.o,\n          changePercent: ((data.c - data.o) / data.o) * 100,\n          volume: data.v,\n          marketCap: undefined,\n          pe: undefined,\n          dividend: undefined\n        }\n      } catch (fallbackError) {\n        console.error('Polygon fallback also failed:', fallbackError)\n        throw new Error(`Failed to fetch quote for ${symbol}`)\n      }\n    }\n  }\n\n  // Get historical candlestick data (optimized for paid plans)\n  async getHistoricalData(\n    symbol: string,\n    timespan: 'minute' | 'hour' | 'day' | 'week' | 'month' = 'day',\n    multiplier: number = 1,\n    from: string,\n    to: string\n  ): Promise<CandlestickData[]> {\n    try {\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/range/${multiplier}/${timespan}/${from}/${to}`,\n        {\n          params: {\n            adjusted: 'true',\n            sort: 'asc',\n            limit: 50000, // Higher limit for paid plans\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      if (!response.data.results || response.data.results.length === 0) {\n        console.warn(`No historical data found for ${symbol}`)\n        return []\n      }\n\n      return response.data.results.map((candle: any) => ({\n        timestamp: candle.t,\n        open: candle.o,\n        high: candle.h,\n        low: candle.l,\n        close: candle.c,\n        volume: candle.v\n      }))\n    } catch (error) {\n      console.error(`Error fetching historical data for ${symbol}:`, error)\n\n      // Log the specific error for debugging\n      if (error.response) {\n        console.error(`Polygon API Error: ${error.response.status} - ${error.response.statusText}`)\n        console.error('Response data:', error.response.data)\n      }\n\n      throw new Error(`Failed to fetch historical data for ${symbol}: ${error.message}`)\n    }\n  }\n\n  // Get company details\n  async getCompanyDetails(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v3/reference/tickers/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data.results\n    } catch (error) {\n      console.error('Error fetching company details:', error)\n      return null\n    }\n  }\n\n  // Get market status\n  async getMarketStatus() {\n    try {\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v1/marketstatus/now`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error fetching market status:', error)\n      return null\n    }\n  }\n\n  // Search for stocks\n  async searchStocks(query: string, limit: number = 10) {\n    try {\n      const response = await axios.get(\n        `${POLYGON_BASE_URL}/v3/reference/tickers`,\n        {\n          params: {\n            search: query,\n            market: 'stocks',\n            active: 'true',\n            limit,\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data.results || []\n    } catch (error) {\n      console.error('Error searching stocks:', error)\n      return []\n    }\n  }\n}\n\n// Create a singleton instance\nexport const polygonAPI = new PolygonAPI()\n", "import axios from 'axios'\nimport { StockData } from '@/types/trading'\n\nconst FMP_BASE_URL = 'https://financialmodelingprep.com/api'\nconst API_KEY = process.env.FMP_API_KEY\n\nexport class FMPAPI {\n  private apiKey: string\n\n  constructor(apiKey?: string) {\n    this.apiKey = apiKey || API_KEY || ''\n    if (!this.apiKey) {\n      throw new Error('FMP API key is required')\n    }\n  }\n\n  // Get real-time stock quote\n  async getStockQuote(symbol: string): Promise<StockData> {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/quote/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      const data = response.data[0]\n      if (!data) {\n        throw new Error(`No data found for symbol ${symbol}`)\n      }\n\n      return {\n        symbol: data.symbol,\n        name: data.name || data.symbol,\n        price: data.price,\n        change: data.change,\n        changePercent: data.changesPercentage,\n        volume: data.volume,\n        marketCap: data.marketCap,\n        pe: data.pe,\n        dividend: undefined // Will be fetched separately if needed\n      }\n    } catch (error) {\n      console.error('Error fetching FMP stock quote:', error)\n      throw new Error(`Failed to fetch quote for ${symbol}`)\n    }\n  }\n\n  // Get company profile\n  async getCompanyProfile(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/profile/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data[0]\n    } catch (error) {\n      console.error('Error fetching company profile:', error)\n      return null\n    }\n  }\n\n  // Get financial ratios\n  async getFinancialRatios(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/ratios/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data[0] // Most recent ratios\n    } catch (error) {\n      console.error('Error fetching financial ratios:', error)\n      return null\n    }\n  }\n\n  // Get key metrics\n  async getKeyMetrics(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/key-metrics/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data[0] // Most recent metrics\n    } catch (error) {\n      console.error('Error fetching key metrics:', error)\n      return null\n    }\n  }\n\n  // Get analyst recommendations\n  async getAnalystRecommendations(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/analyst-stock-recommendations/${symbol}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error fetching analyst recommendations:', error)\n      return []\n    }\n  }\n\n  // Get earnings calendar\n  async getEarningsCalendar(from?: string, to?: string) {\n    try {\n      const params: any = {\n        apikey: this.apiKey\n      }\n\n      if (from) params.from = from\n      if (to) params.to = to\n\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/earning_calendar`,\n        { params }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error fetching earnings calendar:', error)\n      return []\n    }\n  }\n\n  // Get economic calendar\n  async getEconomicCalendar(from?: string, to?: string) {\n    try {\n      const params: any = {\n        apikey: this.apiKey\n      }\n\n      if (from) params.from = from\n      if (to) params.to = to\n\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/economic_calendar`,\n        { params }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error fetching economic calendar:', error)\n      return []\n    }\n  }\n\n  // Search for stocks\n  async searchStocks(query: string, limit: number = 10) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/search`,\n        {\n          params: {\n            query,\n            limit,\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error searching stocks:', error)\n      return []\n    }\n  }\n\n  // Get sector performance\n  async getSectorPerformance() {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/sector-performance`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error('Error fetching sector performance:', error)\n      return []\n    }\n  }\n\n  // Get market gainers/losers\n  async getMarketMovers(type: 'gainers' | 'losers' | 'actives') {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/stock_market/${type}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data\n    } catch (error) {\n      console.error(`Error fetching market ${type}:`, error)\n      return []\n    }\n  }\n\n  // ===== CATALYST DETECTION ENDPOINTS =====\n\n  // Get earnings calendar for catalyst detection\n  async getEarningsCalendar(symbol?: string, days: number = 30) {\n    try {\n      const fromDate = new Date()\n      fromDate.setDate(fromDate.getDate() - days)\n      const toDate = new Date()\n\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/earning_calendar`,\n        {\n          params: {\n            apikey: this.apiKey,\n            from: fromDate.toISOString().split('T')[0],\n            to: toDate.toISOString().split('T')[0],\n            ...(symbol && { symbol: symbol.toUpperCase() })\n          }\n        }\n      )\n\n      return response.data || []\n    } catch (error) {\n      console.error('Error fetching earnings calendar:', error)\n      return []\n    }\n  }\n\n  // Get stock news for catalyst detection\n  async getStockNews(symbol: string, limit: number = 50) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/stock_news`,\n        {\n          params: {\n            apikey: this.apiKey,\n            tickers: symbol.toUpperCase(),\n            limit\n          }\n        }\n      )\n\n      return response.data || []\n    } catch (error) {\n      console.error('Error fetching stock news:', error)\n      return []\n    }\n  }\n\n  // Get analyst recommendations\n  async getAnalystRecommendations(symbol: string, days: number = 30) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/analyst-stock-recommendations/${symbol.toUpperCase()}`,\n        {\n          params: {\n            apikey: this.apiKey,\n            limit: days\n          }\n        }\n      )\n\n      return response.data || []\n    } catch (error) {\n      console.error('Error fetching analyst recommendations:', error)\n      return []\n    }\n  }\n\n  // Get insider trading data\n  async getInsiderTrading(symbol: string, days: number = 30) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v4/insider-trading`,\n        {\n          params: {\n            apikey: this.apiKey,\n            symbol: symbol.toUpperCase(),\n            limit: days * 5 // Approximate multiple to get enough data\n          }\n        }\n      )\n\n      // Filter to last N days\n      const cutoffDate = new Date()\n      cutoffDate.setDate(cutoffDate.getDate() - days)\n\n      return (response.data || []).filter((trade: any) =>\n        new Date(trade.filingDate) >= cutoffDate\n      )\n    } catch (error) {\n      console.error('Error fetching insider trading:', error)\n      return []\n    }\n  }\n\n  // Get SEC filings\n  async getSECFilings(symbol: string, days: number = 30) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/sec_filings/${symbol.toUpperCase()}`,\n        {\n          params: {\n            apikey: this.apiKey,\n            limit: days * 2 // Get more filings to filter by date\n          }\n        }\n      )\n\n      // Filter to last N days\n      const cutoffDate = new Date()\n      cutoffDate.setDate(cutoffDate.getDate() - days)\n\n      return (response.data || []).filter((filing: any) =>\n        new Date(filing.filedDate) >= cutoffDate\n      )\n    } catch (error) {\n      console.error('Error fetching SEC filings:', error)\n      return []\n    }\n  }\n\n  // Get pre-market quotes for gap scanning\n  async getPreMarketQuote(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/quote/${symbol.toUpperCase()}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      const data = response.data[0]\n      if (!data) return null\n\n      return {\n        symbol: data.symbol,\n        price: data.price,\n        previousClose: data.previousClose,\n        change: data.change,\n        changePercent: data.changesPercentage,\n        volume: data.volume,\n        marketCap: data.marketCap,\n        avgVolume: data.avgVolume,\n        // Pre-market specific data (if available)\n        preMarketPrice: data.preMarketPrice || data.price,\n        preMarketChange: data.preMarketChange || data.change,\n        preMarketChangePercent: data.preMarketChangePercent || data.changesPercentage\n      }\n    } catch (error) {\n      console.error('Error fetching pre-market quote:', error)\n      return null\n    }\n  }\n\n  // Get multiple pre-market quotes efficiently\n  async getMultiplePreMarketQuotes(symbols: string[]) {\n    try {\n      const symbolsString = symbols.map(s => s.toUpperCase()).join(',')\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/quote/${symbolsString}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return (response.data || []).map((data: any) => ({\n        symbol: data.symbol,\n        price: data.price,\n        previousClose: data.previousClose,\n        change: data.change,\n        changePercent: data.changesPercentage,\n        volume: data.volume,\n        marketCap: data.marketCap,\n        avgVolume: data.avgVolume,\n        preMarketPrice: data.preMarketPrice || data.price,\n        preMarketChange: data.preMarketChange || data.change,\n        preMarketChangePercent: data.preMarketChangePercent || data.changesPercentage\n      }))\n    } catch (error) {\n      console.error('Error fetching multiple pre-market quotes:', error)\n      return []\n    }\n  }\n\n  // Get company profile for additional context\n  async getCompanyProfile(symbol: string) {\n    try {\n      const response = await axios.get(\n        `${FMP_BASE_URL}/v3/profile/${symbol.toUpperCase()}`,\n        {\n          params: {\n            apikey: this.apiKey\n          }\n        }\n      )\n\n      return response.data[0] || null\n    } catch (error) {\n      console.error('Error fetching company profile:', error)\n      return null\n    }\n  }\n}\n\n// Create a singleton instance\nexport const fmpAPI = new FMPAPI()\n", "import { NextRequest, NextResponse } from 'next/server'\nimport { PreMarketGapScanner } from '@/lib/preMarketGapScanner'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    \n    const minGap = parseFloat(searchParams.get('minGap') || '3')\n    const maxGap = parseFloat(searchParams.get('maxGap') || '15')\n    const customUniverse = searchParams.get('universe')?.split(',').filter(Boolean)\n    const catalystTypes = searchParams.get('catalystTypes')?.split(',').filter(Boolean)\n    const perfectPickOnly = searchParams.get('perfectPickOnly') === 'true'\n    const limit = parseInt(searchParams.get('limit') || '50')\n\n    console.log('📊 Gap Scanner API called with params:', {\n      minGap,\n      maxGap,\n      customUniverse: customUniverse?.length || 'default',\n      catalystTypes,\n      perfectPickOnly,\n      limit\n    })\n\n    // Initialize Gap Scanner\n    const gapScanner = new PreMarketGapScanner(\n      process.env.FMP_API_KEY,\n      process.env.POLYGON_API_KEY\n    )\n\n    let results\n    \n    if (perfectPickOnly) {\n      // Get only Perfect-Pick candidates\n      results = await gapScanner.getPerfectPickCandidates(customUniverse)\n    } else if (catalystTypes && catalystTypes.length > 0) {\n      // Filter by catalyst types\n      results = await gapScanner.getCatalystTypeResults(catalystTypes, customUniverse)\n    } else {\n      // Get results by gap range\n      results = await gapScanner.getGapRangeResults(minGap, maxGap, customUniverse)\n    }\n\n    // Limit results\n    const limitedResults = results.slice(0, limit)\n\n    // Get summary statistics\n    const summary = gapScanner.getScanSummary(limitedResults)\n\n    const response = {\n      success: true,\n      data: {\n        results: limitedResults,\n        summary,\n        scanParams: {\n          minGap,\n          maxGap,\n          universeSize: customUniverse?.length || 'default',\n          catalystTypes,\n          perfectPickOnly,\n          limit\n        },\n        timestamp: new Date().toISOString()\n      }\n    }\n\n    return NextResponse.json(response)\n  } catch (error) {\n    console.error('Error in Gap Scanner API:', error)\n    return NextResponse.json(\n      { \n        success: false, \n        error: 'Failed to run gap scan',\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const { action, data } = body\n\n    const gapScanner = new PreMarketGapScanner(\n      process.env.FMP_API_KEY,\n      process.env.POLYGON_API_KEY\n    )\n\n    switch (action) {\n      case 'update_results':\n        const updatedResults = await gapScanner.updateScanResults(data.results)\n        return NextResponse.json({\n          success: true,\n          data: { results: updatedResults }\n        })\n\n      case 'get_scheduled_times':\n        const scheduledTimes = gapScanner.getScheduledScanTimes()\n        return NextResponse.json({\n          success: true,\n          data: { scheduledTimes }\n        })\n\n      default:\n        return NextResponse.json(\n          { success: false, error: 'Invalid action' },\n          { status: 400 }\n        )\n    }\n  } catch (error) {\n    console.error('Error in Gap Scanner POST API:', error)\n    return NextResponse.json(\n      { \n        success: false, \n        error: 'Failed to process gap scanner request',\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    )\n  }\n}\n", "import { AppRouteRouteModule } from \"next/dist/esm/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/esm/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/esm/server/lib/patch-fetch\";\nimport { getRequestMeta } from \"next/dist/esm/server/request-meta\";\nimport { getTracer, SpanKind } from \"next/dist/esm/server/lib/trace/tracer\";\nimport { normalizeAppPath } from \"next/dist/esm/shared/lib/router/utils/app-paths\";\nimport { NodeNextRequest, NodeNextResponse } from \"next/dist/esm/server/base-http/node\";\nimport { NextRequestAdapter, signalFromNodeResponse } from \"next/dist/esm/server/web/spec-extension/adapters/next-request\";\nimport { BaseServerSpan } from \"next/dist/esm/server/lib/trace/constants\";\nimport { getRevalidateReason } from \"next/dist/esm/server/instrumentation/utils\";\nimport { sendResponse } from \"next/dist/esm/server/send-response\";\nimport { fromNodeOutgoingHttpHeaders, toNodeOutgoingHttpHeaders } from \"next/dist/esm/server/web/utils\";\nimport { getCacheControlHeader } from \"next/dist/esm/server/lib/cache-control\";\nimport { INFINITE_CACHE, NEXT_CACHE_TAGS_HEADER } from \"next/dist/esm/lib/constants\";\nimport { NoFallbackError } from \"next/dist/esm/shared/lib/no-fallback-error.external\";\nimport { CachedRouteKind } from \"next/dist/esm/server/response-cache\";\nimport * as userland from \"INNER_APP_ROUTE\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/scanner/gap-scan/route\",\n        pathname: \"/api/scanner/gap-scan\",\n        filename: \"route\",\n        bundlePath: \"\"\n    },\n    distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n    relativeProjectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || '',\n    resolvedPagePath: \"[project]/swing-trader-ai/src/app/api/scanner/gap-scan/route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\nexport async function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/scanner/gap-scan/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (process.env.TURBOPACK) {\n        srcPage = srcPage.replace(/\\/index$/, '') || '/';\n    } else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = process.env.__NEXT_MULTI_ZONE_DRAFT_MODE;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = normalizeAppPath(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = getTracer();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: getRequestMeta(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new NodeNextRequest(req);\n    const nodeNextRes = new NodeNextResponse(res);\n    const nextReq = NextRequestAdapter.fromNodeNextRequest(nodeNextReq, signalFromNodeResponse(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!getRequestMeta(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = toNodeOutgoingHttpHeaders(response.headers);\n                        if (cacheTags) {\n                            headers[NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await sendResponse(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: getRevalidateReason({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!getRequestMeta(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = fromNodeOutgoingHttpHeaders(cacheEntry.value.headers);\n            if (!(getRequestMeta(req, 'minimalMode') && isIsr)) {\n                headers.delete(NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', getCacheControlHeader(cacheEntry.cacheControl));\n            }\n            await sendResponse(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        if (!(err instanceof NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: getRevalidateReason({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await sendResponse(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n"], "names": [], "mappings": "8yCAAA,IAAA,EAAA,EAAA,CAAA,CAAA,OAGA,IAAM,EAAmB,yBACnB,EAAU,QAAQ,GAAG,CAAC,eAAe,AAEpC,OAAM,EACH,MAER,AAFsB,aAEV,CAAe,CAAE,CAE3B,GADA,IAAI,CAAC,MAAM,CAAG,GAAU,GAAW,GAC/B,CAAC,IAAI,CAAC,MAAM,CACd,CADgB,KACV,AAAI,MAAM,8BAEpB,CAGA,MAAM,cAAc,CAAc,CAAsB,CACtD,GAAI,CAWF,IAAM,EAAO,CATI,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAiB,8CAA8C,EAAE,EAAA,CAAQ,CAC5E,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGoB,IAAI,CAAC,OAAO,CAAC,EAAE,CACrC,GAAI,CAAC,EACH,IADS,EACH,AAAI,MAAM,CAAC,kBAAkB,EAAE,EAAA,CAAQ,EAG/C,IAAM,EAAS,EAAK,KAAK,EAAI,EACvB,EAAU,EAAO,GAAG,EAAI,CAAC,EACzB,EAAc,EAAO,OAAO,EAAI,CAAC,EACrB,EAAO,SAAS,CAIlC,GAJsC,CAAC,AAIjC,EAHY,AAGG,GAHI,SAAS,EAAI,EAAC,EAGR,CAAC,EAAI,EAAQ,CAAC,EAAI,EAAY,CAAC,CACxD,EAAY,EAAY,CAAC,EAAI,EAAQ,CAAC,CACtC,EAAS,EAAe,EAG9B,MAAO,CACL,OAAQ,EAAO,WAAW,GAC1B,KAAM,EAAO,IAAI,EAAI,EAAO,WAAW,GACvC,MAAO,SACP,EACA,cAPqB,EAAS,EAAa,IAQ3C,OAAQ,EAAQ,CAAC,EAAI,EACrB,UAAW,EAAO,UAAU,CAC5B,QAAI,EACJ,cAAU,CACZ,CACF,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,2CAA4C,GAG1D,GAAI,CAWF,IAAM,EAAO,CAVY,MAAM,EAAA,OAAK,CAAC,GAAG,CACtC,CAAA,EAAG,EAAiB,gBAAgB,EAAE,EAAO,KAAK,CAAC,CACnD,CACE,OAAQ,CACN,SAAU,OACV,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAG4B,IAAI,CAAC,OAAO,CAAC,EAAE,CAC7C,MAAO,CACL,OAAQ,EAAO,WAAW,GAC1B,KAAM,EAAO,WAAW,GACxB,MAAO,EAAK,CAAC,CACb,OAAQ,EAAK,CAAC,CAAG,EAAK,CAAC,CACvB,cAAgB,CAAC,EAAK,CAAC,CAAG,GAAM,AAAD,EAAK,EAAK,CAAC,CAAI,IAC9C,OAAQ,EAAK,CAAC,CACd,eAAW,EACX,QAAI,EACJ,cAAU,CACZ,CACF,CAAE,MAAO,EAAe,CAEtB,MADA,QAAQ,KAAK,CAAC,gCAAiC,GACzC,AAAI,MAAM,CAAC,0BAA0B,EAAE,EAAA,CAAQ,CACvD,CACF,CACF,CAGA,MAAM,kBACJ,CAAc,CACd,EAAyD,KAAK,CAC9D,EAAqB,CAAC,CACtB,CAAY,CACZ,CAAU,CACkB,CAC5B,GAAI,CACF,IAAM,EAAW,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAiB,gBAAgB,EAAE,EAAO,OAAO,EAAE,EAAW,CAAC,EAAE,EAAS,CAAC,EAAE,EAAK,CAAC,EAAE,EAAA,CAAI,CAC5F,CACE,OAAQ,CACN,SAAU,OACV,KAAM,MACN,MAAO,IACP,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,GAGF,GAAI,CAAC,EAAS,IAAI,CAAC,OAAO,EAAqC,GAAG,CAApC,EAAS,IAAI,CAAC,OAAO,CAAC,MAAM,CAExD,OADA,QAAQ,IAAI,CAAC,CAAC,6BAA6B,EAAE,EAAA,CAAQ,EAC9C,EAAE,CAGX,OAAO,EAAS,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,AAAC,IAAiB,CACjD,IADgD,MACrC,EAAO,CAAC,CACnB,KAAM,EAAO,CAAC,CACd,KAAM,EAAO,CAAC,CACd,IAAK,EAAO,CAAC,CACb,MAAO,EAAO,CAAC,CACf,OAAQ,EAAO,CAAC,CAClB,CAAC,CACH,CAAE,MAAO,EAAO,CASd,MARA,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,EAAO,CAAC,CAAC,CAAE,GAG3D,EAAM,QAAQ,EAAE,CAClB,QAAQ,KAAK,CAAC,CAAC,mBAAmB,EAAE,EAAM,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,EAAM,QAAQ,CAAC,UAAU,CAAA,CAAE,EAC1F,QAAQ,KAAK,CAAC,iBAAkB,EAAM,QAAQ,CAAC,IAAI,GAG/C,AAAI,MAAM,CAAC,oCAAoC,EAAE,EAAO,EAAE,EAAE,EAAM,OAAO,CAAA,CAAE,CACnF,CACF,CAGA,MAAM,kBAAkB,CAAc,CAAE,CACtC,GAAI,CAUF,MAAO,CATU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAiB,sBAAsB,EAAE,EAAA,CAAQ,CACpD,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,CAAC,OACvB,AAD8B,CAC5B,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,kCAAmC,GAC1C,IACT,CACF,CAGA,MAAM,iBAAkB,CACtB,GAAI,CAUF,MAAO,CATU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAiB,oBAAoB,CAAC,CACzC,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,AACtB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,gCAAiC,GACxC,IACT,CACF,CAGA,MAAM,aAAa,CAAa,CAAE,EAAgB,EAAE,CAAE,CACpD,GAAI,CAcF,MAAO,AAbU,OAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAiB,qBAAqB,CAAC,CAC1C,CACE,OAAQ,CACN,OAAQ,EACR,OAAQ,SACR,OAAQ,aACR,EACA,OAAQ,IAAI,CAAC,MACf,AADqB,CAEvB,EAAA,EAGc,IAAI,CAAC,OAAO,EAAI,EAAE,AACpC,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,0BAA2B,GAClC,EAAE,AACX,CACF,CACF,CAG0B,IAAI,gDCzM9B,IAAA,EAAA,EAAA,CAAA,CAAA,OAGA,IAAM,EAAe,wCACf,EAAU,QAAQ,GAAG,CAAC,WAAW,AAEhC,OAAM,EACH,MAAc,AAEtB,aAAY,CAAe,CAAE,CAE3B,GADA,IAAI,CAAC,MAAM,CAAG,GAAU,GAAW,GAC/B,CAAC,IAAI,CAAC,MAAM,CACd,CADgB,KACV,AAAI,MAAM,0BAEpB,CAGA,MAAM,cAAc,CAAc,CAAsB,CACtD,GAAI,CAUF,IAAM,EAAO,CATI,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,UAAU,EAAE,EAAA,CAAQ,CACpC,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGoB,IAAI,CAAC,EAAE,CAC7B,GAAI,CAAC,EACH,IADS,EACC,AAAJ,MAAU,CAAC,yBAAyB,EAAE,EAAA,CAAQ,EAGtD,MAAO,CACL,OAAQ,EAAK,MAAM,CACnB,KAAM,EAAK,IAAI,EAAI,EAAK,MAAM,CAC9B,MAAO,EAAK,KAAK,CACjB,OAAQ,EAAK,MAAM,CACnB,cAAe,EAAK,iBAAiB,CACrC,OAAQ,EAAK,MAAM,CACnB,UAAW,EAAK,SAAS,CACzB,GAAI,EAAK,EAAE,CACX,cAAU,CACZ,CACF,CAAE,MAAO,CAFe,CAER,CAEd,MADA,QAAQ,KAAK,CAAC,iBAH+C,iBAGZ,GAC3C,AAAI,MAAM,CAAC,0BAA0B,EAAE,EAAA,CAAQ,CACvD,CACF,CAGA,MAAM,kBAAkB,CAAc,CAAE,CACtC,GAAI,CAUF,MAAO,CATU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,YAAY,EAAE,EAAA,CAAQ,CACtC,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MACf,AADqB,CAEvB,EAAA,EAGc,IAAI,CAAC,EAAE,AACzB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,kCAAmC,GAC1C,IACT,CACF,CAGA,MAAM,mBAAmB,CAAc,CAAE,CACvC,GAAI,CAUF,MAAO,CATU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,WAAW,EAAE,EAAA,CAAQ,CACrC,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,CAAC,EAAE,AACzB,CAAE,AADwB,MACjB,EAAO,CAEd,OADA,KAF6C,GAErC,KAAK,CAAC,mCAAoC,GAC3C,IACT,CACF,CAGA,MAAM,cAAc,CAAc,CAAE,CAClC,GAAI,CAUF,MAAO,CATU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,gBAAgB,EAAE,EAAA,CAAQ,CAC1C,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,CAAC,EAAE,AACzB,CAD0B,AACxB,MAAO,EAAO,CAEd,OADA,MAF8C,EAEtC,KAAK,CAAC,8BAA+B,GACtC,IACT,CACF,CAGA,MAAM,0BAA0B,CAAc,CAAE,CAC9C,GAAI,CAUF,MAAO,CATU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,kCAAkC,EAAE,EAAA,CAAQ,CAC5D,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MACf,AADqB,CAEvB,EAAA,EAGc,IAAI,AACtB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,0CAA2C,GAClD,EAAE,AACX,CACF,CAGA,MAAM,oBAAoB,CAAa,CAAE,CAAW,CAAE,CACpD,GAAI,CACF,IAAM,EAAc,CAClB,OAAQ,IAAI,CAAC,MAAM,AACrB,EAUA,OARI,IAAM,EAAO,IAAI,CAAG,CAAA,EACpB,IAAI,EAAO,EAAE,CAAG,CAAA,EAOb,AALU,OAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,oBAAoB,CAAC,CACrC,QAAE,CAAO,EAAA,EAGK,IAAI,AACtB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,oCAAqC,GAC5C,EAAE,AACX,CACF,CAGA,MAAM,oBAAoB,CAAa,CAAE,CAAW,CAAE,CACpD,GAAI,CACF,IAAM,EAAc,CAClB,OAAQ,IAAI,CAAC,MAAM,AACrB,EAUA,OARI,GAAM,GAAO,IAAI,CAAG,CAAA,EACpB,IAAI,EAAO,EAAE,CAAG,CAAA,EAOb,CALU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,qBAAqB,CAAC,CACtC,QAAE,CAAO,EAAA,EAGK,IAAI,AACtB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,oCAAqC,GAC5C,EACT,AADW,CAEb,CAGA,MAAM,aAAa,CAAa,CAAE,EAAgB,EAAE,CAAE,CACpD,GAAI,CAYF,MAXiB,AAWV,OAXgB,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,UAAU,CAAC,CAC3B,CACE,OAAQ,OACN,QACA,EACA,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,AACtB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,0BAA2B,GAClC,EAAE,AACX,CACF,CAGA,MAAM,sBAAuB,CAC3B,GAAI,CAUF,MATiB,AASV,OATgB,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,sBAAsB,CAAC,CACvC,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,AACtB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,qCAAsC,GAC7C,EAAE,AACX,CACF,CAGA,MAAM,gBAAgB,CAAsC,CAAE,CAC5D,GAAI,CAUF,MATiB,AASV,OATgB,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,iBAAiB,EAAE,EAAA,CAAM,CACzC,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,AACtB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,EAAK,CAAC,CAAC,CAAE,GACzC,EAAE,AACX,CACF,CAKA,MAAM,oBAAoB,CAAe,CAAE,EAAe,EAAE,CAAE,CAC5D,GAAI,CACF,IAAM,EAAW,IAAI,KACrB,EAAS,OAAO,CAAC,EAAS,OAAO,GAAK,GACtC,IAAM,EAAS,IAAI,KAcnB,MAAO,CAZU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,oBAAoB,CAAC,CACrC,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,CACnB,KAAM,EAAS,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAC1C,GAAI,EAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CACtC,GAAI,GAAU,CAAE,OAAQ,EAAO,WAAW,EAAG,CAAC,AAChD,CACF,EAAA,EAGc,IAAI,EAAI,EAAE,AAC5B,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,oCAAqC,GAC5C,EACT,AADW,CAEb,CAGA,MAAM,aAAa,CAAc,CAAE,EAAgB,EAAE,CAAE,CACrD,GAAI,CAYF,MAAO,CAXU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,cAAc,CAAC,CAC/B,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,CACnB,QAAS,EAAO,WAAW,SAC3B,CACF,CACF,EAAA,EAGc,IAAI,EAAI,EAAE,AAC5B,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,6BAA8B,GACrC,EAAE,AACX,CACF,CAGA,MAAM,0BAA0B,CAAc,CAAE,EAAe,EAAE,CAAE,CACjE,GAAI,CAWF,MAAO,CAVU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,kCAAkC,EAAE,EAAO,WAAW,GAAA,CAAI,CAC1E,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,CACnB,MAAO,CACT,CACF,EAAA,EAGc,IAAI,EAAI,EAAE,AAC5B,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,0CAA2C,GAClD,EACT,AADW,CAEb,CAGA,MAAM,kBAAkB,CAAc,CAAE,EAAe,EAAE,CAAE,CACzD,GAAI,CACF,IAAM,EAAW,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,mBAAmB,CAAC,CACpC,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,CACnB,OAAQ,EAAO,WAAW,GAC1B,MAAc,EAAP,AAAS,CAClB,CACF,GAII,EAAa,IAAI,KAGvB,OAFA,EAAW,OAAO,CAAC,EAAW,OAAO,AAP2B,GAOtB,GAEnC,CAAC,EAAS,IAAI,EAAI,EAAA,AAAE,EAAE,MAAM,CAAC,AAAC,GACnC,IAAI,KAAK,EAAM,UAAU,GAAK,EAElC,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,kCAAmC,GAC1C,EAAE,AACX,CACF,CAGA,MAAM,cAAc,CAAc,CAAE,EAAe,EAAE,CAAE,CACrD,GAAI,CACF,IAAM,EAAW,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,gBAAgB,EAAE,EAAO,WAAW,GAAA,CAAI,CACxD,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,CACnB,MAAc,EAAP,AAAS,CAClB,CACF,GAII,EAAa,IAAI,KAGvB,OAFA,EAAW,OAAO,CAAC,EAAW,EAP6B,KAOtB,GAAK,GAEnC,CAAC,EAAS,IAAI,EAAI,EAAA,AAAE,EAAE,MAAM,CAAC,AAAC,GACnC,IAAI,KAAK,EAAO,SAAS,GAAK,EAElC,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,8BAA+B,GACtC,EAAE,AACX,CACF,CAGA,MAAM,kBAAkB,CAAc,CAAE,CACtC,GAAI,CAUF,IAAM,EAAO,CATI,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,UAAU,EAAE,EAAO,WAAW,GAAA,CAAI,CAClD,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGoB,IAAI,CAAC,EAAE,CAC7B,GAAI,CAAC,EAAM,OAAO,KAElB,MAAO,CACL,OAAQ,EAAK,MAAM,CACnB,MAAO,EAAK,KAAK,CACjB,cAAe,EAAK,aAAa,CACjC,OAAQ,EAAK,MAAM,CACnB,cAAe,EAAK,iBAAiB,CACrC,OAAQ,EAAK,MAAM,CACnB,UAAW,EAAK,SAAS,CACzB,UAAW,EAAK,SAAS,CAEzB,eAAgB,EAAK,cAAc,EAAI,EAAK,KAAK,CACjD,gBAAiB,EAAK,eAAe,EAAI,EAAK,MAAM,CACpD,uBAAwB,EAAK,sBAAsB,EAAI,EAAK,iBAAiB,AAC/E,CACF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,mCAAoC,GAC3C,IACT,CACF,CAGA,MAAM,2BAA2B,CAAiB,CAAE,CAClD,GAAI,CACF,IAAM,EAAgB,EAAQ,GAAG,CAAC,GAAK,EAAE,WAAW,IAAI,IAAI,CAAC,KAU7D,MAAO,AAAC,EATS,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,UAAU,EAAE,EAAA,CAAe,CAC3C,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGe,IAAI,EAAI,EAAA,AAAE,EAAE,GAAG,CAAC,AAAC,IAAe,CAC/C,EAD8C,KACtC,EAAK,MAAM,CACnB,MAAO,EAAK,KAAK,CACjB,cAAe,EAAK,aAAa,CACjC,OAAQ,EAAK,MAAM,CACnB,cAAe,EAAK,iBAAiB,CACrC,OAAQ,EAAK,MAAM,CACnB,UAAW,EAAK,SAAS,CACzB,UAAW,EAAK,SAAS,CACzB,eAAgB,EAAK,cAAc,EAAI,EAAK,KAAK,CACjD,gBAAiB,EAAK,eAAe,EAAI,EAAK,MAAM,CACpD,uBAAwB,EAAK,sBAAsB,EAAI,EAAK,iBAAiB,CAC/E,CAAC,CACH,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,6CAA8C,GACrD,EACT,AADW,CAEb,CAGA,MAAM,kBAAkB,CAAc,CAAE,CACtC,GAAI,CAUF,MAAO,CATU,MAAM,EAAA,OAAK,CAAC,GAAG,CAC9B,CAAA,EAAG,EAAa,YAAY,EAAE,EAAO,WAAW,GAAA,CAAI,CACpD,CACE,OAAQ,CACN,OAAQ,IAAI,CAAC,MAAM,AACrB,CACF,EAAA,EAGc,IAAI,CAAC,EAAE,EAAI,IAC7B,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,kCAAmC,GAC1C,IACT,CACF,CACF,CAGsB,IAAI,0LEvb1B,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,CAAA,CAAA,OAAA,IAAA,EAAA,EAAA,CAAA,CAAA,2CDfA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEO,eAAe,EAAI,CAAoB,EAC5C,GAAI,CACF,GAAM,CAAE,cAAY,CAAE,CAAG,IAAI,IAAI,EAAQ,GAAG,EAEtC,EAAS,WAAW,EAAa,GAAG,CAAC,WAAa,KAClD,EAAS,WAAW,EAAa,GAAG,CAAC,WAAa,MAClD,EAAiB,EAAa,GAAG,CAAC,aAAa,MAAM,KAAK,OAAO,SACjE,EAAgB,EAAa,GAAG,CAAC,kBAAkB,MAAM,KAAK,OAAO,SACrE,EAAkB,AAAwC,WAA3B,GAAG,CAAC,mBACnC,EAAQ,SAAS,EAAa,GAAG,CAAC,UAAY,MAEpD,QAAQ,GAAG,CAAC,yCAA0C,QACpD,SACA,EACA,eAAgB,GAAgB,QAAU,wBAC1C,kBACA,QACA,CACF,GAGA,IAAM,EAAa,IAAI,EAAA,mBAAmB,CACxC,QAAQ,GAAG,CAAC,WAAW,CACvB,QAAQ,GAAG,CAAC,eAAe,EAiBvB,EAAiB,CAZnB,EAEQ,MAAM,EAAW,OAFR,iBAEgC,CAAC,GAC3C,GAAiB,EAAc,MAAM,CAAG,EAEvC,CAF0C,KAEpC,EAAW,sBAAsB,CAAC,EAAe,GAGvD,MAAM,EAAW,kBAAkB,CAAC,EAAQ,EAAQ,IAIjC,KAAK,CAAC,EAAG,GAGlC,EAAU,EAAW,cAAc,CAAC,GAEpC,EAAW,CACf,QAAS,GACT,KAAM,CACJ,QAAS,UACT,EACA,WAAY,QACV,SACA,EACA,aAAc,GAAgB,QAAU,wBACxC,kBACA,QACA,CACF,EACA,UAAW,IAAI,OAAO,WAAW,EACnC,CACF,EAEA,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,EAC3B,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,4BAA6B,GACpC,EAAA,YAAY,CAAC,IAAI,CACtB,CACE,SAAS,EACT,MAAO,yBACP,QAAS,aAAiB,MAAQ,EAAM,OAAO,CAAG,eACpD,EACA,CAAE,OAAQ,GAAI,EAElB,CACF,CAEO,eAAe,EAAK,CAAoB,EAC7C,GAAI,CAEF,GAAM,QAAE,CAAM,MAAE,CAAI,CAAE,CADT,EACY,IADN,EAAQ,IAAI,GAGzB,EAAa,IAAI,EAAA,mBAAmB,CACxC,QAAQ,GAAG,CAAC,WAAW,CACvB,QAAQ,GAAG,CAAC,eAAe,EAG7B,OAAQ,GACN,IAAK,iBACH,IAAM,EAAiB,MAAM,EAAW,iBAAiB,CAAC,EAAK,OAAO,EACtE,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,CACvB,SAAS,EACT,KAAM,CAAE,QAAS,CAAe,CAClC,EAEF,KAAK,sBACH,IAAM,EAAiB,EAAW,qBAAqB,GACvD,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,CACvB,SAAS,EACT,KAAM,gBAAE,CAAe,CACzB,EAEF,SACE,OAAO,EAAA,YAAY,CAAC,IAAI,CACtB,CAAE,SAAS,EAAO,MAAO,gBAAiB,EAC1C,CAAE,OAAQ,GAAI,EAEpB,CACF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,iCAAkC,GACzC,EAAA,YAAY,CAAC,IAAI,CACtB,CACE,QAAS,GACT,MAAO,wCACP,QAAS,aAAiB,MAAQ,EAAM,OAAO,CAAG,eACpD,EACA,CAAE,OAAQ,GAAI,EAElB,CACF,CCzGA,IAAA,EAAA,EAAA,CAAA,CAAA,MAIA,IAAM,EAAc,IAAI,EAAA,mBAAmB,CAAC,CACxC,WAAY,CACR,KAAM,EAAA,SAAS,CAAC,SAAS,CACzB,KAAM,8BACN,SAAU,wBACV,SAAU,QACV,WAAY,EAChB,EACA,QAAS,CAAA,OACT,IADiD,eACc,CAA3C,EACpB,iBAAkB,kEAClB,iBAZqB,GAarB,SAAA,CACJ,GAIM,kBAAE,CAAgB,CAAE,sBAAoB,aAAE,CAAW,CAAE,CAAG,EAChE,SAAS,IACL,MAAO,CAAA,EAAA,EAAA,UAAA,AAAW,EAAC,kBACf,uBACA,CACJ,EACJ,CAEO,eAAe,EAAQ,CAAG,CAAE,CAAG,CAAE,CAAG,EACvC,IAAI,EACJ,IAAI,EAAU,8BAKV,EAAU,EAAQ,OAAO,CAAC,WAAY,KAAO,IAMjD,IAAM,EAAgB,MAAM,EAAY,OAAO,CAAC,EAAK,EAAK,SACtD,EACA,mBAHE,CAAA,CAIN,GACA,GAAI,CAAC,EAID,OAHA,EAAI,IADY,MACF,CAAG,IACjB,EAAI,GAAG,CAAC,eACS,MAAjB,CAAwB,CAApB,IAAyB,KAAhB,EAAoB,EAAI,SAAS,CAAC,IAAI,CAAC,EAAK,QAAQ,OAAO,IACjE,KAEX,GAAM,CAAE,SAAO,QAAE,CAAM,YAAE,CAAU,aAAE,CAAW,mBAAE,CAAiB,qBAAE,CAAmB,sBAAE,CAAoB,yBAAE,CAAuB,kBAAE,CAAgB,CAAE,CAAG,EACxJ,EAAoB,CAAA,EAAA,EAAA,gBAAgB,AAAhB,EAAiB,GACvC,GAAQ,EAAQ,EAAkB,aAAa,CAAC,EAAkB,EAAI,EAAkB,MAAM,CAAC,EAAA,AAAiB,EACpH,GAAI,GAAS,CAAC,EAAa,CACvB,IAAM,GAAgB,CAAQ,EAAkB,MAAM,CAAC,EAAiB,CAClE,EAAgB,EAAkB,aAAa,CAAC,EAAkB,CACxE,GAAI,IAC+B,IAA3B,EAAc,KADH,GACW,EAAc,CAAC,EACrC,MAAM,IAAI,EAAA,CAD0C,cAIhE,AAHqC,CAIrC,IAAI,EAAW,MACX,GAAU,EAAY,IAAb,CAAkB,EAAK,EAAD,EAG/B,EAAW,AAAa,OAHqB,KAC7C,EAAW,CAAA,EAEwB,IAAM,CAAA,EAE7C,IAAM,GACgB,IAAtB,EAAY,EAAkB,GAAb,EAEjB,CAAC,EAKK,EAAe,GAAS,CAAC,EACzB,EAAS,EAAI,MAAM,EAAI,MACvB,EAAS,CAAA,EAAA,EAAA,SAAA,AAAS,IAClB,EAAa,EAAO,WAVyE,OAUvD,GACtC,EAAU,QACZ,oBACA,EACA,WAAY,CACR,aAAc,CACV,iBAAiB,CAAQ,EAAW,YAAY,CAAC,eAAe,CAChE,gBAAgB,CAAQ,EAAW,YAAY,CAAC,cAAc,AAClE,0BACA,EACA,iBAAkB,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,oBACtC,kBAAmB,AAAwD,OAAvD,EAA2B,EAAW,YAAA,AAAY,EAAY,KAAK,EAAI,EAAyB,SAAS,cAC7H,EACA,UAAW,EAAI,SAAS,CACxB,QAAS,AAAC,IACN,EAAI,EAAE,CAAC,QAAS,EACpB,EACA,sBAAkB,EAClB,8BAA+B,CAAC,EAAO,EAAU,IAAe,EAAY,cAAc,CAAC,EAAK,EAAO,EAAc,EACzH,EACA,cAAe,SACX,CACJ,CACJ,EACM,EAAc,IAAI,EAAA,eAAe,CAAC,GAClC,EAAc,IAAI,EAAA,gBAAgB,CAAC,GACnC,EAAU,EAAA,kBAAkB,CAAC,mBAAmB,CAAC,EAAa,CAAA,EAAA,EAAA,sBAAA,AAAsB,EAAC,IAC3F,GAAI,CACA,IAAM,EAAoB,MAAO,GACtB,EAAY,MAAM,CAAC,EAAS,GAAS,OAAO,CAAC,KAChD,GAAI,CAAC,EAAM,OACX,EAAK,aAAa,CAAC,CACf,mBAAoB,EAAI,UAAU,CAClC,YAAY,CAChB,GACA,IAAM,EAAqB,EAAO,qBAAqB,GAEvD,GAAI,CAAC,EACD,OAEJ,GAAI,EAAmB,GAAG,CAAC,EAHF,kBAGwB,EAAA,cAAc,CAAC,aAAa,CAAE,YAC3E,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,EAAmB,GAAG,CAAC,kBAAkB,qEAAqE,CAAC,EAG9J,IAAM,EAAQ,EAAmB,GAAG,CAAC,cACrC,GAAI,EAAO,CACP,IAAM,EAAO,CAAA,EAAG,EAAO,CAAC,EAAE,EAAA,CAAO,CACjC,EAAK,aAAa,CAAC,CACf,aAAc,EACd,aAAc,EACd,iBAAkB,CACtB,GACA,EAAK,UAAU,CAAC,EACpB,MACI,CADG,CACE,UAAU,CAAC,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAE9C,GAEE,EAAiB,MAAO,QACtB,EA0FI,EAzFR,IAAM,EAAoB,MAAO,oBAAE,CAAkB,CAAE,IACnD,GAAI,CACA,GAAI,CAAC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GAAwB,GAA2B,CAAC,EAK3F,OAJA,EAAI,SAD2G,CACjG,CAAG,IAEjB,EAAI,SAAS,CAAC,iBAAkB,eAChC,EAAI,GAAG,CAAC,gCACD,KAEX,IAAM,EAAW,MAAM,EAAkB,EACzC,GAAI,YAAY,CAAG,EAAQ,UAAU,CAAC,YAAY,CAClD,IAAI,EAAmB,EAAQ,UAAU,CAAC,gBAAgB,CAGtD,GACI,EAAI,SAAS,EAAE,CACf,CAFc,CAEV,SAAS,CAAC,GACd,OAAmB,GAG3B,IAAM,EAAY,EAAQ,UAAU,CAAC,aAAa,CAGlD,IAAI,EA6BA,OADA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,EAAU,EAAQ,UAAU,CAAC,gBAAgB,EACnF,IA7BA,EACP,IAAM,EAAO,MAAM,EAAS,IAAI,GAE1B,EAAU,CAAA,EAAA,EAAA,yBAAA,AAAyB,EAAC,EAAS,OAAO,EACtD,IACA,CAAO,CAAC,EAAA,GADG,mBACmB,CAAC,CAAG,CAAA,EAElC,CAAC,CAAO,CAAC,eAAe,EAAI,EAAK,IAAI,EAAE,CACvC,CAAO,CAAC,eAAe,CAAG,EAAK,IAAA,AAAI,EAEvC,IAAM,EAAa,KAAkD,IAA3C,EAAQ,UAAU,CAAC,mBAAmB,IAAoB,EAAQ,UAAU,CAAC,mBAAmB,EAAI,EAAA,cAAA,AAAc,GAAG,AAAQ,EAAQ,UAAU,CAAC,mBAAmB,CACvL,EAAuD,AAA9C,SAAO,EAAQ,UAAU,CAAC,eAAe,EAAoB,EAAQ,UAAU,CAAC,eAAe,EAAI,EAAA,cAAc,MAAG,EAAY,EAAQ,UAAU,CAAC,eAAe,CAcjL,MAZmB,CAYZ,AAXH,MAAO,CACH,KAAM,EAAA,eAAe,CAAC,SAAS,CAC/B,OAAQ,EAAS,MAAM,CACvB,KAAM,OAAO,IAAI,CAAC,MAAM,EAAK,WAAW,YACxC,CACJ,EACA,aAAc,YACV,SACA,CACJ,CACJ,CAEJ,CAKJ,CAAE,KALS,CAKF,EAAK,CAcV,MAX0B,MAAtB,EAA6B,KAAK,EAAI,EAAmB,OAAO,AAAP,EAAS,CAClE,MAAM,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAmB,AAAnB,EAAoB,cAClC,uBACA,CACJ,EACJ,EAAG,GAED,CACV,CACJ,EACM,EAAa,MAAM,EAAY,cAAc,CAAC,CAChD,iBACA,WACA,EACA,UAAW,EAAA,SAAS,CAAC,SAAS,CAC9B,YAAY,oBACZ,EACA,mBAAmB,EACnB,uBACA,4CACA,EACA,UAAW,EAAI,SAAS,AAC5B,GAEA,GAAI,CAAC,EACD,KADQ,EACD,KAEX,GAAI,CAAe,MAAd,CAAqB,EAAS,AAA0C,GAA9C,IAAK,EAAoB,EAAW,KAAA,AAAK,EAAY,KAAK,EAAI,EAAkB,IAAI,IAAM,EAAA,eAAe,CAAC,SAAS,CAE9I,CAFgJ,KAE1I,OAAO,cAAc,CAAC,AAAI,MAAM,CAAC,kDAAkD,EAAgB,MAAd,CAAqB,EAAS,AAA2C,GAA/C,IAAK,EAAqB,EAAW,KAAK,AAAL,EAAiB,KAAK,EAAI,EAAmB,IAAI,CAAA,CAAE,EAAG,oBAAqB,CACjO,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,EAEA,CAAC,CAAA,EAAA,EAAA,cAAc,AAAd,EAAe,EAAK,gBAAgB,AACrC,EAAI,SAAS,CAAC,iBAAkB,EAAuB,cAAgB,EAAW,MAAM,CAAG,OAAS,EAAW,OAAO,CAAG,QAAU,OAGnI,GACA,EAAI,QADS,CACA,CAAC,gBAAiB,2DAEnC,IAAM,EAAU,CAAA,EAAA,EAAA,2BAAA,AAA2B,EAAC,EAAW,KAAK,CAAC,OAAO,EAapE,MAZI,AAAE,CAAD,AAAC,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GACxC,EAD6C,AACrC,GADwC,GAClC,CAAC,EAAA,sBAAsB,GAIrC,EAAW,YAAY,EAAK,EAAD,AAAK,SAAS,CAAC,kBAAqB,EAAD,AAAS,GAAG,CAAC,kBAAkB,AAC7F,EAAQ,GAAG,CAAC,gBAAiB,CAAA,EAAA,EAAA,qBAAA,AAAqB,EAAC,EAAW,YAAY,GAE9E,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,EAAW,KAAK,CAAC,IAAI,CAAE,SAC7E,EACA,OAAQ,EAAW,KAAK,CAAC,MAAM,EAAI,GACvC,IACO,IACX,EAGI,EACA,MAAM,EAAe,EADT,CAGZ,MAAM,EAAO,qBAAqB,CAAC,EAAI,OAAO,CAAE,IAAI,EAAO,KAAK,CAAC,EAAA,cAAc,CAAC,aAAa,CAAE,CACvF,SAAU,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAChC,KAAM,EAAA,QAAQ,CAAC,MAAM,CACrB,WAAY,CACR,cAAe,EACf,cAAe,EAAI,GAAG,AAC1B,CACJ,EAAG,GAEf,CAAE,MAAO,EAAK,CAcV,GAbI,AAAE,CAAD,YAAgB,EAAA,eAAe,EAChC,CADmC,KAC7B,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,cAClC,uBACA,CACJ,EACJ,GAIA,EAAO,MAAM,EAKjB,OAHA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,KAAM,CAC5D,OAAQ,GACZ,IACO,IACX,CACJ,EAEA,qCAAqC", "ignoreList": [3]}